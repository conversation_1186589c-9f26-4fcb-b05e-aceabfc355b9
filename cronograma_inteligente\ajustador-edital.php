<?php
/**
 * Função universal para converter o formato do edital
 * 
 * Reconhece qualquer matéria (não apenas Direito Administrativo)
 * Converte "X. NOME DA MATÉRIA" para "# NOME DA MATÉRIA"
 */
function converterEdital($texto) {
    $linhas = explode("\n", $texto);
    $resultado = [];
    $numeroMateria = null;
    $padrao = '/^(\d+)\.?\s+(.+)$/';
    
    // Processa o título principal (qualquer matéria)
    if (!empty($linhas[0]) && preg_match($padrao, $linhas[0], $matches)) {
        $numeroMateria = $matches[1]; // Captura o número da matéria (1, 2, 3, etc.)
        $nomeMateria = trim($matches[2]); 
        $resultado[] = "{$nomeMateria}";
    } else {
        $resultado[] = $linhas[0];
    }
    
    // Processa cada linha, mas só se encontrou um número de matéria
    if ($numeroMateria !== null) {
        // Cria uma expressão regular dinâmica para detectar subitens da matéria atual
        $padraoSubitem = '/^' . preg_quote($numeroMateria, '/') . '\.(\d+(?:\.\d+)*)\.?\s+(.+)$/';
        
        for ($i = 1; $i < count($linhas); $i++) {
            $linha = trim($linhas[$i]);
            
            if (empty($linha)) {
                $resultado[] = "";
                continue;
            }
            
            // Verifica se a linha é um subitem da matéria atual
            if (preg_match($padraoSubitem, $linha, $matches)) {
                $subindices = $matches[1]; // Ex: "1", "1.2", "1.2.3"
                $conteudo = $matches[2];
                $resultado[] = "$subindices. $conteudo";
            } else {
                // Se não for um subitem, mantém a linha original
                $resultado[] = $linha;
            }
        }
    } else {
        // Se não encontrou um número de matéria, mantém todas as linhas originais
        for ($i = 1; $i < count($linhas); $i++) {
            $resultado[] = $linhas[$i];
        }
    }
    
    return implode("\n", $resultado);
}

// Função genérica para processar o texto com expressões regulares genéricas
function converterEditalGenerico($texto) {
    $linhas = explode("\n", $texto);
    $resultado = [];
    $numeroMateria = null;
    
    // Processa o título principal (qualquer matéria)
    if (!empty($linhas[0]) && preg_match('/^(\d+)\.?\s+(.+)$/', $linhas[0], $matches)) {
        $numeroMateria = $matches[1]; // Exemplo: "1", "2", "3"
        $nomeMateria = $matches[2]; 
        $resultado[] = $nomeMateria . ":";
    } else {
        $resultado[] = $linhas[0];
        return implode("\n", $resultado); // Se não encontrou padrão, retorna o texto original
    }
    
    // Cria um padrão para reconhecer os subitens baseado no número da matéria
    $padraoBase = '^' . preg_quote($numeroMateria, '/') . '\.';
    
    // Processa cada linha
    for ($i = 1; $i < count($linhas); $i++) {
        $linha = trim($linhas[$i]);
        
        if (empty($linha)) {
            $resultado[] = "";
            continue;
        }
        
        // Verifica diferentes níveis de subitens de forma progressiva
        if (preg_match('/' . $padraoBase . '(\d+)\.(\d+)\.(\d+)\.(\d+)\.?\s+(.+)$/', $linha, $matches)) {
            // Nível 4: *******.5 -> 2.3.4.5
            $resultado[] = "{$matches[1]}.{$matches[2]}.{$matches[3]}.{$matches[4]}. {$matches[5]}";
        } 
        else if (preg_match('/' . $padraoBase . '(\d+)\.(\d+)\.(\d+)\.?\s+(.+)$/', $linha, $matches)) {
            // Nível 3: ******* -> 2.3.4
            $resultado[] = "{$matches[1]}.{$matches[2]}.{$matches[3]}. {$matches[4]}";
        }
        else if (preg_match('/' . $padraoBase . '(\d+)\.(\d+)\.?\s+(.+)$/', $linha, $matches)) {
            // Nível 2: 1.2.3 -> 2.3
            $resultado[] = "{$matches[1]}.{$matches[2]}. {$matches[3]}";
        }
        else if (preg_match('/' . $padraoBase . '(\d+)\.?\s+(.+)$/', $linha, $matches)) {
            // Nível 1: 1.2 -> 2
            $resultado[] = "{$matches[1]}. {$matches[2]}";
        }
        else {
            // Se não for um subitem reconhecido, mantém a linha original
            $resultado[] = $linha;
        }
    }
    
    return implode("\n", $resultado);
}

// Função totalmente universal que detecta o número da matéria e ajusta todos os subitens
function converterEditalUniversal($texto) {
    $linhas = explode("\n", $texto);
    $resultado = [];
    $numeroMateria = null;
    $prefixoMateria = '';
    
    // Verifica se a primeira linha é um título de matéria (um número seguido de um nome em maiúsculas)
    if (!empty($linhas[0]) && preg_match('/^(\d+)\.?\s+([A-Z\s]+(?:\s+[A-Za-z\s]+)?)$/', $linhas[0], $matches)) {
        $numeroMateria = $matches[1]; // Ex: "1", "2", "3"
        $prefixoMateria = $numeroMateria . '.';
        $nomeMateria = trim($matches[2]);
        $resultado[] = "{$nomeMateria}";
    } else {
        // Se não for um título de matéria, retorna o texto original
        return $texto;
    }
    
    // Processa cada linha após o título
    for ($i = 1; $i < count($linhas); $i++) {
        $linha = trim($linhas[$i]);
        
        if (empty($linha)) {
            $resultado[] = "";
            continue;
        }
        
        // Verifica se a linha começa com o prefixo da matéria
        if (strpos($linha, $prefixoMateria) === 0) {
            // Remove o prefixo da matéria
            $novaLinha = substr($linha, strlen($prefixoMateria));
            $resultado[] = $novaLinha;
        } else {
            // Se não começa com o prefixo, mantém a linha original
            $resultado[] = $linha;
        }
    }
    
    return implode("\n", $resultado);
}

// Interface web aprimorada
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conversor Universal de Editais</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f7f9fc;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        textarea {
            width: 100%;
            height: 300px;
            padding: 15px;
            font-family: monospace;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
        }
        button {
            background-color: #3498db;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #2980b9;
        }
        button#limparBtn {
            background-color: #e74c3c;
        }
        button#limparBtn:hover {
            background-color: #c0392b;
        }
        button#copiarBtn, button#copiarCodigoBtn {
            background-color: #2ecc71;
        }
        button#copiarBtn:hover, button#copiarCodigoBtn:hover {
            background-color: #27ae60;
        }
        .resultado {
            border: 1px solid #ddd;
            padding: 20px;
            background-color: white;
            white-space: pre-wrap;
            font-family: monospace;
            margin-top: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .metodos {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #ecf0f1;
            border-radius: 5px;
        }
        .metodos label {
            margin-right: 15px;
            display: inline-block;
            padding: 5px 0;
        }
        .instrucoes {
            background-color: #d5e8f9;
            padding: 20px;
            border-left: 5px solid #3498db;
            margin-bottom: 25px;
            border-radius: 0 5px 5px 0;
        }
        .instrucoes h2 {
            margin-top: 0;
        }
        .alerta {
            background-color: #fdebd0;
            border-left: 5px solid #f39c12;
            padding: 10px;
            margin: 15px 0;
            border-radius: 0 5px 5px 0;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            color: #7f8c8d;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <h1>Conversor Universal de Editais</h1>
    
    <div class="instrucoes">
        <h2>Instruções</h2>
        <p>Este sistema converte automaticamente a numeração de <strong>qualquer matéria em editais</strong>, removendo o primeiro nível do título.</p>
        <p><strong>Formatos suportados:</strong></p>
        <ul>
            <li>Qualquer matéria (ex: "1. DIREITO ADMINISTRATIVO", "3. DIREITO CONSTITUCIONAL", etc.)</li>
            <li>Com ou sem pontos após os números</li>
            <li>Múltiplos níveis de numeração</li>
        </ul>
        
        <div class="alerta">
            <strong>Exemplo:</strong> Converte "3. DIREITO CONSTITUCIONAL" para "# DIREITO CONSTITUCIONAL" e todos os itens "3.x" serão ajustados para nível "x".
        </div>
    </div>
    
    <form method="post">
        <textarea name="texto" placeholder="Cole o texto do edital aqui..."><?php echo isset($_POST['texto']) ? htmlspecialchars($_POST['texto']) : ''; ?></textarea>
        
        <div class="metodos">
            <strong>Escolha o método de conversão:</strong><br>
            <label><input type="radio" name="metodo" value="generico" checked> Genérico (recomendado)</label>
            <label><input type="radio" name="metodo" value="padrao" <?php echo isset($_POST['metodo']) && $_POST['metodo'] == 'padrao' ? 'checked' : ''; ?>> Padrão</label>
            <label><input type="radio" name="metodo" value="universal" <?php echo isset($_POST['metodo']) && $_POST['metodo'] == 'universal' ? 'checked' : ''; ?>> Universal</label>
        </div>
        
        <div>
            <button type="submit" name="converter">Converter</button>
            <button type="button" id="copiarBtn" style="display:none;">Copiar Resultado</button>
            <button type="button" id="copiarCodigoBtn" style="display:none;">Copiar Código</button>
            <button type="button" id="limparBtn">Limpar</button>
        </div>
    </form>
    
    <?php
    if (isset($_POST['converter']) && !empty($_POST['texto'])) {
        // Escolhe o método de conversão
        $metodo = isset($_POST['metodo']) ? $_POST['metodo'] : 'generico';
        
        if ($metodo == 'universal') {
            $resultado = converterEditalUniversal($_POST['texto']);
        } else if ($metodo == 'padrao') {
            $resultado = converterEdital($_POST['texto']);
        } else {
            $resultado = converterEditalGenerico($_POST['texto']);
        }
        
        echo '<h2>Resultado:</h2>';
        echo '<div class="resultado">' . nl2br(htmlspecialchars($resultado)) . '</div>';
        
        echo '<h3>Copiar código:</h3>';
        echo '<textarea id="resultadoTextarea" readonly>' . htmlspecialchars($resultado) . '</textarea>';
        echo '<script>
            document.getElementById("copiarBtn").style.display = "inline-block";
            document.getElementById("copiarCodigoBtn").style.display = "inline-block";
        </script>';
    }
    ?>
    
    <div class="footer">
        <p>Sistema de Conversão de Editais - Versão 2.0</p>
    </div>
    
    <script>
        // Script para copiar o resultado completo
        document.getElementById('copiarBtn')?.addEventListener('click', function() {
            var textarea = document.getElementById('resultadoTextarea');
            textarea.select();
            document.execCommand('copy');
            this.innerText = 'Copiado!';
            setTimeout(() => { this.innerText = 'Copiar Resultado'; }, 2000);
        });
        
        // Script para copiar apenas o código (sem o título "Copiar código:")
        document.getElementById('copiarCodigoBtn')?.addEventListener('click', function() {
            var textarea = document.getElementById('resultadoTextarea');
            // Se quisermos garantir que copiamos o conteúdo exato
            var conteudo = textarea.value;
            
            // Criar um elemento temporário para a cópia
            var tempTextarea = document.createElement('textarea');
            tempTextarea.value = conteudo;
            document.body.appendChild(tempTextarea);
            tempTextarea.select();
            document.execCommand('copy');
            document.body.removeChild(tempTextarea);
            
            this.innerText = 'Código Copiado!';
            setTimeout(() => { this.innerText = 'Copiar Código'; }, 2000);
        });
        
        // Script para limpar o formulário
        document.getElementById('limparBtn')?.addEventListener('click', function() {
            document.querySelector('textarea[name="texto"]').value = '';
            var resultado = document.querySelector('.resultado');
            if (resultado) resultado.style.display = 'none';
            var textarea = document.getElementById('resultadoTextarea');
            if (textarea) textarea.style.display = 'none';
            document.getElementById('copiarBtn').style.display = 'none';
            document.getElementById('copiarCodigoBtn').style.display = 'none';
        });
    </script>
</body>
</html>