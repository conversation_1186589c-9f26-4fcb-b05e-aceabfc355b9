<?php
/**
 * Script de Teste para Migração
 * Sistema de Fidelidade Barbearia
 * 
 * Este script testa se a migração pode ser executada com segurança
 */

require_once 'config/database.php';

class MigrationTester {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * Executar todos os testes
     */
    public function runTests() {
        echo "=== TESTANDO MIGRAÇÃO PARA NOVO SISTEMA DE ID ===\n\n";
        
        $tests = [
            'testDatabaseConnection' => 'Conexão com banco de dados',
            'testCurrentData' => 'Dados atuais do sistema',
            'testConflicts' => 'Conflitos de CPF/tipo',
            'testNewIdGeneration' => 'Geração de novos IDs',
            'testForeignKeys' => 'Integridade referencial'
        ];
        
        $passed = 0;
        $total = count($tests);
        
        foreach ($tests as $method => $description) {
            echo "🧪 Testando: $description\n";
            
            try {
                $result = $this->$method();
                if ($result) {
                    echo "✅ PASSOU\n\n";
                    $passed++;
                } else {
                    echo "❌ FALHOU\n\n";
                }
            } catch (Exception $e) {
                echo "❌ ERRO: " . $e->getMessage() . "\n\n";
            }
        }
        
        echo "=== RESULTADO DOS TESTES ===\n";
        echo "Passou: $passed/$total\n";
        
        if ($passed === $total) {
            echo "✅ Todos os testes passaram! Migração pode ser executada.\n";
            return true;
        } else {
            echo "❌ Alguns testes falharam. Resolva os problemas antes de migrar.\n";
            return false;
        }
    }
    
    /**
     * Testar conexão com banco
     */
    private function testDatabaseConnection() {
        $sql = "SELECT 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        return $stmt->fetch() !== false;
    }
    
    /**
     * Testar dados atuais
     */
    private function testCurrentData() {
        // Verificar se há usuários
        $sql = "SELECT COUNT(*) as total FROM usuarios";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $total = $stmt->fetch()['total'];
        
        echo "  - Total de usuários: $total\n";
        
        if ($total === 0) {
            echo "  ⚠️ Nenhum usuário encontrado\n";
            return false;
        }
        
        // Verificar estrutura das tabelas
        $tables = ['usuarios', 'perfis_clientes', 'pontuacao_fidelidade', 'historico_atendimentos'];
        foreach ($tables as $table) {
            $sql = "SHOW TABLES LIKE '$table'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            if (!$stmt->fetch()) {
                echo "  ❌ Tabela $table não encontrada\n";
                return false;
            }
        }
        
        echo "  - Todas as tabelas necessárias existem\n";
        return true;
    }
    
    /**
     * Testar conflitos de CPF/tipo
     */
    private function testConflicts() {
        $sql = "SELECT 
                    cpf, 
                    tipo, 
                    COUNT(*) as count,
                    GROUP_CONCAT(id) as ids
                FROM usuarios 
                GROUP BY cpf, tipo 
                HAVING COUNT(*) > 1";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $conflicts = $stmt->fetchAll();
        
        if (empty($conflicts)) {
            echo "  - Nenhum conflito encontrado\n";
            return true;
        }
        
        echo "  ❌ Conflitos encontrados:\n";
        foreach ($conflicts as $conflict) {
            echo "    CPF: {$conflict['cpf']}, Tipo: {$conflict['tipo']}, Count: {$conflict['count']}\n";
            echo "    IDs: {$conflict['ids']}\n";
        }
        
        return false;
    }
    
    /**
     * Testar geração de novos IDs
     */
    private function testNewIdGeneration() {
        // Buscar alguns usuários para teste
        $sql = "SELECT cpf, tipo FROM usuarios LIMIT 5";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $users = $stmt->fetchAll();
        
        foreach ($users as $user) {
            $newId = $user['cpf'] . '-' . strtoupper($user['tipo']);
            
            // Verificar formato
            if (!preg_match('/^[0-9]{11}-(CLIENTE|BARBEIRO)$/', $newId)) {
                echo "  ❌ ID inválido gerado: $newId\n";
                return false;
            }
        }
        
        echo "  - Geração de IDs funcionando corretamente\n";
        return true;
    }
    
    /**
     * Testar integridade referencial
     */
    private function testForeignKeys() {
        // Verificar se há registros órfãos
        $checks = [
            [
                'table' => 'perfis_clientes',
                'fk_column' => 'usuario_id',
                'ref_table' => 'usuarios',
                'ref_column' => 'id'
            ],
            [
                'table' => 'pontuacao_fidelidade',
                'fk_column' => 'cliente_id',
                'ref_table' => 'usuarios',
                'ref_column' => 'id'
            ],
            [
                'table' => 'historico_atendimentos',
                'fk_column' => 'cliente_id',
                'ref_table' => 'usuarios',
                'ref_column' => 'id'
            ]
        ];
        
        foreach ($checks as $check) {
            $sql = "SELECT COUNT(*) as orphans 
                    FROM {$check['table']} t 
                    LEFT JOIN {$check['ref_table']} r ON t.{$check['fk_column']} = r.{$check['ref_column']} 
                    WHERE r.{$check['ref_column']} IS NULL";
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $orphans = $stmt->fetch()['orphans'];
            
            if ($orphans > 0) {
                echo "  ❌ {$check['table']} tem $orphans registros órfãos\n";
                return false;
            }
        }
        
        echo "  - Integridade referencial OK\n";
        return true;
    }
}

// Executar testes se chamado diretamente
if (php_sapi_name() === 'cli') {
    $tester = new MigrationTester();
    $success = $tester->runTests();
    exit($success ? 0 : 1);
}
?>
