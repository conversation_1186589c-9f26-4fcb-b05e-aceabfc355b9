<?php
/**
 * Teste das APIs após migração
 */

// Configurações do banco
$host = 'barbeiro_br.mysql.dbaas.com.br';
$db_name = 'barbeiro_br';
$username = 'barbeiro_br';
$password = 'Lucasb90#';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== TESTANDO APIs APÓS MIGRAÇÃO ===\n\n";
    
    // 1. Testar busca de usuário por ID
    echo "🧪 Teste 1: Busca por ID\n";
    $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE id = ?");
    $stmt->execute(['11144477735-CLIENTE']);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "✅ Cliente encontrado: {$user['nome']} (ID: {$user['id']})\n";
    } else {
        echo "❌ Cliente não encontrado\n";
    }
    
    // 2. Testar busca por CPF e tipo
    echo "\n🧪 Teste 2: Busca por CPF e tipo\n";
    $stmt = $pdo->prepare("SELECT * FROM usuarios WHERE cpf = ? AND tipo = ?");
    $stmt->execute(['11144477735', 'cliente']);
    $user = $stmt->fetch();
    
    if ($user) {
        echo "✅ Cliente encontrado por CPF: {$user['nome']} (ID: {$user['id']})\n";
    } else {
        echo "❌ Cliente não encontrado por CPF\n";
    }
    
    // 3. Testar pontuação de fidelidade
    echo "\n🧪 Teste 3: Sistema de fidelidade\n";
    $stmt = $pdo->prepare("SELECT * FROM pontuacao_fidelidade WHERE cliente_id = ?");
    $stmt->execute(['11144477735-CLIENTE']);
    $score = $stmt->fetch();
    
    if ($score) {
        echo "✅ Pontuação encontrada:\n";
        echo "  - Pontos cabelo: {$score['pontos_cabelo']}\n";
        echo "  - Pontos barba: {$score['pontos_barba']}\n";
        echo "  - Pontuação geral: {$score['pontuacao_geral']}\n";
    } else {
        echo "❌ Pontuação não encontrada\n";
    }
    
    // 4. Testar histórico de atendimentos
    echo "\n🧪 Teste 4: Histórico de atendimentos\n";
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM historico_atendimentos WHERE cliente_id = ?");
    $stmt->execute(['11144477735-CLIENTE']);
    $count = $stmt->fetch()['total'];
    echo "✅ Atendimentos encontrados: $count registros\n";
    
    // 5. Testar brindes pendentes
    echo "\n🧪 Teste 5: Brindes pendentes\n";
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM brindes_pendentes WHERE cliente_id = ?");
    $stmt->execute(['11144477735-CLIENTE']);
    $count = $stmt->fetch()['total'];
    echo "✅ Brindes pendentes: $count registros\n";
    
    // 6. Testar integridade referencial
    echo "\n🧪 Teste 6: Integridade referencial\n";
    $checks = [
        'perfis_clientes' => 'usuario_id',
        'pontuacao_fidelidade' => 'cliente_id',
        'historico_atendimentos' => 'cliente_id',
        'brindes_pendentes' => 'cliente_id'
    ];
    
    $allGood = true;
    foreach ($checks as $table => $column) {
        $stmt = $pdo->prepare("SELECT COUNT(*) as orphans 
                              FROM $table t 
                              LEFT JOIN usuarios u ON t.$column = u.id 
                              WHERE u.id IS NULL");
        $stmt->execute();
        $orphans = $stmt->fetch()['orphans'];
        
        if ($orphans == 0) {
            echo "✅ $table: integridade OK\n";
        } else {
            echo "❌ $table: $orphans registros órfãos\n";
            $allGood = false;
        }
    }
    
    // 7. Testar formato dos IDs
    echo "\n🧪 Teste 7: Formato dos IDs\n";
    $stmt = $pdo->query("SELECT id FROM usuarios");
    $users = $stmt->fetchAll();
    
    $validFormat = true;
    foreach ($users as $user) {
        if (!preg_match('/^[0-9]{11}-(CLIENTE|BARBEIRO)$/', $user['id'])) {
            echo "❌ ID com formato inválido: {$user['id']}\n";
            $validFormat = false;
        }
    }
    
    if ($validFormat) {
        echo "✅ Todos os IDs estão no formato correto\n";
    }
    
    // Resultado final
    echo "\n" . str_repeat("=", 50) . "\n";
    if ($allGood && $validFormat) {
        echo "🎉 TODOS OS TESTES PASSARAM!\n";
        echo "✅ Sistema migrado com sucesso!\n";
        echo "✅ APIs funcionando corretamente!\n";
        echo "✅ Integridade dos dados mantida!\n";
    } else {
        echo "⚠️ Alguns testes falharam. Verifique os problemas acima.\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erro nos testes: " . $e->getMessage() . "\n";
}
?>
