<?php
// editar_topico.php
session_start();
include_once("assets/config.php");
include_once("helpers.php");

if (!isset($_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit();
}

redirectIfNotAdmin($conexao, $_SESSION['idusuario']);

if (!isset($_GET['id'])) {
    header("Location: flashcards.php");
    exit();
}

$topico_id = (int)$_GET['id'];
$mensagem = '';

// Buscar informações do tópico
$query_topico = "
    SELECT 
        t.*,
        d.nome as deck_name,
        d.id as deck_id,
        c.nome as categoria_name,
        c.id as categoria_id,
        m.nome as materia_nome
    FROM appestudo.flashcard_topics t
    JOIN appestudo.flashcard_decks d ON d.id = t.deck_id
    JOIN appestudo.flashcard_categories c ON c.id = d.category_id
    JOIN appestudo.materia m ON m.idmateria = d.materia_id
    WHERE t.id = $1";
$result_topico = pg_query_params($conexao, $query_topico, array($topico_id));
$topico = pg_fetch_assoc($result_topico);

if (!$topico) {
    header("Location: flashcards.php");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nome = trim($_POST['nome']);
    $descricao = trim($_POST['descricao']);
    $status = isset($_POST['status']) ? true : false;
    
    if (!empty($nome)) {
        // Verificar se já existe outro tópico com este nome no mesmo baralho
        $query_check = "
            SELECT id FROM appestudo.flashcard_topics 
            WHERE deck_id = $1 AND LOWER(nome) = LOWER($2) AND id != $3";
        $result_check = pg_query_params($conexao, $query_check, array(
            $topico['deck_id'], 
            $nome,
            $topico_id
        ));
        
        if (pg_num_rows($result_check) > 0) {
            $mensagem = "Já existe um tópico com este nome neste baralho.";
        } else {
            $query = "
                UPDATE appestudo.flashcard_topics 
                SET nome = $1, descricao = $2, status = $3
                WHERE id = $4";
            
            $result = pg_query_params($conexao, $query, array(
                $nome, $descricao, $status, $topico_id
            ));
            
            if ($result) {
                header("Location: ver_topicos.php?baralho=" . $topico['deck_id']);
                exit();
            } else {
                $mensagem = "Erro ao atualizar tópico. Tente novamente.";
            }
        }
    } else {
        $mensagem = "O nome do tópico é obrigatório.";
    }
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Tópico - <?php echo htmlspecialchars($topico['nome']); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Quicksand:wght@300..700&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Tektur:wght@400..900&family=Varela+Round&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #000080;
            --paper-color: #FFFFFF;
            --secondary-color: #4169E1;
            --background-color: #E8ECF3;
            --text-color: #2C3345;
            --border-color: #B8C2CC;
            --success-color: #28a745;
        }

        body {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 40px;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .form-container {
            background: var(--paper-color);
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .breadcrumb {
            color: var(--secondary-color);
            margin-bottom: 20px;
            font-style: italic;
        }

        h1 {
            color: var(--primary-color);
            margin-bottom: 30px;
            font-size: 1.8rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            color: var(--primary-color);
            margin-bottom: 5px;
            font-weight: 500;
        }

        input[type="text"],
        textarea,
        select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-family: inherit;
            font-size: 1rem;
            color: var(--text-color);
            box-sizing: border-box;
        }

        textarea {
            min-height: 100px;
            resize: vertical;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 10px;
        }

        .checkbox-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
        }

        .actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        .btn {
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-family: inherit;
            font-size: 1rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            border: none;
        }

        .btn-secondary {
            background: var(--paper-color);
            color: var(--primary-color);
            border: 1px solid var(--primary-color);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .error-message {
            background: #FEE;
            border: 1px solid #FAA;
            color: #A00;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .btn-back {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 50px;
            height: 50px;
            background: var(--paper-color);
            border: 2px solid var(--border-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.2s;
            z-index: 1000;
        }

        .btn-back:hover {
            transform: translateY(-2px);
            background: var(--primary-color);
            color: white;
        }

        @media (max-width: 768px) {
            body {
                padding: 20px;
            }

            .actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* Novo estilo para o toggle switch */
        .switch-container {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--success-color);
        }

        input:focus + .slider {
            box-shadow: 0 0 1px var(--success-color);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .switch-label {
            font-size: 1rem;
            color: var(--text-color);
            font-weight: 500;
        }

        .switch-description {
            font-size: 0.85rem;
            color: #666;
            margin-top: 4px;
        }

        .switch-text {
            flex: 1;
        }

        .status-icon {
            margin-right: 6px;
            font-size: 1.1rem;
        }

        .status-active {
            color: var(--success-color);
        }

        .status-inactive {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <a href="ver_topicos.php?baralho=<?php echo $topico['deck_id']; ?>" class="btn-back">
        <i class="fas fa-arrow-left"></i>
    </a>

    <div class="container">
        <div class="form-container">
            <div class="breadcrumb">
                <?php echo htmlspecialchars($topico['categoria_name']); ?> > 
                <?php echo htmlspecialchars($topico['materia_nome']); ?> >
                <?php echo htmlspecialchars($topico['deck_name']); ?>
            </div>

            <h1>Editar Tópico</h1>

            <?php if (!empty($mensagem)): ?>
                <div class="error-message">
                    <?php echo htmlspecialchars($mensagem); ?>
                </div>
            <?php endif; ?>

            <form method="POST">
                <div class="form-group">
                    <label for="nome">Nome do Tópico*</label>
                    <input type="text" id="nome" name="nome" required 
                           value="<?php echo htmlspecialchars($topico['nome']); ?>"
                           placeholder="Ex: Princípios Constitucionais">
                </div>

                <div class="form-group">
                    <label for="descricao">Descrição</label>
                    <textarea id="descricao" name="descricao" 
                              placeholder="Descreva o conteúdo deste tópico..."><?php echo htmlspecialchars($topico['descricao']); ?></textarea>
                </div>

                <div class="form-group">
        <div class="switch-container">
            <div class="switch-text">
                <div class="switch-label">
                    <i class="fas fa-circle status-icon <?php echo $topico['status'] ? 'status-active' : 'status-inactive'; ?>"></i>
                    Status do Tópico
                </div>
                <div class="switch-description">
                    <?php echo $topico['status'] ? 
                        'O tópico está visível para estudos' : 
                        'O tópico está oculto para estudos'; ?>
                </div>
            </div>
            <label class="switch">
                <input type="checkbox" name="status" <?php echo $topico['status'] ? 'checked' : ''; ?>>
                <span class="slider"></span>
            </label>
        </div>
    </div>

                <div class="actions">
                    <a href="ver_topicos.php?baralho=<?php echo $topico['deck_id']; ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Salvar Alterações
                    </button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>