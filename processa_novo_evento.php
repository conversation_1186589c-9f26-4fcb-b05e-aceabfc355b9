<?php
session_start();
include_once("conexao_POST.php");

header('Content-Type: application/json');

if (!isset($_SESSION['idusuario'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Usuário não está logado'
    ]);
    exit;
}

try {
    $idUsuario = $_SESSION['idusuario'];
    
    // Verificar se é uma requisição para excluir evento
    if (isset($_POST['eventoIdexcluir'])) {
        $eventoId = $_POST['eventoIdexcluir'];
        
        // Usar parâmetros preparados para a exclusão
        $query = "DELETE FROM appEstudo.agenda WHERE id = $1 AND usuario_idusuario = $2 RETURNING id";
        $stmt = pg_prepare($conexao, "excluir_evento", $query);
        $result = pg_execute($conexao, "excluir_evento", array($eventoId, $idUsuario));
        
        if ($result && pg_num_rows($result) > 0) {
            echo json_encode([
                'success' => true,
                'message' => 'Evento excluído com sucesso'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Erro ao excluir evento ou evento não encontrado'
            ]);
        }
        exit;
    }
    
    // Se não for exclusão, continua com a lógica de criação de evento
    $titulo = ($_POST['tipo_evento'] === 'Planejamento')
        ? $_POST['materia']
        : $_POST['titulo'];

    if ($titulo) {
        $dataInicio = $_POST['data_inicio'];
        $dataFim = $_POST['data_inicio'];
        $tipo_evento = $_POST['tipo_evento'];
        $detalhes = $_POST['detalhes'] ?? '';

        // Preparar e executar a query
        $query = "INSERT INTO appEstudo.agenda (usuario_idusuario, titulo, data_inicio, data_fim, tipo_evento, detalhes) 
                 VALUES ($1, $2, $3, $4, $5, $6)";
        $stmt = pg_prepare($conexao, "insere_evento", $query);
        $result = pg_execute($conexao, "insere_evento",
            array($idUsuario, $titulo, $dataInicio, $dataFim, $tipo_evento, $detalhes));

        if ($result) {
            echo json_encode([
                'success' => true,
                'message' => 'Evento criado com sucesso'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Erro ao criar evento: ' . pg_last_error($conexao)
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Título do evento não fornecido'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Erro ao processar evento: ' . $e->getMessage()
    ]);
}
?>