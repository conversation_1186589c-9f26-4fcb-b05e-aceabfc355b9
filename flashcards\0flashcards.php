<?php
// flashcards.php
session_start();
include_once("assets/config.php");
include_once("helpers.php");

// Verifica se o usuário está logado
if (!isset($_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit();
}

$usuario_id = $_SESSION['idusuario'];
$is_admin = checkAdmin($conexao, $usuario_id);

// Consulta para buscar o nome do usuário com base no ID
$query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = " . $_SESSION['idusuario'];
$resultado_nome = pg_query($conexao, $query_buscar_nome);

if ($resultado_nome && pg_num_rows($resultado_nome) > 0) {
    $row = pg_fetch_assoc($resultado_nome);
    $nome_usuario = $row['nome'];
} else {
    $nome_usuario = "Usuário";
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Flashcards - Sistema de Estudos</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700&family=Quicksand:wght@400;500;600;700&family=Varela+Round&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/flashcards.css">
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
</head>
<body>
    <div class="header">
        <div class="header-left">
            <div class="logo">
                <img src="../logo/logo_vertical_azul.png" alt="Estudo Off" class="logo-light">
                <img src="../logo/logo_vertical.png" alt="Estudo Off" class="logo-dark">
            </div>
        </div>
        <div class="header-right">
            <div class="user-info">
                <i class="fas fa-user-circle"></i>
                <span class="user-name"><?php echo htmlspecialchars($nome_usuario); ?></span>
            </div>

            <div class="theme-toggle">
                <button id="theme-toggle-btn" class="theme-btn" aria-label="Alternar tema">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </div>
    </div>

    <a href="index.php" class="btn-back" title="Voltar">
        <i class="fas fa-arrow-left"></i>
    </a>

    <div class="container" id="app" v-cloak>
        <div class="clean-container">
            <div class="header-icon">
                <i class="fas fa-layer-group"></i>
            </div>

            <h1>Flashcards</h1>

            <div class="controls">
                <div class="control-group">
                    <?php if ($is_admin): ?>
                    <a href="criar_categoria.php" class="btn">
                        <i class="fas fa-plus"></i>
                        Nova Categoria
                    </a>
                    <?php endif; ?>
                    <a href="revisoes.php" class="btn">
                        <i class="fas fa-sync"></i>
                        Revisões Pendentes
                    </a>
                </div>
                <div class="search" v-if="categories.length > 0">
                    <input 
                        type="text" 
                        v-model="searchQuery" 
                        placeholder="Buscar categorias..." 
                    >
                </div>
            </div>

            <!-- Lista de Categorias -->
            <div v-if="filteredCategories.length > 0" class="categories">
                <div v-for="(category, index) in filteredCategories" 
                     :key="category.id" 
                     class="category-card fade-in"
                     :style="{'--index': index}">
                    <div class="category-header">
                        <h2 class="category-title">{{ category.categoria }}</h2>
                        <div class="category-stats">
                            <span><i class="fas fa-layer-group"></i> {{ category.total_baralhos }}</span>
                            <span><i class="fas fa-clone"></i> {{ category.total_cards }}</span>
                        </div>
                    </div>
                    <p class="category-description">{{ category.descricao }}</p>
                    <div class="category-actions">
                        <a :href="'ver_baralhos.php?categoria=' + category.id" class="btn">
                            <i class="fas fa-eye"></i>
                            Baralhos
                        </a>
                        <a :href="'estudar.php?categoria=' + category.id" class="btn">
                            <i class="fas fa-graduation-cap"></i>
                            Estudar
                        </a>
                        <a v-if="isAdmin" :href="'editar_categoria.php?id=' + category.id" class="btn btn-secondary">
                            <i class="fas fa-edit"></i>
                            Editar
                        </a>
                    </div>
                </div>
            </div>

            <!-- Estado Vazio -->
            <div v-else-if="!isLoading" class="no-categories fade-in" style="--index: 0">
                <i class="fas fa-inbox"></i>
                <h3>Nenhuma categoria encontrada</h3>
                <p>{{ searchQuery ? 'Nenhuma categoria corresponde à sua busca.' : 'Comece criando uma nova categoria para seus flashcards!' }}</p>
                <?php if ($is_admin): ?>
                <a v-if="!searchQuery" href="criar_categoria.php" class="btn">
                    <i class="fas fa-plus"></i>
                    Criar Categoria
                </a>
                <?php endif; ?>
            </div>

            <!-- Estado de Carregamento -->
            <div v-if="isLoading" class="categories">
                <div v-for="i in 3" :key="i" class="category-card loading-shimmer" style="height: 220px;"></div>
            </div>
        </div>
        
        <div class="footer">
            <p>© <?php echo date('Y'); ?> Sistema de Flashcards | Todos os direitos reservados</p>
        </div>
    </div>

    <!-- Vue.js via CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/vue/3.2.47/vue.global.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // 1. Aplica o tema salvo, se existir
                const savedTheme = localStorage.getItem('theme');
                if (savedTheme) {
                    document.documentElement.setAttribute('data-theme', savedTheme);
                    updateThemeIcon(savedTheme);
                }
                
                // 2. Função de atualização do ícone
                function updateThemeIcon(theme) {
                    const icon = document.querySelector('#theme-toggle-btn i');
                    if (icon) {
                        if (theme === 'dark') {
                            icon.className = 'fas fa-sun';
                        } else {
                            icon.className = 'fas fa-moon';
                        }
                    }
                }
                
                // 3. Função simples de alternância de tema
                function toggleTheme() {
                    const html = document.documentElement;
                    const currentTheme = html.getAttribute('data-theme') || 'light';
                    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                    
                    console.log(`Alternando tema: ${currentTheme} → ${newTheme}`);
                    
                    // Aplica o tema
                    html.setAttribute('data-theme', newTheme);
                    localStorage.setItem('theme', newTheme);
                    updateThemeIcon(newTheme);
                }
                
                // 4. Configura o botão de tema - com tempo suficiente para o DOM carregar
                setTimeout(function() {
                    const themeBtn = document.getElementById('theme-toggle-btn');
                    if (themeBtn) {
                        console.log('Botão de tema encontrado e configurado');
                        themeBtn.onclick = toggleTheme;
                    } else {
                        console.error('Botão de tema não encontrado!');
                    }
                }, 100);
            } catch (e) {
                console.error('Erro ao configurar tema:', e);
            }
        });

        // Dados PHP para Vue
        const isAdmin = <?php echo $is_admin ? 'true' : 'false'; ?>;
        
        // Inicialização do Vue
        const { createApp, ref, computed, onMounted } = Vue;
        
        createApp({
            setup() {
                const categories = ref([]);
                const isLoading = ref(true);
                const error = ref(null);
                const searchQuery = ref('');
                
                // Categorias filtradas por busca
                const filteredCategories = computed(() => {
                    if (!searchQuery.value) return categories.value;
                    
                    const query = searchQuery.value.toLowerCase();
                    return categories.value.filter(category => 
                        category.categoria.toLowerCase().includes(query) ||
                        category.descricao.toLowerCase().includes(query)
                    );
                });
                
                // Função para carregar as categorias
                const carregarCategorias = async () => {
                    try {
                        isLoading.value = true;
                        const response = await fetch('categorias_api.php');
                        
                        if (!response.ok) {
                            throw new Error('Falha ao carregar categorias');
                        }
                        
                        const data = await response.json();
                        categories.value = data;
                    } catch (err) {
                        console.error('Erro:', err);
                        error.value = 'Não foi possível carregar as categorias. Por favor, tente novamente mais tarde.';
                    } finally {
                        isLoading.value = false;
                    }
                };
                
                onMounted(() => {
                    carregarCategorias();
                });
                
                return {
                    categories,
                    filteredCategories,
                    isLoading,
                    error,
                    searchQuery,
                    isAdmin
                };
            }
        }).mount('#app');
    </script>
</body>
</html>