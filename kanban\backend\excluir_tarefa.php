<?php
session_start();
require_once '../../conexao_POST.php';

header('Content-Type: application/json');

try {
    $dados = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($dados['id'])) {
        throw new Exception('ID da tarefa não fornecido');
    }

    $sql = "DELETE FROM appestudo.kanban_tarefas 
            WHERE id = $1 AND usuario_id = $2";

    $result = pg_query_params($conexao, $sql, array(
        $dados['id'],
        $_SESSION['idusuario']
    ));

    if (!$result) {
        throw new Exception(pg_last_error($conexao));
    }

    if (pg_affected_rows($result) === 0) {
        throw new Exception('Tarefa não encontrada ou sem permissão');
    }

    echo json_encode(['success' => true]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'erro' => $e->getMessage()]);
}
