<?php
error_reporting(0);
header('Content-Type: application/json');

try {
    include 'conexao_POST.php';
    session_start();

    if (!isset($_SESSION['idusuario'])) {
        throw new Exception('Usuário não está logado');
    }

    if (!isset($_POST['evento_id'])) {
        throw new Exception('ID do evento não fornecido');
    }

    $idUsuario = $_SESSION['idusuario'];
    $eventoId = (int)$_POST['evento_id'];

    // Buscar detalhes do evento
    $sql = "SELECT a.*, m.nome as nome_materia, m.cor as cor_materia, m.idmateria as materia_idmateria 
            FROM appestudo.agenda a 
            LEFT JOIN appestudo.planejamento p ON p.usuario_idusuario = a.usuario_idusuario
            LEFT JOIN appestudo.planejamento_materia pm ON pm.planejamento_idplanejamento = p.idplanejamento
            LEFT JOIN appestudo.materia m ON m.idmateria = pm.materia_idmateria 
            WHERE a.id = $1 
            AND a.usuario_idusuario = $2
            AND a.tipo_evento = 'Planejamento'
            AND a.titulo = m.nome";

    $result = pg_query_params($conexao, $sql, array($eventoId, $idUsuario));

    if (!$result) {
        throw new Exception("Erro na query: " . pg_last_error($conexao));
    }

    if (pg_num_rows($result) > 0) {
        $evento = pg_fetch_assoc($result);
        $data_inicio = new DateTime($evento['data_inicio']);
        $data_fim = new DateTime($evento['data_fim']);

        $dados = array(
            'id' => $evento['id'],
            'title' => $evento['titulo'],
            'tipo' => $evento['tipo_evento'],
            'detalhes' => $evento['detalhes'],
            'realizado' => $evento['realizado'],
            'data_inicio' => $data_inicio->format('d/m/Y H:i'),
            'data_fim' => $data_fim->format('d/m/Y H:i'),
            'nome_materia' => $evento['nome_materia'],
            'cor_materia' => $evento['cor_materia'],
            'materia_idmateria' => $evento['materia_idmateria']
        );

        echo json_encode($dados);
    } else {
        // Se não encontrar com a condição de título = nome_materia, tentar buscar sem essa condição
        $sql_fallback = "SELECT a.*, m.nome as nome_materia, m.cor as cor_materia, m.idmateria as materia_idmateria 
                        FROM appestudo.agenda a 
                        LEFT JOIN appestudo.planejamento p ON p.usuario_idusuario = a.usuario_idusuario
                        LEFT JOIN appestudo.planejamento_materia pm ON pm.planejamento_idplanejamento = p.idplanejamento
                        LEFT JOIN appestudo.materia m ON m.idmateria = pm.materia_idmateria 
                        WHERE a.id = $1 
                        AND a.usuario_idusuario = $2";

        $result_fallback = pg_query_params($conexao, $sql_fallback, array($eventoId, $idUsuario));

        if (pg_num_rows($result_fallback) > 0) {
            $evento = pg_fetch_assoc($result_fallback);
            $data_inicio = new DateTime($evento['data_inicio']);
            $data_fim = new DateTime($evento['data_fim']);

            $dados = array(
                'id' => $evento['id'],
                'title' => $evento['titulo'],
                'tipo' => $evento['tipo_evento'],
                'detalhes' => $evento['detalhes'],
                'realizado' => $evento['realizado'],
                'data_inicio' => $data_inicio->format('d/m/Y H:i'),
                'data_fim' => $data_fim->format('d/m/Y H:i'),
                'nome_materia' => $evento['titulo'], // Usar o título como nome da matéria
                'cor_materia' => $evento['cor_materia'] ?? '#FFD700', // Cor padrão para planejamento
                'materia_idmateria' => $evento['materia_idmateria']
            );

            echo json_encode($dados);
        } else {
            echo json_encode([
                'erro' => 'Evento não encontrado',
                'id_buscado' => $eventoId
            ]);
        }
    }

} catch (Exception $e) {
    echo json_encode([
        'erro' => $e->getMessage()
    ]);
}
?>