<?php
// includes/functions.php

/**
 * Verifica se existe uma mensagem de sucesso na URL e exibe o alerta apropriado
 * 
 * @return string|null HTML do alerta ou null
 */
function mostrarMensagemSucesso() {
    if (isset($_GET['success'])) {
        $tipo = intval($_GET['success']);
        $mensagem = '';
        
        switch ($tipo) {
            case 1:
                $mensagem = 'Medicamento adicionado com sucesso!';
                break;
            case 2:
                $mensagem = 'Medicamento atualizado com sucesso!';
                break;
            case 3:
                $mensagem = 'Uso de medicamento confirmado com sucesso!';
                break;
            case 4:
                $mensagem = 'Medicamento desativado com sucesso!';
                break;
            default:
                $mensagem = 'Operação realizada com sucesso!';
        }
        
        return '<div class="alert alert-success alert-dismissible fade show" role="alert">
                ' . $mensagem . '
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
            </div>';
    }
    
    if (isset($_GET['error'])) {
        $tipo = intval($_GET['error']);
        $mensagem = '';
        
        switch ($tipo) {
            case 1:
                $mensagem = 'Ocorreu um erro ao processar sua solicitação.';
                break;
            default:
                $mensagem = 'Ocorreu um erro inesperado.';
        }
        
        return '<div class="alert alert-danger alert-dismissible fade show" role="alert">
                ' . $mensagem . '
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
            </div>';
    }
    
    return null;
}

/**
 * Formata a data de acordo com o padrão brasileiro
 * 
 * @param string $data Data no formato Y-m-d
 * @return string Data formatada
 */
function formatarData($data) {
    return date('d/m/Y', strtotime($data));
}

/**
 * Calcula a próxima dose de um medicamento
 * 
 * @param int $medicamento_id ID do medicamento
 * @return string|null Próxima dose formatada ou null
 */
function proximaDose($medicamento_id) {
    $database = new Database();
    $conn = $database->getConnection();
    
    $query = "SELECT data_hora FROM appestudo.registros_uso 
              WHERE medicamento_id = :medicamento_id 
              AND confirmado = false 
              AND data_hora > NOW() 
              ORDER BY data_hora ASC 
              LIMIT 1";
    
    $stmt = $conn->prepare($query);
    $stmt->bindParam(":medicamento_id", $medicamento_id);
    $stmt->execute();
    
    $result = $stmt->fetch();
    
    if ($result) {
        return date('d/m/Y H:i', strtotime($result['data_hora']));
    }
    
    return null;
}
?>