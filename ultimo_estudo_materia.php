<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
include_once("conexao_POST.php"); // ajuste para o seu arquivo de conexão
session_start();

$idMateria = $_GET['id'] ?? '';
$idUsuario = $_SESSION['idusuario'] ?? null;

if (!$idMateria || !$idUsuario) {
    echo json_encode(['erro' => 'Parâmetros ausentes']);
    exit;
}

function timeToSeconds($time) {
    if (!$time) return 0;
    $parts = explode(':', $time);
    if (count($parts) === 3) {
        return ($parts[0] * 3600) + ($parts[1] * 60) + $parts[2];
    } elseif (count($parts) === 2) {
        return ($parts[0] * 3600) + ($parts[1] * 60);
    }
    return 0;
}

$query = "
    SELECT e.*, 
           m.nome AS materia,
           to_char(e.data, 'DD/MM/YYYY') as data_formatada,
           e.tempo_liquido,
           e.tempo_bruto,
           e.tempo_perdido,
           e.ponto_estudado,
           to_char(e.hora_inicio, 'HH24:MI') as hora_inicio,
           to_char(e.hora_fim, 'HH24:MI') as hora_fim,
           e.metodo,
           e.q_total,
           e.q_certa,
           e.q_errada,
           c.nome AS nome_curso,
           e.idcurso,
           e.revisoes,
           e.descricao,
           e.link_conteudo,
           m.cor AS cor_materia
    FROM appEstudo.estudos e
    INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
    INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
    LEFT JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria
    LEFT JOIN appEstudo.curso c ON e.idcurso = c.idcurso
    WHERE u.idusuario = $2 AND m.idmateria = $1
    ORDER BY e.data DESC, e.hora_fim DESC, e.hora_inicio DESC
    LIMIT 1
";
$result = pg_query_params($conexao, $query, [$idMateria, $idUsuario]);
$dados = pg_fetch_assoc($result);

if ($dados) {
    echo json_encode([
        'data' => $dados['data_formatada'],
        'tempo' => $dados['tempo_liquido'] ?? '',
        'observacao' => $dados['descricao'] ?? '',
        'tipo' => $dados['metodo'] ?? '',
        'nome' => $dados['materia'] ?? '',
        'cor' => $dados['cor_materia'] ?? '',
        'tempo_inicio' => $dados['hora_inicio'] ?? '',
        'tempo_fim' => $dados['hora_fim'] ?? '',
        'tempo_perdido' => $dados['tempo_perdido'] ?? '',
        'curso' => $dados['nome_curso'] ?? '',
        'ponto_estudado' => $dados['ponto_estudado'] ?? '',
        'tempo_liquido' => $dados['tempo_liquido'] ?? '',
        'tempo_perdido_horas' => $dados['tempo_perdido'] ? floor(timeToSeconds($dados['tempo_perdido']) / 3600) : '',
        'tempo_perdido_minutos' => $dados['tempo_perdido'] ? floor((timeToSeconds($dados['tempo_perdido']) % 3600) / 60) : '',
        'tempo_liquido_horas' => $dados['tempo_liquido'] ? floor(timeToSeconds($dados['tempo_liquido']) / 3600) : '',
        'tempo_liquido_minutos' => $dados['tempo_liquido'] ? floor((timeToSeconds($dados['tempo_liquido']) % 3600) / 60) : '',
        'tempo_bruto' => $dados['tempo_bruto'] ?? '',
        'questoes_total' => $dados['q_total'] ?? '',
        'questoes_certas' => $dados['q_certa'] ?? '',
        'questoes_erradas' => $dados['q_errada'] ?? '',
        'revisoes' => $dados['revisoes'] ?? '',
        'idcurso' => $dados['idcurso'] ?? '',
        'link_conteudo' => $dados['link_conteudo'] ?? '',
    ]);
} else {
    echo json_encode(['erro' => 'Nenhum registro encontrado']);
}