<?php
/**
 * Configuração central de sessão para o sistema
 * Este arquivo deve ser incluído no início de to<PERSON> as páginas que usam sessão
 */

// Configurações de sessão - 8 horas (28800 segundos)
ini_set('session.gc_maxlifetime', 28800); // 8 horas
ini_set('session.cookie_lifetime', 28800); // 8 horas
ini_set('session.use_strict_mode', 1); // Modo estrito
ini_set('session.use_only_cookies', 1); // Usar apenas cookies
ini_set('session.cookie_httponly', 1); // Cookie HTTP only
ini_set('session.cookie_secure', 0); // 0 para HTTP, 1 para HTTPS
ini_set('session.cookie_samesite', 'Lax'); // Proteção CSRF

// Configurar parâmetros do cookie de sessão
session_set_cookie_params([
    'lifetime' => 28800, // 8 horas
    'path' => '/',
    'domain' => '',
    'secure' => false, // Mude para true se usar HTTPS
    'httponly' => true,
    'samesite' => 'Lax'
]);

// Função para verificar se a sessão é válida
function verificarSessaoValida() {
    return isset($_SESSION['idusuario']) && 
           isset($_SESSION['validacao']) && 
           $_SESSION['validacao'] === true;
}

// Função para renovar a sessão
function renovarSessao() {
    if (session_status() === PHP_SESSION_ACTIVE) {
        // Atualizar timestamp da sessão
        $_SESSION['ultimo_acesso'] = time();
        
        // Regenerar ID da sessão para segurança
        if (!isset($_SESSION['sessao_regenerada']) || 
            (time() - $_SESSION['sessao_regenerada']) > 1800) { // 30 minutos
            session_regenerate_id(true);
            $_SESSION['sessao_regenerada'] = time();
        }
    }
}

// Função para limpar dados de sessão expirada
function limparDadosSessaoExpirada() {
    if (isset($_SESSION['dados_formulario_pendente'])) {
        unset($_SESSION['dados_formulario_pendente']);
    }
    if (isset($_SESSION['sessao_expirada'])) {
        unset($_SESSION['sessao_expirada']);
    }
}

// Função para salvar dados do formulário em caso de sessão expirada
function salvarDadosFormulario($dados) {
    $_SESSION['dados_formulario_pendente'] = $dados;
    $_SESSION['sessao_expirada'] = true;
    $_SESSION['timestamp_expiracao'] = time();
}

// Função para verificar se há dados pendentes
function temDadosPendentes() {
    return isset($_SESSION['dados_formulario_pendente']) && 
           isset($_SESSION['sessao_expirada']) && 
           $_SESSION['sessao_expirada'] === true;
}

// Função para recuperar dados pendentes
function recuperarDadosPendentes() {
    if (temDadosPendentes()) {
        $dados = $_SESSION['dados_formulario_pendente'];
        limparDadosSessaoExpirada();
        return $dados;
    }
    return null;
}

// Função para verificar timeout de sessão
function verificarTimeoutSessao($timeout_minutos = 480) { // 8 horas = 480 minutos
    if (isset($_SESSION['ultimo_acesso'])) {
        $tempo_decorrido = time() - $_SESSION['ultimo_acesso'];
        $timeout_segundos = $timeout_minutos * 60;
        
        if ($tempo_decorrido > $timeout_segundos) {
            return false; // Sessão expirada
        }
    }
    return true; // Sessão válida
}

// Função para redirecionar com dados preservados
function redirecionarComDadosPreservados($url, $dados = null) {
    if ($dados) {
        salvarDadosFormulario($dados);
    }
    header("Location: $url?erro=sessao_expirada");
    exit();
}

// Função para log de eventos de sessão
function logEventoSessao($tipo, $usuario_id = null) {
    $ip = $_SERVER['REMOTE_ADDR'] ?? 'desconhecido';
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'desconhecido';
    $timestamp = date('Y-m-d H:i:s');
    
    $mensagem = "[$timestamp] [$tipo] IP: $ip | User-Agent: $user_agent";
    if ($usuario_id) {
        $mensagem .= " | Usuario: $usuario_id";
    }
    
    error_log($mensagem);
}

// Inicializar sessão se não estiver ativa
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Renovar sessão se válida
if (verificarSessaoValida()) {
    renovarSessao();
}
?> 