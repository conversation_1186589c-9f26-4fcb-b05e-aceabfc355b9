<?php
session_start();

// Headers para debug
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Log de debug
error_log("=== DEBUG API PROGRESSO ===");
error_log("Método: " . $_SERVER['REQUEST_METHOD']);
error_log("Sessão ativa: " . (session_status() === PHP_SESSION_ACTIVE ? 'Sim' : 'Não'));
error_log("Usuário na sessão: " . (isset($_SESSION['idusuario']) ? $_SESSION['idusuario'] : 'Não definido'));

// Verificar se o usuário está autenticado
if (!isset($_SESSION['idusuario'])) {
    error_log("ERRO: Usuário não autenticado");
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit;
}

$usuario_id = $_SESSION['idusuario'];
error_log("Usuário ID: " . $usuario_id);

// Tentar conectar ao banco
try {
    require_once '../../conexao_POST.php';
    error_log("Conexão com banco estabelecida");
} catch (Exception $e) {
    error_log("ERRO na conexão: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['erro' => 'Erro de conexão com banco', 'detalhes' => $e->getMessage()]);
    exit;
}

// Verificar se a conexão está ativa
if (!$conexao) {
    error_log("ERRO: Conexão PostgreSQL falhou");
    http_response_code(500);
    echo json_encode(['erro' => 'Conexão com banco falhou']);
    exit;
}

$metodo = $_SERVER['REQUEST_METHOD'];
error_log("Processando método: " . $metodo);

if ($metodo === 'POST') {
    // Obter dados do corpo da requisição
    $input = file_get_contents('php://input');
    error_log("Input recebido: " . $input);
    
    $dados = json_decode($input, true);
    error_log("Dados decodificados: " . json_encode($dados));
    
    if (!$dados) {
        error_log("ERRO: Falha ao decodificar JSON");
        http_response_code(400);
        echo json_encode(['erro' => 'JSON inválido', 'input' => $input]);
        exit;
    }
    
    if (!isset($dados['artigo_numero']) || !isset($dados['lido'])) {
        error_log("ERRO: Dados incompletos - artigo_numero ou lido ausente");
        http_response_code(400);
        echo json_encode(['erro' => 'Dados incompletos', 'dados' => $dados]);
        exit;
    }
    
    $artigo_numero = $dados['artigo_numero'];
    $lido = $dados['lido'] ? true : false;
    
    error_log("Artigo: $artigo_numero, Lido: " . ($lido ? 'true' : 'false'));
    
    // Verificar se existe lei CF
    $query_cf = "SELECT id FROM appestudo.lexjus_leis WHERE codigo = 'CF'";
    $result_cf = pg_query($conexao, $query_cf);
    
    if (!$result_cf) {
        error_log("ERRO na query CF: " . pg_last_error($conexao));
        http_response_code(500);
        echo json_encode(['erro' => 'Erro ao buscar lei CF', 'pg_error' => pg_last_error($conexao)]);
        exit;
    }
    
    if (pg_num_rows($result_cf) == 0) {
        error_log("ERRO: Lei CF não encontrada");
        http_response_code(500);
        echo json_encode(['erro' => 'Lei CF não encontrada']);
        exit;
    }
    
    $cf_row = pg_fetch_assoc($result_cf);
    $lei_id = $cf_row['id'];
    error_log("Lei CF encontrada com ID: " . $lei_id);
    
    // Verificar se tabela lexjus_progresso existe
    $query_table = "SELECT table_name FROM information_schema.tables 
                    WHERE table_schema = 'appestudo' AND table_name = 'lexjus_progresso'";
    $result_table = pg_query($conexao, $query_table);
    
    if (!$result_table || pg_num_rows($result_table) == 0) {
        error_log("ERRO: Tabela lexjus_progresso não existe");
        http_response_code(500);
        echo json_encode(['erro' => 'Tabela lexjus_progresso não existe']);
        exit;
    }
    
    error_log("Tabela lexjus_progresso existe");
    
    // Tentar inserir/atualizar
    if ($lido) {
        $query = "INSERT INTO appestudo.lexjus_progresso (usuario_id, lei_id, artigo_numero, lido)
                 VALUES ($1, $2, $3, $4)
                 ON CONFLICT (usuario_id, lei_id, artigo_numero)
                 DO UPDATE SET lido = $4, data_leitura = CURRENT_TIMESTAMP";
        
        $params = [$usuario_id, $lei_id, $artigo_numero, $lido];
    } else {
        $query = "DELETE FROM appestudo.lexjus_progresso
                 WHERE usuario_id = $1 AND lei_id = $2 AND artigo_numero = $3";
        
        $params = [$usuario_id, $lei_id, $artigo_numero];
    }
    
    error_log("Query: " . $query);
    error_log("Parâmetros: " . json_encode($params));
    
    $result = pg_query_params($conexao, $query, $params);
    
    if (!$result) {
        $pg_error = pg_last_error($conexao);
        error_log("ERRO na query: " . $pg_error);
        http_response_code(500);
        echo json_encode([
            'erro' => 'Erro ao executar query',
            'pg_error' => $pg_error,
            'query' => $query,
            'params' => $params
        ]);
        exit;
    }
    
    error_log("Query executada com sucesso");
    
    // Verificar se foi inserido/atualizado
    $verify_query = "SELECT * FROM appestudo.lexjus_progresso 
                     WHERE usuario_id = $1 AND lei_id = $2 AND artigo_numero = $3";
    $verify_result = pg_query_params($conexao, $verify_query, [$usuario_id, $lei_id, $artigo_numero]);
    
    if ($verify_result && pg_num_rows($verify_result) > 0) {
        $row = pg_fetch_assoc($verify_result);
        error_log("Verificação: Registro encontrado - ID: " . $row['id'] . ", Lido: " . $row['lido']);
    } else {
        error_log("Verificação: Nenhum registro encontrado (normal se foi DELETE)");
    }
    
    echo json_encode(['sucesso' => true, 'debug' => 'Operação concluída']);
    
} else if ($metodo === 'GET') {
    // Implementação GET simplificada
    echo json_encode(['progresso' => [], 'debug' => 'GET funcionando']);
    
} else {
    error_log("ERRO: Método não permitido: " . $metodo);
    http_response_code(405);
    echo json_encode(['erro' => 'Método não permitido']);
}

error_log("=== FIM DEBUG API PROGRESSO ===");
?>
