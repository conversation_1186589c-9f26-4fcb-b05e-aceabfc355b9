<?php
session_start();
require_once('../includes/auth.php');
require_once('../conexao_POST.php');
require_once('../cadastros/includes/verify_admin.php');

// Configurar codificação
pg_set_client_encoding($conexao, "UTF8");

// Verifica se é o super admin
verificarSuperAdmin($conexao, false);

// Recuperar mensagem da sessão, se existir
$mensagem = '';
if (isset($_SESSION['mensagem'])) {
    $mensagem = $_SESSION['mensagem'];
    unset($_SESSION['mensagem']); // Limpa a mensagem da sessão
}

// Processar formulário de nova notificação
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['acao']) && $_POST['acao'] === 'criar') {
        $titulo = trim($_POST['titulo']);
        $mensagem_notif = trim($_POST['mensagem']);
        $tipo = $_POST['tipo'];
        $data_expiracao = $_POST['data_expiracao'];
        $destinatarios = isset($_POST['destinatarios']) ? $_POST['destinatarios'] : [];
        
        // Validações básicas
        if (empty($titulo) || empty($mensagem_notif) || empty($data_expiracao)) {
            $_SESSION['mensagem'] = "Todos os campos obrigatórios devem ser preenchidos.";
        } else {
            try {
                // Iniciar transação
                pg_query($conexao, "BEGIN");
                
                // Verificar se é global (true/false)
                $is_global = false;
                if (!empty($destinatarios)) {
                    $is_global = in_array('global', $destinatarios);
                }
                
                // Inserir notificação
                $query = "
                    INSERT INTO appestudo.notificacoes 
                    (titulo, mensagem, tipo, data_expiracao, status, is_global, criado_por)
                    VALUES ($1, $2, $3, $4, true, $5, $6)
                    RETURNING id";
                
                $result = pg_query_params(
                    $conexao,
                    $query,
                    array(
                        $titulo,
                        $mensagem_notif,
                        $tipo,
                        $data_expiracao,
                        $is_global ? 't' : 'f', // Convertendo boolean para 't' ou 'f'
                        $_SESSION['idusuario']
                    )
                );

                if (!$result) {
                    throw new Exception(pg_last_error($conexao));
                }

                $notificacao = pg_fetch_assoc($result);
                $notificacao_id = $notificacao['id'];

                // Se não for global, inserir destinatários específicos
                if (!$is_global && !empty($destinatarios)) {
                    $values = [];
                    $params = [];
                    $param_count = 1;

                    foreach ($destinatarios as $usuario_id) {
                        if ($usuario_id !== 'global') {
                            $values[] = "($" . $param_count . ", $" . ($param_count + 1) . ")";
                            $params[] = $notificacao_id;
                            $params[] = $usuario_id;
                            $param_count += 2;
                        }
                    }

                    if (!empty($values)) {
                        $query_dest = "
                            INSERT INTO appestudo.notificacoes_usuarios 
                            (notificacao_id, usuario_id)
                            VALUES " . implode(", ", $values);
                        
                        $result_dest = pg_query_params($conexao, $query_dest, $params);
                        
                        if (!$result_dest) {
                            throw new Exception(pg_last_error($conexao));
                        }
                    }
                }

                // Commit da transação
                pg_query($conexao, "COMMIT");
                
                $_SESSION['mensagem'] = "Notificação criada com sucesso!";
                header('Location: ' . $_SERVER['PHP_SELF']);
                exit();

            } catch (Exception $e) {
                // Rollback em caso de erro
                pg_query($conexao, "ROLLBACK");
                $_SESSION['mensagem'] = "Erro ao criar notificação: " . $e->getMessage();
                error_log("Erro ao criar notificação: " . $e->getMessage());
            }
        }
    }
    if (isset($_POST['acao']) && isset($_POST['id'])) {
        $id = (int)$_POST['id'];
        
        if ($id <= 0) {
            $_SESSION['mensagem'] = "ID inválido";
        } else {
            $novo_status = ($_POST['acao'] === 'ativar') ? 't' : 'f';  // Alterado de 'true'/'false' para 't'/'f'
            
            $query = "
                UPDATE appestudo.notificacoes 
                SET status = $1 
                WHERE id = $2 
                RETURNING id, titulo";
                
            $result = pg_query_params($conexao, $query, array($novo_status, $id));
            
            if ($result) {
                $row = pg_fetch_assoc($result);
                $acao_texto = $novo_status === 't' ? "ativada" : "desativada";
                $_SESSION['mensagem'] = "Notificação '{$row['titulo']}' {$acao_texto} com sucesso!";
                
                header('Location: ' . $_SERVER['PHP_SELF']);
                exit();
            } else {
                $_SESSION['mensagem'] = "Erro ao atualizar notificação: " . pg_last_error($conexao);
            }
        }
    }
    if (isset($_POST['acao']) && $_POST['acao'] === 'editar') {
        $id = (int)$_POST['notificacao_id'];
        $titulo = trim($_POST['titulo']);
        $mensagem_notif = trim($_POST['mensagem']);
        $tipo = $_POST['tipo'];
        $data_expiracao = $_POST['data_expiracao'];
        $destinatarios = isset($_POST['destinatarios']) ? $_POST['destinatarios'] : [];
        
        if (empty($titulo) || empty($mensagem_notif) || empty($data_expiracao)) {
            $_SESSION['mensagem'] = "Todos os campos obrigatórios devem ser preenchidos.";
        } else {
            try {
                pg_query($conexao, "BEGIN");
                
                $is_global = !empty($destinatarios) && in_array('global', $destinatarios);
                
                // Atualizar notificação
                $query = "
                    UPDATE appestudo.notificacoes 
                    SET titulo = $1, 
                        mensagem = $2, 
                        tipo = $3, 
                        data_expiracao = $4, 
                        is_global = $5
                    WHERE id = $6";
                
                $result = pg_query_params(
                    $conexao,
                    $query,
                    array(
                        $titulo,
                        $mensagem_notif,
                        $tipo,
                        $data_expiracao,
                        $is_global ? 't' : 'f',
                        $id
                    )
                );

                if (!$result) {
                    throw new Exception(pg_last_error($conexao));
                }

                // Atualizar destinatários específicos
                if (!$is_global) {
                    // Remover destinatários antigos
                    pg_query_params($conexao, 
                        "DELETE FROM appestudo.notificacoes_usuarios WHERE notificacao_id = $1",
                        array($id)
                    );

                    // Inserir novos destinatários
                    if (!empty($destinatarios)) {
                        $values = [];
                        $params = [];
                        $param_count = 1;

                        foreach ($destinatarios as $usuario_id) {
                            if ($usuario_id !== 'global') {
                                $values[] = "($" . $param_count . ", $" . ($param_count + 1) . ")";
                                $params[] = $id;
                                $params[] = $usuario_id;
                                $param_count += 2;
                            }
                        }

                        if (!empty($values)) {
                            $query_dest = "
                                INSERT INTO appestudo.notificacoes_usuarios 
                                (notificacao_id, usuario_id)
                                VALUES " . implode(", ", $values);
                            
                            $result_dest = pg_query_params($conexao, $query_dest, $params);
                            
                            if (!$result_dest) {
                                throw new Exception(pg_last_error($conexao));
                            }
                        }
                    }
                }

                pg_query($conexao, "COMMIT");
                
                $_SESSION['mensagem'] = "Notificação atualizada com sucesso!";
                header('Location: ' . $_SERVER['PHP_SELF']);
                exit();

            } catch (Exception $e) {
                pg_query($conexao, "ROLLBACK");
                $_SESSION['mensagem'] = "Erro ao atualizar notificação: " . $e->getMessage();
                error_log("Erro ao atualizar notificação: " . $e->getMessage());
            }
        }
    }
}

// Buscar notificações com ordenação mais relevante
$query_notificacoes = "
    SELECT 
        n.*,
        CASE 
            WHEN n.status = true AND n.data_expiracao > CURRENT_TIMESTAMP THEN 'Ativo'
            ELSE 'Inativo'
        END as status_texto,
        CASE 
            WHEN n.is_global = true THEN 'Global'
            ELSE 'Específico'
        END as tipo_destino,
        CASE 
            WHEN n.data_expiracao < CURRENT_TIMESTAMP THEN true
            ELSE false
        END as expirado,
        CASE
            WHEN n.is_global = true THEN 'Todos os usuários'
            ELSE (
                SELECT string_agg(u.nome, ', ')
                FROM appestudo.notificacoes_usuarios nu
                JOIN appestudo.usuario u ON u.idusuario = nu.usuario_id
                WHERE nu.notificacao_id = n.id
            )
        END as destinatarios_nomes
    FROM appestudo.notificacoes n 
    ORDER BY 
        CASE 
            WHEN n.status = true AND n.data_expiracao > CURRENT_TIMESTAMP THEN 0 
            ELSE 1 
        END,
        n.data_criacao DESC";

$result_notificacoes = pg_query($conexao, $query_notificacoes);

if (!$result_notificacoes) {
    error_log("Erro ao buscar notificações: " . pg_last_error($conexao));
}

// Adicione antes do HTML
date_default_timezone_set('America/Sao_Paulo'); // Garante o timezone correto

$query_usuarios = "
    SELECT idusuario as id, nome 
    FROM appestudo.usuario 
    WHERE status = 'ativo' 
    ORDER BY nome ASC";
$result_usuarios = pg_query($conexao, $query_usuarios);

if (!$result_usuarios) {
    error_log("Erro ao buscar usuários: " . pg_last_error($conexao));
    $usuarios = array();
} else {
    $usuarios = pg_fetch_all($result_usuarios);
}

// Para edição, buscar usuários já selecionados
if (isset($_GET['id'])) {
    $query_selected = "
        SELECT usuario_id 
        FROM appestudo.notificacoes_usuarios 
        WHERE notificacao_id = $1";
    $result_selected = pg_query_params($conexao, $query_selected, array($_GET['id']));
    $selected_usuarios = array_column(pg_fetch_all($result_selected) ?: array(), 'usuario_id');
} else {
    $selected_usuarios = array();
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Notificações</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <style>
        :root {
            --primary: #00008B;
            --hover: #0000CD;
            --background: #f5f5f5;
            --card: #ffffff;
            --danger: #dc3545;
            --danger-hover: #c82333;
            --success: #28a745;
            --success-hover: #218838;
            --warning: #ffc107;
            --warning-hover: #e0a800;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-600: #6c757d;
            --gray-800: #343a40;
        }

        body {
            font-family: 'Quicksand', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: var(--background);
            color: var(--gray-800);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: var(--card);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--gray-200);
        }

        .page-header h1 {
            margin: 0;
            font-size: 2em;
            color: var(--primary);
        }

        .page-header i {
            margin-right: 15px;
            font-size: 1.8em;
            color: var(--primary);
        }

        .notification-form {
            background: var(--background);
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 30px;
            border: 1px solid var(--gray-300);
        }

        .form-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .form-header h2 {
            margin: 0;
            color: var(--gray-800);
            font-size: 1.5em;
        }

        .form-header i {
            margin-right: 10px;
            color: var(--primary);
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--gray-800);
        }

        input[type="text"],
        textarea,
        select,
        input[type="datetime-local"] {
            width: 100%;
            padding: 12px;
            border: 2px solid var(--gray-300);
            border-radius: 8px;
            box-sizing: border-box;
            font-family: 'Quicksand', sans-serif;
            transition: border-color 0.3s ease;
        }

        input[type="text"]:focus,
        textarea:focus,
        select:focus,
        input[type="datetime-local"]:focus {
            outline: none;
            border-color: var(--primary);
        }

        textarea {
            min-height: 120px;
            resize: vertical;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            font-size: 1em;
            transition: all 0.2s;
            font-family: 'Quicksand', sans-serif;
        }

        .btn-primary {
            background-color: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            background-color: var(--hover);
            transform: translateY(-2px);
        }

        .btn-danger {
            background-color: var(--danger);
            color: white;
        }

        .btn-danger:hover {
            background-color: var(--danger-hover);
            transform: translateY(-2px);
        }

        .btn-success {
            background-color: var(--success);
            color: white;
        }

        .btn-success:hover {
            background-color: var(--success-hover);
            transform: translateY(-2px);
        }

        .btn-warning {
            background-color: var(--warning);
            color: var(--gray-800);
        }

        .btn-warning:hover {
            background-color: var(--warning-hover);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background-color: var(--gray-600);
            color: white;
        }

        .btn-secondary:hover {
            background-color: var(--gray-800);
            transform: translateY(-2px);
        }

        .notifications-list {
            margin-top: 30px;
        }

        .notification-item {
            position: relative;
            border-left: 4px solid transparent;
        }
        
        .notification-item.active {
            border-left-color: var(--success);
        }
        
        .notification-item.inactive {
            border-left-color: var(--danger);
            opacity: 0.8;
        }
        
        .status-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .status-active {
            background-color: var(--success);
            color: white;
        }
        
        .status-inactive {
            background-color: var(--danger);
            color: white;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 8px;
        }
        
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .notification-item {
            background: var(--card);
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 15px;
            border: 1px solid var(--gray-300);
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .notification-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .notification-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .notification-title {
            font-size: 1.2em;
            font-weight: 600;
            color: var(--gray-800);
        }

        .notification-meta {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }

        .notification-meta span {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .badge i {
            font-size: 0.9em;
        }

        .badge-info { 
            background: #3498db;
            color: white;
        }

        .badge-warning { 
            background: var(--warning);
            color: var(--gray-800);
        }

        .badge-danger { 
            background: var(--danger);
            color: white;
        }

        .badge-success {
            background: var(--success);
            color: white;
        }

        .message {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 25px;
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .message i {
            font-size: 1.2em;
        }

        .notification-actions {
            margin-top: 15px;
            text-align: right;
        }

        .btn-action {
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 0.9em;
            display: inline-flex;
            align-items: center;
            gap: 5px;
        }

        .btn-secondary {
            background-color: var(--gray-600);
            color: white;
            margin-left: auto;
        }

        .btn-secondary:hover {
            background-color: var(--gray-800);
            transform: translateY(-2px);
        }

        .status-badge {
            margin-left: 10px;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85em;
            font-weight: 600;
        }

        .status-ativo {
            background-color: var(--success);
            color: white;
        }

        .status-inativo {
            background-color: var(--danger);
            color: white;
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .container {
                padding: 15px;
            }

            .notification-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .notification-actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <i class="fas fa-bell"></i>
            <h1>Gerenciar Notificações</h1>
            <a href="index.php" class="btn btn-secondary" style="margin-left: auto;">
                <i class="fas fa-arrow-left"></i> Voltar
            </a>
        </div>

        <?php if ($mensagem): ?>
            <div class="alert <?php echo strpos($mensagem, 'Erro') !== false ? 'alert-error' : ''; ?>">
                <?php echo htmlspecialchars($mensagem); ?>
            </div>
        <?php endif; ?>

        <div class="notification-form">
            <div class="form-header">
                <i class="fas fa-plus-circle"></i>
                <h2>Nova Notificação</h2>
            </div>
            
            <form method="POST">
                <input type="hidden" name="acao" value="criar">
                
                <div class="form-group">
                    <label for="titulo">
                        <i class="fas fa-heading"></i> Título
                    </label>
                    <input type="text" id="titulo" name="titulo" required maxlength="255">
                </div>

                <div class="form-group">
                    <label for="mensagem">
                        <i class="fas fa-comment-alt"></i> Mensagem
                    </label>
                    <textarea id="mensagem" name="mensagem" required></textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="tipo">
                            <i class="fas fa-info-circle"></i> Tipo
                        </label>
                        <select id="tipo" name="tipo" required>
                            <option value="info">Informação</option>
                            <option value="warning">Aviso</option>
                            <option value="danger">Alerta</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="data_expiracao">
                            <i class="fas fa-calendar-alt"></i> Data de Expiração
                        </label>
                        <input type="datetime-local" id="data_expiracao" name="data_expiracao" required>
                    </div>

                    <div class="form-group">
                        <label>
                            <i class="fas fa-users"></i> Destinatários
                        </label>
                        <div class="usuarios-container">
                            <div class="search-container">
                                <input 
                                    type="text" 
                                    id="searchUsuarios" 
                                    placeholder="Buscar usuários..." 
                                    class="search-input"
                                >
                                <i class="fas fa-search search-icon"></i>
                            </div>
                            <div class="usuarios-lista">
                                <!-- Seus checkboxes de usuários aqui -->
                                <?php
                                $query_usuarios = "SELECT idusuario, nome FROM appestudo.usuario WHERE status = 'ativo' ORDER BY nome";
                                $result_usuarios = pg_query($conexao, $query_usuarios);
                                while ($usuario = pg_fetch_assoc($result_usuarios)) {
                                    echo "<div class='usuario-item'>
                                            <input 
                                                type='checkbox' 
                                                name='destinatarios[]' 
                                                value='{$usuario['idusuario']}' 
                                                id='usuario_{$usuario['idusuario']}'>
                                            <label for='usuario_{$usuario['idusuario']}'>
                                                " . htmlspecialchars($usuario['nome']) . "
                                            </label>
                                        </div>";
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                </div>

                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-paper-plane"></i> Criar Notificação
                </button>
            </form>
        </div>

        <div class="notifications-list">
            <h2><i class="fas fa-list"></i> Notificações Existentes</h2>
            
            <?php if (pg_num_rows($result_notificacoes) > 0): ?>
                <?php while ($notificacao = pg_fetch_assoc($result_notificacoes)): ?>
                    <?php 
                    $tipo_classe = match($notificacao['tipo']) {
                        'info' => 'badge-info',
                        'warning' => 'badge-warning',
                        'danger' => 'badge-danger'
                    };
                    $expirado = strtotime($notificacao['data_expiracao']) < time();
                    $esta_ativo = $notificacao['status'] === 't' && !$expirado;
                    ?>
                    <div class="notification-item <?php echo $esta_ativo ? 'active' : 'inactive'; ?>" 
                         data-id="<?php echo $notificacao['id']; ?>">
                        <div class="notification-header">
                            <h3 class="notification-title">
                                <?php echo htmlspecialchars($notificacao['titulo']); ?>
                                <?php if ($expirado): ?>
                                    <span class="badge badge-expired">Expirado</span>
                                <?php endif; ?>
                            </h3>
                            <div class="notification-meta">
                                <span class="badge <?php echo $tipo_classe; ?>">
                                    <i class="fas fa-info-circle"></i>
                                    <?php 
                                    echo match($notificacao['tipo']) {
                                        'info' => 'Info',
                                        'warning' => 'Aviso',
                                        'danger' => 'Alerta',
                                        default => ucfirst($notificacao['tipo'])
                                    }; 
                                    ?>
                                </span>
                                <span class="badge <?php echo $esta_ativo ? 'badge-success' : 'badge-danger'; ?>">
                                    <?php echo $notificacao['status_texto']; ?>
                                </span>
                            </div>
                        </div>
                        <div class="notification-content">
                            <p><?php echo nl2br(htmlspecialchars($notificacao['mensagem'])); ?></p>
                            <div class="notification-details">
                                <span><i class="far fa-clock"></i> Criado: <?php echo date('d/m/Y H:i', strtotime($notificacao['data_criacao'])); ?></span>
                                <span><i class="fas fa-hourglass-end"></i> Expira: <?php echo date('d/m/Y H:i', strtotime($notificacao['data_expiracao'])); ?></span>
                                <span><i class="fas fa-users"></i> <?php echo $notificacao['tipo_destino']; ?></span>
                                <div class="destinatarios-info">
                                    <strong><i class="fas fa-user-check"></i> Destinatários:</strong>
                                    <span class="destinatarios-lista">
                                        <?php echo htmlspecialchars($notificacao['destinatarios_nomes'] ?: 'Nenhum destinatário específico'); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div class="notification-actions">
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="id" value="<?php echo $notificacao['id']; ?>">
                                <input type="hidden" name="acao" value="<?php echo $notificacao['status'] === 't' ? 'desativar' : 'ativar'; ?>">
                                <button type="submit" class="btn <?php echo $notificacao['status'] === 't' ? 'btn-danger' : 'btn-success'; ?>">
                                    <i class="fas <?php echo $notificacao['status'] === 't' ? 'fa-times-circle' : 'fa-check-circle'; ?>"></i>
                                    <?php echo $notificacao['status'] === 't' ? 'Desativar' : 'Ativar'; ?>
                                </button>
                            </form>
                            <button 
                                onclick="editarNotificacao(<?php 
                                    echo htmlspecialchars(json_encode([
                                        'id' => $notificacao['id'],
                                        'titulo' => $notificacao['titulo'],
                                        'mensagem' => $notificacao['mensagem'],
                                        'tipo' => $notificacao['tipo'],
                                        'data_expiracao' => $notificacao['data_expiracao'],
                                        'is_global' => $notificacao['is_global'] === 't'
                                    ])); 
                                ?>)" 
                                class="btn btn-edit">
                                <i class="fas fa-edit"></i> Editar
                            </button>
                            <button class="btn btn-danger btn-delete" onclick="confirmarExclusao(<?php echo $notificacao['id']; ?>)">
                                <i class="fas fa-trash-alt"></i> Excluir
                            </button>
                        </div>
                    </div>
                <?php endwhile; ?>
            <?php else: ?>
                <div class="no-notifications">
                    <i class="fas fa-bell-slash"></i>
                    <p>Nenhuma notificação encontrada</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <?php
    function getBadgeIcon($tipo) {
        switch ($tipo) {
            case 'info':
                return 'info-circle';
            case 'warning':
                return 'exclamation-triangle';
            case 'danger':
                return 'exclamation-circle';
            default:
                return 'bell';
        }
    }
    ?>
    <script>
    function confirmarExclusao(id) {
        Swal.fire({
            title: 'Confirmar exclusão?',
            text: "Esta ação não poderá ser revertida!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#dc3545',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Sim, excluir!',
            cancelButtonText: 'Cancelar'
        }).then((result) => {
            if (result.isConfirmed) {
                const formData = new FormData();
                formData.append('id', id);

                fetch('excluir_notificacao.php', {
                    method: 'POST',
                    body: formData
                })
                .then(async response => {
                    const text = await response.text();
                    try {
                        const data = JSON.parse(text);
                        return data;
                    } catch (e) {
                        console.error('Resposta não-JSON:', text);
                        throw new Error('Resposta inválida do servidor');
                    }
                })
                .then(data => {
                    if (data.success) {
                        const notificacao = document.querySelector(`[data-id="${id}"]`);
                        if (notificacao) {
                            notificacao.remove();
                            Swal.fire({
                                title: 'Sucesso!',
                                text: data.message,
                                icon: 'success',
                                timer: 2000,
                                showConfirmButton: false
                            }).then(() => {
                                // Recarregar a página após a exclusão
                                window.location.reload();
                            });
                        }
                    } else {
                        throw new Error(data.message || 'Erro ao excluir notificação');
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    Swal.fire({
                        title: 'Erro!',
                        text: error.message,
                        icon: 'error'
                    });
                });
            }
        });
    }
    </script>
    <style>
    .btn-delete {
        background-color: #dc3545;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s;
    }

    .btn-delete:hover {
        background-color: #c82333;
    }

    .notification-actions {
        display: flex;
        gap: 10px;
        margin-top: 15px;
    }

    .notification-actions form {
        margin: 0;
    }

    /* Efeito de fade-out ao excluir */
    .notification-item {
        transition: opacity 0.3s, transform 0.3s;
    }

    .notification-item.removing {
        opacity: 0;
        transform: translateX(-100%);
    }

    /* Estilo para o botão editar */
    .btn-edit {
        background-color: #ffd700; /* Amarelo dourado */
        color: #333;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn-edit:hover {
        transform: translateY(-2px);
        background-color: #ffed4a;
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .btn-edit:active {
        transform: translateY(0);
    }

    .btn-edit i {
        transition: transform 0.3s ease;
    }

    .btn-edit:hover i {
        transform: rotate(15deg);
    }
    </style>
    <script>
    function editarNotificacao(dados) {
        // Preencher o formulário com os dados existentes
        document.querySelector('input[name="titulo"]').value = dados.titulo;
        document.querySelector('textarea[name="mensagem"]').value = dados.mensagem;
        document.querySelector('select[name="tipo"]').value = dados.tipo;
        
        // Formatar a data para o formato aceito pelo input datetime-local
        const data = new Date(dados.data_expiracao);
        const dataFormatada = data.toISOString().slice(0, 16);
        document.querySelector('input[name="data_expiracao"]').value = dataFormatada;
        
        // Atualizar destinatários
        if (dados.is_global) {
            document.querySelector('input[value="global"]').checked = true;
        }
        
        // Adicionar ID da notificação em um campo hidden
        let idInput = document.querySelector('input[name="notificacao_id"]');
        if (!idInput) {
            idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.name = 'notificacao_id';
            document.querySelector('form').appendChild(idInput);
        }
        idInput.value = dados.id;
        
        // Mudar o texto do botão e a ação do formulário
        document.querySelector('button[type="submit"]').innerHTML = '<i class="fas fa-save"></i> Atualizar';
        document.querySelector('input[name="acao"]').value = 'editar';
        
        // Rolar até o formulário
        document.querySelector('.notification-form').scrollIntoView({ behavior: 'smooth' });
    }

    // Função para limpar o formulário
    function limparFormulario() {
        document.querySelector('form').reset();
        document.querySelector('input[name="notificacao_id"]')?.remove();
        document.querySelector('button[type="submit"]').innerHTML = '<i class="fas fa-plus"></i> Criar';
        document.querySelector('input[name="acao"]').value = 'criar';
    }

    // Adicionar botão para cancelar edição
    document.querySelector('.form-header').innerHTML += `
        <button type="button" onclick="limparFormulario()" class="btn btn-secondary" style="margin-left: auto;">
            <i class="fas fa-times"></i> Cancelar
        </button>
    `;
    </script>
    <style>
    /* Estilos para a busca e lista de usuários */
    .usuarios-container {
        margin: 20px 0;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        overflow: hidden;
    }

    .search-container {
        position: relative;
        padding: 12px;
        background-color: #f8fafc;
        border-bottom: 1px solid #e2e8f0;
    }

    .search-input {
        width: 100%;
        padding: 8px 32px 8px 12px;
        border: 1px solid #cbd5e1;
        border-radius: 4px;
        font-size: 14px;
        transition: all 0.3s ease;
    }

    .search-input:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .search-icon {
        position: absolute;
        right: 24px;
        top: 50%;
        transform: translateY(-50%);
        color: #64748b;
    }

    .usuarios-lista {
        max-height: 300px;
        overflow-y: auto;
        padding: 12px;
    }

    .usuario-item {
        display: flex;
        align-items: center;
        padding: 8px;
        transition: background-color 0.2s ease;
        border-radius: 4px;
    }

    .usuario-item:hover {
        background-color: #f1f5f9;
    }

    .usuario-item input[type="checkbox"] {
        margin-right: 12px;
    }

    .usuario-item label {
        cursor: pointer;
        font-size: 14px;
        color: #334155;
    }

    /* Estilizar a scrollbar */
    .usuarios-lista::-webkit-scrollbar {
        width: 8px;
    }

    .usuarios-lista::-webkit-scrollbar-track {
        background: #f1f5f9;
    }

    .usuarios-lista::-webkit-scrollbar-thumb {
        background: #cbd5e1;
        border-radius: 4px;
    }

    .usuarios-lista::-webkit-scrollbar-thumb:hover {
        background: #94a3b8;
    }
    </style>

    <script>
    // JavaScript para a funcionalidade de busca
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchUsuarios');
        const usuarioItems = document.querySelectorAll('.usuario-item');

        searchInput.addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();

            usuarioItems.forEach(item => {
                const label = item.querySelector('label');
                const userName = label.textContent.toLowerCase();
                
                if (userName.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        // Limpar busca quando clicar no X do input
        searchInput.addEventListener('search', function() {
            usuarioItems.forEach(item => {
                item.style.display = 'flex';
            });
        });
    });
    </script>
</body>
</html>







