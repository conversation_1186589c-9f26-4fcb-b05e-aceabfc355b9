<!DOCTYPE html>
<html>
<head>
    <title>Registrar Estudo</title>
</head>
<body>
<h1>Registrar Estudo</h1>
<form method="post" action="processar_estudo.php">
    <label>Planejamento ID:</label>
    <select name="planejamento_id" required>
        <?php
        // Aqui você deve realizar a conexão com o banco de dados PostgreSQL
        include_once("conexao_POST.php");

        // Verificar se a conexão foi estabelecida com sucesso


        // Query para buscar os IDs de planejamentos na tabela "planejamento"
        $query_buscar_planejamentos = "SELECT idplanejamento, nome, usuario_idusuario FROM appEstudo.planejamento";

        // Executar a query
        $resultado_planejamentos = pg_query($conexao, $query_buscar_planejamentos);

        // Loop para preencher as opções do campo de seleção (select) com os IDs de planejamentos
        while ($linha_planejamento = pg_fetch_assoc($resultado_planejamentos)) {
            $idplanejamento = $linha_planejamento["idplanejamento"];
            $nome_planejamento = $linha_planejamento["nome"];
            $idusuario_planejamento = $linha_planejamento["usuario_idusuario"];
            echo "<option value='$idplanejamento'>$idplanejamento - $nome_planejamento --- $idusuario_planejamento</option>";
            // Campo oculto para enviar o usuario_id via POST
            echo "<input type='hidden' name='usuario_id' value='$idusuario_planejamento'>";

        }
        $_POST['planejamento_id']=$idplanejamento;
        $_POST['usuario_id']=$idusuario_planejamento;

        ?>
    </select><br>

    <label>Data:</label>
    <input type="date" name="data" required><br>

    <label>Tempo Líquido:</label>
    <input type="time" name="tempo_liquido" required><br>

    <label>Hora de Início:</label>
    <input type="time" name="hora_inicio" required><br>

    <label>Hora de Fim:</label>
    <input type="time" name="hora_fim" required><br>

    <label>Tempo Bruto:</label>
    <input type="time" name="tempo_bruto" required><br>

    <label>Tempo Perdido:</label>
    <input type="time" name="tempo_perdido" required><br>
    <!-- Adicione os demais campos do formulário de acordo com as necessidades -->
    <?php
        // Verifica o conteúdo do array $_POST
        var_dump($_POST);?><br>

    <label>Matéria:</label>
    <select name="materia_id" required>
        <?php



        if (isset($_POST['planejamento_id']) && is_numeric($_POST['planejamento_id'])) {
            $planejamento_id = $_POST['planejamento_id'];

            // Consultar as matérias relacionadas ao planejamento selecionado
            $query_buscar_materias_planejamento = "SELECT pm.materia_idmateria, m.nome 
                                                   FROM appEstudo.planejamento_materia pm 
                                                   INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria 
                                                   WHERE pm.planejamento_idplanejamento = $planejamento_id";

            // Executar a query
            $resultado_materias_planejamento = pg_query($conexao, $query_buscar_materias_planejamento);

            // Verificar se há matérias associadas ao planejamento
            if (pg_num_rows($resultado_materias_planejamento) > 0) {
                // Loop para preencher as opções do campo de seleção (select) com as matérias relacionadas
                while ($linha_materia_planejamento = pg_fetch_assoc($resultado_materias_planejamento)) {
                    $materia_id = $linha_materia_planejamento["materia_idmateria"];
                    $nome_materia = $linha_materia_planejamento["nome"];
                    echo "<option value='$materia_id'>$nome_materia</option>";
                }
            } else {
                echo "<option disabled>Nenhuma matéria associada a esse planejamento.</option>";
            }
        } else {
            // Caso o formulário não tenha sido submetido ou o planejamento não tenha sido selecionado
            echo "<option disabled>Selecione um planejamento válido primeiro.</option>";
        }
        // Fechar a conexão com o banco de dados PostgreSQL
        pg_close($conexao);
        ?>
    </select><br>

    <input type="submit" value="Registrar">
</form>
</body>
</html>
