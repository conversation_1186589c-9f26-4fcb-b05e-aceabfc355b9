RewriteEngine On

# Permitir CORS
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"

# Responder a requisições OPTIONS
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ $1 [R=200,L]

# Redirecionar todas as requisições para index.php (exceto arquivos existentes)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/fidelidade_barbearia/api/test_api\.php$
RewriteRule ^(.*)$ index.php?path=$1 [QSA,L]

# Configurações de segurança
<Files "*.php">
    Order allow,deny
    Allow from all
</Files>

# Bloquear acesso a arquivos de configuração
<Files "config/*">
    Order deny,allow
    Deny from all
</Files>

# Configurações de cache
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType application/json "access plus 0 seconds"
</IfModule>

# Configurações de compressão
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE application/json
    AddOutputFilterByType DEFLATE text/plain
</IfModule>
