<?php
// revisoes.php
session_start();
include_once("assets/config.php");

if (!isset($_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit();
}

$usuario_id = $_SESSION['idusuario'];

// Buscar estatísticas de revisão
$query_stats = "
    SELECT 
        COUNT(*) as total_cards,
        COUNT(CASE WHEN proxima_revisao <= CURRENT_TIMESTAMP THEN 1 END) as pendentes,
        COUNT(CASE WHEN nivel_conhecimento >= 4 THEN 1 END) as dominados,
        ROUND(AVG(nivel_conhecimento)::numeric, 2) as nivel_medio
    FROM appestudo.flashcard_progress
    WHERE usuario_id = $1";

$result_stats = pg_query_params($conexao, $query_stats, array($usuario_id));
$stats = pg_fetch_assoc($result_stats);

// Buscar revisões pendentes agrupadas por matéria
$query_revisoes = "
    WITH cards_revisao AS (
        SELECT 
            f.id,
            f.pergunta,
            f.resposta,
            d.nome as deck_name,
            c.nome as category_name,
            m.nome as materia_nome,
            m.cor as materia_cor,
            fp.nivel_conhecimento,
            fp.proxima_revisao,
            fp.total_revisoes
        FROM appestudo.flashcard_progress fp
        JOIN appestudo.flashcards f ON f.id = fp.flashcard_id
        JOIN appestudo.flashcard_decks d ON d.id = f.deck_id
        JOIN appestudo.flashcard_categories c ON c.id = d.category_id
        JOIN appestudo.flashcard_materias fm ON fm.flashcard_id = f.id
        JOIN appestudo.materia m ON m.idmateria = fm.materia_id
        WHERE fp.usuario_id = $1 
        AND fp.proxima_revisao <= CURRENT_TIMESTAMP
    )
    SELECT 
        materia_nome,
        materia_cor,
        COUNT(*) as total_cards,
        json_agg(json_build_object(
            'id', id,
            'pergunta', pergunta,
            'resposta', resposta,
            'deck_name', deck_name,
            'category_name', category_name,
            'nivel_conhecimento', nivel_conhecimento,
            'proxima_revisao', proxima_revisao,
            'total_revisoes', total_revisoes
        )) as cards
    FROM cards_revisao
    GROUP BY materia_nome, materia_cor
    ORDER BY materia_nome";

$result_revisoes = pg_query_params($conexao, $query_revisoes, array($usuario_id));
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Revisões Pendentes</title>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Old+Standard+TT:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #B85C5C;
            --paper-color: #EDE3D0;
            --secondary-color: #D2691E;
            --background-color: #F5E6D3;
            --text-color: #2C1810;
            --border-color: #8B4513;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: var(--paper-color);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 4px 4px 0 var(--border-color);
        }

        .stat-value {
            font-size: 2rem;
            color: var(--primary-color);
            font-weight: bold;
            margin: 10px 0;
        }

        .stat-label {
            color: var(--secondary-color);
            font-family: 'Old Standard TT', serif;
        }

        .materia-section {
            background: var(--paper-color);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 4px 4px 0 var(--border-color);
            overflow: hidden;
        }

        .materia-header {
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
        }

        .materia-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-family: 'Playfair Display', serif;
            font-size: 1.2rem;
        }

        .materia-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }

        .cards-container {
            display: none;
            padding: 20px;
            background: rgba(255, 255, 255, 0.5);
        }

        .cards-container.active {
            display: block;
        }

        .card-item {
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .card-item:hover {
            transform: translateX(5px);
            box-shadow: 2px 2px 0 var(--border-color);
        }

        .card-meta {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
            color: var(--secondary-color);
            margin-bottom: 10px;
        }

        .card-content {
            margin: 10px 0;
        }

        .card-actions {
            display: flex;
            gap: 10px;
            margin-top: 10px;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            background: var(--paper-color);
            border: 2px solid var(--border-color);
            border-radius: 8px;
            margin-top: 30px;
        }

        @media (max-width: 768px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <a href="flashcards.php" class="btn-back">
        <i class="fas fa-arrow-left"></i>
    </a>

    <div class="container">
        <div class="menu-icon">
            <i class="fas fa-sync"></i>
        </div>

        <h1>Revisões Pendentes</h1>

        <!-- Estatísticas -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-label">Cards Pendentes</div>
                <div class="stat-value"><?php echo $stats['pendentes']; ?></div>
            </div>
            <div class="stat-card">
                <div class="stat-label">Total de Cards</div>
                <div class="stat-value"><?php echo $stats['total_cards']; ?></div>
            </div>
            <div class="stat-card">
                <div class="stat-label">Cards Dominados</div>
                <div class="stat-value"><?php echo $stats['dominados']; ?></div>
            </div>
            <div class="stat-card">
                <div class="stat-label">Nível Médio</div>
                <div class="stat-value"><?php echo $stats['nivel_medio']; ?></div>
            </div>
        </div>

        <!-- Revisões por Matéria -->
        <?php
        if (pg_num_rows($result_revisoes) > 0) {
            while ($materia = pg_fetch_assoc($result_revisoes)) {
                $cards = json_decode($materia['cards'], true);
                ?>
                <div class="materia-section">
                    <div class="materia-header" onclick="toggleMateria(this)">
                        <div class="materia-title">
                            <div class="materia-indicator" style="background-color: <?php echo $materia['materia_cor']; ?>"></div>
                            <span><?php echo $materia['materia_nome']; ?></span>
                        </div>
                        <div class="materia-count">
                            <?php echo count($cards); ?> cards pendentes
                        </div>
                    </div>
                    <div class="cards-container">
                        <?php foreach ($cards as $card) { ?>
                            <div class="card-item">
                                <div class="card-meta">
                                    <span><?php echo $card['deck_name']; ?></span>
                                    <span>Nível: <?php echo $card['nivel_conhecimento']; ?>/5</span>
                                </div>
                                <div class="card-content">
                                    <strong>Q:</strong> <?php echo $card['pergunta']; ?>
                                </div>
                                <div class="card-actions">
                                    <button class="btn btn-primary" onclick="window.location.href='estudar.php?card=<?php echo $card['id']; ?>'">
                                        Revisar Agora
                                    </button>
                                </div>
                            </div>
                        <?php } ?>
                    </div>
                </div>
                <?php
            }
        } else {
            ?>
            <div class="empty-state">
                <i class="fas fa-check-circle" style="font-size: 3rem; color: var(--primary-color); margin-bottom: 20px;"></i>
                <h3>Nenhuma revisão pendente!</h3>
                <p>Você está em dia com seus estudos. Volte mais tarde para novas revisões.</p>
            </div>
            <?php
        }
        ?>
    </div>

    <script>
        function toggleMateria(header) {
            const container = header.nextElementSibling;
            container.classList.toggle('active');
        }

        // Expandir primeira matéria automaticamente
        document.addEventListener('DOMContentLoaded', function() {
            const firstContainer = document.querySelector('.cards-container');
            if (firstContainer) {
                firstContainer.classList.add('active');
            }
        });
    </script>
</body>
</html>