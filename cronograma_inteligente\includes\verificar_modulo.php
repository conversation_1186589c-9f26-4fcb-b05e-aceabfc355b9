<?php
// includes/verificar_modulo.php
function verificarModulo() {
    global $conexao;
    
    if (!isset($_SESSION['idusuario'])) {
        header("Location: ../login_index.php");
        exit();
    }

    // Verifica se a conexão está disponível
    if (!isset($conexao) || !$conexao) {
        require_once __DIR__ . '/../assets/config.php';
    }

    // Verifica se a conexão está ativa
    if (!pg_ping($conexao)) {
        error_log("Erro: Conexão com banco de dados perdida");
        die("Erro de conexão com o banco de dados. Por favor, atualize a página.");
    }

    require_once 'verify_cronograma_access.php';
    
    $verificacao = verificarAcessoCronograma($conexao, $_SESSION['idusuario']);

    if (!$verificacao['acesso']) {
        $_SESSION['erro_acesso'] = $verificacao['mensagem'];
        header("Location: planos_usuarios.php");
        exit();
    }

    // Se tem alerta de vencimento próximo, guarda na sessão
    if (isset($verificacao['tipo']) && $verificacao['tipo'] === 'alerta_vencimento') {
        $_SESSION['alerta_assinatura'] = $verificacao['mensagem'];
    }

    return $verificacao;
}