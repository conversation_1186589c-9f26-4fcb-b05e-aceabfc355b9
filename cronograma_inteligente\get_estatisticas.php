<?php
//get_estatisticas.php
error_reporting(E_ALL);
ini_set('display_errors', 0); // Desativa exibição de erros na saída
header('Content-Type: application/json');

try {
    define('IS_API_CONTEXT', true); // Define que estamos em um contexto de API
    session_start();
    include_once("includes/init.php");
    include_once("includes/plano_estudo_logic.php");
    include_once("includes/calculos.php");

    if (!isset($_SESSION['idusuario'])) {
        throw new Exception('Usuário não autenticado');
    }

    $usuario_id = $_SESSION['idusuario'];

    // Usa a classe PlanoEstudoLogic para obter as estatísticas
    $planoData = new PlanoEstudoLogic($conexao);
    $conteudos = $planoData->getConteudos();
    
    if (!function_exists('filtrarTodosConteudos')) {
        throw new Exception('Função filtrarTodosConteudos não encontrada');
    }
    
    $conteudosFiltrados = filtrarTodosConteudos($conteudos);

    // Agrupa por matéria
    $estatisticas = [];
    $materias = [];

    foreach ($conteudosFiltrados as $conteudo) {
        $materia = $conteudo['materia_nome'];
        if (!isset($materias[$materia])) {
            $materias[$materia] = [
                'total_itens' => 0,
                'itens_estudados' => 0,
                'cor' => $conteudo['cor']
            ];
        }
        
        $materias[$materia]['total_itens']++;
        if ($conteudo['status_estudo'] === 'Estudado') {
            $materias[$materia]['itens_estudados']++;
        }
    }

    // Calcula percentuais e formata resultado
    foreach ($materias as $materia => $dados) {
        $estatisticas[] = [
            'materia_nome' => $materia,
            'cor' => $dados['cor'],
            'total_itens' => $dados['total_itens'],
            'itens_estudados' => $dados['itens_estudados'],
            'percentual' => round(($dados['itens_estudados'] / $dados['total_itens'] * 100), 2)
        ];
    }

    // Ordena por nome da matéria
    usort($estatisticas, function($a, $b) {
        return strcmp($a['materia_nome'], $b['materia_nome']);
    });

    echo json_encode($estatisticas);

} catch (Exception $e) {
    error_log("Erro em get_estatisticas.php: " . $e->getMessage());
    echo json_encode([
        'error' => true,
        'message' => 'Erro ao obter estatísticas: ' . $e->getMessage()
    ]);
} catch (Error $e) {
    error_log("Erro fatal em get_estatisticas.php: " . $e->getMessage());
    echo json_encode([
        'error' => true,
        'message' => 'Erro interno ao processar estatísticas'
    ]);
}