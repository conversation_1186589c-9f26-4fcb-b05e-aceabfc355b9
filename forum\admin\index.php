<?php
include_once("../../session_config.php");
require_once('../../assets/config.php');
require_once('../../cadastros/includes/verify_admin.php');

if (!verificarSessaoValida()) {
    header("Location: ../../login_index.php");
    exit();
}

// Verifica se é qualquer admin
verificarAcessoAdmin($conexao, false);

// Buscar estatísticas do fórum
$query_stats = "
    SELECT 
        (SELECT COUNT(*) FROM appestudo.forum_categorias WHERE status = true) as total_categorias,
        (SELECT COUNT(*) FROM appestudo.forum_topicos WHERE status = true) as total_topicos,
        (SELECT COUNT(*) FROM appestudo.forum_respostas WHERE status = true) as total_respostas,
        (SELECT COUNT(DISTINCT usuario_id) FROM appestudo.forum_topicos WHERE status = true) as usuarios_ativos
";

$result_stats = pg_query($conexao, $query_stats);
$stats = pg_fetch_assoc($result_stats);

// Buscar nome do usuário logado
$nome_admin = isset($_SESSION['nome']) ? $_SESSION['nome'] : 'Administrador';
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Painel Administrativo - Fórum</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

</head>
<body>
    <header class="admin-header">
        <a href="index.php" class="admin-logo"><strong>PlanejaAqui</strong> <span style="font-weight:400;">Admin</span></a>
        <div class="admin-header-actions">
            <span style="font-size:1rem; color:var(--text-secondary); margin-right:16px; font-weight:500;">
                <?php echo htmlspecialchars($nome_admin); ?>
            </span>
            <button class="icon-button" id="toggleDarkMode" title="Alternar modo escuro">
                <i class="fas fa-moon"></i>
            </button>
        </div>
    </header>
    <div class="container">
        <h1>Painel Administrativo do Fórum</h1>
        <div class="stats-grid">
            <div class="stat-card">
                <i class="fas fa-folder"></i>
                <h3>Categorias</h3>
                <p><?php echo $stats['total_categorias']; ?></p>
                <a href="categorias.php" class="btn">Gerenciar</a>
            </div>
            <div class="stat-card">
                <i class="fas fa-comments"></i>
                <h3>Tópicos</h3>
                <p><?php echo $stats['total_topicos']; ?></p>
                <a href="topicos.php" class="btn">Gerenciar</a>
            </div>
            <div class="stat-card">
                <i class="fas fa-reply"></i>
                <h3>Respostas</h3>
                <p><?php echo $stats['total_respostas']; ?></p>
            </div>
        </div>
    </div>
    <script>
        // Dark mode toggle
        function toggleDarkMode() {
            const body = document.body;
            const icon = document.querySelector('#toggleDarkMode i');
            body.classList.toggle('dark-mode');
            if (body.classList.contains('dark-mode')) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
                localStorage.setItem('adminDarkMode', 'enabled');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
                localStorage.setItem('adminDarkMode', 'disabled');
            }
        }
        document.addEventListener('DOMContentLoaded', function() {
            const darkMode = localStorage.getItem('adminDarkMode');
            const icon = document.querySelector('#toggleDarkMode i');
            if (darkMode === 'enabled') {
                document.body.classList.add('dark-mode');
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            }
            document.getElementById('toggleDarkMode').addEventListener('click', function(e) {
                e.preventDefault();
                toggleDarkMode();
            });
        });
    </script>
</body>
</html>

