<?php
// ver_topicos.php
session_start();
include_once("assets/config.php");
include_once("helpers.php");

if (!isset($_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit();
}

if (!isset($_GET['baralho'])) {
    header("Location: flashcards.php");
    exit();
}

$baralho_id = (int)$_GET['baralho'];
$mensagem = '';
$is_admin = checkAdmin($conexao, $_SESSION['idusuario']);

// Consulta para buscar o nome do usuário com base no ID
$query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = " . $_SESSION['idusuario'];
$resultado_nome = pg_query($conexao, $query_buscar_nome);

if ($resultado_nome && pg_num_rows($resultado_nome) > 0) {
    $row = pg_fetch_assoc($resultado_nome);
    $nome_usuario = $row['nome'];
} else {
    $nome_usuario = "Usuário";
}

// Buscar informações do baralho e categoria
$query_info = "
    SELECT 
        d.nome as deck_name,
        d.materia_id,
        c.nome as categoria_name,
        c.id as categoria_id,
        m.nome as materia_nome
    FROM appestudo.flashcard_decks d
    JOIN appestudo.flashcard_categories c ON c.id = d.category_id
    JOIN appestudo.materia m ON m.idmateria = d.materia_id
    WHERE d.id = $1";
$result_info = pg_query_params($conexao, $query_info, array($baralho_id));
$info = pg_fetch_assoc($result_info);

if (!$info) {
    header("Location: flashcards.php");
    exit();
}

// Buscar tópicos do baralho
$query_topics = "
    SELECT 
        t.id,
        t.nome,
        t.descricao,
        t.ordem,
        (
            SELECT COUNT(*) 
            FROM appestudo.flashcard_topic_association fta
            JOIN appestudo.flashcards f ON f.id = fta.flashcard_id
            WHERE fta.topic_id = t.id
        ) as total_cards
    FROM appestudo.flashcard_topics t
    WHERE t.deck_id = $1
    ORDER BY t.ordem";
$result_topics = pg_query_params($conexao, $query_topics, array($baralho_id));

// Preparar os tópicos para uso no Vue
$topics = [];
while ($topic = pg_fetch_assoc($result_topics)) {
    $topics[] = [
        'id' => $topic['id'],
        'nome' => htmlspecialchars($topic['nome']),
        'descricao' => htmlspecialchars($topic['descricao']),
        'ordem' => (int)$topic['ordem'],
        'total_cards' => (int)$topic['total_cards']
    ];
}

// Processar reordenação se solicitado via Ajax
if (isset($_POST['action']) && $_POST['action'] === 'reorder' && isset($_POST['topic_id']) && isset($_POST['direction'])) {
    $topic_id = (int)$_POST['topic_id'];
    $direction = $_POST['direction'];
    
    // Obter a ordem atual do tópico
    $query_ordem = "SELECT ordem FROM appestudo.flashcard_topics WHERE id = $1";
    $result_ordem = pg_query_params($conexao, $query_ordem, array($topic_id));
    $topic_ordem = pg_fetch_assoc($result_ordem);
    
    if ($topic_ordem) {
        $ordem_atual = (int)$topic_ordem['ordem'];
        $nova_ordem = ($direction === 'up') ? $ordem_atual - 1 : $ordem_atual + 1;
        
        // Iniciar transação
        pg_query($conexao, "BEGIN");
        
        // Atualizar o tópico que está na posição desejada
        $query_swap = "
            UPDATE appestudo.flashcard_topics
            SET ordem = $1
            WHERE deck_id = $2 AND ordem = $3";
        pg_query_params($conexao, $query_swap, array($ordem_atual, $baralho_id, $nova_ordem));
        
        // Atualizar o tópico atual
        $query_update = "
            UPDATE appestudo.flashcard_topics
            SET ordem = $1
            WHERE id = $2";
        pg_query_params($conexao, $query_update, array($nova_ordem, $topic_id));
        
        // Confirmar transação
        pg_query($conexao, "COMMIT");
        
        // Se for uma requisição Ajax, retornar sucesso
        if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest') {
            header('Content-Type: application/json');
            echo json_encode(['success' => true]);
            exit;
        }
        
        // Caso contrário, redirecionar para a mesma página
        header("Location: ver_topicos.php?baralho=$baralho_id");
        exit();
    }
}

// Preparar dados JSON para o Vue
$infoJson = json_encode([
    'deck_name' => htmlspecialchars($info['deck_name']),
    'materia_id' => $info['materia_id'],
    'categoria_name' => htmlspecialchars($info['categoria_name']),
    'categoria_id' => $info['categoria_id'],
    'materia_nome' => htmlspecialchars($info['materia_nome'])
]);
$topicsJson = json_encode($topics);
$mensagemJson = json_encode($mensagem);
$baralhoIdJson = json_encode($baralho_id);
$isAdminJson = json_encode($is_admin);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tópicos - <?php echo htmlspecialchars($info['deck_name']); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700&family=Quicksand:wght@400;500;600;700&family=Varela+Round&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/ver_topicos.css">
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <!-- Vue.js via CDN -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <!-- Axios para requisições AJAX -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
</head>
<body>
    <div id="app">
        <div class="header">
            <div class="header-left">
                <div class="logo">
                    <img src="../logo/logo_vertical_azul.png" alt="Estudo Off" class="logo-light">
                    <img src="../logo/logo_vertical.png" alt="Estudo Off" class="logo-dark">
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span class="user-name"><?php echo htmlspecialchars($nome_usuario); ?></span>
                </div>

                <div class="theme-toggle">
                    <button id="theme-toggle-btn" class="theme-btn" aria-label="Alternar tema">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>

        <a :href="'ver_baralhos.php?categoria=' + info.categoria_id" class="btn-back">
            <i class="fas fa-arrow-left"></i>
        </a>

        <div class="container">
            <div class="clean-container">
                <div class="clean-header">
                    <h1>Tópicos de {{ info.materia_nome }}</h1>
                </div>

                <div class="breadcrumb">
                    <i class="fas fa-home"></i>
                    <span>{{ info.categoria_name }}</span>
                    <i class="fas fa-chevron-right"></i>
                    <span>{{ info.deck_name }}</span>
                </div>

                <div class="header-actions">
                    <div class="header-info">
                        <i class="fas fa-info-circle"></i>
                        Selecione um tópico para estudar ou visualizar seus flashcards
                    </div>
                    <a v-if="isAdmin" :href="'criar_topico.php?baralho=' + baralhoId" class="btn">
                        <i class="fas fa-plus"></i> Novo Tópico
                    </a>
                </div>

                <transition name="fade">
                    <div v-if="mensagem" class="alert" :class="{'alert-danger': mensagemTipo === 'erro', 'alert-success': mensagemTipo === 'sucesso'}">
                        <i :class="mensagemTipo === 'erro' ? 'fas fa-exclamation-circle' : 'fas fa-check-circle'"></i>
                        {{ mensagem }}
                    </div>
                </transition>

                <div v-if="loading" class="loader"></div>
                
                <transition-group v-else-if="topics.length > 0" name="list" tag="ul" class="topics-list">
                    <li v-for="topic in topics" :key="topic.id" class="topic-item">
                        <div class="order-buttons">
                            <button @click="reorderTopic(topic.id, 'up')" 
                                    :disabled="topic.ordem === 1 || reordering" 
                                    class="order-btn">
                                <i class="fas fa-chevron-up"></i>
                            </button>
                            
                            <button @click="reorderTopic(topic.id, 'down')" 
                                    :disabled="topic.ordem === topics.length || reordering" 
                                    class="order-btn">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>

                        <div class="topic-info">
                            <h3 class="topic-title">{{ topic.nome }}</h3>
                            <p v-if="topic.descricao" class="topic-description">{{ topic.descricao }}</p>
                            <div class="topic-stats">
                                <i class="fas fa-book-open"></i> {{ topic.total_cards }} cards disponíveis
                            </div>
                        </div>

                        <div class="topic-actions">
                            <a :href="'ver_cards.php?topico=' + topic.id" class="btn btn-secondary">
                                <i class="fas fa-eye"></i> Cards
                            </a>
                            <a :href="'estudar.php?topico=' + topic.id" class="btn">
                                <i class="fas fa-graduation-cap"></i> Estudar Tópico
                            </a>
                            <a v-if="isAdmin" :href="'criar_card.php?topico=' + topic.id" class="btn btn-success">
                                <i class="fas fa-plus"></i> Novo Card
                            </a>
                            <a v-if="isAdmin" :href="'editar_topico.php?id=' + topic.id" class="btn btn-warning">
                                <i class="fas fa-edit"></i> Editar
                            </a>
                        </div>
                    </li>
                </transition-group>
                
                <div v-else class="empty-state">
                    <i class="fas fa-folder-open"></i>
                    <p>Nenhum tópico cadastrado ainda.</p>
                    <a v-if="isAdmin" :href="'criar_topico.php?baralho=' + baralhoId" class="btn">
                        <i class="fas fa-plus"></i> Criar Primeiro Tópico
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // Inicialize os dados do Vue
                const infoData = <?php echo $infoJson; ?>;
                const topicsData = <?php echo $topicsJson; ?>;
                const mensagemData = <?php echo $mensagemJson; ?>;
                const baralhoIdData = <?php echo $baralhoIdJson; ?>;
                const isAdminData = <?php echo $isAdminJson; ?>;
                
                // 1. Aplica o tema salvo, se existir
                const savedTheme = localStorage.getItem('theme');
                if (savedTheme) {
                    document.documentElement.setAttribute('data-theme', savedTheme);
                    updateThemeIcon(savedTheme);
                }
                
                // 2. Função de atualização do ícone
                function updateThemeIcon(theme) {
                    const icon = document.querySelector('#theme-toggle-btn i');
                    if (icon) {
                        if (theme === 'dark') {
                            icon.className = 'fas fa-sun';
                        } else {
                            icon.className = 'fas fa-moon';
                        }
                    }
                }
                
                // 3. Função simples de alternância de tema
                function toggleTheme() {
                    const html = document.documentElement;
                    const currentTheme = html.getAttribute('data-theme') || 'light';
                    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                    
                    console.log(`Alternando tema: ${currentTheme} → ${newTheme}`);
                    
                    // Aplica o tema
                    html.setAttribute('data-theme', newTheme);
                    localStorage.setItem('theme', newTheme);
                    updateThemeIcon(newTheme);
                }
                
                // 4. Configura o botão de tema - com tempo suficiente para o DOM carregar
                setTimeout(function() {
                    const themeBtn = document.getElementById('theme-toggle-btn');
                    if (themeBtn) {
                        console.log('Botão de tema encontrado e configurado');
                        themeBtn.onclick = toggleTheme;
                    } else {
                        console.error('Botão de tema não encontrado!');
                    }
                }, 100);
                
                // 5. Inicializa o Vue
                new Vue({
                    el: '#app',
                    data: {
                        info: infoData,
                        topics: topicsData,
                        mensagem: mensagemData,
                        mensagemTipo: 'info',
                        baralhoId: baralhoIdData,
                        isAdmin: isAdminData,
                        loading: true,
                        reordering: false
                    },
                    mounted() {
                        // Simulação de carregamento para demonstrar a animação
                        setTimeout(() => {
                            this.loading = false;
                        }, 500);
                    },
                    methods: {
                        reorderTopic(topicId, direction) {
                            if (this.reordering) return;
                            
                            this.reordering = true;
                            
                            // Criar FormData para enviar
                            const formData = new FormData();
                            formData.append('action', 'reorder');
                            formData.append('topic_id', topicId);
                            formData.append('direction', direction);
                            
                            // Adicionar cabeçalho para identificar como ajax
                            const config = {
                                headers: {
                                    'X-Requested-With': 'XMLHttpRequest'
                                }
                            };
                            
                            // Fazer requisição AJAX
                            axios.post('ver_topicos.php?baralho=' + this.baralhoId, formData, config)
                                .then(response => {
                                    if (response.data.success) {
                                        // Atualizar localmente os tópicos
                                        this.updateLocalOrder(topicId, direction);
                                        this.mensagem = 'Ordem atualizada com sucesso';
                                        this.mensagemTipo = 'sucesso';
                                        
                                        // Limpar mensagem após alguns segundos
                                        setTimeout(() => {
                                            this.mensagem = '';
                                        }, 3000);
                                    }
                                })
                                .catch(error => {
                                    this.mensagem = 'Erro ao reordenar tópicos';
                                    this.mensagemTipo = 'erro';
                                })
                                .finally(() => {
                                    this.reordering = false;
                                });
                        },
                        
                        updateLocalOrder(topicId, direction) {
                            // Encontrar o tópico pelo ID
                            const topicIndex = this.topics.findIndex(t => t.id === topicId);
                            if (topicIndex === -1) return;
                            
                            const currentTopic = this.topics[topicIndex];
                            
                            // Calcular a nova ordem
                            const newOrder = direction === 'up' ? currentTopic.ordem - 1 : currentTopic.ordem + 1;
                            
                            // Encontrar o tópico que tem a ordem que queremos
                            const swapTopicIndex = this.topics.findIndex(t => t.ordem === newOrder);
                            if (swapTopicIndex === -1) return;
                            
                            // Trocar as ordens
                            this.topics[swapTopicIndex].ordem = currentTopic.ordem;
                            this.topics[topicIndex].ordem = newOrder;
                            
                            // Reordenar o array
                            this.topics.sort((a, b) => a.ordem - b.ordem);
                        }
                    }
                });
            } catch (e) {
                console.error('Erro ao inicializar aplicação:', e);
                document.getElementById('app').innerHTML = '<div class="empty-state"><i class="fas fa-exclamation-triangle"></i><p>Erro ao carregar a aplicação. Por favor, tente novamente.</p></div>';
            }
        });
    </script>
</body>
</html>