<?php
include 'conexao_POST.php';
include 'processa_index.php';

// Consultar os IDs e nomes das matérias associadas ao planejamento
$query_consultar_materias_planejamento = "
    SELECT pm.materia_idmateria, m.nome AS nome_materia, m.cor AS cor_materia
    FROM appEstudo.planejamento_materia pm
    INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    WHERE pm.planejamento_idplanejamento = $id_planejamento";

$resultado_materias_planejamento = pg_query($conexao, $query_consultar_materias_planejamento);

// Inicializa arrays para armazenar os dados das matérias
$materias_no_planejamento = array();
$cores_materias = array();

// Preenche os arrays com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_materias_planejamento)) {
    $materia_id = $row['materia_idmateria'];
    $nome_materia = $row['nome_materia'];
    $cor_materia = $row['cor_materia'];

    $materias_no_planejamento[$materia_id] = $nome_materia;
    $cores_materias[$nome_materia] = $cor_materia;
}

// Função para converter tempo (HH:MM:SS) para segundos
function converterParaSegundos($tempo) {
    list($horas, $minutos, $segundos) = explode(':', $tempo);
    return $horas * 3600 + $minutos * 60 + $segundos;
}

// Função para converter segundos para tempo (HH:MM:SS)
function converterParaTempo($segundos) {
    $horas = floor($segundos / 3600);
    $segundos %= 3600;
    $minutos = floor($segundos / 60);
    $segundos %= 60;
    return sprintf('%02d:%02d:%02d', $horas, $minutos, $segundos);
}

// Realiza a consulta ao banco de dados para obter os dados de estudo por ponto estudado de cada matéria no planejamento
$query_consulta_pontos_estudo = "
    SELECT m.nome AS nome_materia, 
           e.ponto_estudado AS ponto_estudado,
           SUM(e.tempo_liquido) AS tempo_estudo
    FROM appEstudo.estudos e
    INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
    INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
    LEFT JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria
    WHERE u.idusuario = $id_usuario AND e.materia_idmateria IN (" . implode(',', array_keys($materias_no_planejamento)) . ")
    GROUP BY m.nome, e.ponto_estudado";

$resultado_consulta_pontos_estudo = pg_query($conexao, $query_consulta_pontos_estudo);

// Inicializa um array para armazenar os dados de tempo de estudo por ponto estudado para cada matéria
$pontos_estudo_por_materia = array();
$tempo_total_por_materia = array();

// Preenche o array com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_consulta_pontos_estudo)) {
    $materia = $row['nome_materia'];
    $ponto_estudado = $row['ponto_estudado'];
    $tempo_estudo = $row['tempo_estudo']; // Tempo no formato "HH:MM:SS"

    // Converte o tempo de estudo para segundos
    $tempo_estudo_segundos = converterParaSegundos($tempo_estudo);

    // Adiciona os tempos de estudo por ponto estudado para a matéria correspondente
    if (!isset($pontos_estudo_por_materia[$materia])) {
        $pontos_estudo_por_materia[$materia] = array();
    }
    // Convertendo para segundos e armazenando
    $pontos_estudo_por_materia[$materia][$ponto_estudado] = $tempo_estudo_segundos;

    // Calcula o tempo total por matéria
    if (!isset($tempo_total_por_materia[$materia])) {
        $tempo_total_por_materia[$materia] = 0;
    }
    $tempo_total_por_materia[$materia] += $tempo_estudo_segundos;
}

// Convertendo os arrays para JSON para serem utilizados no JavaScript
$dados_json = json_encode($pontos_estudo_por_materia);
$cores_json = json_encode($cores_materias);
$tempo_total_json = json_encode($tempo_total_por_materia);
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Porcentagem do Ciclo de Estudo</title>
    <script src="https://code.highcharts.com/highcharts.js"></script>
    <script src="https://code.highcharts.com/modules/treemap.js"></script>
    <script src="https://code.highcharts.com/modules/sunburst.js"></script>
    <script src="https://code.highcharts.com/modules/treemap.js"></script>
    <script src="https://code.highcharts.com/modules/drilldown.js"></script>
    <link rel="stylesheet" href="css/index1.css">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <style>
        .chart-container {
            position: relative;
            width: 95%;
            height: 90vh;
            margin: auto;
        }

        #myTreeChart {
            width: 100%;
            height: 100%;
        }
    </style>
</head>
<body>

<h1 class="titulo">Porcentagem de Estudo Por Ponto Estudado</h1>
<div id="myTreeChart" class="caixa-titulo1 chart-container"></div>

<script>
    // Recuperando os dados do PHP
    var dados = <?php echo $dados_json; ?>;
    var cores = <?php echo $cores_json; ?>;
    var tempoTotalPorMateria = <?php echo $tempo_total_json; ?>;

    //.log("Dados recebidos:", dados); // Debug: Imprime os dados no console
    //console.log("Cores recebidas:", cores); // Debug: Imprime as cores no console
    //console.log("Tempo total por matéria:", tempoTotalPorMateria); // Debug: Imprime o tempo total no console

    // Transformando os dados no formato esperado pelo Highcharts
    var seriesData = [];

    for (var materia in dados) {
        var totalTempoEstudo = tempoTotalPorMateria[materia];
        var horas = Math.floor(totalTempoEstudo / 3600);
        var minutos = Math.floor((totalTempoEstudo % 3600) / 60);
        var formattedTime = horas + 'h ' + minutos + 'm';

        // Adiciona a matéria como um item de nível superior
        seriesData.push({
            id: materia,
            name: materia,
            value: totalTempoEstudo,
            color: cores[materia],
            formattedTime: formattedTime
        });

        // Adiciona os pontos estudados como itens filhos
        for (var pontoEstudado in dados[materia]) {
            var tempoEstudo = parseInt(dados[materia][pontoEstudado]);
            var horasPontoEstudo = Math.floor(tempoEstudo / 3600);
            var minutosPontoEstudo = Math.floor((tempoEstudo % 3600) / 60);
            var formattedTimePontoEstudo = horasPontoEstudo + 'h ' + minutosPontoEstudo + 'm';
            seriesData.push({
                parent: materia,
                name: pontoEstudado,
                value: tempoEstudo,
                formattedTime: formattedTimePontoEstudo
            });
        }
    }

    // Configuração do gráfico
    Highcharts.chart('myTreeChart', {
        series: [{
            type: 'treemap',
            layoutAlgorithm: 'squarified',
            allowDrillToNode: true,
            dataLabels: {
                enabled: true,
                format: '{point.name}: {point.percentage:.2f}% ({point.formattedTime})',
                style: {
                    textOutline: 'none'
                }
            },
            levelIsConstant: false,
            levels: [{
                level: 1,
                dataLabels: {
                    enabled: true
                },
                borderWidth: 3
            }],
            data: seriesData
        }],
        subtitle: {
            text: 'Clique nos blocos para ver os detalhes dos pontos estudados'
        },
        title: {
            text: 'Porcentagem de Estudo Por Ponto Estudado'
        },
        tooltip: {
            pointFormat: '{point.name}: <b>{point.percentage:.2f}%</b> ({point.formattedTime})'
        },
        credits: {
            enabled: false // Desativar créditos
        }
    });
</script>
</body>
</html>
