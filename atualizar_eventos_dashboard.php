<?php
session_start();
include_once("conexao_POST.php");

if (!isset($_SESSION['idusuario'])) {
    exit('Não autorizado');
}

header('Content-Type: text/html; charset=utf-8');

$id_usuario = $_SESSION['idusuario'];
$hoje = date('Y-m-d');

// Limpar qualquer saída anterior
ob_clean();

// Consultar eventos pendentes
$query_pendentes = "SELECT DISTINCT * FROM appEstudo.agenda 
                   WHERE usuario_idusuario = $1 
                   AND data_inicio < CURRENT_DATE 
                   AND realizado = 'f' 
                   ORDER BY data_inicio ASC";
$stmt = pg_prepare($conexao, "pendentes", $query_pendentes);
$resultado_pendentes = pg_execute($conexao, "pendentes", array($id_usuario));
$eventosPendentes = array();
while ($row = pg_fetch_assoc($resultado_pendentes)) {
    $eventosPendentes[$row['id']] = $row; // Usa ID como chave para evitar duplicatas
}

// Consultar eventos do dia
$query_hoje = "SELECT DISTINCT * FROM appEstudo.agenda 
              WHERE usuario_idusuario = $1 
              AND DATE(data_inicio) = CURRENT_DATE 
              ORDER BY data_inicio ASC";
$stmt = pg_prepare($conexao, "hoje", $query_hoje);
$resultado_hoje = pg_execute($conexao, "hoje", array($id_usuario));
$eventos_do_Dia = array();
while ($row = pg_fetch_assoc($resultado_hoje)) {
    $eventos_do_Dia[$row['id']] = $row; // Usa ID como chave para evitar duplicatas
}

// Consultar próximo evento
$query_proximo = "SELECT DISTINCT * FROM appEstudo.agenda 
                 WHERE usuario_idusuario = $1 
                 AND data_inicio > CURRENT_DATE 
                 ORDER BY data_inicio ASC 
                 LIMIT 1";
$stmt = pg_prepare($conexao, "proximo", $query_proximo);
$resultado_proximo = pg_execute($conexao, "proximo", array($id_usuario));
$evento_proximo = null;
$eventosProximos = array();

if ($row = pg_fetch_assoc($resultado_proximo)) {
    $evento_proximo = strtotime($row['data_inicio']);
    $eventosProximos[$row['id']] = $row; // Usa ID como chave para evitar duplicatas

    // Limpar conteúdo antes de gerar novo
    if (ob_get_length()) ob_clean();

    // Gerar o HTML
    if (empty($eventosPendentes) && empty($eventos_do_Dia) && empty($eventosProximos)) {
        echo '<div class="sem-eventos" style="text-align: center; padding: 20px; background: #f9f9f9; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">';
        echo '<i class="fas fa-calendar-times" style="font-size: 48px; color: #ccc; margin-bottom: 10px;"></i>';
        echo '<p style="color: #666; font-size: 18px;">Não há eventos agendados</p>';
        echo '</div>';
    } else {
        if (!empty($eventosPendentes)) {
            echo '<div class="quadrado_pendente">';
            echo "<strong>📍 Pendente: ";
            echo implode("<strong style='color: #666;'> • </strong>", array_map(function($evento) {
                return sprintf(
                    "<span class='evento-link-dashboard' data-id='%s' style='color: red; cursor: pointer; text-decoration: underline;'>%s</span>",
                    htmlspecialchars($evento['id']),
                    htmlspecialchars($evento['titulo'])
                );
            }, $eventosPendentes));
            echo "</strong></div>";
        }

        if (!empty($eventos_do_Dia)) {
            echo '<div class="quadrado_hoje" style="margin: 10px 0; padding: 10px; background: #f0fff4; border-left: 4px solid green; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">';
            echo "<strong style='font-size: 14px;'>⌚ Hoje: ";
            echo implode("<strong style='color: #666;'> • </strong>", array_map(function($evento) {
                return sprintf(
                    "<span class='evento-link-dashboard' data-id='%s' style='color: green; cursor: pointer; text-decoration: underline;'>%s</span>",
                    htmlspecialchars($evento['id']),
                    htmlspecialchars($evento['titulo'])
                );
            }, $eventos_do_Dia));
            echo "</strong></div>";
        }

        if ($evento_proximo && !empty($eventosProximos)) {
            $diasAteProximoEvento = (new DateTime())->diff(new DateTime(date('Y-m-d', $evento_proximo)))->days;
            $palavraDias = ($diasAteProximoEvento <= 1) ? "Dia" : "Dias";

            echo '<div class="quadrado_proximo_1" style="margin: 10px 0; padding: 10px; background: #fff9f0; border-left: 4px solid #df7201; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">';
            echo "<strong style='font-size: 14px;'>🗓️ Data Mais Próxima: <span style='color: #df7201;'>" . date('d-m-Y', $evento_proximo) . "</span></strong><br>";
            echo "<strong style='font-size: 14px;'>📌 Eventos: ";
            echo implode("<span style='color: #666;'> • </span>", array_map(function($evento) {
                return sprintf(
                    "<span class='evento-link-dashboard' data-id='%s' style='color: #df7201; cursor: pointer; text-decoration: underline;'>%s</span>",
                    htmlspecialchars($evento['id']),
                    htmlspecialchars($evento['titulo'])
                );
            }, $eventosProximos));
            echo "</strong><br>";
            echo "<strong style='font-size: 14px;'>⏳ Falta: <span style='color: #df7201;'>$diasAteProximoEvento $palavraDias</span></strong>";
            echo '</div>';
        }
    }
}
?>