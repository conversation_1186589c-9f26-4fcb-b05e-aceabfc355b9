<?php
include 'processa_index.php';
include 'consulta_banco_ultima_proxima.php';
// Incluir o arquivo de conexão com o banco de dados
include_once("conexao_POST.php");

// Verificar se a conexão foi estabelecida com sucesso
if (!$conexao) {
    die("Erro na conexão: " . pg_last_error());
}
//$id_planejamento = $_SESSION['idusuario'];
$id_usuario = $_SESSION['idusuario'];
//echo '<div class="caixa-titulo3">' . $titulo . '</div>';
//echo "<div class='caixa-titulo3'> Sua Agenda Pessoal --> $id_usuario <--</div>";

// Definir o ID do usuário (você precisa atribuir um valor a $id_usuario)
if ($semRegistro === true) {
    echo ' <h3>Último <PERSON>udo</h3>
<div class="card text-black  mb-3" style="max-width;background-color: #0DCAF0;">
           <div class="card-header text-center"><h4><strong><b class="text-secondary">Não Iniciou o Estudo!</strong></b></h4></div>
           </div>';
}else {
    echo '
<div class="card text-black  mb-3" style="max-width;background-color: #0DCAF0;">
<h3>Último Estudo</h3>
             <div class="card-header text-center">
             <h4><strong>' . $ultimo_estudo['nome_materia_estudo'] . '</strong></h4></div>' . '
                <div class="card-body">
       
                    <h5 class="card-text text-center"> Último Estudo:<strong> ' . $ultimo_estudo['ponto_estudado'] . '</strong></h5>  
                    <h5 class="card-text text-center ">Método:<strong> <b class="text-secondary">' . $ultimo_estudo['metodo'] . '</b></strong> </h5>
    </div>
           
        <div class="row">
            <div class="col text-body">
                      </div>
            <div class="col text-end">
           
            </div>
        </div> 
       
    <div class="row">
        <div class="col text-body">
         <h5 class="card-text"><strong>Dia da Semana:</strong><b class="text-secondary">' . $dias_semana[$dia_semana] . '</b></strong></h5>
         
        
        </div>
    
         <div class="col text-end">
            <h5 class="card-text text-end"><strong>Data: </strong><b class="text-secondary">' . $data_estudo . '</b></h5>
        </div>
        </div>
    </div>';
}

// Fechar a conexão com o banco de dados
//pg_close($conexao);
?>

