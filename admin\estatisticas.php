<?php
session_start();
require_once('../includes/auth.php');
require_once('../conexao_POST.php');
require_once('../cadastros/includes/verify_admin.php');

// Verifica se é o super admin
verificarSuperAdmin($conexao, false);

// Estatísticas gerais
$stats = [
    'total_usuarios' => pg_fetch_result(pg_query($conexao, "SELECT COUNT(*) FROM appEstudo.usuario"), 0),
    'usuarios_ativos' => pg_fetch_result(pg_query($conexao, "SELECT COUNT(*) FROM appEstudo.usuario WHERE status = 'ativo'"), 0),
    'logins_hoje' => pg_fetch_result(pg_query($conexao, "SELECT COUNT(*) FROM appEstudo.log_seguranca WHERE tipo_evento = 'login_sucesso' AND data_evento::date = CURRENT_DATE"), 0),
    'logins_mes' => pg_fetch_result(pg_query($conexao, "SELECT COUNT(*) FROM appEstudo.log_seguranca WHERE tipo_evento = 'login_sucesso' AND data_evento >= date_trunc('month', CURRENT_DATE)"), 0)
];

?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estatísticas do Sistema</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            font-family: 'Quicksand', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .stat-card i {
            font-size: 2em;
            color: #00008B;
            margin-bottom: 10px;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #00008B;
            margin: 10px 0;
        }
        .stat-label {
            color: #666;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #00008B;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 20px;
        }
        .btn:hover {
            background-color: #0000CD;
        }
        .btn-voltar {
            background-color: #6c757d;
        }
        .btn-voltar:hover {
            background-color: #5a6268;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-chart-line"></i> Estatísticas do Sistema</h1>
        
        <div class="stats-grid">
            <div class="stat-card">
                <i class="fas fa-users"></i>
                <div class="stat-number"><?php echo $stats['total_usuarios']; ?></div>
                <div class="stat-label">Total de Usuários</div>
            </div>
            
            <div class="stat-card">
                <i class="fas fa-user-check"></i>
                <div class="stat-number"><?php echo $stats['usuarios_ativos']; ?></div>
                <div class="stat-label">Usuários Ativos</div>
            </div>
            
            <div class="stat-card">
                <i class="fas fa-sign-in-alt"></i>
                <div class="stat-number"><?php echo $stats['logins_hoje']; ?></div>
                <div class="stat-label">Logins Hoje</div>
            </div>
            
            <div class="stat-card">
                <i class="fas fa-calendar-check"></i>
                <div class="stat-number"><?php echo $stats['logins_mes']; ?></div>
                <div class="stat-label">Logins este Mês</div>
            </div>
        </div>

        <a href="index.php" class="btn btn-voltar">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
    </div>
</body>
</html>

