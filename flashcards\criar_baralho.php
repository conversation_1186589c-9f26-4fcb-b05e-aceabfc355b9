<?php
// criar_baralho.php
session_start();
include_once("assets/config.php");
include_once("helpers.php");

if (!isset($_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit();
}

redirectIfNotAdmin($conexao, $_SESSION['idusuario']);

if (!isset($_GET['categoria'])) {
    header("Location: flashcards.php");
    exit();
}

$categoria_id = (int)$_GET['categoria'];
$mensagem = '';

// Buscar informações da categoria
$query_categoria = "SELECT nome FROM appestudo.flashcard_categories WHERE id = $1";
$result_categoria = pg_query_params($conexao, $query_categoria, array($categoria_id));
$categoria = pg_fetch_assoc($result_categoria);

// Buscar todas as matérias disponíveis
$query_materias = "SELECT idmateria, nome FROM appestudo.materia ORDER BY nome";
$result_materias = pg_query($conexao, $query_materias);

if (!$categoria) {
    header("Location: flashcards.php");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $materia_id = (int)$_POST['materia_id'];
    
    if ($materia_id > 0) {
        // Verificar se já existe um baralho para esta matéria nesta categoria
        $query_check = "SELECT id FROM appestudo.flashcard_decks 
                       WHERE category_id = $1 AND materia_id = $2";
        $result_check = pg_query_params($conexao, $query_check, 
                                      array($categoria_id, $materia_id));
        
        if (pg_num_rows($result_check) > 0) {
            $mensagem = "Já existe um baralho para esta matéria nesta categoria.";
        } else {
            // Buscar nome da matéria para usar como nome do baralho
            $query_materia = "SELECT nome FROM appestudo.materia WHERE idmateria = $1";
            $result_materia = pg_query_params($conexao, $query_materia, array($materia_id));
            $materia = pg_fetch_assoc($result_materia);
            
            $query = "INSERT INTO appestudo.flashcard_decks 
                     (category_id, usuario_id, nome, materia_id, status) 
                     VALUES ($1, $2, $3, $4, true)";
            
            $result = pg_query_params($conexao, $query, array(
                $categoria_id,
                $_SESSION['idusuario'],
                $materia['nome'],
                $materia_id
            ));
            
            if ($result) {
                header("Location: ver_baralhos.php?categoria=" . $categoria_id);
                exit();
            } else {
                $mensagem = "Erro ao criar baralho. Tente novamente.";
            }
        }
    } else {
        $mensagem = "Selecione uma matéria válida.";
    }
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Novo Baralho - <?php echo htmlspecialchars($categoria['nome']); ?></title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600&display=swap');

        :root {
            --primary-color: #000080;    /* Navy da marca */
            --paper-color: #FFFFFF;      /* Branco puro */
            --secondary-color: #4169E1;  /* Azul royal suave */
            --background-color: #E8ECF3; /* Cinza azulado claro */
            --text-color: #2C3345;      /* Azul muito escuro */
            --border-color: #B8C2CC;    /* Cinza azulado */
            --hover-bg: #F0F4F8;        /* Cor de hover suave */
            --error-bg: #FEE;           /* Fundo de erro */
            --error-border: #FAA;       /* Borda de erro */
            --error-text: #A00;         /* Texto de erro */
        }

        body {
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 40px;
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .form-container {
            background: var(--paper-color);
            padding: 40px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .breadcrumb {
            margin-bottom: 24px;
            color: var(--secondary-color);
            font-style: italic;
            font-size: 0.95rem;
        }

        h1 {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            margin-bottom: 32px;
            font-size: 2.25rem;
            font-weight: 600;
        }

        .form-group {
            margin-bottom: 24px;
            position: relative;
        }

        label {
            display: block;
            color: var(--primary-color);
            margin-bottom: 8px;
            font-weight: 500;
            font-size: 1rem;
        }

        .form-select {
            width: 100%;
            padding: 14px 18px;
            font-size: 1rem;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            background-color: var(--paper-color);
            color: var(--text-color);
            cursor: pointer;
            appearance: none;
            -webkit-appearance: none;
            transition: all 0.2s ease;
        }

        .form-group::after {
            content: '\f107';
            font-family: 'Font Awesome 6 Free';
            font-weight: 900;
            font-size: 1.2rem;
            color: var(--primary-color);
            position: absolute;
            right: 16px;
            top: 50%;
            transform: translateY(-25%);
            pointer-events: none;
        }

        .form-select:hover {
            border-color: var(--primary-color);
            background-color: var(--hover-bg);
        }

        .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(0, 0, 128, 0.1);
        }

        .form-select option {
            padding: 12px;
            font-size: 1rem;
        }

        .form-select option:first-child {
            color: #6B7280;
            font-style: italic;
        }

        .actions {
            display: flex;
            justify-content: flex-end;
            gap: 16px;
            margin-top: 32px;
            padding-top: 24px;
            border-top: 1px solid var(--border-color);
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            min-width: 120px;
            justify-content: center;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            border: none;
        }

        .btn-secondary {
            background: var(--paper-color);
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .btn-primary:hover {
            background: var(--secondary-color);
        }

        .btn-secondary:hover {
            background: var(--hover-bg);
        }

        .error-message {
            background: var(--error-bg);
            border: 1px solid var(--error-border);
            color: var(--error-text);
            padding: 12px 16px;
            border-radius: 6px;
            margin-bottom: 24px;
            font-size: 0.95rem;
        }

        @media (max-width: 768px) {
            body {
                padding: 20px;
            }

            .form-container {
                padding: 24px;
            }

            h1 {
                font-size: 1.75rem;
            }

            .actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
            }

            .form-select {
                padding: 12px 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="form-container">
            <div class="breadcrumb">
                Categoria: <?php echo htmlspecialchars($categoria['nome']); ?>
            </div>

            <h1>Novo Baralho</h1>

            <?php if (!empty($mensagem)): ?>
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($mensagem); ?>
                </div>
            <?php endif; ?>

            <form method="POST">
                <div class="form-group">
                    <label for="materia_id">Selecione a Matéria*</label>
                    <select id="materia_id" name="materia_id" required class="form-select">
                        <option value="" disabled selected>Selecione uma matéria...</option>
                        <?php while ($materia = pg_fetch_assoc($result_materias)): ?>
                            <option value="<?php echo $materia['idmateria']; ?>">
                                <?php echo htmlspecialchars($materia['nome']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>

                <div class="actions">
                    <a href="ver_baralhos.php?categoria=<?php echo $categoria_id; ?>" 
                       class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-check"></i>
                        Criar Baralho
                    </button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>