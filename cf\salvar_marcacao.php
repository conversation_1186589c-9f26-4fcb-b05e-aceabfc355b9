<?php
session_start();
// Desativa a exibição de erros no output
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Garante que estamos enviando JSON
header('Content-Type: application/json');
// Headers de segurança HTTP completos
header('X-Frame-Options: DENY');
header('X-Content-Type-Options: nosniff');
header('Referrer-Policy: no-referrer');
header('Permissions-Policy: interest-cohort=()');
header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');

require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/csrf.php';

try {
    // Log para depuração
    error_log("Iniciando salvamento de marcação");

    // Verifica se o usuário está logado
    if (!isset($_SESSION['idusuario'])) {
        http_response_code(401);
        echo json_encode(['erro' => 'Usuário não autenticado', 'redirect' => '../login_index.php']);
        exit;
    }

    // Lê o corpo da requisição
    $jsonInput = file_get_contents('php://input');
    $dados = json_decode($jsonInput, true);

    // Log dos dados recebidos
    error_log("Dados recebidos: " . json_encode($dados));

    if (!$dados || !isset($dados['marcacoes'])) {
        throw new Exception('Dados inválidos');
    }

    // CSRF token obrigatório
    if (!isset($dados['csrf_token']) || !validateCsrfToken($dados['csrf_token'])) {
        http_response_code(403);
        echo json_encode(['erro' => 'CSRF token inválido ou ausente']);
        exit;
    }

    // Usa o ID do usuário da sessão
    $usuario_id = $_SESSION['idusuario'];
    error_log("Usando ID do usuário da sessão: {$usuario_id}");

    // Sanitização básica de cada marcação
    foreach ($dados['marcacoes'] as &$marcacao) {
        $marcacao['elemento_id'] = filter_var($marcacao['elemento_id'] ?? '', FILTER_SANITIZE_STRING);
        $marcacao['pagina_url'] = filter_var($marcacao['pagina_url'] ?? '', FILTER_SANITIZE_URL);
        $marcacao['tipo_marcacao'] = filter_var($marcacao['tipo_marcacao'] ?? '', FILTER_SANITIZE_STRING);
        $marcacao['texto_marcado'] = filter_var($marcacao['texto_marcado'] ?? '', FILTER_SANITIZE_STRING);
        $marcacao['posicao_inicio'] = isset($marcacao['posicao_inicio']) ? intval($marcacao['posicao_inicio']) : null;
        $marcacao['posicao_fim'] = isset($marcacao['posicao_fim']) ? intval($marcacao['posicao_fim']) : null;

        // Validação adicional dos dados
        if (empty($marcacao['elemento_id']) || empty($marcacao['pagina_url']) || 
            empty($marcacao['tipo_marcacao']) || empty($marcacao['texto_marcado'])) {
            throw new Exception('Dados de marcação incompletos');
        }
    }
    unset($marcacao);

    $pdo = getDbConnection();
    $pdo->beginTransaction();
    
    // Primeiro, remove todas as marcações existentes para esta página
    $stmt = $pdo->prepare("
        DELETE FROM marcacoes 
        WHERE usuario_id = :usuario_id 
        AND pagina_url = :pagina_url
    ");
    
    if (count($dados['marcacoes']) > 0) {
        $stmt->execute([
            ':usuario_id' => $usuario_id,
            ':pagina_url' => $dados['marcacoes'][0]['pagina_url']
        ]);
    }
    
    // Agora insere as novas marcações
    $stmt = $pdo->prepare("
        INSERT INTO marcacoes 
        (usuario_id, pagina_url, elemento_id, tipo_marcacao, texto_marcado, posicao_inicio, posicao_fim) 
        VALUES 
        (:usuario_id, :pagina_url, :elemento_id, :tipo_marcacao, :texto_marcado, :posicao_inicio, :posicao_fim)
    ");
    
    foreach ($dados['marcacoes'] as $marcacao) {
        if (!isset($marcacao['elemento_id']) || 
            !isset($marcacao['tipo_marcacao']) || 
            !isset($marcacao['texto_marcado']) || 
            !isset($marcacao['posicao_inicio']) || 
            !isset($marcacao['posicao_fim'])) {
            throw new Exception('Dados de marcação incompletos');
        }

        $stmt->execute([
            ':usuario_id' => $usuario_id,
            ':pagina_url' => $marcacao['pagina_url'],
            ':elemento_id' => $marcacao['elemento_id'],
            ':tipo_marcacao' => $marcacao['tipo_marcacao'],
            ':texto_marcado' => $marcacao['texto_marcado'],
            ':posicao_inicio' => $marcacao['posicao_inicio'],
            ':posicao_fim' => $marcacao['posicao_fim']
        ]);
    }
    
    $pdo->commit();
    error_log("Operação concluída com sucesso");
    echo json_encode(['status' => 'success', 'mensagem' => 'Marcações atualizadas com sucesso']);
    
} catch (Exception $e) {
    error_log("Erro em salvar_marcacao.php: " . $e->getMessage());
    if (isset($pdo)) {
        $pdo->rollBack();
    }
    
    http_response_code(500);
    echo json_encode([
        'status' => 'error',
        'erro' => $e->getMessage()
    ]);
}



