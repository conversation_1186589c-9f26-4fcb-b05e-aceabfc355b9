<?php
/**
 * Teste final completo do sistema migrado
 */

// Configurações do banco
$host = 'barbeiro_br.mysql.dbaas.com.br';
$db_name = 'barbeiro_br';
$username = 'barbeiro_br';
$password = 'Lucasb90#';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== TESTE FINAL COMPLETO DO SISTEMA MIGRADO ===\n\n";
    
    // 1. Verificar usuários
    echo "👥 USUÁRIOS:\n";
    $stmt = $pdo->query("SELECT id, nome, cpf, tipo FROM usuarios ORDER BY tipo, nome");
    $users = $stmt->fetchAll();
    
    foreach ($users as $user) {
        echo "✅ {$user['nome']} ({$user['tipo']})\n";
        echo "   ID: {$user['id']}\n";
        echo "   CPF: {$user['cpf']}\n\n";
    }
    
    // 2. Verificar sistema de fidelidade
    echo "🎯 SISTEMA DE FIDELIDADE:\n";
    $stmt = $pdo->query("SELECT pf.*, u.nome as cliente_nome 
                         FROM pontuacao_fidelidade pf 
                         JOIN usuarios u ON pf.cliente_id = u.id");
    $pontuacoes = $stmt->fetchAll();
    
    foreach ($pontuacoes as $pont) {
        echo "✅ Cliente: {$pont['cliente_nome']}\n";
        echo "   Pontos cabelo: {$pont['pontos_cabelo']}\n";
        echo "   Pontos barba: {$pont['pontos_barba']}\n";
        echo "   Pontuação geral: {$pont['pontuacao_geral']}\n\n";
    }
    
    // 3. Verificar brindes pendentes
    echo "🎁 BRINDES PENDENTES:\n";
    $stmt = $pdo->query("SELECT bp.*, u.nome as cliente_nome 
                         FROM brindes_pendentes bp 
                         JOIN usuarios u ON bp.cliente_id = u.id 
                         ORDER BY bp.data_ganho DESC");
    $brindes = $stmt->fetchAll();
    
    if (empty($brindes)) {
        echo "- Nenhum brinde pendente\n\n";
    } else {
        foreach ($brindes as $brinde) {
            echo "✅ {$brinde['tipo_brinde']}\n";
            echo "   Cliente: {$brinde['cliente_nome']}\n";
            echo "   Data: {$brinde['data_ganho']}\n\n";
        }
    }
    
    // 4. Verificar histórico recente
    echo "📊 HISTÓRICO RECENTE (últimos 5 atendimentos):\n";
    $stmt = $pdo->query("SELECT ha.*, u.nome as cliente_nome 
                         FROM historico_atendimentos ha 
                         JOIN usuarios u ON ha.cliente_id = u.id 
                         ORDER BY ha.data_atendimento DESC 
                         LIMIT 5");
    $historico = $stmt->fetchAll();
    
    foreach ($historico as $atend) {
        echo "✅ {$atend['cliente_nome']} - {$atend['tipo_servico']}\n";
        echo "   Data: {$atend['data_atendimento']}\n\n";
    }
    
    // 5. Verificar integridade dos dados
    echo "🔍 VERIFICAÇÃO DE INTEGRIDADE:\n";
    
    // Verificar se todos os IDs estão no formato correto
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM usuarios WHERE id REGEXP '^[0-9]{11}-(CLIENTE|BARBEIRO)$'");
    $formatoCorreto = $stmt->fetch()['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM usuarios");
    $totalUsuarios = $stmt->fetch()['total'];
    
    echo "✅ Formato de IDs: $formatoCorreto/$totalUsuarios corretos\n";
    
    // Verificar registros órfãos
    $tabelas = [
        'perfis_clientes' => 'usuario_id',
        'pontuacao_fidelidade' => 'cliente_id',
        'historico_atendimentos' => 'cliente_id',
        'brindes_pendentes' => 'cliente_id'
    ];
    
    $integridadeOK = true;
    foreach ($tabelas as $tabela => $coluna) {
        $stmt = $pdo->query("SELECT COUNT(*) as orfaos 
                            FROM $tabela t 
                            LEFT JOIN usuarios u ON t.$coluna = u.id 
                            WHERE u.id IS NULL");
        $orfaos = $stmt->fetch()['orfaos'];
        
        if ($orfaos == 0) {
            echo "✅ $tabela: sem registros órfãos\n";
        } else {
            echo "❌ $tabela: $orfaos registros órfãos\n";
            $integridadeOK = false;
        }
    }
    
    // 6. Estatísticas finais
    echo "\n📈 ESTATÍSTICAS FINAIS:\n";
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM usuarios WHERE tipo = 'cliente'");
    $totalClientes = $stmt->fetch()['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM usuarios WHERE tipo = 'barbeiro'");
    $totalBarbeiros = $stmt->fetch()['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM historico_atendimentos");
    $totalAtendimentos = $stmt->fetch()['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM brindes_pendentes");
    $totalBrindes = $stmt->fetch()['total'];
    
    echo "- Clientes: $totalClientes\n";
    echo "- Barbeiros: $totalBarbeiros\n";
    echo "- Total de atendimentos: $totalAtendimentos\n";
    echo "- Brindes pendentes: $totalBrindes\n";
    
    // Resultado final
    echo "\n" . str_repeat("=", 60) . "\n";
    
    if ($formatoCorreto == $totalUsuarios && $integridadeOK) {
        echo "🎉 SISTEMA MIGRADO COM SUCESSO!\n";
        echo "✅ Todos os IDs estão no novo formato\n";
        echo "✅ Nomes dos usuários sendo exibidos corretamente\n";
        echo "✅ Integridade dos dados mantida\n";
        echo "✅ Sistema pronto para produção!\n\n";
        
        echo "🚀 BENEFÍCIOS ALCANÇADOS:\n";
        echo "- ✅ IDs sem espaços (URLs mais limpos)\n";
        echo "- ✅ CPF + tipo (identificação única)\n";
        echo "- ✅ Consultas mais eficientes\n";
        echo "- ✅ Mesmo CPF pode ser cliente e barbeiro\n";
        echo "- ✅ Nomes sempre exibidos nas interfaces\n";
        
    } else {
        echo "⚠️ Alguns problemas detectados:\n";
        if ($formatoCorreto != $totalUsuarios) {
            echo "- Nem todos os IDs estão no formato correto\n";
        }
        if (!$integridadeOK) {
            echo "- Problemas de integridade detectados\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Erro no teste final: " . $e->getMessage() . "\n";
}
?>
