<?php
session_start();
if (!isset($_SESSION['idusuario']) || $_SESSION['idusuario'] != 1) {
    header("Location: login.php");
    exit();
}

include_once("assets/config.php");

$usuario_id = $_SESSION['idusuario'];

// Consulta para buscar os conteúdos com status de estudo e organizá-los por matéria
$query = "
    SELECT 
    uc.id, 
    m.idmateria,
    m.nome AS materia_nome, 
    m.cor, 
    ce.descricao, 
    ce.capitulo, 
    uc.status_estudo,
    CAST(split_part(ce.capitulo, '.', 1) AS INTEGER) AS capitulo_principal,
    COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 2), '') AS INTEGER), 0) AS subnivel1,
    COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 3), '') AS INTEGER), 0) AS subnivel2
FROM 
    appestudo.usuario_conteudo AS uc
JOIN 
    appestudo.conteudo_edital AS ce ON uc.conteudo_id = ce.id_conteudo
JOIN 
    appestudo.materia AS m ON ce.materia_id = m.idmateria
WHERE 
    uc.usuario_id = $usuario_id
ORDER BY 
    capitulo_principal, subnivel1, subnivel2, ce.capitulo;

";

$result = pg_query($conexao, $query);
$conteudos_por_materia = [];

// Organizar os conteúdos por matéria
while ($row = pg_fetch_assoc($result)) {
    $materia_id = $row['idmateria'];
    if (!isset($conteudos_por_materia[$materia_id])) {
        $conteudos_por_materia[$materia_id] = [
            'nome' => $row['materia_nome'],
            'cor' => $row['cor'],
            'conteudos' => []
        ];
    }
    $conteudos_por_materia[$materia_id]['conteudos'][] = $row;
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Progresso de Estudo</title>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .btn-voltar {
            position: fixed;
            top: 20px;
            left: 20px;
            background: linear-gradient(145deg, #8b4513, #a0522d);
            color: #fff;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            text-decoration: none;
            z-index: 1000;
        }

        .btn-voltar:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            background: linear-gradient(145deg, #a0522d, #8b4513);
        }

        .btn-voltar i {
            font-size: 1.5rem;
        }
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier Prime', monospace;
            min-height: 100vh;
            background: linear-gradient(135deg, #8B0000, #B22222);
            padding: 40px 20px;
            line-height: 1.6;
            color: #2c1810;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header-vintage {
            text-align: center;
            color: #fff;
            margin-bottom: 40px;
        }

        .header-vintage h2 {
            font-family: 'Cinzel', serif;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 2px;
            margin-bottom: 10px;
        }

        .header-vintage p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .materias-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .materia-group {
            background: #fff;
            border-radius: 15px;
            margin-bottom: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        .materia-header {
            display: flex;
            align-items: center;
            padding: 20px;
            background: #fdfbf7;
            border-bottom: 3px solid;
            cursor: pointer;
        }

        .materia-icon {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            color: #fff;
            font-size: 1.4rem;
            margin-right: 20px;
            transition: transform 0.3s ease;
        }

        .materia-nome {
            font-family: 'Cinzel', serif;
            font-size: 1.4rem;
            color: #2c1810;
            flex-grow: 1;
        }

        .materia-toggle {
            font-size: 1.2rem;
            color: #8B4513;
            transform: rotate(-90deg);
            transition: transform 0.3s ease;
        }

        .materia-group.expanded .materia-toggle {
            transform: rotate(0deg);
        }

        .conteudo-list {
            padding: 20px;
            display: none;
        }

        .materia-group.expanded .conteudo-list {
            display: block;
        }

        .conteudo-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #fdfbf7;
            border: 1px solid #e8e0d8;
            border-radius: 10px;
            padding: 15px 20px;
            margin-bottom: 10px;
            transition: transform 0.3s ease;
        }

        .conteudo-info {
            flex: 1;
            padding-right: 20px;
        }

        .conteudo-header {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .capitulo-badge {
            background: #8B4513;
            color: #fff;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
            min-width: 60px;
            text-align: center;
        }

        .form-group {
            width: 180px;
        }

        select {
            width: 100%;
            padding: 8px 12px;
            border: 2px solid #d3c5b8;
            border-radius: 6px;
            font-family: 'Courier Prime', monospace;
            font-size: 0.9rem;
            color: #2c1810;
            background: #fff;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        select.status-nao-estudado { border-left: 4px solid #d32f2f; }
        select.status-estudando { border-left: 4px solid #f57c00; }
        select.status-estudado { border-left: 4px solid #388e3c; }

        select:hover, select:focus {
            border-color: #8B4513;
            outline: none;
        }

        .btn-salvar {
            display: block;
            width: 100%;
            padding: 16px;
            background: linear-gradient(145deg, #8b4513, #a0522d);
            color: #fff;
            border: none;
            border-radius: 10px;
            font-size: 1.2rem;
            font-family: 'Cinzel', serif;
            cursor: pointer;
            margin-top: 30px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 2px;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.2);
        }

        .btn-salvar:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 69, 19, 0.3);
            background: linear-gradient(145deg, #a0522d, #8b4513);
        }

        @media (max-width: 768px) {
            .btn-voltar {
                top: 10px;
                left: 10px;
                width: 40px;
                height: 40px;
            }

            .btn-voltar i {
                font-size: 1.2rem;
            }
            body {
                padding: 20px 10px;
            }

            .header-vintage h2 {
                font-size: 2rem;
            }

            .materias-container {
                padding: 20px;
            }

            .conteudo-item {
                flex-direction: column;
                gap: 15px;
            }

            .conteudo-info {
                padding-right: 0;
            }

            .form-group {
                width: 100%;
            }
        }
    </style>
</head>
<body>
<a href="https://concurseirooff.com.br/edital_verticalizado/index.php" class="btn-voltar">
    <i class="fas fa-arrow-left"></i>
</a>
<div class="container">
    <div class="header-vintage">
        <h2>Progresso de Estudo</h2>
        <p>Acompanhe e atualize seu progresso em cada matéria</p>
    </div>

    <div class="materias-container">
        <form action="atualizar_estudo.php" method="POST">
            <?php foreach ($conteudos_por_materia as $materia_id => $materia): ?>
                <div class="materia-group">
                    <div class="materia-header" onclick="toggleMateria(this)" style="border-color: <?= htmlspecialchars($materia['cor']) ?>">
                        <div class="materia-icon" style="background-color: <?= htmlspecialchars($materia['cor']) ?>">
                            <i class="fas fa-book"></i>
                        </div>
                        <h3 class="materia-nome"><?= htmlspecialchars($materia['nome']) ?></h3>
                        <i class="fas fa-chevron-down materia-toggle"></i>
                    </div>

                    <div class="conteudo-list">
                        <?php foreach ($materia['conteudos'] as $conteudo): ?>
                            <div class="conteudo-item">
                                <div class="conteudo-info">
                                    <div class="conteudo-header">
                                        <span class="capitulo-badge">Cap. <?= htmlspecialchars($conteudo['capitulo']) ?></span>
                                        <span><?= htmlspecialchars($conteudo['descricao']) ?></span>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <select name="status_estudo[<?= $conteudo['id'] ?>]"
                                            id="status_<?= $conteudo['id'] ?>"
                                            class="status-<?= strtolower(str_replace(' ', '-', $conteudo['status_estudo'])) ?>">
                                        <option value="Não Estudado" <?= $conteudo['status_estudo'] == 'Não Estudado' ? 'selected' : '' ?>>
                                            Não Estudado
                                        </option>
                                        <option value="Estudando" <?= $conteudo['status_estudo'] == 'Estudando' ? 'selected' : '' ?>>
                                            Estudando
                                        </option>
                                        <option value="Estudado" <?= $conteudo['status_estudo'] == 'Estudado' ? 'selected' : '' ?>>
                                            Estudado
                                        </option>
                                    </select>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>

            <button type="submit" class="btn-salvar">
                <i class="fas fa-save"></i> Salvar Progresso
            </button>
        </form>
    </div>
</div>

<script>
    function toggleMateria(header) {
        const materiaGroup = header.closest('.materia-group');
        materiaGroup.classList.toggle('expanded');
    }

    document.querySelectorAll('select').forEach(select => {
        // Define a classe inicial
        select.className = 'status-' + select.value.toLowerCase().replace(' ', '-');

        // Atualiza quando mudar
        select.addEventListener('change', function() {
            this.className = 'status-' + this.value.toLowerCase().replace(' ', '-');
        });
    });
</script>
</body>
</html>
