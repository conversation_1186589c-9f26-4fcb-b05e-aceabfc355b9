-- Backup da Estrutura do Banco de Dados
-- Gerado em: 2025-08-19 15:35:53

-- =============================================
-- TABELAS ENCONTRADAS
-- =============================================

-- Tabela: agenda
CREATE TABLE appestudo.agenda (
    id integer NOT NULL DEFAULT nextval('appestudo.agenda_id_seq'::regclass),
    usuario_idusuario integer NOT NULL,
    titulo character varying(45) NOT NULL,
    data_inicio timestamp without time zone NOT NULL,
    data_fim timestamp without time zone NOT NULL,
    detalhes character varying(250),
    tipo_evento character varying(50),
    realizado boolean DEFAULT false
);

ALTER TABLE appestudo.agenda ADD PRIMARY KEY (id);


-- Tabela: ajustes_plano
CREATE TABLE appestudo.ajustes_plano (
    id integer NOT NULL DEFAULT nextval('appestudo.ajustes_plano_id_seq'::regclass),
    prova_id integer,
    usuario_id integer,
    data_inicio date,
    data_fim date,
    dias_estudo_semana integer DEFAULT 6,
    horas_estudo_dia integer,
    intensificar_revisoes boolean DEFAULT true,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE appestudo.ajustes_plano ADD PRIMARY KEY (id);

CREATE UNIQUE INDEX unique_ajustes_prova_id ON appestudo.ajustes_plano USING btree (prova_id);

-- Tabela: anotacao
CREATE TABLE appestudo.anotacao (
    planejamento_idplanejamento integer NOT NULL,
    planejamento_usuario_idusuario integer NOT NULL,
    materia_idmateria integer NOT NULL,
    detalhe text
);


-- Tabela: anotacoes
CREATE TABLE appestudo.anotacoes (
    id integer NOT NULL DEFAULT nextval('appestudo.anotacoes_id_seq'::regclass),
    usuario_id integer NOT NULL,
    pagina_url text NOT NULL,
    elemento_id text NOT NULL,
    texto_anotacao text NOT NULL,
    conteudo_anotado text NOT NULL,
    data_criacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    posicao_inicio integer,
    posicao_fim integer
);

ALTER TABLE appestudo.anotacoes ADD PRIMARY KEY (id);

CREATE INDEX idx_anotacoes_composto ON appestudo.anotacoes USING btree (usuario_id, pagina_url, elemento_id);
CREATE INDEX idx_anotacoes_elemento ON appestudo.anotacoes USING btree (elemento_id);
CREATE INDEX idx_anotacoes_pagina ON appestudo.anotacoes USING btree (pagina_url);
CREATE INDEX idx_anotacoes_usuario ON appestudo.anotacoes USING btree (usuario_id);

-- Tabela: assinaturas
CREATE TABLE appestudo.assinaturas (
    id integer NOT NULL DEFAULT nextval('appestudo.assinaturas_id_seq'::regclass),
    usuario_id integer NOT NULL,
    plano_id integer NOT NULL,
    modulo character varying(50) NOT NULL,
    data_inicio timestamp without time zone NOT NULL DEFAULT CURRENT_TIMESTAMP,
    data_fim timestamp without time zone NOT NULL,
    status boolean DEFAULT true,
    valor_pago numeric NOT NULL,
    forma_pagamento character varying(50),
    codigo_transacao character varying(100),
    tipo_assinatura character varying(20),
    renovacao_automatica boolean DEFAULT false,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE appestudo.assinaturas ADD PRIMARY KEY (id);

CREATE UNIQUE INDEX idx_unique_active_subscription ON appestudo.assinaturas USING btree (usuario_id, modulo) WHERE (status = true);

-- Tabela: conteudo_edital
CREATE TABLE appestudo.conteudo_edital (
    id_conteudo integer NOT NULL DEFAULT nextval('appestudo.conteudo_edital_id_conteudo_seq'::regclass),
    edital_id integer NOT NULL,
    materia_id integer NOT NULL,
    descricao text NOT NULL,
    capitulo character varying(50),
    ordem integer
);

ALTER TABLE appestudo.conteudo_edital ADD PRIMARY KEY (id_conteudo);

CREATE INDEX idx_conteudo_edital_cap_pattern ON appestudo.conteudo_edital USING btree (capitulo text_pattern_ops);
CREATE INDEX idx_conteudo_edital_hierarchy ON appestudo.conteudo_edital USING btree (materia_id, edital_id, ((string_to_array((capitulo)::text, '.'::text))::integer[]));
CREATE INDEX idx_conteudo_edital_hierarquia ON appestudo.conteudo_edital USING btree (materia_id, edital_id, capitulo);
CREATE INDEX idx_conteudo_edital_materia_capitulo ON appestudo.conteudo_edital USING btree (materia_id, capitulo);
CREATE INDEX idx_conteudo_edital_nivel_1 ON appestudo.conteudo_edital USING btree (materia_id, capitulo) WHERE ((capitulo)::text ~ '^[0-9]+$'::text);
CREATE INDEX idx_conteudo_edital_nivel_2 ON appestudo.conteudo_edital USING btree (materia_id, capitulo) WHERE ((capitulo)::text ~ '^[0-9]+\.[0-9]+$'::text);
CREATE INDEX idx_conteudo_edital_nivel_3 ON appestudo.conteudo_edital USING btree (materia_id, capitulo) WHERE ((capitulo)::text ~ '^[0-9]+\.[0-9]+\.[0-9]+$'::text);
CREATE INDEX idx_conteudo_edital_ordem ON appestudo.conteudo_edital USING btree (materia_id, ordem);
CREATE INDEX idx_conteudo_edital_sorting ON appestudo.conteudo_edital USING btree (materia_id, capitulo text_pattern_ops);

-- Tabela: curso
CREATE TABLE appestudo.curso (
    idcurso integer NOT NULL DEFAULT nextval('appestudo.curso_idcurso_seq'::regclass),
    nome character varying(500),
    logo_url character varying(255)
);

ALTER TABLE appestudo.curso ADD PRIMARY KEY (idcurso);


-- Tabela: edital
CREATE TABLE appestudo.edital (
    id_edital integer NOT NULL DEFAULT nextval('appestudo.edital_id_edital_seq'::regclass),
    nome character varying(255) NOT NULL,
    descricao text,
    ano integer,
    orgao character varying(255),
    logo_url character varying(255)
);

ALTER TABLE appestudo.edital ADD PRIMARY KEY (id_edital);


-- Tabela: estudos
CREATE TABLE appestudo.estudos (
    idestudos integer NOT NULL DEFAULT nextval('appestudo.estudos_idestudos_seq'::regclass),
    data date NOT NULL,
    tempo_liquido time without time zone NOT NULL,
    tempo_bruto time without time zone NOT NULL,
    tempo_perdido time without time zone NOT NULL,
    ponto_estudado text,
    hora_inicio time without time zone NOT NULL,
    hora_fim time without time zone NOT NULL,
    metodo character varying(45),
    q_total integer,
    q_errada integer,
    q_certa integer,
    planejamento_idplanejamento integer NOT NULL,
    planejamento_usuario_idusuario integer NOT NULL,
    materia_idmateria integer NOT NULL,
    idcurso integer,
    revisoes date,
    descricao text,
    hora_revisao time without time zone,
    revisao_concluida timestamp without time zone,
    link_conteudo character varying(1000)
);

ALTER TABLE appestudo.estudos ADD PRIMARY KEY (idestudos);


-- Tabela: eventos_calendario
CREATE TABLE appestudo.eventos_calendario (
    id integer NOT NULL DEFAULT nextval('appestudo.eventos_calendario_id_seq'::regclass),
    usuario_id integer NOT NULL,
    titulo character varying(255) NOT NULL,
    data_inicio date NOT NULL,
    data_fim date NOT NULL,
    tipo character varying(50) NOT NULL,
    materia character varying(100) NOT NULL
);

ALTER TABLE appestudo.eventos_calendario ADD PRIMARY KEY (id);


-- Tabela: flashcard_categories
CREATE TABLE appestudo.flashcard_categories (
    id integer NOT NULL DEFAULT nextval('appestudo.flashcard_categories_id_seq'::regclass),
    usuario_id integer NOT NULL,
    nome character varying(100) NOT NULL,
    descricao text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    status boolean DEFAULT true
);

ALTER TABLE appestudo.flashcard_categories ADD PRIMARY KEY (id);

CREATE INDEX idx_flashcard_categories_user ON appestudo.flashcard_categories USING btree (usuario_id);

-- Tabela: flashcard_deck_association
CREATE TABLE appestudo.flashcard_deck_association (
    flashcard_id integer NOT NULL,
    deck_id integer NOT NULL
);

ALTER TABLE appestudo.flashcard_deck_association ADD PRIMARY KEY (flashcard_id, deck_id);


-- Tabela: flashcard_decks
CREATE TABLE appestudo.flashcard_decks (
    id integer NOT NULL DEFAULT nextval('appestudo.flashcard_decks_id_seq'::regclass),
    category_id integer,
    usuario_id integer NOT NULL,
    nome character varying(100) NOT NULL,
    descricao text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    status boolean DEFAULT true,
    materia_id integer
);

ALTER TABLE appestudo.flashcard_decks ADD PRIMARY KEY (id);

CREATE INDEX idx_flashcard_deck_user ON appestudo.flashcard_decks USING btree (usuario_id);

-- Tabela: flashcard_materias
CREATE TABLE appestudo.flashcard_materias (
    flashcard_id integer NOT NULL,
    materia_id integer NOT NULL
);

ALTER TABLE appestudo.flashcard_materias ADD PRIMARY KEY (flashcard_id, materia_id);


-- Tabela: flashcard_mindmaps
CREATE TABLE appestudo.flashcard_mindmaps (
    id integer NOT NULL DEFAULT nextval('appestudo.flashcard_mindmaps_id_seq'::regclass),
    flashcard_id integer NOT NULL,
    imagem_url text,
    imagem_base64 text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE appestudo.flashcard_mindmaps ADD PRIMARY KEY (id);


-- Tabela: flashcard_progress
CREATE TABLE appestudo.flashcard_progress (
    id integer NOT NULL DEFAULT nextval('appestudo.flashcard_progress_id_seq'::regclass),
    usuario_id integer NOT NULL,
    flashcard_id integer NOT NULL,
    nivel_conhecimento integer,
    ultima_revisao timestamp without time zone,
    proxima_revisao timestamp without time zone,
    total_revisoes integer,
    revisoes_corretas integer,
    intervalo_atual integer,
    fator_facilidade numeric DEFAULT 2.5,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE appestudo.flashcard_progress ADD PRIMARY KEY (id);

CREATE UNIQUE INDEX flashcard_progress_usuario_id_flashcard_id_key ON appestudo.flashcard_progress USING btree (usuario_id, flashcard_id);
CREATE INDEX idx_flashcard_progress_next_review ON appestudo.flashcard_progress USING btree (proxima_revisao);
CREATE INDEX idx_flashcard_progress_user ON appestudo.flashcard_progress USING btree (usuario_id);

-- Tabela: flashcard_review_history
CREATE TABLE appestudo.flashcard_review_history (
    id integer NOT NULL DEFAULT nextval('appestudo.flashcard_review_history_id_seq'::regclass),
    progress_id integer NOT NULL,
    avaliacao integer NOT NULL,
    tempo_resposta integer,
    data_revisao timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE appestudo.flashcard_review_history ADD PRIMARY KEY (id);


-- Tabela: flashcard_topic_association
CREATE TABLE appestudo.flashcard_topic_association (
    flashcard_id integer NOT NULL,
    topic_id integer NOT NULL
);

ALTER TABLE appestudo.flashcard_topic_association ADD PRIMARY KEY (flashcard_id, topic_id);


-- Tabela: flashcard_topics
CREATE TABLE appestudo.flashcard_topics (
    id integer NOT NULL DEFAULT nextval('appestudo.flashcard_topics_id_seq'::regclass),
    deck_id integer NOT NULL,
    nome character varying(255) NOT NULL,
    descricao text,
    ordem integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    status boolean DEFAULT true
);

ALTER TABLE appestudo.flashcard_topics ADD PRIMARY KEY (id);

CREATE INDEX idx_flashcard_topics_deck ON appestudo.flashcard_topics USING btree (deck_id);

-- Tabela: flashcards
CREATE TABLE appestudo.flashcards (
    id integer NOT NULL DEFAULT nextval('appestudo.flashcards_id_seq'::regclass),
    pergunta text NOT NULL,
    resposta text NOT NULL,
    resumo text,
    previsao_legal text,
    jurisprudencia text,
    mapa_mental text,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    status boolean DEFAULT true
);

ALTER TABLE appestudo.flashcards ADD PRIMARY KEY (id);


-- Tabela: forum_categorias
CREATE TABLE appestudo.forum_categorias (
    id integer NOT NULL DEFAULT nextval('appestudo.forum_categorias_id_seq'::regclass),
    nome character varying(100) NOT NULL,
    descricao text,
    ordem integer,
    status boolean DEFAULT true,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE appestudo.forum_categorias ADD PRIMARY KEY (id);


-- Tabela: forum_notificacoes
CREATE TABLE appestudo.forum_notificacoes (
    id integer NOT NULL DEFAULT nextval('appestudo.forum_notificacoes_id_seq'::regclass),
    usuario_id integer NOT NULL,
    mensagem text NOT NULL,
    lida boolean DEFAULT false,
    data_criada timestamp without time zone DEFAULT now(),
    topico_id integer
);

ALTER TABLE appestudo.forum_notificacoes ADD PRIMARY KEY (id);


-- Tabela: forum_respostas
CREATE TABLE appestudo.forum_respostas (
    id integer NOT NULL DEFAULT nextval('appestudo.forum_respostas_id_seq'::regclass),
    topico_id integer,
    usuario_id integer,
    conteudo text NOT NULL,
    is_solucao boolean DEFAULT false,
    status boolean DEFAULT true,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    parent_id integer
);

ALTER TABLE appestudo.forum_respostas ADD PRIMARY KEY (id);


-- Tabela: forum_topicos
CREATE TABLE appestudo.forum_topicos (
    id integer NOT NULL DEFAULT nextval('appestudo.forum_topicos_id_seq'::regclass),
    categoria_id integer,
    usuario_id integer,
    titulo character varying(200) NOT NULL,
    conteudo text NOT NULL,
    views integer,
    status boolean DEFAULT true,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    updated_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE appestudo.forum_topicos ADD PRIMARY KEY (id);


-- Tabela: forum_votos
CREATE TABLE appestudo.forum_votos (
    id integer NOT NULL DEFAULT nextval('appestudo.forum_votos_id_seq'::regclass),
    usuario_id integer,
    resposta_id integer,
    tipo_voto smallint NOT NULL,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE appestudo.forum_votos ADD PRIMARY KEY (id);

CREATE UNIQUE INDEX forum_votos_usuario_id_resposta_id_key ON appestudo.forum_votos USING btree (usuario_id, resposta_id);

-- Tabela: itens
CREATE TABLE appestudo.itens (
    id integer NOT NULL DEFAULT nextval('appestudo.itens_id_seq'::regclass),
    nome character varying(255) NOT NULL,
    quantidade integer NOT NULL DEFAULT 1,
    unidade character varying(50) NOT NULL DEFAULT 'unidade'::character varying,
    preco numeric NOT NULL DEFAULT 0.00,
    categoria character varying(50) NOT NULL DEFAULT 'mercearia'::character varying,
    urgente boolean NOT NULL DEFAULT false,
    status character varying(20) NOT NULL DEFAULT 'pendente'::character varying,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_compra timestamp without time zone
);

ALTER TABLE appestudo.itens ADD PRIMARY KEY (id);


-- Tabela: kanban_etiquetas
CREATE TABLE appestudo.kanban_etiquetas (
    id integer NOT NULL DEFAULT nextval('appestudo.kanban_etiquetas_id_seq'::regclass),
    nome character varying(50) NOT NULL,
    cor character varying(7) NOT NULL,
    usuario_id integer
);

ALTER TABLE appestudo.kanban_etiquetas ADD PRIMARY KEY (id);

CREATE INDEX idx_kanban_etiquetas_usuario ON appestudo.kanban_etiquetas USING btree (usuario_id);

-- Tabela: kanban_tarefa_etiqueta
CREATE TABLE appestudo.kanban_tarefa_etiqueta (
    tarefa_id integer NOT NULL,
    etiqueta_id integer NOT NULL
);

ALTER TABLE appestudo.kanban_tarefa_etiqueta ADD PRIMARY KEY (tarefa_id, etiqueta_id);


-- Tabela: kanban_tarefas
CREATE TABLE appestudo.kanban_tarefas (
    id integer NOT NULL DEFAULT nextval('appestudo.kanban_tarefas_id_seq'::regclass),
    titulo character varying(100) NOT NULL,
    descricao text,
    status character varying(20) NOT NULL DEFAULT 'backlog'::character varying,
    prioridade character varying(10) NOT NULL DEFAULT 'media'::character varying,
    data_criacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_limite date,
    data_conclusao timestamp without time zone,
    usuario_id integer
);

ALTER TABLE appestudo.kanban_tarefas ADD PRIMARY KEY (id);

CREATE INDEX idx_kanban_tarefas_status ON appestudo.kanban_tarefas USING btree (status);
CREATE INDEX idx_kanban_tarefas_usuario ON appestudo.kanban_tarefas USING btree (usuario_id);

-- Tabela: leitura_lei_seca
CREATE TABLE appestudo.leitura_lei_seca (
    id integer NOT NULL DEFAULT nextval('appestudo.leitura_lei_seca_id_seq'::regclass),
    id_usuario integer NOT NULL,
    dia character varying(10),
    materia character varying(255) NOT NULL,
    conteudo text NOT NULL,
    lido boolean DEFAULT false
);

ALTER TABLE appestudo.leitura_lei_seca ADD PRIMARY KEY (id);

CREATE UNIQUE INDEX leitura_lei_seca_id_usuario_dia_materia_key ON appestudo.leitura_lei_seca USING btree (id_usuario, dia, materia);

-- Tabela: lexjus_anotacoes
CREATE TABLE appestudo.lexjus_anotacoes (
    id integer NOT NULL DEFAULT nextval('appestudo.lexjus_anotacoes_id_seq'::regclass),
    usuario_id integer NOT NULL,
    artigo_numero character varying(100) NOT NULL,
    texto text NOT NULL,
    data_criacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    lei_id integer
);

ALTER TABLE appestudo.lexjus_anotacoes ADD PRIMARY KEY (id);

CREATE INDEX idx_lexjus_anotacoes_usuario_lei ON appestudo.lexjus_anotacoes USING btree (usuario_id, lei_id);

-- Tabela: lexjus_config_revisao
CREATE TABLE appestudo.lexjus_config_revisao (
    id integer NOT NULL DEFAULT nextval('appestudo.lexjus_config_revisao_id_seq'::regclass),
    usuario_id integer NOT NULL,
    facilidade_inicial numeric DEFAULT 2.50,
    intervalo_inicial integer DEFAULT 1,
    multiplicador_facil numeric DEFAULT 1.30,
    multiplicador_dificil numeric DEFAULT 0.80,
    max_revisoes_dia integer DEFAULT 20,
    max_novos_dia integer DEFAULT 10,
    horario_preferido_inicio time without time zone DEFAULT '08:00:00'::time without time zone,
    horario_preferido_fim time without time zone DEFAULT '22:00:00'::time without time zone,
    notificar_revisoes boolean DEFAULT true,
    notificar_antecedencia integer DEFAULT 60,
    auto_promover_faceis boolean DEFAULT true,
    auto_rebaixar_dificeis boolean DEFAULT true,
    limite_acertos_promocao integer DEFAULT 3,
    limite_erros_rebaixamento integer DEFAULT 2,
    data_criacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE appestudo.lexjus_config_revisao ADD PRIMARY KEY (id);

CREATE UNIQUE INDEX unique_config_usuario ON appestudo.lexjus_config_revisao USING btree (usuario_id);

-- Tabela: lexjus_favoritos
CREATE TABLE appestudo.lexjus_favoritos (
    id integer NOT NULL DEFAULT nextval('appestudo.lexjus_favoritos_id_seq'::regclass),
    usuario_id integer NOT NULL,
    artigo_numero character varying(100) NOT NULL,
    data_adicionado timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    lei_id integer
);

ALTER TABLE appestudo.lexjus_favoritos ADD PRIMARY KEY (id);

CREATE UNIQUE INDEX unique_usuario_lei_artigo_favorito ON appestudo.lexjus_favoritos USING btree (usuario_id, lei_id, artigo_numero);
CREATE INDEX idx_lexjus_favoritos_usuario_lei ON appestudo.lexjus_favoritos USING btree (usuario_id, lei_id);

-- Tabela: lexjus_historico_revisoes
CREATE TABLE appestudo.lexjus_historico_revisoes (
    id integer NOT NULL DEFAULT nextval('appestudo.lexjus_historico_revisoes_id_seq'::regclass),
    revisao_id integer NOT NULL,
    usuario_id integer NOT NULL,
    artigo_numero character varying(100) NOT NULL,
    qualidade_resposta integer NOT NULL,
    tempo_resposta integer,
    tipo_revisao character varying(20) NOT NULL,
    facilidade_anterior numeric,
    intervalo_anterior integer,
    facilidade_nova numeric,
    intervalo_novo integer,
    dispositivo character varying(50),
    hora_do_dia integer,
    dia_da_semana integer,
    data_revisao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    lei_id integer
);

ALTER TABLE appestudo.lexjus_historico_revisoes ADD PRIMARY KEY (id);

CREATE INDEX idx_lexjus_historico_revisao_id ON appestudo.lexjus_historico_revisoes USING btree (revisao_id);
CREATE INDEX idx_lexjus_historico_usuario_data ON appestudo.lexjus_historico_revisoes USING btree (usuario_id, data_revisao);

-- Tabela: lexjus_leis
CREATE TABLE appestudo.lexjus_leis (
    id integer NOT NULL DEFAULT nextval('appestudo.lexjus_leis_id_seq'::regclass),
    codigo character varying(10) NOT NULL,
    nome character varying(100) NOT NULL,
    nome_completo character varying(200) NOT NULL,
    descricao text,
    arquivo_json character varying(100) NOT NULL,
    cor_tema character varying(7) DEFAULT '#3498db'::character varying,
    icone character varying(50) DEFAULT 'fas fa-book'::character varying,
    ativa boolean DEFAULT true,
    ordem_exibicao integer DEFAULT 1,
    total_artigos integer,
    data_criacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE appestudo.lexjus_leis ADD PRIMARY KEY (id);

CREATE UNIQUE INDEX lexjus_leis_codigo_key ON appestudo.lexjus_leis USING btree (codigo);

-- Tabela: lexjus_lista_artigos
CREATE TABLE appestudo.lexjus_lista_artigos (
    id integer NOT NULL DEFAULT nextval('appestudo.lexjus_lista_artigos_id_seq'::regclass),
    lista_id integer NOT NULL,
    artigo_numero character varying(100) NOT NULL,
    data_adicionado timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE appestudo.lexjus_lista_artigos ADD PRIMARY KEY (id);

CREATE UNIQUE INDEX lexjus_lista_artigos_lista_id_artigo_numero_key ON appestudo.lexjus_lista_artigos USING btree (lista_id, artigo_numero);

-- Tabela: lexjus_listas
CREATE TABLE appestudo.lexjus_listas (
    id integer NOT NULL DEFAULT nextval('appestudo.lexjus_listas_id_seq'::regclass),
    usuario_id integer NOT NULL,
    nome character varying(100) NOT NULL,
    descricao text,
    data_criacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    cor character varying(7) DEFAULT '#e74c3c'::character varying,
    lei_id integer
);

ALTER TABLE appestudo.lexjus_listas ADD PRIMARY KEY (id);

CREATE INDEX idx_lexjus_listas_cor ON appestudo.lexjus_listas USING btree (cor);
CREATE INDEX idx_lexjus_listas_usuario_lei ON appestudo.lexjus_listas USING btree (usuario_id, lei_id);

-- Tabela: lexjus_progresso
CREATE TABLE appestudo.lexjus_progresso (
    id integer NOT NULL DEFAULT nextval('appestudo.lexjus_progresso_id_seq'::regclass),
    usuario_id integer NOT NULL,
    artigo_numero character varying(100) NOT NULL,
    lido boolean DEFAULT true,
    data_leitura timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    lei_id integer
);

ALTER TABLE appestudo.lexjus_progresso ADD PRIMARY KEY (id);

CREATE UNIQUE INDEX lexjus_progresso_usuario_lei_artigo_unique ON appestudo.lexjus_progresso USING btree (usuario_id, lei_id, artigo_numero);
CREATE UNIQUE INDEX unique_usuario_lei_artigo_progresso ON appestudo.lexjus_progresso USING btree (usuario_id, lei_id, artigo_numero);
CREATE INDEX idx_lexjus_progresso_lei_artigo ON appestudo.lexjus_progresso USING btree (lei_id, artigo_numero);
CREATE INDEX idx_lexjus_progresso_usuario_lei ON appestudo.lexjus_progresso USING btree (usuario_id, lei_id);

-- Tabela: lexjus_revisoes
CREATE TABLE appestudo.lexjus_revisoes (
    id integer NOT NULL DEFAULT nextval('appestudo.lexjus_revisoes_id_seq'::regclass),
    usuario_id integer NOT NULL,
    artigo_numero character varying(100) NOT NULL,
    facilidade numeric DEFAULT 2.50,
    intervalo_dias integer DEFAULT 1,
    repeticoes integer,
    data_primeira_revisao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_ultima_revisao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_proxima_revisao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    status character varying(20) DEFAULT 'novo'::character varying,
    ultima_qualidade integer,
    total_revisoes integer,
    acertos_consecutivos integer,
    tempo_medio_resposta integer,
    data_criacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    lei_id integer
);

ALTER TABLE appestudo.lexjus_revisoes ADD PRIMARY KEY (id);

CREATE UNIQUE INDEX unique_usuario_lei_artigo ON appestudo.lexjus_revisoes USING btree (usuario_id, lei_id, artigo_numero);
CREATE UNIQUE INDEX unique_usuario_lei_artigo_revisao ON appestudo.lexjus_revisoes USING btree (usuario_id, lei_id, artigo_numero);
CREATE INDEX idx_lexjus_revisoes_lei_artigo ON appestudo.lexjus_revisoes USING btree (lei_id, artigo_numero);
CREATE INDEX idx_lexjus_revisoes_proxima_revisao ON appestudo.lexjus_revisoes USING btree (data_proxima_revisao);
CREATE INDEX idx_lexjus_revisoes_status ON appestudo.lexjus_revisoes USING btree (status);
CREATE INDEX idx_lexjus_revisoes_usuario_id ON appestudo.lexjus_revisoes USING btree (usuario_id);
CREATE INDEX idx_lexjus_revisoes_usuario_lei ON appestudo.lexjus_revisoes USING btree (usuario_id, lei_id);
CREATE INDEX idx_lexjus_revisoes_usuario_proxima ON appestudo.lexjus_revisoes USING btree (usuario_id, data_proxima_revisao);
CREATE INDEX idx_lexjus_revisoes_usuario_status ON appestudo.lexjus_revisoes USING btree (usuario_id, status);

-- Tabela: log_seguranca
CREATE TABLE appestudo.log_seguranca (
    id integer NOT NULL DEFAULT nextval('appestudo.log_seguranca_id_seq'::regclass),
    usuario_id integer,
    tipo_evento character varying(20) NOT NULL,
    ip character varying(45) NOT NULL,
    data_evento timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    detalhes jsonb
);

ALTER TABLE appestudo.log_seguranca ADD PRIMARY KEY (id);


-- Tabela: marcacoes
CREATE TABLE appestudo.marcacoes (
    id integer NOT NULL DEFAULT nextval('appestudo.marcacoes_id_seq'::regclass),
    usuario_id integer NOT NULL,
    pagina_url text NOT NULL,
    elemento_id text NOT NULL,
    tipo_marcacao text NOT NULL,
    texto_marcado text,
    data_criacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    posicao_inicio integer,
    posicao_fim integer
);

ALTER TABLE appestudo.marcacoes ADD PRIMARY KEY (id);

CREATE UNIQUE INDEX unique_marcacao_posicao ON appestudo.marcacoes USING btree (usuario_id, pagina_url, elemento_id, posicao_inicio, posicao_fim);
CREATE INDEX idx_marcacoes_composto ON appestudo.marcacoes USING btree (usuario_id, pagina_url, elemento_id);
CREATE INDEX idx_marcacoes_elemento ON appestudo.marcacoes USING btree (elemento_id);
CREATE INDEX idx_marcacoes_pagina ON appestudo.marcacoes USING btree (pagina_url);
CREATE INDEX idx_marcacoes_usuario ON appestudo.marcacoes USING btree (usuario_id);

-- Tabela: materia
CREATE TABLE appestudo.materia (
    idmateria integer NOT NULL DEFAULT nextval('appestudo.materia_idmateria_seq'::regclass),
    nome character varying(100),
    cor character varying(10)
);

ALTER TABLE appestudo.materia ADD PRIMARY KEY (idmateria);


-- Tabela: materias_estudadas
CREATE TABLE appestudo.materias_estudadas (
    id integer NOT NULL DEFAULT nextval('appestudo.materias_estudadas_id_seq'::regclass),
    nome character varying(100) NOT NULL,
    cor character varying(20) NOT NULL,
    detalhe text
);

ALTER TABLE appestudo.materias_estudadas ADD PRIMARY KEY (id);


-- Tabela: medicamento
CREATE TABLE appestudo.medicamento (
    id_medicamento integer NOT NULL DEFAULT nextval('appestudo.medicamento_id_medicamento_seq'::regclass),
    nome character varying(450),
    detalhes character varying(1000),
    data_inicio timestamp without time zone,
    dosagem character varying(256),
    usuario_idusuario integer NOT NULL,
    foto bytea
);

ALTER TABLE appestudo.medicamento ADD PRIMARY KEY (id_medicamento);

CREATE INDEX idx_usuario_idusuario ON appestudo.medicamento USING btree (usuario_idusuario);

-- Tabela: medicamentos
CREATE TABLE appestudo.medicamentos (
    id integer NOT NULL DEFAULT nextval('appestudo.medicamentos_id_seq'::regclass),
    nome character varying(100) NOT NULL,
    dosagem character varying(50) NOT NULL,
    intervalo_horas integer NOT NULL,
    data_inicio date NOT NULL,
    dias_tratamento integer NOT NULL,
    horario_inicial time without time zone NOT NULL,
    observacoes text,
    status boolean DEFAULT true,
    data_criacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE appestudo.medicamentos ADD PRIMARY KEY (id);

CREATE INDEX idx_medicamento_status ON appestudo.medicamentos USING btree (status);

-- Tabela: metodo_estudo
CREATE TABLE appestudo.metodo_estudo (
    idmetodo integer NOT NULL DEFAULT nextval('appestudo.metodo_estudo_idmetodo_seq'::regclass),
    nome character varying(100) NOT NULL,
    descricao text,
    ativo boolean DEFAULT true,
    data_criacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_atualizacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE appestudo.metodo_estudo ADD PRIMARY KEY (idmetodo);


-- Tabela: notificacoes
CREATE TABLE appestudo.notificacoes (
    id integer NOT NULL DEFAULT nextval('appestudo.notificacoes_id_seq'::regclass),
    titulo character varying(255) NOT NULL,
    mensagem text NOT NULL,
    tipo character varying(50) NOT NULL,
    data_criacao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    data_expiracao timestamp without time zone NOT NULL,
    status boolean DEFAULT true,
    is_global boolean DEFAULT false,
    criado_por integer
);

ALTER TABLE appestudo.notificacoes ADD PRIMARY KEY (id);

CREATE INDEX idx_notificacoes_data_criacao ON appestudo.notificacoes USING btree (data_criacao);
CREATE INDEX idx_notificacoes_data_exp ON appestudo.notificacoes USING btree (data_expiracao);
CREATE INDEX idx_notificacoes_global ON appestudo.notificacoes USING btree (is_global);
CREATE INDEX idx_notificacoes_status ON appestudo.notificacoes USING btree (status);

-- Tabela: notificacoes_usuarios
CREATE TABLE appestudo.notificacoes_usuarios (
    id integer NOT NULL DEFAULT nextval('appestudo.notificacoes_usuarios_id_seq'::regclass),
    notificacao_id integer,
    usuario_id integer,
    lida boolean DEFAULT false,
    data_leitura timestamp without time zone
);

ALTER TABLE appestudo.notificacoes_usuarios ADD PRIMARY KEY (id);

CREATE UNIQUE INDEX notificacoes_usuarios_notificacao_id_usuario_id_key ON appestudo.notificacoes_usuarios USING btree (notificacao_id, usuario_id);
CREATE INDEX idx_notificacoes_usuarios ON appestudo.notificacoes_usuarios USING btree (usuario_id);

-- Tabela: pesos_materias
CREATE TABLE appestudo.pesos_materias (
    id integer NOT NULL DEFAULT nextval('appestudo.pesos_materias_id_seq'::regclass),
    prova_id integer,
    materia_id integer,
    peso integer DEFAULT 1,
    nivel_dificuldade integer DEFAULT 3,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE appestudo.pesos_materias ADD PRIMARY KEY (id);

CREATE UNIQUE INDEX unique_prova_materia ON appestudo.pesos_materias USING btree (prova_id, materia_id);

-- Tabela: planejamento
CREATE TABLE appestudo.planejamento (
    idplanejamento integer NOT NULL DEFAULT nextval('appestudo.planejamento_idplanejamento_seq'::regclass),
    usuario_idusuario integer NOT NULL,
    nome character varying(45) NOT NULL,
    data_inicio date NOT NULL,
    data_fim date,
    tempo_planejamento time without time zone
);

ALTER TABLE appestudo.planejamento ADD PRIMARY KEY (idplanejamento);


-- Tabela: planejamento_materia
CREATE TABLE appestudo.planejamento_materia (
    idplanejamento_materia integer NOT NULL DEFAULT nextval('appestudo.planejamento_materia_idplanejamento_materia_seq'::regclass),
    planejamento_idplanejamento integer NOT NULL,
    materia_idmateria integer NOT NULL,
    ordem integer,
    ativo boolean DEFAULT true
);

ALTER TABLE appestudo.planejamento_materia ADD PRIMARY KEY (idplanejamento_materia);


-- Tabela: planejamento_materia_backup
CREATE TABLE appestudo.planejamento_materia_backup (
    idplanejamento_materia integer,
    planejamento_idplanejamento integer,
    materia_idmateria integer
);


-- Tabela: planos
CREATE TABLE appestudo.planos (
    id integer NOT NULL DEFAULT nextval('appestudo.planos_id_seq'::regclass),
    nome character varying(100) NOT NULL,
    descricao text,
    valor_mensal numeric,
    valor_anual numeric,
    modulo character varying(50) NOT NULL,
    ativo boolean DEFAULT true,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE appestudo.planos ADD PRIMARY KEY (id);


-- Tabela: provas
CREATE TABLE appestudo.provas (
    id integer NOT NULL DEFAULT nextval('appestudo.provas_id_seq'::regclass),
    usuario_id integer,
    nome character varying(255),
    data_prova date,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    status boolean DEFAULT true,
    data_inicio_estudo date,
    replanejado boolean DEFAULT false,
    data_replanejamento timestamp without time zone
);

ALTER TABLE appestudo.provas ADD PRIMARY KEY (id);

CREATE INDEX idx_provas_status ON appestudo.provas USING btree (usuario_id, status);
CREATE INDEX idx_provas_status_data ON appestudo.provas USING btree (usuario_id, status, data_prova);

-- Tabela: registros_uso
CREATE TABLE appestudo.registros_uso (
    id integer NOT NULL DEFAULT nextval('appestudo.registros_uso_id_seq'::regclass),
    medicamento_id integer,
    data_hora timestamp without time zone NOT NULL,
    confirmado boolean DEFAULT false,
    observacao text,
    data_confirmacao timestamp without time zone
);

ALTER TABLE appestudo.registros_uso ADD PRIMARY KEY (id);

CREATE INDEX idx_registros_confirmado ON appestudo.registros_uso USING btree (confirmado);
CREATE INDEX idx_registros_data_hora ON appestudo.registros_uso USING btree (data_hora);
CREATE INDEX idx_registros_medicamento ON appestudo.registros_uso USING btree (medicamento_id);

-- Tabela: revisoes
CREATE TABLE appestudo.revisoes (
    id integer NOT NULL DEFAULT nextval('appestudo.revisoes_id_seq'::regclass),
    conteudo_id integer,
    usuario_id integer,
    nivel_revisao integer DEFAULT 1,
    ultima_revisao timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    proxima_revisao timestamp without time zone,
    status_revisao character varying(20) DEFAULT 'pendente'::character varying,
    confianca integer,
    created_at timestamp without time zone DEFAULT CURRENT_TIMESTAMP,
    edital_id integer NOT NULL
);

ALTER TABLE appestudo.revisoes ADD PRIMARY KEY (id);

CREATE INDEX idx_revisoes_proxima ON appestudo.revisoes USING btree (proxima_revisao);
CREATE INDEX idx_revisoes_status ON appestudo.revisoes USING btree (status_revisao);
CREATE INDEX idx_revisoes_usuario ON appestudo.revisoes USING btree (usuario_id);

-- Tabela: usuario
CREATE TABLE appestudo.usuario (
    idusuario integer NOT NULL DEFAULT nextval('appestudo.usuario_idusuario_seq'::regclass),
    nome character varying(45) NOT NULL,
    senha character varying(45) NOT NULL,
    usuario character varying(255),
    email character varying(255) NOT NULL,
    is_admin boolean DEFAULT false,
    cronograma_inteligente boolean DEFAULT false,
    status character varying(20) DEFAULT 'ativo'::character varying,
    tentativas_falhas integer
);

ALTER TABLE appestudo.usuario ADD PRIMARY KEY (idusuario);

CREATE UNIQUE INDEX usuario_email_key ON appestudo.usuario USING btree (email);
CREATE UNIQUE INDEX usuario_usuario_key ON appestudo.usuario USING btree (usuario);

-- Tabela: usuario_conteudo
CREATE TABLE appestudo.usuario_conteudo (
    id integer NOT NULL DEFAULT nextval('appestudo.usuario_conteudo_id_seq'::regclass),
    usuario_id integer NOT NULL,
    conteudo_id integer NOT NULL,
    status boolean DEFAULT false,
    data_revisao date,
    status_revisao character varying(50) DEFAULT 'Não Iniciado'::character varying,
    status_estudo character varying(15) DEFAULT 'Não Estudado'::character varying,
    replanejado_em timestamp without time zone
);

ALTER TABLE appestudo.usuario_conteudo ADD PRIMARY KEY (id);

CREATE UNIQUE INDEX usuario_conteudo_unico ON appestudo.usuario_conteudo USING btree (usuario_id, conteudo_id);
CREATE INDEX idx_usuario_conteudo_composto ON appestudo.usuario_conteudo USING btree (usuario_id, status, conteudo_id);
CREATE INDEX idx_usuario_conteudo_lookup ON appestudo.usuario_conteudo USING btree (usuario_id, status, conteudo_id);
CREATE INDEX idx_usuario_conteudo_status ON appestudo.usuario_conteudo USING btree (usuario_id, status);

-- Tabela: usuario_dias_estudo
CREATE TABLE appestudo.usuario_dias_estudo (
    id integer NOT NULL DEFAULT nextval('appestudo.usuario_dias_estudo_id_seq'::regclass),
    usuario_id integer,
    dia_semana integer,
    horas_estudo numeric,
    ativo boolean DEFAULT true
);

ALTER TABLE appestudo.usuario_dias_estudo ADD PRIMARY KEY (id);


-- Tabela: usuario_edital
CREATE TABLE appestudo.usuario_edital (
    id integer NOT NULL DEFAULT nextval('appestudo.usuario_edital_id_seq'::regclass),
    usuario_id integer NOT NULL,
    edital_id integer NOT NULL,
    data_inscricao timestamp without time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE appestudo.usuario_edital ADD PRIMARY KEY (id);


-- Tabela: usuario_has_curso
CREATE TABLE appestudo.usuario_has_curso (
    usuario_idusuario integer NOT NULL,
    curso_idcurso integer NOT NULL
);

ALTER TABLE appestudo.usuario_has_curso ADD PRIMARY KEY (usuario_idusuario, curso_idcurso);


-- Tabela: vw_registros_completos
CREATE TABLE appestudo.vw_registros_completos (
    id integer,
    medicamento_id integer,
    nome character varying(100),
    dosagem character varying(50),
    data_hora timestamp without time zone,
    confirmado boolean,
    observacao text
);


-- =============================================
-- FOREIGN KEYS
-- =============================================

ALTER TABLE appestudo.agenda ADD CONSTRAINT fk_agenda_usuario1 FOREIGN KEY (usuario_idusuario) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.ajustes_plano ADD CONSTRAINT ajustes_plano_prova_id_fkey FOREIGN KEY (prova_id) REFERENCES appestudo.provas(id);
ALTER TABLE appestudo.assinaturas ADD CONSTRAINT assinaturas_plano_id_fkey FOREIGN KEY (plano_id) REFERENCES appestudo.planos(id);
ALTER TABLE appestudo.assinaturas ADD CONSTRAINT assinaturas_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.conteudo_edital ADD CONSTRAINT conteudo_edital_edital_id_fkey FOREIGN KEY (edital_id) REFERENCES appestudo.edital(id_edital);
ALTER TABLE appestudo.conteudo_edital ADD CONSTRAINT conteudo_edital_materia_id_fkey FOREIGN KEY (materia_id) REFERENCES appestudo.materia(idmateria);
ALTER TABLE appestudo.flashcard_categories ADD CONSTRAINT flashcard_categories_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.flashcard_deck_association ADD CONSTRAINT flashcard_deck_association_deck_id_fkey FOREIGN KEY (deck_id) REFERENCES appestudo.flashcard_decks(id);
ALTER TABLE appestudo.flashcard_deck_association ADD CONSTRAINT flashcard_deck_association_flashcard_id_fkey FOREIGN KEY (flashcard_id) REFERENCES appestudo.flashcards(id);
ALTER TABLE appestudo.flashcard_decks ADD CONSTRAINT flashcard_decks_category_id_fkey FOREIGN KEY (category_id) REFERENCES appestudo.flashcard_categories(id);
ALTER TABLE appestudo.flashcard_decks ADD CONSTRAINT flashcard_decks_materia_id_fkey FOREIGN KEY (materia_id) REFERENCES appestudo.materia(idmateria);
ALTER TABLE appestudo.flashcard_decks ADD CONSTRAINT flashcard_decks_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.flashcard_materias ADD CONSTRAINT flashcard_materias_flashcard_id_fkey FOREIGN KEY (flashcard_id) REFERENCES appestudo.flashcards(id);
ALTER TABLE appestudo.flashcard_materias ADD CONSTRAINT flashcard_materias_materia_id_fkey FOREIGN KEY (materia_id) REFERENCES appestudo.materia(idmateria);
ALTER TABLE appestudo.flashcard_mindmaps ADD CONSTRAINT flashcard_mindmaps_flashcard_id_fkey FOREIGN KEY (flashcard_id) REFERENCES appestudo.flashcards(id);
ALTER TABLE appestudo.flashcard_progress ADD CONSTRAINT flashcard_progress_flashcard_id_fkey FOREIGN KEY (flashcard_id) REFERENCES appestudo.flashcards(id);
ALTER TABLE appestudo.flashcard_progress ADD CONSTRAINT flashcard_progress_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.flashcard_review_history ADD CONSTRAINT flashcard_review_history_progress_id_fkey FOREIGN KEY (progress_id) REFERENCES appestudo.flashcard_progress(id);
ALTER TABLE appestudo.flashcard_topic_association ADD CONSTRAINT flashcard_topic_association_flashcard_id_fkey FOREIGN KEY (flashcard_id) REFERENCES appestudo.flashcards(id);
ALTER TABLE appestudo.flashcard_topic_association ADD CONSTRAINT flashcard_topic_association_topic_id_fkey FOREIGN KEY (topic_id) REFERENCES appestudo.flashcard_topics(id);
ALTER TABLE appestudo.flashcard_topics ADD CONSTRAINT flashcard_topics_deck_id_fkey FOREIGN KEY (deck_id) REFERENCES appestudo.flashcard_decks(id);
ALTER TABLE appestudo.forum_notificacoes ADD CONSTRAINT forum_notificacoes_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.forum_respostas ADD CONSTRAINT forum_respostas_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES appestudo.forum_respostas(id);
ALTER TABLE appestudo.forum_respostas ADD CONSTRAINT forum_respostas_topico_id_fkey FOREIGN KEY (topico_id) REFERENCES appestudo.forum_topicos(id);
ALTER TABLE appestudo.forum_respostas ADD CONSTRAINT forum_respostas_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.forum_topicos ADD CONSTRAINT forum_topicos_categoria_id_fkey FOREIGN KEY (categoria_id) REFERENCES appestudo.forum_categorias(id);
ALTER TABLE appestudo.forum_topicos ADD CONSTRAINT forum_topicos_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.forum_votos ADD CONSTRAINT forum_votos_resposta_id_fkey FOREIGN KEY (resposta_id) REFERENCES appestudo.forum_respostas(id);
ALTER TABLE appestudo.forum_votos ADD CONSTRAINT forum_votos_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.kanban_etiquetas ADD CONSTRAINT kanban_etiquetas_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.kanban_tarefa_etiqueta ADD CONSTRAINT kanban_tarefa_etiqueta_etiqueta_id_fkey FOREIGN KEY (etiqueta_id) REFERENCES appestudo.kanban_etiquetas(id);
ALTER TABLE appestudo.kanban_tarefa_etiqueta ADD CONSTRAINT kanban_tarefa_etiqueta_tarefa_id_fkey FOREIGN KEY (tarefa_id) REFERENCES appestudo.kanban_tarefas(id);
ALTER TABLE appestudo.kanban_tarefas ADD CONSTRAINT kanban_tarefas_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.lexjus_anotacoes ADD CONSTRAINT lexjus_anotacoes_lei_id_fkey FOREIGN KEY (lei_id) REFERENCES appestudo.lexjus_leis(id);
ALTER TABLE appestudo.lexjus_anotacoes ADD CONSTRAINT lexjus_anotacoes_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.lexjus_config_revisao ADD CONSTRAINT fk_config_usuario FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.lexjus_favoritos ADD CONSTRAINT lexjus_favoritos_lei_id_fkey FOREIGN KEY (lei_id) REFERENCES appestudo.lexjus_leis(id);
ALTER TABLE appestudo.lexjus_favoritos ADD CONSTRAINT lexjus_favoritos_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.lexjus_historico_revisoes ADD CONSTRAINT fk_historico_revisao FOREIGN KEY (revisao_id) REFERENCES appestudo.lexjus_revisoes(id);
ALTER TABLE appestudo.lexjus_historico_revisoes ADD CONSTRAINT fk_historico_usuario FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.lexjus_historico_revisoes ADD CONSTRAINT lexjus_historico_revisoes_lei_id_fkey FOREIGN KEY (lei_id) REFERENCES appestudo.lexjus_leis(id);
ALTER TABLE appestudo.lexjus_lista_artigos ADD CONSTRAINT lexjus_lista_artigos_lista_id_fkey FOREIGN KEY (lista_id) REFERENCES appestudo.lexjus_listas(id);
ALTER TABLE appestudo.lexjus_listas ADD CONSTRAINT lexjus_listas_lei_id_fkey FOREIGN KEY (lei_id) REFERENCES appestudo.lexjus_leis(id);
ALTER TABLE appestudo.lexjus_listas ADD CONSTRAINT lexjus_listas_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.lexjus_progresso ADD CONSTRAINT lexjus_progresso_lei_id_fkey FOREIGN KEY (lei_id) REFERENCES appestudo.lexjus_leis(id);
ALTER TABLE appestudo.lexjus_progresso ADD CONSTRAINT lexjus_progresso_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.lexjus_revisoes ADD CONSTRAINT fk_revisoes_usuario FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.lexjus_revisoes ADD CONSTRAINT lexjus_revisoes_lei_id_fkey FOREIGN KEY (lei_id) REFERENCES appestudo.lexjus_leis(id);
ALTER TABLE appestudo.log_seguranca ADD CONSTRAINT log_seguranca_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.medicamento ADD CONSTRAINT fk_medicamento_usuario1 FOREIGN KEY (usuario_idusuario) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.notificacoes ADD CONSTRAINT notificacoes_criado_por_fkey FOREIGN KEY (criado_por) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.notificacoes_usuarios ADD CONSTRAINT notificacoes_usuarios_notificacao_id_fkey FOREIGN KEY (notificacao_id) REFERENCES appestudo.notificacoes(id);
ALTER TABLE appestudo.notificacoes_usuarios ADD CONSTRAINT notificacoes_usuarios_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.pesos_materias ADD CONSTRAINT pesos_materias_materia_id_fkey FOREIGN KEY (materia_id) REFERENCES appestudo.materia(idmateria);
ALTER TABLE appestudo.pesos_materias ADD CONSTRAINT pesos_materias_prova_id_fkey FOREIGN KEY (prova_id) REFERENCES appestudo.provas(id);
ALTER TABLE appestudo.planejamento ADD CONSTRAINT fk_planejamento_usuario1 FOREIGN KEY (usuario_idusuario) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.planejamento_materia ADD CONSTRAINT fk_planejamento_materia_materia FOREIGN KEY (materia_idmateria) REFERENCES appestudo.materia(idmateria);
ALTER TABLE appestudo.planejamento_materia ADD CONSTRAINT fk_planejamento_materia_planejamento FOREIGN KEY (planejamento_idplanejamento) REFERENCES appestudo.planejamento(idplanejamento);
ALTER TABLE appestudo.registros_uso ADD CONSTRAINT registros_uso_medicamento_id_fkey FOREIGN KEY (medicamento_id) REFERENCES appestudo.medicamentos(id);
ALTER TABLE appestudo.revisoes ADD CONSTRAINT revisoes_conteudo_id_fkey FOREIGN KEY (conteudo_id) REFERENCES appestudo.conteudo_edital(id_conteudo);
ALTER TABLE appestudo.revisoes ADD CONSTRAINT revisoes_edital_id_fkey FOREIGN KEY (edital_id) REFERENCES appestudo.edital(id_edital);
ALTER TABLE appestudo.usuario_conteudo ADD CONSTRAINT usuario_conteudo_conteudo_id_fkey FOREIGN KEY (conteudo_id) REFERENCES appestudo.conteudo_edital(id_conteudo);
ALTER TABLE appestudo.usuario_conteudo ADD CONSTRAINT usuario_conteudo_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.usuario_dias_estudo ADD CONSTRAINT usuario_dias_estudo_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.usuario_edital ADD CONSTRAINT usuario_edital_edital_id_fkey FOREIGN KEY (edital_id) REFERENCES appestudo.edital(id_edital);
ALTER TABLE appestudo.usuario_edital ADD CONSTRAINT usuario_edital_usuario_id_fkey FOREIGN KEY (usuario_id) REFERENCES appestudo.usuario(idusuario);
ALTER TABLE appestudo.usuario_has_curso ADD CONSTRAINT usuario_has_curso_curso_idcurso_fkey FOREIGN KEY (curso_idcurso) REFERENCES appestudo.curso(idcurso);
ALTER TABLE appestudo.usuario_has_curso ADD CONSTRAINT usuario_has_curso_usuario_idusuario_fkey FOREIGN KEY (usuario_idusuario) REFERENCES appestudo.usuario(idusuario);
