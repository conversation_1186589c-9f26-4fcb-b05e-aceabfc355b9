<?php
//plano_estudo_logic.php

/**
 * Filtra todos os conteúdos para remover aqueles que possuem filhos
 * Deve ser chamada antes de calcular estatísticas
 */
function filtrarTodosConteudos($conteudos) {
    // Primeiro, construímos um mapa de quais capítulos têm filhos
    $temFilhos = [];
    
    foreach ($conteudos as $conteudo) {
        $capitulo = $conteudo['capitulo'];
        $partes = explode('.', $capitulo);
        
        // Se for um capítulo com mais de um nível
        if (count($partes) > 1) {
            // Remove o último nível para encontrar o pai
            array_pop($partes);
            $pai = implode('.', $partes);
            
            // Marca o pai como tendo filhos
            $temFilhos[$pai] = true;
        }
    }
    
    // Agora filtramos os conteúdos, removendo aqueles que têm filhos
    $filtrados = [];
    foreach ($conteudos as $conteudo) {
        if (!isset($temFilhos[$conteudo['capitulo']])) {
            $filtrados[] = $conteudo;
        }
    }
    
    return $filtrados;
}

function debugOutput($label, $data) {
    echo "<div style='background:#f8f9fa;padding:10px;margin:10px;border:1px solid #ddd;'>";
    echo "<strong>$label:</strong><br>";
    echo "<pre>";
    print_r($data);
    echo "</pre>";
    echo "</div>";
}
class PlanoEstudoLogic {
    private $conexao;
    private $usuario_id;
    private $prova;
    private $conteudos;
    private $info_estudo;

    private $dias_estudo = [];

    public function __construct($conexao) {
        $this->conexao = $conexao;
        $this->usuario_id = $this->validarUsuario();
        $this->carregarDiasEstudo();
        $this->prova = $this->getProvaAtiva();
        $this->info_estudo = $this->getInfoEstudo();
    }

    private function carregarDiasEstudo() {
        $query = "SELECT dia_semana, horas_estudo 
                 FROM appestudo.usuario_dias_estudo 
                 WHERE usuario_id = $1 
                 AND ativo = true 
                 ORDER BY dia_semana";
        $result = pg_query_params($this->conexao, $query, array($this->usuario_id));
        
        $this->dias_estudo = [];
        while ($row = pg_fetch_assoc($result)) {
            $this->dias_estudo[intval($row['dia_semana'])] = floatval($row['horas_estudo']);
        }
        
        if (empty($this->dias_estudo)) {
            for ($i = 1; $i <= 5; $i++) {
                $this->dias_estudo[$i] = 3.0;
            }
        }
    }

    
    public function getDiasEstudo() {
        return array_keys($this->dias_estudo); // Retorna array como [1, 2, 3, ...]
    }

    private function validarUsuario() {
        $usuario_id = filter_var($_SESSION['idusuario'], FILTER_VALIDATE_INT);
        if (!$usuario_id || $usuario_id <= 0) {
            header('Location: /login_index.php');
            exit();
        }
        return $usuario_id;
    }

    public function getProvaAtiva() {
        // Verificar se estamos em um contexto de API
        $is_api_context = defined('IS_API_CONTEXT') && IS_API_CONTEXT;
    
        $query = "SELECT p.*, ap.horas_estudo_dia, 
                 COALESCE(p.replanejado, false) as replanejado 
                 FROM appestudo.provas p
                 LEFT JOIN appestudo.ajustes_plano ap ON p.id = ap.prova_id
                 WHERE p.usuario_id = $1 
                 AND p.status = true 
                 ORDER BY p.created_at DESC 
                 LIMIT 1";
        
        // Log a consulta SQL para debug (apenas se não estivermos em um contexto de API)
        if (!$is_api_context) {
            error_log("Query getProvaAtiva: " . $query);
        }
        
        $result = pg_query_params($this->conexao, $query, array($this->usuario_id));
        
        // Verifica erros na consulta
        if (!$result) {
            error_log("Erro na consulta getProvaAtiva: " . pg_last_error($this->conexao));
        }
        
        $prova = pg_fetch_assoc($result);
    
        // Exibe o resultado bruto para debug
        if (!$is_api_context) {
            error_log("Resultado bruto da prova: " . print_r($prova, true));
        }
    
        if (!$prova) {
            if (!$is_api_context) {
                header('Location: configurar_prova.php');
                exit();
            } else {
                return null;
            }
        }
    
        // Valor original para debug
        $original_value = $prova['replanejado'];
        
        // Converte explicitamente o valor para booleano, considerando todos os formatos possíveis
        $prova['replanejado'] = (
            $prova['replanejado'] === 't' || 
            $prova['replanejado'] === true || 
            $prova['replanejado'] === 1 || 
            $prova['replanejado'] === '1' || 
            $prova['replanejado'] === 'true'
        );
        
        // Debug para verificar o valor da flag (apenas se não estivermos em um contexto de API)
        if (!$is_api_context) {
            error_log("Valor original da flag replanejado: " . var_export($original_value, true));
            error_log("Tipo original: " . gettype($original_value));
            error_log("Valor normalizado da flag replanejado: " . var_export($prova['replanejado'], true));
            error_log("Tipo após normalização: " . gettype($prova['replanejado']));
            
            // Adiciona um console.log para debug no navegador
            echo "<script>
                //console.log('DADOS DA PROVA (getProvaAtiva):');
                //console.log('Flag replanejado - valor original: ', " . json_encode($original_value) . ");
                //console.log('Flag replanejado - valor normalizado: ', " . json_encode($prova['replanejado']) . ");
            </script>";
        }
        
        return $prova;
    }

    public function getConteudos() {
        $query = "WITH niveis_do_edital AS (
        SELECT 
            edital_id,
            materia_id,
            MIN(capitulo) as primeiro_capitulo
        FROM 
            appestudo.conteudo_edital
        GROUP BY 
            edital_id, materia_id
    ),
    pesos_calculados AS (
        SELECT 
            m.idmateria,
            COALESCE(pm.peso, 1) * COALESCE(pm.nivel_dificuldade, 1) * 
            CASE 
                WHEN (SELECT data_prova::date - CURRENT_DATE <= 7 
                      FROM appestudo.provas 
                      WHERE usuario_id = $1 AND status = true) 
                THEN 2 
                ELSE 1 
            END as prioridade_calculada
        FROM appestudo.materia m
        LEFT JOIN appestudo.pesos_materias pm ON m.idmateria = pm.materia_id
        LEFT JOIN appestudo.provas p ON pm.prova_id = p.id AND p.status = true
        WHERE p.usuario_id = $1
    ),
    conteudo_base AS (
        SELECT 
            uc.id, 
            m.idmateria,
            m.nome AS materia_nome, 
            m.cor, 
            ce.descricao, 
            ce.capitulo,
            pc.prioridade_calculada,
            uc.replanejado_em,
            
            -- Nível 1 (Principal)
            CASE 
                WHEN strpos(ce.capitulo, '.') > 0 THEN 
                    (SELECT tn.capitulo || '. ' || tn.descricao
                     FROM appestudo.conteudo_edital tn
                     WHERE tn.materia_id = ce.materia_id
                     AND tn.capitulo = split_part(ce.capitulo, '.', 1)
                     LIMIT 1)
                ELSE NULL
            END as descricao_capitulo_principal,
            
            -- Nível 2
            CASE 
                WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 2 THEN 
                    (SELECT cp.capitulo || '. ' || cp.descricao
                     FROM appestudo.conteudo_edital cp
                     WHERE cp.materia_id = ce.materia_id
                     AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || split_part(ce.capitulo, '.', 2)
                     LIMIT 1)
                ELSE NULL
            END as descricao_capitulo_secundario,
            
            -- Nível 3
            CASE 
                WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 3 THEN 
                    (SELECT cp.capitulo || '. ' || cp.descricao
                     FROM appestudo.conteudo_edital cp
                     WHERE cp.materia_id = ce.materia_id
                     AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || 
                         split_part(ce.capitulo, '.', 2) || '.' ||
                         split_part(ce.capitulo, '.', 3)
                     LIMIT 1)
                ELSE NULL
            END as descricao_capitulo_terciario,
            
            -- Nível 4
            CASE 
                WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 4 THEN 
                    (SELECT cp.capitulo || '. ' || cp.descricao
                     FROM appestudo.conteudo_edital cp
                     WHERE cp.materia_id = ce.materia_id
                     AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || 
                         split_part(ce.capitulo, '.', 2) || '.' ||
                         split_part(ce.capitulo, '.', 3) || '.' ||
                         split_part(ce.capitulo, '.', 4)
                     LIMIT 1)
                ELSE NULL
            END as descricao_capitulo_quaternario,
    
            -- Nível 5
            CASE 
                WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 5 THEN 
                    (SELECT cp.capitulo || '. ' || cp.descricao
                     FROM appestudo.conteudo_edital cp
                     WHERE cp.materia_id = ce.materia_id
                     AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || 
                         split_part(ce.capitulo, '.', 2) || '.' ||
                         split_part(ce.capitulo, '.', 3) || '.' ||
                         split_part(ce.capitulo, '.', 4) || '.' ||
                         split_part(ce.capitulo, '.', 5)
                     LIMIT 1)
                ELSE NULL
            END as descricao_capitulo_quinario,
            
            -- Nível 6
            CASE 
                WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 6 THEN 
                    (SELECT cp.capitulo || '. ' || cp.descricao
                     FROM appestudo.conteudo_edital cp
                     WHERE cp.materia_id = ce.materia_id
                     AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || 
                         split_part(ce.capitulo, '.', 2) || '.' ||
                         split_part(ce.capitulo, '.', 3) || '.' ||
                         split_part(ce.capitulo, '.', 4) || '.' ||
                         split_part(ce.capitulo, '.', 5) || '.' ||
                         split_part(ce.capitulo, '.', 6)
                     LIMIT 1)
                ELSE NULL
            END as descricao_capitulo_sexto,
            
            -- Nível 7
            CASE 
                WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 7 THEN 
                    (SELECT cp.capitulo || '. ' || cp.descricao
                     FROM appestudo.conteudo_edital cp
                     WHERE cp.materia_id = ce.materia_id
                     AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || 
                         split_part(ce.capitulo, '.', 2) || '.' ||
                         split_part(ce.capitulo, '.', 3) || '.' ||
                         split_part(ce.capitulo, '.', 4) || '.' ||
                         split_part(ce.capitulo, '.', 5) || '.' ||
                         split_part(ce.capitulo, '.', 6) || '.' ||
                         split_part(ce.capitulo, '.', 7)
                     LIMIT 1)
                ELSE NULL
            END as descricao_capitulo_setimo,
            
            uc.status_estudo,
            CAST(split_part(ce.capitulo, '.', 1) AS INTEGER) AS capitulo_principal,
            COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 2), '') AS INTEGER), 0) AS subnivel1,
            COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 3), '') AS INTEGER), 0) AS subnivel2,
            COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 4), '') AS INTEGER), 0) AS subnivel3,
            COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 5), '') AS INTEGER), 0) AS subnivel4,
            COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 6), '') AS INTEGER), 0) AS subnivel5,
            COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 7), '') AS INTEGER), 0) AS subnivel6,
            ce.ordem,
            ROW_NUMBER() OVER (
                PARTITION BY ce.materia_id 
                ORDER BY 
                    CAST(split_part(ce.capitulo, '.', 1) AS INTEGER),
                    COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 2), '') AS INTEGER), 0),
                    COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 3), '') AS INTEGER), 0),
                    COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 4), '') AS INTEGER), 0),
                    COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 5), '') AS INTEGER), 0),
                    COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 6), '') AS INTEGER), 0),
                    COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 7), '') AS INTEGER), 0)
            ) as ordem_na_materia
        FROM 
            appestudo.usuario_conteudo AS uc
        JOIN 
            appestudo.conteudo_edital AS ce ON uc.conteudo_id = ce.id_conteudo
        JOIN 
            appestudo.materia AS m ON ce.materia_id = m.idmateria
        JOIN
            pesos_calculados pc ON m.idmateria = pc.idmateria
         WHERE 
            uc.usuario_id = $1
            AND uc.status = true
           AND (
        ce.capitulo ~ '^\\d+$' OR
        ce.capitulo ~ '^\\d+\\.\\d+$' OR
        ce.capitulo ~ '^\\d+\\.\\d+\\.\\d+$' OR
        ce.capitulo ~ '^\\d+\\.\\d+\\.\\d+\\.\\d+$' OR
        ce.capitulo ~ '^\\d+\\.\\d+\\.\\d+\\.\\d+\\.\\d+$' OR
        ce.capitulo ~ '^\\d+\\.\\d+\\.\\d+\\.\\d+\\.\\d+\\.\\d+$' OR
        ce.capitulo ~ '^\\d+\\.\\d+\\.\\d+\\.\\d+\\.\\d+\\.\\d+\\.\\d+$'
    )
    )
    -- SELECT principal com todos os campos
    SELECT 
        cb.id,
        cb.idmateria,
        cb.materia_nome,
        cb.cor,
        cb.descricao,
        cb.capitulo,
        cb.descricao_capitulo_principal,
        cb.descricao_capitulo_secundario,
        cb.descricao_capitulo_terciario,
        cb.descricao_capitulo_quaternario,
        cb.descricao_capitulo_quinario,
        cb.descricao_capitulo_sexto,
        cb.descricao_capitulo_setimo,
        cb.status_estudo,
        cb.capitulo_principal,
        cb.subnivel1,
        cb.subnivel2,
        cb.subnivel3,
        cb.subnivel4,
        cb.subnivel5,
        cb.subnivel6,
        cb.ordem,
        cb.ordem_na_materia,
        cb.prioridade_calculada * 
        CASE 
            WHEN (SELECT data_prova::date - CURRENT_DATE <= 7 
                  FROM appestudo.provas 
                  WHERE usuario_id = $1 AND status = true)
            THEN 2 
            ELSE 1 
        END as prioridade_final,
        cb.replanejado_em
    FROM conteudo_base cb
    ORDER BY cb.prioridade_calculada DESC, cb.ordem_na_materia;";
        
        $result = pg_query_params($this->conexao, $query, array($this->usuario_id));
        $this->conteudos = pg_fetch_all($result);
        return $this->conteudos;
    }

    public function getInfoEstudo() {
        // Garante que os conteúdos foram carregados
        if ($this->conteudos === null) {
            $this->getConteudos();
        }
        
        // Filtrar conteúdos que têm filhos
        $conteudosFiltrados = filtrarTodosConteudos($this->conteudos);
        $total_conteudos = count($conteudosFiltrados);
        
        $calculo = new Calculos($this->prova, $total_conteudos, $this->conexao);
        
        // Pega os conteúdos organizados por semana (já filtrados)
        $conteudosPorSemana = $this->organizarConteudosPorSemanaFiltrados();
        
        // Inicializa array para contar cards por dia da semana
        $cards_por_dia = array_fill(0, count($this->dias_estudo), 0);
        
        // Soma os cards de cada dia através de todas as semanas
        foreach ($conteudosPorSemana as $semana) {
            foreach ($semana as $diaIndex => $conteudosDia) {
                if (!empty($conteudosDia)) {  // Verifica se há conteúdos para este dia
                    $cards_por_dia[$diaIndex] += count($conteudosDia);
                }
            }
        }
        
        // Remove índices vazios (dias sem conteúdo)
        $cards_por_dia = array_values(array_filter($cards_por_dia));
        
        return [
            'total_conteudos' => $total_conteudos,
            'dias_ate_prova' => $calculo->getDiasParaProva(),
            'dias_uteis' => $calculo->getDiasUteis(),
            'cards_por_dia' => $cards_por_dia,
            'semanas_necessarias' => $calculo->getTotalSemanas()
        ];
    }

    public function gerarSemanasDatas() {
        $semanas_datas = [];
        $data_inicio = new DateTime($this->prova['data_inicio_estudo']); // 10/02/2025
        $data_prova = new DateTime($this->prova['data_prova']); // 02/03/2025
        
        // Encontra a segunda-feira da primeira semana
        $primeira_segunda = clone $data_inicio;
        while ($primeira_segunda->format('N') > 1) {
            $primeira_segunda->modify('-1 day');
        }
        
        // Se a data de início não for segunda-feira, ajusta para começar da data correta
        $inicio_real = clone $data_inicio;
        
        // Inicializa a data atual para iteração
        $data_atual = clone $primeira_segunda;
        
        while ($data_atual < $data_prova) {
            $fim_semana = clone $data_atual;
            $fim_semana->modify('+6 days'); // Vai até domingo
            
            // Se o fim da semana ultrapassar a data da prova, ajusta
            if ($fim_semana > $data_prova) {
                $fim_semana = clone $data_prova;
            }
            
            // Determina as datas válidas para esta semana
            $datas_validas = [];
            $data_temp = clone $data_atual;
            
            while ($data_temp <= $fim_semana) {
                $dia_semana = (int)$data_temp->format('N');
                
                // Só inclui a data se:
                // 1. É um dia configurado para estudo
                // 2. É depois ou igual à data de início
                // 3. É antes da data da prova
                if (isset($this->dias_estudo[$dia_semana]) && 
                    $data_temp >= $inicio_real && 
                    $data_temp < $data_prova) {
                    $datas_validas[] = clone $data_temp;
                }
                $data_temp->modify('+1 day');
            }
            
            // Só adiciona a semana se tiver datas válidas
            if (!empty($datas_validas)) {
                $semanas_datas[] = [
                    'inicio' => clone $data_atual,
                    'fim' => clone $fim_semana,
                    'datas' => $datas_validas,
                    'primeiro_card' => clone $datas_validas[0],
                    'ultimo_card' => clone $datas_validas[count($datas_validas) - 1]
                ];
            }
            
            // Avança para a próxima semana
            $data_atual->modify('+7 days');
        }
        
        return $semanas_datas;
    }
    
    public function organizarConteudosPorSemana() {
        $total_conteudos = count($this->conteudos);
        $semanas_datas = $this->gerarSemanasDatas();
        $total_semanas = count($semanas_datas);
        
        // Dias de estudo ordenados
        $dias_estudo = array_keys($this->dias_estudo);
        sort($dias_estudo);
        
        $conteudosPorSemana = [];
        $conteudoIndex = 0;
        
        for ($semana = 0; $semana < $total_semanas; $semana++) {
            $semanaAtual = array_fill(0, count($dias_estudo), []);
            
            // Se for a última semana (semana da prova), não distribui conteúdos
            if ($semana === $total_semanas - 1) {
                $conteudosPorSemana[] = $semanaAtual;
                continue;
            }
            
            $datas_validas = $semanas_datas[$semana]['datas'];
            
            // Calcula conteúdos para esta semana
            $conteudos_restantes = $total_conteudos - $conteudoIndex;
            $semanas_restantes = $total_semanas - $semana - 1;
            
            $conteudos_desta_semana = $semanas_restantes > 0 
                ? ceil($conteudos_restantes / $semanas_restantes)
                : $conteudos_restantes;
            
            // Calcula total de horas da semana
            $total_horas_semana = 0;
            foreach ($datas_validas as $data) {
                $dia_semana = (int)$data->format('N');
                $total_horas_semana += $this->dias_estudo[$dia_semana];
            }
            
            // Distribui os conteúdos
            if ($total_horas_semana > 0 && $conteudos_desta_semana > 0) {
                foreach ($datas_validas as $data) {
                    $dia_semana = (int)$data->format('N');
                    $dia_index = array_search($dia_semana, $dias_estudo);
                    
                    if ($dia_index !== false) {
                        $horas_dia = $this->dias_estudo[$dia_semana];
                        $proporcao = $horas_dia / $total_horas_semana;
                        $cards_dia = ceil($conteudos_desta_semana * $proporcao);
                        
                        for ($j = 0; $j < $cards_dia && $conteudoIndex < $total_conteudos; $j++) {
                            $conteudo = $this->conteudos[$conteudoIndex++];
                            // Adiciona a data prevista ao conteúdo
                            $conteudo['data_prevista'] = $data->format('Y-m-d');
                            $semanaAtual[$dia_index][] = $conteudo;
                        }
                    }
                }
            }
            
            $conteudosPorSemana[] = $semanaAtual;
        }
        
        return $conteudosPorSemana;
    }


    public function replanejarEstudo($novaDataInicio) {
        if (!isset($this->prova['id'])) {
            throw new Exception('Prova não encontrada');
        }
    
        try {
            pg_query($this->conexao, 'BEGIN');
    
            // Atualiza a data de início da prova
            $query = "UPDATE appestudo.provas 
                     SET data_inicio_estudo = $1 
                     WHERE id = $2";
            $result = pg_query_params($this->conexao, $query, array(
                $novaDataInicio,
                $this->prova['id']
            ));
    
            if (!$result) {
                throw new Exception('Erro ao atualizar data de início da prova');
            }
    
            // Recalcula as datas para cada conteúdo
            $dataInicio = new DateTime($novaDataInicio);
            $conteudos = $this->getConteudos();
            $info = $this->getInfoEstudo();
            $cardsPorDia = $info['cards_por_dia'];
    
            $dataAtual = clone $dataInicio;
            $conteudoIndex = 0;
    
            foreach ($conteudos as $conteudo) {
                // Pula para o próximo dia útil quando atingir o limite de cards
                if ($conteudoIndex >= $cardsPorDia) {
                    $dataAtual->modify('+1 day');
                    $conteudoIndex = 0;
    
                    // Pula finais de semana
                    while ($dataAtual->format('N') >= 6) {
                        $dataAtual->modify('+1 day');
                    }
                }
    
                // Atualiza a data de estudo do conteúdo
                $query = "UPDATE appestudo.usuario_conteudo 
                         SET data_estudo = $1 
                         WHERE id = $2 AND usuario_id = $3";
                
                $result = pg_query_params($this->conexao, $query, array(
                    $dataAtual->format('Y-m-d'),
                    $conteudo['id'],
                    $this->usuario_id
                ));
    
                if (!$result) {
                    throw new Exception('Erro ao atualizar conteúdo: ' . pg_last_error($this->conexao));
                }
    
                $conteudoIndex++;
            }
    
            pg_query($this->conexao, 'COMMIT');
    
            return [
                'nova_data_inicio' => $novaDataInicio,
                'conteudos_atualizados' => count($conteudos),
                'cards_por_dia' => $cardsPorDia
            ];
    
        } catch (Exception $e) {
            pg_query($this->conexao, 'ROLLBACK');
            throw $e;
        }
    }
    public function getPlanejamentoAtivo() {
        $query = "SELECT p.idplanejamento 
                  FROM appEstudo.planejamento p 
                  WHERE p.usuario_idusuario = $1 
                  ORDER BY p.data_inicio DESC 
                LIMIT 1";
    
        $resultado = pg_query_params($this->conexao, $query, array($this->usuario_id));

        if ($resultado && pg_num_rows($resultado) > 0) {
            $planejamento = pg_fetch_assoc($resultado);
            return $planejamento['idplanejamento'];
        }
    
        return null;
    }

    public function adicionarMateriaPlanejamento($materia_nome) {
        try {
            // Primeiro, pegar o ID da matéria
            $query_materia = "SELECT idmateria FROM appEstudo.materia WHERE nome = $1";
            $result_materia = pg_query_params($this->conexao, $query_materia, array($materia_nome));
            
            if (!$result_materia || pg_num_rows($result_materia) === 0) {
                error_log("Matéria não encontrada: " . $materia_nome);
                return false;
            }
            
            $materia = pg_fetch_assoc($result_materia);
            $id_materia = $materia['idmateria'];
            
            $id_planejamento = $this->getPlanejamentoAtivo();
            if (!$id_planejamento) {
                error_log("Planejamento ativo não encontrado");
                return false;
            }
            
            // Verificar se já existe essa relação
            $query_check = "SELECT 1 FROM appEstudo.planejamento_materia 
                           WHERE planejamento_idplanejamento = $1 
                           AND materia_idmateria = $2";
            
            $result_check = pg_query_params($this->conexao, $query_check, array(
                $id_planejamento,
                $id_materia
            ));
            
            if (pg_num_rows($result_check) > 0) {
                error_log("Matéria já existe no planejamento");
                return true; // Já existe, consideramos sucesso
            }
            
            // Inserir na tabela planejamento_materia
            $query_insert = "INSERT INTO appEstudo.planejamento_materia 
                            (planejamento_idplanejamento, materia_idmateria) 
                            VALUES ($1, $2)";
                            
            $result_insert = pg_query_params($this->conexao, $query_insert, array(
                $id_planejamento,
                $id_materia
            ));
            
            if (!$result_insert) {
                error_log("Erro ao inserir matéria: " . pg_last_error($this->conexao));
                return false;
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Erro ao adicionar matéria: " . $e->getMessage());
            return false;
        }
    }

    public function getQuantidadeDiasEstudo() {
        // Se dias_estudo ainda não foi carregado, carrega
        if (empty($this->dias_estudo)) {
            $this->carregarDiasEstudo();
        }
        
        // Retorna a quantidade de dias no array
        return count($this->dias_estudo);
    }

    /**
 * Retorna apenas os conteúdos que não têm filhos (conteúdos finais)
 */
public function getConteudosFiltrados() {
    if ($this->conteudos === null) {
        $this->getConteudos();
    }
    
    return filtrarTodosConteudos($this->conteudos);
}

public function organizarConteudosPorSemanaFiltrados() {
    $conteudosFiltrados = filtrarTodosConteudos($this->conteudos);
    
    // Se a prova foi replanejada, removemos apenas os conteúdos estudados ANTES do replanejamento
    if (isset($this->prova['replanejado']) && $this->prova['replanejado'] === true) {
        // Removemos do cronograma principal APENAS os conteúdos que:
        // 1. Estão marcados como estudados E
        // 2. Têm o campo replanejado_em preenchido (foram estudados antes do replanejamento)
        $conteudosFiltrados = array_filter($conteudosFiltrados, function($item) {
            // Se não for estudado OU se for estudado mas não tiver replanejado_em, mantém no cronograma
            return $item['status_estudo'] !== 'Estudado' || 
                   !isset($item['replanejado_em']) || 
                   empty($item['replanejado_em']);
        });
    }
    
    // Log para depuração
    error_log("Total de conteúdos para distribuição: " . count($conteudosFiltrados));

    // Continuação da função original
    $total_conteudos = count($conteudosFiltrados);
    $semanas_datas = $this->gerarSemanasDatas();
    $total_semanas = count($semanas_datas);
    
    // Dias de estudo ordenados
    $dias_estudo = array_keys($this->dias_estudo);
    sort($dias_estudo);
    
    $conteudosPorSemana = [];
    $conteudoIndex = 0;
    
    for ($semana = 0; $semana < $total_semanas; $semana++) {
        $semanaAtual = array_fill(0, count($dias_estudo), []);
        
        // Se for a última semana (semana da prova), não distribui conteúdos
        if ($semana === $total_semanas - 1) {
            $conteudosPorSemana[] = $semanaAtual;
            continue;
        }
        
        $datas_validas = $semanas_datas[$semana]['datas'];
        
        // Calcula conteúdos para esta semana
        $conteudos_restantes = $total_conteudos - $conteudoIndex;
        $semanas_restantes = $total_semanas - $semana - 1;
        
        $conteudos_desta_semana = $semanas_restantes > 0 
            ? ceil($conteudos_restantes / $semanas_restantes)
            : $conteudos_restantes;
        
        // Calcula total de horas da semana
        $total_horas_semana = 0;
        foreach ($datas_validas as $data) {
            $dia_semana = (int)$data->format('N');
            $total_horas_semana += $this->dias_estudo[$dia_semana];
        }
        
        // Distribui os conteúdos
        if ($total_horas_semana > 0 && $conteudos_desta_semana > 0) {
            foreach ($datas_validas as $data) {
                $dia_semana = (int)$data->format('N');
                $dia_index = array_search($dia_semana, $dias_estudo);
                
                if ($dia_index !== false) {
                    $horas_dia = $this->dias_estudo[$dia_semana];
                    $proporcao = $horas_dia / $total_horas_semana;
                    $cards_dia = ceil($conteudos_desta_semana * $proporcao);
                    
                    // Convertemos para array indexado para acesso preciso pelo índice
                    $conteudosFiltradosArray = array_values($conteudosFiltrados);
                    
                    for ($j = 0; $j < $cards_dia && $conteudoIndex < $total_conteudos; $j++) {
                        if (isset($conteudosFiltradosArray[$conteudoIndex])) {
                            $conteudo = $conteudosFiltradosArray[$conteudoIndex++];
                            // Adiciona a data prevista ao conteúdo
                            $conteudo['data_prevista'] = $data->format('Y-m-d');
                            $semanaAtual[$dia_index][] = $conteudo;
                        }
                    }
                }
            }
        }
        
        $conteudosPorSemana[] = $semanaAtual;
    }
    
    return $conteudosPorSemana;
}

}

/**
 * Verifica se um capítulo possui filhos na lista de conteúdos
 * @param string $capitulo O número do capítulo a verificar
 * @param array $todosConteudos Lista de todos os conteúdos
 * @return boolean
 */
function possuiFilhos($capitulo, $todosConteudos) {
    foreach ($todosConteudos as $c) {
        // Verifica se o conteúdo é filho direto do capítulo
        if (strpos($c['capitulo'], $capitulo . '.') === 0) {
            // Garante que é um filho direto (um nível abaixo)
            $restante = substr($c['capitulo'], strlen($capitulo) + 1);
            if (strpos($restante, '.') === false) {
                return true;
            }
        }
    }
    return false;
}

/**
 * Filtra conteúdos que possuem filhos da lista
 * @param array $conteudos Lista de conteúdos para filtrar
 * @return array Lista filtrada sem os pais que possuem filhos
 */
function filtrarConteudosComFilhos($conteudos) {
    $resultado = [];
    foreach ($conteudos as $conteudo) {
        if (!possuiFilhos($conteudo['capitulo'], $conteudos)) {
            $resultado[] = $conteudo;
        }
    }
    return $resultado;
}

