<?php
include 'processa_index.php';

// Consultar os dados do planejamento relacionado ao usuario logado
$query_consultar_idplanejamento = "SELECT p.idplanejamento
        FROM appEstudo.planejamento p 
        WHERE p.usuario_idusuario = $id_usuario";
$resultado_idplanejamento = pg_query($conexao, $query_consultar_idplanejamento);

if ($resultado_idplanejamento) {
    $row = pg_fetch_assoc($resultado_idplanejamento);
    $id_planejamento = $row['idplanejamento'];
} else {
    echo "Erro ao executar a consulta.";
}

?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src='fullcalendar-6.1.8/dist/index.global.js'></script>
    <script src='fullcalendar-6.1.8/packages/core/locales/pt-br.global.js'></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Anton&family=Orbitron:wght@400;500;600;700;800;900&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Ysabeau+SC:wght@1;100;200;300;400;500;600;700;800;900;1000&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courier+Prime:ital,wght@0,400;0,700;1,400;1,700&display=swap"
          rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.6/jquery.inputmask.min.js"></script>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css"
          integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"
            integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy"
            crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js"></script>
    <title>Calendário - Agenda Pessoal</title>
    <link rel="stylesheet" href="css/index_agenda.css">
    <script src="js/caminho_para_fullcalendar.js"></script>

    <script>
        document.addEventListener("DOMContentLoaded", function () {
            var selectBox = document.getElementById("tipo_evento");
            selectBox.addEventListener("change", function () {
                var selectedValue = selectBox.value;

                if (selectedValue === "Planejamento") {
                    document.getElementById("campo_materia").style.display = "block";
                    document.getElementById("materia").setAttribute("required", "required");
                    document.getElementById("campo_titulo").style.display = "none";
                    document.getElementById("titulo").removeAttribute("required");
                } else {
                    document.getElementById("campo_materia").style.display = "none";
                    document.getElementById("materia").removeAttribute("required");
                    document.getElementById("campo_titulo").style.display = "block";
                    document.getElementById("titulo").setAttribute("required", "required");
                }
            });

// Função para validar o intervalo de datas
            function validateDateRange() {
                var startDate = new Date(document.getElementById('start_date').value);
                var endDate = new Date(document.getElementById('end_date').value);
                var timeDiff = endDate - startDate;
                var dayDiff = timeDiff / (1000 * 3600 * 24);

                if (dayDiff > 15) {
                    alert('O intervalo entre a data de início e a data de término não pode exceder 15 dias.');
                    // Limpar o campo de data de término
                    document.getElementById('end_date').value = '';
                    return false;
                }
                return true;
            }


            // Adicionar evento de mudança ao campo de data de término
            var endDateInput = document.getElementById('end_date');
            endDateInput.addEventListener('change', function () {
                validateDateRange();
            });

            // Adicionar evento de mudança ao campo de data de início
            var startDateInput = document.getElementById('start_date');
            startDateInput.addEventListener('change', function () {
                validateDateRange();
            });

            // Adicionar validação ao enviar o formulário
            var form = document.getElementById('eventoForm');
            form.addEventListener('submit', function (event) {
                if (!validateDateRange()) {
                    event.preventDefault();
                }
            });
        });

        function validarFormulario() {
            // Obter os valores do formulário
            var start_date = document.getElementById('start_date').value;
            var end_date = document.getElementById('end_date').value;
            var materias_per_day = parseInt(document.getElementById('materias_per_day').value);
            var materias_selecionadas = document.querySelectorAll('input[name="materias_selecionadas[]"]:checked');

            // Calcular a quantidade de dias entre as datas de início e término
            var intervalo = new Date(end_date) - new Date(start_date);
            var quantidadeDias = intervalo / (1000 * 3600 * 24) + 1; // +1 para incluir o último dia

            // Calcular a quantidade total de matérias selecionadas
            var quantidadeMateriasSelecionadas = materias_selecionadas.length;

            // Calcular a quantidade total de matérias necessárias
            var quantidadeTotalMaterias = quantidadeDias * materias_per_day;

            // Verificar se a quantidade de matérias selecionadas é menor que a quantidade total de matérias necessárias
            if (quantidadeMateriasSelecionadas > quantidadeTotalMaterias) {
                alert('ERRO! Tem Mais Matérias do que Quantidade de Dias Multiplicada por Matéria ao Dia');
                return false; // Impedir o envio do formulário
            }
            return true; // Permitir o envio do formulário
        }

    </script>
</head>
<link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">

<style>
    body, html {
        margin: 0;
        padding: 0;
        height: 100%;
    }

    .container {
        margin: 0 auto; /* Centraliza horizontalmente */
        padding: 0; /* Remove o preenchimento padrão */
    }

    .btn-gold {
        color: #F5DEB3; /* Cor dourada */
        font-family: 'Courier Prime', monospace;
    }
    #calendar {
        /* Seu estilo para o calendário */
        border: 2px solid #2b2723; /* Exemplo de borda, ajuste conforme necessário */
        padding: 20px; /* Exemplo de padding, ajuste conforme necessário */
        box-shadow: 0px 4px 8px rgb(43, 39, 35); /* Sombra */
        background-color: #FCE6E9; /* Fundo cinza */
        border-radius: 10px; /* Bordas arredondadas */
        max-width: 1100px;
        --fc-today-bg-color: rgba(1, 176, 204, 0.27);
        --fc-border-color: rgba(43, 39, 35, 0.84);
        --fc-non-business-color: rgba(252, 163, 163, 0.33);

    }
    .legenda-eventos {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 10px;
    }

    .legenda-item {
        display: flex;
        flex-wrap: wrap;
    }

    .legenda-cor {
        width: 20px;
        height: 20px;
        margin-right: 5px;
    }

    .legenda-texto {
        margin-right: 20px;
    }

    /* Espaço entre as legendas */
    .legenda-spacer {
        width: 20px;
    }

    /* Estilo para telas menores */
    @media (max-width: 600px) {
        .legenda-item {
            flex-direction: column;
            align-items: center;
        }

        .legenda-texto {
            margin-right: 0;
            margin-bottom: 10px;
        }

        .legenda-spacer {
            display: none;
        }
    }
</style>
<body>

<div class="container">
    <div id="header" style="border: 2px solid #2b2723; padding: 20px; border-radius: 10px; box-shadow: 0px 4px 8px rgb(43,39,35); background-color: #FCE6E9;">
        <?php
        echo "<h2 class='titulo'>Criar Estudo Automatico na Agenda de <strong>$nome_usuario</strong></h2>";
        ?>
        <form id="eventoForm" action="inserir_evento_agenda_AUTOMATICO.php" method="POST" onsubmit="return validarFormulario();">
            <label for="tipo_evento" class="titulo">Tipo de Evento:</label>
            <select id="tipo_evento" name="tipo_evento" class="campoTipo" required>
                <option value="Planejamento">Planejamento</option>
            </select>

            <input type="hidden" name="autoFill" value="true">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px;">
                <div>
                    <label for="start_date" class="titulo">Data de Início:</label>
                    <input class="titulo" type="date" id="start_date" name="start_date" required>
                </div>
                <div>
                    <label for="end_date" class="titulo">Data de Término(Máximo 15 Dias):</label>
                    <input class="titulo" type="date" id="end_date" name="end_date" required>
                </div>
                <div>
                    <label for="materias_per_day" class="titulo">Matérias por Dia(Máximo 3):</label>
                    <input class="titulo" type="number" id="materias_per_day" name="materias_per_day" min="1" max="3" required>
                </div>
            </div>

            <div style="margin-top: 10px;">
                <label class="titulo"><strong>Matérias</strong>(Selecione Quais Deseja Utilizar Para Preenchimento)<strong>:</strong></label><br>
                <?php
                $materias = array();
                $query_consultar_materias = "SELECT m.nome FROM appEstudo.materia m 
                                     INNER JOIN appEstudo.planejamento_materia pm ON m.idmateria = pm.materia_idmateria
                                     WHERE pm.planejamento_idplanejamento = (SELECT p.idplanejamento FROM appEstudo.planejamento p WHERE p.usuario_idusuario = $id_usuario)";
                $resultado_materias = pg_query($conexao, $query_consultar_materias);

                // Exibir as matérias com caixas de seleção
                while ($row = pg_fetch_assoc($resultado_materias)) {
                    $nomeMateria = $row['nome'];
                    echo "<input type='checkbox' name='materias_selecionadas[]' value='$nomeMateria'> <span class='titulo'>$nomeMateria</span><br>";
                }
                ?>
            </div>

            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px;">
                <button type="submit" class="btn btn-warning btn-sm" style="font-family: 'Courier Prime', monospace;">Preencher</button>
                <div>
                    <!-- Botão para voltar à página index.php com a font definida -->
                    <a href="#" class="btn btn-dark btn-sm btn-gold"
                       onclick="if (window.opener) { window.opener.location.reload(); } window.close(); return false;">Fechar Página</a>
                </div>
            </div>
        </form>


        <!-- Legenda de tipos de evento -->
        <div class="legenda-eventos" style="display: flex; justify-content: center; align-items: justify; margin-top: 10px;">
            <div class="legenda-item" style="margin-right: 20px;">
                <div class="legenda-cor" style="background-color: #1976D2;"></div>
                <div class="legenda-texto">Faculdade</div>

                <div class="legenda-cor" style="background-color: gray;"></div>
                <div class="legenda-texto">Trabalho</div>

                <div class="legenda-cor" style="background-color: blue;"></div>
                <div class="legenda-texto">Concurso</div>

                <div class="legenda-cor" style="background-color: #00796B;"></div>
                <div class="legenda-texto">Pessoal</div>

                <div class="legenda-cor" style="background-color: #FFD700;"></div>
                <div class="legenda-texto">Planejamento</div>

                <div class="legenda-cor legenda-pendente"></div>
                <div class="legenda-texto">Evento Pendente</div>

                <div class="legenda-cor legenda-realizado"></div>
                <div class="legenda-texto">Evento Realizado</div>
            </div>
        </div>

    </div>
</div>

<div style="display: flex; justify-content: center; align-items: center;" id="calendar-container">
    <div id="calendar"></div>
</div>
<!-- Local onde o calendário será renderizado -->

</body>
</html>
