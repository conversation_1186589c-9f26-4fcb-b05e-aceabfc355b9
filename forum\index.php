<?php
// Incluir arquivo de configuração de sessão
include_once("../session_config.php");
include_once("../assets/config.php");

if (!verificarSessaoValida()) {
    header("Location: ../login_index.php");
    exit();
}

// Buscar nome do usuário
$query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = " . $_SESSION['idusuario'];
$resultado_nome = pg_query($conexao, $query_buscar_nome);
$nome_usuario = ($resultado_nome && pg_num_rows($resultado_nome) > 0) 
    ? pg_fetch_assoc($resultado_nome)['nome'] 
    : "Usuário";

// Determinar a ordenação com base no parâmetro 'tab'
$tab = isset($_GET['tab']) ? $_GET['tab'] : 'recentes';

// Consulta base para todos os tópicos
$query_base = "
    SELECT 
        t.id,
        t.titulo,
        t.conteudo,
        t.created_at,
        t.views,
        u.nome as autor_nome,
        c.nome as categoria_nome,
        c.id as categoria_id,
        (SELECT COUNT(*) FROM appestudo.forum_respostas r WHERE r.topico_id = t.id AND r.status = true) as total_respostas,
        (SELECT MAX(created_at) FROM appestudo.forum_respostas r WHERE r.topico_id = t.id AND r.status = true) as ultima_resposta
    FROM 
        appestudo.forum_topicos t
    JOIN 
        appestudo.usuario u ON t.usuario_id = u.idusuario
    JOIN 
        appestudo.forum_categorias c ON t.categoria_id = c.id
    WHERE 
        t.status = true";

// Adicionar ordenação específica com base na aba selecionada
if ($tab == 'recentes') {
    $query_topicos = $query_base . " ORDER BY t.created_at DESC LIMIT 10";
} elseif ($tab == 'populares') {
    $query_topicos = $query_base . " ORDER BY total_respostas DESC, t.views DESC LIMIT 10";
} elseif ($tab == 'emalta') {
    // Em alta: tópicos com mais visualizações, sem filtro de data
    $query_topicos = $query_base . " ORDER BY t.views DESC, t.created_at DESC LIMIT 10";
} else {
    // Padrão: recentes
    $query_topicos = $query_base . " ORDER BY t.created_at DESC LIMIT 10";
}

$result_topicos = pg_query($conexao, $query_topicos);

// Buscar categorias populares
$query_categorias = "
    SELECT 
        c.id,
        c.nome,
        COUNT(t.id) as total_topicos
    FROM 
        appestudo.forum_categorias c
    LEFT JOIN 
        appestudo.forum_topicos t ON c.id = t.categoria_id AND t.status = true
    WHERE 
        c.status = true
    GROUP BY 
        c.id, c.nome
    ORDER BY 
        total_topicos DESC
    LIMIT 5";

$result_categorias = pg_query($conexao, $query_categorias);
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fórum PlanejaAqui</title>
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <link rel="stylesheet" href="css/forum.css">
    
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Notificações */
        .notification-wrapper {
            position: relative;
            display: inline-block;
        }
        .icon-button {
            position: relative;
        }
        .notification-badge {
            position: absolute;
            top: 2px;
            right: 2px;
            background: #e53935;
            color: #fff;
            border-radius: 50%;
            font-size: 0.7em;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2;
        }
        .notification-dropdown {
            display: none;
            position: absolute;
            right: 0;
            top: 36px;
            width: 320px;
            background: var(--bg-color, #fff);
            box-shadow: 0 4px 24px rgba(0,0,0,0.13);
            border-radius: 8px;
            z-index: 1002;
            border: 1px solid var(--border, #e0e0e0);
            animation: fadeInNotif 0.18s;
        }
        @keyframes fadeInNotif {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .notification-dropdown .dropdown-header {
            padding: 14px 18px 10px 18px;
            font-weight: 600;
            border-bottom: 1px solid var(--border, #e0e0e0);
            color: var(--text-color, #222);
        }
        .notification-dropdown .dropdown-list {
            list-style: none;
            margin: 0;
            padding: 0;
            max-height: 220px;
            overflow-y: auto;
        }
        .notification-dropdown .dropdown-list li {
            padding: 12px 18px 8px 18px;
            border-bottom: 1px solid var(--border, #f0f0f0);
            font-size: 0.97em;
            cursor: pointer;
            transition: background 0.18s;
        }
        .notification-dropdown .dropdown-list li:hover {
            background: var(--hover-color, #f5f5f5);
        }
        .notif-title {
            font-weight: 500;
            color: var(--text-color, #222);
        }
        .notif-time {
            font-size: 0.85em;
            color: #888;
        }
        .notification-dropdown .dropdown-footer {
            padding: 10px 18px;
            text-align: right;
            background: var(--bg-color, #fff);
            border-top: 1px solid var(--border, #e0e0e0);
        }
        .notification-dropdown .dropdown-footer a {
            color: var(--accent-color, #2a7ae4);
            font-size: 0.97em;
            text-decoration: none;
        }
        @media (max-width: 600px) {
            .notification-dropdown {
                width: 98vw;
                right: -10vw;
            }
        }
    </style>
</head>
<body>
    <header class="main-header">
        <div class="header-container">
            <div class="header-left">
                <a href="index.php" class="logo">
                    <strong>PlanejaAqui</strong> <span>Fórum</span>
                </a>
                <form class="search-container" action="buscar.php" method="get" style="display:flex;">
                    <input type="text" name="termo" placeholder="Buscar no fórum..." class="search-input">
                    <button type="submit" class="search-button"><i class="fas fa-search"></i></button>
                </form>
            </div>
            <div class="header-right">
                <a href="#" class="btn-criar-post" id="btnCriarPost">Criar Post</a>
                <div class="notification-wrapper" style="position:relative;display:inline-block;">
                    <a href="#" class="icon-button" id="notificationBell" title="Notificações">
                        <i class="far fa-bell"></i>
                        <span class="notification-badge" id="notificationBadge" style="display:none;">2</span>
                    </a>
                    <div class="notification-dropdown" id="notificationDropdown">
                        <div class="dropdown-header">Notificações</div>
                        <ul class="dropdown-list">
                            <li><span class="notif-title">Nova resposta no seu tópico</span><br><span class="notif-time">há 2 minutos</span></li>
                            <li><span class="notif-title">Você foi mencionado em um comentário</span><br><span class="notif-time">há 1 hora</span></li>
                        </ul>
                        <div class="dropdown-footer"><a href="todas_notificacoes.php">Ver todas</a></div>
                    </div>
                </div>
                <a href="#" class="icon-button" id="toggleDarkMode" title="Alternar modo escuro">
                    <i class="fas fa-moon"></i>
                </a>
            </div>
        </div>
    </header>

    <div class="main-container">
        <div class="content-container">
            <div class="forum-header">
                <h1>Fórum PlanejaAqui</h1>
                <div class="forum-tabs">
                    <a href="#" data-tab="recentes" class="tab <?php echo ($tab == 'recentes' || $tab == '') ? 'active' : ''; ?>">
                        <i class="fas fa-clock"></i> Recentes
                    </a>
                    <a href="#" data-tab="populares" class="tab <?php echo ($tab == 'populares') ? 'active' : ''; ?>">
                        <i class="fas fa-fire"></i> Populares
                    </a>
                    <a href="#" data-tab="emalta" class="tab <?php echo ($tab == 'emalta') ? 'active' : ''; ?>">
                        <i class="fas fa-arrow-up"></i> Em Alta
                    </a>
                </div>
                <div class="forum-actions">
                    <a href="#" class="btn-criar-post">Criar Post</a>
                    <button class="btn-filtrar" id="btnFiltro">
                        <i class="fas fa-filter"></i>
                        <span>Filtros</span>
                        <span class="filter-badge" id="filterBadge" style="display: none;">0</span>
                    </button>
                </div>
            </div>

            <!-- Painel de Filtros -->
            <div class="filter-panel" id="filterPanel">
                <div class="filter-panel-header">
                    <h3><i class="fas fa-sliders-h"></i> Filtros</h3>
                    <button class="close-filter" onclick="toggleFilterPanel()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="filter-panel-content">
                    <div class="filter-section">
                        <h4>Categoria</h4>
                        <div class="filter-options" id="filterCategorias">
                            <label class="filter-option">
                                <input type="radio" name="categoria" value="" checked>
                                <span>Todas as categorias</span>
                            </label>
                            <?php
                            $query_todas_categorias = "SELECT id, nome FROM appestudo.forum_categorias WHERE status = true ORDER BY nome";
                            $result_todas_categorias = pg_query($conexao, $query_todas_categorias);
                            while ($cat = pg_fetch_assoc($result_todas_categorias)) {
                                echo '<label class="filter-option">
                                    <input type="radio" name="categoria" value="' . $cat['id'] . '">
                                    <span>' . htmlspecialchars($cat['nome']) . '</span>
                                </label>';
                            }
                            ?>
                        </div>
                    </div>

                    <div class="filter-section">
                        <h4>Período</h4>
                        <div class="filter-options" id="filterPeriodo">
                            <label class="filter-option">
                                <input type="radio" name="periodo" value="" checked>
                                <span>Todo o período</span>
                            </label>
                            <label class="filter-option">
                                <input type="radio" name="periodo" value="1">
                                <span>Últimas 24 horas</span>
                            </label>
                            <label class="filter-option">
                                <input type="radio" name="periodo" value="7">
                                <span>Última semana</span>
                            </label>
                            <label class="filter-option">
                                <input type="radio" name="periodo" value="30">
                                <span>Último mês</span>
                            </label>
                            <label class="filter-option">
                                <input type="radio" name="periodo" value="365">
                                <span>Último ano</span>
                            </label>
                        </div>
                    </div>

                    <div class="filter-section">
                        <h4>Ordenar por</h4>
                        <div class="filter-options" id="filterOrdem">
                            <label class="filter-option">
                                <input type="radio" name="ordem" value="recentes" checked>
                                <span><i class="fas fa-clock"></i> Mais recentes</span>
                            </label>
                            <label class="filter-option">
                                <input type="radio" name="ordem" value="populares">
                                <span><i class="fas fa-comments"></i> Mais comentados</span>
                            </label>
                            <label class="filter-option">
                                <input type="radio" name="ordem" value="emalta">
                                <span><i class="fas fa-fire"></i> Mais visualizados</span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="filter-panel-footer">
                    <button class="btn-limpar" onclick="limparFiltros()">
                        <i class="fas fa-undo"></i> Limpar Filtros
                    </button>
                    <button class="btn-aplicar" onclick="aplicarFiltros()">
                        <i class="fas fa-check"></i> Aplicar
                    </button>
                </div>
            </div>

            <div class="topics-list" id="topics-container">
                <?php if ($result_topicos && pg_num_rows($result_topicos) > 0): ?>
                    <?php while ($topico = pg_fetch_assoc($result_topicos)): 
                        // Extrair um resumo do conteúdo
                        $resumo = strip_tags($topico['conteudo']);
                        $resumo = (strlen($resumo) > 150) ? substr($resumo, 0, 150) . '...' : $resumo;
                        
                        // Calcular tempo desde a postagem
                        $data_postagem = strtotime($topico['created_at']);
                        $agora = time();
                        $diferenca = $agora - $data_postagem;
                        
                        if ($diferenca < 60) {
                            $tempo = "agora mesmo";
                        } elseif ($diferenca < 3600) {
                            $minutos = floor($diferenca / 60);
                            $tempo = $minutos . " minuto" . ($minutos > 1 ? "s" : "") . " atrás";
                        } elseif ($diferenca < 86400) {
                            $horas = floor($diferenca / 3600);
                            $tempo = $horas . " hora" . ($horas > 1 ? "s" : "") . " atrás";
                        } else {
                            $dias = floor($diferenca / 86400);
                            if ($dias < 30) {
                                $tempo = $dias . " dia" . ($dias > 1 ? "s" : "") . " atrás";
                            } else {
                                $tempo = date('d/m/Y', $data_postagem);
                            }
                        }
                    ?>
                    <div class="topic-card">

                        <div class="topic-content">
                            <div class="topic-category">
                                <a href="ver_categoria.php?id=<?php echo $topico['categoria_id']; ?>">c/<?php echo $topico['categoria_nome']; ?></a>
                                <span class="topic-meta">• Postado por u/<?php echo strtolower(str_replace(' ', '_', $topico['autor_nome'])); ?> • <?php echo $tempo; ?></span>
                            </div>
                            <h2 class="topic-title">
                                <a href="ver_topico.php?id=<?php echo $topico['id']; ?>" onclick="incrementarVisualizacao(<?php echo $topico['id']; ?>)"><?php echo htmlspecialchars($topico['titulo']); ?></a>
                            </h2>
                            <p class="topic-excerpt"><?php echo htmlspecialchars($resumo); ?></p>
                            <div class="topic-actions">
                                <a href="ver_topico.php?id=<?php echo $topico['id']; ?>#comentarios" class="topic-action">
                                    <i class="far fa-comment-alt"></i> <?php echo $topico['total_respostas']; ?> comentários
                                </a>
                                <span class="topic-action">
                                    <i class="far fa-eye"></i> <?php echo $topico['views']; ?> visualizações
                                </span>
                                <button class="topic-action">
                                    <i class="fas fa-share"></i> Compartilhar
                                </button>
                            </div>
                        </div>
                    </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="no-topics">
                        <p>Nenhum tópico encontrado. Seja o primeiro a criar um!</p>
                        <a href="novo_topico.php" class="btn-criar-post">Criar Tópico</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="sidebar">
            <div class="sidebar-card welcome-card">
                <h2>Bem-vindo ao Fórum, <?php echo htmlspecialchars($nome_usuario); ?></h2>
                <p>Um espaço para discutir planejamento financeiro, investimentos e muito mais.</p>
                <a href="#" class="btn-criar-post">Criar uma nova publicação</a>
            </div>

            <div class="sidebar-card">
                <h2>Categorias Populares</h2>
                <ul class="categories-list">
                    <?php if ($result_categorias && pg_num_rows($result_categorias) > 0): ?>
                        <?php while ($categoria = pg_fetch_assoc($result_categorias)): ?>
                        <li>
                            <a href="ver_categoria.php?id=<?php echo $categoria['id']; ?>">c/<?php echo $categoria['nome']; ?></a>
                            <span class="category-count"><?php echo $categoria['total_topicos']; ?></span>
                        </li>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <li>Nenhuma categoria encontrada</li>
                    <?php endif; ?>
                </ul>
                <a href="categorias.php" class="link-ver-todas">Ver todas as categorias</a>
            </div>

            <div class="sidebar-card">
                <h2>Regras do Fórum</h2>
                <ol class="rules-list">
                    <li>Seja respeitoso com os outros usuários</li>
                    <li>Não compartilhe informações pessoais</li>
                    <li>Evite spam e conteúdo promocional</li>
                    <li>Mantenha a discussão relacionada ao tema</li>
                    <li>Respeite as leis de direitos autorais</li>
                    <li>Posts que violarem estas regras serão removidos sem aviso prévio</li>   
                </ol>
            </div>
        </div>
    </div>

    <!-- Modal de Seleção de Categoria -->
    <div id="modalCategoria" class="modal" style="display:none; align-items:center; justify-content:center;">
        <div class="modal-content" style="max-width: 400px;">
            <div class="modal-header">
                <h2>Escolha a Categoria</h2>
                <button type="button" class="close-button" onclick="fecharModalCategoria()">&times;</button>
            </div>
            <div class="modal-body">
                <label for="select-categoria" style="font-weight:500; margin-bottom:8px; display:block;">Categoria:</label>
                <select id="select-categoria" style="width:100%; padding:8px; border-radius:4px; border:1px solid var(--border); font-size:15px;">
                    <option value="">Selecione...</option>
                    <?php
                    $query_todas_categorias = "SELECT id, nome FROM appestudo.forum_categorias WHERE status = true ORDER BY nome";
                    $result_todas_categorias = pg_query($conexao, $query_todas_categorias);
                    while ($cat = pg_fetch_assoc($result_todas_categorias)) {
                        echo '<option value="' . $cat['id'] . '">' . htmlspecialchars($cat['nome']) . '</option>';
                    }
                    ?>
                </select>
            </div>
            <div class="modal-buttons" style="justify-content: flex-end; margin-top: 18px;">
                <button class="btn-submit" onclick="continuarCriarPost()"><i class="fas fa-arrow-right"></i> Continuar</button>
                <button class="btn-cancel" onclick="fecharModalCategoria()" style="margin-left:8px;">Cancelar</button>
            </div>
        </div>
    </div>
    <script src="js/forum.js"></script>
    <script>
    document.querySelectorAll('.btn-criar-post').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            abrirModalCategoria();
        });
    });
    </script>
</body>
</html>



