<?php
session_start();
require_once('../includes/auth.php');
require_once('../conexao_POST.php');
require_once('../cadastros/includes/verify_admin.php');

// Verifica se é o super admin
verificarSuperAdmin($conexao, false);

function realizarBackup($conexao) {
    try {
        $data = date('Y-m-d_H-i-s');
        $diretorio_backup = '../backups/';
        
        // Cria diretório se não existir
        if (!file_exists($diretorio_backup)) {
            mkdir($diretorio_backup, 0755, true);
        }

        // Array com as tabelas para backup
        $tabelas = array(
            'usuario',
            'planejamento',
            'log_seguranca',
            'log_requisicoes',
            'provas',
            'agenda'
        );

        $sucessos = [];
        $erros = [];

        foreach ($tabelas as $tabela) {
            $arquivo = $diretorio_backup . "backup_{$tabela}_{$data}.csv";
            $query = "COPY appEstudo.{$tabela} TO '" . pg_escape_string($arquivo) . "' WITH CSV HEADER";
            
            if (pg_query($conexao, $query)) {
                $sucessos[] = $tabela;
            } else {
                $erros[] = $tabela;
            }
        }

        // Criar arquivo de metadados
        $metadata = [
            'data_backup' => $data,
            'tabelas_backup' => $sucessos,
            'erros' => $erros,
            'versao_sistema' => '1.0',
            'usuario_backup' => $_SESSION['nome']
        ];

        file_put_contents(
            $diretorio_backup . "metadata_{$data}.json",
            json_encode($metadata, JSON_PRETTY_PRINT)
        );

        return [
            'sucesso' => true,
            'sucessos' => $sucessos,
            'erros' => $erros,
            'data' => $data
        ];

    } catch (Exception $e) {
        error_log("Erro no backup: " . $e->getMessage());
        return [
            'sucesso' => false,
            'erro' => $e->getMessage()
        ];
    }
}

$mensagem = '';
$tipo_mensagem = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['realizar_backup'])) {
    $resultado = realizarBackup($conexao);
    
    if ($resultado['sucesso']) {
        $mensagem = "Backup realizado com sucesso em " . $resultado['data'];
        $tipo_mensagem = "success";
    } else {
        $mensagem = "Erro ao realizar backup: " . $resultado['erro'];
        $tipo_mensagem = "error";
    }
}

?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Backup do Sistema</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            font-family: 'Quicksand', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            text-decoration: none;
            margin: 10px 0;
        }
        .btn-primary {
            background-color: #00008B;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .backup-info {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-database"></i> Backup do Sistema</h1>

        <?php if ($mensagem): ?>
            <div class="alert alert-<?php echo $tipo_mensagem; ?>">
                <?php echo $mensagem; ?>
            </div>
        <?php endif; ?>

        <div class="backup-info">
            <h3>Informações do Backup</h3>
            <p><strong>Tabelas incluídas:</strong></p>
            <ul>
                <li>Usuários</li>
                <li>Planejamentos</li>
                <li>Logs de Segurança</li>
                <li>Logs de Requisições</li>
                <li>Provas</li>
                <li>Agenda</li>
            </ul>
            <p><strong>Local do backup:</strong> /backups/</p>
        </div>

        <form method="POST" onsubmit="return confirm('Confirma a realização do backup?');">
            <button type="submit" name="realizar_backup" class="btn btn-primary">
                <i class="fas fa-download"></i> Realizar Backup
            </button>
        </form>

        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
    </div>
</body>
</html>