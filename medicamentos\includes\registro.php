<?php
// includes/registro.php
require_once __DIR__ . '/../config/database.php';

class Registro {
    private $conn;
    private $table_name = "appestudo.registros_uso";
    
    // Propriedades
    public $id;
    public $medicamento_id;
    public $data_hora;
    public $confirmado;
    public $observacao;
    
    // Construtor
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }
    
    // Adicionar um novo registro de uso
    public function adicionar() {
        $query = "INSERT INTO " . $this->table_name . " 
                (medicamento_id, data_hora, observacao) 
                VALUES 
                (:medicamento_id, :data_hora, :observacao)";
        
        $stmt = $this->conn->prepare($query);
        
        // Sanitizar dados
        $this->medicamento_id = intval($this->medicamento_id);
        if ($this->observacao) {
            $this->observacao = htmlspecialchars(strip_tags($this->observacao));
        } else {
            $this->observacao = null;
        }
        
        // Vincular parâmetros
        $stmt->bindParam(":medicamento_id", $this->medicamento_id);
        $stmt->bindParam(":data_hora", $this->data_hora);
        $stmt->bindParam(":observacao", $this->observacao);
        
        return $stmt->execute();
    }
    
    // Confirmar uso de medicamento
    public function confirmar() {
        $query = "UPDATE " . $this->table_name . " 
                SET confirmado = true, observacao = :observacao 
                WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        
        // Sanitizar dados
        if ($this->observacao) {
            $this->observacao = htmlspecialchars(strip_tags($this->observacao));
        } else {
            $this->observacao = null;
        }
        
        // Vincular parâmetros
        $stmt->bindParam(":id", $this->id);
        $stmt->bindParam(":observacao", $this->observacao);
        
        return $stmt->execute();
    }
    
    // Listar todos os registros de um medicamento
    public function listarPorMedicamento($medicamento_id) {
        $query = "SELECT r.*, m.nome, m.dosagem 
                FROM " . $this->table_name . " r
                JOIN appestudo.medicamentos m ON r.medicamento_id = m.id
                WHERE r.medicamento_id = :medicamento_id 
                ORDER BY r.data_hora";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":medicamento_id", $medicamento_id);
        $stmt->execute();
        
        return $stmt;
    }
    
    // Obter registros pendentes (não confirmados e passados)
    public function listarPendentes() {
        $query = "SELECT r.*, m.nome, m.dosagem 
                FROM " . $this->table_name . " r
                JOIN appestudo.medicamentos m ON r.medicamento_id = m.id
                WHERE r.confirmado = false 
                AND r.data_hora <= NOW() 
                ORDER BY r.data_hora";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt;
    }
    
    // Limpar todos os registros de um medicamento (usado ao atualizar)
    public function limparPorMedicamento($medicamento_id) {
        $query = "DELETE FROM " . $this->table_name . " 
                WHERE medicamento_id = :medicamento_id 
                AND confirmado = false";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":medicamento_id", $medicamento_id);
        
        return $stmt->execute();
    }
    
    // Obter detalhes de um registro específico
    public function obter($id) {
        $query = "SELECT r.*, m.nome, m.dosagem 
                FROM " . $this->table_name . " r
                JOIN appestudo.medicamentos m ON r.medicamento_id = m.id
                WHERE r.id = :id";
                
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        $stmt->execute();
        
        $row = $stmt->fetch();
        
        if ($row) {
            $this->id = $row['id'];
            $this->medicamento_id = $row['medicamento_id'];
            $this->data_hora = $row['data_hora'];
            $this->confirmado = $row['confirmado'];
            $this->observacao = $row['observacao'];
            
            return $row;
        }
        
        return false;
    }
}
?>