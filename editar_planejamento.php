<!DOCTYPE html>
<html>
<head>
    <title><PERSON><PERSON>alhes do Planejamento</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            margin: 0;
            padding: 0;
        }
        .quadro-cinza {
            background-color: #f5deb3; /* Cor cinza claro */
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #ccc;
            margin: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Sombra para relevo */
            font-family: 'Indie Flower', cursive;
        }
        .quadro-cinza h2 {
            font-family: "Courier Prime", monospace;
            font-weight: bold;
            color: #DEB887; /* Cor do texto do cabeçalho do quadro */
            text-align: center;
            padding: 10px;
            background-color: #2b2723;
            border-radius: 10px;
            margin: -20px -20px 20px -20px; /* Remover o padding adicional */
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Sombra para relevo */
        }
        .quadro-titulo {
            background-color: #f5deb3; /* Cor cinza claro */
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #ccc;
            margin: 0;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Sombra para relevo */
            text-align: center;
        }
        .quadro-titulo h1 {
            font-family: "Courier Prime", monospace;
            font-weight: bold;
            color: #333;
            margin: 0;
        }
    </style>
</head>
<link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
<body>
<div class="quadro-titulo">
    <h1>Seu Planejamento</h1>
</div>

<?php
session_start();
include_once("conexao_POST.php");

if (!$conexao) {
    die("Erro na conexão: " . pg_last_error());
}

if (isset($_SESSION['idusuario'])) {
    $id_usuario = $_SESSION['idusuario'];
    // Consultar os dados do planejamento relacionado ao usuário logado
    $query_consultar_idplanejamento = "SELECT p.idplanejamento
        FROM appEstudo.planejamento p 
        WHERE p.usuario_idusuario = $id_usuario";

    $resultado_idplanejamento = pg_query($conexao, $query_consultar_idplanejamento);

    if ($resultado_idplanejamento) {
        $row = pg_fetch_assoc($resultado_idplanejamento);
        $id_planejamento = $row['idplanejamento'];

        // Agora $id_planejamento contém o valor do idplanejamento
    } else {
        echo "Erro ao executar a consulta.";
    }

    // Consultar os dados do planejamento pelo ID, incluindo o nome do usuário
    $query_consultar_planejamento = "SELECT p.*, u.nome as nome_usuario 
                                     FROM appEstudo.planejamento p 
                                     INNER JOIN appEstudo.usuario u ON p.usuario_idusuario = u.idusuario
                                     WHERE p.idplanejamento = $id_planejamento";
    $resultado_planejamento = pg_query($conexao, $query_consultar_planejamento);

    if (pg_num_rows($resultado_planejamento) > 0) {
        $planejamento = pg_fetch_assoc($resultado_planejamento);

        echo "<div class='quadro-cinza'>";
        echo "<h2>Dados do Planejamento:</h2>";
        echo "<form action='editar_detalhesPlanejamento.php' method='post'>"; // Defina a página de processamento aqui
        echo "<input type='hidden' name='id_planejamento' value='" . $planejamento['idplanejamento'] . "'>";

        echo "<label style='display: block; margin-bottom: 5px;'>Nome do Planejamento: ";
        echo "<input type='text' name='nome' value='" . $planejamento['nome'] . "'></label>";

        echo "<div style='display: flex; align-items: center;'>";
        echo "<label style='margin-right: 10px;'>Data de Início: ";
        echo "<input type='date' name='data_inicio' value='" . $planejamento['data_inicio'] . "'></label>";

        echo "<label style='margin-right: 10px;'>Data de Fim: ";
        echo "<input type='date' name='data_fim' value='" . $planejamento['data_fim'] . "'></label>";

       // echo "<label style='margin-right: 10px;'>Tempo Disponível Diário: ";
       // echo "<input type='time' name='tempo_planejamento' value='" . $planejamento['tempo_planejamento'] . "'></label>";


$tempo_planejamento = empty($planejamento['tempo_planejamento']) ? '00:00' : $planejamento['tempo_planejamento'];
echo "<label style='margin-right: 10px;'>Tempo Disponível Diário (hh:mm): ";
echo "<input type='time' name='tempo_planejamento' value='" . $tempo_planejamento . "' min='00:01' required></label>";



        echo "</div>";

        // Consultar todas as matérias
        $query_consultar_todas_materias = "SELECT * FROM appEstudo.materia";
        $resultado_todas_materias = pg_query($conexao, $query_consultar_todas_materias);
        echo "</div>";

        echo "<div class='quadro-cinza'>";
        echo "<h2>Matérias Que Você Vai Estudar:</h2>";
        if (pg_num_rows($resultado_todas_materias) > 0) {
            while ($materia = pg_fetch_assoc($resultado_todas_materias)) {
                $checked = '';
                // Verificar se a matéria está relacionada ao planejamento
                $query_verificar_relacionamento = "SELECT * FROM appEstudo.planejamento_materia 
                                           WHERE planejamento_idplanejamento = $id_planejamento 
                                           AND materia_idmateria = " . $materia['idmateria'];
                $resultado_relacionamento = pg_query($conexao, $query_verificar_relacionamento);
                if (pg_num_rows($resultado_relacionamento) > 0) {
                    $checked = 'checked';
                }

                echo "<label>";
                echo "<div style='display: inline-block; width: 15px; height: 15px; background-color: " . $materia['cor'] . ";'></div>";
                echo "<input type='checkbox' name='materias[]' value='" . $materia['idmateria'] . "' $checked>";
                echo $materia['nome'];
                echo "</label><br>";
            }
        } else {
            echo "<p>Nenhuma matéria encontrada.</p>";
        }

        echo "<button type='submit'>Salvar Alterações</button>";
        echo "</form>";
        echo "</div>";
    } else {
        echo "<p>Planejamento não encontrado.</p>";
    }
}

pg_close($conexao);
?>

<br>

</body>
<script>
    // Função para recarregar a página "index.php" quando o pop-up for fechado
    window.onunload = function () {
        if (window.opener) {
            window.opener.location.reload();
        }
    };
</script>
</html>
