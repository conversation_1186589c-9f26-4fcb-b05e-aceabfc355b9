/* Sistema de Migração - Estilos */

/* Container principal */
.sistema-migracao {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10000;
    display: none;
    opacity: 0;
    transition: all 0.3s ease;
}

.sistema-migracao.ativo {
    display: flex;
    opacity: 1;
}

.migracao-overlay {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.migracao-container {
    background: white;
    border-radius: 20px;
    width: 100%;
    max-width: 1200px;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    display: flex;
    flex-direction: column;
}

/* Cabeçalho */
.migracao-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 25px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.migracao-header h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
}

.migracao-header h2 i {
    margin-right: 12px;
    color: #3498db;
}

.btn-fechar {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.btn-fechar:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* Conteúdo */
.migracao-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
    background: #f8f9fa;
}

/* Seções */
.migracao-content > div {
    margin-bottom: 30px;
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.migracao-content h3 {
    margin: 0 0 20px 0;
    font-size: 1.3rem;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.migracao-content h3 i {
    color: #3498db;
}

/* Cards de Status */
.status-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.status-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.status-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: #3498db;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.3rem;
}

.status-info {
    flex: 1;
}

.status-info h4 {
    margin: 0 0 5px 0;
    font-size: 1rem;
    color: #2c3e50;
    font-weight: 600;
}

.status-text {
    margin: 0;
    font-size: 0.9rem;
    color: #7f8c8d;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    position: absolute;
    top: 15px;
    right: 15px;
}

.status-indicator.status-ok {
    background: #27ae60;
    box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
}

.status-indicator.status-erro {
    background: #e74c3c;
    box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
}

.status-indicator.status-aviso {
    background: #f39c12;
    box-shadow: 0 0 10px rgba(243, 156, 18, 0.5);
}

/* Detalhes das Tabelas */
.tabelas-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.tabela-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    border-left: 4px solid #3498db;
}

.tabela-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.tabela-header h4 {
    margin: 0;
    font-size: 1.1rem;
    color: #2c3e50;
    text-transform: capitalize;
}

.tabela-percentual {
    background: #3498db;
    color: white;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.8rem;
    font-weight: bold;
}

.tabela-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.stat {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: #7f8c8d;
    margin-bottom: 2px;
}

.stat-valor {
    display: block;
    font-size: 1.1rem;
    font-weight: bold;
    color: #2c3e50;
}

.progresso-barra-mini {
    height: 6px;
    background: #ecf0f1;
    border-radius: 3px;
    overflow: hidden;
}

.progresso-preenchimento-mini {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Botões de Ação */
.acoes-buttons {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn:not(:disabled):hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #2980b9;
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-warning:hover:not(:disabled) {
    background: #e67e22;
}

.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover:not(:disabled) {
    background: #138496;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #218838;
}

/* Log de Execução */
.log-container {
    background: #2c3e50;
    border-radius: 8px;
    padding: 20px;
    max-height: 300px;
    overflow-y: auto;
    font-family: 'Courier New', monospace;
}

.log-empty {
    text-align: center;
    color: #7f8c8d;
    padding: 20px;
}

.log-empty i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
}

.log-linha {
    display: flex;
    gap: 10px;
    margin-bottom: 8px;
    font-size: 0.9rem;
}

.log-timestamp {
    color: #95a5a6;
    min-width: 80px;
}

.log-mensagem {
    flex: 1;
}

.log-info .log-mensagem {
    color: #ecf0f1;
}

.log-sucesso .log-mensagem {
    color: #2ecc71;
}

.log-erro .log-mensagem {
    color: #e74c3c;
}

.log-aviso .log-mensagem {
    color: #f39c12;
}

/* Barra de Progresso */
.progresso-container {
    text-align: center;
}

.progresso-barra {
    height: 8px;
    background: #ecf0f1;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progresso-preenchimento {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    border-radius: 4px;
    transition: width 0.3s ease;
    animation: progresso-pulse 1.5s ease-in-out infinite;
}

@keyframes progresso-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.progresso-texto {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Modal de Relatório */
.relatorio-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10001;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.relatorio-container {
    background: white;
    border-radius: 15px;
    width: 100%;
    max-width: 800px;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.relatorio-header {
    background: #34495e;
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.relatorio-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.btn-fechar-relatorio {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn-fechar-relatorio:hover {
    background: rgba(255, 255, 255, 0.1);
}

.relatorio-content {
    flex: 1;
    padding: 25px;
    overflow-y: auto;
}

.relatorio-section {
    margin-bottom: 25px;
}

.relatorio-section h4 {
    margin: 0 0 15px 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.leis-lista {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.lei-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
}

.lei-codigo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 0.9rem;
}

.lei-nome {
    flex: 1;
    font-weight: 600;
    color: #2c3e50;
}

.lei-artigos {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.relatorio-tabela {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.relatorio-tabela th,
.relatorio-tabela td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ecf0f1;
}

.relatorio-tabela th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.relatorio-tabela td {
    color: #34495e;
}

/* Responsividade */
@media (max-width: 768px) {
    .migracao-overlay {
        padding: 10px;
    }
    
    .migracao-container {
        max-height: 95vh;
    }
    
    .migracao-header {
        padding: 20px;
    }
    
    .migracao-content {
        padding: 20px;
    }
    
    .status-cards {
        grid-template-columns: 1fr;
    }
    
    .tabelas-grid {
        grid-template-columns: 1fr;
    }
    
    .acoes-buttons {
        flex-direction: column;
    }
    
    .btn {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .migracao-header h2 {
        font-size: 1.4rem;
    }
    
    .status-card {
        padding: 15px;
    }
    
    .tabela-card {
        padding: 15px;
    }
    
    .tabela-stats {
        flex-direction: column;
        gap: 10px;
    }
    
    .stat {
        text-align: left;
    }
}
