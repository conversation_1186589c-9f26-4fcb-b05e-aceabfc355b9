<?php
// cadastro_edital.php
session_start();
include_once("assets/config.php");

// Verifica se é o usuário admin (ID 1)
if (!isset($_SESSION['idusuario']) || $_SESSION['idusuario'] != 1) {
    header("Location: /login_index.php");
    exit();
}

// Processamento do formulário
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nome = $_POST['nome'];
    $descricao = $_POST['descricao'];
    $ano = $_POST['ano'];
    $orgao = $_POST['orgao'];

    $query = "INSERT INTO appestudo.edital (nome, descricao, ano, orgao) 
              VALUES ($1, $2, $3, $4) RETURNING id_edital";

    $result = pg_query_params($conexao, $query, array($nome, $descricao, $ano, $orgao));

    if ($result) {
        $mensagem = "Edital cadastrado com sucesso!";
        $tipo = "success";
    } else {
        $mensagem = "Erro ao cadastrar edital: " . pg_last_error($conexao);
        $tipo = "error";
    }
}

// Buscar editais existentes
$query_editais = "SELECT * FROM appestudo.edital ORDER BY ano DESC, nome";
$result_editais = pg_query($conexao, $query_editais);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cadastro de Editais</title>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Old+Standard+TT:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #B85C5C;
            --paper-color: #EDE3D0;
            --secondary-color: #D2691E;
            --text-color: #2C1810;
            --border-color: #8B4513;
        }

        body {
            font-family: 'Crimson Text', serif;
            background-color: var(--paper-color);
            color: var(--text-color);
            padding: 40px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            text-align: center;
            margin-bottom: 40px;
        }

        .form-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 40px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-family: 'Old Standard TT', serif;
            color: var(--primary-color);
        }

        input[type="text"],
        input[type="number"],
        textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-family: 'Crimson Text', serif;
        }

        button {
            background: var(--primary-color);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-family: 'Old Standard TT', serif;
            transition: all 0.3s ease;
        }

        button:hover {
            background: var(--secondary-color);
        }

        .mensagem {
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .editais-lista {
            margin-top: 40px;
        }

        .edital-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .edital-card h3 {
            color: var(--primary-color);
            margin: 0 0 10px 0;
        }

        .edital-meta {
            color: var(--secondary-color);
            font-style: italic;
            margin-bottom: 10px;
        }

        .btn-voltar {
            position: fixed;
            top: 20px;
            left: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: white;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            color: var(--primary-color);
            text-decoration: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .btn-voltar:hover {
            background: var(--primary-color);
            color: white;
        }
    </style>
</head>
<body>
<a href="/" class="btn-voltar">
    <i class="fas fa-arrow-left"></i>
</a>

<div class="container">
    <h1>Cadastro de Editais</h1>

    <?php if (isset($mensagem)): ?>
        <div class="mensagem <?php echo $tipo; ?>">
            <?php echo $mensagem; ?>
        </div>
    <?php endif; ?>

    <div class="form-container">
        <form method="POST">
            <div class="form-group">
                <label for="nome">Nome do Edital</label>
                <input type="text" id="nome" name="nome" required>
            </div>

            <div class="form-group">
                <label for="descricao">Descrição</label>
                <textarea id="descricao" name="descricao" rows="4"></textarea>
            </div>

            <div class="form-group">
                <label for="ano">Ano</label>
                <input type="number" id="ano" name="ano" required min="2000" max="2100">
            </div>

            <div class="form-group">
                <label for="orgao">Órgão</label>
                <input type="text" id="orgao" name="orgao" required>
            </div>

            <button type="submit">Cadastrar Edital</button>
        </form>
    </div>

    <div class="editais-lista">
        <h2>Editais Cadastrados</h2>
        <?php while ($edital = pg_fetch_assoc($result_editais)): ?>
            <div class="edital-card">
                <h3><?php echo htmlspecialchars($edital['nome']); ?></h3>
                <div class="edital-meta">
                    <?php echo htmlspecialchars($edital['orgao']); ?> -
                    <?php echo htmlspecialchars($edital['ano']); ?>
                </div>
                <p><?php echo htmlspecialchars($edital['descricao']); ?></p>
            </div>
        <?php endwhile; ?>
    </div>
</div>
</body>
</html>