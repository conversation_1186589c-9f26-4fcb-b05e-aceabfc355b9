<?php
session_start();
if (!isset($_SESSION['idusuario']) || $_SESSION['idusuario'] != 1) {
    header("Location: login.php");
    exit();
}

include_once("assets/config.php");

$usuario_id = $_SESSION['idusuario'];

// Consulta para obter o progresso de estudo por matéria
$query = "
    SELECT 
        m.idmateria,
        m.nome AS materia_nome,
        m.cor,
        uc.status_revisao,
        COUNT(*) AS total
    FROM 
        appestudo.usuario_conteudo AS uc
    JOIN 
        appestudo.conteudo_edital AS ce ON uc.conteudo_id = ce.id_conteudo
    JOIN 
        appestudo.materia AS m ON ce.materia_id = m.idmateria
    WHERE 
        uc.usuario_id = $usuario_id
    GROUP BY 
        m.idmateria, m.nome, m.cor, uc.status_revisao
    ORDER BY 
        m.nome;
";

$result = pg_query($conexao, $query);
if (!$result) {
    die("Erro ao buscar o progresso por matéria.");
}

// Organizar os dados para exibição agrupada por matéria
$progresso_por_materia = [];
while ($row = pg_fetch_assoc($result)) {
    $materia_id = $row['idmateria'];
    if (!isset($progresso_por_materia[$materia_id])) {
        $progresso_por_materia[$materia_id] = [
            'nome' => $row['materia_nome'],
            'cor' => $row['cor'],
            'status' => [
                'Não Iniciado' => 0,
                'Em Andamento' => 0,
                'Concluído' => 0
            ]
        ];
    }
    $progresso_por_materia[$materia_id]['status'][$row['status_revisao']] = $row['total'];
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Progresso de Revisão por Matéria</title>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier Prime', monospace;
            min-height: 100vh;
            background: linear-gradient(135deg, #8B0000, #B22222);
            padding: 40px 20px;
            line-height: 1.6;
            color: #2c1810;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .btn-voltar {
            position: fixed;
            top: 20px;
            left: 20px;
            background: linear-gradient(145deg, #8b4513, #a0522d);
            color: #fff;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            text-decoration: none;
            z-index: 1000;
        }

        .btn-voltar:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            background: linear-gradient(145deg, #a0522d, #8b4513);
        }

        .header-vintage {
            text-align: center;
            color: #fff;
            margin-bottom: 40px;
        }

        .header-vintage h2 {
            font-family: 'Cinzel', serif;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 2px;
            margin-bottom: 10px;
        }

        .materias-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .materia {
            background: #fff;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .materia:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .materia:last-child {
            margin-bottom: 0;
        }

        .materia-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid;
        }

        .materia-icon {
            width: 50px;
            height: 50px;
            min-width: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            color: #fff;
            font-size: 1.4rem;
            transition: transform 0.3s ease;
        }

        .materia:hover .materia-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .materia-nome {
            font-family: 'Cinzel', serif;
            font-size: 1.3rem;
            color: #2c1810;
            font-weight: 600;
            flex-grow: 1;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .progress-label {
            font-family: 'Cinzel', serif;
            color: #2c1810;
            font-size: 1rem;
        }

        .progress-percentage {
            font-family: 'Courier Prime', monospace;
            font-weight: bold;
            font-size: 1.2rem;
            color: #8B4513;
        }

        .progress-bar {
            height: 10px;
            background: #f4f1ea;
            border-radius: 5px;
            overflow: hidden;
            margin-bottom: 20px;
        }

        .progress-fill {
            height: 100%;
            border-radius: 5px;
            transition: width 1s ease-in-out;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
        }

        .status-card {
            background: #fff;
            padding: 20px 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e8e0d8;
            transition: all 0.3s ease;
        }

        .status-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .status-icon {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .status-number {
            font-size: 1.8rem;
            font-weight: bold;
            margin: 5px 0;
        }

        .status-label {
            font-size: 0.9rem;
            color: #666;
            white-space: nowrap;
        }

        /* Status Cards */
        .nao-iniciado {
            border-left: 4px solid #d32f2f;
        }

        .nao-iniciado .status-icon {
            color: #d32f2f;
        }

        .em-andamento {
            border-left: 4px solid #f57c00;
        }

        .em-andamento .status-icon {
            color: #f57c00;
        }

        .concluido {
            border-left: 4px solid #388e3c;
        }

        .concluido .status-icon {
            color: #388e3c;
        }

        @media (max-width: 768px) {
            body {
                padding: 15px;
            }

            .btn-voltar {
                top: 10px;
                left: 10px;
                width: 40px;
                height: 40px;
            }

            .header-vintage h2 {
                font-size: 2rem;
            }

            .materias-container {
                padding: 20px;
            }

            .materia {
                padding: 20px;
            }

            .status-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .status-card {
                padding: 15px;
            }

            .materia-icon {
                width: 40px;
                height: 40px;
                min-width: 40px;
                font-size: 1.2rem;
            }

            .materia-nome {
                font-size: 1.1rem;
            }

            .progress-label,
            .progress-percentage {
                font-size: 0.9rem;
            }

            .status-number {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
<a href="https://concurseirooff.com.br/edital_verticalizado/index.php" class="btn-voltar">
    <i class="fas fa-arrow-left"></i>
</a>
<div class="container">
    <div class="header-vintage">
        <h2>PProgresso de Revisão por Matéria</h2>
    </div>

    <div class="materias-container">
        <?php foreach ($progresso_por_materia as $materia): ?>
            <?php
            $total = $materia['status']['Não Iniciado'] +
                $materia['status']['Em Andamento'] +
                $materia['status']['Concluído'];

            $percentual_concluido = ($total > 0) ?
                ($materia['status']['Concluído'] / $total) * 100 : 0;
            ?>
            <div class="materia">
                <div class="materia-header" style="border-color: <?= htmlspecialchars($materia['cor']) ?>">
                    <div class="materia-icon" style="background-color: <?= htmlspecialchars($materia['cor']) ?>">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="materia-nome"><?= htmlspecialchars($materia['nome']) ?></div>
                </div>

                <div class="materia-content">
                    <div class="progress-info">
                        <span class="progress-label">Progresso Total</span>
                        <span class="progress-percentage"><?= number_format($percentual_concluido, 1) ?>%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill"
                             style="width: <?= $percentual_concluido ?>%;
                                     background-color: <?= htmlspecialchars($materia['cor']) ?>">
                        </div>
                    </div>

                    <div class="status-grid">
                        <div class="status-card nao-iniciado">
                            <div class="status-icon">
                                <i class="fas fa-hourglass-start"></i>
                            </div>
                            <div class="status-number"><?= $materia['status']['Não Iniciado'] ?></div>
                            <div class="status-label">Não Iniciado</div>
                        </div>

                        <div class="status-card em-andamento">
                            <div class="status-icon">
                                <i class="fas fa-sync-alt"></i>
                            </div>
                            <div class="status-number"><?= $materia['status']['Em Andamento'] ?></div>
                            <div class="status-label">Em Andamento</div>
                        </div>

                        <div class="status-card concluido">
                            <div class="status-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="status-number"><?= $materia['status']['Concluído'] ?></div>
                            <div class="status-label">Concluído</div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>
</body>
</html>