<?php
session_start();
if (!isset($_SESSION['idusuario']) || $_SESSION['idusuario'] != 1) {
    header("Location: login.php");
    exit();
}

include_once("assets/config.php");

$usuario_id = $_SESSION['idusuario'];

// Consulta para obter o resumo do progresso geral
$query = "
    SELECT 
        status_revisao,
        COUNT(*) AS total
    FROM 
        appestudo.usuario_conteudo
    WHERE 
        usuario_id = $usuario_id
    GROUP BY 
        status_revisao;
";

$result = pg_query($conexao, $query);
if (!$result) {
    die("Erro ao buscar o resumo do progresso.");
}

// Inicializa as variáveis de contagem
$total_nao_iniciado = 0;
$total_em_andamento = 0;
$total_concluido = 0;

while ($row = pg_fetch_assoc($result)) {
    switch ($row['status_revisao']) {
        case 'Não Iniciado':
            $total_nao_iniciado = $row['total'];
            break;
        case 'Em Andamento':
            $total_em_andamento = $row['total'];
            break;
        case 'Concluído':
            $total_concluido = $row['total'];
            break;
    }
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resumo do Progresso de Estudo</title>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier Prime', monospace;
            background: #f4f1ea;
            color: #2c1810;
            padding: 20px;
            line-height: 1.6;
            background-image: linear-gradient(rgba(244, 241, 234, 0.9), rgba(244, 241, 234, 0.9)),
            url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23d3c5b8' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header-vintage {
            background: linear-gradient(to right, #8B0000, #B22222);
            padding: 30px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 30px;
            position: relative;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .header-vintage h2 {
            font-family: 'Cinzel', serif;
            font-size: 28px;
            color: #fff;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 1.5px;
        }

        .header-vintage::after {
            content: '';
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 2px;
            background: rgba(255,255,255,0.5);
        }

        .progress-container {
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .progress-overview {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: #fdfbf7;
            border-radius: 8px;
            border: 1px solid #d3c5b8;
        }

        .total-items {
            font-family: 'Cinzel', serif;
            font-size: 2.5rem;
            color: #8B4513;
            margin-bottom: 10px;
        }

        .progress-label {
            font-size: 1.1rem;
            color: #666;
        }

        .progress-bar-container {
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-bar {
            height: 100%;
            background: linear-gradient(to right, #8B0000, #B22222);
            transition: width 0.3s ease;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .status-card {
            padding: 20px;
            border-radius: 8px;
            background: #fff;
            border: 1px solid #d3c5b8;
            transition: transform 0.3s ease;
            cursor: pointer;
        }

        .status-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .status-icon {
            font-size: 2rem;
            margin-bottom: 15px;
        }

        .status-number {
            font-size: 2rem;
            font-weight: bold;
            margin: 10px 0;
        }

        .status-label {
            font-family: 'Cinzel', serif;
            font-size: 1.1rem;
        }

        .nao-iniciado {
            border-left: 4px solid #d32f2f;
        }

        .nao-iniciado .status-icon {
            color: #d32f2f;
        }

        .em-andamento {
            border-left: 4px solid #f57c00;
        }

        .em-andamento .status-icon {
            color: #f57c00;
        }

        .concluido {
            border-left: 4px solid #388e3c;
        }

        .concluido .status-icon {
            color: #388e3c;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .header-vintage {
                padding: 20px;
            }

            .progress-container {
                padding: 20px;
            }

            .status-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header-vintage">
        <h2>Resumo do Progresso de Estudo</h2>
    </div>

    <div class="progress-container">
        <?php
        $total_items = $total_nao_iniciado + $total_em_andamento + $total_concluido;
        $progress_percentage = $total_items > 0 ? ($total_concluido / $total_items) * 100 : 0;
        ?>

        <div class="progress-overview">
            <div class="total-items"><?= $total_items ?></div>
            <div class="progress-label">Total de Itens para Estudo</div>
            <div class="progress-bar-container">
                <div class="progress-bar" style="width: <?= $progress_percentage ?>%"></div>
            </div>
            <div class="progress-label">
                <?= number_format($progress_percentage, 1) ?>% Concluído
            </div>
        </div>

        <div class="status-grid">
            <div class="status-card nao-iniciado">
                <div class="status-icon">
                    <i class="fas fa-hourglass-start"></i>
                </div>
                <div class="status-number"><?= $total_nao_iniciado ?></div>
                <div class="status-label">Não Iniciado</div>
            </div>

            <div class="status-card em-andamento">
                <div class="status-icon">
                    <i class="fas fa-sync-alt"></i>
                </div>
                <div class="status-number"><?= $total_em_andamento ?></div>
                <div class="status-label">Em Andamento</div>
            </div>

            <div class="status-card concluido">
                <div class="status-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="status-number"><?= $total_concluido ?></div>
                <div class="status-label">Concluído</div>
            </div>
        </div>
    </div>
</div>
</body>
</html>