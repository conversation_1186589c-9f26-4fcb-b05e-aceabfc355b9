<?php
// =========================================================================
// Conexão com o Banco de Dados PostgreSQL
// Utiliza o arquivo de conexão fornecido pelo usuário
// =========================================================================
require_once('../conexao_POST.php');

// =========================================================================
// Lógica do Backend para Gerenciar Blocos de Rotina e Modelos
// =========================================================================

// Função auxiliar para converter cor hex para RGB
function hexToRgb($hex) {
    list($r, $g, $b) = sscanf($hex, "#%02x%02x%02x");
    return "$r, $g, $b";
}

// =========================================================================
// Funções para gerenciar a tabela principal de tarefas (appestudo.tasks)
// =========================================================================

// Função para adicionar um novo bloco de rotina ao banco de dados
function addRoutineBlock($pdo, $description, $start_time, $end_time, $task_date, $color) {
    try {
        $category = 'Rotina'; 
        $sql = "INSERT INTO appestudo.tasks (description, category, start_time, end_time, task_date, color) VALUES (?, ?, ?, ?, ?, ?)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$description, $category, $start_time, $end_time, $task_date, $color]);
    } catch (PDOException $e) {
        echo "<div style='color:red;padding:10px;'>Erro ao adicionar bloco de rotina: " . $e->getMessage() . "</div>";
    }
}

// Função para editar um bloco de rotina existente
function editRoutineBlock($pdo, $id, $description, $start_time, $end_time, $task_date, $color) {
    try {
        $sql = "UPDATE appestudo.tasks SET description = ?, start_time = ?, end_time = ?, task_date = ?, color = ? WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$description, $start_time, $end_time, $task_date, $color, $id]);
    } catch (PDOException $e) {
        echo "<div style='color:red;padding:10px;'>Erro ao editar bloco de rotina: " . $e->getMessage() . "</div>";
    }
}

// Função para deletar um bloco de rotina
function deleteRoutineBlock($pdo, $id) {
    try {
        $sql = "DELETE FROM appestudo.tasks WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$id]);
    } catch (PDOException $e) {
        echo "<div style='color:red;padding:10px;'>Erro ao deletar bloco: " . $e->getMessage() . "</div>";
    }
}

// =========================================================================
// Funções para gerenciar a nova tabela de modelos (appestudo.routine_templates)
// =========================================================================

// Função para adicionar um novo modelo de rotina
function addRoutineTemplate($pdo, $description, $start_time, $end_time, $color) {
    try {
        $sql = "INSERT INTO appestudo.routine_templates (description, start_time, end_time, color) VALUES (?, ?, ?, ?)";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$description, $start_time, $end_time, $color]);
    } catch (PDOException $e) {
        echo "<div style='color:red;padding:10px;'>Erro ao adicionar modelo: " . $e->getMessage() . "</div>";
    }
}

// Função para editar um modelo de rotina existente
function editRoutineTemplate($pdo, $id, $description, $start_time, $end_time, $color) {
    try {
        $sql = "UPDATE appestudo.routine_templates SET description = ?, start_time = ?, end_time = ?, color = ? WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$description, $start_time, $end_time, $color, $id]);
    } catch (PDOException $e) {
        echo "<div style='color:red;padding:10px;'>Erro ao editar modelo: " . $e->getMessage() . "</div>";
    }
}

// Função para aplicar um modelo de rotina a uma data específica
function applyTemplate($pdo, $template_id, $task_date) {
    try {
        // Primeiro, busca o modelo para obter seus dados
        $sql_select = "SELECT description, start_time, end_time, color FROM appestudo.routine_templates WHERE id = ?";
        $stmt_select = $pdo->prepare($sql_select);
        $stmt_select->execute([$template_id]);
        $template = $stmt_select->fetch(PDO::FETCH_ASSOC);

        if ($template) {
            // Se o modelo for encontrado, o adiciona como uma tarefa
            addRoutineBlock($pdo, $template['description'], $template['start_time'], $template['end_time'], $task_date, $template['color']);
        }
    } catch (PDOException $e) {
        echo "<div style='color:red;padding:10px;'>Erro ao aplicar modelo: " . $e->getMessage() . "</div>";
    }
}

// Função para deletar um modelo de rotina
function deleteRoutineTemplate($pdo, $id) {
    try {
        $sql = "DELETE FROM appestudo.routine_templates WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$id]);
    } catch (PDOException $e) {
        echo "<div style='color:red;padding:10px;'>Erro ao deletar modelo: " . $e->getMessage() . "</div>";
    }
}

// =========================================================================
// Processamento de requisições GET e POST
// =========================================================================

$week_start_date_param = isset($_POST['week_start']) ? $_POST['week_start'] : (isset($_GET['week_start']) ? $_GET['week_start'] : null);
$current_week_start = $week_start_date_param ? $week_start_date_param : (new DateTime('this week'))->format('Y-m-d');


// Unifica a lógica de edição para blocos e modelos
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['save_edit'])) {
    $id = $_POST['id'];
    $description = htmlspecialchars($_POST['description']);
    $start_time = $_POST['start_time'];
    $end_time = $_POST['end_time'];
    $color = $_POST['color'];

    if ($_POST['type'] === 'block') {
        $task_date = $_POST['task_date'];
        editRoutineBlock($pdo, $id, $description, $start_time, $end_time, $task_date, $color);
    } elseif ($_POST['type'] === 'template') {
        editRoutineTemplate($pdo, $id, $description, $start_time, $end_time, $color);
    }
    header("Location: index.php?week_start=" . $current_week_start);
    exit();
}

// Adicionar um novo bloco de rotina (do formulário principal)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_block'])) {
    $description = htmlspecialchars($_POST['description']);
    $start_time = $_POST['start_time'];
    $end_time = $_POST['end_time'];
    $task_date = $_POST['task_date'];
    $color = $_POST['block_color'];
    addRoutineBlock($pdo, $description, $start_time, $end_time, $task_date, $color);
    header("Location: index.php?week_start=" . $current_week_start);
    exit();
}

// Deletar um bloco de rotina
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_block_id'])) {
    deleteRoutineBlock($pdo, $_POST['delete_block_id']);
    header("Location: index.php?week_start=" . $current_week_start);
    exit();
}

// Adicionar um novo modelo de rotina
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_template'])) {
    $description = htmlspecialchars($_POST['description']);
    $start_time = $_POST['start_time'];
    $end_time = $_POST['end_time'];
    $color = $_POST['template_color'];
    addRoutineTemplate($pdo, $description, $start_time, $end_time, $color);
    header("Location: index.php?week_start=" . $current_week_start);
    exit();
}

// Deletar um modelo de rotina
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['delete_template_id'])) {
    deleteRoutineTemplate($pdo, $_POST['delete_template_id']);
    header("Location: index.php?week_start=" . $current_week_start);
    exit();
}

// Aplicar um modelo de rotina a uma data específica
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['apply_template_id']) && isset($_POST['apply_date'])) {
    applyTemplate($pdo, $_POST['apply_template_id'], $_POST['apply_date']);
    header("Location: index.php?week_start=" . $current_week_start);
    exit();
}

// Aplicar um modelo de rotina para a semana inteira
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['apply_template_to_week_id'])) {
    $template_id = $_POST['apply_template_to_week_id'];
    $week_start = new DateTime($_POST['week_start']);
    for ($i = 0; $i < 7; $i++) {
        $date_to_apply = clone $week_start;
        $date_to_apply->modify("+$i days");
        applyTemplate($pdo, $template_id, $date_to_apply->format('Y-m-d'));
    }
    header("Location: index.php?week_start=" . $current_week_start);
    exit();
}

// =========================================================================
// Lógica para a visualização semanal
// =========================================================================

// Determina a data de início da semana (segunda-feira), priorizando POST para URLs limpas
if ($week_start_date_param) {
    $week_start_date = new DateTime($week_start_date_param);
} else {
    $week_start_date = new DateTime('this week');
}

$week_end_date = clone $week_start_date;
$week_end_date->modify('+6 days');

// Busca todos os blocos de rotina para a semana selecionada
try {
    $sql = "SELECT * FROM appestudo.tasks WHERE task_date BETWEEN ? AND ? ORDER BY task_date, start_time";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$week_start_date->format('Y-m-d'), $week_end_date->format('Y-m-d')]);
    $routine_blocks = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $routine_blocks = []; 
    echo "<div style='color:red;padding:10px;'>Erro ao buscar blocos de rotina: " . $e->getMessage() . "</div>";
}

// Busca todos os modelos de rotina
try {
    $sql = "SELECT * FROM appestudo.routine_templates ORDER BY start_time";
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $routine_templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $routine_templates = [];
    echo "<div style='color:red;padding:10px;'>Erro ao buscar modelos de rotina: " . $e->getMessage() . "</div>";
}

// Organiza os blocos por dia da semana para facilitar a exibição
$blocks_by_day = [];
$current_day = clone $week_start_date;
for ($i = 0; $i < 7; $i++) {
    $date_str = $current_day->format('Y-m-d');
    $blocks_by_day[$date_str] = array_filter($routine_blocks, function($block) use ($date_str) {
        return $block['task_date'] === $date_str;
    });
    $current_day->modify('+1 day');
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minha Rotina Semanal</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --secondary-color: #10b981;
            --secondary-dark: #059669;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: var(--gray-800);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            line-height: 1.6;
        }

        .container {
            max-width: 1600px;
            width: 100%;
            margin: auto;
            background-color: #fff;
            padding: 40px;
            border-radius: 24px;
            box-shadow: var(--shadow-xl);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color), var(--warning-color));
        }

        h1 {
            color: var(--gray-800);
            text-align: center;
            font-size: 3em;
            margin-bottom: 30px;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            position: relative;
        }

        h1::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            border-radius: 2px;
        }

        .navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            padding: 20px;
            background: var(--gray-50);
            border-radius: 16px;
            border: 1px solid var(--gray-200);
        }

        .navigation form {
            display: inline;
        }

        .navigation button {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: #fff;
            font-weight: 600;
            padding: 14px 24px;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: var(--shadow-md);
            font-size: 14px;
            position: relative;
            overflow: hidden;
        }

        .navigation button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .navigation button:hover::before {
            left: 100%;
        }

        .navigation button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .week-display {
            font-size: 2em;
            font-weight: 700;
            color: var(--gray-800);
            text-align: center;
            padding: 12px 24px;
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.1), rgba(16, 185, 129, 0.1));
            border-radius: 12px;
            border: 2px solid var(--gray-200);
        }
        .week-grid {
            display: grid;
            grid-template-columns: repeat(7, minmax(220px, 1fr));
            gap: 16px;
            margin-bottom: 50px;
            overflow-x: auto;
        }

        .day-column {
            background: linear-gradient(145deg, #ffffff, #f8fafc);
            border-radius: 16px;
            padding: 24px;
            min-height: 300px;
            display: flex;
            flex-direction: column;
            border: 1px solid var(--gray-200);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .day-column::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .day-column:hover::before {
            opacity: 1;
        }

        .day-column:hover {
            box-shadow: var(--shadow-lg);
            transform: translateY(-4px);
            border-color: var(--primary-color);
        }

        .day-column h3 {
            text-align: center;
            margin-top: 0;
            color: var(--gray-700);
            font-size: 1.3em;
            font-weight: 700;
            margin-bottom: 20px;
            position: relative;
        }

        .day-column small {
            font-size: 0.85em;
            color: var(--gray-500);
            font-weight: 500;
        }

        .routine-item {
            padding: 16px;
            margin-bottom: 12px;
            border-radius: 12px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            min-height: 70px;
        }

        .routine-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: inherit;
            border-radius: 0 12px 12px 0;
        }

        .routine-item:hover {
            transform: translateX(4px);
            box-shadow: var(--shadow-md);
        }

        .routine-description {
            font-weight: 600;
            font-size: 1em;
            color: var(--gray-800);
            word-break: keep-all;
            overflow-wrap: normal;
            line-height: 1.4;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .routine-time {
            font-size: 0.85em;
            color: var(--gray-600);
            margin-top: 4px;
            font-weight: 500;
        }

        .routine-content {
            flex: 1;
            margin-right: 12px;
            min-width: 0;
        }

        .routine-actions {
            display: flex;
            gap: 8px;
            flex-shrink: 0;
            align-self: flex-start;
            margin-top: 4px;
        }

        .routine-actions button, .routine-actions a {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid var(--gray-200);
            cursor: pointer;
            font-size: 0.9em;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            padding: 8px;
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }

        .routine-actions .delete-btn {
            color: var(--danger-color);
        }

        .routine-actions .edit-btn {
            color: var(--primary-color);
        }

        .routine-actions button:hover, .routine-actions a:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-sm);
        }

        .routine-actions .delete-btn:hover {
            background: rgba(239, 68, 68, 0.1);
            border-color: var(--danger-color);
        }

        .routine-actions .edit-btn:hover {
            background: rgba(99, 102, 241, 0.1);
            border-color: var(--primary-color);
        }

        .empty-day {
            font-size: 1em;
            color: var(--gray-400);
            text-align: center;
            padding-top: 60px;
            font-style: italic;
            position: relative;
        }

        .empty-day::before {
            content: '📅';
            display: block;
            font-size: 2em;
            margin-bottom: 10px;
            opacity: 0.5;
        }
        .form-section {
            display: flex;
            flex-direction: column;
            gap: 24px;
            padding: 32px;
            background: linear-gradient(145deg, var(--gray-50), #ffffff);
            border-radius: 20px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            position: relative;
            overflow: hidden;
        }

        .form-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .form-section h2 {
            margin: 0;
            color: var(--gray-800);
            font-size: 1.75em;
            font-weight: 700;
            position: relative;
            padding-left: 20px;
        }

        .form-section h2::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 24px;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border-radius: 2px;
        }

        .add-block-form, .add-template-form {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            align-items: center;
            padding: 20px;
            background: rgba(255, 255, 255, 0.7);
            border-radius: 16px;
            border: 1px solid var(--gray-200);
        }

        .add-block-form input, .add-template-form input, .add-block-form button, .add-template-form button {
            padding: 14px 16px;
            border-radius: 12px;
            border: 2px solid var(--gray-200);
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: #ffffff;
        }

        input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            transform: translateY(-1px);
        }

        .add-block-form input[type="text"], .add-template-form input[type="text"] {
            flex-grow: 1;
            min-width: 200px;
        }

        .add-block-form button, .add-template-form button {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: #fff;
            font-weight: 600;
            cursor: pointer;
            border: none;
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
            min-width: 140px;
        }

        .add-block-form button::before, .add-template-form button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .add-block-form button:hover::before, .add-template-form button:hover::before {
            left: 100%;
        }

        .add-block-form button:hover, .add-template-form button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }
        input[type="color"] {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            width: 50px;
            height: 50px;
            background: none;
            border: 2px solid var(--gray-200);
            padding: 0;
            cursor: pointer;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        input[type="color"]:hover {
            border-color: var(--primary-color);
            transform: scale(1.05);
        }

        input[type="color"]::-webkit-color-swatch-wrapper {
            padding: 4px;
        }

        input[type="color"]::-webkit-color-swatch {
            border: none;
            border-radius: 8px;
        }

        input[type="color"]::-moz-color-swatch {
            border: none;
            border-radius: 8px;
        }

        .templates-section {
            margin-top: 50px;
        }

        .templates-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 30px;
        }

        .template-card {
            background: linear-gradient(145deg, #ffffff, var(--gray-50));
            padding: 28px;
            border-radius: 20px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--gray-200);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .template-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        .template-card:hover {
            transform: translateY(-6px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-color);
        }
        .template-card .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .template-card h4 {
            margin: 0;
            color: var(--gray-800);
            font-size: 1.25em;
            font-weight: 700;
            line-height: 1.3;
        }

        .template-card .delete-template-btn {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
            color: var(--danger-color);
            text-decoration: none;
            font-size: 0.85em;
            font-weight: 600;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 8px;
        }

        .template-card .delete-template-btn:hover {
            background: rgba(239, 68, 68, 0.2);
            border-color: var(--danger-color);
            transform: scale(1.05);
        }

        .template-card .card-body {
            font-size: 1em;
            color: var(--gray-600);
            margin-top: 12px;
            font-weight: 500;
            padding: 12px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 12px;
            border: 1px solid var(--gray-200);
        }

        .template-card .card-actions {
            margin-top: 20px;
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            flex-wrap: wrap;
        }

        .template-card .apply-btn, .template-card .apply-week-btn {
            background: linear-gradient(135deg, var(--secondary-color), var(--secondary-dark));
            color: #fff;
            border: none;
            padding: 12px 16px;
            border-radius: 10px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9em;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
        }

        .template-card .apply-btn::before, .template-card .apply-week-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .template-card .apply-btn:hover::before, .template-card .apply-week-btn:hover::before {
            left: 100%;
        }

        .template-card .apply-btn:hover, .template-card .apply-week-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .template-card .apply-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.6);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            padding-top: 60px;
        }

        .modal-content {
            background: linear-gradient(145deg, #ffffff, var(--gray-50));
            margin: 5% auto;
            padding: 32px;
            border: 1px solid var(--gray-200);
            width: 90%;
            max-width: 600px;
            border-radius: 24px;
            box-shadow: var(--shadow-xl);
            animation-name: animatetop;
            animation-duration: 0.4s;
            position: relative;
            overflow: hidden;
        }

        .modal-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        @keyframes animatetop {
            from {
                transform: translateY(-50px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .close {
            color: var(--gray-400);
            float: right;
            font-size: 32px;
            font-weight: bold;
            transition: all 0.3s ease;
            cursor: pointer;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
        }

        .close:hover,
        .close:focus {
            color: var(--danger-color);
            background: rgba(239, 68, 68, 0.1);
            transform: scale(1.1);
        }

        .edit-form-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 20px;
        }

        .edit-form-container label {
            font-weight: 600;
            color: var(--gray-700);
            margin-bottom: 5px;
            display: block;
        }

        .edit-form-container input, .edit-form-container button {
            padding: 14px 16px;
            border-radius: 12px;
            border: 2px solid var(--gray-200);
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .edit-form-container input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
        }

        .edit-form-container button {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            color: #fff;
            font-weight: 600;
            cursor: pointer;
            border: none;
            box-shadow: var(--shadow-md);
            position: relative;
            overflow: hidden;
        }

        .edit-form-container button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .edit-form-container button:hover::before {
            left: 100%;
        }

        .edit-form-container button:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .edit-form-container button.cancel-btn {
            background: linear-gradient(135deg, var(--gray-500), var(--gray-600));
        }

        .edit-icon-btn {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid var(--gray-200);
            cursor: pointer;
            padding: 8px;
            color: var(--primary-color);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 8px;
            backdrop-filter: blur(10px);
        }

        .edit-icon-btn:hover {
            color: var(--primary-dark);
            background: rgba(99, 102, 241, 0.1);
            border-color: var(--primary-color);
            transform: scale(1.1);
        }

        /* Responsividade */
        @media (max-width: 1024px) {
            .container {
                padding: 30px;
                margin: 10px;
            }

            .week-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
            }

            .templates-grid {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                padding: 20px;
                border-radius: 16px;
            }

            h1 {
                font-size: 2.2em;
            }

            .navigation {
                flex-direction: column;
                gap: 16px;
                padding: 16px;
            }

            .navigation button {
                width: 100%;
                padding: 16px;
            }

            .week-display {
                font-size: 1.5em;
                order: -1;
            }

            .week-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .day-column {
                margin-bottom: 16px;
                padding: 20px;
            }

            .add-block-form, .add-template-form {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }

            .add-block-form input, .add-template-form input,
            .add-block-form button, .add-template-form button {
                width: 100%;
                min-width: unset;
            }

            .templates-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }

            .template-card .card-actions {
                flex-direction: column;
                gap: 8px;
            }

            .template-card .apply-btn, .template-card .apply-week-btn {
                width: 100%;
                text-align: center;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
                padding: 24px;
            }

            .routine-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
                min-height: auto;
                padding: 16px;
            }

            .routine-content {
                margin-right: 0;
                width: 100%;
            }

            .routine-description {
                white-space: normal;
                overflow: visible;
                text-overflow: unset;
            }

            .routine-actions {
                align-self: flex-end;
                margin-top: 8px;
            }
        }

        @media (max-width: 480px) {
            h1 {
                font-size: 1.8em;
            }

            .form-section {
                padding: 20px;
            }

            .form-section h2 {
                font-size: 1.4em;
            }

            .day-column {
                padding: 16px;
            }

            .template-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>
            <i class="fas fa-calendar-week" style="margin-right: 15px; font-size: 0.8em;"></i>
            Minha Rotina Semanal
        </h1>

        <div class="navigation">
            <?php
            $prev_week = clone $week_start_date;
            $prev_week->modify('-7 days');
            $next_week = clone $week_start_date;
            $next_week->modify('+7 days');
            ?>
            <form method="post" action="index.php" style="display:inline;">
                <input type="hidden" name="week_start" value="<?= $prev_week->format('Y-m-d') ?>">
                <button type="submit">
                    <i class="fas fa-chevron-left" style="margin-right: 8px;"></i>
                    Semana Anterior
                </button>
            </form>
            <div class="week-display">
                <i class="fas fa-calendar-alt" style="margin-right: 10px; font-size: 0.8em;"></i>
                <?= $week_start_date->format('d/m') ?> - <?= $week_end_date->format('d/m') ?>
            </div>
            <form method="post" action="index.php" style="display:inline;">
                <input type="hidden" name="week_start" value="<?= $next_week->format('Y-m-d') ?>">
                <button type="submit">
                    Próxima Semana
                    <i class="fas fa-chevron-right" style="margin-left: 8px;"></i>
                </button>
            </form>
        </div>

        <div class="week-grid">
            <?php
            $days_of_week = ['Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado', 'Domingo'];
            $day_cursor = clone $week_start_date;
            foreach ($days_of_week as $day_name):
            ?>
                <div class="day-column">
                    <h3>
                        <?= $day_name ?><br>
                        <small><?= $day_cursor->format('d/m') ?></small>
                    </h3>
                    <?php $date_str = $day_cursor->format('Y-m-d'); ?>
                    <?php if (!empty($blocks_by_day[$date_str])): ?>
                        <?php foreach ($blocks_by_day[$date_str] as $block): ?>
                            <div class="routine-item" 
                                style="border-left: 4px solid <?= htmlspecialchars($block['color']) ?>; 
                                       background-color: rgba(<?= hexToRgb($block['color']) ?>, 0.1);"
                                data-id="<?= htmlspecialchars($block['id']) ?>"
                                data-description="<?= htmlspecialchars($block['description']) ?>"
                                data-start_time="<?= htmlspecialchars($block['start_time']) ?>"
                                data-end_time="<?= htmlspecialchars($block['end_time']) ?>"
                                data-date="<?= htmlspecialchars($block['task_date']) ?>"
                                data-color="<?= htmlspecialchars($block['color']) ?>"
                            >
                                <div class="routine-content">
                                    <div class="routine-description" title="<?= htmlspecialchars($block['description']) ?>"><?= htmlspecialchars($block['description']) ?></div>
                                    <div class="routine-time"><?= date('H:i', strtotime($block['start_time'])) ?> - <?= date('H:i', strtotime($block['end_time'])) ?></div>
                                </div>
                                <div class="routine-actions">
                                    <button class="edit-icon-btn" onclick="openEditModal('<?= htmlspecialchars($block['id']) ?>', '<?= htmlspecialchars($block['description']) ?>', '<?= htmlspecialchars($block['start_time']) ?>', '<?= htmlspecialchars($block['end_time']) ?>', '<?= htmlspecialchars($block['task_date']) ?>', '<?= htmlspecialchars($block['color']) ?>', 'block')" title="Editar atividade">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <form method="post" action="index.php" style="display:inline;">
                                        <input type="hidden" name="delete_block_id" value="<?= $block['id'] ?>">
                                        <input type="hidden" name="week_start" value="<?= $week_start_date->format('Y-m-d') ?>">
                                        <button type="submit" class="delete-btn" onclick="return confirm('Tem certeza que deseja excluir esta atividade?')" title="Excluir atividade">
                                            <i class="fas fa-trash-alt"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <p class="empty-day">Vazio</p>
                    <?php endif; ?>
                </div>
            <?php $day_cursor->modify('+1 day'); ?>
            <?php endforeach; ?>
        </div>

        <div class="form-section">
            <h2>
                <i class="fas fa-plus-circle" style="margin-right: 10px; font-size: 0.9em;"></i>
                Adicionar Atividade Avulsa
            </h2>
            <!-- Formulário para adicionar um novo bloco de rotina manualmente -->
            <form class="add-block-form" action="index.php" method="POST">
                <input type="text" name="description" placeholder="📝 Atividade (Ex: Estudar)" required>
                <input type="date" name="task_date" value="<?= date('Y-m-d') ?>" required title="Data da atividade">
                <input type="time" name="start_time" required title="Horário de início">
                <input type="time" name="end_time" required title="Horário de término">
                <input type="color" name="block_color" value="#6366f1" title="Escolha uma cor para a atividade">
                <button type="submit" name="add_block">
                    <i class="fas fa-plus" style="margin-right: 8px;"></i>
                    Adicionar Atividade
                </button>
            </form>
        </div>

        <div class="templates-section">
            <div class="form-section">
                <h2>
                    <i class="fas fa-bookmark" style="margin-right: 10px; font-size: 0.9em;"></i>
                    Meus Modelos de Rotina
                </h2>
                <form class="add-template-form" action="index.php" method="POST">
                    <input type="text" name="description" placeholder="🏷️ Nome do modelo (Ex: Trabalho Manhã)" required>
                    <input type="time" name="start_time" required title="Horário de início do modelo">
                    <input type="time" name="end_time" required title="Horário de término do modelo">
                    <input type="color" name="template_color" value="#10b981" title="Escolha uma cor para o modelo">
                    <button type="submit" name="add_template">
                        <i class="fas fa-save" style="margin-right: 8px;"></i>
                        Salvar Modelo
                    </button>
                </form>
            </div>
            
            <div class="templates-grid">
                <?php if (empty($routine_templates)): ?>
                    <div style="text-align: center; color: var(--gray-500); grid-column: 1 / -1; margin-top: 40px; padding: 40px; background: rgba(255,255,255,0.5); border-radius: 16px; border: 2px dashed var(--gray-300);">
                        <i class="fas fa-bookmark" style="font-size: 3em; margin-bottom: 20px; opacity: 0.3;"></i>
                        <p style="font-size: 1.1em; margin: 0;">Nenhum modelo salvo ainda</p>
                        <p style="font-size: 0.9em; margin: 10px 0 0 0; opacity: 0.8;">Crie um modelo acima para reutilizar suas atividades!</p>
                    </div>
                <?php else: ?>
                    <?php foreach ($routine_templates as $template): ?>
                        <div class="template-card"
                            style="background-color: rgba(<?= hexToRgb($template['color']) ?>, 0.05);"
                            data-id="<?= htmlspecialchars($template['id']) ?>"
                            data-description="<?= htmlspecialchars($template['description']) ?>"
                            data-start_time="<?= htmlspecialchars($template['start_time']) ?>"
                            data-end_time="<?= htmlspecialchars($template['end_time']) ?>"
                            data-color="<?= htmlspecialchars($template['color']) ?>"
                        >
                            <div class="card-header">
                                <h4>
                                    <i class="fas fa-bookmark" style="margin-right: 8px; color: <?= htmlspecialchars($template['color']) ?>; font-size: 0.9em;"></i>
                                    <?= htmlspecialchars($template['description']) ?>
                                </h4>
                                <form method="post" action="index.php" style="display:inline;">
                                    <input type="hidden" name="delete_template_id" value="<?= $template['id'] ?>">
                                    <input type="hidden" name="week_start" value="<?= $week_start_date->format('Y-m-d') ?>">
                                    <button type="submit" class="delete-template-btn" onclick="return confirm('Tem certeza que deseja excluir este modelo?')">
                                        <i class="fas fa-trash-alt" style="margin-right: 5px;"></i>
                                        Excluir
                                    </button>
                                </form>
                            </div>
                            <div class="card-body">
                                <i class="fas fa-clock" style="margin-right: 8px; color: <?= htmlspecialchars($template['color']) ?>;"></i>
                                <?= date('H:i', strtotime($template['start_time'])) ?> - <?= date('H:i', strtotime($template['end_time'])) ?>
                            </div>
                            <div class="card-actions">
                                <button class="edit-icon-btn" onclick="openEditModal('<?= htmlspecialchars($template['id']) ?>', '<?= htmlspecialchars($template['description']) ?>', '<?= htmlspecialchars($template['start_time']) ?>', '<?= htmlspecialchars($template['end_time']) ?>', null, '<?= htmlspecialchars($template['color']) ?>', 'template')" title="Editar modelo">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <a class="apply-btn" href="#" onclick="applyTemplate('<?= $template['id'] ?>', '<?= $week_start_date->format('Y-m-d') ?>')" title="Aplicar em um dia específico">
                                    <i class="fas fa-calendar-plus" style="margin-right: 6px;"></i>
                                    Aplicar no Dia
                                </a>
                                <form method="post" action="index.php" style="display:inline;">
                                    <input type="hidden" name="apply_template_to_week_id" value="<?= $template['id'] ?>">
                                    <input type="hidden" name="week_start" value="<?= $week_start_date->format('Y-m-d') ?>">
                                    <button type="submit" class="apply-week-btn" onclick="return confirm('Aplicar este modelo para todos os dias da semana?')" title="Aplicar para toda a semana">
                                        <i class="fas fa-calendar-week" style="margin-right: 6px;"></i>
                                        Aplicar na Semana
                                    </button>
                                </form>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- The Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeEditModal()" title="Fechar">&times;</span>
            <h2 id="modalTitle" style="color: var(--gray-800); margin-bottom: 20px;">
                <i class="fas fa-edit" style="margin-right: 10px; color: var(--primary-color);"></i>
                Editar Item
            </h2>
            <form id="editForm" class="edit-form-container" method="POST" action="index.php">
                <input type="hidden" id="edit-id" name="id">
                <input type="hidden" id="edit-type" name="type">
                <input type="hidden" name="week_start" value="<?= $week_start_date->format('Y-m-d') ?>">

                <div>
                    <label for="edit-description">
                        <i class="fas fa-tag" style="margin-right: 5px;"></i>
                        Descrição:
                    </label>
                    <input type="text" id="edit-description" name="description" required placeholder="Digite a descrição da atividade">
                </div>

                <div style="display: flex; gap: 16px;">
                    <div style="flex-grow: 1;">
                        <label for="edit-start-time">
                            <i class="fas fa-play" style="margin-right: 5px;"></i>
                            Início:
                        </label>
                        <input type="time" id="edit-start-time" name="start_time" required>
                    </div>
                    <div style="flex-grow: 1;">
                        <label for="edit-end-time">
                            <i class="fas fa-stop" style="margin-right: 5px;"></i>
                            Fim:
                        </label>
                        <input type="time" id="edit-end-time" name="end_time" required>
                    </div>
                </div>

                <div id="date-field" style="display: none;">
                    <label for="edit-date">
                        <i class="fas fa-calendar" style="margin-right: 5px;"></i>
                        Data:
                    </label>
                    <input type="date" id="edit-date" name="task_date">
                </div>

                <div>
                    <label for="edit-color">
                        <i class="fas fa-palette" style="margin-right: 5px;"></i>
                        Cor:
                    </label>
                    <input type="color" id="edit-color" name="color">
                </div>

                <button type="submit" id="save-btn" name="save_edit">
                    <i class="fas fa-save" style="margin-right: 8px;"></i>
                    Salvar Alterações
                </button>
            </form>
        </div>
    </div>

    <script>
    // Elementos do DOM
    const modal = document.getElementById("editModal");
    const modalTitle = document.getElementById("modalTitle");
    const editForm = document.getElementById("editForm");
    const editId = document.getElementById("edit-id");
    const editType = document.getElementById("edit-type");
    const editDescription = document.getElementById("edit-description");
    const editStartTime = document.getElementById("edit-start-time");
    const editEndTime = document.getElementById("edit-end-time");
    const editDate = document.getElementById("edit-date");
    const editColor = document.getElementById("edit-color");
    const dateField = document.getElementById("date-field");

    // Função para abrir o modal de edição
    function openEditModal(id, description, startTime, endTime, date, color, type) {
        modal.style.display = "block";
        document.body.style.overflow = "hidden"; // Previne scroll do body

        editId.value = id;
        editType.value = type;
        editDescription.value = description;
        editStartTime.value = startTime;
        editEndTime.value = endTime;
        editColor.value = color;

        if (type === 'block') {
            modalTitle.innerHTML = '<i class="fas fa-edit" style="margin-right: 10px; color: var(--primary-color);"></i>Editar Atividade';
            dateField.style.display = "block";
            editDate.value = date;
        } else if (type === 'template') {
            modalTitle.innerHTML = '<i class="fas fa-edit" style="margin-right: 10px; color: var(--primary-color);"></i>Editar Modelo';
            dateField.style.display = "none";
            editDate.value = '';
        }

        // Foca no primeiro campo
        setTimeout(() => editDescription.focus(), 100);
    }

    // Função para fechar o modal
    function closeEditModal() {
        modal.style.display = "none";
        document.body.style.overflow = "auto"; // Restaura scroll do body
    }

    // Fecha modal ao clicar fora dele
    window.onclick = function(event) {
        if (event.target == modal) {
            closeEditModal();
        }
    }

    // Fecha modal com tecla ESC
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && modal.style.display === 'block') {
            closeEditModal();
        }
    });

    // Função melhorada para aplicar template
    function applyTemplate(templateId, weekStartDate) {
        // Cria um modal customizado para seleção de data
        const dateModal = document.createElement('div');
        dateModal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.6);
            backdrop-filter: blur(10px);
            z-index: 1001;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        const dateContent = document.createElement('div');
        dateContent.style.cssText = `
            background: white;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            max-width: 400px;
            width: 90%;
        `;

        dateContent.innerHTML = `
            <h3 style="margin: 0 0 20px 0; color: var(--gray-800);">
                <i class="fas fa-calendar-plus" style="margin-right: 10px; color: var(--primary-color);"></i>
                Aplicar Modelo
            </h3>
            <p style="color: var(--gray-600); margin-bottom: 20px;">Selecione a data para aplicar este modelo:</p>
            <input type="date" id="template-date" style="width: 100%; padding: 12px; border: 2px solid var(--gray-200); border-radius: 10px; font-size: 16px;" value="${new Date().toISOString().split('T')[0]}">
            <div style="display: flex; gap: 10px; margin-top: 20px; justify-content: flex-end;">
                <button id="cancel-apply" style="padding: 12px 20px; background: var(--gray-500); color: white; border: none; border-radius: 10px; cursor: pointer; font-weight: 600;">Cancelar</button>
                <button id="confirm-apply" style="padding: 12px 20px; background: var(--primary-color); color: white; border: none; border-radius: 10px; cursor: pointer; font-weight: 600;">Aplicar</button>
            </div>
        `;

        dateModal.appendChild(dateContent);
        document.body.appendChild(dateModal);

        const templateDateInput = document.getElementById('template-date');
        const cancelBtn = document.getElementById('cancel-apply');
        const confirmBtn = document.getElementById('confirm-apply');

        // Foca no input de data
        setTimeout(() => templateDateInput.focus(), 100);

        // Eventos dos botões
        cancelBtn.onclick = () => document.body.removeChild(dateModal);

        confirmBtn.onclick = () => {
            const selectedDate = templateDateInput.value;
            if (selectedDate) {
                // Cria e submete o formulário
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'index.php';

                const templateInput = document.createElement('input');
                templateInput.type = 'hidden';
                templateInput.name = 'apply_template_id';
                templateInput.value = templateId;
                form.appendChild(templateInput);

                const dateInput = document.createElement('input');
                dateInput.type = 'hidden';
                dateInput.name = 'apply_date';
                dateInput.value = selectedDate;
                form.appendChild(dateInput);

                const weekStartInput = document.createElement('input');
                weekStartInput.type = 'hidden';
                weekStartInput.name = 'week_start';
                weekStartInput.value = weekStartDate;
                form.appendChild(weekStartInput);

                document.body.appendChild(form);
                form.submit();
            }
        };

        // Fecha modal ao clicar fora
        dateModal.onclick = (e) => {
            if (e.target === dateModal) {
                document.body.removeChild(dateModal);
            }
        };
    }

    // Adiciona animações suaves aos elementos
    document.addEventListener('DOMContentLoaded', function() {
        // Animação de entrada para os cards
        const cards = document.querySelectorAll('.day-column, .template-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            setTimeout(() => {
                card.style.transition = 'all 0.5s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 50);
        });

        // Validação de horários no formulário
        const startTimeInputs = document.querySelectorAll('input[name="start_time"]');
        const endTimeInputs = document.querySelectorAll('input[name="end_time"]');

        function validateTimes(startInput, endInput) {
            if (startInput.value && endInput.value) {
                if (startInput.value >= endInput.value) {
                    endInput.setCustomValidity('O horário de fim deve ser posterior ao horário de início');
                } else {
                    endInput.setCustomValidity('');
                }
            }
        }

        startTimeInputs.forEach((startInput, index) => {
            const endInput = endTimeInputs[index];
            if (endInput) {
                startInput.addEventListener('change', () => validateTimes(startInput, endInput));
                endInput.addEventListener('change', () => validateTimes(startInput, endInput));
            }
        });
    });
    </script>
</body>
</html>
