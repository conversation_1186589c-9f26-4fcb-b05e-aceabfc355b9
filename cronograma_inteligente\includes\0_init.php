<?php
//includes/init.php

// Inicializa a sessão
session_start();

// Define o caminho absoluto para o config.php
$config_path = __DIR__ . '/../assets/config.php';

// Verifica se o arquivo existe
if (!file_exists($config_path)) {
    die("Erro: Arquivo de configuração não encontrado em: " . $config_path);
}

// Inclui as configurações do banco de dados
require_once $config_path;

// A variável $conexao já está disponível do config.php
// Vamos apenas verificar se ela está funcionando
if (!$conexao || !pg_ping($conexao)) {
    error_log("Erro: Conexão com banco de dados falhou");
    die("Erro ao conectar ao banco de dados. Por favor, tente novamente mais tarde.");
}

// Define a conexão como global
$GLOBALS['conexao'] = $conexao;

// Verifica se o usuário está logado
if (!isset($_SESSION['idusuario'])) {
    header('Location: /login_index.php');
    exit();
}