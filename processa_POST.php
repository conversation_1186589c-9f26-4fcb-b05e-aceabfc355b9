<?php
//processa_POST.php
include_once("conexao_POST.php");
session_start();

// REMOVER A FUNÇÃO buscarId() DAQUI - ELA NÃO É MAIS NECESSÁRIA E É INSEGURA
/*
function buscarId($conexao, $tabela, $campo, $valor) {
    $valor = pg_escape_string($conexao, $valor);
    $query = "SELECT id{$tabela} FROM appEstudo.{$tabela} WHERE {$campo} = '$valor'";
    $resultado = pg_query($conexao, $query);
    return $resultado ? pg_fetch_result($resultado, 0, 0) : false;
}
*/

// Verificação simplificada: apenas verifica se existe um usuário logado
// Remove a verificação de $_SESSION['validacao'] que estava causando perda de dados
if (!isset($_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit();
}

// Recebe dados do POST
$materia = $_POST['materia'];
$curso = $_POST['curso'];
$id_planejamento = $_POST['id_planejamento'];
$ponto_estudado = $_POST['ponto'];
$tempo_estudo = $_POST['tempo'];
$tempo_bruto = $_POST['tempoTotal'];
$tempo_perdido = $_POST['tempoPausado'];
$data_estudo = $_POST['data'];
$tempo_inicio_estudo = $_POST['tempoInicialEstudo'];
$tempo_fim_estudo = $_POST['tempoFinalEstudo'];
$metodo = $_POST['metodo'];
$id_usuario = $_SESSION['idusuario'];
$descricao = isset($_POST['descricao']) ? $_POST['descricao'] : null;
$link_conteudo = isset($_POST['link_conteudo']) ? $_POST['link_conteudo'] : null;

// Busca ID da matéria de forma segura
$query_buscar_id_materia = "SELECT idmateria FROM appEstudo.materia WHERE nome = $1";
$resultado_id_materia = pg_query_params($conexao, $query_buscar_id_materia, array($materia));
if ($resultado_id_materia && pg_num_rows($resultado_id_materia) > 0) {
    $id_materia = pg_fetch_result($resultado_id_materia, 0, 0);
} else {
    $id_materia = false;
}

// Validação de id_materia
if (!$id_materia) {
    // Considerar usar mensagens flash na sessão para exibir no 0cronometro.php
    $_SESSION['mensagem_erro_cronometro'] = "Matéria '$materia' não cadastrada!";
    header("Location: 0cronometro.php");
    exit();
}

// Busca ID do curso de forma segura
$query_buscar_id_curso = "SELECT idcurso FROM appEstudo.curso WHERE nome = $1";
$resultado_id_curso = pg_query_params($conexao, $query_buscar_id_curso, array($curso));
if ($resultado_id_curso && pg_num_rows($resultado_id_curso) > 0) {
    $id_curso = pg_fetch_result($resultado_id_curso, 0, 0);
} else {
    $id_curso = false;
}

// Validação de id_curso
if (!$id_curso) {
    $_SESSION['mensagem_erro_cronometro'] = "Curso '$curso' não cadastrado!";
    header("Location: 0cronometro.php");
    exit();
}

// Configura valores das questões
$q_total = $q_errada = $q_certa = 0;
if (in_array($metodo, ['Simulado', 'Questões'])) {
    $q_total = $_POST['num-questoes'];
    $q_errada = $_POST['questoes-erradas'];
    $q_certa = $_POST['questoes-certas'];
}

// Salva os dados na sessão para reutilização
$_SESSION = array_merge($_SESSION, [
    'curso' => $curso,
    'materia' => $materia,
    'ponto_estudado' => $ponto_estudado,
    'tempo_estudo' => $tempo_estudo,
    'tempo_bruto' => $tempo_bruto,
    'tempo_perdido' => $tempo_perdido,
    'data_estudo' => $data_estudo,
    'tempo_inicio_estudo' => $tempo_inicio_estudo,
    'tempo_fim_estudo' => $tempo_fim_estudo,
    'metodo' => $metodo,
    'q_total' => $q_total,
    'q_errada' => $q_errada,
    'q_certa' => $q_certa,
    'descricao' => $descricao,
    'link_conteudo' => $link_conteudo,
]);

// Insere os dados na tabela de estudos
$sql = "INSERT INTO appEstudo.estudos (
    planejamento_idplanejamento, planejamento_usuario_idusuario, materia_idmateria, ponto_estudado, 
    tempo_liquido, tempo_bruto, tempo_perdido, data, hora_inicio, hora_fim, metodo, q_total, 
    q_errada, q_certa, idcurso, descricao, link_conteudo
) VALUES (
    $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17
)";
$params = array(
    $id_planejamento, $id_usuario, $id_materia, $ponto_estudado, 
    $tempo_estudo, $tempo_bruto, $tempo_perdido, $data_estudo, 
    $tempo_inicio_estudo, $tempo_fim_estudo, $metodo, $q_total, 
    $q_errada, $q_certa, $id_curso, 
    // Para campos que podem ser NULL, passe o valor PHP null se for o caso
    // O driver PostgreSQL lidará com a conversão para NULL no SQL.
    empty($descricao) ? null : $descricao, 
    empty($link_conteudo) ? null : $link_conteudo
);
$salvar = pg_query_params($conexao, $sql, $params);
pg_close($conexao);

// Redireciona para o cronômetro com os dados do estudo para exibir o modal
$dados = [
    'materia' => $materia,
    'ponto_estudado' => $ponto_estudado,
    'tempo_estudo' => $tempo_estudo,
    'tempo_bruto' => $tempo_bruto,
    'tempo_perdido' => $tempo_perdido,
    'data_estudo' => $data_estudo,
    'tempo_inicio_estudo' => $tempo_inicio_estudo,
    'tempo_fim_estudo' => $tempo_fim_estudo,
    'metodo' => $metodo,
    'curso' => $curso,
    'descricao' => $descricao,
    'link_conteudo' => $link_conteudo
];
$dados_url = urlencode(json_encode($dados));
header("Location: 0cronometro.php?sucesso=1&dados=$dados_url");
exit();

?>