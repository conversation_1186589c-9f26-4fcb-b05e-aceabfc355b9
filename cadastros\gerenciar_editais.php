<?php
session_start();
require_once("assets/config.php");
require_once('includes/verify_admin.php');

// Verifica se é admin
verificarAcessoAdmin($conexao, false);

// Headers de segurança
header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com https://code.jquery.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data:;");
header("X-Frame-Options: DENY");
header("X-Content-Type-Options: nosniff");
header("X-XSS-Protection: 1; mode=block");
header("Referrer-Policy: strict-origin-when-cross-origin");

if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$query = "SELECT id_edital, nome, descricao, ano, orgao, logo_url FROM appestudo.edital ORDER BY ano DESC, nome";
$resultado = pg_query($conexao, $query);
if (!$resultado) {
    die("Erro ao buscar editais: " . pg_last_error($conexao));
}
$editais = pg_fetch_all($resultado);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciamento de Editais</title>
    <link href="https://fonts.googleapis.com/css2?family=Varela+Round&family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert2/11.7.32/sweetalert2.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert2/11.7.32/sweetalert2.all.min.js"></script>
    <style>
        :root {
            --primary: #00008B;
            --primary-light: #3949ab;
            --secondary: #f8f0e3;
            --accent: #4a4a4a;
            --border: #e0e0e0;
            --text: #2c3e50;
            --active: #00008B;
            --hover: #f5f5f5;
            --primary-blue: #00008B;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --background: #f5f5f5;
            --card-background: white;
        }

        [data-theme="dark"] {
            --primary: #4169E1;
            --secondary: #1a1a2e;
            --accent: #6c7293;
            --border: #2d2d42;
            --text: #e4e6f0;
            --active: #4169E1;
            --hover: #232338;
            --primary-blue: #4169E1;
            --shadow-color: rgba(0, 0, 0, 0.3);
            --background: #0a0a1f;
            --card-background: #13132b;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background: var(--background);
            color: var(--text);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: var(--primary);
            box-shadow: 0 2px 4px var(--shadow-color);
            margin-bottom: 30px;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .header-right {
            display: flex;
            align-items: center;
        }

        .theme-toggle {
            margin-left: 20px;
        }

        .theme-btn {
            background: none;
            border: none;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .theme-btn:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .header_centro {
            text-align: center;
            margin-bottom: 30px;
        }

        .header_centro h1 {
            font-size: 2rem;
            color: var(--text);
            margin-bottom: 10px;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            padding: 10px 15px;
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            text-decoration: none;
            gap: 8px;
        }

        .btn:hover {
            background: var(--primary-light);
            transform: translateY(-2px);
        }

        .btn-novo {
            background: var(--primary);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 50px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 139, 0.2);
            margin: 0 auto;
        }

        .btn-novo:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 139, 0.3);
        }

        .btn-novo i {
            font-size: 1.2rem;
        }

        .edital-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin-top: 20px;
        }

        .edital-card {
            background: var(--card-background);
            border-radius: 15px;
            box-shadow: 0 4px 6px var(--shadow-color);
            transition: transform 0.3s ease;
            border: 1px solid var(--border);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .edital-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px var(--shadow-color);
        }

        .edital-header {
            padding: 20px;
            border-bottom: 1px solid var(--border);
            text-align: center;
        }

        .edital-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 15px;
            border-radius: 50%;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--secondary);
        }

        .edital-logo img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
        }

        .edital-titulo {
            font-size: 1.2rem;
            margin-bottom: 5px;
            color: var(--text);
        }

        .edital-meta {
            font-size: 0.9rem;
            color: var(--accent);
            margin-bottom: 10px;
        }

        .edital-descricao {
            font-size: 0.9rem;
            color: var(--text);
            margin-bottom: 10px;
            max-height: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }

        .edital-body {
            padding: 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
        }

        .edital-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-danger {
            background: #e53935;
            color: white;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            padding: 20px;
            overflow-y: auto;
        }

        .hidden {
            display: none;
        }

        .modal-content {
            background: var(--card-background);
            border-radius: 20px;
            width: 90%;
            max-width: 600px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            animation: modalSlideIn 0.3s ease-out;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }

        @keyframes modalSlideIn {
            from {
                transform: translateY(-20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .modal-header {
            padding: 25px 30px;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h2 {
            color: var(--primary);
            margin: 0;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--text);
            transition: color 0.3s;
        }

        .close-btn:hover {
            color: #e53935;
        }

        .modal-body {
            padding: 30px;
        }

        .form-grid {
            display: grid;
            gap: 25px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text);
        }

        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid var(--border);
            border-radius: 8px;
            background: var(--card-background);
            color: var(--text);
            font-size: 1rem;
            transition: border-color 0.3s;
        }

        .form-input:focus {
            border-color: var(--primary);
            outline: none;
        }

        .input-group {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--accent);
        }

        .input-with-icon {
            padding-left: 45px;
        }

        .modal-footer {
            padding: 20px 30px;
            border-top: 1px solid var(--border);
            display: flex;
            justify-content: flex-end;
            gap: 15px;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s;
        }

        .btn-primary:hover {
            background: var(--primary-light);
        }

        .btn-secondary {
            background: transparent;
            color: var(--text);
            border: 1px solid var(--border);
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s;
        }

        .btn-secondary:hover {
            background: var(--hover);
        }

        .menu-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            margin-bottom: 15px;
        }

        .menu-icon i {
            color: var(--primary);
        }

        @media (max-width: 1200px) {
            .edital-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .edital-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
            }

            .modal {
                padding: 10px;
            }

            .modal-content {
                width: 95%;
                max-height: 95vh;
            }

            .modal-header {
                padding: 15px 20px;
            }

            .modal-body {
                padding: 20px;
            }

            .modal-footer {
                padding: 15px 20px;
            }

            .form-grid {
                gap: 15px;
            }
        }

        @media (max-width: 480px) {
            .modal-content {
                width: 98%;
                border-radius: 15px;
            }

            .modal-header {
                padding: 12px 15px;
            }

            .modal-body {
                padding: 15px;
            }

            .modal-footer {
                padding: 12px 15px;
                flex-direction: column;
                gap: 10px;
            }

            .modal-footer button {
                width: 100%;
            }

            .form-input {
                padding: 10px 12px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <div class="logo">
                <img src="../logo/logo_vertical.png" alt="Logo" style="height: 50px;">
            </div>
        </div>
        <div class="header-right">
            <div class="theme-toggle">
                <button id="theme-toggle-btn" class="theme-btn">
                    <i id="theme-icon" class="fas fa-moon"></i>
                </button>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Botão Voltar -->
        <div style="margin: 20px 0;">
            <a href="index.php" class="btn" style="background-color: var(--primary); color: white;">
                <i class="fas fa-arrow-left"></i> Voltar
            </a>
        </div>

        <div class="header_centro">
            <div class="menu-icon">
                <i class="fas fa-file-alt fa-3x" style="color: var(--primary); margin-bottom: 15px;"></i>
            </div>
            <h1>Gerenciamento de Editais</h1>
        </div>

        <div class="text-center" style="margin-bottom: 30px; text-align: center;">
            <button onclick="abrirModalCadastro()" class="btn-novo">
                <i class="fas fa-plus-circle"></i>
                <span>Adicionar Novo Edital</span>
            </button>
        </div>

        <div class="edital-grid">
            <?php if ($editais): ?>
                <?php foreach ($editais as $edital): ?>
                    <div id="edital-<?php echo $edital['id_edital']; ?>" class="edital-card">
                        <div class="edital-header">
                            <?php if (!empty($edital['logo_url'])): ?>
                                <div class="edital-logo">
                                    <?php
                                    // Exemplo de caminho completo: /cronograma_inteligente/uploads/logos/logo_1741992747_67d4b32bd5d20.jpg
                                    $logo_url = $edital['logo_url'];
                                    
                                    // Se o caminho já começar com /cronograma_inteligente/, mantém como está
                                    if (strpos($logo_url, '/cronograma_inteligente/') === 0) {
                                        $logo_src = $logo_url;
                                    }
                                    // Se o caminho não tiver o prefixo completo, adiciona
                                    else {
                                        // Remove qualquer ../ inicial se existir
                                        if (strpos($logo_url, '../') === 0) {
                                            $logo_url = substr($logo_url, 3);
                                        }
                                        
                                        // Remove qualquer / inicial se existir
                                        if (strpos($logo_url, '/') === 0) {
                                            $logo_url = substr($logo_url, 1);
                                        }
                                        
                                        // Adiciona o caminho base
                                        $logo_src = '/cronograma_inteligente/' . $logo_url;
                                    }
                                    ?>
                                    <img src="<?php echo htmlspecialchars($logo_src); ?>" 
                                         alt="Logo <?php echo htmlspecialchars($edital['nome']); ?>"
                                         onerror="this.onerror=null; this.src='/cronograma_inteligente/assets/images/placeholder.png';">
                                </div>
                            <?php else: ?>
                                <div class="edital-logo">
                                    <i class="fas fa-file-alt fa-2x" style="color: var(--primary);"></i>
                                </div>
                            <?php endif; ?>
                            <h2 class="edital-titulo">
                                <?php echo htmlspecialchars($edital['nome']); ?>
                            </h2>
                            <div class="edital-meta">
                                <?php echo htmlspecialchars($edital['orgao']); ?> - 
                                <?php echo htmlspecialchars($edital['ano']); ?>
                            </div>
                            <?php if (!empty($edital['descricao'])): ?>
                                <div class="edital-descricao">
                                    <?php echo htmlspecialchars($edital['descricao']); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="edital-body">
                            <div class="edital-actions">
                                <button onclick="abrirModalEdicao(
                                    <?php echo $edital['id_edital']; ?>, 
                                    '<?php echo addslashes($edital['nome']); ?>', 
                                    '<?php echo addslashes($edital['descricao']); ?>', 
                                    <?php echo $edital['ano']; ?>, 
                                    '<?php echo addslashes($edital['orgao']); ?>',
                                    '<?php echo addslashes($edital['logo_url']); ?>'
                                )" class="btn btn-primary">
                                    <i class="fas fa-edit"></i> Editar
                                </button>
                                <button onclick="confirmarExclusao(<?php echo $edital['id_edital']; ?>)" class="btn btn-danger">
                                    <i class="fas fa-trash-alt"></i> Excluir
                                </button>
                                <a href="gerenciar_conteudo_edital.php?id_edital=<?php echo $edital['id_edital']; ?>" class="btn btn-primary" style="background-color: #17a2b8; border-color: #17a2b8;"> 
                                    <i class="fas fa-list-ul"></i> Conteúdo
                                </a>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div style="grid-column: 1 / -1; text-align: center; padding: 30px;">
                    <i class="fas fa-info-circle fa-2x" style="color: var(--accent); margin-bottom: 15px;"></i>
                    <p>Nenhum edital cadastrado. Clique em "Adicionar Novo Edital" para começar.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Modal de Cadastro -->
    <div id="cadastroEditalModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-plus-circle"></i> Novo Edital</h2>
                <button onclick="fecharModalCadastro()" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="cadastroEditalForm" class="modal-body" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <div class="form-grid">
                    <!-- Logo Upload -->
                    <div class="form-group">
                        <label class="form-label">Logo do Edital (opcional)</label>
                        <div class="input-group">
                            <input type="file" id="logoUpload" name="logo" class="form-input" 
                                   accept="image/jpeg, image/png, image/gif, image/webp">
                            <small class="form-text">Formatos aceitos: JPG, PNG, GIF, WEBP. Tamanho máximo: 2MB.</small>
                        </div>
                    </div>
                    
                    <!-- Nome do Edital -->
                    <div class="form-group">
                        <label for="nome" class="form-label">Nome do Edital</label>
                        <div class="input-group">
                            <input type="text" id="nome" name="nome" class="form-input" required>
                        </div>
                    </div>
                    
                    <!-- Órgão -->
                    <div class="form-group">
                        <label for="orgao" class="form-label">Órgão</label>
                        <div class="input-group">
                            <input type="text" id="orgao" name="orgao" class="form-input" required>
                        </div>
                    </div>
                    
                    <!-- Ano -->
                    <div class="form-group">
                        <label for="ano" class="form-label">Ano</label>
                        <div class="input-group">
                            <input type="number" id="ano" name="ano" class="form-input" min="2000" max="2100" value="<?php echo date('Y'); ?>" required>
                        </div>
                    </div>
                    
                    <!-- Descrição -->
                    <div class="form-group">
                        <label for="descricao" class="form-label">Descrição</label>
                        <div class="input-group">
                            <textarea id="descricao" name="descricao" class="form-input" rows="4"></textarea>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" onclick="fecharModalCadastro()" class="btn-secondary">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save"></i> Salvar
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal de Edição -->
    <div id="editarEditalModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-edit"></i> Editar Edital</h2>
                <button onclick="fecharModalEdicao()" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="editarEditalForm" class="modal-body" enctype="multipart/form-data">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" id="idEditalEdicao" name="id_edital">
                <div class="form-grid">
                    <!-- Logo Upload -->
                    <div class="form-group">
                        <label class="form-label">Logo do Edital (opcional)</label>
                        <div class="input-group">
                            <input type="file" id="logoUploadEdicao" name="logo" class="form-input" 
                                   accept="image/jpeg, image/png, image/gif, image/webp">
                            <small class="form-text">Formatos aceitos: JPG, PNG, GIF, WEBP. Tamanho máximo: 2MB.</small>
                        </div>
                        <div id="logoPreviewContainer" class="edital-logo" style="margin-top: 10px; display: none;">
                            <img id="logoPreviewEdicao" src="" alt="Logo do Edital">
                        </div>
                    </div>
                    
                    <!-- Nome do Edital -->
                    <div class="form-group">
                        <label for="nomeEdicao" class="form-label">Nome do Edital</label>
                        <div class="input-group">
                            <input type="text" id="nomeEdicao" name="nome" class="form-input" required>
                        </div>
                    </div>
                    
                    <!-- Órgão -->
                    <div class="form-group">
                        <label for="orgaoEdicao" class="form-label">Órgão</label>
                        <div class="input-group">
                            <input type="text" id="orgaoEdicao" name="orgao" class="form-input" required>
                        </div>
                    </div>
                    
                    <!-- Ano -->
                    <div class="form-group">
                        <label for="anoEdicao" class="form-label">Ano</label>
                        <div class="input-group">
                            <input type="number" id="anoEdicao" name="ano" class="form-input" min="2000" max="2100" required>
                        </div>
                    </div>
                    
                    <!-- Descrição -->
                    <div class="form-group">
                        <label for="descricaoEdicao" class="form-label">Descrição</label>
                        <div class="input-group">
                            <textarea id="descricaoEdicao" name="descricao" class="form-input" rows="4"></textarea>
                        </div>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" onclick="fecharModalEdicao()" class="btn-secondary">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save"></i> Salvar
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal de Resposta -->
    <div id="modalResposta" class="modal hidden">
        <div class="modal-content" style="max-width: 400px;">
            <div class="modal-header">
                <h2 id="tituloResposta"></h2>
                <button onclick="fecharModalResposta()" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div id="conteudoResposta" style="text-align: center;"></div>
            </div>
            <div class="modal-footer">
                <button onclick="fecharModalResposta()" class="btn-primary">
                    <i class="fas fa-check"></i> OK
                </button>
            </div>
        </div>
    </div>

    <script>
        // Configuração do tema
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggleBtn = document.getElementById('theme-toggle-btn');
            if (themeToggleBtn) {
                themeToggleBtn.addEventListener('click', function() {
                    const currentTheme = document.documentElement.getAttribute('data-theme');
                    const newTheme = currentTheme === 'dark' ? '' : 'dark';
                    document.documentElement.setAttribute('data-theme', newTheme);
                    localStorage.setItem('theme', newTheme);
                    updateThemeIcon(newTheme);
                });
            }
            
            // Aplicar tema salvo ao carregar
            const savedTheme = localStorage.getItem('theme') || '';
            document.documentElement.setAttribute('data-theme', savedTheme);
            updateThemeIcon(savedTheme);
            
            // Configurar formulários
            document.getElementById('cadastroEditalForm').addEventListener('submit', function(e) {
                e.preventDefault();
                cadastrarEdital();
            });
            
            document.getElementById('editarEditalForm').addEventListener('submit', function(e) {
                e.preventDefault();
                atualizarEdital();
            });
        });

        function updateThemeIcon(theme) {
            const icon = document.getElementById('theme-icon');
            if (icon) {
                icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }

        // Funções do Modal de Cadastro
        function abrirModalCadastro() {
            document.getElementById('cadastroEditalForm').reset();
            document.getElementById('cadastroEditalModal').classList.remove('hidden');
        }

        function fecharModalCadastro() {
            document.getElementById('cadastroEditalModal').classList.add('hidden');
        }

        // Funções do Modal de Edição
        function abrirModalEdicao(id, nome, descricao, ano, orgao, logoUrl) {
            document.getElementById('idEditalEdicao').value = id;
            document.getElementById('nomeEdicao').value = nome;
            document.getElementById('descricaoEdicao').value = descricao;
            document.getElementById('anoEdicao').value = ano;
            document.getElementById('orgaoEdicao').value = orgao;
            
            const logoPreviewContainer = document.getElementById('logoPreviewContainer');
            const logoPreview = document.getElementById('logoPreviewEdicao');
            
            if (logoUrl) {
                // Ajusta o caminho da logo para exibição correta
                let logoSrc = logoUrl;
                
                // Se o caminho não começar com http:// ou https:// (URL absoluta)
                if (!logoUrl.match(/^https?:\/\//)) {
                    // Se o caminho não começar com /cronograma_inteligente/
                    if (logoUrl.indexOf('/cronograma_inteligente/') !== 0) {
                        // Remove qualquer ../ inicial se existir
                        if (logoUrl.indexOf('../') === 0) {
                            logoUrl = logoUrl.substring(3);
                        }
                        
                        // Remove qualquer / inicial se existir
                        if (logoUrl.indexOf('/') === 0) {
                            logoUrl = logoUrl.substring(1);
                        }
                        
                        // Adiciona o caminho base
                        logoSrc = '/cronograma_inteligente/' + logoUrl;
                    }
                }
                
                logoPreview.src = logoSrc;
                logoPreviewContainer.style.display = 'flex';
            } else {
                logoPreviewContainer.style.display = 'none';
            }
            
            document.getElementById('editarEditalModal').classList.remove('hidden');
        }

        function fecharModalEdicao() {
            document.getElementById('editarEditalModal').classList.add('hidden');
        }

        // Funções do Modal de Resposta
        function mostrarModalResposta(resposta) {
            document.getElementById('tituloResposta').textContent = resposta.success ? 'Sucesso!' : 'Erro';
            document.getElementById('conteudoResposta').innerHTML = resposta.message;
            document.getElementById('modalResposta').classList.remove('hidden');
        }

        function fecharModalResposta() {
            document.getElementById('modalResposta').classList.add('hidden');
        }

        // Função para cadastrar edital
        async function cadastrarEdital() {
            try {
                const formData = new FormData(document.getElementById('cadastroEditalForm'));
                
                console.log('Enviando dados para cadastro...');
                
                const response = await fetch('processar_edital.php', {
                    method: 'POST',
                    body: formData
                });

                // Primeiro, obter o texto da resposta
                const responseText = await response.text();
                console.log('Resposta do servidor (texto):', responseText);
                
                // Tentar analisar como JSON
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('Erro ao analisar resposta JSON:', parseError);
                    console.error('Resposta recebida:', responseText);
                    throw new Error('Resposta inválida do servidor. Verifique o console para mais detalhes.');
                }
                
                if (result.success) {
                    mostrarModalResposta({
                        success: true,
                        message: 'Edital cadastrado com sucesso!'
                    });
                    
                    // Fecha o modal de cadastro
                    fecharModalCadastro();
                    
                    // Recarrega a página após 2 segundos
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    throw new Error(result.message || 'Erro ao cadastrar edital');
                }
            } catch (error) {
                console.error('Erro completo:', error);
                mostrarModalResposta({
                    success: false,
                    message: `Erro ao cadastrar edital: ${error.message}`
                });
            }
        }

        // Função para atualizar edital
        async function atualizarEdital() {
            try {
                const formData = new FormData(document.getElementById('editarEditalForm'));
                
                console.log('Enviando dados para atualização...');
                
                // Adicionar um timeout para a requisição
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 segundos
                
                const response = await fetch('processar_edital.php?acao=editar', {
                    method: 'POST',
                    body: formData,
                    signal: controller.signal
                }).catch(error => {
                    if (error.name === 'AbortError') {
                        throw new Error('A requisição excedeu o tempo limite.');
                    }
                    throw error;
                });
                
                clearTimeout(timeoutId);

                // Verificar o status da resposta
                if (!response.ok) {
                    throw new Error(`Erro HTTP: ${response.status} ${response.statusText}`);
                }

                // Verificar se a resposta está vazia
                const responseText = await response.text();
                console.log('Resposta do servidor (texto):', responseText);
                
                if (!responseText.trim()) {
                    throw new Error('O servidor retornou uma resposta vazia');
                }
                
                // Tentar analisar como JSON
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (parseError) {
                    console.error('Erro ao analisar resposta JSON:', parseError);
                    console.error('Resposta recebida:', responseText);
                    throw new Error('Resposta inválida do servidor. Verifique o console para mais detalhes.');
                }
                
                if (result.success) {
                    mostrarModalResposta({
                        success: true,
                        message: 'Edital atualizado com sucesso!'
                    });
                    
                    // Fecha o modal de edição
                    fecharModalEdicao();
                    
                    // Recarrega a página após 2 segundos
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    throw new Error(result.message || 'Erro ao atualizar edital');
                }
            } catch (error) {
                console.error('Erro completo:', error);
                mostrarModalResposta({
                    success: false,
                    message: `Erro ao atualizar edital: ${error.message}`
                });
            }
        }

        // Função para confirmar exclusão
        function confirmarExclusao(id) {
            Swal.fire({
                title: 'Tem certeza?',
                text: "Esta ação não poderá ser revertida!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Sim, excluir!',
                cancelButtonText: 'Cancelar'
            }).then((result) => {
                if (result.isConfirmed) {
                    excluirEdital(id);
                }
            });
        }

        // Função para excluir edital
        async function excluirEdital(id) {
            try {
                const formData = new FormData();
                formData.append('id_edital', id);
                formData.append('csrf_token', '<?php echo $_SESSION['csrf_token']; ?>');
                
                const response = await fetch('processar_edital.php?acao=excluir', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    Swal.fire(
                        'Excluído!',
                        'O edital foi excluído com sucesso.',
                        'success'
                    );
                    
                    // Remove o card do edital da página
                    document.getElementById(`edital-${id}`).remove();
                    
                    // Se não houver mais editais, recarrega a página
                    if (document.querySelectorAll('.edital-card').length === 0) {
                        setTimeout(() => {
                            window.location.reload();
                        }, 2000);
                    }
                } else {
                    throw new Error(result.message || 'Erro ao excluir edital');
                }
            } catch (error) {
                Swal.fire(
                    'Erro!',
                    `Erro ao excluir edital: ${error.message}`,
                    'error'
                );
            }
        }

        // Adicionar ao script JavaScript existente
        document.addEventListener('DOMContentLoaded', function() {
            // Preview da imagem ao selecionar arquivo no formulário de cadastro
            const logoUpload = document.getElementById('logoUpload');
            if (logoUpload) {
                logoUpload.addEventListener('change', function() {
                    previewImagem(this);
                });
            }
            
            // Preview da imagem ao selecionar arquivo no formulário de edição
            const logoUploadEdicao = document.getElementById('logoUploadEdicao');
            if (logoUploadEdicao) {
                logoUploadEdicao.addEventListener('change', function() {
                    previewImagem(this, 'logoPreviewEdicao', 'logoPreviewContainer');
                });
            }
        });

        // Função para preview da imagem
        function previewImagem(input, previewId = null, containerId = null) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    if (previewId) {
                        const preview = document.getElementById(previewId);
                        if (preview) {
                            preview.src = e.target.result;
                        }
                        
                        const container = document.getElementById(containerId);
                        if (container) {
                            container.style.display = 'flex';
                        }
                    } else {
                        // Criar preview temporário para o formulário de cadastro
                        let container = document.querySelector('.preview-container');
                        if (!container) {
                            container = document.createElement('div');
                            container.className = 'preview-container';
                            container.style.cssText = 'margin-top: 10px; width: 100px; height: 100px; display: flex; align-items: center; justify-content: center; border: 1px solid var(--border); border-radius: 8px; overflow: hidden;';
                            input.parentNode.appendChild(container);
                        }
                        
                        container.innerHTML = `<img src="${e.target.result}" style="max-width: 90%; max-height: 90%; object-fit: contain;">`;
                    }
                };
                
                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
</body>
</html>















