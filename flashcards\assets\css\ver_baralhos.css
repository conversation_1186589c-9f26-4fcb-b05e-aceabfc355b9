/* Dark mode & theme variables */
:root {
  --primary-color: #000080;
  --paper-color: #FFFFFF;
  --secondary-color: #4169E1;
  --background-color: #E8ECF3;
  --text-color: #2C3345;
  --border-color: #B8C2CC;
  --hover-color: #f0f5ff;
  --shadow-color: rgba(0, 0, 128, 0.1);
  --success-color: #2e7d32;
  --error-color: #b71c1c;
  --warning-color: #f0ad4e;
}

/* Dark theme variables */
[data-theme="dark"] {
  --primary-color: #4169E1;
  --paper-color: #1e2130;
  --secondary-color: #6c92ff;
  --background-color: #121420;
  --text-color: #e4e8f0;
  --border-color: #3a4056;
  --hover-color: #2c3251;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --success-color: #4caf50;
  --error-color: #f44336;
  --warning-color: #ffb74d;
}

body {
  background-color: var(--background-color);
  font-family: 'Nunito', 'Quicksand', 'Varela Round', sans-serif;
  color: var(--text-color);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Header styles for dark mode */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 2rem;
  background-color: var(--paper-color);
  box-shadow: 0 3px 8px var(--shadow-color);
  margin-bottom: 2rem;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  z-index: 100;
}

.header-left, .header-right {
  display: flex;
  align-items: center;
  height: 60px;
}

.logo {
  display: flex;
  align-items: center;
  height: 60px;
  padding: 5px 0;
}

.logo img {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
  transition: opacity 0.3s ease;
}

/* Show/hide logo based on theme */
.logo-light {
  display: block;
  opacity: 1;
}

.logo-dark {
  display: none;
  opacity: 0;
}

[data-theme="dark"] .logo-light {
  display: none;
  opacity: 0;
}

[data-theme="dark"] .logo-dark {
  display: block;
  opacity: 1;
}

.user-info {
  display: flex;
  align-items: center;
  margin-right: 1.5rem;
  background-color: rgba(0, 0, 128, 0.08);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  transition: all 0.3s ease;
}

[data-theme="dark"] .user-info {
  background-color: rgba(65, 105, 225, 0.15);
}

.user-info:hover {
  background-color: rgba(0, 0, 128, 0.12);
}

[data-theme="dark"] .user-info:hover {
  background-color: rgba(65, 105, 225, 0.25);
}

.user-info i {
  font-size: 1.5rem;
  margin-right: 0.8rem;
  color: var(--primary-color);
  transition: color 0.3s ease;
}

.user-name {
  font-weight: 600;
  color: var(--text-color);
  transition: color 0.3s ease;
}

.theme-toggle {
  margin-left: 1rem;
  position: relative;
}

.theme-btn {
  background-color: var(--paper-color);
  border: 2px solid var(--primary-color);
  font-size: 1.2rem;
  color: var(--primary-color);
  cursor: pointer;
  padding: 0.6rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px var(--shadow-color);
}

[data-theme="dark"] .theme-btn {
  color: var(--warning-color);
  border-color: var(--secondary-color);
  background-color: var(--hover-color);
}

.theme-btn:hover {
  background-color: var(--hover-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

[data-theme="dark"] .theme-btn:hover {
  background-color: rgba(255, 183, 77, 0.15);
  color: var(--warning-color);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.clean-container {
  background-color: var(--paper-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px var(--shadow-color);
  padding: 30px;
  margin-bottom: 30px;
  position: relative;
  overflow: hidden;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.clean-header {
  background: var(--primary-color);
  color: var(--paper-color);
  margin: -30px -30px 30px -30px;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 3px solid var(--secondary-color);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.clean-header h1 {
  font-family: 'Varela Round', 'Quicksand', sans-serif;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.breadcrumb {
  font-family: 'Nunito', sans-serif;
  color: var(--primary-color);
  margin-bottom: 25px;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: color 0.3s ease;
}

.breadcrumb i {
  font-size: 0.9rem;
  opacity: 0.7;
}

.btn-novo {
  background: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.4);
  padding: 8px 16px;
  border-radius: 6px;
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-novo:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.btn-back {
  position: fixed;
  top: 102px;
  left: 20px;
  width: 40px;
  height: 40px;
  background: var(--paper-color);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  text-decoration: none;
  font-size: 1rem;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: 0 2px 6px var(--shadow-color);
}

.btn-back:hover {
  transform: translateY(-2px);
  background: var(--primary-color);
  color: white;
  box-shadow: 0 4px 8px var(--shadow-color);
}

.baralhos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 25px;
  padding: 10px;
}

.baralho-card {
  background: var(--paper-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px var(--shadow-color);
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

.baralho-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px var(--shadow-color);
}

.baralho-header {
  padding: 15px 20px;
  color: white;
  font-weight: 600;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
  gap: 12px;
  font-family: 'Varela Round', 'Quicksand', sans-serif;
}

.baralho-header i {
  font-size: 1.2rem;
}

.baralho-body {
  padding: 15px;
  transition: background-color 0.3s ease;
}

.baralho-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  margin: 10px 0;
  padding: 15px;
  background: var(--background-color);
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.stat-item {
  text-align: center;
  padding: 8px;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-3px);
}

.stat-number {
  font-size: 1.6rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 5px;
  line-height: 1;
  font-family: 'Varela Round', sans-serif;
  transition: color 0.3s ease;
}

.stat-label {
  font-size: 0.85rem;
  color: var(--text-color);
  opacity: 0.8;
  font-weight: 500;
  transition: color 0.3s ease;
}

.baralho-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  padding: 15px;
  border-top: 1px solid var(--border-color);
  transition: border-color 0.3s ease;
}

.baralho-actions a:last-child {
  grid-column: 1 / -1;
}

.btn {
  padding: 10px 5px;
  border-radius: 6px;
  cursor: pointer;
  font-family: 'Nunito', 'Quicksand', sans-serif;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  justify-content: center;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
  border: none;
  box-shadow: 0 2px 6px var(--shadow-color);
}

.btn-primary:hover {
  background: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

.btn-secondary {
  background: var(--paper-color);
  color: var(--primary-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--hover-color);
  color: var(--secondary-color);
  border-color: var(--secondary-color);
  transform: translateY(-2px);
}

[data-theme="dark"] .btn-secondary {
  color: var(--secondary-color);
}

.empty-state {
  text-align: center;
  padding: 60px 40px;
  background: var(--paper-color);
  border-radius: 8px;
  border: 1px dashed var(--border-color);
  margin-top: 20px;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.empty-state i {
  font-size: 3rem;
  color: var(--secondary-color);
  opacity: 0.7;
  margin-bottom: 25px;
  transition: color 0.3s ease;
}

.empty-state p {
  font-size: 1.1rem;
  color: var(--text-color);
  margin-bottom: 25px;
  font-family: 'Nunito', sans-serif;
  transition: color 0.3s ease;
}

.loading-shimmer {
  background: linear-gradient(
    90deg,
    var(--background-color) 0%,
    var(--paper-color) 50%,
    var(--background-color) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

[data-theme="dark"] .loading-shimmer {
  background: linear-gradient(
    90deg,
    rgba(30, 33, 48, 0.7) 0%,
    rgba(65, 105, 225, 0.1) 50%,
    rgba(30, 33, 48, 0.7) 100%
  );
  background-size: 200% 100%;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeInUp 0.5s ease forwards;
  animation-delay: calc(var(--index) * 0.1s);
  opacity: 0;
}

.footer {
  text-align: center;
  margin-top: 20px;
  font-size: 0.9rem;
  color: var(--text-color);
  opacity: 0.7;
  transition: color 0.3s ease;
}

[v-cloak] {
  display: none;
}

@media (max-width: 768px) {
  .container {
    padding: 20px 15px;
    padding-top: 60px;
  }
  
  .header {
    padding: 0.8rem 1rem;
  }
  
  .logo img {
    max-height: 40px;
  }
  
  .user-name {
    font-size: 0.9rem;
  }
  
  .clean-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
    padding: 20px;
  }
  
  .baralho-stats {
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    padding: 10px;
  }
  
  .stat-number {
    font-size: 1.4rem;
  }
  
  .stat-label {
    font-size: 0.75rem;
  }
  
  .baralho-actions {
    grid-template-columns: 1fr;
  }
  
  .baralho-actions a:last-child {
    grid-column: auto;
  }
  
  .btn {
    width: 100%;
  }
  
  .btn-back {
    top: 10px;
    left: 10px;
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
  }
}