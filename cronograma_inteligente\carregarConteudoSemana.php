<?php
require_once 'includes/init.php';
require_once 'includes/verificar_modulo.php';
verificarModulo();
require_once 'includes/mensagens.php';
require_once 'includes/calculos.php';
require_once 'includes/plano_estudo_logic.php';

header('Content-Type: application/json'); // Define o tipo de conteúdo como JSON

if (!isset($_GET['semana'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Semana não especificada']);
    exit;
}

$semanaIndex = (int)$_GET['semana'];

try {
    $planoData = new PlanoEstudoLogic($conexao);
    $conteudosPorSemana = $planoData->organizarConteudosPorSemana();
    $semanas_datas = $planoData->gerarSemanasDatas();
    $dias_estudo = array_keys($planoData->getDiasEstudo());
    
    if (!isset($conteudosPorSemana[$semanaIndex])) {
        throw new Exception('Semana não encontrada');
    }
    
    $semana = $conteudosPorSemana[$semanaIndex];
    ob_start(); // Inicia o buffer de saída
    
    // Para cada dia da semana
    foreach ($semana as $diaIndex => $diaConteudos) {
        if (!empty($diaConteudos)) {
            foreach ($diaConteudos as $conteudo) {
                $isLightColor = isLightColor($conteudo['cor']);
                $lightClass = $isLightColor ? 'light-color' : '';
                $estudado = $conteudo['status_estudo'] === 'Estudado';
                $completedClass = $estudado ? 'completed' : '';
                
                echo "<div class='conteudo-item {$lightClass} {$completedClass}' 
                      style='background-color: {$conteudo['cor']}'
                      data-id='{$conteudo['id']}'>";
                
                echo "<label>";
                echo "<input type='checkbox' class='conteudo-checkbox' 
                      data-id='{$conteudo['id']}'" . 
                      ($estudado ? ' checked' : '') . ">";
                echo "<div>";
                echo "<h4>{$conteudo['materia_nome']}</h4>";
                
                // Exibe os tópicos formatados
                if (!empty($conteudo['descricao_capitulo_principal'])) {
                    echo "<p class='capitulo-principal'>{$conteudo['descricao_capitulo_principal']}</p>";
                }
                if (!empty($conteudo['descricao_capitulo_secundario'])) {
                    echo "<p class='capitulo-secundario'>{$conteudo['descricao_capitulo_secundario']}</p>";
                }
                echo "<p class='subcapitulo'>{$conteudo['capitulo']} {$conteudo['descricao']}</p>";
                
                echo "</div>";
                echo "</label>";
                
                // Botões
                echo '<div style="display: flex; gap: 10px; width: 100%;">';
                echo '<button onclick="verificarEAbrirCronometro(\'' . 
                    htmlspecialchars($conteudo['materia_nome'], ENT_QUOTES) . '\', \'' . 
                    htmlspecialchars($conteudo['capitulo'] . ' ' . $conteudo['descricao'], ENT_QUOTES) . 
                    '\')" class="btn-cronometro">
                    <i class="fas fa-clock"></i> Cronômetro
                </button>';
                
                echo '<form method="POST" action="../painel_editar_materias.php" target="_blank" style="width: 100%; margin: 0;">
                    <input type="hidden" name="id_materia" value="' . $conteudo['idmateria'] . '">
                    <button type="submit" class="btn-anotacoes">
                        <i class="fas fa-notes-medical"></i> Anotações
                    </button>
                </form>';
                
                echo '</div>';
                echo '</div>';
            }
        }
    }
    
    $html = ob_get_clean(); // Pega o conteúdo do buffer e limpa
    
    echo json_encode([
        'success' => true,
        'content' => $html
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

function isLightColor($hex) {
    $hex = ltrim($hex, '#');
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));
    $yiq = (($r * 299) + ($g * 587) + ($b * 114)) / 1000;
    return $yiq >= 128;
}
?>