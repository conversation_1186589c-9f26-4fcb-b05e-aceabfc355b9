<?php
include_once("../../session_config.php");
require_once("../../assets/config.php");
require_once('../../cadastros/includes/verify_admin.php');

// Verifica se é qualquer admin
verificarAcessoAdmin($conexao, false);

// Processar ações
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'delete') {
        // Qualquer admin pode deletar tópicos
        $id = (int)$_POST['id'];
        
        // Log da ação de exclusão
        $admin_id = $_SESSION['idusuario'];
        $admin_nome = $_SESSION['nome'] ?? 'Admin';
        $ip = $_SERVER['REMOTE_ADDR'];
        
        // Buscar informações do tópico antes de deletar para o log
        $query_topico_info = "SELECT titulo, usuario_id FROM appestudo.forum_topicos WHERE id = $1";
        $result_topico_info = pg_query_params($conexao, $query_topico_info, array($id));
        $topico_info = pg_fetch_assoc($result_topico_info);
        
        // Primeiro desativa as respostas do tópico
        $query_respostas = "UPDATE appestudo.forum_respostas SET status = false WHERE topico_id = $1";
        pg_query_params($conexao, $query_respostas, array($id));
        
        // Depois desativa o tópico
        $query_topico = "UPDATE appestudo.forum_topicos SET status = false WHERE id = $1";
        pg_query_params($conexao, $query_topico, array($id));
        
        // Registrar no log de segurança
        $query_log = "INSERT INTO appestudo.log_seguranca (usuario_id, tipo_evento, ip, detalhes) 
                      VALUES ($1, 'topico_deletado_admin', $2, $3)";
        $detalhes = "Admin: $admin_nome deletou tópico ID: $id - Título: " . ($topico_info['titulo'] ?? 'N/A');
        pg_query_params($conexao, $query_log, array($admin_id, $ip, $detalhes));
        
    } elseif ($action === 'restore') {
        // Qualquer admin pode restaurar tópicos
        $id = (int)$_POST['id'];
        
        // Log da ação de restauração
        $admin_id = $_SESSION['idusuario'];
        $admin_nome = $_SESSION['nome'] ?? 'Admin';
        $ip = $_SERVER['REMOTE_ADDR'];
        
        // Reativa o tópico
        $query_topico = "UPDATE appestudo.forum_topicos SET status = true WHERE id = $1";
        pg_query_params($conexao, $query_topico, array($id));
        
        // Registrar no log de segurança
        $query_log = "INSERT INTO appestudo.log_seguranca (usuario_id, tipo_evento, ip, detalhes) 
                      VALUES ($1, 'topico_restaurado_admin', $2, $3)";
        $detalhes = "Admin: $admin_nome restaurou tópico ID: $id";
        pg_query_params($conexao, $query_log, array($admin_id, $ip, $detalhes));
    }
    
    header("Location: topicos.php");
    exit();
}

// Parâmetros de filtro
$categoria_id = isset($_GET['categoria']) ? (int)$_GET['categoria'] : null;
$status = isset($_GET['status']) ? $_GET['status'] : 'active';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Construir a query base
$query_topicos = "
    SELECT 
        t.id,
        t.titulo,
        t.conteudo,
        t.views,
        t.status,
        t.created_at,
        c.nome as categoria_nome,
        u.nome as autor_nome,
        (SELECT COUNT(*) FROM appestudo.forum_respostas WHERE topico_id = t.id) as total_respostas
    FROM appestudo.forum_topicos t
    JOIN appestudo.forum_categorias c ON c.id = t.categoria_id
    JOIN appestudo.usuario u ON u.idusuario = t.usuario_id
    WHERE 1=1
";

$params = array();
$param_count = 1;

if ($categoria_id) {
    $query_topicos .= " AND t.categoria_id = $" . $param_count;
    $params[] = $categoria_id;
    $param_count++;
}

if ($status === 'active') {
    $query_topicos .= " AND t.status = true";
} elseif ($status === 'inactive') {
    $query_topicos .= " AND t.status = false";
}

if ($search) {
    $query_topicos .= " AND (t.titulo ILIKE $" . $param_count . " OR t.conteudo ILIKE $" . $param_count . ")";
    $params[] = "%$search%";
    $param_count++;
}

$query_topicos .= " ORDER BY t.created_at DESC";

$result_topicos = pg_query_params($conexao, $query_topicos, $params);

// Buscar categorias para o filtro
$query_categorias = "SELECT id, nome FROM appestudo.forum_categorias WHERE status = true ORDER BY nome";
$result_categorias = pg_query($conexao, $query_categorias);
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Tópicos - Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <?php $nome_admin = isset($_SESSION['nome']) ? $_SESSION['nome'] : 'Administrador'; ?>
    <header class="admin-header">
        <a href="index.php" class="admin-logo"><strong>PlanejaAqui</strong> <span style="font-weight:400;">Admin</span></a>
        <div class="admin-header-actions">
            <span style="font-size:1rem; color:var(--text-secondary); margin-right:16px; font-weight:500;">
                <?php echo htmlspecialchars($nome_admin); ?>
            </span>
            <button class="icon-button" id="toggleDarkMode" title="Alternar modo escuro">
                <i class="fas fa-moon"></i>
            </button>
        </div>
    </header>
    <div class="container">
        <a href="index.php" class="btn-primary" style="display:inline-flex;align-items:center;gap:8px;margin-bottom:18px;text-decoration:none;font-size:1rem;">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
        <h1>Gerenciar Tópicos</h1>

        <!-- Filtros -->
        <div class="filters">
            <form method="GET" class="filter-form">
                <div class="filter-group">
                    <label for="categoria">Categoria:</label>
                    <select name="categoria" id="categoria">
                        <option value="">Todas</option>
                        <?php while ($cat = pg_fetch_assoc($result_categorias)): ?>
                            <option value="<?php echo $cat['id']; ?>" <?php echo ($categoria_id == $cat['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($cat['nome']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="status">Status:</label>
                    <select name="status" id="status">
                        <option value="active" <?php echo ($status === 'active') ? 'selected' : ''; ?>>Ativos</option>
                        <option value="inactive" <?php echo ($status === 'inactive') ? 'selected' : ''; ?>>Inativos</option>
                        <option value="all" <?php echo ($status === 'all') ? 'selected' : ''; ?>>Todos</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="search">Buscar:</label>
                    <input type="text" name="search" id="search" value="<?php echo htmlspecialchars($search); ?>">
                </div>

                <button type="submit" class="btn-primary">Filtrar</button>
            </form>
        </div>

        <!-- Lista de Tópicos -->
        <div class="topics-list">
            <?php while ($topico = pg_fetch_assoc($result_topicos)): ?>
                <div class="topic-card <?php echo $topico['status'] ? '' : 'inactive'; ?>">
                    <div class="topic-info">
                        <h3><?php echo htmlspecialchars($topico['titulo']); ?></h3>
                        <div class="topic-meta">
                            <span><i class="fas fa-folder"></i> <?php echo htmlspecialchars($topico['categoria_nome']); ?></span>
                            <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($topico['autor_nome']); ?></span>
                            <span><i class="fas fa-calendar"></i> <?php echo date('d/m/Y H:i', strtotime($topico['created_at'])); ?></span>
                            <span><i class="fas fa-eye"></i> <?php echo $topico['views']; ?></span>
                            <span><i class="fas fa-comments"></i> <?php echo $topico['total_respostas']; ?></span>
                        </div>
                    </div>
                    <div class="topic-actions">
                        <a href="../ver_topico.php?id=<?php echo $topico['id']; ?>" class="btn-view" target="_blank">
                            <i class="fas fa-eye"></i>
                        </a>
                        <?php if ($topico['status']): ?>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="delete">
                                <input type="hidden" name="id" value="<?php echo $topico['id']; ?>">
                                <button type="submit" class="btn-delete" onclick="return confirm('Tem certeza que deseja desativar este tópico?')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        <?php else: ?>
                            <form method="POST" style="display: inline;">
                                <input type="hidden" name="action" value="restore">
                                <input type="hidden" name="id" value="<?php echo $topico['id']; ?>">
                                <button type="submit" class="btn-restore" onclick="return confirm('Deseja reativar este tópico?')">
                                    <i class="fas fa-undo"></i>
                                </button>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endwhile; ?>
        </div>
    </div>
    <script>
        // Dark mode toggle igual ao index.php
        function toggleDarkMode() {
            const body = document.body;
            const icon = document.querySelector('#toggleDarkMode i');
            body.classList.toggle('dark-mode');
            if (body.classList.contains('dark-mode')) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
                localStorage.setItem('adminDarkMode', 'enabled');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
                localStorage.setItem('adminDarkMode', 'disabled');
            }
        }
        document.addEventListener('DOMContentLoaded', function() {
            const darkMode = localStorage.getItem('adminDarkMode');
            const icon = document.querySelector('#toggleDarkMode i');
            if (darkMode === 'enabled') {
                document.body.classList.add('dark-mode');
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            }
            document.getElementById('toggleDarkMode').addEventListener('click', function(e) {
                e.preventDefault();
                toggleDarkMode();
            });
        });
    </script>
</body>
</html>
