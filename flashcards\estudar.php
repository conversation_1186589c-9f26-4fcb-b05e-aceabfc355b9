<?php
// estudar.php
session_start();
include_once("assets/config.php");

if (!isset($_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit();
}

$usuario_id = $_SESSION['idusuario'];
$modo = 'revisao';
$categoria_id = null;
$baralho_id = null;
$topico_id = null;
$deck_name = '';

if (isset($_GET['baralho'])) {
    $modo = 'baralho';
    $baralho_id = (int)$_GET['baralho'];
} elseif (isset($_GET['topico'])) {
    $modo = 'topico';
    $topico_id = (int)$_GET['topico'];
} elseif (isset($_GET['categoria'])) {
    $modo = 'categoria';
    $categoria_id = (int)$_GET['categoria'];
}

// Definir query_base antes de usá-la
$query_base = "
    WITH cards_info AS (
        SELECT 
            f.id,
            f.pergunta,
            f.resposta,
            f.resumo,
            f.previsao_legal,
            f.jurisprudencia,
            fmm.imagem_base64 as mapa_mental,
            d.nome as deck_name,
            d.id as deck_id,
            c.nome as category_name,
            c.id as category_id,
            t.nome as topic_name,
            t.id as topic_id,
            COALESCE(fp.nivel_conhecimento, 0) as nivel,
            fp.proxima_revisao,
            fp.total_revisoes,
            fp.revisoes_corretas,
            STRING_AGG(m.nome, ', ') as materias,
            STRING_AGG(m.cor, ',') as materias_cores
        FROM appestudo.flashcards f
        JOIN appestudo.flashcard_topic_association fta ON fta.flashcard_id = f.id
        JOIN appestudo.flashcard_topics t ON t.id = fta.topic_id
        JOIN appestudo.flashcard_decks d ON d.id = t.deck_id
        JOIN appestudo.flashcard_categories c ON c.id = d.category_id
        LEFT JOIN appestudo.flashcard_progress fp ON fp.flashcard_id = f.id AND fp.usuario_id = $1
        LEFT JOIN appestudo.flashcard_materias fm ON fm.flashcard_id = f.id
        LEFT JOIN appestudo.materia m ON m.idmateria = fm.materia_id
        LEFT JOIN appestudo.flashcard_mindmaps fmm ON fmm.flashcard_id = f.id
        WHERE c.status = true
        GROUP BY 
            f.id, 
            d.nome, 
            d.id, 
            c.nome,
            c.id,
            t.nome,
            t.id,
            fp.nivel_conhecimento, 
            fp.proxima_revisao,
            fp.total_revisoes,
            fp.revisoes_corretas,
            fmm.imagem_base64
    )";

// Buscar informação adicional baseada no modo
if ($modo === 'baralho' && $baralho_id) {
    $query_deck = "
        SELECT 
            d.nome as deck_name,
            c.id as categoria_id,
            c.nome as categoria_name
        FROM appestudo.flashcard_decks d
        JOIN appestudo.flashcard_categories c ON c.id = d.category_id
        WHERE d.id = $1";
    
    $result_deck = pg_query_params($conexao, $query_deck, array($baralho_id));
    if ($deck = pg_fetch_assoc($result_deck)) {
        $deck_name = $deck['deck_name'];
        $categoria_id = $deck['categoria_id'];
        $categoria_name = $deck['categoria_name'];
    }
}

// Queries específicas para cada modo
if ($modo === 'topico') {
    $query = $query_base . "
        SELECT * FROM cards_info 
        WHERE topic_id = $2 
        ORDER BY RANDOM() 
        LIMIT 50";
    $result = pg_query_params($conexao, $query, array($usuario_id, $topico_id));
} elseif ($modo === 'baralho') {
    $query = $query_base . "
        SELECT * FROM cards_info 
        WHERE deck_id = $2 
        ORDER BY RANDOM() 
        LIMIT 50";
    $result = pg_query_params($conexao, $query, array($usuario_id, $baralho_id));
} elseif ($modo === 'categoria') {
    $query = $query_base . "
        SELECT * FROM cards_info 
        WHERE category_id = $2
        ORDER BY RANDOM() 
        LIMIT 50";
    $result = pg_query_params($conexao, $query, array($usuario_id, $categoria_id));
} else {
    // Modo revisão padrão
    $query = $query_base . "
        SELECT * FROM cards_info 
        WHERE proxima_revisao <= CURRENT_TIMESTAMP
            OR proxima_revisao IS NULL
        ORDER BY RANDOM() 
        LIMIT 50";
    $result = pg_query_params($conexao, $query, array($usuario_id));
}

$cards = array();
while ($row = pg_fetch_assoc($result)) {
    $cards[] = $row;
}
$cards_json = json_encode($cards);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estudar Flashcards</title>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Old+Standard+TT:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');

:root {
    --primary-color: #000080;    /* Navy da marca */
    --paper-color: #FFFFFF;      /* Branco puro */
    --secondary-color: #4169E1;  /* Azul royal suave */
    --background-color: #E8ECF3; /* Cinza azulado claro */
    --text-color: #2C3345;      /* Azul muito escuro */
    --border-color: #B8C2CC;    /* Cinza azulado */
}

body {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    margin: 0;
    padding: 20px;
    line-height: 1.7;
    min-height: 100vh;
    letter-spacing: -0.011em;
}

/* Container principal com grid layout */
.study-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
    display: grid;
    grid-template-columns: 80px 1fr 200px;
    gap: 30px;
    align-items: start;
}

/* Flashcard */
.flashcard {
    position: relative;
    flex: 1;
    height: auto;
    transform-style: preserve-3d;
    transition: transform 0.6s;
}

.flashcard.is-flipped {
    transform: rotateY(180deg);
}

.card-face {
    position: absolute;
    width: 100%;
    height: auto;
    backface-visibility: hidden;
    padding: 40px;
    background: var(--paper-color);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 4px 4px 0 var(--border-color);
    box-sizing: border-box;
}

.card-back {
    transform: rotateY(180deg);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: start;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.card-meta {
    font-size: 0.9375rem;
    color: var(--secondary-color);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
}

.card-meta span {
    position: relative;
    display: inline-flex;
    align-items: center;
}

.card-meta span:not(:last-child)::after {
    content: '>';
    margin: 0 5px;
    opacity: 0.6;
}

.card-content {
    font-size: 1.125rem;
    line-height: 1.7;
    text-align: left;
    padding: 20px 0;
    max-width: 70ch;
    margin: 0 auto;
}

/* Question display */
.question-display {
    padding: 20px;
    background: rgba(0, 0, 0, 0.03);
    border-radius: 8px;
    margin-bottom: 25px;
    font-size: 1.125rem;
}

/* Back button */
.btn-back {
    position: sticky;
    top: 20px;
    width: 50px;
    height: 50px;
    background: var(--paper-color);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    text-decoration: none;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    margin-left: auto;
}

.btn-back:hover {
    transform: translateY(-2px);
    background: var(--primary-color);
    color: white;
}

/* Progress container */
.study-progress {
    position: sticky;
    top: 20px;
    background: var(--paper-color);
    padding: 20px;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 4px 4px 0 var(--border-color);
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.progress-text {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    color: var(--primary-color);
    font-weight: bold;
    font-size: 1.1rem;
    text-align: center;
}

.progress-detail {
    font-size: 0.9rem;
    color: var(--secondary-color);
    text-align: center;
    padding-top: 5px;
    border-top: 1px solid var(--border-color);
}

/* Sections */
.section-collapsible {
    margin-top: 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    overflow: hidden;
}

.section-header {
    padding: 15px;
    background: rgba(0, 0, 0, 0.03);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: 500;
    font-size: 1rem;
}

.section-content {
    padding: 20px;
    display: none;
    background: white;
    font-size: 1.125rem;
    line-height: 1.7;
}

.section-content.active {
    display: block;
}

/* Rating buttons */
.rating-buttons {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-top: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 8px;
}

.btn-rating {
    padding: 15px;
    border: none;
    border-radius: 6px;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    color: white;
}

.btn-rating:hover {
    transform: translateY(-2px);
}

.btn-hard { background: #DC2626; }
.btn-medium { background: #F59E0B; }
.btn-easy { background: #10B981; }

/* Show answer button */
.btn-show-answer {
    width: 100%;
    background: var(--primary-color);
    color: white;
    border: 2px solid var(--border-color);
    padding: 15px 30px;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: 4px 4px 0 var(--border-color);
    margin-top: 20px;
}

.btn-show-answer:hover {
    transform: translateY(-2px);
    box-shadow: 6px 6px 0 var(--border-color);
    background: var(--secondary-color);
}

/* Mindmap viewer */
.mindmap-viewer {
    width: 100%;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    margin: 15px 0;
}

#mindmap-display {
    width: 100%;
    min-height: 200px;
    overflow: auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

#mindmap-display img {
    max-width: 100%;
    height: auto;
    transform-origin: center;
    transition: transform 0.3s ease;
}

.mindmap-controls {
    position: absolute;
    bottom: 10px;
    right: 10px;
    display: flex;
    gap: 5px;
    background: rgba(255, 255, 255, 0.9);
    padding: 5px;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-control {
    width: 30px;
    height: 30px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.no-mindmap-message {
    text-align: center;
    padding: 40px;
    color: var(--secondary-color);
    font-style: italic;
}

/* Headers */
h3 {
    font-weight: 600;
    margin-bottom: 1rem;
}

/* Responsividade */
@media (max-width: 1200px) {
    .study-container {
        grid-template-columns: 60px 1fr 180px;
        gap: 20px;
        padding: 15px;
    }
}

@media (max-width: 992px) {
    .study-container {
        grid-template-columns: 50px 1fr 160px;
        gap: 15px;
    }
}

@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .study-container {
        grid-template-columns: 1fr;
        gap: 60px 15px;
        padding: 10px;
    }

    .btn-back {
        position: fixed;
        top: 10px;
        left: 10px;
        z-index: 1000;
        margin: 0;
    }

    .study-progress {
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 1000;
        padding: 10px 15px;
        flex-direction: row;
        align-items: center;
        gap: 15px;
    }

    .progress-detail {
        border-top: none;
        border-left: 1px solid var(--border-color);
        padding-top: 0;
        padding-left: 15px;
    }

    .flashcard {
        margin-top: 80px;
    }

    .card-face {
        padding: 20px;
    }

    .rating-buttons {
        grid-template-columns: 1fr;
        gap: 10px;
        padding: 15px;
    }

    .btn-rating {
        padding: 12px;
    }

    .card-content {
        font-size: 1rem;
        padding: 15px 0;
    }

    .section-header {
        padding: 12px;
    }

    .section-content {
        padding: 15px;
    }
}

@media (max-width: 480px) {
    .study-progress {
        max-width: calc(100% - 80px);
        padding: 8px 12px;
    }

    .progress-text {
        font-size: 0.9rem;
    }

    .progress-detail {
        font-size: 0.8rem;
        padding-left: 10px;
    }

    .card-face {
        padding: 15px;
    }

    .card-header {
        margin-bottom: 15px;
    }

    .btn-show-answer {
        padding: 12px 20px;
        font-size: 1rem;
    }
}
</style>
</head>
<body>
<div class="study-container">
    <?php
    $voltar_para = "flashcards.php";

    if ($modo === 'topico') {
        // Buscar informações do tópico e baralho
        $query_info = "
            SELECT 
                t.deck_id,
                d.category_id
            FROM appestudo.flashcard_topics t
            JOIN appestudo.flashcard_decks d ON d.id = t.deck_id
            WHERE t.id = $1";
        $result_info = pg_query_params($conexao, $query_info, array($topico_id));
        if ($info = pg_fetch_assoc($result_info)) {
            $voltar_para = "ver_topicos.php?baralho=" . $info['deck_id'];
        }
    } elseif ($modo === 'baralho') {
        $query_info = "
            SELECT category_id
            FROM appestudo.flashcard_decks
            WHERE id = $1";
        $result_info = pg_query_params($conexao, $query_info, array($baralho_id));
        if ($info = pg_fetch_assoc($result_info)) {
            $voltar_para = "ver_baralhos.php?categoria=" . $info['category_id'];
        }
    } elseif ($categoria_id) {
        $voltar_para = "ver_baralhos.php?categoria=" . $categoria_id;
    }
    ?>
<a href="<?php echo $voltar_para; ?>" class="btn-back">
    <i class="fas fa-arrow-left"></i>
</a>
    
    <div class="flashcard">
        <div class="card-face card-front">
            <div class="card-header">
                <div class="card-meta">
                    <span class="deck-name"></span>
                </div>
            </div>
            <div class="card-content question-content"></div>
            <button class="btn btn-show-answer" onclick="flipCard()">
                <i class="fas fa-eye"></i>
                Mostrar Resposta
            </button>
        </div>

        <div class="card-face card-back">
            <div class="card-header">
                <div class="card-meta">
                    <span class="deck-name"></span>
                </div>
            </div>

            <!-- Pergunta -->
            <div class="card-content question-display" style="color: #666; margin-bottom: 20px;">
                <h3 style="margin-bottom: 10px; color: #888;">Pergunta:</h3>
                <div class="question-text"></div>
            </div>

            <!-- Resposta -->
            <div class="card-content answer-content">
                <h3 style="margin-bottom: 10px;">Resposta:</h3>
                <div class="answer-text"></div>
            </div>

            <div class="sections-container"></div>

            <div class="section-collapsible">
                <div class="section-header" onclick="toggleSection('mapa-mental')">
                    <span>🎯 Mapa Mental</span>
                    <span class="text-[#8B4513]">▶</span>
                </div>
                <div id="mapa-mental" class="section-content">
                    <div class="mindmap-viewer">
                        <div id="mindmap-display">
                            <!-- Se não houver mapa mental, mostra mensagem -->
                            <div class="no-mindmap-message">
                                Nenhum mapa mental disponível para este card.
                            </div>
                        </div>
                        
                        <div class="mindmap-controls">
                            <button onclick="zoomIn()" class="btn-control">
                                <i class="fas fa-search-plus"></i>
                            </button>
                            <button onclick="zoomOut()" class="btn-control">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <button onclick="resetZoom()" class="btn-control">
                                <i class="fas fa-sync"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="rating-buttons">
                <button class="btn-rating btn-hard" onclick="rateCard('hard')">
                    <i class="fas fa-times"></i>
                    Difícil
                </button>
                <button class="btn-rating btn-medium" onclick="rateCard('medium')">
                    <i class="fas fa-clock"></i>
                    Médio
                </button>
                <button class="btn-rating btn-easy" onclick="rateCard('easy')">
                    <i class="fas fa-check"></i>
                    Fácil
                </button>
            </div>
        </div>
    </div>
    <div class="study-progress">
    <div class="progress-text">
        <span class="current-progress">0</span>/<span class="total-progress">0</span> Cards
    </div>
    <?php if ($modo === 'baralho' && $deck_name): ?>
        <div class="progress-detail">
            Estudando: <?php echo htmlspecialchars($deck_name); ?>
        </div>
    <?php elseif ($modo === 'revisao'): ?>
        <div class="progress-detail">
            Modo: Revisão Geral
        </div>
    <?php endif; ?>
</div>



    <script>
        // Dados dos cards e variáveis de controle
        const cards = <?php echo $cards_json; ?>;
        let currentCardIndex = 0;
        let totalReviewed = 0;
        let currentZoom = 1;
        const ZOOM_STEP = 0.1;
        const MAX_ZOOM = 3;
        const MIN_ZOOM = 0.5;

        // Elementos DOM
        const flashcard = document.querySelector('.flashcard');
        const questionContent = document.querySelector('.question-content');
        const answerContent = document.querySelector('.answer-content');
        const deckName = document.querySelector('.deck-name');
        const sectionsContainer = document.querySelector('.sections-container');
        const progressText = document.querySelector('.progress-text');

        function updateProgress() {
    const currentProgress = document.querySelector('.current-progress');
    const totalProgress = document.querySelector('.total-progress');
    
    // Atualiza o número atual de cards estudados
    currentProgress.textContent = totalReviewed;
    totalProgress.textContent = cards.length;

    // Se o card atual tiver informações de progresso, mostra
    if (cards[currentCardIndex]) {
        const card = cards[currentCardIndex];
        if (card.total_revisoes > 0) {
            const acuracia = ((card.revisoes_corretas / card.total_revisoes) * 100).toFixed(1);
            document.querySelector('.progress-detail').textContent = 
                `Acurácia: ${acuracia}% (${card.revisoes_corretas}/${card.total_revisoes})`;
        }
    }
}

function showCard(card) {
    // Atualizar deck name em ambos os lados do card com a hierarquia completa
    const deckNames = document.querySelectorAll('.deck-name');
    deckNames.forEach(element => {
        element.textContent = `${card.category_name} > ${card.deck_name} > ${card.topic_name}`;
    });

    // Atualizar conteúdo da pergunta e resposta usando innerHTML
    questionContent.innerHTML = card.pergunta;
    document.querySelector('.question-text').innerHTML = card.pergunta;
    document.querySelector('.answer-text').innerHTML = card.resposta;

    // Criar seções colapsáveis
    sectionsContainer.innerHTML = '';
    
    if (card.resumo) {
        addSection('resumo', '📝 Resumo', card.resumo);
    }
    if (card.previsao_legal) {
        addSection('previsao', '📜 Previsão Legal', card.previsao_legal);
    }
    if (card.jurisprudencia) {
        addSection('jurisprudencia', '⚖️ Jurisprudência', card.jurisprudencia);
    }

    // Inicializar o mapa mental
    initMindMap(card);

    // Resetar estado do card
    flashcard.classList.remove('is-flipped');
}

        function initMindMap(card) {
    const display = document.getElementById('mindmap-display');
    const controls = document.querySelector('.mindmap-controls');
    
    if (!display) return;

    if (card.mapa_mental) {
        display.innerHTML = `<img src="data:image/jpeg;base64,${card.mapa_mental}" alt="Mapa Mental">`;
        controls.style.display = 'flex'; // Mostra controles apenas quando há imagem
    } else {
        display.innerHTML = `
            <div class="no-mindmap-message">
                Nenhum mapa mental disponível para este card.
            </div>
        `;
        controls.style.display = 'none'; // Esconde controles quando não há imagem
    }
}

        function showUploadInterface() {
            const display = document.getElementById('mindmap-display');
            display.innerHTML = `
                <div class="mindmap-upload" id="dropZone">
                    <i class="fas fa-image fa-2x"></i>
                    <p>Arraste uma imagem ou clique para fazer upload</p>
                    <input type="file" id="mindmapInput" accept="image/*" style="display: none">
                </div>
            `;
            setupUploadHandlers();
        }

        function setupUploadHandlers() {
            const dropZone = document.getElementById('dropZone');
            const input = document.getElementById('mindmapInput');

            dropZone.onclick = () => input.click();

            input.onchange = (e) => {
                const file = e.target.files[0];
                if (file) handleUpload(file);
            };

            dropZone.ondragover = (e) => {
                e.preventDefault();
                dropZone.classList.add('drag-over');
            };

            dropZone.ondragleave = () => {
                dropZone.classList.remove('drag-over');
            };

            dropZone.ondrop = (e) => {
                e.preventDefault();
                dropZone.classList.remove('drag-over');
                const file = e.dataTransfer.files[0];
                if (file) handleUpload(file);
            };
        }

        function handleUpload(file) {
            const reader = new FileReader();
            reader.onload = async (e) => {
                const base64Data = e.target.result.split(',')[1];
                try {
                    const response = await fetch('upload_mindmap.php', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            flashcardId: cards[currentCardIndex].id,
                            imageData: base64Data
                        })
                    });
                    
                    if (response.ok) {
                        const display = document.getElementById('mindmap-display');
                        display.innerHTML = `<img src="data:image/jpeg;base64,${base64Data}" alt="Mapa Mental">`;
                        cards[currentCardIndex].mapa_mental = base64Data;
                    }
                } catch (error) {
                    console.error('Erro ao fazer upload:', error);
                }
            };
            reader.readAsDataURL(file);
        }

// Função addSection também precisa ser atualizada
function addSection(id, title, content) {
    const section = document.createElement('div');
    section.className = 'section-collapsible';
    section.innerHTML = `
        <div class="section-header" onclick="toggleSection('${id}')">
            <span>${title}</span>
            <span class="text-[#8B4513]">▶</span>
        </div>
        <div id="${id}" class="section-content">
            ${content}
        </div>
    `;
    sectionsContainer.appendChild(section);
}

        function toggleSection(id) {
            const content = document.getElementById(id);
            const header = content.previousElementSibling;
            const arrow = header.querySelector('span:last-child');
            
            if (content.classList.contains('active')) {
                content.classList.remove('active');
                arrow.textContent = '▶';
            } else {
                content.classList.add('active');
                arrow.textContent = '▼';
            }
        }

        function flipCard() {
            flashcard.classList.add('is-flipped');
        }

        function zoomIn() {
            if (currentZoom < MAX_ZOOM) {
                currentZoom += ZOOM_STEP;
                updateZoom();
            }
        }

        function zoomOut() {
            if (currentZoom > MIN_ZOOM) {
                currentZoom -= ZOOM_STEP;
                updateZoom();
            }
        }

        function resetZoom() {
            currentZoom = 1;
            updateZoom();
        }

        function updateZoom() {
            const img = document.querySelector('#mindmap-display img');
            if (img) {
                img.style.transform = `scale(${currentZoom})`;
            }
        }

        async function rateCard(difficulty) {
            const card = cards[currentCardIndex];
            
            try {
                const response = await fetch('rate_card.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        cardId: card.id,
                        rating: difficulty
                    })
                });

                if (response.ok) {
                    totalReviewed++;
                    updateProgress();
                    nextCard();
                }
            } catch (error) {
                console.error('Erro ao avaliar card:', error);
            }
        }

        function nextCard() {
            if (currentCardIndex < cards.length - 1) {
                currentCardIndex++;
                showCard(cards[currentCardIndex]);
            } else {
                // Finalizar sessão de estudo
                document.querySelector('.study-container').innerHTML = `
                    <div class="form-container" style="text-align: center;">
                        <h2>Parabéns!</h2>
                        <p>Você completou todos os cards desta sessão.</p>
                        <a href="flashcards.php" class="btn btn-primary">
                            <i class="fas fa-home"></i>
                            Voltar ao Início
                        </a>
                    </div>
                `;
            }
        }

        // Inicializar primeiro card
        if (cards.length > 0) {
            showCard(cards[0]);
            updateProgress();
        } else {
            document.querySelector('.study-container').innerHTML = `
                <div class="form-container" style="text-align: center;">
                    <h2>Nenhum card para estudar</h2>
                    <p>Não há cards disponíveis para revisão no momento.</p>
                    <a href="flashcards.php" class="btn btn-primary">
                        <i class="fas fa-home"></i>
                        Voltar ao Início
                    </a>
                </div>
            `;
        }
    </script>
</body>
</html>