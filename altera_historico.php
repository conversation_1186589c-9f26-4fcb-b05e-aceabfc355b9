<?php
include 'conexao_POST.php';
include 'processa_index.php';

// Inicializa o array de resposta
$response = array();

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitiza e valida o ID
    $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
    $updates = array();
    $params = array();

    // Verifica e processa os campos enviados
    if (isset($_POST['ponto_estudado'])) {
        $updates[] = "ponto_estudado = $" . (count($params) + 1);
        $params[] = $_POST['ponto_estudado'];
    }

    if (isset($_POST['tempo_liquido'])) {
        if (preg_match('/^([0-9]{2}):([0-9]{2}):([0-9]{2})$/', $_POST['tempo_liquido'])) {
            $updates[] = "tempo_liquido = $" . (count($params) + 1);
            $params[] = $_POST['tempo_liquido'];
        }
    }

    if (isset($_POST['metodo'])) {
        $updates[] = "metodo = $" . (count($params) + 1);
        $params[] = $_POST['metodo'];
    }

    if (isset($_POST['q_total'])) {
        $q_total = filter_input(INPUT_POST, 'q_total', FILTER_VALIDATE_INT);
        if ($q_total !== false) {
            $updates[] = "q_total = $" . (count($params) + 1);
            $params[] = $q_total;
        }
    }

    if (isset($_POST['q_certa'])) {
        $q_certa = filter_input(INPUT_POST, 'q_certa', FILTER_VALIDATE_INT);
        if ($q_certa !== false) {
            $updates[] = "q_certa = $" . (count($params) + 1);
            $params[] = $q_certa;
        }
    }

    if (isset($_POST['q_errada'])) {
        $q_errada = filter_input(INPUT_POST, 'q_errada', FILTER_VALIDATE_INT);
        if ($q_errada !== false) {
            $updates[] = "q_errada = $" . (count($params) + 1);
            $params[] = $q_errada;
        }
    }

    if (!empty($updates) && $id !== false) {
        // Adiciona o ID aos parâmetros
        $params[] = $id;

        // Monta a query de update
        $query = "UPDATE appEstudo.estudos SET " . implode(", ", $updates) . " WHERE idestudos = $" . count($params);

        try {
            // Prepara e executa a query
            $stmt = pg_prepare($conexao, "", $query);
            $result = pg_execute($conexao, "", $params);

            if ($result) {
                $response['success'] = true;
                $response['message'] = 'Atualização realizada com sucesso';
            } else {
                $response['success'] = false;
                $response['message'] = 'Erro ao atualizar os dados';
            }
        } catch (Exception $e) {
            $response['success'] = false;
            $response['message'] = 'Erro no banco de dados: ' . $e->getMessage();
        }
    } else {
        $response['success'] = false;
        $response['message'] = 'Dados inválidos para atualização';
    }
} else {
    $response['success'] = false;
    $response['message'] = 'Método de requisição inválido';
}

// Define o cabeçalho como JSON
header('Content-Type: application/json');

// Retorna a resposta como JSON
echo json_encode($response);
?>