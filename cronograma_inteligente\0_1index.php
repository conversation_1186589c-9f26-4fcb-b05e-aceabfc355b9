<?php
//index.php
session_start();
include_once 'assets/config.php';
require_once 'includes/verify_cronograma_access.php';
require_once 'includes/calculos.php';
require_once 'includes/plano_estudo_logic.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['idusuario'])) {
    header("Location: ../login_index.php");
    exit();
}

$usuario_id = $_SESSION['idusuario'];


// Verifica acesso ao módulo
$verificacao = verificarAcessoCronograma($conexao, $_SESSION['idusuario']);

if (!$verificacao['acesso']) {
    $_SESSION['erro_acesso'] = $verificacao['mensagem'];
    header("Location: planos_usuarios.php");
    exit();
}

// Se tem alerta de vencimento próximo, guarda na sessão para exibir
if (isset($verificacao['tipo']) && $verificacao['tipo'] === 'alerta_vencimento') {
    $_SESSION['alerta_assinatura'] = $verificacao['mensagem'];
}

// Consulta para buscar o nome do usuário com base no ID
$query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = $usuario_id";
$resultado_nome = pg_query($conexao, $query_buscar_nome);

if ($resultado_nome && pg_num_rows($resultado_nome) > 0) {
    $row = pg_fetch_assoc($resultado_nome);
    $nome_usuario = $row['nome'];
} else {
    echo "Usuário não encontrado.";
}

// Verificar se o usuário tem edital selecionado
$query_edital = "
    SELECT ue.*, e.nome as edital_nome, e.orgao
    FROM appestudo.usuario_edital ue
    JOIN appestudo.edital e ON ue.edital_id = e.id_edital
    WHERE ue.usuario_id = $usuario_id
    ORDER BY ue.data_inscricao DESC
    LIMIT 1";
$result_edital = pg_query($conexao, $query_edital);
$edital_atual = pg_fetch_assoc($result_edital);

// Inicializa o PlanoEstudoLogic
$planoData = new PlanoEstudoLogic($conexao);
$prova_ativa = $planoData->getProvaAtiva();
$conteudos = $planoData->getConteudos();
$conteudos = filtrarTodosConteudos($conteudos);

// Calcula o progresso
$total_conteudos = count($conteudos);
$conteudos_estudados = 0;
foreach ($conteudos as $conteudo) {
    if ($conteudo['status_estudo'] === 'Estudado') {
        $conteudos_estudados++;
    }
}
$progresso = [
    'total' => $total_conteudos,
    'estudados' => $conteudos_estudados
];

?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cronograma Inteligente</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Quicksand:wght@300..700&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Tektur:wght@400..900&family=Varela+Round&display=swap" rel="stylesheet">
    <style>
:root {
    /* Cores modo claro (já existentes) */
    --primary: #00008B;
    --secondary: #f8f0e3;
    --accent: #4a4a4a;
    --border: #e0e0e0;
    --text: #2c3e50;
    --active: #00008B;
    --hover: #f5f5f5;
    --primary-blue: #00008B;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --background: #f5f5f5;
    --card-background: white;
}

/* Variáveis para modo escuro */
[data-theme="dark"] {
    --primary: #4169E1;          /* Azul royal mais claro */
    --secondary: #1a1a2e;        /* Azul escuro quase preto */
    --accent: #6c7293;          /* Cinza azulado */
    --border: #2d2d42;          /* Borda escura com tom azulado */
    --text: #e4e6f0;           /* Texto claro com tom azulado */
    --active: #4169E1;         /* Mesmo que primary */
    --hover: #232338;          /* Fundo hover com tom azulado */
    --primary-blue: #4169E1;    /* Mesmo que primary */
    --shadow-color: rgba(0, 0, 0, 0.3);
    --background: #0a0a1f;      /* Fundo principal escuro com tom azulado */
    --card-background: #13132b;  /* Fundo dos cards escuro com tom azulado */
}

        body {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
           
            color: var(--text);
            line-height: 1.6;
            margin: 0;
            min-height: 100vh;
            box-sizing: border-box;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
            box-sizing: border-box;
        }

        h1 {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            text-align: center;
            color: var(--primary);
            font-size: 2rem;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .status-card {
           
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 6px var(--shadow-color);
            margin: 0 20px 25px 20px;
            transition: transform 0.3s ease;
            border: 1px solid var(--border);
        }

        .status-title {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            color: var(--primary);
            font-size: 1.3rem;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid var(--border);
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: var(--primary);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-family: 'Quicksand', sans-serif;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            margin: 5px;
            flex: 1;
            min-width: 200px;
            max-width: 300px;
            box-sizing: border-box;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
            opacity: 0.9;
        }

        .btn-secondary {
            background: var(--accent);
        }

        .btn-secondary:hover {
            background: var(--accent);
            opacity: 0.9;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .info-item {
           
            padding: 15px;
            border-radius: 8px;
            border: 1px solid var(--border);
        }

        .info-item strong {
            color: var(--primary);
            font-family: 'Varela Round', 'Quicksand', sans-serif;
        }

        .progress-section {
            margin: 25px 0;
            padding: 20px;
            background: var(--card-background);
            border-radius: 10px;
            box-shadow: 0 2px 4px var(--shadow-color);
            border: 1px solid var(--border);
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: var(--hover);
            border-radius: 5px;
            overflow: hidden;
            border: 1px solid var(--border);
        }

        .progress {
            height: 100%;
            background: var(--primary);
            transition: width 0.3s ease;
        }

        .menu-icon {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary);
            border-radius: 50%;
            margin: 0 auto 20px;
            color: white;
            font-size: 1.5rem;
            box-shadow: 0 4px 6px var(--shadow-color);
        }

        .welcome-message {
            text-align: center;
            margin-bottom: 30px;
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            color: var(--text);
            font-size: 1.1rem;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .progress-text {
            color: var(--text);
            font-weight: 600;
        }

        .progress-percentage {
            color: var(--primary);
            font-weight: 700;
            font-size: 1.1rem;
        }

        .progress-stats {
            margin-top: 10px;
            text-align: center;
            font-size: 0.9rem;
            color: var(--text);
        }

        .alert-warning {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px 20px;
            margin: 20px 0;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 12px;
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            color: #856404;
        }

        .alert-warning i {
            color: #ffc107;
            font-size: 1.2rem;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            justify-content: center;
            margin-top: 20px;
            padding: 0 10px;
            width: 100%;
            box-sizing: border-box;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
          
            border-bottom: 1px solid var(--border);
            margin-bottom: 30px;
            border-radius: 0 0 10px 10px;  /* topo-esquerda topo-direita baixo-direita baixo-esquerda */
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo img {
            height: 50px;
            width: auto;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .user-info i {
            color: var(--primary);
            font-size: 1.2rem;
        }

        .user-name {
            color: var(--text);
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .container {
                padding: 0;
            }

            .status-card {
                margin: 0 10px 20px 10px;
                padding: 15px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
                padding: 0 10px;
            }
            
            .action-buttons {
                flex-direction: column;
                align-items: center;
                padding: 0 20px;
                margin: 20px 0;
                gap: 10px;
            }
            
            .btn {
                width: 100%;
                max-width: 100%;
                min-width: auto;
                margin: 0;
            }

            .header {
                padding: 10px;
            }

            .logo img {
                height: 40px;
            }

            .user-info {
                font-size: 0.8rem;
                padding: 6px 12px;
            }

            h1 {
                font-size: 1.5rem;
                padding: 0 10px;
            }

            .welcome-message {
                font-size: 1rem;
                padding: 0 10px;
            }
        }

        @media (max-width: 480px) {
            .status-card {
                margin: 0 5px 15px 5px;
                padding: 10px;
            }

            .btn {
                padding: 10px 20px;
                font-size: 0.9rem;
            }

            .info-item {
                padding: 10px;
                font-size: 0.9rem;
            }
        }
        .theme-toggle {
    margin-left: 15px;
}

.theme-btn {
    background: transparent;
    border: none;
    color: var(--primary);
    cursor: pointer;
    font-size: 1.2rem;
    padding: 8px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.theme-btn:hover {
    background: var(--hover);
}

/* Atualize estas classes para usar as variáveis */
body {
    background-color: var(--background);
}

.status-card {
    background: var(--card-background);
}

.header {
    background: var(--card-background);
}

.info-item {
    background: var(--hover);
}

.user-info {
    background: var(--hover);
}

/* Estilos para os logos */
.logo {
    position: relative;
}

.logo img {
    height: 50px;
    width: auto;
    transition: opacity 0.3s ease;
}

.logo-dark {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
}

/* Controle de visibilidade baseado no tema */
[data-theme="dark"] .logo-light {
    opacity: 0;
}

[data-theme="dark"] .logo-dark {
    opacity: 1;
}

.conquista-completa {
    text-align: center;
    padding: 30px;
    background: linear-gradient(135deg, var(--primary) 0%, #4a90e2 100%);
    color: white;
    border-radius: 12px;
    margin: 20px 0;
    animation: celebrar 2s ease-in-out;
    box-shadow: 0 4px 15px var(--shadow-color);
}

.conquista-header {
    margin-bottom: 25px;
}

.conquista-header .icone-grande {
    font-size: 4em;
    display: block;
    margin-bottom: 15px;
    animation: flutuar 2s ease-in-out infinite;
}

.conquista-header h2 {
    font-size: 2.2em;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
    font-family: 'Varela Round', 'Quicksand', sans-serif;
}

.conquista-mensagem {
    font-size: 1.1em;
    line-height: 1.6;
}

.conquista-destaque {
    font-size: 1.3em;
    font-weight: bold;
    margin: 20px 0;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

.conquista-detalhes {
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: 10px;
    margin-top: 25px;
}

.conquista-detalhes p {
    margin: 12px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    font-size: 1.1em;
}

.conquista-detalhes i {
    font-size: 1.3em;
}

@keyframes celebrar {
    0% { transform: scale(0.8); opacity: 0; }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes flutuar {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* Ajustes para modo escuro */
[data-theme="dark"] .conquista-completa {
    background: linear-gradient(135deg, #4169E1 0%, #1a1a2e 100%);
}

[data-theme="dark"] .conquista-detalhes {
    background: rgba(255,255,255,0.05);
}
    </style>
</head>
<body>
<div class="container">
<?php if (isset($_SESSION['sucesso_pagamento'])): ?>
        <div class="alert alert-success" style="text-align: center; margin: 20px auto; padding: 15px; background: #d4edda; border-radius: 5px; color: #155724; max-width: 800px;">
            <i class="fas fa-check-circle"></i>
            <?php 
            echo $_SESSION['sucesso_pagamento'];
            unset($_SESSION['sucesso_pagamento']);
            ?>
        </div>
    <?php endif; ?>

    <div class="header">
        <div class="header-left">
            <div class="logo">
            <img src="../logo/logo_vertical_azul.png" alt="Logo" class="logo-light">
            <img src="../logo/logo_vertical.png" alt="Logo" class="logo-dark">
            </div>
        </div>
        <div class="header-right">
            <div class="user-info">
                <i class="fas fa-user-circle"></i>
                <span class="user-name"><?php echo htmlspecialchars($nome_usuario); ?></span>
            </div>

            <div class="theme-toggle">
    <button id="theme-toggle-btn" class="theme-btn">
        <i class="fas fa-moon"></i>
    </button>
</div>
        </div>
    </div>

    <div class="menu-icon">
        <i class="fas fa-graduation-cap"></i>
    </div>
    <h1>Cronograma Inteligente
        <div class="welcome-message">
        Organize seus estudos de forma inteligente e eficiente
        </div>
    </h1>

    

    <?php if (!$edital_atual): ?>
        <div class="status-card">
            <div class="status-title">
                <i class="fas fa-flag"></i> Começando sua Jornada
            </div>
            <p>Para começar seus estudos, primeiro precisamos saber qual concurso você está se preparando.</p>
            <div class="action-buttons">
                <a href="selecionar_edital.php" class="btn">
                    <i class="fas fa-plus-circle"></i> Selecionar Edital
                </a>
            </div>
        </div>
    <?php else: ?>
        <div class="status-card">
            <div class="status-title">
                <i class="fas fa-book"></i> Edital Atual
            </div>
            <div class="info-grid">
                <div class="info-item">
                    <strong><i class="fas fa-building"></i> Concurso:</strong> 
                    <?= htmlspecialchars($edital_atual['edital_nome']) ?>
                </div>
                <div class="info-item">
                    <strong><i class="fas fa-landmark"></i> Órgão:</strong> 
                    <?= htmlspecialchars($edital_atual['orgao']) ?>
                </div>
                <div class="info-item">
                    <strong><i class="fas fa-calendar-check"></i> Selecionado em:</strong>
                    <?= date('d/m/Y', strtotime($edital_atual['data_inscricao'])) ?>
                </div>
            </div>

            <?php if (!$prova_ativa): ?>
                <div class="action-buttons">
                    <a href="selecionar_conteudos.php" class="btn">
                        <i class="fas fa-list-check"></i> Selecionar Conteúdos
                    </a>
                    <a href="selecionar_edital.php" class="btn btn-secondary">
                        <i class="fas fa-exchange-alt"></i> Trocar Edital
                    </a>
                </div>
            <?php else: ?>
                <div class="status-title">
                    <i class="fas fa-clipboard-check"></i> Prova Configurada
                </div>
                <div class="info-grid">
                    <div class="info-item">
                        <strong><i class="fas fa-file-alt"></i> Prova:</strong> 
                        <?= htmlspecialchars($prova_ativa['nome']) ?>
                    </div>
                    <div class="info-item">
                        <strong><i class="fas fa-calendar"></i> Data da Prova:</strong>
                        <?= date('d/m/Y', strtotime($prova_ativa['data_prova'])) ?>
                    </div>
                </div>

                <?php if ($progresso['total'] > 0): ?>
                    <?php if ($progresso['estudados'] === $progresso['total']): ?>
                        <div class="conquista-completa">
                            <div class="conquista-header">
                                <span class="icone-grande">🎓</span>
                                <h2>Parabéns, Mestre!</h2>
                            </div>
                            <div class="conquista-mensagem">
                                <p>Você completou todos os tópicos do seu plano de estudos!</p>
                                <p class="conquista-destaque">Este é um momento histórico na sua jornada de conhecimento.</p>
                                <div class="conquista-detalhes">
                                    <p><i class="fas fa-check-circle"></i> Total de tópicos dominados: <?= $progresso['total'] ?></p>
                                    <p><i class="fas fa-star"></i> Nível alcançado: Mestre Supremo</p>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="progress-section">
                            <div class="progress-info">
                                <span class="progress-text">
                                    <i class="fas fa-chart-line"></i> Progresso Geral
                                </span>
                                <span class="progress-percentage">
                                    <?= number_format(($progresso['estudados']/$progresso['total']*100), 2) ?>%
                                </span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress" style="width: <?= ($progresso['estudados']/$progresso['total']*100) ?>%"></div>
                            </div>
                            <div class="progress-stats">
                                <i class="fas fa-check-circle"></i>
                                <span><strong><?= $progresso['estudados'] ?></strong> de <strong><?= $progresso['total'] ?></strong> conteúdos estudados</span>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>

<?php
    $data_prova = new DateTime($prova_ativa['data_prova']);
    $hoje = new DateTime();
    
    // Zera as horas para comparação apenas das datas
    $data_prova->setTime(0, 0, 0);
    $hoje->setTime(0, 0, 0);
    
    // Calcula a diferença em dias
    $interval = $hoje->diff($data_prova);
    $dias_para_prova = $interval->invert ? 0 : ($interval->days + 1); // Adiciona 1 para incluir o dia atual
    
    if ($dias_para_prova <= 7): ?>
        <div class="alert-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>Atenção:</strong> Faltam apenas <?= $dias_para_prova ?> dias para sua prova!
        </div>
    <?php endif; ?>

                <div class="action-buttons">
                    <a href="plano_estudo_inteligente.php" class="btn">
                        <i class="fas fa-tasks"></i> Ver Plano de Estudos
                    </a>
                    <a href="selecionar_edital.php" class="btn btn-secondary">
                        <i class="fas fa-exchange-alt"></i> Trocar Edital
                    </a>
                </div>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>

<script>
document.addEventListener('DOMContentLoaded', () => {
    const themeToggleBtn = document.getElementById('theme-toggle-btn');
    const themeIcon = themeToggleBtn.querySelector('i');
    
    // Verifica se há preferência salva
    const currentTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', currentTheme);
    updateIcon(currentTheme);
    
    themeToggleBtn.addEventListener('click', () => {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        updateIcon(newTheme);
    });
    
    function updateIcon(theme) {
        if (theme === 'dark') {
            themeIcon.classList.remove('fa-moon');
            themeIcon.classList.add('fa-sun');
        } else {
            themeIcon.classList.remove('fa-sun');
            themeIcon.classList.add('fa-moon');
        }
    }
});
</script>

</body>
</html>