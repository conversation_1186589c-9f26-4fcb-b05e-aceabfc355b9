<?php
//get_revisoes_ignoradas.php
session_start();
include_once("assets/config.php");

if (!isset($_SESSION['idusuario'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Usuário não autenticado']);
    exit();
}

$usuario_id = $_SESSION['idusuario'];

// Primeiro busca o edital ativo do usuário
$query_edital_ativo = "
    SELECT ue.edital_id 
    FROM appestudo.usuario_edital ue
    WHERE ue.usuario_id = $usuario_id
    ORDER BY ue.data_inscricao DESC
    LIMIT 1";

$result_edital = pg_query($conexao, $query_edital_ativo);

if (!$result_edital || pg_num_rows($result_edital) == 0) {
    // Se não houver edital ativo, retorna array vazio
    header('Content-Type: application/json');
    echo json_encode([]);
    exit();
}

$edital_atual = pg_fetch_assoc($result_edital);
$edital_id = $edital_atual['edital_id'];

$query = "
    SELECT 
        r.id, 
        r.conteudo_id, 
        r.nivel_revisao, 
        r.confianca, 
        r.status_revisao,
        r.ultima_revisao,
        ce.descricao as conteudo,
        ce.capitulo,
        m.nome as materia,
        m.cor,
        -- Hierarquia Nível 1 (Principal)
        CASE 
            WHEN strpos(ce.capitulo, '.') > 0 THEN 
                (SELECT tn.capitulo || '. ' || tn.descricao
                 FROM appestudo.conteudo_edital tn
                 WHERE tn.materia_id = ce.materia_id
                 AND tn.capitulo = split_part(ce.capitulo, '.', 1)
                 LIMIT 1)
            ELSE NULL
        END as descricao_capitulo_principal,
        
        -- Hierarquia Nível 2 (Secundário)
        CASE 
            WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 2 THEN 
                (SELECT cp.capitulo || '. ' || cp.descricao
                 FROM appestudo.conteudo_edital cp
                 WHERE cp.materia_id = ce.materia_id
                 AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || split_part(ce.capitulo, '.', 2)
                 LIMIT 1)
            ELSE NULL
        END as descricao_capitulo_secundario,
        
        -- Hierarquia Nível 3 (Terciário)
        CASE 
            WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 3 THEN 
                (SELECT cp.capitulo || '. ' || cp.descricao
                 FROM appestudo.conteudo_edital cp
                 WHERE cp.materia_id = ce.materia_id
                 AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || 
                     split_part(ce.capitulo, '.', 2) || '.' ||
                     split_part(ce.capitulo, '.', 3)
                 LIMIT 1)
            ELSE NULL
        END as descricao_capitulo_terciario,
        
        -- Hierarquia Nível 4 (Quaternário)
        CASE 
            WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 4 THEN 
                (SELECT cp.capitulo || '. ' || cp.descricao
                 FROM appestudo.conteudo_edital cp
                 WHERE cp.materia_id = ce.materia_id
                 AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || 
                     split_part(ce.capitulo, '.', 2) || '.' ||
                     split_part(ce.capitulo, '.', 3) || '.' ||
                     split_part(ce.capitulo, '.', 4)
                 LIMIT 1)
            ELSE NULL
        END as descricao_capitulo_quaternario,

        -- Hierarquia Nível 5 (Quinário)
        CASE 
            WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 5 THEN 
                (SELECT cp.capitulo || '. ' || cp.descricao
                 FROM appestudo.conteudo_edital cp
                 WHERE cp.materia_id = ce.materia_id
                 AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || 
                     split_part(ce.capitulo, '.', 2) || '.' ||
                     split_part(ce.capitulo, '.', 3) || '.' ||
                     split_part(ce.capitulo, '.', 4) || '.' ||
                     split_part(ce.capitulo, '.', 5)
                 LIMIT 1)
            ELSE NULL
        END as descricao_capitulo_quinario,
        
        -- Hierarquia Nível 6 (Sexto)
        CASE 
            WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 6 THEN 
                (SELECT cp.capitulo || '. ' || cp.descricao
                 FROM appestudo.conteudo_edital cp
                 WHERE cp.materia_id = ce.materia_id
                 AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || 
                     split_part(ce.capitulo, '.', 2) || '.' ||
                     split_part(ce.capitulo, '.', 3) || '.' ||
                     split_part(ce.capitulo, '.', 4) || '.' ||
                     split_part(ce.capitulo, '.', 5) || '.' ||
                     split_part(ce.capitulo, '.', 6)
                 LIMIT 1)
            ELSE NULL
        END as descricao_capitulo_sexto,
        
        -- Hierarquia Nível 7 (Sétimo)
        CASE 
            WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 7 THEN 
                (SELECT cp.capitulo || '. ' || cp.descricao
                 FROM appestudo.conteudo_edital cp
                 WHERE cp.materia_id = ce.materia_id
                 AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || 
                     split_part(ce.capitulo, '.', 2) || '.' ||
                     split_part(ce.capitulo, '.', 3) || '.' ||
                     split_part(ce.capitulo, '.', 4) || '.' ||
                     split_part(ce.capitulo, '.', 5) || '.' ||
                     split_part(ce.capitulo, '.', 6) || '.' ||
                     split_part(ce.capitulo, '.', 7)
                 LIMIT 1)
            ELSE NULL
        END as descricao_capitulo_setimo
    FROM appestudo.revisoes r
    JOIN appestudo.conteudo_edital ce ON r.conteudo_id = ce.id_conteudo
    JOIN appestudo.materia m ON ce.materia_id = m.idmateria
    WHERE r.usuario_id = $usuario_id
    AND ce.edital_id = $edital_id -- Adicionado filtro por edital_id
    AND r.status_revisao IN ('ignorado', 'ignorado_permanente')
    ORDER BY r.ultima_revisao DESC";

$result = pg_query($conexao, $query);

if (!$result) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Erro ao buscar revisões ignoradas']);
    exit();
}

$revisoes = array();
while ($row = pg_fetch_assoc($result)) {
    $revisoes[] = $row;
}

header('Content-Type: application/json');
echo json_encode($revisoes);
?>