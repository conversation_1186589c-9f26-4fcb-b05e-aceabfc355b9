/* Estilos adicionais para mensagens e botões */
.erro-mensagem {
  background-color: #fee2e2;
  border: 1px solid #ef4444;
  color: #991b1b;
  padding: 1rem;
  margin: 1rem;
  border-radius: 0.5rem;
  text-align: center;
}

.sucesso-mensagem {
  background-color: #dcfce7;
  border: 1px solid #22c55e;
  color: #166534;
  padding: 1rem;
  margin: 1rem;
  border-radius: 0.5rem;
  text-align: center;
}

.botoes-container {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
}

.btn-padrao {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-weight: 600;
  font-family: 'Quicksand', sans-serif;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-padrao:hover {
  background-color: #e5e7eb;
  transform: translateY(-2px);
}

/* Estilos para inputs com erro */
.input-error {
  border-color: #ef4444 !important;
  background-color: #fef2f2 !important;
}

.input-error:focus {
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2) !important;
}

/* Animações */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.erro-mensagem,
.sucesso-mensagem {
  animation: fadeIn 0.3s ease-out;
}

/* Responsividade para botões */
@media (max-width: 640px) {
  .botoes-container {
      flex-direction: column;
      padding: 0 1rem;
  }

  .btn-padrao,
  .btn-salvar {
      width: 100%;
      justify-content: center;
  }
}