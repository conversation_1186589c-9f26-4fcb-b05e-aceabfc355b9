:root {
  /* <PERSON>s principais */
  --primary-color: #000080;
  --paper-color: #FFFFFF;
  --secondary-color: #4169E1;
  --background-color: #E8ECF3;
  --text-color: #2C3345;
  --border-color: #B8C2CC;
  --hover-color: #f0f5ff;
  --shadow-color: rgba(0, 0, 128, 0.1);
  --success-color: #2e7d32;
  --error-color: #b71c1c;
  --warning-color: #f0ad4e;
  --card-background: white;
  
  /* Variáveis do cabeçalho */
  --primary: #00008B;
  --text: #2C3345;
  --hover: #f5f5f5;
  --shadow: rgba(0, 0, 128, 0.1);
  --border: #B8C2CC;
}

/* Variáveis para modo escuro */
[data-theme="dark"] {
  --primary-color: #4169E1;
  --paper-color: #1a1a2e;
  --secondary-color: #6c7293;
  --background-color: #0a0a1f;
  --text-color: #e4e6f0;
  --border-color: #2d2d42;
  --hover-color: #232338;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --success-color: #81c784;
  --error-color: #ef5350;
  --warning-color: #ffb74d;
  --card-background: #13132b;
  
  /* Variáveis do cabeçalho */
  --primary: #4169E1;
  --text: #e4e6f0;
  --hover: #232338;
  --shadow: rgba(0, 0, 0, 0.3);
  --border: #2d2d42;
}

body {
  background-color: var(--background-color);
  font-family: 'Nunito', 'Quicksand', 'Varela Round', sans-serif;
  color: var(--text-color);
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

.container {
  max-width: 1500px;
  margin: 0 auto;
  padding: 40px 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 2rem;
  background-color: var(--paper-color);
  box-shadow: 0 3px 8px var(--shadow-color);
  margin-bottom: 2rem;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  z-index: 100;
}

.header-left, .header-right {
  display: flex;
  align-items: center;
  height: 60px;
}

.logo {
  display: flex;
  align-items: center;
  height: 60px;
  padding: 5px 0;
}

.logo img {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
  transition: opacity 0.3s ease;
}

/* Show/hide logo based on theme */
.logo-light {
  display: block;
  opacity: 1;
}

.logo-dark {
  display: none;
  opacity: 0;
}

[data-theme="dark"] .logo-light {
  display: none;
  opacity: 0;
}

[data-theme="dark"] .logo-dark {
  display: block;
  opacity: 1;
}

.user-info {
  display: flex;
  align-items: center;
  margin-right: 1.5rem;
  background-color: rgba(0, 0, 128, 0.08);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  transition: all 0.3s ease;
}

[data-theme="dark"] .user-info {
  background-color: rgba(65, 105, 225, 0.15);
}

.user-info:hover {
  background-color: rgba(0, 0, 128, 0.12);
}

[data-theme="dark"] .user-info:hover {
  background-color: rgba(65, 105, 225, 0.25);
}

.user-info i {
  font-size: 1.5rem;
  margin-right: 0.8rem;
  color: var(--primary-color);
  transition: color 0.3s ease;
}

.user-name {
  font-weight: 600;
  color: var(--text-color);
  transition: color 0.3s ease;
}

.theme-toggle {
  margin-left: 1rem;
  position: relative;
}

.theme-btn {
  background-color: var(--paper-color);
  border: 2px solid var(--primary-color);
  font-size: 1.2rem;
  color: var(--primary-color);
  cursor: pointer;
  padding: 0.6rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px var(--shadow-color);
}

[data-theme="dark"] .theme-btn {
  color: var(--warning-color);
  border-color: var(--secondary-color);
  background-color: var(--hover-color);
}

.theme-btn:hover {
  background-color: var(--hover-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

[data-theme="dark"] .theme-btn:hover {
  background-color: rgba(255, 183, 77, 0.15);
  color: var(--warning-color);
}


/* Conteúdo principal */
.clean-container {
  background-color: var(--paper-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px var(--shadow-color);
  padding: 30px;
  margin-bottom: 30px;
  position: relative;
  overflow: hidden;
}

.clean-header {
  background: var(--primary-color);
  color: var(--paper-color);
  margin: -30px -30px 30px -30px;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 3px solid var(--secondary-color);
}

.clean-header h1 {
  font-family: 'Varela Round', 'Quicksand', sans-serif;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}

/* Estilos para navegação */
.breadcrumb {
  font-family: 'Nunito', sans-serif;
  color: var(--primary-color);
  margin-bottom: 25px;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.breadcrumb i {
  font-size: 0.9rem;
  opacity: 0.7;
}

.btn-back {
  position: fixed;
  top: 102px;
  left: 20px;
  width: 40px;
  height: 40px;
  background: var(--paper-color);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  text-decoration: none;
  font-size: 1rem;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: 0 2px 6px var(--shadow-color);
}

.btn-back:hover {
  transform: translateY(-2px);
  background: var(--primary-color);
  color: white;
  box-shadow: 0 4px 8px var(--shadow-color);
}

/* Elementos de ação e informação */
.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.header-info {
  color: var(--text-color);
  opacity: 0.8;
  font-size: 0.95rem;
  font-style: italic;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-info i {
  color: var(--secondary-color);
}

/* Botões */
.btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-family: 'Nunito', 'Quicksand', sans-serif;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 4px var(--shadow-color);
}

.btn:hover {
  background: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

.btn-secondary {
  background: var(--paper-color);
  color: var(--primary-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--hover-color);
  color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-success {
  background: var(--success-color);
}

.btn-success:hover {
  background: #43a047;
}

/* Tabela */
.clean-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  background: var(--paper-color);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px var(--shadow-color);
}

.clean-table th,
.clean-table td {
  padding: 15px;
  border-bottom: 1px solid var(--border-color);
  text-align: left;
}

.clean-table th {
  background-color: rgba(0, 0, 128, 0.05);
  font-weight: 600;
  color: var(--primary-color);
  font-family: 'Nunito', sans-serif;
  letter-spacing: 0.3px;
  position: sticky;
  top: 0;
  z-index: 10;
}

[data-theme="dark"] .clean-table th {
  background-color: rgba(65, 105, 225, 0.1);
}

/* Coluna de numeração */
.number-column {
  width: 60px;
  min-width: 60px;
  position: sticky;
  left: 0;
  text-align: center;
  background-color: rgba(0, 0, 128, 0.05);
  z-index: 11;
  border-right: 1px solid var(--border-color);
  font-family: 'Varela Round', sans-serif;
  font-weight: 600;
}

.clean-table tbody td.number-column {
  background-color: var(--paper-color);
  color: var(--primary-color);
  z-index: 11;
}

/* Coluna de ações */
.clean-table td:last-child,
.clean-table th:last-child {
  position: sticky;
  right: 0;
  background: var(--paper-color);
  z-index: 11;
  border-left: 1px solid var(--border-color);
}

.clean-table th:last-child {
  background: rgba(0, 0, 128, 0.05);
  z-index: 12;
}

/* Efeitos de hover */
.clean-table tbody tr:hover {
  background-color: var(--hover-color);
}

.clean-table tr:hover td.number-column,
.clean-table tr:hover td:last-child {
  background-color: var(--hover-color);
}

/* Ações na tabela */
.table-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-start;
}

.table-actions .btn {
  padding: 8px 16px;
  font-size: 0.9rem;
}

/* Badges de estatísticas */
.stats-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 10px;
  border-radius: 20px;
  background: rgba(0, 0, 128, 0.05);
  font-size: 0.9rem;
  margin-right: 10px;
  white-space: nowrap;
  color: var(--text-color);
  font-family: 'Nunito', sans-serif;
}

.stats-badge i {
  color: var(--secondary-color);
}

[data-theme="dark"] .stats-badge {
  background: rgba(65, 105, 225, 0.1);
}

/* Estado vazio */
.empty-state {
  text-align: center;
  padding: 60px 40px;
  background: var(--paper-color);
  border-radius: 8px;
  border: 1px dashed var(--border-color);
  margin-top: 20px;
}

.empty-state i {
  font-size: 3rem;
  color: var(--secondary-color);
  opacity: 0.7;
  margin-bottom: 25px;
}

.empty-state p {
  font-size: 1.1rem;
  color: var(--text-color);
  margin-bottom: 25px;
}

/* Loading spinner */
.loader {
  border: 4px solid rgba(65, 105, 225, 0.2);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin: 30px auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Transições Vue */
.fade-enter-active, .fade-leave-active {
  transition: opacity .5s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}

/* Controle de tema */
[data-theme="dark"] .btn-secondary {
  color: var(--text-color);
}

[data-theme="dark"] .logo-light {
  opacity: 0;
}

[data-theme="dark"] .logo-dark {
  opacity: 1;
}

/* Responsividade */
@media (max-width: 768px) {
  .container {
      padding: 20px 15px;
      padding-top: 60px;
  }
  
  .clean-header {
      padding: 15px 20px;
  }
  
  .header-actions {
      flex-direction: column;
      align-items: flex-start;
      gap: 15px;
  }
  
  .clean-table {
      display: block;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
  }
  
  .clean-table th,
  .clean-table td {
      padding: 12px;
  }
  
  .clean-table td:nth-child(2) {
      min-width: 200px;
  }
  
  /* Sombras nas colunas fixas no mobile */
  .number-column {
      box-shadow: 2px 0 4px rgba(0, 0, 128, 0.1);
  }
  
  .clean-table td:last-child {
      box-shadow: -2px 0 4px rgba(0, 0, 128, 0.1);
  }
  
  .btn-back {
      top: 10px;
      left: 10px;
      width: 36px;
      height: 36px;
      font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .clean-container {
      padding: 20px;
  }
  
  .clean-header {
      margin: -20px -20px 20px -20px;
  }
  
  .clean-table th,
  .clean-table td {
      padding: 10px;
  }
  
  .stats-badge {
      font-size: 0.8rem;
      padding: 3px 8px;
  }
  
  .table-actions {
      flex-direction: column;
      gap: 6px;
  }
  
  .table-actions .btn {
      width: 100%;
      justify-content: center;
      padding: 6px 12px;
      font-size: 0.85rem;
  }
}