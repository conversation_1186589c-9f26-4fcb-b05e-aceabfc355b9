<?php
// confirmar_uso.php
require_once 'includes/functions.php';
require_once 'includes/registro.php';

// Verificar se o ID foi fornecido
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: index.php");
    exit();
}

// Instanciar objeto
$registro = new Registro();

// Obter dados do registro
$item = $registro->obter($_GET['id']);

if (!$item) {
    header("Location: index.php");
    exit();
}

// Se o registro já foi confirmado, redirecionar
if ($item['confirmado']) {
    header("Location: visualizar_medicamento.php?id=" . $item['medicamento_id']);
    exit();
}

// Verificar se o formulário foi enviado
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Definir observação
    $registro->observacao = $_POST['observacao'] ?? null;
    
    // Confirmar o uso
    if ($registro->confirmar()) {
        // Redirecionar com mensagem de sucesso
        header("Location: index.php?success=3");
        exit();
    } else {
        $erro = "Ocorreu um erro ao confirmar o uso.";
    }
}

// Formatar data e hora para exibição
$data_hora_formatada = date('d/m/Y H:i', strtotime($item['data_hora']));
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Confirmar Uso - Controle de Medicamentos</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="container my-4">
        <header class="mb-4">
            <h1 class="text-center">Confirmar Uso de Medicamento</h1>
        </header>
        
        <?php if (isset($erro)): ?>
            <div class="alert alert-danger"><?= $erro ?></div>
        <?php endif; ?>
        
        <div class="card">
            <div class="card-body">
                <div class="mb-4">
                    <h3><?= htmlspecialchars($item['nome']) ?></h3>
                    <p><strong>Dosagem:</strong> <?= htmlspecialchars($item['dosagem']) ?></p>
                    <p><strong>Horário Previsto:</strong> <?= $data_hora_formatada ?></p>
                </div>
                
                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="observacao" class="form-label">Observações (opcional)</label>
                        <textarea class="form-control" id="observacao" name="observacao" rows="3" 
                                  placeholder="Registre aqui qualquer informação adicional, como efeitos colaterais, etc."></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="<?= $_SERVER['HTTP_REFERER'] ?? 'index.php' ?>" class="btn btn-secondary">Voltar</a>
                        <button type="submit" class="btn btn-success">Confirmar Uso</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>