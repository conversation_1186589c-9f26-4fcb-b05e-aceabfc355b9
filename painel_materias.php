<?php
session_start();

include_once 'conexao_POST.php'; // Fornece $conexao
include_once 'includes/auth.php';   // Fornece verificarAutenticacao()

// Verifica a autenticação do usuário. Se não for válido, o script é encerrado em verificarAutenticacao().
verificarAutenticacao($conexao);

// Define a constante para permitir a inclusão segura de processa_index.php
define('MEU_SISTEMA_PHP_EXECUCAO_VALIDA', true);
// processa_index.php usa $_SESSION['idusuario'] (já validado) e define $id_planejamento.
// Ele também lida com o caso de planejamento não encontrado, encerrando o script se necessário.
include_once 'processa_index.php';
include_once 'consulta_banco_ultima_proxima.php';


$query_consultar_materias_planejamento = "
    SELECT 
        pm.materia_idmateria AS id,
        m.nome AS nome,
        m.cor AS cor,
        a.detalhe AS detalhe
    FROM appEstudo.planejamento_materia pm
    INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    LEFT JOIN appEstudo.anotacao a ON pm.planejamento_idplanejamento = a.planejamento_idplanejamento 
        AND pm.materia_idmateria = a.materia_idmateria 
        AND a.planejamento_usuario_idusuario = $1
    WHERE pm.planejamento_idplanejamento = $2";

$result = pg_query_params($conexao, $query_consultar_materias_planejamento, 
    array($id_usuario, $id_planejamento));
if (!$result) {
    die("Erro na consulta: " . pg_last_error($conexao));
}

$materias = array();
while ($materia = pg_fetch_assoc($result)) {
    $materias[] = $materia;
}
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minhas Matérias</title>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Quicksand:wght@300..700&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Tektur:wght@400..900&family=Varela+Round&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <style>
        :root {
            /* Cores modo claro (já existentes) */
            --primary: #00008B;
            --primary-light: #0000CD;
            --secondary: #f8f0e3;
            --accent: #4a4a4a;
            --border: #e0e0e0;
            --text: #2c3e50;
            --active: #00008B;
            --hover: #f5f5f5;
            --primary-blue: #00008B;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --background: #f5f5f5;
            --card-background: white;
        }
        [data-theme="dark"] {
            --primary: #4169E1;
            --secondary: #1a1a2e;
            --accent: #6c7293;
            --border: #2d2d42;
            --text: #e4e6f0;
            --active: #4169E1;
            --hover: #232338;
            --primary-blue: #4169E1;
            --shadow-color: rgba(0, 0, 0, 0.3);
            --background: #0a0a1f;
            --card-background: #13132b;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            color: var(--text);
            line-height: 1.6;
            margin: 0;
            min-height: 100vh;
            box-sizing: border-box;
            background-color: var(--border);
        }

        body::before {
            content: "";
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('lei_seca/img/ConcurseiroOff_v1_2.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            opacity: 0.15;
            z-index: -1;
            pointer-events: none;
        }

        .page-header {
            max-width: 1200px;
            margin: 0 auto 3rem;
            text-align: center;
            position: relative;
            padding-bottom: 2rem;
        }

        .page-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 3px;
            background: var(--elegant-border);
        }

        .page-title {
            font-family: 'Cinzel', serif;
            font-size: 2.5rem;
            color: var(--burgundy);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 2rem;
            padding: 1rem;
        }

        .card {
            background: white;
            border-radius: 12px;
            box-shadow:
                    0 0 0 1px var(--vintage-gold),
                    0 0 0 8px white,
                    0 0 0 9px var(--vintage-gold),
                    0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow:
                    0 0 0 1px var(--vintage-gold),
                    0 0 0 8px white,
                    0 0 0 9px var(--vintage-gold),
                    0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .card-header {
            padding: 2rem;
            text-align: center;
            position: relative;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            pointer-events: none;
        }

        .card h3 {
            font-family: 'Cinzel', serif;
            font-size: 1.25rem;
            font-weight: 700;
            color: white;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 1;
        }

        .destacada1 {
            position: relative;
        }

        .destacada1::before {
            content: '★ Próxima';
            position: absolute;
            top: -0.5rem;
            right: -0.5rem;
            background: var(--vintage-gold);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0;
            font-size: 0.8rem;
            font-weight: 600;
            z-index: 2;
            transform: rotate(45deg) translateX(25%);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            font-family: 'Quicksand', sans-serif;
        }

        @media (max-width: 768px) {
            .page-title {
                font-size: 2rem;
            }

            .container {
                grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
                gap: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 1rem;
            }

            .page-title {
                font-size: 1.5rem;
            }

            .container {
                grid-template-columns: 1fr;
            }
        }
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid var(--border);
            margin-bottom: 30px;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 2px 4px var(--shadow-color);
            background: var(--card-background);
        }
        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        .logo img {
            height: 50px;
            width: auto;
            transition: opacity 0.3s ease;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            background: var(--hover);
        }

        .user-info i {
            color: var(--primary);
            font-size: 1.2rem;
        }

        .user-name {
            color: var(--text);
            font-weight: 600;
        }
        .header_centro {
            text-align: center;
            margin-bottom: 1rem;
        }

        .header_centro h1 {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            color: var(--primary);
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .header_centro p {
            color: #666;
            font-size: 1.1rem;
        }

        .theme-toggle {
            margin-left: 15px;
        }

        .theme-btn {
            background: transparent;
            border: none;
            color: var(--primary);
            cursor: pointer;
            font-size: 1.2rem;
            padding: 8px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .theme-btn:hover {
            background: var(--hover);
        }

        .back-button {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: var(--primary);
            color: white;
            padding: 1rem;
            border-radius: 50%;
            box-shadow: 0 2px 4px var(--houver);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .back-button:hover {
            transform: scale(1.1);
        }

    </style>
</head>
<body>
<div class="header">
            <div class="header-left">
                <div class="logo">
                    <img src="./logo/logo_vertical_azul.png" alt="Logo" class="logo-light">
                    <img src="./logo/logo_vertical.png" alt="Logo" class="logo-dark">
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span class="user-name"><?php echo htmlspecialchars($nome_usuario); ?></span>
                </div>
                <div class="theme-toggle">
                    <button id="theme-toggle-btn" class="theme-btn">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>

        <header class="header_centro">
            <div class="menu-icon">
                <i class="fas fa-sticky-note fa-3x" style="color: var(--primary); margin-bottom: 15px;"></i>
            </div>
            <h1>Anotações das Matérias</h1>
            <p>Visível Somente Matérias que Estão no Seu Planejamento</p>
        </header>
        <div class="back-button" onclick="window.close()" title="Fechar Janela">
            <i class="fas fa-times"></i>
        </div>
</header>

<div class="container">
    <?php foreach ($materias as $materia): ?>
        <?php $destacadaClass = (isset($proximaMateriaEstudada) && $proximaMateriaEstudada === $materia['nome']) ? 'destacada1' : ''; ?>
        <form method="POST" action="painel_editar_materias.php" style="display: contents;">
            <input type="hidden" name="id_materia" value="<?= $materia['id'] ?>">
            <input type="hidden" name="id_planejamento" value="<?= $id_planejamento ?>">
            <div class="card <?= $destacadaClass ?>" onclick="this.closest('form').submit();">
                <div class="card-header" style="background-color: <?= $materia['cor'] ?>;">
                    <h3><?= htmlspecialchars($materia['nome']) ?></h3>
                </div>
            </div>
        </form>
    <?php endforeach; ?>
</div>
</body>
</html>
