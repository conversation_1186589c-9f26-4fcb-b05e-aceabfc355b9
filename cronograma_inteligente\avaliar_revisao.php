<?php
//avaliar_revisao.php

session_start();
require_once 'includes/verificar_modulo.php';
require_once 'includes/mensagens.php';
verificarModulo();
include_once("assets/config.php");

if (!isset($_SESSION['idusuario'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Usuário não autenticado']);
    exit();
}

$data = json_decode(file_get_contents('php://input'), true);
$revisao_id = $data['revisao_id'];
$confianca = $data['confianca'];

// Calcula próximo nível e intervalo baseado na confiança
function calcularProximaRevisao($nivelAtual, $confianca) {
    $intervalos = [
        1 => '1 day',
        2 => '3 days',
        3 => '7 days',
        4 => '14 days',
        5 => '30 days'
    ];

    $proximoNivel = $nivelAtual;
    if ($confianca >= 4) {
        $proximoNivel = min($nivelAtual + 1, 5);
    } elseif ($confianca <= 2) {
        $proximoNivel = max($nivelAtual - 1, 1);
    }

    return [
        'nivel' => $proximoNivel,
        'intervalo' => $intervalos[$proximoNivel]
    ];
}

// Busca nível atual
$query = "SELECT nivel_revisao FROM appestudo.revisoes WHERE id = $revisao_id";
$result = pg_query($conexao, $query);
$row = pg_fetch_assoc($result);
$nivelAtual = $row['nivel_revisao'];

// Calcula próxima revisão
$proxima = calcularProximaRevisao($nivelAtual, $confianca);

// Atualiza a revisão
$query = "
    UPDATE appestudo.revisoes 
    SET 
        status_revisao = 'concluida',
        confianca = $confianca,
        nivel_revisao = {$proxima['nivel']},
        ultima_revisao = CURRENT_TIMESTAMP,
        proxima_revisao = CURRENT_TIMESTAMP + INTERVAL '{$proxima['intervalo']}'
    WHERE id = $revisao_id
    RETURNING *";

$result = pg_query($conexao, $query);

if (!$result) {
    echo json_encode(['success' => false, 'error' => 'Erro ao atualizar revisão']);
    exit();
}

echo json_encode([
    'success' => true,
    'proxima_revisao' => $proxima
]);
?>