<?php
session_start();
if (!isset($_SESSION['idusuario']) || $_SESSION['idusuario'] != 1) {
    header("Location: login.php");
    exit();
}

include_once("assets/config.php");

$usuario_id = $_SESSION['idusuario'];

// Consulta para o progresso de Revisão por matéria
$query_revisao = "
    SELECT 
        m.idmateria,
        m.nome AS materia_nome,
        m.cor,
        COUNT(*) AS total_conteudos,
        SUM(CASE WHEN uc.status_revisao = 'Concluído' THEN 1 ELSE 0 END) AS concluido_revisao,
        SUM(CASE WHEN uc.status_revisao = 'Em Andamento' THEN 1 ELSE 0 END) AS em_andamento_revisao,
        SUM(CASE WHEN uc.status_revisao = 'Não Iniciado' THEN 1 ELSE 0 END) AS nao_iniciado_revisao
    FROM 
        appestudo.usuario_conteudo AS uc
    JOIN 
        appestudo.conteudo_edital AS ce ON uc.conteudo_id = ce.id_conteudo
    JOIN 
        appestudo.materia AS m ON ce.materia_id = m.idmateria
    WHERE 
        uc.usuario_id = $usuario_id
    GROUP BY 
        m.idmateria, m.nome, m.cor
    ORDER BY 
        m.nome;
";

$result_revisao = pg_query($conexao, $query_revisao);
$progresso_revisao = [];
while ($row = pg_fetch_assoc($result_revisao)) {
    $progresso_revisao[$row['idmateria']] = $row;
}

// Consulta para o progresso de Estudo por matéria
$query_estudo = "
    SELECT 
        m.idmateria,
        m.nome AS materia_nome,
        m.cor,
        COUNT(*) AS total_conteudos,
        SUM(CASE WHEN uc.status_estudo = 'Estudado' THEN 1 ELSE 0 END) AS estudado,
        SUM(CASE WHEN uc.status_estudo = 'Estudando' THEN 1 ELSE 0 END) AS estudando,
        SUM(CASE WHEN uc.status_estudo = 'Não Estudado' THEN 1 ELSE 0 END) AS nao_estudado
    FROM 
        appestudo.usuario_conteudo AS uc
    JOIN 
        appestudo.conteudo_edital AS ce ON uc.conteudo_id = ce.id_conteudo
    JOIN 
        appestudo.materia AS m ON ce.materia_id = m.idmateria
    WHERE 
        uc.usuario_id = $usuario_id
    GROUP BY 
        m.idmateria, m.nome, m.cor
    ORDER BY 
        m.nome;
";

$result_estudo = pg_query($conexao, $query_estudo);
$progresso_estudo = [];
while ($row = pg_fetch_assoc($result_estudo)) {
    $progresso_estudo[$row['idmateria']] = $row;
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Progresso Geral</title>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier Prime', monospace;
            min-height: 100vh;
            background: linear-gradient(135deg, #8B0000, #B22222);
            padding: 40px 20px;
            line-height: 1.6;
            color: #2c1810;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header-vintage {
            text-align: center;
            color: #fff;
            margin-bottom: 40px;
        }

        .header-vintage h2 {
            font-family: 'Cinzel', serif;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 2px;
            margin-bottom: 10px;
        }

        .header-vintage p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
        }

        .section-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: linear-gradient(to right, #8B0000, #B22222);
            color: #fff;
            border-radius: 10px;
            margin: 30px 0 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .section-title:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .section-title i {
            color: #fff;
            margin-right: 10px;
        }

        .section-content {
            display: none;
        }

        .section-content.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        .progresso-materia {
            background: #fff;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .progresso-materia:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
        }

        .materia-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            padding-right: 50px; /* Aumentado para dar mais espaço */
        }

        .materia-info {
            display: flex;
            align-items: center;
            gap: 15px;
            flex: 1; /* Permite que o elemento ocupe o espaço disponível */
        }

        .materia-nome {
            font-family: 'Cinzel', serif;
            font-size: 1.3rem;
            color: #2c1810;
            margin: 0;
        }

        .toggle-icon {
            position: absolute;
            right: 15px; /* Aumentado para afastar da borda */
            top: 50%;
            transform: translateY(-50%) rotate(-90deg);
            transition: transform 0.3s ease;
        }

        .progresso-materia.expanded .toggle-icon {
            transform: translateY(-50%) rotate(0);
        }

        .materia-detalhes {
            max-height: 0;
            overflow: hidden;
            transition: all 0.3s ease-out;
            opacity: 0;
        }

        .progresso-materia.expanded .materia-detalhes {
            max-height: 500px;
            opacity: 1;
            margin-top: 20px;
        }

        .progress-bar {
            height: 10px;
            background: #f4f1ea;
            border-radius: 5px;
            overflow: hidden;
            display: flex;
            margin: 15px 0;
        }

        .bar-concluido, .bar-estudado { background: #388e3c; }
        .bar-em-andamento, .bar-estudando { background: #f57c00; }
        .bar-nao-iniciado, .bar-nao-estudado { background: #d32f2f; }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 15px;
        }

        .stat-item {
            background: #fdfbf7;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e8e0d8;
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }

        .legend-container {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .legend-color {
            width: 12px;
            height: 12px;
            border-radius: 3px;
        }

        .legend-concluido, .legend-estudado { background: #388e3c; }
        .legend-em-andamento, .legend-estudando { background: #f57c00; }
        .legend-nao-iniciado, .legend-nao-estudado { background: #d32f2f; }

        .total-progress {
            font-size: 1.1rem;
            font-weight: bold;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .btn-voltar {
            position: fixed;
            top: 20px;
            left: 20px;
            background: linear-gradient(145deg, #8b4513, #a0522d);
            color: #fff;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            text-decoration: none;
            z-index: 1000;
        }

        .btn-voltar:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            background: linear-gradient(145deg, #a0522d, #8b4513);
        }

        .btn-voltar i {
            font-size: 1.5rem;
        }

        @media (max-width: 768px) {
            .btn-voltar {
                top: 10px;
                left: 10px;
                width: 40px;
                height: 40px;
            }

            .btn-voltar i {
                font-size: 1.2rem;
            }

            body {
                padding: 20px 10px;
            }

            .header-vintage h2 {
                font-size: 2rem;
            }

            .content-container {
                padding: 20px;
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .materia-header {
                padding-right: 40px;
            }

            .porcentagem {
                font-size: 1rem;
                margin-right: 20px;
                min-width: 100px;
            }

            .toggle-icon {
                right: 10px;
            }
        }
    </style>
</head>
<body>
<a href="https://concurseirooff.com.br/edital_verticalizado/index.php" class="btn-voltar">
    <i class="fas fa-arrow-left"></i>
</a>
<div class="container">
    <div class="header-vintage">
        <h2>Progresso Geral</h2>
        <p>Acompanhe seu progresso de Estudo e Revisão</p>
    </div>

    <div class="content-container">
        <!-- Seção de Estudo -->
        <h3 class="section-title">
            <div>
                <i class="fas fa-book"></i>
                Progresso de Estudo
            </div>
            <div class="total-progress" data-total-estudo>0% Estudado</div>
        </h3>
        <div class="section-content" data-tipo="estudo">
            <?php foreach ($progresso_estudo as $materia_id => $materia): ?>
                <?php
                $total_estudo = $materia['total_conteudos'];
                $porcentagem_estudado = $total_estudo > 0 ? ($materia['estudado'] / $total_estudo) * 100 : 0;
                $porcentagem_estudando = $total_estudo > 0 ? ($materia['estudando'] / $total_estudo) * 100 : 0;
                $porcentagem_nao_estudado = $total_estudo > 0 ? ($materia['nao_estudado'] / $total_estudo) * 100 : 0;
                ?>
                <div class="progresso-materia">
                    <div class="materia-header">
                        <div class="materia-info">
                            <i class="materia-icon" style="color: <?= htmlspecialchars($materia['cor']) ?>;">
                                <i class="fas fa-book"></i>
                            </i>
                            <h4 class="materia-nome"><?= htmlspecialchars($materia['materia_nome']) ?></h4>
                        </div>
                        <span class="porcentagem"><?= number_format($porcentagem_estudado, 1) ?>% Estudado</span>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </div>

                    <div class="materia-detalhes">
                        <div class="progress-bar">
                            <div class="bar-estudado" style="width: <?= $porcentagem_estudado ?>%;"></div>
                            <div class="bar-estudando" style="width: <?= $porcentagem_estudando ?>%;"></div>
                            <div class="bar-nao-estudado" style="width: <?= $porcentagem_nao_estudado ?>%;"></div>
                        </div>

                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number"><?= $materia['estudado'] ?></div>
                                <div class="stat-label">Estudado</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number"><?= $materia['estudando'] ?></div>
                                <div class="stat-label">Estudando</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number"><?= $materia['nao_estudado'] ?></div>
                                <div class="stat-label">Não Estudado</div>
                            </div>
                        </div>

                        <div class="legend-container">
                            <div class="legend-item">
                                <div class="legend-color legend-estudado"></div>
                                <span>Estudado</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color legend-estudando"></div>
                                <span>Estudando</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color legend-nao-estudado"></div>
                                <span>Não Estudado</span>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <!-- Seção de Revisão -->
        <h3 class="section-title">
            <div>
                <i class="fas fa-sync-alt"></i>
                Progresso de Revisão
            </div>
            <div class="total-progress" data-total-revisao>0% Concluído</div>
        </h3>
        <div class="section-content" data-tipo="revisao">
            <?php foreach ($progresso_revisao as $materia_id => $materia): ?>
                <?php
                $total_revisao = $materia['total_conteudos'];
                $porcentagem_concluido = ($materia['concluido_revisao'] / $total_revisao) * 100;
                $porcentagem_em_andamento = ($materia['em_andamento_revisao'] / $total_revisao) * 100;
                $porcentagem_nao_iniciado = ($materia['nao_iniciado_revisao'] / $total_revisao) * 100;
                ?>
                <div class="progresso-materia">
                    <div class="materia-header">
                        <div class="materia-info">
                            <i class="materia-icon" style="color: <?= htmlspecialchars($materia['cor']) ?>;">
                                <i class="fas fa-book"></i>
                            </i>
                            <h4 class="materia-nome"><?= htmlspecialchars($materia['materia_nome']) ?></h4>
                        </div>
                        <span class="porcentagem"><?= number_format($porcentagem_concluido, 1) ?>% Concluído</span>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </div>

                    <div class="materia-detalhes">
                        <div class="progress-bar">
                            <div class="bar-concluido" style="width: <?= $porcentagem_concluido ?>%;"></div>
                            <div class="bar-em-andamento" style="width: <?= $porcentagem_em_andamento ?>%;"></div>
                            <div class="bar-nao-iniciado" style="width: <?= $porcentagem_nao_iniciado ?>%;"></div>
                        </div>

                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-number"><?= $materia['concluido_revisao'] ?></div>
                                <div class="stat-label">Concluído</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number"><?= $materia['em_andamento_revisao'] ?></div>
                                <div class="stat-label">Em Andamento</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number"><?= $materia['nao_iniciado_revisao'] ?></div>
                                <div class="stat-label">Não Iniciado</div>
                            </div>
                        </div>

                        <div class="legend-container">
                            <div class="legend-item">
                                <div class="legend-color legend-concluido"></div>
                                <span>Concluído</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color legend-em-andamento"></div>
                                <span>Em Andamento</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color legend-nao-iniciado"></div>
                                <span>Não Iniciado</span>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle simples das seções
        document.querySelectorAll('.section-title').forEach(section => {
            section.addEventListener('click', function() {
                const content = this.nextElementSibling;
                content.classList.toggle('active');
            });
        });

        // Toggle simples dos cards de matéria
        document.querySelectorAll('.progresso-materia').forEach(card => {
            card.addEventListener('click', function(e) {
                // Ignora cliques nos elementos internos de estatísticas e legendas
                if (e.target.closest('.stats-grid') ||
                    e.target.closest('.legend-container')) {
                    return;
                }
                this.classList.toggle('expanded');
            });
        });

        // Cálculo do progresso total
        function calcularProgressoTotal(tipo) {
            const materias = document.querySelectorAll(`[data-tipo="${tipo}"] .progresso-materia`);
            let totalItens = 0;
            let totalConcluido = 0;

            materias.forEach(materia => {
                const stats = materia.querySelectorAll('.stat-number');
                const concluido = parseInt(stats[0].textContent);
                const emAndamento = parseInt(stats[1].textContent);
                const naoIniciado = parseInt(stats[2].textContent);

                totalItens += (concluido + emAndamento + naoIniciado);
                totalConcluido += concluido;
            });

            return totalItens > 0 ? ((totalConcluido / totalItens) * 100).toFixed(1) : 0;
        }

        // Atualiza os totais
        const progressoEstudo = calcularProgressoTotal('estudo');
        const progressoRevisao = calcularProgressoTotal('revisao');

        document.querySelector('[data-total-estudo]').textContent = `${progressoEstudo}% Estudado`;
        document.querySelector('[data-total-revisao]').textContent = `${progressoRevisao}% Concluído`;
    });
</script>
</body>
</html>