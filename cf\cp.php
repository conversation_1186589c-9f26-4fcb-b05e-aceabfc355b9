<?php
session_start();

// Verifica se o usuário está logado
if (!isset($_SESSION['idusuario'])) {
    header("Location: ../login_index.php");
    exit;
}

// URL da Constituição
$urlOriginal = "https://www.planalto.gov.br/ccivil_03/decreto-lei/del2848compilado.htm";

function buscarConteudoOriginal($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    $response = curl_exec($ch);
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        error_log("Erro cURL: " . $error);
        return null; 
    }
    curl_close($ch);
    return $response;
}

function processarHTML($html) {
    if (empty($html)) {
        return "Erro: Não foi possível carregar o conteúdo original.";
    }

    $dom = new DOMDocument();
    libxml_use_internal_errors(true);
    @$dom->loadHTML('<?xml encoding="UTF-8">' . $html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
    libxml_clear_errors();

    $xpath = new DOMXPath($dom);

    $head = $dom->getElementsByTagName('head')->item(0);
    $body = $dom->getElementsByTagName('body')->item(0);

    if (!$head || !$body) {
        return "Erro: Estrutura HTML básica (head/body) não encontrada no conteúdo original.";
    }

    // 1. Adiciona meta charset
    $metaCharset = $dom->createElement('meta');
    $metaCharset->setAttribute('charset', 'UTF-8');
    $head->insertBefore($metaCharset, $head->firstChild);

    // 2. Define a Base URL correta
    $baseUrl = "https://www.planalto.gov.br/ccivil_03/constituicao/";

    // 3. Corrige URLs de IMAGENS
    $images = $dom->getElementsByTagName('img');
    foreach ($images as $img) {
        $src = $img->getAttribute('src');
        if (!empty($src) && strpos($src, 'data:') !== 0 && strpos($src, 'http') !== 0) {
            if (strpos($src, '../') === 0) {
                $pathParts = explode('/', rtrim($baseUrl, '/'));
                array_pop($pathParts); 
                $parentBaseUrl = implode('/', $pathParts) . '/';
                $img->setAttribute('src', $parentBaseUrl . substr($src, 3)); 
            } else {
                $img->setAttribute('src', $baseUrl . ltrim($src, '/'));
            }
        }
    }

    // 4. Corrige URLs de LINKS CSS
    $links = $dom->getElementsByTagName('link');
    foreach ($links as $linkNode) {
        if (strtolower($linkNode->getAttribute('rel')) === 'stylesheet') {
            $href = $linkNode->getAttribute('href');
            if (!empty($href) && strpos($href, 'http') !== 0) {
                 if (strpos($href, '../') === 0) {
                    $pathParts = explode('/', rtrim($baseUrl, '/'));
                    array_pop($pathParts);
                    $parentBaseUrl = implode('/', $pathParts) . '/';
                    $linkNode->setAttribute('href', $parentBaseUrl . substr($href, 3));
                } else {
                    $linkNode->setAttribute('href', $baseUrl . ltrim($href, '/'));
                }
            }
        }
    }

    // 5. Remove scripts problemáticos
    $scripts = $dom->getElementsByTagName('script');
    $scriptsToRemove = [];
    foreach ($scripts as $script) { if (!empty($script->getAttribute('src'))) { $scriptsToRemove[] = $script; } }
    foreach ($scriptsToRemove as $script) { if ($script->parentNode) $script->parentNode->removeChild($script); }

    // 6. *** NOVO: REMOVER PARÁGRAFOS INTEIROS QUE CONTÊM TEXTO TACHADO ***
    $paragraphsContainingStrike = $xpath->query('//p[.//strike or .//s or .//del]');
    $paragraphsToRemoveEntirely = []; 
    foreach ($paragraphsContainingStrike as $p_element) {
        $paragraphsToRemoveEntirely[] = $p_element;
    }
    foreach ($paragraphsToRemoveEntirely as $pToRemove) {
        if ($pToRemove->parentNode) {
            $pToRemove->parentNode->removeChild($pToRemove);
        }
    }
    // *** FIM DO CÓDIGO PARA REMOVER PARÁGRAFOS COM TEXTO TACHADO ***
    

    // 7. Reestrutura o cabeçalho da tabela (Brasão)
    $trsToModify = $xpath->query("//tr[count(td)=2 and td[1]//img[contains(@src, 'Brastra.gif')]]");
    $toRemoveHeader = [];
    foreach ($trsToModify as $originalTr) {
        $tds_reorg = $xpath->query('./td', $originalTr);
        if ($tds_reorg->length === 2) {
            $td1 = $tds_reorg->item(0); $td2 = $tds_reorg->item(1);
            $parent = $originalTr->parentNode;
            if ($parent) {
                $newTr1 = $dom->createElement('tr'); $newTr1->appendChild($td1->cloneNode(true));
                $newTr2 = $dom->createElement('tr'); $clonedTd2 = $td2->cloneNode(true); $clonedTd2->setAttribute('width', '100%'); $newTr2->appendChild($clonedTd2);
                $parent->insertBefore($newTr1, $originalTr);
                $parent->insertBefore($newTr2, $originalTr);
                $toRemoveHeader[] = $originalTr;
            }
        }
    }
    foreach($toRemoveHeader as $tr) { if ($tr->parentNode) $tr->parentNode->removeChild($tr); }

    // 8. LIMPEZA GERAL E ABRANGENTE DE PARÁGRAFOS E SEUS CONTEÚDOS INTERNOS
    $allParagraphsQuery = $xpath->query("//p"); // Seleciona todos os <p> que sobraram
    foreach ($allParagraphsQuery as $p) {
        $originalAlignAttr = strtolower($p->getAttribute('align'));
        $originalStyleAttr = $p->getAttribute('style');
        
        $isCentered = ($originalAlignAttr === 'center' || strpos($originalStyleAttr, 'text-align: center') !== false);
        $isRightAligned = ($originalAlignAttr === 'right' || strpos($originalStyleAttr, 'text-align: right') !== false);

        $p->removeAttribute('class');
        $p->removeAttribute('style');

        if ($isCentered) {
            $p->setAttribute('align', 'center');
        } elseif ($isRightAligned) {
            $p->setAttribute('align', 'right');
        } else {
            $p->setAttribute('align', 'JUSTIFY');
            $p->setAttribute('style', 'text-indent: 38px;');
        }

        $innerFormattingTags = $xpath->query('.//font | .//strong | .//em | .//span | .//b | .//i | .//u | .//big | .//small', $p);
        foreach ($innerFormattingTags as $innerTag) {
            $innerTag->removeAttribute('style'); 
            $innerTag->removeAttribute('class'); 
            
            if ($innerTag->tagName === 'font') {
                $innerTag->removeAttribute('color');
                $innerTag->removeAttribute('size');
                // $innerTag->removeAttribute('face'); // Opcional: remover face para padronizar com CSS
            }
        }
    }
    
    // 9. JUNÇÃO DE PARÁGRAFOS QUEBRADOS (P seguido de FONT)
    $allParagraphsJoinQuery = $xpath->query("//p"); 
    $nodesToRemoveMerge = []; 
    foreach ($allParagraphsJoinQuery as $p_join) { 
        $nextElement = $xpath->query('following-sibling::*[1]', $p_join)->item(0);
        if ($nextElement && $nextElement->tagName === 'font') {
            $fontText = trim($nextElement->nodeValue);
            if ($fontText !== '' && preg_match('/^[a-zà-ü]/u', $fontText)) {
                $targetFont = $xpath->query('.//font[last()]', $p_join)->item(0);
                $targetNode = $targetFont ? $targetFont : $p_join;
                $targetNode->appendChild($dom->createTextNode(' '));
                while ($nextElement->firstChild) {
                    $targetNode->appendChild($nextElement->firstChild); 
                }
                $nodesToRemoveMerge[] = $nextElement;
            }
        }
    }
    foreach ($nodesToRemoveMerge as $node) { if ($node->parentNode) $node->parentNode->removeChild($node); }

    // 10. Processa elementos 'cstf'
    $cstfElements = $xpath->query("//*[contains(@class, 'cstf')]");
    foreach ($cstfElements as $element) {
        $currentClass = $element->getAttribute('class');
        $element->setAttribute('class', $currentClass . ' meu-proxy');
    }

    // 11. Adiciona IDs para marcação
    $elementosParaId = $xpath->query("//p[not(@id) and normalize-space()] | //div[not(@id) and normalize-space() and not(ancestor::p[normalize-space()])] | //font[not(@id) and normalize-space() and not(ancestor::p[normalize-space()]) and not(ancestor::div[normalize-space()])]"); 
    $idCount = 0; 
    foreach ($elementosParaId as $elemento) {
        if (!$elemento->hasAttribute('id')) { 
             $elemento->setAttribute('id', 'el-' . $idCount++);
        }
    }

    // 12. Adiciona FontAwesome e Favicon
    $fontAwesome = $dom->createElement('link');
    $fontAwesome->setAttribute('rel', 'stylesheet');
    $fontAwesome->setAttribute('href', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');
    $head->appendChild($fontAwesome);

    $favicon = $dom->createElement('link');
    $favicon->setAttribute('rel', 'shortcut icon');
    $favicon->setAttribute('href', '../logo/Estudo Off/favicon_32x32.png');
    $favicon->setAttribute('type', 'image/x-icon');
    $head->appendChild($favicon);

    // 13. Adiciona CSS
    $styleNode = $dom->createElement('style');
    // ... (O CSS completo que definimos anteriormente, incluindo modo escuro e tooltip fix) ...
    // (Para economizar espaço aqui, vou omitir o bloco CSS gigante, mas ele deve ser o mesmo da resposta anterior, sem o .content-wrapper)
    $cssContent = $dom->createTextNode("
        :root {
            --primary-color: #00008B; --text-color: #333; --bg-color: #f5f5f5;
            --button-bg: white; --button-border: #ccc; --button-hover: #f0f0f0; --link-color: var(--primary-color);
            --highlight-yellow: #ffff99; --highlight-green: #c8ffc8; --highlight-blue: #c8f4ff;
            --highlight-yellow-icon: #b3b300; --highlight-green-icon: #008a00; --highlight-blue-icon: #0077b3;
            --remove-bg: #ffeeee; --remove-icon: #cc0000; --toolbar-bg: white; --toolbar-border: #ccc;
            --toolbar-shadow: rgba(0,0,0,0.2); --back-button-bg: #00008B; --back-button-hover: #0000CD;
            --tooltip-bg: #f9f9f9; --tooltip-text: #333; --tooltip-border: #ccc;
        }
        body.dark-mode {
            --primary-color: #58a6ff; --text-color: #e0e0e0; --bg-color: #0d1117;
            --button-bg: #21262d; --button-border: #30363d; --button-hover: #30363d; --link-color: #58a6ff;
            --highlight-yellow: #b0a900; --highlight-green: #007300; --highlight-blue: #006e9c;
            --highlight-yellow-icon: #ffffc3; --highlight-green-icon: #d4ffd4; --highlight-blue-icon: #d4f4ff;
            --remove-bg: #5c1f1f; --remove-icon: #ff8a8a; --toolbar-bg: #161b22; --toolbar-border: #30363d;
            --toolbar-shadow: rgba(0,0,0,0.5); --back-button-bg: #58a6ff; --back-button-hover: #79bbff;
            --tooltip-bg: #3a3a4a; --tooltip-text: #e5e5e5; --tooltip-border: #555;
        }
        body { 
            padding-bottom:60px; font-family:'Roboto',sans-serif; line-height:1.8; 
            background-color:var(--bg-color); color:var(--text-color); 
            margin: 0 auto; padding-left: 15px; padding-right: 15px;
            padding-top: 80px; max-width: 1200px; font-size:16px; 
            transition: background-color 0.3s, color 0.3s; 
        }
        .marcacao-usuario { 
        border-radius:2px; 
        cursor:pointer; 
        transition:background-color 0.3s; 
        color: #333 !important; 
        }
        body.dark-mode .marcacao-usuario { color: black !important; }
        body.dark-mode .texto-anotado { color: black !important; }
        .marcacao-amarela { background-color: var(--highlight-yellow) !important; }
        .marcacao-verde { background-color: var(--highlight-green) !important; }
        .marcacao-azul { background-color: var(--highlight-blue) !important; }
        #botao-marcacao { position:absolute; z-index:1000; background-color:var(--button-bg); border:1px solid var(--button-border); border-radius:4px; box-shadow:0 2px 5px var(--toolbar-shadow); padding:5px; display:flex; transition:background-color 0.3s, border 0.3s; }
        #botao-marcacao button { margin:0 3px; border:none; width:30px; height:30px; border-radius:3px; cursor:pointer; display:flex; align-items:center; justify-content:center; transition:transform 0.2s, background-color 0.3s; }
        #botao-marcacao button:hover { transform: scale(1.1); }
        #marcar-amarelo { background-color:var(--highlight-yellow); } #marcar-amarelo i { color:var(--highlight-yellow-icon); }
        #marcar-verde { background-color:var(--highlight-green); } #marcar-verde i { color:var(--highlight-green-icon); }
        #marcar-azul { background-color:var(--highlight-blue); } #marcar-azul i { color:var(--highlight-blue-icon); }
        #remover-marcacao { background-color:var(--remove-bg); } #remover-marcacao i { color:var(--remove-icon); }
        #barra-ferramentas { 
        position:fixed; 
        top:80px; 
        right:20px; 
        z-index:1000; 
        background-color:var(--toolbar-bg); 
        border:1px solid var(--toolbar-border); 
        border-radius:4px; box-shadow:0 2px 5px var(--toolbar-shadow); 
        padding:10px; display:flex; gap:10px; 
        transition:background-color 0.3s, border 0.3s; 
        }
                #barra-marcacao { 
        background-color:var(--toolbar-bg); 
  
        }
        #limpar-marcacoes, #modo-escuro-toggle { background-color:var(--button-bg); border:1px solid var(--button-border); padding:5px 10px; border-radius:3px; cursor:pointer; display:flex; align-items:center; font-size:14px; color:var(--text-color); transition:background-color 0.3s, border 0.3s, color 0.3s; }
        #limpar-marcacoes:hover, #modo-escuro-toggle:hover { background-color:var(--button-hover); }
        #limpar-marcacoes i { margin-right:5px; color:var(--remove-icon); }
        #modo-escuro-toggle i { margin-right:5px; color:var(--primary-color); }
        body.dark-mode #modo-escuro-toggle i { color:#f9d71c; }
        p, div, font { 
        font-size:1.1rem !important; 
        line-height:1.8 !important; 
        color:var(--text-color) !important; 
        font-family:'Roboto',sans-serif !important; }
        span:not(.marcacao-usuario):not(.texto-anotado) { background-color: transparent !important; color:var(--text-color) !important; font-size:1.1rem !important; line-height:1.8 !important; font-family:'Roboto',sans-serif !important; }
        a { color:var(--link-color) !important; text-decoration:none; } a:hover { text-decoration:underline; }
        .back-button { position:fixed; top:20px; left:20px; background:var(--back-button-bg); color:white !important; padding:10px 20px; border-radius:5px; text-decoration:none; display:flex; align-items:center; gap:8px; font-weight:500; box-shadow:0 2px 5px var(--toolbar-shadow); z-index:1000; transition:background-color 0.3s; }
        .back-button:hover { background:var(--back-button-hover); color:white !important; text-decoration:none; }
        .tooltip-anotacao {
            position: fixed !important; z-index: 10001 !important;
            background-color: var(--tooltip-bg) !important; color: var(--tooltip-text) !important;
            border: 1px solid var(--tooltip-border) !important; border-radius: 4px !important;
            padding: 8px 12px !important; box-shadow: 0 2px 8px rgba(0,0,0,0.25) !important;
            font-size: 14px !important; max-width: 300px !important; pointer-events: none !important;
            opacity: 0.95 !important; word-wrap: break-word !important; display: none;
            transition: opacity 0.2s, background-color 0.3s, color 0.3s, border-color 0.3s !important;
        }
        .tooltip-anotacao::after { 
            content: '' !important; position: absolute !important; top: 100% !important; 
            left: 50% !important; transform: translateX(-50%) translateY(-1px) !important; 
            border-width: 6px 6px 0 !important;
            border-style: solid !important; border-color: var(--tooltip-bg) transparent transparent transparent !important;
            transition: border-color 0.3s !important;
        }
        @media (max-width:768px) {
            body{font-size:15px; padding-left: 10px; padding-right: 10px; padding-top: 70px;}
            p, div, font, span {font-size: 1rem !important;}
            .back-button{padding:8px 15px;font-size:.9rem;top:10px;left:10px;}
            #barra-ferramentas{top:10px;right:20px;}
            #limpar-marcacoes, #modo-escuro-toggle{padding:5px 8px;font-size:12px;}
            #barra-ferramentas i{margin-right:3px;}
        }
    ");
    $styleNode->appendChild($cssContent);
    $head->appendChild($styleNode);

    // 14. Adiciona elementos da UI (botões, etc. - SEM WRAPPER)
    $barraFerramentas = $dom->createElement('div');
    $barraFerramentas->setAttribute('id', 'barra-ferramentas');
    $modoEscuroButton = $dom->createElement('button');
    $modoEscuroButton->setAttribute('id', 'modo-escuro-toggle');
    $modoEscuroIcon = $dom->createElement('i'); $modoEscuroIcon->setAttribute('class', 'fas fa-moon');
    $modoEscuroButton->appendChild($modoEscuroIcon); $modoEscuroButton->appendChild($dom->createTextNode(' Modo'));
    $barraFerramentas->appendChild($modoEscuroButton);

    $backButton = $dom->createElement('a');
    $backButton->setAttribute('href', 'index.php'); 
    $backButton->setAttribute('class', 'back-button');
    $backButton->nodeValue = '← Voltar';

    $body->appendChild($backButton);
    $body->appendChild($barraFerramentas);
    
    // 15. Adiciona JavaScripts
    $scriptModoEscuro = $dom->createElement('script');
    // ... (Conteúdo do script JS do modo escuro - sem alterações) ...
    $scriptModoEscuroContent = $dom->createTextNode("
        document.addEventListener('DOMContentLoaded', () => {
            const toggleButton = document.getElementById('modo-escuro-toggle');
            const body = document.body;
            if (!toggleButton || !body) return; 
            const toggleIcon = toggleButton.querySelector('i');
            const applyMode = (isDark) => {
                if (isDark) {
                    body.classList.add('dark-mode');
                    if(toggleIcon) { toggleIcon.classList.remove('fa-moon'); toggleIcon.classList.add('fa-sun'); }
                    localStorage.setItem('darkMode', 'enabled');
                } else {
                    body.classList.remove('dark-mode');
                    if(toggleIcon) { toggleIcon.classList.remove('fa-sun'); toggleIcon.classList.add('fa-moon'); }
                    localStorage.setItem('darkMode', 'disabled');
                }
            };
            const currentMode = localStorage.getItem('darkMode');
            applyMode(currentMode === 'enabled');
            toggleButton.addEventListener('click', () => {
                applyMode(!body.classList.contains('dark-mode'));
            });
        });
    ");
    $scriptModoEscuro->appendChild($scriptModoEscuroContent);
    $body->appendChild($scriptModoEscuro);

    $scriptMarcacao = $dom->createElement('script');
    $scriptMarcacao->setAttribute('type', 'text/javascript');
    $scriptMarcacao->setAttribute('src', 'marcacao.js'); 
    $body->appendChild($scriptMarcacao);
    
    return $dom->saveHTML();
}

// ----- INÍCIO DO SCRIPT -----
$conteudoOriginal = buscarConteudoOriginal($urlOriginal);

if ($conteudoOriginal === null || strpos($conteudoOriginal, "Erro ao acessar") !== false) {
    echo "Falha ao carregar o conteúdo da página original. Verifique a URL ou a conexão.";
    exit;
}

$conteudoProcessado = processarHTML($conteudoOriginal);

if (is_string($conteudoProcessado) && strpos($conteudoProcessado, "Erro:") === 0) {
    echo $conteudoProcessado;
    exit;
}

header('Content-Type: text/html; charset=utf-8');
echo $conteudoProcessado;
?>