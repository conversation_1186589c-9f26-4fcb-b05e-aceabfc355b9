<?php
// includes/medicamento.php
require_once __DIR__ . '/../config/database.php';

class Medicamento {
    private $conn;
    private $table_name = "appestudo.medicamentos";
    
    // Propriedades
    public $id;
    public $nome;
    public $dosagem;
    public $intervalo_horas;
    public $data_inicio;
    public $dias_tratamento;
    public $horario_inicial;
    public $observacoes;
    public $status;
    
    // Construtor
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }
    
    // Listar todos os medicamentos ativos
    public function listar() {
        $query = "SELECT * FROM " . $this->table_name . " WHERE status = true ORDER BY nome";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt;
    }
    
    // Obter detalhes de um medicamento específico
    public function obter($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        $stmt->execute();
        
        $row = $stmt->fetch();
        
        if ($row) {
            $this->id = $row['id'];
            $this->nome = $row['nome'];
            $this->dosagem = $row['dosagem'];
            $this->intervalo_horas = $row['intervalo_horas'];
            $this->data_inicio = $row['data_inicio'];
            $this->dias_tratamento = $row['dias_tratamento'];
            $this->horario_inicial = $row['horario_inicial'];
            $this->observacoes = $row['observacoes'];
            $this->status = $row['status'];
            
            return true;
        }
        
        return false;
    }
    
    // Adicionar um novo medicamento
    public function adicionar() {
        $query = "INSERT INTO " . $this->table_name . " 
                (nome, dosagem, intervalo_horas, data_inicio, dias_tratamento, horario_inicial, observacoes) 
                VALUES 
                (:nome, :dosagem, :intervalo_horas, :data_inicio, :dias_tratamento, :horario_inicial, :observacoes)";
        
        $stmt = $this->conn->prepare($query);
        
        // Limpar e sanitizar dados
        $this->nome = htmlspecialchars(strip_tags($this->nome));
        $this->dosagem = htmlspecialchars(strip_tags($this->dosagem));
        $this->intervalo_horas = intval($this->intervalo_horas);
        $this->dias_tratamento = intval($this->dias_tratamento);
        $this->observacoes = htmlspecialchars(strip_tags($this->observacoes));
        
        // Vincular parâmetros
        $stmt->bindParam(":nome", $this->nome);
        $stmt->bindParam(":dosagem", $this->dosagem);
        $stmt->bindParam(":intervalo_horas", $this->intervalo_horas);
        $stmt->bindParam(":data_inicio", $this->data_inicio);
        $stmt->bindParam(":dias_tratamento", $this->dias_tratamento);
        $stmt->bindParam(":horario_inicial", $this->horario_inicial);
        $stmt->bindParam(":observacoes", $this->observacoes);
        
        // Executar a query
        if ($stmt->execute()) {
            // Obter o ID gerado para o novo medicamento
            $this->id = $this->conn->lastInsertId();
            
            // Criar registros de uso programados
            $this->criarRegistrosUso();
            
            return true;
        }
        
        return false;
    }
    
    // Atualizar um medicamento existente
    public function atualizar() {
        $query = "UPDATE " . $this->table_name . " 
                SET 
                    nome = :nome, 
                    dosagem = :dosagem, 
                    intervalo_horas = :intervalo_horas, 
                    data_inicio = :data_inicio, 
                    dias_tratamento = :dias_tratamento, 
                    horario_inicial = :horario_inicial, 
                    observacoes = :observacoes 
                WHERE 
                    id = :id";
        
        $stmt = $this->conn->prepare($query);
        
        // Limpar e sanitizar dados
        $this->nome = htmlspecialchars(strip_tags($this->nome));
        $this->dosagem = htmlspecialchars(strip_tags($this->dosagem));
        $this->intervalo_horas = intval($this->intervalo_horas);
        $this->dias_tratamento = intval($this->dias_tratamento);
        $this->observacoes = htmlspecialchars(strip_tags($this->observacoes));
        
        // Vincular parâmetros
        $stmt->bindParam(":nome", $this->nome);
        $stmt->bindParam(":dosagem", $this->dosagem);
        $stmt->bindParam(":intervalo_horas", $this->intervalo_horas);
        $stmt->bindParam(":data_inicio", $this->data_inicio);
        $stmt->bindParam(":dias_tratamento", $this->dias_tratamento);
        $stmt->bindParam(":horario_inicial", $this->horario_inicial);
        $stmt->bindParam(":observacoes", $this->observacoes);
        $stmt->bindParam(":id", $this->id);
        
        // Executar a query
        if ($stmt->execute()) {
            // Remover registros de uso antigos
            $this->limparRegistrosUso();
            
            // Criar novos registros de uso
            $this->criarRegistrosUso();
            
            return true;
        }
        
        return false;
    }
    
    // Desativar um medicamento (não excluir)
    public function desativar() {
        $query = "UPDATE " . $this->table_name . " SET status = false WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        
        return $stmt->execute();
    }
    /**
     * Exclui um medicamento e seus registros associados
     * 
     * @param int $id ID do medicamento a ser excluído
     * @return bool Retorna true se a exclusão foi bem-sucedida, false caso contrário
     */
    public function excluir($id) {
        try {
            // Iniciar transação para garantir integridade dos dados
            $this->conn->beginTransaction();
            
            // Primeiro excluir os registros de uso associados
            $stmt = $this->conn->prepare("DELETE FROM appestudo.registros_uso WHERE medicamento_id = ?");
            $stmt->execute([$id]);
            
            // Depois excluir o medicamento
            $stmt = $this->conn->prepare("DELETE FROM appestudo.medicamentos WHERE id = ?");
            $resultado = $stmt->execute([$id]);
            
            // Confirmar transação
            $this->conn->commit();
            
            return $resultado;
        } catch (PDOException $e) {
            // Em caso de erro, reverter transação
            $this->conn->rollBack();
            return false;
        }
    }
    // Criar registros de uso programados
// Criar registros de uso programados
    private function criarRegistrosUso() {
        require_once 'registro.php';
        $registro = new Registro();

        // Data e hora de início
        $data_hora_inicio = new DateTime($this->data_inicio . ' ' . $this->horario_inicial);

        // Calcular data e hora final (data início + dias tratamento)
        $data_hora_fim = clone $data_hora_inicio;
        $data_hora_fim->modify('+' . $this->dias_tratamento . ' days');

        // Horário atual para iteração
        $horario_atual = clone $data_hora_inicio;

        // Garantir que o intervalo_horas seja tratado como inteiro
        $intervalo = (int)$this->intervalo_horas;

        // Contador para debug
        $contador = 0;

        // Registrar doses até atingir a data final
        while ($horario_atual < $data_hora_fim) {
            // Registrar esta dose programada
            $registro->medicamento_id = $this->id;
            $registro->data_hora = $horario_atual->format('Y-m-d H:i:s');
            $resultado = $registro->adicionar();

            $contador++;

            // Avançar para a próxima dose usando DateInterval para maior precisão
            $horario_atual->add(new DateInterval('PT' . $intervalo . 'H'));
        }

        // Opcional: registrar o número total de doses para fins de depuração
        // error_log("Total de registros criados para medicamento ID {$this->id}: {$contador}");
    }
    
    // Limpar registros de uso antigos
    private function limparRegistrosUso() {
        require_once 'registro.php';
        $registro = new Registro();
        $registro->limparPorMedicamento($this->id);
    }
    
    // Obter os próximos medicamentos a serem tomados
    public function obterProximos() {
        $query = "SELECT m.id, m.nome, m.dosagem, r.id as registro_id, r.data_hora 
                FROM " . $this->table_name . " m
                JOIN appestudo.registros_uso r ON m.id = r.medicamento_id
                WHERE m.status = true 
                AND r.confirmado = false 
                AND r.data_hora > NOW() 
                ORDER BY r.data_hora ASC 
                LIMIT 5";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        
        return $stmt;
    }
}
?>