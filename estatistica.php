<?php
include 'processa_index.php';
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estatística</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>
</head>
<body>
<canvas id="chart-options-example"></canvas>

<script>
    // PHP - Recuperar os dados do PostgreSQL
    <?php
    include_once("conexao_POST.php");
    if (isset($_SESSION['idusuario'])) {
        $id_usuario = $_SESSION['idusuario'];
        $query_consultar_estudos = "SELECT e.*, 
       m_estudo.nome AS nome_materia_estudo, 
       m_estudo.cor AS cor_materia_estudo
FROM appEstudo.estudos e
INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
LEFT JOIN appEstudo.materia m_estudo ON e.materia_idmateria = m_estudo.idmateria
WHERE u.idusuario = $id_usuario
ORDER BY EXTRACT(YEAR FROM e.data), EXTRACT(MONTH FROM e.data)";
    }
    // Consulta para obter as datas do banco de dados
    $result = pg_query($conexao, $query_consultar_estudos);

    // Função para obter o nome do mês e o ano em português
    function obterNomeMesEAno($data) {
        $mesesEmPortugues = array(
            'January' => 'Janeiro',
            'February' => 'Fevereiro',
            'March' => 'Março',
            'April' => 'Abril',
            'May' => 'Maio',
            'June' => 'Junho',
            'July' => 'Julho',
            'August' => 'Agosto',
            'September' => 'Setembro',
            'October' => 'Outubro',
            'November' => 'Novembro',
            'December' => 'Dezembro'
        );

        $timestamp = strtotime($data);
        $mes = date("F", $timestamp);
        $ano = date("Y", $timestamp);
        return $mesesEmPortugues[$mes] . '/' . $ano;
    }

    // Array para armazenar as datas já contabilizadas
    $datasContabilizadas = array();

    // Array para armazenar as contagens por mês/ano
    $meses = array();

    // Percorre os resultados da consulta
    while ($row = pg_fetch_assoc($result)) {
        // Obter a data
        $data = $row["data"];

        // Verificar se a data já foi contabilizada
        if (!in_array($data, $datasContabilizadas)) {
            // Adicionar a data às datas contabilizadas
            $datasContabilizadas[] = $data;

            // Obter o mês e ano da data formatado em português
            $mesEAno = obterNomeMesEAno($data);

            // Incrementar a contagem para o mês atual ou definir como 1 se for o primeiro encontro
            if (isset($meses[$mesEAno])) {
                $meses[$mesEAno]++;
            } else {
                $meses[$mesEAno] = 1;
            }
        }
    }

    // Função para definir as cores com base na quantidade de dias por mês
    function definirCoresPorMes($meses) {
        $cores = array();

        foreach ($meses as $mes => $quantidade) {
            if ($quantidade <= 10) {
                $cores[$mes] = 'rgba(255, 99, 132, 0.2)'; // Vermelho
            } elseif ($quantidade > 20) {
                $cores[$mes] = 'rgba(54, 162, 235, 0.2)'; // Azul
            } else {
                $cores[$mes] = 'rgba(255, 206, 86, 0.2)'; // Amarelo
            }
        }

        return $cores;
    }

    // Obter as cores com base na quantidade de dias por mês
    $coresPorMes = definirCoresPorMes($meses);
    // Adiciona a contagem máxima de dias por mês
    $maxDiasPorMes = array();
    foreach ($meses as $mes => $quantidade) {
        if (!isset($maxDiasPorMes[$mes]) || $quantidade > $maxDiasPorMes[$mes]) {
            $maxDiasPorMes[$mes] = $quantidade;
        }
    }
    ?>

    // JavaScript - Dados obtidos do PHP
    const dataFromPHP = <?php echo json_encode(array_values($meses)); ?>; // Valores das contagens por mês
    const labelsFromPHP = <?php echo json_encode(array_keys($meses)); ?>; // Meses e anos formatados em português
    const coresFromPHP = <?php echo json_encode(array_values($coresPorMes)); ?>; // Cores por mês
    const maxDiasPorMesFromPHP = <?php echo json_encode(array_values($maxDiasPorMes)); ?>; // Máximos dias por mês
    Chart.register(ChartDataLabels);

    // Configuração da fonte para o gráfico
    Chart.defaults.font.family = '"Courier Prime", monospace';

    // Data para o gráfico
    const dataChartOptionsExample = {
        type: 'bar',
        data: {
            labels: labelsFromPHP, // Meses e anos em português
            datasets: [
                {
                    label: 'Dias Estudados',
                    data: dataFromPHP, // Contagens por mês
                    backgroundColor: coresFromPHP, // Cores por mês
                    borderColor: 'rgba(0, 0, 0, 1)', // Cor da borda
                    borderWidth: 1,
                },
                {
                    type: 'line',
                    label: 'Máximos Dias por Mês',
                    data: maxDiasPorMesFromPHP, // Máximos dias por mês
                    fill: false,
                    borderColor: 'rgb(248,0,0)', // Cor da linha
                    borderWidth: 2,
                },
            ],
        },
    };

    // Opções do gráfico (se necessário)
    const optionsChartOptionsExample = {
        scales: {
            x: {
                ticks: {
                    color: '#2b2723',
                },
            },
            y: {
                ticks: {
                    color: '#2b2723',
                },
            },
        },
        plugins: {
            datalabels: {
                display: true,
                color: 'black',
                font: {
                    size: 16, // Aumenta o tamanho da fonte
                    //weight: 'bold' // Deixa a fonte em negrito
                },
            },
        },
    };

    // Criar o gráfico
    new Chart(
        document.getElementById('chart-options-example'),
        {
            type: 'bar',
            data: dataChartOptionsExample.data,
            options: optionsChartOptionsExample
        }
    );
</script>

</body>
</html>
