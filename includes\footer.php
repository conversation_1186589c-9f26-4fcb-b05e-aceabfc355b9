<!-- includes/footer.php -->


    <!-- Script para navegação das abas -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const menuToggle = document.querySelector('.menu-toggle');
            const tabsWrapper = document.querySelector('.tabs-wrapper');
            const tabs = document.querySelectorAll('.tab');
            const contents = document.querySelectorAll('.content');
            const navMenu = document.querySelector('.nav-menu');

            // Toggle do menu mobile da navbar
            menuToggle?.addEventListener('click', () => {
                navMenu?.classList.toggle('active');
            });

            // Toggle do menu mobile das abas
            menuToggle?.addEventListener('click', () => {
                tabsWrapper?.classList.toggle('active');
            });

            // Navegação das abas
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const target = tab.dataset.tab;

                    // Remove classes ativas
                    tabs.forEach(t => t.classList.remove('active'));
                    contents.forEach(c => c.classList.remove('active'));

                    // Adiciona classes ativas
                    tab.classList.add('active');
                    document.getElementById(target)?.classList.add('active');

                    // Fecha menu mobile após clique
                    if (window.innerWidth <= 768) {
                        tabsWrapper?.classList.remove('active');
                        navMenu?.classList.remove('active');
                    }
                });
            });

            // Ajuste responsivo
            window.addEventListener('resize', () => {
                if (window.innerWidth > 768) {
                    tabsWrapper?.classList.remove('active');
                    navMenu?.classList.remove('active');
                }
            });

            // Criar ícones Lucide
            lucide.createIcons();
        });

        // Funções de popup
        function abrirPopUp50() {
            var url = "0cronometro.php";
            var novaJanela = window.open(url, "_blank", "width=" + screen.availWidth + ", height=" + screen.availHeight + ", left=0, top=0, resizable=yes, scrollbars=yes");
            if (novaJanela) {
                novaJanela.focus();
                novaJanela.moveTo(0, 0);
                novaJanela.resizeTo(screen.availWidth, screen.availHeight);
            }
        }

        function abrirPopUp6() {
            var url = "historico.php";
            window.open(url, "_blank");
        }

        function abrirPopUp40() {
            var url = "painel_materias.php";
            window.open(url, "_blank");
        }

        function abrirPopUp60() {
            var url = "cronograma_inteligente/index.php";
            window.open(url, "_blank");
        }

        function abrirPopUp80() {
            var url = "flashcards/flashcards.php";
            window.open(url, "_blank");
        }
        
        function abrirPopUp90() {
            var url = "cf/index.php";
            window.open(url, "_blank");
        }
        
        function abrirPopUpRevisao() {
            var url = "revisao.php";
            window.open(url, "_blank");
        }
    </script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const darkModeToggle = document.getElementById('dark-mode-toggle');
    const body = document.body;
    const icon = darkModeToggle.querySelector('i');
    const text = darkModeToggle.querySelector('span');

    // Function to apply the saved theme or default
    function applyTheme() {
        let currentTheme = localStorage.getItem('theme');
        if (currentTheme === 'dark') {
            body.setAttribute('data-theme', 'dark');
            icon.classList.remove('fa-moon');
            icon.classList.add('fa-sun');
            text.textContent = 'Modo Claro';
        } else {
            body.removeAttribute('data-theme');
            icon.classList.remove('fa-sun');
            icon.classList.add('fa-moon');
            text.textContent = 'Modo Escuro';
        }
    }

    // Apply theme on initial load
    applyTheme();

    // Event listener for the toggle button
    darkModeToggle.addEventListener('click', function(event) {
        event.preventDefault(); // Prevent default anchor action
        let currentTheme = localStorage.getItem('theme');
        if (currentTheme === 'dark') {
            localStorage.setItem('theme', 'light');
        } else {
            localStorage.setItem('theme', 'dark');
        }
        applyTheme();
    });
});
</script>
</body>
</html>
