<?php
session_start();
require_once '../../conexao_POST.php';

// Configurações de erro e cabeçalhos
error_reporting(E_ALL);
ini_set('display_errors', 0);
header('Content-Type: application/json; charset=utf-8');

try {
    // Debug
    error_log("Debug: Iniciando get_tarefas.php");
    error_log("Debug: SESSION = " . print_r($_SESSION, true));
    
    // Verificar se o usuário está logado
    if (!isset($_SESSION['idusuario'])) {
        http_response_code(401);
        echo json_encode(['erro' => 'Usuário não autenticado']);
        exit;
    }

    // Verificar conexão com o banco
    if (!$conexao) {
        throw new Exception('Conexão com o banco de dados não estabelecida');
    }

    $usuario_id = $_SESSION['idusuario'];
    $query = "SELECT * FROM appestudo.kanban_tarefas WHERE usuario_id = $1 ORDER BY data_criacao DESC";
    
    $result = pg_query_params($conexao, $query, array($usuario_id));
    
    if (!$result) {
        throw new Exception(pg_last_error($conexao));
    }
    
    $tarefas = array();
    while ($row = pg_fetch_assoc($result)) {
        $tarefas[] = $row;
    }
    
    // Log do resultado
    error_log("Debug: Tarefas encontradas = " . count($tarefas));
    
    echo json_encode(['sucesso' => true, 'dados' => $tarefas]);

} catch (Exception $e) {
    error_log("Erro: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['erro' => 'Erro ao carregar tarefas: ' . $e->getMessage()]);
}



