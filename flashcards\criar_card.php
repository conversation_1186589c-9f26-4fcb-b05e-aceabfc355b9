<?php
// criar_card.php
session_start();
include_once("assets/config.php");
include_once("helpers.php");

if (!isset($_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit();
}

redirectIfNotAdmin($conexao, $_SESSION['idusuario']);

if (!isset($_GET['topico'])) {
    header("Location: flashcards.php");
    exit();
}

$topico_id = (int)$_GET['topico'];
$mensagem = '';

// Buscar informações do tópico, baralho e categoria
$query_info = "
    SELECT 
        t.nome as topic_name,
        d.nome as deck_name,
        d.id as deck_id,
        c.nome as categoria_name,
        c.id as categoria_id
    FROM appestudo.flashcard_topics t
    JOIN appestudo.flashcard_decks d ON d.id = t.deck_id
    JOIN appestudo.flashcard_categories c ON c.id = d.category_id
    WHERE t.id = $1";
$result_info = pg_query_params($conexao, $query_info, array($topico_id));
$info = pg_fetch_assoc($result_info);

if (!$info) {
    header("Location: flashcards.php");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $pergunta = trim($_POST['pergunta']);
    $resposta = trim($_POST['resposta']);
    $resumo = trim($_POST['resumo']);
    $previsao_legal = trim($_POST['previsao_legal']);
    $jurisprudencia = trim($_POST['jurisprudencia']);
    
    if (!empty($pergunta) && !empty($resposta)) {
        pg_query($conexao, "BEGIN");
        
        try {
            $query_insert = "
                INSERT INTO appestudo.flashcards 
                    (pergunta, resposta, resumo, previsao_legal, jurisprudencia)
                VALUES ($1, $2, $3, $4, $5)
                RETURNING id";
            
            $result = pg_query_params($conexao, $query_insert, array(
                $pergunta, 
                $resposta, 
                $resumo, 
                $previsao_legal, 
                $jurisprudencia
            ));
            
            if ($result) {
                $card = pg_fetch_assoc($result);
                $card_id = $card['id'];
                
                // Associar o card ao tópico
                $query_associate_topic = "
                    INSERT INTO appestudo.flashcard_topic_association (flashcard_id, topic_id)
                    VALUES ($1, $2)";
                pg_query_params($conexao, $query_associate_topic, array($card_id, $topico_id));
                
                // Associar o card ao baralho
                $query_associate_deck = "
                    INSERT INTO appestudo.flashcard_deck_association (flashcard_id, deck_id)
                    VALUES ($1, $2)";
                pg_query_params($conexao, $query_associate_deck, array($card_id, $info['deck_id']));
                
                if (isset($_FILES['mapa_mental']) && $_FILES['mapa_mental']['error'] === 0) {
                    $imagedata = file_get_contents($_FILES['mapa_mental']['tmp_name']);
                    $base64 = base64_encode($imagedata);
                    
                    $query_mindmap = "
                        INSERT INTO appestudo.flashcard_mindmaps (flashcard_id, imagem_base64)
                        VALUES ($1, $2)";
                    pg_query_params($conexao, $query_mindmap, array($card_id, $base64));
                }

                pg_query($conexao, "COMMIT");
                header("Location: ver_cards.php?topico=" . $topico_id);
                exit();
            }
        } catch (Exception $e) {
            pg_query($conexao, "ROLLBACK");
            $mensagem = "Erro ao criar card: " . $e->getMessage();
        }
    } else {
        $mensagem = "Pergunta e resposta são obrigatórios.";
    }
}
?>

<!-- Restante do código HTML permanece o mesmo -->

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Criar Card - <?php echo htmlspecialchars($info['topic_name']); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Old+Standard+TT:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.8.2/tinymce.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Quicksand:wght@300..700&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Tektur:wght@400..900&family=Varela+Round&display=swap" rel="stylesheet">
    
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');
        :root {
            --primary-color: #000080;    
            --paper-color: #FFFFFF;      
            --secondary-color: #4169E1;  
            --background-color: #E8ECF3; 
            --text-color: #2C3345;      
            --border-color: #B8C2CC;    
        }

        body {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 40px;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .form-container {
            background: var(--paper-color);
            padding: 30px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 4px 4px 0 var(--border-color);
        }

        .breadcrumb {
            margin-bottom: 20px;
            color: var(--secondary-color);
            font-style: italic;
        }

        h1 {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            color: var(--primary-color);
            margin-bottom: 30px;
            font-size: 2rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            color: var(--primary-color);
            margin-bottom: 5px;
            font-weight: bold;
        }

        .tox-tinymce {
            border: 1px solid var(--border-color) !important;
            border-radius: 4px !important;
            margin-bottom: 10px;
        }

        .editor-content {
            min-height: 200px;
        }

        .actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        .btn {
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            font-size: 1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            border: none;
        }

        .btn-secondary {
            background: var(--paper-color);
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .error-message {
            background: #FEE;
            border: 1px solid #FAA;
            color: #A00;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .btn-back {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 50px;
            height: 50px;
            background: var(--paper-color);
            border: 2px solid var(--border-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .btn-back:hover {
            transform: translateY(-2px);
            background: var(--primary-color);
            color: white;
        }

        .file-input-container {
            margin-top: 10px;
        }

        .file-input-button {
            background: var(--paper-color);
            border: 2px solid var(--border-color);
            color: var(--primary-color);
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.3s ease;
        }

        .file-input-button:hover {
            background: var(--primary-color);
            color: white;
        }

        .file-input-hidden {
            display: none;
        }

        .file-name {
            margin-top: 8px;
            font-size: 0.9rem;
            color: var(--secondary-color);
            font-style: italic;
        }

        @media (max-width: 768px) {
            body {
                padding: 20px;
            }

            .actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <a href="ver_cards.php?topico=<?php echo $topico_id; ?>" class="btn-back">
        <i class="fas fa-arrow-left"></i>
    </a>

    <div class="container">
        <div class="form-container">
            <div class="breadcrumb">
                <?php echo htmlspecialchars($info['categoria_name']); ?> > 
                <?php echo htmlspecialchars($info['deck_name']); ?> >
                <?php echo htmlspecialchars($info['topic_name']); ?>
            </div>

            <h1>Criar Novo Card</h1>

            <?php if (!empty($mensagem)): ?>
                <div class="error-message">
                    <?php echo htmlspecialchars($mensagem); ?>
                </div>
            <?php endif; ?>

            <form method="POST" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="pergunta">Pergunta*</label>
                    <textarea id="pergunta" name="pergunta" class="editor-content" required>
                        <?php echo isset($_POST['pergunta']) ? $_POST['pergunta'] : ''; ?>
                    </textarea>
                </div>

                <div class="form-group">
                    <label for="resposta">Resposta*</label>
                    <textarea id="resposta" name="resposta" class="editor-content" required>
                        <?php echo isset($_POST['resposta']) ? $_POST['resposta'] : ''; ?>
                    </textarea>
                </div>

                <div class="form-group">
                    <label for="resumo">Resumo</label>
                    <textarea id="resumo" name="resumo" class="editor-content">
                        <?php echo isset($_POST['resumo']) ? $_POST['resumo'] : ''; ?>
                    </textarea>
                </div>

                <div class="form-group">
                    <label for="previsao_legal">Previsão Legal</label>
                    <textarea id="previsao_legal" name="previsao_legal" class="editor-content">
                        <?php echo isset($_POST['previsao_legal']) ? $_POST['previsao_legal'] : ''; ?>
                    </textarea>
                </div>

                <div class="form-group">
                    <label for="jurisprudencia">Jurisprudência</label>
                    <textarea id="jurisprudencia" name="jurisprudencia" class="editor-content">
                        <?php echo isset($_POST['jurisprudencia']) ? $_POST['jurisprudencia'] : ''; ?>
                    </textarea>
                </div>

                <div class="form-group">
                    <label>Mapa Mental (Imagem)</label>
                    <div class="file-input-container">
                        <label class="file-input-button">
                            <i class="fas fa-upload"></i>
                            Escolher Arquivo
                            <input type="file" class="file-input-hidden" id="mapa_mental" name="mapa_mental" accept="image/*">
                        </label>
                        <div class="file-name" id="fileName">Nenhum arquivo selecionado</div>
                    </div>
                </div>

                <div class="actions">
                    <a href="ver_cards.php?topico=<?php echo $topico_id; ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-check"></i>
                        Criar Card
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Configuração do TinyMCE
        tinymce.init({
            selector: '.editor-content',
            height: 300,
            menubar: false,
            language: 'pt_BR',
            language_url: 'https://cdn.tiny.cloud/1/no-api-key/tinymce/6/langs/pt_BR.js',
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap',
                'visualblocks', 'code', 'fullscreen',
                'table', 'help', 'wordcount', 'emoticons'
            ],
            toolbar: [
                'styles | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify',
                'bullist numlist | outdent indent | forecolor backcolor | table | removeformat'
            ],
            content_style: `
                body {
                    font-family: 'Inter', system-ui, -apple-system, sans-serif;
                    font-size: 16px;
                    line-height: 1.6;
                    color: #2C3345;
                }
            `,
            browser_spellcheck: true,
            contextmenu: false,
            resize: true,
            paste_data_images: true,
            paste_as_text: false,
            table_style_by_css: true,
            table_sizing_mode: 'relative',
            table_default_attributes: {
                class: 'table-responsive'
            },
            table_default_styles: {
                width: '100%'
            },
            statusbar: false,
            elementpath: false,
            branding: false,
            promotion: false,
            translations: {
                'pt_BR': {
                    'Bold': 'Negrito',
                    'Italic': 'Itálico',
                    'Underline': 'Sublinhado',
                    'Strikethrough': 'Tachado',
                    'Align left': 'Alinhar à esquerda',
                    'Align center': 'Centralizar',
                    'Align right': 'Alinhar à direita',
                    'Justify': 'Justificar',
                    'Bullet list': 'Lista não ordenada',
                    'Numbered list': 'Lista ordenada',
                    'Decrease indent': 'Diminuir recuo',
                    'Increase indent': 'Aumentar recuo',
                    'Clear formatting': 'Limpar formatação'
                }
            },
            style_formats: [
                { title: 'Parágrafo', format: 'p' },
                { title: 'Título 1', format: 'h1' },
                { title: 'Título 2', format: 'h2' },
                { title: 'Título 3', format: 'h3' },
                { title: 'Título 4', format: 'h4' }
            ],
            setup: function(editor) {
                editor.on('change', function() {
                    editor.save();
                });
            }
        });

        // Script para atualizar o nome do arquivo quando selecionado
        document.getElementById('mapa_mental').addEventListener('change', function(e) {
            const fileName = e.target.files[0] ? e.target.files[0].name : 'Nenhum arquivo selecionado';
            document.getElementById('fileName').textContent = fileName;
        });
    </script>
</body>
</html>