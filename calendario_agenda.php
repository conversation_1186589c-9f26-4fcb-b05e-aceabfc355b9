
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src='fullcalendar-6.1.8/dist/index.global.js'></script>
    <script src='fullcalendar-6.1.8/packages/core/locales/pt-br.global.js'></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Anton&family=Orbitron:wght@400;500;600;700;800;900&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Ysabeau+SC:wght@1;100;200;300;400;500;600;700;800;900;1000&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courier+Prime:ital,wght@0,400;0,700;1,400;1,700&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.6/jquery.inputmask.min.js"></script>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css"
          integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"
            integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy"
            crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">

    <!-- Fontes necessárias -->
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Indie+Flower&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <title>Calendário - Agenda Pessoal</title>

    <script src="assets/js/calendario_agenda.js"></script>
</head>
<link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">

<style>

    /* Estilo do modal específico */
    #modalEvento.modal-especifico {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        justify-content: center;
        align-items: center;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
    }

    .modal-especifico-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
    }

    .modal-especifico-content {
        position: relative;
        background: white;
        padding: 2rem;
        border-radius: 8px;
        max-width: 600px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }

    .modal-especifico-close {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: var(--burgundy);
        border: none;
        color: white;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    .modal-especifico-close:hover{
        background: var(-vintage-gold);
        transform: rotate(90deg);

    }

    .modal-especifico-titulo {
        font-family: 'Cinzel', serif;
        color: var(--dark-bg);
        margin-bottom: 1.5rem;
        font-size: 1.5rem;
        background: rgba(0, 0, 0, 0.1);
        border-bottom: 2px solid var(--vintage-gold);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.8rem 0;
        border-radius: 4px;
    }

    .modal-especifico-badge.realizado {
        background-color: green;
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
    }

    .modal-especifico-badge.pendente {
        background-color: red;
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
    }

    .modal-green-bg {
        background-color: lightgreen; /* Cor de fundo verde claro */
    }

    .modal-red-bg {
        background-color: #fca3a3; /* Cor de fundo vermelho claro */
    }



    /* Ajustar altura do calendário */
    .fc {
        min-height: 700px !important; /* Aumenta a altura mínima */
    }

    /* Ajustar tamanho das células dos dias */
    .fc-daygrid-day {
        min-height: 120px !important; /* Aumenta a altura mínima das células */
    }

    /* Aumentar o texto dentro do calendário */
    .fc {
        font-size: 1.1rem !important; /* Aumenta o tamanho da fonte */
    }

    /* Aumentar cabeçalho dos dias */
    .fc-col-header-cell {
        padding: 10px !important;
        font-size: 1.2rem !important;
    }

    /* Ajustar container principal para tela cheia */

    /* Responsividade */
    @media (min-width: 1600px) {
        #calendario_agenda {
            max-width: 1600px !important;
        }
    }

    @media (max-width: 1200px) {
        #calendario_agenda {
            max-width: 95% !important;
        }
    }

    @media (max-width: 768px) {
        .fc {
            font-size: 1rem !important;
        }

        .fc-col-header-cell {
            font-size: 1.1rem !important;
        }
    }

    :root {
        --parchment: #f8f0e3;
        --vintage-gold: #b8860b;
        --dark-ink: #1a1a1a;
        --burgundy: #800020;
        --gold-accent: #daa520;
        --shadow-color: rgba(0, 0, 0, 0.2);
        --elegant-border: linear-gradient(45deg, var(--vintage-gold), #ffd700, var(--vintage-gold));
    }

    body, html {
        margin: 0;
        padding: 0;
        height: 100%;
        font-family: 'Quicksand', sans-serif;
        color: var(--dark-ink);
        line-height: 1.6;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    #header {
        background: var(--parchment);
        background-image:
                linear-gradient(rgba(255, 255, 255, .2) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, .2) 1px, transparent 1px);
        background-size: 20px 20px;
        border: 1px solid var(--vintage-gold);
        box-shadow:
                0 0 0 1px var(--vintage-gold),
                0 0 0 15px var(--parchment),
                0 0 0 16px var(--vintage-gold),
                0 2px 5px 16px var(--shadow-color);
        padding: 2.5rem;
        position: relative;
        border-radius: 2px;
        margin-bottom: 2rem;
    }

    .titulo {
        font-family: 'Cinzel', serif;
        color: var(--burgundy);
        margin-bottom: 1rem;
    }

    h2.titulo {
        font-size: 2.5rem;
        text-align: center;
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        position: relative;
    }

    h2.titulo::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 200px;
        height: 3px;
        background: var(--elegant-border);
    }

    /* Estilo para o select de matéria */
    /* Estilo para os selects */
    select.campoTipo,
    #materia {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid var(--vintage-gold);
        background: rgba(255, 255, 255, 0.7);
        font-family: 'Quicksand', sans-serif;
        font-size: 1rem;
        transition: all 0.3s ease;
        border-radius: 4px;
        margin-bottom: 1rem;
        height: 55px; /* Aumentada a altura */
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23b8860b' d='M6 8.825L1.175 4 2.238 2.938 6 6.7l3.763-3.762L10.825 4z'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 1rem center;
        background-size: 1em;
        cursor: pointer;
        line-height: 1.5; /* Ajuste da linha para melhor alinhamento vertical */
    }

    select.campoTipo:focus,
    #materia:focus {
        border-color: var(--burgundy);
        background-color: rgba(255, 255, 255, 0.9);
        outline: none;
        box-shadow: 0 0 0 2px rgba(128, 0, 32, 0.1);
    }

    /* Estilo para as opções dentro do select */
    select.campoTipo option,
    #materia option {
        padding: 12px; /* Aumentado o padding das opções */
        font-size: 1rem;
        background-color: var(--parchment);
        color: var(--dark-ink);
        min-height: 40px; /* Altura mínima para cada opção */
        line-height: 1.5; /* Ajuste da linha para as opções */
    }

    input[type="text"],
    input[type="date"] {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid var(--vintage-gold);
        background: rgba(255, 255, 255, 0.7);
        font-family: 'Quicksand', sans-serif;
        font-size: 1rem;
        transition: all 0.3s ease;
        border-radius: 4px;
        margin-bottom: 1rem;
    }

    .details-container {
        margin: 1.5rem 0;
    }

    .details-container label {
        display: block;
        margin-bottom: 0.5rem;
    }

    textarea.campoEstudado {
        width: 100%;
        min-height: 300px; /* Aumenta a altura mínima para que o campo seja exibido maior inicialmente */
        padding: 0.75rem 1rem;
        border: 2px solid var(--vintage-gold);
        background: rgba(255, 255, 255, 0.7);
        font-family: 'Quicksand', sans-serif;
        font-size: 1rem;
        transition: all 0.3s ease;
        border-radius: 4px;
        margin-bottom: 1rem;
        resize: vertical; /* Permite redimensionar verticalmente, se necessário */
    }

    select.campoTipo:focus,
    input[type="text"]:focus,
    input[type="date"]:focus,
    textarea.campoEstudado:focus {
        border-color: var(--burgundy);
        background: rgba(255, 255, 255, 0.9);
        outline: none;
        box-shadow: 0 0 0 2px rgba(128, 0, 32, 0.1);
    }

    .button-group {
        display: flex;
        gap: 1rem;
        margin-top: 2rem;
    }

    .btn-elegant {
        font-family: 'Cinzel', serif;
        background: transparent;
        border: 2px solid var(--vintage-gold);
        color: var(--dark-ink);
        padding: 1rem 2rem;
        font-size: 1.1rem;
        text-transform: uppercase;
        letter-spacing: 2px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        z-index: 1;
        flex: 1;
        transition: all 0.4s ease;
        border-radius: 4px;
    }

    .btn-elegant::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--elegant-border);
        transition: all 0.4s ease;
        z-index: -1;
    }

    .btn-elegant:hover::before {
        left: 0;
    }

    .btn-elegant.btn-close {
        border-color: #8B0000;
        color: #8B0000;
    }

    .btn-elegant.btn-close::before {
        background: linear-gradient(45deg, #8B0000, #FF0000, #8B0000);
    }

    .btn-elegant.btn-close:hover {
        color: white;
        border-color: transparent;
    }


    /* Ajustar altura do calendário */
    .fc {
        min-height: 700px !important;
    }

    /* Ajustar tamanho das células dos dias */
    .fc-daygrid-day {
        min-height: 120px !important;
    }

    /* Aumentar o texto dentro do calendário */
    .fc {
        font-size: 1rem !important;
    }

    /* Aumentar cabeçalho dos dias */
    .fc-col-header-cell {
        padding: 10px !important;
        font-size: 1.2rem !important;
    }

    .legenda-eventos {
        margin-top: 1.5rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.7);
        border: 1px solid var(--vintage-gold);
        border-radius: 4px;
    }

    .legenda-item {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
        margin: 0.5rem 0;
    }

    .legenda-cor {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        border: 1px solid rgba(0, 0, 0, 0.1);
    }

    .legenda-texto {
        font-family: 'Quicksand', sans-serif;
        color: var(--dark-ink);
    }

    @media (max-width: 768px) {
        .container {
            padding: 1rem;
        }

        #header {
            padding: 1.5rem;
        }

        h2.titulo {
            font-size: 2rem;
        }

        .button-group {
            flex-direction: column;
        }

        .btn-elegant {
            width: 100%;
        }

        .legenda-item {
            flex-direction: column;
            align-items: center;
        }
    }

    @media (max-width: 480px) {
        h2.titulo {
            font-size: 1.5rem;
        }

        .legenda-eventos {
            padding: 0.5rem;
        }
    }

    /* Adicione isso ao seu CSS existente */

    .legenda-pendente {
        background-color: white; /* Fundo branco */
        border: 2px solid #FF0000; /* Borda vermelha para pendente */
    }

    .legenda-realizado {
        background-color: white; /* Fundo branco */
        border: 2px solid #008000; /* Borda verde para realizado */
    }

    .legenda-item {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        margin: 0.5rem 0;
    }

    .legenda-cor {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        display: inline-block;
        margin-right: 5px;
    }

    .legenda-texto {
        font-family: 'Quicksand', sans-serif;
        color: var(--dark-ink);
        margin-right: 15px;
    }

    /* Estilo para o campo de matéria e datalist */
    /* Ajuste do espaçamento do container */


    /* Estilo para o label */


    /* Estilo para as opções do datalist */
    input[list="materias-list"] {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid var(--vintage-gold);
        background: rgba(255, 255, 255, 0.7);
        font-family: 'Quicksand', sans-serif;
        font-size: 1rem;
        transition: all 0.3s ease;
        border-radius: 4px;
        height: 45px;
    }

    /* Animação suave para quando o campo aparece/desaparece */
    #campo_materia {
        opacity: 0;
        transform: translateY(-10px);
        transition: all 0.3s ease;
    }

    #campo_materia.visible {
        opacity: 1;
        transform: translateY(0);
    }

    /* Estilização base do Modal do FullCalendar */
    .fc-popover {
        background: var(--parchment) !important;
        background-image:
                linear-gradient(rgba(255, 255, 255, .2) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255, 255, 255, .2) 1px, transparent 1px) !important;
        background-size: 20px 20px !important;
        border: 1px solid var(--vintage-gold) !important;
        box-shadow:
                0 0 0 1px var(--vintage-gold),
                0 0 0 10px var(--parchment),
                0 0 0 11px var(--vintage-gold),
                0 2px 5px 11px var(--shadow-color) !important;
        border-radius: 4px !important;
        padding: 0 !important;
    }

    /* Cores específicas para cada tipo de evento */
    .fc-event[data-tipo="Faculdade"] {
        background-color: #1976D2 !important;
        color: white !important;
    }

    .fc-event[data-tipo="Trabalho"] {
        background-color: gray !important;
        color: white !important;
    }

    .fc-event[data-tipo="Concurso"] {
        background-color: blue !important;
        color: white !important;
    }

    .fc-event[data-tipo="Pessoal"] {
        background-color: #00796B !important;
        color: white !important;
    }

    .fc-event[data-tipo="Planejamento"] {
        background-color: #FFD700 !important;
        color: black !important;
    }

    /* Borda para eventos realizados/não realizados */
    .fc-event[data-realizado="true"] {
        border: 2px solid #008000 !important; /* Borda verde para eventos realizados */
    }

    .fc-event[data-realizado="false"] {
        border: 2px solid #FF0000 !important; /* Borda vermelha para eventos não realizados */
    }

    /* Resto da estilização */
    .fc-popover-header {
        background: transparent !important;
        border-bottom: 2px solid var(--vintage-gold) !important;
        padding: 1rem !important;
    }

    .fc-popover-title {
        font-family: 'Cinzel', serif !important;
        color: var(--burgundy) !important;
        font-size: 1.2rem !important;
        margin: 0 !important;
    }

    .fc-popover-close {
        opacity: 1 !important;
        color: var(--burgundy) !important;
        font-size: 1.2rem !important;
        padding: 0.5rem !important;
    }

    .fc-popover-close:hover {
        color: var(--vintage-gold) !important;
    }

    .fc-popover-body {
        padding: 1rem !important;
        background: rgba(255, 255, 255, 0.5) !important;
    }

    .fc-event {
        padding: 2px 4px !important; /* Reduz o padding para tornar o evento mais compacto */
        margin-bottom: 2px !important; /* Reduz a margem entre os eventos */
        border-radius: 2px !important; /* Mantém o arredondamento mais sutil */
    }

    .fc-event-title {
        font-family: 'Quicksand', sans-serif !important;
        font-size: 0.9rem !important; /* Diminuído o tamanho da fonte do título */
    }

    .fc-event-time {
        font-family: 'Quicksand', sans-serif !important;
        font-weight: bold !important;
        font-size: 0.9rem !important; /* Diminuído o tamanho da fonte do horário */
    }

    .fc-event:hover {
        transform: translateX(5px) !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    }


    .fc-more-link {
        background: transparent !important;
        color: var(--burgundy) !important;
        font-family: 'Cinzel', serif !important;
        padding: 0.3rem 0.6rem !important;
        border: 1px solid var(--vintage-gold) !important;
        border-radius: 4px !important;
        transition: all 0.3s ease !important;
    }

    .fc-more-link:hover {
        background: var(--vintage-gold) !important;
        color: white !important;
        text-decoration: none !important;
    }

    @keyframes fcPopoverIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .fc-popover {
        animation: fcPopoverIn 0.3s ease-out !important;
    }


    /* Container do formulário */
    #formContainer {
        width: 90% !important; /* Define a mesma largura do #calendar */
        max-width: 1100px !important; /* Define o limite máximo de largura, igual ao calendário */
        margin: 2rem auto !important; /* Centraliza o formulário */
        padding: 1.5rem;
        background: var(--parchment);
        border: 2px solid var(--vintage-gold);
        border-radius: 4px;
        transition: max-height 0.5s ease-in-out;
    }

    /* Grid para organizar os campos */
    .form-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr); /* 3 colunas */
        gap: 1rem;
        align-items: start;
    }

    /* Ajustes dos campos individuais */
    .form-field {
        margin-bottom: 1rem;
    }

    /* Campo de tipo ocupa uma coluna */
    #campo_tipo {
        grid-column: 1 / 2;
    }

    /* Campo de título/matéria ocupa uma coluna */
    #campo_titulo,
    #campo_materia {
        grid-column: 2 / 3;
    }

    /* Campo de data ocupa uma coluna */
    #data_inicio {
        grid-column: 3 / 4;
    }

    /* Campo de detalhes ocupa largura total */
    .details-container {
        grid-column: 1 / -1;
    }

    /* Ajuste da altura do textarea */
    textarea.campoEstudado {
        min-height: 120px;
        max-height: 150px;
    }

    /* Ajuste dos botões */
    .form-buttons {
        grid-column: 1 / -1;
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
    }

    /* Centraliza o botão no topo e ajusta estilo */
    /* Estilo do botão de toggle */
    .btn-toggle-form {
        width: 100%;
        background: var(--parchment);
        border: 2px solid var(--vintage-gold);
        padding: 0.75rem;
        font-family: 'Cinzel', serif;
        color: var(--burgundy);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .btn-toggle-form::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: var(--elegant-border);
        transition: all 0.4s ease;
        z-index: 0;
    }

    .btn-toggle-form:hover::before {
        left: 0;
    }

    .btn-toggle-form i,
    .btn-toggle-form span {
        position: relative;
        z-index: 1;
    }

    /* Container do formulário com animação */
    #formContainer {
        max-height: 1000px; /* Ajuste este valor conforme necessário */
        overflow: hidden;
        transition: max-height 0.5s ease-in-out;
        background: var(--parchment);
        border: 2px solid var(--vintage-gold);
        border-radius: 4px;
        margin-bottom: 1rem;
    }

    #formContainer.collapsed {
        max-height: 0;
        border: none;
        margin-bottom: 0;
    }

    /* Rotação do ícone */
    #toggleIcon {
        transition: transform 0.3s ease;
    }

    #toggleIcon.rotated {
        transform: rotate(180deg);
    }

    .collapsed {
        display: none;
    }

    #eventoRealizadoCheckboxContainer {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    #eventoRealizadoCheckbox {
        display: inline-block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }




</style>

<body>


<!-- Envolva seu formulário existente em uma div -->
<!-- Container do formulário com a classe "collapsed" para ocultar por padrão -->

<div id="calendario_agenda"></div>

<!-- Legenda de tipos de evento -->


<div id="modalEvento" class="modal-especifico" style="display: none;">
    <div class="modal-especifico-overlay"></div>
    <div class="modal-especifico-content">
        <button class="modal-especifico-close"><i class="fas fa-times"></i></button>
        <h2 class="modal-especifico-titulo">Título do Evento</h2>
        <div class="status-section modal-especifico-status-section">
            <span class="status-badge realizado modal-especifico-badge">
                <i class="fas fa-check-circle"></i> Realizado
            </span>
            <span class="status-badge pendente modal-especifico-badge">
                <i class="fas fa-clock"></i> Pendente
            </span>
        </div>
        <div class="details-container modal-especifico-detalhes"></div>

        <!-- Checkbox para marcar o evento como realizado -->
        <div style="margin-top: 10px;">
            <!-- Checkbox para marcar o evento como realizado -->
            <!-- Checkbox para marcar o evento como realizado -->
            <div id="eventoRealizadoCheckboxContainer" style="display: block; margin-top: 10px;">
                <label>
                    <input type="checkbox" id="eventoRealizadoCheckbox"> Marcar como realizado
                </label>
            </div>


        </div>

        <div class="button-group">
            <button class="btn-elegant salvarEventoModal">
                <i class="fas fa-save"></i> Salvar
            </button>
            <button class="btn-elegant excluirEventoModal">
                <i class="fas fa-trash"></i> Excluir
            </button>
        </div>
    </div>
</div>


    <!-- Local onde o calendário será renderizado -->
<script>

    window.addEventListener("beforeunload", function () {
        if (window.opener) {
            window.opener.location.reload();
        }
    });




</script>

</body>
</html>
