<?php
include_once("../assets/config.php");
include_once("../includes/auth.php");

// Verifica autenticação e obtém ID do usuário
$id_usuario = verificarAutenticacao($conexao);

// Verifica conexão com banco
if (!$conexao) {
    die("Erro na conexão: " . pg_last_error());
}

// Verifica se o usuário já tem um planejamento
if (isset($_SESSION['idusuario'])) {
    $id_usuario = $_SESSION['idusuario'];
    $query_verificar_planejamento = "SELECT COUNT(*) as total 
                                     FROM appEstudo.planejamento 
                                     WHERE usuario_idusuario = $1";
    $resultado_verificar = pg_query_params($conexao, $query_verificar_planejamento, array($id_usuario));
    $row = pg_fetch_assoc($resultado_verificar);
    
    // Se o usuário já tem um planejamento, redireciona para a página de edição
    if ($row['total'] > 0) {
        header("Location: editar_planejamento.php");
        exit;
    }
    
    // Consulta para obter o nome do usuário
    $query_usuario = "SELECT nome FROM appEstudo.usuario WHERE idusuario = $1";
    $resultado_usuario = pg_query_params($conexao, $query_usuario, array($id_usuario));
    $usuario = pg_fetch_assoc($resultado_usuario);
    $nome_usuario = isset($usuario['nome']) ? $usuario['nome'] : 'Usuário';
    
    // Consulta para obter todas as matérias
    $query_consultar_materias = "SELECT * FROM appEstudo.materia ORDER BY nome ASC";
    $resultado_materias = pg_query($conexao, $query_consultar_materias);
    $todas_materias = pg_fetch_all($resultado_materias);
    
    // Consulta para obter todos os cursos
    $query_cursos = "SELECT * FROM appEstudo.curso ORDER BY nome ASC";
    $resultado_cursos = pg_query($conexao, $query_cursos);
    $todos_cursos = pg_fetch_all($resultado_cursos);

    // Consulta para obter os editais com suas matérias
    $query_editais = "
        SELECT DISTINCT e.*, 
            (SELECT string_agg(materia_info, '|')
            FROM (
                SELECT DISTINCT ON (m.nome) m.idmateria || ',' || m.nome || ',' || m.cor as materia_info, m.nome
                FROM appestudo.materia m 
                JOIN appestudo.conteudo_edital ce ON m.idmateria = ce.materia_id 
                WHERE ce.edital_id = e.id_edital
                ORDER BY m.nome, m.idmateria
            ) subq) as materias
        FROM appestudo.edital e
        LEFT JOIN appestudo.conteudo_edital ce ON e.id_edital = ce.edital_id
        WHERE ce.materia_id IS NOT NULL
        ORDER BY e.nome";
    
    $result_editais = pg_query($conexao, $query_editais);
    $editais = [];
    while ($edital = pg_fetch_assoc($result_editais)) {
        $materias_array = [];
        if (!empty($edital['materias'])) {
            $materias = explode('|', $edital['materias']);
            $materias_unicas = array_unique($materias); // Remove duplicatas
            foreach ($materias_unicas as $materia) {
                list($id, $nome, $cor) = explode(',', $materia);
                $materias_array[] = [
                    'idmateria' => $id,
                    'nome' => $nome,
                    'cor' => $cor
                ];
            }
        }
        $edital['materias'] = $materias_array;
        $editais[] = $edital;
    }
}
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cadastrar Planejamento</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://unpkg.com/vue@3"></script>
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <style>
:root {
    --primary: #00008B;
    --primary-light: #3949ab;
    --secondary: #f8f0e3;
    --accent: #4a4a4a;
    --border: #e0e0e0;
    --text: #2c3e50;
    --active: #00008B;
    --hover: #f5f5f5;
    --primary-blue: #00008B;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --background: #f5f5f5;
    --card-background: white;
}
[data-theme="dark"] {
    --primary: #4169E1;
    --secondary: #1a1a2e;
    --accent: #6c7293;
    --border: #2d2d42;
    --text: #e4e6f0;
    --active: #4169E1;
    --hover: #232338;
    --primary-blue: #4169E1;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --background: #0a0a1f;
    --card-background: #13132b;
}

body {
    background: var(--background);
    color: var(--text);
    font-family: 'Varela Round', 'Quicksand', 'Open Sans', Arial, sans-serif;
    margin: 0;
    padding: 0;
    line-height: 1.6;
}
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2.5rem 1.5rem;
}
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border);
    margin-bottom: 30px;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 2px 4px var(--shadow-color);
    background: linear-gradient(90deg, var(--primary) 60%, var(--primary) 100%);
}

.header-left {
    display: flex;
    align-items: center;
}

.logo {
    height: 50px;
    margin-right: 15px;
}

.logo img {
    height: 100%;
}

.header-right {
    display: flex;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    margin-right: 20px;
    color: white;
}

.user-info i {
    font-size: 1.5rem;
    margin-right: 8px;
}

.user-name {
    font-weight: 500;
}

.theme-toggle {
    display: flex;
    align-items: center;
}

.theme-btn {
    background: transparent;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.theme-btn:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.card {
    background: var(--card-background);
    border-radius: 12px;
    box-shadow: 0 4px 8px var(--shadow-color);
    padding: 2rem 2rem;
    margin-bottom: 2.5rem;
    border: 1px solid var(--border);
    transition: box-shadow 0.2s, transform 0.2s;
}
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 14px var(--shadow-color);
}
h2 {
    color: var(--primary);
    font-size: 1.5rem;
    margin-top: 0;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}
h2 i {
    margin-right: 10px;
}
.form-group {
    margin-bottom: 1.5rem;
}
.form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 1.5rem;
}
.form-row .form-group {
    flex: 1;
    min-width: 200px;
    margin-bottom: 0;
}
label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text);
}
input[type="text"],
input[type="date"],
input[type="time"],
select {
    width: 100%;
    padding: 10px 5px;
    border: 1px solid var(--border);
    border-radius: 8px;
    font-family: inherit;
    font-size: 1rem;
    background-color: var(--card-background);
    color: var(--text);
    transition: border-color 0.2s, box-shadow 0.2s;
}
input[type="text"]:focus,
input[type="date"]:focus,
input[type="time"]:focus,
select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(0, 0, 139, 0.2);
}
button {
    background-color: var(--primary);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.2s;
}
button:hover {
    background-color: var(--primary-light);
    transform: translateY(-2px);
}
.tab-container {
    margin-bottom: 1.5rem;
}
.tab-buttons {
    display: flex;
    border-bottom: 1px solid var(--border);
    margin-bottom: 1.5rem;
}
.tab-btn.active {
    color: var(--primary);
    border-bottom-color: var(--primary);
    background: var(--background);
}
.tab-btn {
    padding: 1em 2em;
    background: transparent;
    border: none;
    border-bottom: 3px solid transparent;
    color: var(--text);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 1.08rem;
    border-radius: 10px 10px 0 0;
}
button {
    background-color: var(--primary);
    color: white;
    border: none;
    border-radius: 10px;
    padding: 14px 28px;
    font-size: 1.08rem;
    font-weight: 600;
    cursor: pointer;
    transition: background-color 0.2s, transform 0.2s, box-shadow 0.2s;
    box-shadow: 0 2px 8px var(--shadow-color);
    letter-spacing: 0.01em;
}
.tab-btn:hover:not(.active) {
    background-color: var(--hover);
}
.tab-content {
    display: none;
}
.tab-content.active {
    display: block;
}
.search-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    margin-bottom: 1rem;
}
.search-bar {
    position: relative;
    max-width: 400px;
    width: 100%;
    margin-bottom: 0;
}
.search-bar input {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 2.5rem;
    border: 2px solid var(--primary);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: white;
    color: var(--text);
    box-sizing: border-box;
}
.search-bar input:focus {
    border-color: var(--primary-light);
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 0, 139, 0.08);
}
.search-bar i.fas.fa-search {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--primary);
    pointer-events: none;
    z-index: 2;
    font-size: 1.1rem;
}
.clear-search {
    position: absolute;
    right: 0.5rem;
    top: 18%;
    cursor: pointer;
    color: #fff;
    background: var(--primary);
    border: none;
    border-radius: 6px;
    padding: 0.4rem 0.7rem;
    z-index: 3;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
}
.clear-search:active, .clear-search:focus {
    /* Remove qualquer efeito de deslocamento vertical */
    top: 50%;
    transform: translateY(-50%);
}
.search-results-count {
    font-size: 0.92rem;
    color: var(--primary);
    background: #f3f6fa;
    border-radius: 4px;
    display: inline-block;
    margin-left: 0.1rem;
    margin-top: 0.3rem;
    padding: 0.18rem 0.7rem;
    font-weight: 500;
    letter-spacing: 0.01em;
    box-shadow: 0 1px 2px var(--shadow-color);
}
.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 1.5rem;
}
.checkbox-wrapper {
    display: flex;
    align-items: center;
    padding: 10px;
    border-radius: 8px;
    /*border: 1px solid var(--border);*/
    transition: all 0.2s;
}
.checkbox-wrapper:hover {
  /*
    background-color: var(--hover);
    border-color: var(--primary-light);
    */
}
input[type="checkbox"] {
    accent-color: var(--primary);
    width: 20px;
    height: 20px;
    border-radius: 5px;
    margin-right: 10px;
    transition: box-shadow 0.2s;
    box-shadow: 0 1px 2px var(--shadow-color);
}

.checkbox-wrapper input[type="checkbox"] {
    margin-right: 10px;
}
.color-preview {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    margin-right: 10px;
    display: inline-block;
    vertical-align: middle;
}
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}
.modal {
    background-color: var(--card-background);
    border-radius: 12px;
    padding: 2rem;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    text-align: center;
}
.modal h3 {
    margin-top: 0;
    color: var(--primary);
}
.modal-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 1.5rem;
}
.modal-buttons button {
    min-width: 120px;
}
.modal-buttons button.secondary {
    background-color: var(--accent);
}
.modal-buttons button.secondary:hover {
    background-color: #5a5a5a;
}
.checkbox-wrapper span {
    font-size: 1rem;
    font-weight: 500;
}
.color-box {
    display: none;
}
.curso-logo {
    width: 53.19px;
    height: 53.19px;
    object-fit: contain;
    margin-right: 8px;
    border-radius: 5px;
    background: #fff;
    border: 1px solid #eee;
}
.alert-info {
    background-color: var(--primary-light);
    color: white;
    padding: 12px 16px;
    border-radius: 8px;
    margin-top: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 500;
}
.alert-info i {
    font-size: 1.2rem;
    /*color: var(--primary);*/
}
@media (max-width: 900px) {
    .container {
        padding: 1.2rem;
    }
    .header {
        padding: 1.5rem 0.7rem;
    }
    .card {
        padding: 1.2rem 0.7rem;
    }
}
@media (max-width: 600px) {
    .header h1 {
        font-size: 1.3rem;
    }
    .card {
        padding: 0.9rem 0.4rem;
    }
    .tab-btn {
        padding: 0.6em 1.1em;
        font-size: 1em;
    }
}

/* Ajustes para o modo escuro */
[data-theme="dark"] .logo-light {
    display: none;
}

[data-theme="dark"] .logo-dark {
    display: block;
}

.logo-dark {
    display: none;
}

.logo-light {
    display: block;
}

.welcome-message {
    text-align: center;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: var(--card-background);
    border-radius: 12px;
    box-shadow: 0 4px 8px var(--shadow-color);
}

.welcome-message h2 {
    color: var(--primary);
    margin-bottom: 1rem;
    justify-content: center;
}

.welcome-message p {
    font-size: 1.1rem;
    line-height: 1.6;
}

.welcome-icon {
    font-size: 3rem;
    color: var(--primary);
    margin-bottom: 1rem;
}

.subjects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.subject-item {
    background: var(--card-background);
    padding: 1rem;
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.subject-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}


.checkbox-wrapper input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.checkbox-wrapper span {
    font-size: 1rem;
    font-weight: 500;
}

/* Animações para os modais */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}
@keyframes scaleIn {
    from { transform: scale(0.95); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}
@keyframes scaleOut {
    from { transform: scale(1); opacity: 1; }
    to { transform: scale(0.95); opacity: 0; }
}
.edital-info {
    /*padding: 1rem;*/
    border-bottom: 1px solid var(--border);
    margin-bottom: 1rem;
}

.edital-nome {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary);
    margin: 0 0 0.5rem 0;
}

.edital-orgao {
    font-size: 0.9rem;
    color: var(--accent);
    display: block;
    margin-bottom: 0.5rem;
}

.edital-descricao {
    font-size: 0.9rem;
    color: var(--accent);
    margin: 0.5rem 0;
    line-height: 1.4;
    font-style: italic;
}

.materias-count {
    font-size: 0.8rem;
    color: var(--accent);
    background: var(--hover);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    display: inline-block;
}

.materias-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 0.75rem;
    margin-top: 1rem;
}

.subject-item {
    padding: 0.75rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.subject-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.checkbox-wrapper {
    display: flex;
    align-items: center;
    /*gap: 0.75rem;*/
    width: 100%;
}

.checkbox-wrapper input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}

.checkbox-wrapper span {
    font-size: 1rem;
    font-weight: 500;
}

.edital-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 1rem;
    border-bottom: 1px solid var(--border);
    transition: background-color 0.2s ease;
}

.edital-header:hover {
    background-color: var(--hover);
}

.edital-header-content {
    flex: 1;
}

.edital-toggle {
    padding: 0.5rem;
    color: var(--accent);
    transition: transform 0.2s ease;
}

.materias-container {
    /*padding: 1rem;*/
    background-color: var(--card-background);
    border-radius: 0 0 8px 8px;
}

.materias-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 0.75rem;
}
</style>
</head>
<body>
    <div id="app">
        <div class="header">
            <div class="header-left">
                <div class="logo">
                    <img src="../logo/logo_vertical.png" alt="Logo" class="logo-light">
                    <img src="../logo/logo_vertical.png" alt="Logo" class="logo-dark">
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span class="user-name"><?php echo htmlspecialchars($nome_usuario); ?></span>
                </div>
                <div class="theme-toggle">
                    <button id="theme-toggle-btn" class="theme-btn">
                        <i class="fas fa-moon" id="theme-icon"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="container">
            <div class="welcome-message">
                <i class="fas fa-clipboard-list welcome-icon"></i>
                <h2>Bem-vindo ao seu primeiro planejamento!</h2>
                <p>Vamos começar a organizar seus estudos. Preencha as informações abaixo para criar seu planejamento personalizado.</p>
                <div class="alert-info">
                    <i class="fas fa-info-circle"></i>
                    <span>É necessário selecionar pelo menos uma matéria (Podendo ser da Aba Matérias ou Editais) e um curso para criar seu planejamento.</span>
                </div>
            </div>

            <!-- Formulário de cadastro de planejamento -->
            <form @submit.prevent="salvarPlanejamento">
                <div class="card">
                    <h2>
                        <i class="fas fa-info-circle"></i>
                        Informações Básicas
                    </h2>
                    <div class="form-group">
                        <label>Nome do Planejamento</label>
                        <input 
                            type="text" 
                            v-model="planejamento.nome" 
                            placeholder="Digite o nome do seu planejamento"
                            required
                        >
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Data de Início</label>
                            <input type="date" v-model="planejamento.data_inicio" required>
                        </div>
                        <div class="form-group">
                            <label>Data de Fim</label>
                            <input type="date" v-model="planejamento.data_fim" required>
                        </div>
                        <div class="form-group">
                            <label>Tempo Disponível Diário</label>
                            <input type="time" v-model="planejamento.tempo_planejamento" required>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="tab-container">
                        <div class="tab-buttons">
                            <button 
                                type="button" 
                                class="tab-btn" 
                                :class="{ active: abaAtiva === 'materias' }"
                                @click="abaAtiva = 'materias'"
                            >
                                <i class="fas fa-book"></i> Matérias
                            </button>
                            <button 
                                type="button" 
                                class="tab-btn" 
                                :class="{ active: abaAtiva === 'editais' }"
                                @click="abaAtiva = 'editais'"
                            >
                                <i class="fas fa-file-alt"></i> Editais
                            </button>
                            <button 
                                type="button" 
                                class="tab-btn" 
                                :class="{ active: abaAtiva === 'cursos' }"
                                @click="abaAtiva = 'cursos'"
                            >
                                <i class="fas fa-graduation-cap"></i> Cursos
                            </button>
                        </div>
                        
                        <!-- Aba de Matérias -->
                        <div class="tab-content" :class="{ active: abaAtiva === 'materias' }">
                            <h2>
                                <i class="fas fa-book"></i>
                                Selecione as Matérias
                            </h2>
                            <div class="search-container">
                                <div class="search-bar">
                                    <i class="fas fa-search"></i>
                                    <input 
                                        type="text" 
                                        v-model="buscarMateria" 
                                        placeholder="Buscar matéria..."
                                    >
                                    <button 
                                        type="button" 
                                        class="clear-search" 
                                        v-if="buscarMateria" 
                                        @click="buscarMateria = ''"
                                    >
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="search-results-count" v-if="buscarMateria">
                                    {{ materiasFiltradas.length }} resultado(s) encontrado(s)
                                </div>
                            </div>
                            <div class="subjects-grid">
                                <div 
                                    v-for="materia in materiasFiltradas"
                                    :key="materia.idmateria" 
                                    class="subject-item"
                                    :style="{
                                        backgroundColor: materia.cor || '#ccc',
                                        color: isColorDark(materia.cor) ? '#ffffff' : '#000000'
                                    }"
                                >
                                    <div class="checkbox-wrapper">
                                        <input 
                                            type="checkbox" 
                                            :id="'materia-' + materia.idmateria" 
                                            :value="materia.idmateria" 
                                            v-model="planejamento.materias"
                                        >
                                        <span>{{ materia.nome }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Aba de Editais -->
                        <div class="tab-content" :class="{ active: abaAtiva === 'editais' }">
                            <h2>
                                <i class="fas fa-file-alt"></i>
                                Selecione as Matérias dos Editais
                            </h2>
                            <div class="search-container">
                                <div class="search-bar">
                                    <i class="fas fa-search"></i>
                                    <input 
                                        type="text" 
                                        v-model="buscarEdital" 
                                        placeholder="Buscar edital..."
                                    >
                                    <button 
                                        type="button" 
                                        class="clear-search" 
                                        v-if="buscarEdital" 
                                        @click="buscarEdital = ''"
                                    >
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="search-results-count" v-if="buscarEdital">
                                    {{ editaisFiltrados.length }} resultado(s) encontrado(s)
                                </div>
                            </div>
                            <div class="subjects-grid">
                                <div v-for="edital in editaisFiltrados" :key="edital.id_edital" class="card">
                                    <div class="edital-info">
                                        <div class="edital-header" @click="toggleEdital(edital.id_edital)" style="cursor: pointer;">
                                            <div class="edital-header-content">
                                                <h3 class="edital-nome">{{ edital.nome }}</h3>
                                                <span class="edital-orgao">{{ edital.orgao }}</span>
                                                <p class="edital-descricao" v-if="edital.descricao">{{ edital.descricao }}</p>
                                                <span class="materias-count">
                                                    <i class="fas fa-book"></i> {{ edital.materias.length }} matérias
                                                </span>
                                            </div>
                                            <div class="edital-toggle">
                                                <i class="fas" :class="editaisExpandidos.includes(edital.id_edital) ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                                            </div>
                                        </div>
                                        <div class="materias-container" v-show="editaisExpandidos.includes(edital.id_edital)">
                                            <div style="margin-bottom: 0.7rem; display: flex; align-items: center; gap: 0.5rem;">
                                                <input type="checkbox"
                                                       :id="'selecionar-todas-' + edital.id_edital"
                                                       :checked="edital.materias.every(m => planejamento.materias.includes(m.idmateria)) && edital.materias.length > 0"
                                                       @change="toggleSelecionarTodasMaterias(edital)"
                                                >
                                                <label :for="'selecionar-todas-' + edital.id_edital" style="font-weight: 500; cursor: pointer;">Selecionar todas</label>
                                            </div>
                                            <div class="materias-grid">
                                                <div v-for="materia in edital.materias" 
                                                     :key="materia.idmateria" 
                                                     class="subject-item"
                                                     :style="{
                                                         backgroundColor: materia.cor || '#ccc',
                                                         color: isColorDark(materia.cor) ? '#ffffff' : '#000000'
                                                     }">
                                                    <div class="checkbox-wrapper">
                                                        <input 
                                                            type="checkbox" 
                                                            :id="'materia-edital-' + materia.idmateria" 
                                                            :value="materia.idmateria" 
                                                            v-model="planejamento.materias"
                                                        >
                                                        <span>{{ materia.nome }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Aba de Cursos -->
                        <div class="tab-content" :class="{ active: abaAtiva === 'cursos' }">
                            <h2>
                                <i class="fas fa-graduation-cap"></i>
                                Selecione os Cursos
                            </h2>
                            <div class="search-container">
                                <div class="search-bar">
                                    <i class="fas fa-search"></i>
                                    <input 
                                        type="text" 
                                        v-model="buscarCurso" 
                                        placeholder="Buscar curso..."
                                    >
                                    <button 
                                        type="button" 
                                        class="clear-search" 
                                        v-if="buscarCurso" 
                                        @click="buscarCurso = ''"
                                    >
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div class="search-results-count" v-if="buscarCurso">
                                    {{ cursosFiltrados.length }} resultado(s) encontrado(s)
                                </div>
                            </div>
                            <div class="subjects-grid">
                                <div 
                                    v-for="curso in cursosFiltrados"
                                    :key="curso.idcurso" 
                                    class="subject-item"
                                >
                                    <div class="checkbox-wrapper">
                                        <input 
                                            type="checkbox" 
                                            :id="'curso-' + curso.idcurso" 
                                            :value="curso.idcurso" 
                                            v-model="planejamento.cursos"
                                        >
                                        <img :src="
                                            curso.logo_url
                                                ? '/cadastros/img/cursos/' + curso.logo_url.split('/').pop()
                                                : '/logo/Estudo Off/favicon_32x32.png'"
                                            alt="Logo do curso" 
                                            class="curso-logo"
                                        >
                                        <span>{{ curso.nome }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                
                    <div class="form-group" style="text-align: center;">
                        <button type="submit">
                            <i class="fas fa-save"></i> Salvar Planejamento
                        </button>
                    </div>
               
            </form>
        </div>
    </div>

    <script>
        // Função para aplicar tema salvo ou preferência do sistema
        function applyTheme(theme) {
            if (theme === 'dark') {
                document.documentElement.setAttribute('data-theme', 'dark');
            } else {
                document.documentElement.setAttribute('data-theme', '');
            }
        }
        
        // Detecta preferência do sistema
        function getPreferredTheme() {
            if (localStorage.getItem('theme')) {
                return localStorage.getItem('theme');
            }
            return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }
        
        // Função para atualizar o ícone do tema
        function updateThemeIcon(theme) {
            var icon = document.getElementById('theme-icon');
            if (icon) {
                if (theme === 'dark') {
                    icon.classList.remove('fa-moon');
                    icon.classList.add('fa-sun');
                } else {
                    icon.classList.remove('fa-sun');
                    icon.classList.add('fa-moon');
                }
            }
        }
        
        // Aplica tema ao carregar
        document.addEventListener('DOMContentLoaded', function() {
            var currentTheme = getPreferredTheme();
            applyTheme(currentTheme);
            updateThemeIcon(currentTheme);
            
            var btn = document.getElementById('theme-toggle-btn');
            if (btn) {
                btn.addEventListener('click', function() {
                    var currentTheme = document.documentElement.getAttribute('data-theme');
                    var newTheme = (currentTheme === 'dark') ? 'light' : 'dark';
                    applyTheme(newTheme);
                    updateThemeIcon(newTheme);
                    localStorage.setItem('theme', newTheme);
                });
            }
        });
        
        // Atualiza se preferência do sistema mudar
        window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
            if (!localStorage.getItem('theme')) {
                applyTheme(e.matches ? 'dark' : 'light');
            }
        });

        // Inicialização do Vue
        const { createApp } = Vue;
        
        createApp({
            data() {
                return {
                    abaAtiva: 'materias',
                    buscarMateria: '',
                    buscarCurso: '',
                    buscarEdital: '',
                    editalSelecionado: [],
                    editaisExpandidos: [],
                    planejamento: {
                        nome: '',
                        data_inicio: new Date().toISOString().split('T')[0],
                        data_fim: '', 
                        tempo_planejamento: '02:00',
                        materias: [],
                        cursos: []
                    },
                    materias: <?php echo json_encode($todas_materias ?? []); ?>,
                    cursos: <?php echo json_encode($todos_cursos ?? []); ?>,
                    editais: <?php echo json_encode($editais ?? []); ?>,
                    temaEscuro: false
                }
            },
            computed: {
                materiasFiltradas() {
                    if (!this.buscarMateria) return this.materias;
                    const busca = this.buscarMateria.toLowerCase();
                    return this.materias.filter(materia => 
                        materia.nome.toLowerCase().includes(busca)
                    );
                },
                cursosFiltrados() {
                    if (!this.buscarCurso) return this.cursos;
                    const busca = this.buscarCurso.toLowerCase();
                    return this.cursos.filter(curso => 
                        curso.nome.toLowerCase().includes(busca)
                    );
                },
                editaisFiltrados() {
                    if (!this.buscarEdital) return this.editais;
                    const busca = this.buscarEdital.toLowerCase();
                    return this.editais.filter(edital => 
                        edital.nome.toLowerCase().includes(busca) ||
                        edital.orgao.toLowerCase().includes(busca)
                    );
                }
            },
            methods: {
                isColorDark(hexColor) {
                    // Se não houver cor, retorna falso (texto escuro)
                    if (!hexColor) return false;
                    
                    // Remove o # se existir
                    hexColor = hexColor.replace('#', '');
                    
                    // Converte para RGB
                    const r = parseInt(hexColor.substr(0, 2), 16);
                    const g = parseInt(hexColor.substr(2, 2), 16);
                    const b = parseInt(hexColor.substr(4, 2), 16);
                    
                    // Calcula a luminosidade
                    // Fórmula: (0.299*R + 0.587*G + 0.114*B)
                    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
                    
                    // Se a luminosidade for menor que 0.5, a cor é escura
                    return luminance < 0.5;
                },
                validarFormulario() {
                    // Validar campos obrigatórios
                    if (!this.planejamento.nome.trim()) {
                        this.mostrarAlerta('Por favor, digite um nome para o planejamento.');
                        return false;
                    }
                    
                    if (!this.planejamento.data_inicio) {
                        this.mostrarAlerta('Por favor, selecione uma data de início.');
                        return false;
                    }
                    
                    if (!this.planejamento.data_fim) {
                        this.mostrarAlerta('Por favor, selecione uma data de fim.');
                        return false;
                    }
                    
                    if (!this.planejamento.tempo_planejamento) {
                        this.mostrarAlerta('Por favor, defina o tempo disponível diário.');
                        return false;
                    }
                    
                    // Validar datas
                    if (new Date(this.planejamento.data_fim) <= new Date(this.planejamento.data_inicio)) {
                        this.mostrarAlerta('A data de fim deve ser posterior à data de início.');
                        return false;
                    }
                    
                    // Validar seleção de matérias
                    if (this.planejamento.materias.length === 0) {
                        this.mostrarAlerta('Por favor, selecione pelo menos uma matéria.');
                        return false;
                    }
                    
                    // Validar seleção de cursos
                    if (this.planejamento.cursos.length === 0) {
                        this.mostrarAlerta('Por favor, selecione pelo menos um curso.');
                        return false;
                    }
                    
                    return true;
                },
                
                mostrarAlerta(mensagem) {
                    // Usar SweetAlert2 para mostrar um modal bonito e centralizado
                    Swal.fire({
                        title: 'Atenção',
                        text: mensagem,
                        icon: 'info',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#00008B',
                        background: document.documentElement.getAttribute('data-theme') === 'dark' ? '#13132b' : '#fff',
                        color: document.documentElement.getAttribute('data-theme') === 'dark' ? '#e4e6f0' : '#2c3e50',
                        customClass: {
                            popup: 'rounded-lg shadow-md',
                            title: 'text-xl font-bold mb-4',
                            confirmButton: 'px-6 py-2 rounded-md transition-all hover:shadow-lg'
                        }
                    });
                },
                async salvarPlanejamento() {
                    // Validar o formulário antes de enviar
                    if (!this.validarFormulario()) {
                        return;
                    }
                    
                    try {
                        // Mostrar loading
                        Swal.fire({
                            title: 'Salvando...',
                            text: 'Aguarde enquanto salvamos seu planejamento',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });
                        
                        // Enviar dados para o servidor
                        const response = await fetch('processar_planejamento.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            },
                            body: JSON.stringify(this.planejamento)
                        });
                        
                        const data = await response.json();
                        
                        // Fechar loading
                        Swal.close();
                        
                        if (data.success) {
                            // Mostrar mensagem de sucesso
                            Swal.fire({
                                title: 'Sucesso!',
                                text: data.message || 'Planejamento criado com sucesso!',
                                icon: 'success',
                                confirmButtonText: 'OK',
                                confirmButtonColor: '#00008B'
                            }).then((result) => {
                                // Redirecionar para a página de cronograma
                                window.location.href = '../index.php';
                            });
                        } else {
                            // Mostrar mensagem de erro
                            Swal.fire({
                                title: 'Erro!',
                                text: data.message || 'Ocorreu um erro ao salvar o planejamento.',
                                icon: 'error',
                                confirmButtonText: 'OK',
                                confirmButtonColor: '#00008B'
                            });
                        }
                    } catch (error) {
                        console.error('Erro:', error);
                        Swal.fire({
                            title: 'Erro!',
                            text: 'Ocorreu um erro ao salvar o planejamento.',
                            icon: 'error',
                            confirmButtonText: 'OK',
                            confirmButtonColor: '#00008B'
                        });
                    }
                },
                toggleEdital(editalId) {
                    const index = this.editaisExpandidos.indexOf(editalId);
                    if (index === -1) {
                        this.editaisExpandidos.push(editalId);
                    } else {
                        this.editaisExpandidos.splice(index, 1);
                    }
                },
                toggleSelecionarTodasMaterias(edital) {
                    const todasSelecionadas = edital.materias.every(m => this.planejamento.materias.includes(m.idmateria));
                    if (todasSelecionadas) {
                        // Desmarcar todas
                        edital.materias.forEach(m => {
                            const idx = this.planejamento.materias.indexOf(m.idmateria);
                            if (idx !== -1) this.planejamento.materias.splice(idx, 1);
                        });
                    } else {
                        // Selecionar todas
                        edital.materias.forEach(m => {
                            if (!this.planejamento.materias.includes(m.idmateria)) {
                                this.planejamento.materias.push(m.idmateria);
                            }
                        });
                    }
                }
            },
            mounted() {
                // Verificar tema salvo
                const temaSalvo = localStorage.getItem('tema');
                if (temaSalvo === 'dark') {
                    this.temaEscuro = true;
                    document.documentElement.setAttribute('data-theme', 'dark');
                }

                // Configurar botão de tema
                const themeToggleBtn = document.getElementById('theme-toggle-btn');
                const themeIcon = document.getElementById('theme-icon');

                themeToggleBtn.addEventListener('click', () => {
                    this.temaEscuro = !this.temaEscuro;
                    if (this.temaEscuro) {
                        document.documentElement.setAttribute('data-theme', 'dark');
                        localStorage.setItem('tema', 'dark');
                        themeIcon.classList.remove('fa-moon');
                        themeIcon.classList.add('fa-sun');
                    } else {
                        document.documentElement.removeAttribute('data-theme');
                        localStorage.setItem('tema', 'light');
                        themeIcon.classList.remove('fa-sun');
                        themeIcon.classList.add('fa-moon');
                    }
                });
            }
        }).mount('#app');
    </script>
</body>