<?php
session_start();
include_once("conexao_POST.php");

// Função para limpar caracteres especiais e evitar injeção de SQL
function limpar_entrada($conexao, $entrada) {
    return pg_escape_string($conexao, htmlspecialchars($entrada));
}

// Verificação de sessão e permissão de usuário
if (isset($_SESSION['idusuario'])) {
    $id_usuario = $_SESSION['idusuario'];
    if ($id_usuario != 1) {
        header("Location: login_index.php");
        exit;
    }
} else {
    $_SESSION['validacao'] = false;
    header("Location: login_index.php");
    exit;
}

// Inserir curso
if (isset($_POST['adicionar_curso'])) {
    $nome_curso = limpar_entrada($conexao, $_POST['nome_curso']);

    $query_inserir_curso = "INSERT INTO appEstudo.curso (nome) VALUES ('$nome_curso')";
    $resultado_inserir = pg_query($conexao, $query_inserir_curso);

    if ($resultado_inserir) {
        header("Location: {$_SERVER['PHP_SELF']}");
        exit;
    } else {
        echo "Erro ao inserir curso.";
    }
}

// Editar curso
if (isset($_POST['editar_curso'])) {
    $id_curso = limpar_entrada($conexao, $_POST['id_curso']);
    $nome_curso = limpar_entrada($conexao, $_POST['nome_curso']);

    $query_editar_curso = "UPDATE appEstudo.curso SET nome = '$nome_curso' WHERE idcurso = $id_curso";
    $resultado_editar = pg_query($conexao, $query_editar_curso);

    if ($resultado_editar) {
        header("Location: {$_SERVER['PHP_SELF']}");
        exit;
    } else {
        echo "Erro ao editar curso.";
    }
}

// Excluir curso
if (isset($_POST['excluir_curso'])) {
    $id_curso = limpar_entrada($conexao, $_POST['id_curso']);

    $query_excluir_curso = "DELETE FROM appEstudo.curso WHERE idcurso = $id_curso";
    $resultado_excluir = pg_query($conexao, $query_excluir_curso);

    if ($resultado_excluir) {
        header("Location: {$_SERVER['PHP_SELF']}");
        exit;
    } else {
        echo "Erro ao excluir curso.";
    }
}

// Consultar cursos
$query_consultar_cursos = "SELECT * FROM appEstudo.curso";
$resultado_cursos = pg_query($conexao, $query_consultar_cursos);

$cursos = array();
while ($row = pg_fetch_assoc($resultado_cursos)) {
    $cursos[] = $row;
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <title>Gerenciador de Cursos</title>
    <!-- Adicionando o CSS do Bootstrap -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        /* Estilo global da página com a fonte Courier Prime, monospace */
        body {
            font-family: "Courier Prime", monospace;
        }
        /* Classe personalizada para alinhar os botões à direita */
        .align-right {
            display: flex;
            align-items: center;
            margin-left: auto;
        }

        /* Espaçamento entre os botões */
        .edit-button {
            margin-right: 8px;
        }
        /* Estilo para o círculo colorido */
        .color-circle {
            display: inline-block;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .btn-burlywood:hover {
            background-color: darkgoldenrod;
            /* Cor mais clara ao passar o mouse */
        }
        /* Classe personalizada para a cor burlywood no botão */
        .btn-burlywood {
            background-color: #DEB887;
            color: white;
        }
        /* Estilo para o botão "Excluir" quando o mouse estiver sobre ele */
        .btn-danger:hover {
            color: black;
        }
    </style>
</head>
<body>
<div class="container">
    <h1 class="my-4">Gerenciador de Cursos</h1>

    <!-- Abas -->
    <ul class="nav nav-tabs">
        <li class="nav-item">
            <a class="nav-link active" data-toggle="tab" href="#ListarCursos">Listar Cursos</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-toggle="tab" href="#AdicionarCurso">Adicionar Curso</a>
        </li>
    </ul>

    <!-- Conteúdo das abas -->
    <div class="tab-content my-4">
        <div id="ListarCursos" class="tab-pane active">
            <h2>Cursos existentes:</h2>
            <ul class="list-group">
                <?php
                $contador = 1;
                foreach ($cursos as $curso) {
                    echo '<li class="list-group-item d-flex justify-content-between align-items-center">' .
                        '<div><span class="badge badge-secondary mr-2">' . $contador . '</span>' . htmlspecialchars($curso['nome']) . '</div>' .
                        '<div class="align-right">' .
                        '<button class="btn btn-burlywood edit-button" onclick="fillEditForm(' . $curso['idcurso'] . ', \'' . htmlspecialchars($curso['nome']) . '\')">Editar</button>' .
                        '<form method="post" style="display: inline-block;" onsubmit="return confirm(\'Tem certeza que deseja excluir o curso?\')">' .
                        '<input type="hidden" name="id_curso" value="' . $curso['idcurso'] . '">' .
                        '<button type="submit" class="btn btn-danger" name="excluir_curso">Excluir</button>' .
                        '</form>' .
                        '</div></li>';
                    $contador++;
                }
                ?>
            </ul>
        </div>

        <div id="AdicionarCurso" class="tab-pane">
            <h2>Adicionar novo curso:</h2>
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                <label for="nome_curso">Nome do curso:</label>
                <input type="text" id="nome_curso" name="nome_curso" required><br>

                <input type="submit" value="Adicionar" name="adicionar_curso" class="btn btn-success">
            </form>
        </div>

        <div id="EditarCurso" class="tab-pane">
            <h2>Editar curso:</h2>
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                <input type="hidden" id="id_curso" name="id_curso" value="">
                <label for="nome_curso_edit">Nome do curso:</label>
                <input type="text" id="nome_curso_edit" name="nome_curso" required><br>

                <input type="submit" value="Salvar" name="editar_curso" class="btn btn-success">
            </form>
            <!-- Botão "Voltar" -->
            <button class="btn btn-secondary mt-3" onclick="openTab('ListarCursos', document.querySelector('.nav-link.active'))">Voltar</button>
        </div>
    </div>
</div>

<!-- Adicionando o JavaScript do Bootstrap -->
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.1/dist/umd/popper.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

<script>
    // Função para alternar as abas
    function openTab(tabName, element) {
        // Esconder todas as abas
        var tabcontent = document.getElementsByClassName("tab-pane");
        for (var i = 0; i < tabcontent.length; i++) {
            tabcontent[i].style.display = "none";
        }
        // Remover a classe "active" de todos os links de aba
        var tablinks = document.getElementsByClassName("nav-link");
        for (var i = 0; i < tablinks.length; i++) {
            tablinks[i].classList.remove("active");
        }
        // Mostrar a aba selecionada
        document.getElementById(tabName).style.display = "block";
        // Adicionar a classe "active" ao link de aba selecionado
        element.classList.add("active");
    }

    // Adicionar o evento de clique às abas
    var tabLinks = document.getElementsByClassName("nav-link");
    for (var i = 0; i < tabLinks.length; i++) {
        tabLinks[i].addEventListener("click", function(event) {
            event.preventDefault();
            var tabName = this.getAttribute("href").substring(1);
            openTab(tabName, this);
        });
    }

    // Função para preencher o formulário de edição quando clicar em "Editar"
    function fillEditForm(id, nome) {
        document.getElementById("id_curso").value = id;
        document.getElementById("nome_curso_edit").value = nome;
        openTab("EditarCurso", document.querySelector(".nav-link[href='#EditarCurso']"));
    }
</script>

</body>
</html>
