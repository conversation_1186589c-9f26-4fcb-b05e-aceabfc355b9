<?php
/**
 * historico.php
 * 
 * Esta página exibe o histórico de estudos do usuário.
 * A autenticação é verificada pela função verificarAutenticacao() de includes/auth.php.
 * As variáveis $id_usuario e $id_planejamento são estabelecidas e validadas
 * por auth.php e processa_index.php antes que a lógica principal desta página seja executada.
 */
session_start();

include_once 'conexao_POST.php'; // Fornece $conexao
include_once 'includes/auth.php';   // Fornece verificarAutenticacao()

// Verifica a autenticação do usuário. Se não for válido, o script é encerrado em verificarAutenticacao().
verificarAutenticacao($conexao);

// Define a constante para permitir a inclusão segura de processa_index.php
define('MEU_SISTEMA_PHP_EXECUCAO_VALIDA', true);
// processa_index.php usa $_SESSION['idusuario'] (já validado) e define $id_planejamento.
// Ele também lida com o caso de planejamento não encontrado, encerrando o script se necessário.
include_once 'processa_index.php';

// Neste ponto, o usuário está autenticado e as variáveis essenciais estão prontas.
// $id_usuario é implicitamente definido por $_SESSION['idusuario'] e validado.
// $id_planejamento é definido e validado por processa_index.php.

// A variável $nome_usuario já deve estar disponível a partir de processa_index.php

// Consultar os métodos de estudo ativos
$query_consultar_metodos = "SELECT nome, descricao FROM appEstudo.metodo_estudo WHERE ativo = true ORDER BY nome";
$resultado_metodos = pg_query($conexao, $query_consultar_metodos);

$metodos = array();
if ($resultado_metodos) {
    while ($row = pg_fetch_assoc($resultado_metodos)) {
        $metodos[] = $row['nome'];
    }
}

// Mantém suas consultas SQL originais
// As variáveis $id_usuario (de $_SESSION['idusuario']) e $id_planejamento (de processa_index.php) já estão disponíveis e validadas.
$id_usuario_sessao = (int)$_SESSION['idusuario']; // Garantir que é um inteiro

$query_consultar_materias_planejamento = "
    SELECT pm.materia_idmateria, m.nome AS nome_materia, m.cor AS cor_materia
    FROM appEstudo.planejamento_materia pm
    INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    WHERE pm.planejamento_idplanejamento = $1";
$resultado_materias_planejamento = pg_query_params($conexao, $query_consultar_materias_planejamento, array($id_planejamento));

$materias_no_planejamento = array();
$cores_materias = array();

if ($resultado_materias_planejamento) {
    while ($row = pg_fetch_assoc($resultado_materias_planejamento)) {
        $materia_id = $row['materia_idmateria'];
        $nome_materia = $row['nome_materia'];
        $cor_materia = $row['cor_materia'];

        $materias_no_planejamento[$materia_id] = $nome_materia;
        $cores_materias[$nome_materia] = $cor_materia;
    }
}

// Consulta do histórico de estudos
$materias_ids = array_keys($materias_no_planejamento);
$pontos_estudo_por_materia = array();
$resultado_consulta_pontos_estudo = null; // Inicializa para evitar erro se não entrar no if

if (empty($materias_ids)) {
    // Não faz nada ou define $resultado_consulta_pontos_estudo como false/vazio se necessário mais abaixo
} else {
    $placeholders = [];
    $params = [$id_usuario_sessao]; // Usa o id_usuario da sessão que foi validado
    foreach ($materias_ids as $i => $id) {
        $placeholders[] = '$' . ($i + 2);
        $params[] = (int)$id;
    }
    $in_clause = implode(',', $placeholders);
    $query_consulta_pontos_estudo = "
        SELECT
            m.nome AS nome_materia,
            e.ponto_estudado,
            e.idestudos,
            e.tempo_liquido,
            to_char(e.data, 'DD-MM-YYYY') AS data_estudo,
            c.nome AS nome_curso,
            e.metodo,
            e.q_total,
            e.q_certa,
            e.q_errada,
            e.descricao, -- Adicionando a coluna descricao que faltava para o modal de edição
            e.link_conteudo -- Adicionando a coluna link_conteudo
        FROM appEstudo.estudos e
        INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
        INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
        LEFT JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria
        LEFT JOIN appEstudo.curso c ON e.idcurso = c.idcurso
        WHERE u.idusuario = $1 
        AND e.materia_idmateria IN ($in_clause)
        ORDER BY m.nome, e.data";
    $resultado_consulta_pontos_estudo = pg_query_params($conexao, $query_consulta_pontos_estudo, $params);
}

if ($resultado_consulta_pontos_estudo) {
    while ($row = pg_fetch_assoc($resultado_consulta_pontos_estudo)) {
        $materia = $row['nome_materia'];
        if (!isset($pontos_estudo_por_materia[$materia])) {
            $pontos_estudo_por_materia[$materia] = array();
        }
        $pontos_estudo_por_materia[$materia][] = $row;
    }
}

function formatarTempo($tempo) {
    if (is_string($tempo) && strpos($tempo, ':') !== false) {
        $partes = explode(':', $tempo);
        if (count($partes) === 3) {
            $segundos = ($partes[0] * 3600) + ($partes[1] * 60) + $partes[2];
        } elseif (count($partes) === 2) { // Formato MM:SS
            $segundos = ($partes[0] * 60) + $partes[1];
        } else {
            return 'Tempo inválido';
        }
    } else {
        $segundos = (int)$tempo;
    }
    
    $horas = floor($segundos / 3600);
    $minutos = floor(($segundos % 3600) / 60);
    
    if ($horas > 0) {
        return sprintf("%dh %02dmin", $horas, $minutos);
    } else {
        return sprintf("%dmin", $minutos);
    }
}
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Histórico de Estudo</title>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&family=Quicksand:wght@400;500;600;700&family=Varela+Round&family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Quicksand:wght@300..700&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Tektur:wght@400..900&family=Varela+Round&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">

    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <style>
:root {
    /* Cores modo claro (já existentes) */
    --primary: #00008B;
    --primary-light: #0000CD;
    --secondary: #f8f0e3;
    --accent: #4a4a4a;
    --border: #e0e0e0;
    --text: #2c3e50;
    --active: #00008B;
    --hover: #f5f5f5;
    --primary-blue: #00008B;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --background: #f5f5f5;
    --card-background: white;
}
[data-theme="dark"] {
    --primary: #4169E1;
    --secondary: #1a1a2e;
    --accent: #6c7293;
    --border: #2d2d42;
    --text: #e4e6f0;
    --active: #4169E1;
    --hover: #232338;
    --primary-blue: #4169E1;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --background: #0a0a1f;
    --card-background: #13132b;
}

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    color: var(--text);
    line-height: 1.6;
    margin: 0;
    min-height: 100vh;
    box-sizing: border-box;
    background-color: var(--border);
}
        .container {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            padding: 1rem;
        }

        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px var(--shadow-color);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            overflow: hidden;
            cursor: pointer;
            border: 1px solid var(--border);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 12px var(--shadow-color);
        }

        .card-header {
            background: var(--primary-blue);
            color: white;
            padding: 1rem;
            font-size: 1.2rem;
            font-weight: 600;
            border-bottom: 2px solid var(--border);
        }

        .card-content {
            padding: 1rem;
        }

        .estudo-item {
            border-bottom: 1px solid var(--border);
            padding: 1rem 0;
        }

        .estudo-item:last-child {
            border-bottom: none;
        }

        .estudo-data {
            font-size: 0.9rem;
            color: var(--accent);
            margin-bottom: 0.5rem;
        }

        .estudo-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 0.5rem;
            margin-top: 0.5rem;
        }

        .info-item {
            background: var(--hover);
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .info-label {
            color: var(--accent);
            font-weight: 600;
        }

        .empty-state {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px var(--shadow-color);
            grid-column: 1 / -1;
        }

        .empty-icon {
            font-size: 3rem;
            color: var(--accent);
            margin-bottom: 1rem;
        }

        .back-button {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            background: var(--primary);
            color: white;
            padding: 1rem;
            border-radius: 50%;
            box-shadow: 0 2px 4px var(--houver);
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .back-button:hover {
            transform: scale(1.1);
        }

        .ver-mais-btn {
            background: transparent;
            border: 1px solid var(--vintage-gold);
            color: var(--vintage-gold);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            font-size: 0.9rem;
            width: 100%;
            margin-top: 1rem;
            transition: all 0.3s ease;
        }

        .ver-mais-btn:hover {
            background: var(--vintage-gold);
            color: white;
        }

        .estudos-hidden {
            display: none;
        }

        .card-content {
            position: relative;
        }

        .estudos-preview {
            position: relative;
        }

        .estudos-completos {
            display: none;
        }

        .estudos-completos.mostrar {
            display: block;
        }

        .resumo-materia {
            padding: 1rem;
        }

        .estatisticas {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin-top: 1rem;
        }

        .stat-item {
            background: var(--hover);
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            font-size: 0.9rem;
            color: var(--accent);
        }

        .ultimo-estudo {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid var(--border);
        }

        .ver-historico-btn {
            display: block;
            width: 100%;
            padding: 0.8rem;
            margin-top: 1rem;
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .ver-historico-btn:hover {
            background: var(--primary-light);
            transform: translateY(-2px);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1000;
        }

        .modal-content {
            position: relative;
            background: white;
            width: 90%;
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            border-radius: 12px;
            max-height: 90vh;
            overflow-y: auto;
        }

        .close-modal {
            position: absolute;
            top: 1rem;
            right: 1rem;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--accent);
        }

        .modal-header {
            border-bottom: 2px solid var(--vintage-gold);
            padding-bottom: 1rem;
            margin-bottom: 1.5rem;
        }

        .modal-header h2 {
            color: var(--primary-blue);
            font-family: 'Varela Round', sans-serif;
        }

        .historico-lista {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .historico-item {
            background: var(--hover);
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid var(--primary);
        }

        .historico-data {
            font-weight: bold;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
        }

        .historico-detalhes {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 0.5rem;
        }

        .detalhe-item {
            background: white;
            padding: 0.5rem;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .detalhe-label {
            color: var(--accent);
            font-size: 0.8rem;
        }

        .detalhe-valor {
            color: var(--primary-blue);
            font-weight: 600;
            margin-top: 0.2rem;
        }

        .form-group {
            margin-bottom: 1rem;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--primary-blue);
            font-weight: 600;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid var(--border);
            border-radius: 4px;
            font-size: 1rem;
        }

        .form-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .btn-primary,
        .btn-secondary {
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
        }

        .btn-primary {
            background: var(--primary-blue);
            color: white;
            border: none;
        }

        .btn-secondary {
            background: var(--border);
            color: var(--text);
            border: 1px solid var(--border);
        }

        .editar-btn {
            background: transparent;
            border: none;
            color: var(--primary-blue);
            cursor: pointer;
            padding: 0.25rem;
            margin-left: 0.5rem;
        }

        .editar-btn:hover {
            color: var(--vintage-gold);
        }

        @media (max-width: 768px) {
            .container {
                grid-template-columns: 1fr;
            }

            .estudo-info {
                grid-template-columns: 1fr;
            }
        }
        .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border);
    margin-bottom: 30px;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 2px 4px var(--shadow-color);
    background: var(--card-background);
}

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo img {
            height: 50px;
            width: auto;
            transition: opacity 0.3s ease;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            background: var(--hover);
        }

        .user-info i {
            color: var(--primary);
            font-size: 1.2rem;
        }

        .user-name {
            color: var(--text);
            font-weight: 600;
        }

        .theme-toggle {
            margin-left: 15px;
        }

        .theme-btn {
            background: transparent;
            border: none;
            color: var(--primary);
            cursor: pointer;
            font-size: 1.2rem;
            padding: 8px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .theme-btn:hover {
            background: var(--hover);
        }

        .logo {
            position: relative;
        }

        .logo-dark {
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
        }

        [data-theme="dark"] .logo-light {
            opacity: 0;
        }

        [data-theme="dark"] .logo-dark {
            opacity: 1;
        }

        .header_centro {
            text-align: center;
            margin-bottom: 1rem;
        }

        .header_centro h1 {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            color: var(--primary);
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .header_centro p {
            color: #666;
            font-size: 1.1rem;
        }
    </style>
</head>
<body>
<div class="header">
            <div class="header-left">
                <div class="logo">
                    <img src="./logo/logo_vertical_azul.png" alt="Logo" class="logo-light">
                    <img src="./logo/logo_vertical.png" alt="Logo" class="logo-dark">
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span class="user-name"><?php echo htmlspecialchars($nome_usuario ?? 'Usuário'); // Adicionado fallback para $nome_usuario ?></span>
                </div>
                <div class="theme-toggle">
                    <button id="theme-toggle-btn" class="theme-btn">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>

        <header class="header_centro">
            <div class="menu-icon">
                <i class="fas fa-history fa-3x" style="color: var(--primary); margin-bottom: 15px;"></i>
            </div>
            <h1>Histórico de Matérias</h1>
            <p>Visível Somente Matérias que Estão no Seu Planejamento</p>
        </header>
    <div class="container">
        
        <?php if (empty($pontos_estudo_por_materia)): ?>
            <div class="empty-state">
                <div class="empty-icon">
                    <i class="fas fa-book"></i>
                </div>
                <h2>Nenhum histórico de estudo encontrado</h2>
                <p>Comece a estudar para ver seu histórico aqui!</p>
            </div>
        <?php else: ?>
            <?php foreach ($pontos_estudo_por_materia as $materia => $estudos): ?>
                <?php
                // Calcular estatísticas
                $total_tempo = 0;
                $total_questoes = 0;
                $total_acertos = 0;
                $ultimo_estudo = end($estudos);
                
                foreach ($estudos as $estudo) {
                    if (!empty($estudo['tempo_liquido'])) {
                        if (strpos($estudo['tempo_liquido'], ':') !== false) {
                            $partes_tempo = explode(':', $estudo['tempo_liquido']);
                            if (count($partes_tempo) === 3) { // HH:MM:SS
                                $total_tempo += ($partes_tempo[0] * 3600) + ($partes_tempo[1] * 60) + $partes_tempo[2];
                            } elseif (count($partes_tempo) === 2) { // MM:SS ou HH:MM (assumindo MM:SS se for o caso)
                                 // Se o formato for HH:MM, precisaria de ajuste ou padronização na entrada.
                                 // Por ora, tratando como MM:SS se apenas dois blocos.
                                $total_tempo += ($partes_tempo[0] * 60) + $partes_tempo[1];
                            }
                        } else {
                            $total_tempo += (int)$estudo['tempo_liquido']; // Assume que é segundos se não tiver ':'
                        }
                    }
                    $total_questoes += (int)($estudo['q_total'] ?? 0);
                    $total_acertos += (int)($estudo['q_certa'] ?? 0);
                }
                ?>
                
                <div class="card" data-materia="<?php echo htmlspecialchars($materia); ?>">
                    <div class="card-header" style="background-color: <?php echo htmlspecialchars($cores_materias[$materia] ?? '#00008B'); // Fallback para cor ?>">
                        <i class="fas fa-book-open"></i> <?php echo htmlspecialchars($materia); ?>
                    </div>
                    <div class="resumo-materia">
                        <div class="estatisticas">
                            <div class="stat-item">
                                <div class="stat-value"><?php echo formatarTempo($total_tempo); ?></div>
                                <div class="stat-label">Tempo Total</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">
                                    <?php echo $total_questoes > 0 ? 
                                        round(($total_acertos / $total_questoes) * 100) . '%' : 
                                        '0%'; ?>
                                </div>
                                <div class="stat-label">Taxa de Acerto</div>
                            </div>
                        </div>
                        
                        <div class="ultimo-estudo">
                            <small>Último estudo:</small>
                            <div><?php echo htmlspecialchars($ultimo_estudo['data_estudo'] ?? 'N/A'); ?></div>
                            <div><?php echo htmlspecialchars($ultimo_estudo['ponto_estudado'] ?? 'N/A'); ?></div>
                        </div>

                        <button class="ver-historico-btn" onclick="verHistoricoCompleto('<?php echo htmlspecialchars(addslashes($materia)); ?>')">
                            Ver Histórico Completo
                        </button>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <div class="back-button" onclick="window.location.href='index.php'">
        <i class="fas fa-home"></i>
    </div>

    <!-- Modal para histórico detalhado -->
    <div id="historicoModal" class="modal">
        <div class="modal-content">
            <span class="close-modal" onclick="fecharModal()">&times;</span>
            <div id="historicoDetalhado"></div>
        </div>
    </div>

    <!-- Modal para edição -->
    <div id="editarHistoricoModal" class="modal">
        <div class="modal-content">
            <span class="close-modal" onclick="fecharModalEdicao()">&times;</span>
            <div class="modal-header">
                <h2>Editar Registro de Estudo</h2>
            </div>
            <form id="formEditarHistorico" onsubmit="salvarEdicaoHistorico(event)">
                <input type="hidden" id="editIdEstudo" name="idestudo">
                <div class="form-group">
                    <label for="editData">Data do Estudo</label>
                    <input type="date" id="editData" name="data" required>
                </div>
                <div class="form-group">
                    <label for="editPonto">Ponto Estudado</label>
                    <input type="text" id="editPonto" name="ponto_estudado" required>
                </div>
                <div class="form-group">
                    <label for="editTempo">Tempo de Estudo (HH:MM)</label>
                    <input type="time" id="editTempo" name="tempo_liquido" required step="60"> <!-- step="60" para minutos -->
                </div>
                <div class="form-group">
                    <label for="editQTotal">Total de Questões</label>
                    <input type="number" id="editQTotal" name="q_total" min="0">
                </div>
                <div class="form-group">
                    <label for="editQCerta">Questões Certas</label>
                    <input type="number" id="editQCerta" name="q_certa" min="0">
                </div>
                <div class="form-group">
                    <label for="editQErrada">Questões Erradas</label>
                    <input type="number" id="editQErrada" name="q_errada" min="0">
                </div>
                <div class="form-group">
                    <label for="editMetodo">Método de Estudo</label>
                    <select id="editMetodo" name="metodo" required>
                        <?php foreach ($metodos as $metodo_option): // Renomeado para evitar conflito ?>
                            <option value="<?php echo htmlspecialchars($metodo_option); ?>"><?php echo htmlspecialchars($metodo_option); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="form-group">
                    <label for="editLinkConteudo">Link do Conteúdo</label>
                    <input type="url" id="editLinkConteudo" name="link_conteudo" placeholder="URL do conteúdo estudado">
                </div>
                 <div class="form-group">
                     <label for="editDescricao">Observações</label>
                     <textarea id="editDescricao" name="descricao" class="form-control" rows="3"
                               placeholder="Observações adicionais do Ponto estudado (opcional)"
                               style="min-height: 100px; min-width: 100%; width: 100%; max-width: 100%; resize: vertical;"></textarea>
                 </div>
                 <div class="form-actions">
                     <button type="submit" class="btn-primary">Salvar Alterações</button>
                     <button type="button" class="btn-secondary" onclick="fecharModalEdicao()">Cancelar</button>
                 </div>
             </form>
         </div>
     </div>
 
     <script>
 function verHistoricoCompleto(materia) {
     const modal = document.getElementById('historicoModal');
     const historicoDetalhado = document.getElementById('historicoDetalhado');
     
     fetch(`get_historico_detalhado.php?materia=${encodeURIComponent(materia)}`)
         .then(response => {
             if (!response.ok) {
                 throw new Error(`HTTP error! status: ${response.status}`);
             }
             return response.json();
         })
         .then(data => {
             let html = `
                 <div class="modal-header">
                     <h2>${materia}</h2>
                 </div>
                 <div class="historico-lista">
             `;
 
             if (data && data.length > 0) {
                 data.forEach(estudo => {
                     html += `
                         <div class="historico-item">
                             <div class="historico-data">
                                 ${estudo.data_estudo || 'N/A'}
                                 <button class="editar-btn" onclick="editarHistorico(${estudo.idestudos})">
                                     <i class="fas fa-edit"></i>
                                 </button>
                             </div>
                             <div class="historico-ponto">
                                 ${estudo.ponto_estudado || 'N/A'}
                             </div>
                             ${estudo.link_conteudo ? `
                             <div class="historico-link" style="margin-top: 0.5rem; font-size: 0.9rem;">
                                 <a href="${estudo.link_conteudo}" target="_blank" style="color: var(--primary-blue); text-decoration: none;">
                                     <i class="fas fa-external-link-alt"></i> Link do Conteúdo
                                 </a>
                             </div>
                             ` : ''}
                             <div class="historico-detalhes">
                                 <div class="detalhe-item">
                                     <div class="detalhe-label">Tempo de Estudo</div>
                                     <div class="detalhe-valor">${estudo.tempo_liquido || '-'}</div>
                                 </div>
                                 <div class="detalhe-item">
                                     <div class="detalhe-label">Questões</div>
                                     <div class="detalhe-valor">${estudo.q_certa || 0}/${estudo.q_total || 0}</div>
                                 </div>
                                 <div class="detalhe-item">
                                     <div class="detalhe-label">Método</div>
                                     <div class="detalhe-valor">${estudo.metodo || '-'}</div>
                                 </div>
                             </div>
                         </div>
                     `;
                 });
             } else {
                 html += '<p>Nenhum histórico detalhado encontrado para esta matéria.</p>';
             }
 
             html += '</div>';
             historicoDetalhado.innerHTML = html;
             modal.style.display = 'block';
         })
         .catch(error => {
             console.error('Erro ao carregar histórico:', error);
             historicoDetalhado.innerHTML = `
                 <div class="error-message" style="color: red; text-align: center; padding: 20px;">
                     <i class="fas fa-exclamation-circle"></i>
                     Erro ao carregar o histórico: ${error.message}. Por favor, tente novamente.
                 </div>
             `;
             modal.style.display = 'block'; // Mostra o modal mesmo com erro para exibir a mensagem
         });
 }
 
     function editarHistorico(idEstudo) {
         fetch(`get_registro_estudo.php?id=${idEstudo}`)
             .then(response => {
                 if (!response.ok) {
                     throw new Error(`HTTP error! status: ${response.status}`);
                 }
                 return response.json();
             })
             .then(estudo => {
                 if (!estudo) {
                     throw new Error('Dados do estudo não encontrados ou inválidos.');
                 }
                 document.getElementById('editIdEstudo').value = estudo.idestudos;
                 // Formatar data para YYYY-MM-DD se vier como DD-MM-YYYY
                 let dataEstudoFormatada = estudo.data_estudo;
                 if (estudo.data_estudo && estudo.data_estudo.includes('-')) {
                     const partesData = estudo.data_estudo.split('-');
                     if (partesData.length === 3 && partesData[0].length === 2) { // DD-MM-YYYY
                         dataEstudoFormatada = `${partesData[2]}-${partesData[1]}-${partesData[0]}`;
                     }
                 }
                 document.getElementById('editData').value = dataEstudoFormatada;
                 document.getElementById('editPonto').value = estudo.ponto_estudado || '';
                 document.getElementById('editDescricao').value = estudo.descricao || '';
                 document.getElementById('editLinkConteudo').value = estudo.link_conteudo || ''; // Preencher o novo campo
                 
                 let tempoFormatado = '00:00';
                 if (estudo.tempo_liquido && estudo.tempo_liquido.includes(':')) {
                     const [horas, minutos] = estudo.tempo_liquido.split(':');
                     tempoFormatado = `${horas.padStart(2, '0')}:${minutos.padStart(2, '0')}`;
                 } else if (estudo.tempo_liquido) { // Assume que é minutos se for só número, ou precisa de mais lógica
                      // Para simplificar, se for apenas um número, pode ser necessário convertê-lo para HH:MM
                      // Aqui, vamos apenas deixar como está se não for HH:MM, o input type="time" pode não gostar.
                      // O ideal é que `tempo_liquido` sempre venha no formato esperado ou seja tratado.
                      // Se for segundos, por exemplo: const mins = Math.floor(estudo.tempo_liquido / 60); const secs = estudo.tempo_liquido % 60;
                      // tempoFormatado = `${String(Math.floor(mins/60)).padStart(2,'0')}:${String(mins%60).padStart(2,'0')}`;
                 }
                 document.getElementById('editTempo').value = tempoFormatado;
                 
                 document.getElementById('editQTotal').value = estudo.q_total || 0;
                 document.getElementById('editQCerta').value = estudo.q_certa || 0;
                 document.getElementById('editQErrada').value = estudo.q_errada || 0;
                 document.getElementById('editMetodo').value = estudo.metodo || '';
                 
                 document.getElementById('editarHistoricoModal').style.display = 'block';
                 document.getElementById('historicoModal').style.display = 'none'; // Esconde o modal de visualização
             })
             .catch(error => {
                 console.error('Erro ao carregar dados para edição:', error);
                 alert('Erro ao carregar dados para edição: ' + error.message + '. Por favor, tente novamente.');
             });
     }
 
     function salvarEdicaoHistorico(event) {
         event.preventDefault();
         
         const formData = new FormData(document.getElementById('formEditarHistorico'));
         const materiaAtual = document.querySelector('#historicoModal .modal-header h2').textContent; // Pega a matéria do modal de visualização
 
         fetch('atualizar_registro_estudo.php', {
             method: 'POST',
             body: formData
         })
         .then(async response => {
             const text = await response.text();
             try {
                 return JSON.parse(text);
             } catch (e) {
                 console.error('Resposta do servidor (não JSON):', text);
                 throw new Error('Resposta inválida do servidor. Verifique o console para detalhes.');
             }
         })
         .then(data => {
             if (data.success) {
                 // Dispara evento para recarregar index.php em qualquer aba aberta
                 localStorage.setItem('recarregarIndex', Date.now().toString());
                 
                 Swal.fire({
                     title: 'Sucesso!',
                     text: 'Registro atualizado com sucesso!',
                     icon: 'success',
                     confirmButtonColor: 'var(--primary-blue)',
                     confirmButtonText: 'OK'
                 }).then(() => {
                     fecharModalEdicao();
                     // Recarregar a visualização do histórico para a matéria correta
                     if (materiaAtual) {
                        verHistoricoCompleto(materiaAtual); // Usa a matéria que estava sendo visualizada
                     } else if (data.materia) {
                        verHistoricoCompleto(data.materia); // Fallback para a matéria retornada, se houver
                     }
                 });
             } else {
                 Swal.fire({
                     title: 'Erro!',
                     text: 'Erro ao atualizar registro: ' + (data.message || 'Erro desconhecido.'),
                     icon: 'error',
                     confirmButtonColor: 'var(--primary-blue)',
                     confirmButtonText: 'OK'
                 });
             }
         })
         .catch(error => {
             console.error('Erro completo ao salvar:', error);
             Swal.fire({
                 title: 'Erro de Conexão!',
                 text: 'Erro ao salvar alterações: ' + error.message + '. Por favor, tente novamente.',
                 icon: 'error',
                 confirmButtonColor: 'var(--primary-blue)',
                 confirmButtonText: 'OK'
             });
         });
     }
 
     function fecharModalEdicao() {
         document.getElementById('editarHistoricoModal').style.display = 'none';
         // Reabrir o modal de visualização se ele estava aberto (ou decidir o fluxo)
         // Se a matéria atual é conhecida, podemos reabrir o modal de histórico:
         const materiaAtual = document.querySelector('#historicoModal .modal-header h2');
         if (materiaAtual && materiaAtual.textContent) {
              document.getElementById('historicoModal').style.display = 'block';
         } // Senão, apenas fecha o modal de edição.
     }
 
     function fecharModal() {
         document.getElementById('historicoModal').style.display = 'none';
     }
 
     // Fechar modais com a tecla Escape
     document.addEventListener('keydown', function (event) {
         if (event.key === "Escape") {
             if (document.getElementById('editarHistoricoModal').style.display === 'block') {
                 fecharModalEdicao();
             } else if (document.getElementById('historicoModal').style.display === 'block') {
                 fecharModal();
             }
         }
     });
 
     // Fechar modal ao clicar fora (para o modal de visualização)
     window.onclick = function(event) {
         const historicoModal = document.getElementById('historicoModal');
         if (event.target == historicoModal) {
             historicoModal.style.display = 'none';
         }
         // Não fechar o modal de edição ao clicar fora, pois pode perder dados não salvos.
         // Se desejar esse comportamento para o modal de edição, adicione:
         // const editarModal = document.getElementById('editarHistoricoModal');
         // if (event.target == editarModal) {
         //     fecharModalEdicao();
         // }
     }
 
     // Theme switcher logic (exemplo)
     const themeToggleBtn = document.getElementById('theme-toggle-btn');
     if (themeToggleBtn) {
         themeToggleBtn.addEventListener('click', () => {
             const currentTheme = document.documentElement.getAttribute('data-theme');
             if (currentTheme === 'dark') {
                 document.documentElement.setAttribute('data-theme', 'light');
                 localStorage.setItem('theme', 'light');
                 themeToggleBtn.innerHTML = '<i class="fas fa-moon"></i>'; // Ícone para mudar para escuro
             } else {
                 document.documentElement.setAttribute('data-theme', 'dark');
                 localStorage.setItem('theme', 'dark');
                 themeToggleBtn.innerHTML = '<i class="fas fa-sun"></i>'; // Ícone para mudar para claro
             }
         });
 
         // Apply saved theme on load
         const savedTheme = localStorage.getItem('theme') || 'light';
         document.documentElement.setAttribute('data-theme', savedTheme);
         if (savedTheme === 'dark') {
             themeToggleBtn.innerHTML = '<i class="fas fa-sun"></i>';
         } else {
             themeToggleBtn.innerHTML = '<i class="fas fa-moon"></i>';
        }
    }

    </script>
</body>
</html>
