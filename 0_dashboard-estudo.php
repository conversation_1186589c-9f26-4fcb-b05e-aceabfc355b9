<?php
// Topo de dashboard-estudo.php

if (!defined('MEU_SISTEMA_PHP_DASHBOARD_VALIDO')) {
    // Se a constante não estiver definida, o arquivo foi acessado diretamente.
    // Mostra a mensagem SweetAlert e redireciona.

    echo '<!DOCTYPE html>
    <html>
    <head>
        <title>Acesso Negado - Redirecionando...</title>
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <style>
            body { margin: 0; padding: 0; font-family: "Quicksand", sans-serif; display: flex; align-items: center; justify-content: center; min-height: 100vh; background-color: #f0f2f5; }
            .swal2-popup { font-family: "Quicksand", sans-serif !important; }
        </style>
    </head>
    <body>
        <script>
            document.addEventListener("DOMContentLoaded", function() {
                Swal.fire({
                    title: "Acesso Direto Negado",
                    text: "Esta página não pode ser acessada diretamente. Você será redirecionado.",
                    icon: "error",
                    confirmButtonText: "OK",
                    confirmButtonColor: "#00008B",
                    allowOutsideClick: false
                }).then((result) => {
                    window.location.href = "index.php"; // Redireciona para a página principal
                });
            });
        </script>
    </body>
    </html>';
    exit; // Para a execução do script.
}

// O restante do código original de dashboard-inicio.php continua aqui...
// Ex: echo "<h1>Conteúdo do Dashboard de Início</h1>";
// Suas consultas e lógica específica para este dashboard...
?>

<?php
//dashboard-estudo.php
include_once 'consulta_banco_ultima_proxima.php';
?>




<style>
    /* Modal Base */
    .modal-estudo {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 9999;
        visibility: hidden;
        opacity: 0;
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }

    .modal-estudo.ativo {
        visibility: visible;
        opacity: 1;
    }

    .modal-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(3px);
    }

    .modal-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.8);
        background: var(--hover);
        width: 90%;
        max-width: 800px;
        max-height: 90vh;
        border-radius: 12px;
        padding: 2rem;
        overflow-y: auto;
        transition: transform 0.3s ease;
    }

    .modal-estudo.ativo .modal-content {
        transform: translate(-50%, -50%) scale(1);
    }

    .modal-close {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: var(--dark-bg);
        border: none;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .modal-close:hover {
        transform: rotate(90deg);
        background: #aa0000;
    }

    .modal-titulo {
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
        padding: 0.8rem 0;
        text-align: center;
        color: var(--dark-bg);
        border-bottom: 2px solid var(--vintage-gold);
        background: rgba(0, 0, 0, 0.05);
        border-radius: 4px;
    }

    /* Esconder os cards originais */
    .listar_ultimo, .listar_proximo {
        display: none;
    }

    /* Ajustes para o conteúdo dentro do modal */
    .modal-content .ultimo-estudo,
    .modal-content .proxima-materia {
        box-shadow: none;
        margin: 0;
    }

    /* Scrollbar personalizada */
    .modal-content::-webkit-scrollbar {
        width: 8px;
    }

    .modal-content::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }

    .modal-content::-webkit-scrollbar-thumb {
        background: var(--vintage-gold);
        border-radius: 4px;
    }

    /* Responsividade */
    @media (max-width: 768px) {
        .modal-content {
            width: 95%;
            padding: 1rem;
        }
    }
    .dashboard-grid-duasColunas-estudo {
        display: grid;
        grid-template-columns: 6fr 2fr;
        gap: 1.5rem;
        max-width: 1400px;
        margin: 0 auto;
        padding: 1rem;
    }

    /* Barra de rolagem dos eventos do calendário */
    .fc-popover-body {
        max-height: 300px; /* ajuste conforme necessário */
        overflow-y: auto;
    }

    .btn-ciclo-navegacao {
        padding: 16px 24px;
        border-radius: 16px;
        border: 2px solid var(--primary-blue);
        font-weight: bold;
        background: #fff;
        color: var(--primary-blue);
        box-shadow: 0 2px 8px rgba(0,0,0,0.04);
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        transition: background 0.2s, color 0.2s, box-shadow 0.2s, transform 0.15s;
        outline: none;
    }
    .btn-ciclo-navegacao:hover, .btn-ciclo-navegacao:focus {
        background: #f0f4ff;
        color: var(--primary-blue);
        border: 2px solid var(--primary-blue);
        box-shadow: 0 4px 16px rgba(0,0,139,0.10);
        transform: translateY(-2px) scale(1.04);
    }
    .btn-ciclo-navegacao:active {
        background: #dde6ff;
        color: var(--primary-blue);
        transform: scale(0.98);
    }
    .btn-ciclo-navegacao.ultima {
        color: var(--danger-color);
        border-color: var(--danger-color);
    }
    .btn-ciclo-navegacao.ultima:hover, .btn-ciclo-navegacao.ultima:focus {
        background: #fff0f0;
        color: var(--danger-color);
        border: 2px solid var(--danger-color);
        box-shadow: 0 4px 16px rgba(231,111,81,0.10);
    }
    .btn-ciclo-navegacao.ultima:active {
        background: #ffe6e6;
        color: var(--danger-color);
    }
    .resumo-table {
        width: 100%;
        border-collapse: collapse;
        margin: 10px 0;
        font-size: 14px;
        border-radius: 8px;
                
    }

    .resumo-table td {
        padding: 5px 10px;
        white-space: nowrap;
     }

    .resumo-table i {
        margin-right: 5px;
    }

    .valor {
        font-weight: bold;
    }
</style>


<div class="listar_ultimo">
    <?php include '0listar_ultimo_estudo_caixa.php'; ?>
</div>

<div class="listar_proximo">
    <?php include '0listar_proximo_estudo.php'; ?>
</div>


<div class="dashboard-grid-umaColuna">

        <div class="resumo">
        <div class="cards-stack">
        <div class="card-planejamento">
        <div class="card-header">
        <h3>Resumo do Seu Estudo</h3>
        </div>
            <?php
            $corTexto = ($mediaTempoEstudo < (2 * 3600)) ? 'red' : 'green';
            $corTexto2 = ($ultimoEstudo < 2) ? 'green' : (($ultimoEstudo == 2) ? 'orange' : 'red');
            ?>

            <table class="resumo-table">
                    <tr>
                        <td><i class="fas fa-calendar-alt" style="color: <?php echo $corTextoInicio; ?>"></i> Início:</td>
                        <td class="valor" style="color: <?php echo $corTextoInicio; ?>"><?php echo $primeiraData; ?></td>

                        <td><i class="fas fa-clock" style="color: green"></i> T.Líquido:</td>
                        <td class="valor" style="color: green"><?php echo sprintf("%02d", $horasFormatadasLiquido); ?>h<?php echo sprintf("%02d", $minutosFormatadosLiquido); ?>min</td>

                        <td><i class="fas fa-chart-line" style="color: <?php echo $corTexto; ?>"></i> Média:</td>
                        <td class="valor" style="color: <?php echo $corTexto; ?>"><?php echo sprintf("%02d", $mediaFormatadahoras); ?>h<?php echo sprintf("%02d", $mediaFormatadominutos); ?>min</td>

                        <td><i class="fas fa-calendar-check" style="color: green"></i> Estudados:</td>
                        <td class="valor" style="color: green"><?php echo sprintf("%02d", $diasEstudados); ?> dias</td>
                    </tr>
                    <tr>
                        <td><i class="fas fa-calendar-times" style="color: red"></i> Perdidos:</td>
                        <td class="valor" style="color: red"><?php echo sprintf("%02d", $diasNaoEstudados); ?> dias</td>

                        <td><i class="fas fa-calendar" style="color: #2196F3"></i> Passados:</td>
                        <td class="valor" style="color: #2196F3"><?php echo sprintf("%02d", $numDias); ?> dias</td>

                        <td><i class="fas fa-fire" style="color: <?php echo $corTexto2; ?>"></i> Sem estudar:</td>
                        <td class="valor" style="color: <?php echo $corTexto2; ?>"><?php echo sprintf("%02d", $ultimoEstudo); ?> dias</td>

                        <td colspan="2"></td>
                    </tr>
            </table>
        </div>
        </div>
        

    </div>

</div>

<div class="dashboard-grid-duasColunas-estudo">

    <div class="cards-stack">

            <div class="card-planejamento">
                <div class="card-header" style="margin-bottom: 15px;">
            <h3>Calendário de Estudos</h3>
                </div>
            <div id="calendario_estudo"></div>
        </div>
    </div>


    <div class="cards-stack">
        <!-- Gráfico de Estudos -->
        <?php include 'ciclo_publico.php'; ?>
    </div>

<script>
        // Criar estrutura do modal
        const modalHTML = `
        <div id="modal-estudo" class="modal-estudo">
            <div class="modal-overlay"></div>
            <div class="modal-content">
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
                <div class="modal-body"></div>
            </div>
        </div>
    `;

        // Adicionar modal ao body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Elementos do modal
        const modal = document.getElementById('modal-estudo');
        const modalOverlay = modal.querySelector('.modal-overlay');
        const modalBody = modal.querySelector('.modal-body');
        const modalClose = modal.querySelector('.modal-close');

        // Função para abrir modal
        function abrirModal(conteudo, titulo) {
            modalBody.innerHTML = `
            <h3 class="modal-titulo">${titulo}</h3>
            <div class="modal-conteudo">${conteudo}</div>
        `;
            modal.classList.add('ativo');
            document.body.style.overflow = 'hidden';
        }

        // Função para fechar modal
        function fecharModal() {
            modal.classList.remove('ativo');
            document.body.style.overflow = '';
        }

        // Eventos para fechar modal
        modalClose.addEventListener('click', fecharModal);
        modalOverlay.addEventListener('click', fecharModal);
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && modal.classList.contains('ativo')) {
                fecharModal();
            }
        });

</script>

