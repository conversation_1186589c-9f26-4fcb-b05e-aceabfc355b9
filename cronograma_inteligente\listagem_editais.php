<?php
session_start();
if (!isset($_SESSION['idusuario']) || $_SESSION['idusuario'] != 1) {
    header("Location: /login_index.php");
    exit();
}
include_once("assets/config.php");

$query = "SELECT id_edital, nome FROM appestudo.edital ORDER BY id_edital";
$result = pg_query($conexao, $query);
if (!$result) {
    die("Erro ao buscar os editais.");
}
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editais Disponíveis</title>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Old+Standard+TT:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <style>
        :root {
            --primary-color: #B85C5C; /* Vermelho claro queimado */
            --secondary-color: #D2691E;
            --background-color: #F5E6D3;
            --paper-color: #EDE3D0; /* Papel velho um pouco mais claro o*/
            --text-color: #2C1810;
            --border-color: #8B4513;
            --accent-gold: #DAA520;
            --shadow-color: rgba(139, 69, 19, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Old Standard TT', serif;
            min-height: 100vh;
            background: var(--background-color);
            background-image:
                    linear-gradient(rgba(245, 230, 211, 0.9), rgba(245, 230, 211, 0.9)),
                    url("data:image/svg+xml,%3Csvg width='52' height='26' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%238b4513' fill-opacity='0.1'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/svg%3E");
            padding: 40px 20px;
            color: var(--text-color);
        }

        .btn-voltar {
            position: fixed;
            top: 20px;
            left: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: var(--paper-color);
            border: 2px solid var(--border-color);
            border-radius: 50%;
            box-shadow: 4px 4px 0 var(--border-color);
            color: var(--primary-color);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .btn-voltar::before {
            content: '';
            position: absolute;
            width: calc(100% + 4px);
            height: calc(100% + 4px);
            border: 1px solid var(--border-color);
            border-radius: 50%;
            opacity: 0.5;
            transform: scale(0.9);
            transition: all 0.3s ease;
        }

        .btn-voltar:hover {
            transform: translateY(-2px);
            box-shadow: 4px 4px 0 var(--border-color);
            background-color: var(--secondary-color);
            color: white;
        }

        .btn-voltar:hover::before {
            transform: scale(1.1);
            opacity: 0;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header-vintage {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .header-vintage h1 {
            font-family: 'Playfair Display', serif;
            font-size: 3rem;
            color: var(--primary-color);
            text-transform: uppercase;
            letter-spacing: 3px;
            margin-bottom: 20px;
            position: relative;
            display: inline-block;
        }

        .header-vintage h1::after {
            content: "";
            display: block;
            width: 150px;
            height: 3px;
            background: var(--primary-color);
            margin: 15px auto;
        }

        .editais-container {
            background: var(--paper-color);
            border-radius: 10px;
            padding: 40px;
            box-shadow: 8px 8px 0 var(--border-color);
            border: 2px solid var(--border-color);
            position: relative;
        }

        .editais-container::before {
            content: '';
            position: absolute;
            top: 10px;
            left: 10px;
            right: 10px;
            bottom: 10px;
            border: 1px solid var(--border-color);
            opacity: 0.3;
            pointer-events: none;
        }

        .editais-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            position: relative;
        }

        .edital-card {
            background: white;
            padding: 30px;
            border: 2px solid var(--border-color);
            box-shadow: 4px 4px 0 var(--border-color);
            text-align: center;
            transition: all 0.3s ease;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            min-height: 250px;
        }

        .edital-card:hover {
            transform: translate(-2px, -2px);
            box-shadow: 6px 6px 0 var(--border-color);
        }

        .edital-icon {
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-color);
            border-radius: 50%;
            margin: 0 auto 20px;
            color: var(--paper-color);
            font-size: 2rem;
            transition: all 0.3s ease;
            border: 2px solid var(--border-color);
        }

        .edital-card:hover .edital-icon {
            transform: rotate(5deg) scale(1.1);
            background: var(--secondary-color);
        }

        .edital-card h3 {
            font-family: 'Playfair Display', serif;
            font-size: 1.4rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--border-color);
            position: relative;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 12px 25px;
            background: var(--paper-color);
            color: var(--primary-color);
            border: 2px solid var(--border-color);
            box-shadow: 3px 3px 0 var(--border-color);
            text-decoration: none;
            font-family: 'Playfair Display', serif;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            position: relative;
        }

        .btn:hover {
            transform: translate(-2px, -2px);
            box-shadow: 5px 5px 0 var(--border-color);
        }

        .btn i {
            transition: transform 0.3s ease;
        }

        .btn:hover i {
            transform: translateX(5px);
        }

        /* Ornamentos */
        .ornament {
            position: absolute;
            width: 30px;
            height: 30px;
            border: 2px solid var(--border-color);
            opacity: 0.5;
        }

        .ornament-tl { top: -15px; left: -15px; border-right: none; border-bottom: none; }
        .ornament-tr { top: -15px; right: -15px; border-left: none; border-bottom: none; }
        .ornament-bl { bottom: -15px; left: -15px; border-right: none; border-top: none; }
        .ornament-br { bottom: -15px; right: -15px; border-left: none; border-top: none; }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .edital-card {
            animation: fadeInUp 0.6s ease-out forwards;
            opacity: 0;
        }

        <?php
        $total_editais = pg_num_rows($result);
        for($i = 1; $i <= $total_editais; $i++) {
            echo ".edital-card:nth-child($i) { animation-delay: " . ($i * 0.1) . "s; }\n";
        }
        ?>

        @media (max-width: 768px) {
            body {
                padding: 20px 10px;
            }

            .header-vintage h1 {
                font-size: 2rem;
            }

            .editais-container {
                padding: 20px;
            }

            .editais-grid {
                gap: 20px;
            }

            .edital-card {
                padding: 20px;
                min-height: 220px;
            }

            .btn-voltar {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
<a href="index.php" class="btn-voltar">
    <i class="fas fa-arrow-left"></i>
</a>

<div class="container">
    <div class="header-vintage">
        <h1>Editais Disponíveis</h1>
    </div>

    <div class="editais-container">
        <div class="ornament ornament-tl"></div>
        <div class="ornament ornament-tr"></div>
        <div class="ornament ornament-bl"></div>
        <div class="ornament ornament-br"></div>

        <div class="editais-grid">
            <?php while ($edital = pg_fetch_assoc($result)): ?>
                <div class="edital-card">
                    <div>
                        <div class="edital-icon">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <h3><?= htmlspecialchars($edital['nome']) ?></h3>
                    </div>
                    <a href="exibir_materias.php?edital_id=<?= htmlspecialchars($edital['id_edital']) ?>" class="btn">
                        Ver Matérias
                        <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            <?php endwhile; ?>
        </div>
    </div>
</div>
</body>
</html>