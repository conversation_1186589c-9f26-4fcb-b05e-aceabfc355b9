<?php
/**
 * Endpoint para Functions
 * Sistema de Fidelidade da Barbearia
 */

function handleFunction($method, $action, $input, $db, $database) {
    if ($method !== 'POST') {
        ApiResponse::methodNotAllowed();
    }

    try {
        // Garantir que $input seja um array e extrair apenas os valores
        $params = is_array($input) ? array_values($input) : [];
        $result = $database->executeFunction($action, $params);

        // Para get_senha_mestra, retornar no formato esperado pelo Flutter
        if ($action === 'get_senha_mestra') {
            ApiResponse::success(['senha_mestra' => $result], 'Senha mestra obtida com sucesso');
        } else {
            ApiResponse::success(['result' => $result], 'Function executada com sucesso');
        }

    } catch (Exception $e) {
        error_log("Function error: " . $e->getMessage());
        ApiResponse::error('Erro ao executar function: ' . $e->getMessage());
    }
}
?>
