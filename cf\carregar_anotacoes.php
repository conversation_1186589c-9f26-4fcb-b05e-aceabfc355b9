<?php
session_start();
header('Content-Type: application/json');
// Headers de segurança HTTP
header('X-Frame-Options: DENY');
header('X-Content-Type-Options: nosniff');
header('Referrer-Policy: no-referrer');
header('Permissions-Policy: interest-cohort=()');
header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');

// Inclui o arquivo de configuração do banco de dados
require_once __DIR__ . '/config/database.php';

try {
    if (!isset($_GET['pagina_url'])) {
        throw new Exception('URL da página não fornecida');
    }
    // Sanitização da entrada
    $pagina_url = filter_input(INPUT_GET, 'pagina_url', FILTER_SANITIZE_URL);
    if (empty($pagina_url)) {
        throw new Exception('URL da página inválida');
    }

    $pdo = getDbConnection();

    // Verifica se o usuário está logado
    if (!isset($_SESSION['idusuario'])) {
        http_response_code(401);
        echo json_encode(['erro' => 'Usuário não autenticado', 'redirect' => '../login_index.php']);
        exit;
    }

    // Usa o ID do usuário da sessão
    $usuario_id = $_SESSION['idusuario'];
    error_log("Usando ID do usuário da sessão: {$usuario_id}");

    // Verifica se a tabela existe
    try {
        $check_table = $pdo->query("SELECT to_regclass('appestudo.anotacoes')");
        $table_exists = $check_table->fetchColumn();
    } catch (Exception $e) {
        error_log("Erro ao verificar tabela: " . $e->getMessage());
        $table_exists = false;
    }

    error_log("Verificando tabela: " . ($table_exists ? "Tabela existe" : "Tabela não existe"));

    if (!$table_exists) {
        // Cria a tabela se não existir
        error_log("Criando tabela anotacoes");
        try {
            // Primeiro, verifica se o esquema existe
            $pdo->exec("CREATE SCHEMA IF NOT EXISTS appestudo");
            error_log("Esquema appestudo criado ou já existente");

            // Depois cria a tabela
            $pdo->exec("CREATE TABLE IF NOT EXISTS appestudo.anotacoes (
                id SERIAL PRIMARY KEY,
                usuario_id INTEGER NOT NULL,
                pagina_url TEXT NOT NULL,
                elemento_id TEXT NOT NULL,
                texto_anotacao TEXT NOT NULL,
                conteudo_anotado TEXT NOT NULL,
                posicao_inicio INTEGER,
                posicao_fim INTEGER,
                data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )");
            error_log("Tabela anotacoes criada com sucesso");

            // Se a tabela acabou de ser criada, retorna um array vazio
            echo json_encode([
                'sucesso' => true,
                'dados' => []
            ]);
            exit;
        } catch (Exception $e) {
            error_log("Erro ao criar tabela: " . $e->getMessage());
            throw new Exception('Erro ao criar tabela: ' . $e->getMessage());
        }
    }

    $stmt = $pdo->prepare("
        SELECT
            id,
            elemento_id,
            texto_anotacao,
            conteudo_anotado,
            posicao_inicio,
            posicao_fim
        FROM appestudo.anotacoes
        WHERE pagina_url = :pagina_url
        AND usuario_id = :usuario_id
        ORDER BY posicao_inicio ASC
    ");

    $stmt->execute([
        ':pagina_url' => $_GET['pagina_url'],
        ':usuario_id' => $usuario_id
    ]);

    $anotacoes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Garantir que as posições sejam números inteiros
    foreach ($anotacoes as &$anotacao) {
        // Converter para inteiros ou null se não existirem
        $anotacao['posicao_inicio'] = $anotacao['posicao_inicio'] !== null ? intval($anotacao['posicao_inicio']) : null;
        $anotacao['posicao_fim'] = $anotacao['posicao_fim'] !== null ? intval($anotacao['posicao_fim']) : null;

        // Log para depuração
        error_log("Anotação ID {$anotacao['id']} - Elemento: {$anotacao['elemento_id']} - Posições: {$anotacao['posicao_inicio']} a {$anotacao['posicao_fim']}");
    }
    unset($anotacao); // Remover a referência

    // Log do total de anotações encontradas
    error_log("Total de anotações encontradas: " . count($anotacoes));

    echo json_encode([
        'sucesso' => true,
        'dados' => $anotacoes
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'sucesso' => false,
        'erro' => 'Erro ao carregar anotações: ' . $e->getMessage()
    ]);
}




