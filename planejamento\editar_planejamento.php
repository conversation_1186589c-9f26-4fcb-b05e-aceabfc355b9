<?php
include_once("../assets/config.php");
include_once("../includes/auth.php");

// Verifica autenticação e obtém ID do usuário
$id_usuario = verificarAutenticacao($conexao);

// Verifica conexão com banco
if (!$conexao) {
    die("Erro na conexão: " . pg_last_error());
}

// Consulta para obter o ID do planejamento
if (isset($_SESSION['idusuario'])) {
    $id_usuario = $_SESSION['idusuario'];
    $query_consultar_idplanejamento = "SELECT p.idplanejamento
                    FROM appEstudo.planejamento p 
                    WHERE p.usuario_idusuario = $1";
    $resultado_idplanejamento = pg_query_params($conexao, $query_consultar_idplanejamento, array($id_usuario));

    if ($resultado_idplanejamento) {
        $row = pg_fetch_assoc($resultado_idplanejamento);
        $id_planejamento = $row['idplanejamento'];

        // Consulta para obter os dados do planejamento
        $query_consultar_planejamento = "SELECT p.*, u.nome as nome_usuario 
                        FROM appEstudo.planejamento p 
                        INNER JOIN appEstudo.usuario u ON p.usuario_idusuario = u.idusuario
                        WHERE p.idplanejamento = $id_planejamento";
        $resultado_planejamento = pg_query($conexao, $query_consultar_planejamento);
        $planejamento = pg_fetch_assoc($resultado_planejamento);

        // Consulta para obter todas as matérias
        $query_consultar_materias = "SELECT * FROM appEstudo.materia ORDER BY nome ASC";
        $resultado_materias = pg_query($conexao, $query_consultar_materias);
        $todas_materias = pg_fetch_all($resultado_materias);

        // Consulta para obter as matérias selecionadas
        $query_materias_selecionadas = "SELECT materia_idmateria 
                                       FROM appEstudo.planejamento_materia 
                                       WHERE planejamento_idplanejamento = $id_planejamento";
        $resultado_materias_selecionadas = pg_query($conexao, $query_materias_selecionadas);
        $materias_selecionadas = [];
        while ($row = pg_fetch_assoc($resultado_materias_selecionadas)) {
            $materias_selecionadas[] = intval($row['materia_idmateria']);
        }
        // Consulta para obter todos os cursos
        $query_cursos = "SELECT * FROM appEstudo.curso ORDER BY nome ASC";
        $resultado_cursos = pg_query($conexao, $query_cursos);
        $todos_cursos = pg_fetch_all($resultado_cursos);
        // Cursos já selecionados pelo usuário
        $query_selecionados = "SELECT curso_idcurso FROM appEstudo.usuario_has_curso WHERE usuario_idusuario = $id_usuario";
        $resultado_selecionados = pg_query($conexao, $query_selecionados);
        $cursos_selecionados = array();
        while ($row = pg_fetch_assoc($resultado_selecionados)) {
            $cursos_selecionados[] = $row['curso_idcurso'];
        }
    }
    $nome_usuario = isset($planejamento['nome_usuario']) ? $planejamento['nome_usuario'] : 'Usuário';
}

// Após buscar todas as matérias e cursos, buscar também os editais e suas matérias
$query_editais = "
    SELECT DISTINCT e.*, 
        (SELECT string_agg(materia_info, '|')
        FROM (
            SELECT DISTINCT ON (m.nome) m.idmateria || ',' || m.nome || ',' || m.cor as materia_info, m.nome
            FROM appestudo.materia m 
            JOIN appestudo.conteudo_edital ce ON m.idmateria = ce.materia_id 
            WHERE ce.edital_id = e.id_edital
            ORDER BY m.nome, m.idmateria
        ) subq) as materias
    FROM appestudo.edital e
    LEFT JOIN appestudo.conteudo_edital ce ON e.id_edital = ce.edital_id
    WHERE ce.materia_id IS NOT NULL
    ORDER BY e.nome";
$result_editais = pg_query($conexao, $query_editais);
$editais = [];
while ($edital = pg_fetch_assoc($result_editais)) {
    $materias_array = [];
    if (!empty($edital['materias'])) {
        $materias = explode('|', $edital['materias']);
        $materias_unicas = array_unique($materias); // Remove duplicatas
        foreach ($materias_unicas as $materia) {
            list($id, $nome, $cor) = explode(',', $materia);
            $materias_array[] = [
                'idmateria' => $id,
                'nome' => $nome,
                'cor' => $cor
            ];
        }
    }
    $edital['materias'] = $materias_array;
    $editais[] = $edital;
}
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <title>Planejamento de Estudos</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <style>
:root {
    --primary: #00008B;
    --primary-light: #3949ab;
    --secondary: #f8f0e3;
    --accent: #4a4a4a;
    --border: #e0e0e0;
    --text: #2c3e50;
    --active: #00008B;
    --hover: #f5f5f5;
    --primary-blue: #00008B;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --background: #f5f5f5;
    --card-background: white;
}
[data-theme="dark"] {
    --primary: #4169E1;
    --secondary: #1a1a2e;
    --accent: #6c7293;
    --border: #2d2d42;
    --text: #e4e6f0;
    --active: #4169E1;
    --hover: #232338;
    --primary-blue: #4169E1;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --background: #0a0a1f;
    --card-background: #13132b;
}

body {
    background: var(--background);
    color: var(--text);
    font-family: 'Varela Round', 'Quicksand', 'Open Sans', Arial, sans-serif;
    margin: 0;
    padding: 0;
    line-height: 1.6;
}
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2.5rem 1.5rem;
}
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border);
    margin-bottom: 30px;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 2px 4px var(--shadow-color);
    background: linear-gradient(90deg, var(--primary) 60%, var(--primary) 100%);
}
.header h1 {
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #fff;
}
.header p {
    font-size: 1.15rem;
    color: #e0e0e0;
}
.card {
    background: var(--card-background);
    border-radius: 12px;
    box-shadow: 0 4px 8px var(--shadow-color);
    padding: 2.2rem 2rem;
    margin-bottom: 2.5rem;
    border: 1px solid var(--border);
    transition: box-shadow 0.2s, transform 0.2s;
}
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 14px var(--shadow-color);
}
h2 {
    color: var(--primary);
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    font-size: 1.4rem;
    margin-bottom: 1.2rem;
    font-weight: 700;
}
.form-group, .form-row {
    margin-bottom: 1.5rem;
}
input[type="text"], input[type="date"], input[type="time"], select, textarea {
    width: 100%;
    padding: 0.9rem;
    border: 1.5px solid var(--border);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Quicksand', Arial, sans-serif;
    background: #fff;
    /* color: var(--text); */
    transition: border 0.2s, box-shadow 0.2s;
    box-sizing: border-box;
}
input:focus, select:focus, textarea:focus {
    border-color: var(--primary);
    outline: none;
    box-shadow: 0 0 0 2px var(--primary-light, #3949ab22);
}
.btn, .btn-primary {
    display: inline-block;
    padding: 12px 24px;
    background: var(--primary);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-family: 'Quicksand', sans-serif;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    margin: 5px;
    min-width: 140px;
}
.btn:hover, .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-color);
    opacity: 0.93;
}
.btn-secondary {
    background: var(--accent);
    color: #fff;
}
.btn-secondary:hover {
    background: var(--accent);
    opacity: 0.9;
}
.button-group {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
    justify-content: flex-end;
}
.tabs-menu {
    display: flex;
    gap: 1.5em;
    margin-bottom: 2.2em;
    justify-content: center;
}
.tab-btn {
    background: #f0f0f0;
    color: var(--primary);
    border: none;
    border-radius: 10px 10px 0 0;
    cursor: pointer;
    transition: background 0.2s, color 0.2s, border-bottom 0.2s;
    outline: none;
    padding: 0.8em 2em;
    font-size: 1.1em;
    font-family: 'Quicksand', sans-serif;
    font-weight: 600;
}
.tab-btn.active {
    background: white;
    color: var(--primary);
    border-bottom: 3px solid var(--primary);
    font-weight: 700;
}
.tab-btn:not(.active):hover {
    background: #e8e8e8;
}
.subjects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}
.subject-item {
    background: var(--background-color);
    padding: 1rem;
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
/* Removemos qualquer alteração de background no hover */
.subject-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

/* Importante: sobrescrever explicitamente qualquer background no hover 
.subject-item:hover {
    background: var(--background-color) !important; /* Mantém a mesma cor do estado normal 
}
*/
.checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    width: 100%;
}
.checkbox-wrapper input[type="checkbox"] {
    width: 18px;
    height: 18px;
    cursor: pointer;
}
.checkbox-wrapper span {
    font-size: 1rem;
    font-weight: 500;
    /*color: white;*/
}
/* Esconde o color-box original */
.color-box {
    display: none;
}
.curso-logo {
    width: 53.19px;
    height: 53.19px;
    object-fit: contain;
    margin-right: 8px;
    border-radius: 5px;
    background: #fff;
    border: 1px solid #eee;
}
@media (max-width: 900px) {
    .container {
        padding: 1.2rem;
    }
    .header {
        padding: 1.5rem 0.7rem;
    }
    .card {
        padding: 1.2rem 0.7rem;
    }
}
@media (max-width: 600px) {
    .header h1 {
        font-size: 1.3rem;
    }
    .card {
        padding: 0.9rem 0.4rem;
    }
    .tab-btn {
        padding: 0.6em 1.1em;
        font-size: 1em;
    }
}




.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2.5rem 1.5rem;
}
.header {
    text-align: center;
    padding: 2.5rem 1.5rem 2rem 1.5rem;
    background: var(--primary);
    color: white;
    border-radius: 14px;
    box-shadow: 0 4px 6px var(--shadow-color);
    margin-bottom: 2.5rem;
}
.header h1 {
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #fff;
}
.header p {
    font-size: 1.15rem;
    color: #e0e0e0;
}
.card {
    background: var(--card-background);
    border-radius: 12px;
    box-shadow: 0 4px 8px var(--shadow-color);
    padding: 2.2rem 2rem;
    margin-bottom: 2.5rem;
    border: 1px solid var(--border);
    transition: box-shadow 0.2s, transform 0.2s;
}
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 14px var(--shadow-color);
}
h2 {
    color: var(--primary);
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    font-size: 1.4rem;
    margin-bottom: 1.2rem;
    font-weight: 700;
}
.form-group, .form-row {
    margin-bottom: 1.5rem;
}
input[type="text"], input[type="date"], input[type="time"], select, textarea {
    width: 100%;
    padding: 0.9rem;
    border: 1.5px solid var(--border);
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Quicksand', Arial, sans-serif;
    background: #fff;
    /* color: var(--text); */
    transition: border 0.2s, box-shadow 0.2s;
    box-sizing: border-box;
}
input:focus, select:focus, textarea:focus {
    border-color: var(--primary);
    outline: none;
    box-shadow: 0 0 0 2px var(--primary-light, #3949ab22);
}
.btn, .btn-primary {
    display: inline-block;
    padding: 12px 24px;
    background: var(--primary);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-family: 'Quicksand', sans-serif;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    margin: 5px;
    min-width: 140px;
}
.btn:hover, .btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-color);
    opacity: 0.93;
}
.btn-secondary {
    background: var(--accent);
    color: #fff;
}
.btn-secondary:hover {
    background: var(--accent);
    opacity: 0.9;
}
.button-group {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    flex-wrap: wrap;
    justify-content: flex-end;
}
.tabs-menu {
    display: flex;
    gap: 1.5em;
    margin-bottom: 2.2em;
    justify-content: center;
}
.tab-btn {
    background: #f0f0f0;
    color: var(--primary);
    border: none;
    border-radius: 10px 10px 0 0;
    cursor: pointer;
    transition: background 0.2s, color 0.2s, border-bottom 0.2s;
    outline: none;
    padding: 0.8em 2em;
    font-size: 1.1em;
    font-family: 'Quicksand', sans-serif;
    font-weight: 600;
}
.tab-btn.active {
    background: white;
    color: var(--primary);
    border-bottom: 3px solid var(--primary);
    font-weight: 700;
}
.tab-btn:not(.active):hover {
    background: #e8e8e8;
}
.subjects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.2rem;
    margin-top: 1.5rem;
}

.subject-item:hover {
    background: var(--hover);
    border-color: var(--primary);
}
.checkbox-wrapper {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    flex: 1;
}
input[type="checkbox"] {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border);
    border-radius: 4px;
    cursor: pointer;
    accent-color: var(--primary);
}
input[type="checkbox"]:checked {
    border-color: var(--primary);
}
.curso-logo {
    width: 53.19px;
    height: 53.19px;
    object-fit: contain;
    margin-right: 8px;
    border-radius: 5px;
    background: #fff;
    border: 1px solid #eee;
}
@media (max-width: 900px) {
    .container {
        padding: 1.2rem;
    }
    .header {
        padding: 1.5rem 0.7rem;
    }
    .card {
        padding: 1.2rem 0.7rem;
    }
}
@media (max-width: 600px) {
    .header h1 {
        font-size: 1.3rem;
    }
    .card {
        padding: 0.9rem 0.4rem;
    }
    .tab-btn {
        padding: 0.6em 1.1em;
        font-size: 1em;
    }
}

        .tabs-menu {
            margin-bottom: 2em;
        }
        .tab-btn {
            background: #f0f0f0;
            color: #222;
            border: none;
            border-bottom: 3px solid transparent;
            font-size: 1.16rem;
            font-weight: 500;
            padding: 0.7em 2.2em;
            border-radius: 10px 10px 0 0;
            cursor: pointer;
            transition: background 0.2s, color 0.2s, border-bottom 0.2s;
            outline: none;
        }
        .tab-btn.active {
            background: white;
            color: var(--primary);
            border-bottom: 3px solid var(--primary);
            font-weight: 700;
        }
        .tab-btn:not(.active):hover {
            background: #e8e8e8;
        }


        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Quicksand', serif;
        }

        body {
            background-color: var(--background);
            color: var(--text);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            background: var(--primary);
            color: white;
            padding: 2rem;
            border-radius: 12px;
            box-shadow: 0 4px 6px var(--shadow-color);
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            font-weight: 600;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .card {
            background: var(--card-background);
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px var(--shadow-color);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        .card h2 {
            color: var(--primary);
            font-size: 1.5rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text);
            font-weight: 500;
        }

        input[type="text"],
        input[type="date"],
        input[type="time"] {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid var(--border);
            border-radius: 8px;
            font-size: 1rem;
            transition: all 0.2s ease;
            background: white;
        }

        input[type="text"]:focus,
        input[type="date"]:focus,
        input[type="time"]:focus {
            border-color: var(--primary);
            outline: none;
            box-shadow: 0 0 0 3px rgba(0, 0, 139, 0.1);
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .subjects-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }



        .subject-item:hover {
            background: var(--hover);
            border-color: var(--primary);
        }

        .color-box {
            width: 24px;
            height: 24px;
            border-radius: 6px;
            border: 2px solid var(--border);
        }

        .checkbox-wrapper {
            display: flex;
            align-items: center;
            gap: 0.8rem;
            flex: 1;
        }

        input[type="checkbox"] {
            width: 18px;
            height: 18px;
            border: 2px solid var(--border);
            border-radius: 4px;
            cursor: pointer;
            accent-color: var(--primary);
        }

        .button-group {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }

        button {
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex: 1;
            justify-content: center;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
            border: none;
        }

        .btn-primary:hover {
            background: var(--primary-blue);
            transform: translateY(-1px);
            box-shadow: 0 4px 6px var(--shadow-color);
        }

        .btn-secondary {
            background: white;
            color: var(--text);
            border: 2px solid var(--border);
        }

        .btn-secondary:hover {
            background: var(--hover);
            border-color: var(--primary);
        }

        /* Modal */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            padding: 2rem;
            border-radius: 12px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 4px 20px var(--shadow-color);
            text-align: center;
        }

        .btn-primary {
            margin: 0 auto;
            width: 120px;
            display: block;
        }

        .modal-title {
            color: var(--primary);
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .modal-message {
            margin-bottom: 1.5rem;
            color: var(--text);
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .header {
                padding: 1.5rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .card {
                padding: 1.5rem;
            }

            .button-group {
                flex-direction: column;
            }

            .subjects-grid {
                grid-template-columns: 1fr;
            }
        }

/* Substitua todo o CSS da seção de filtro por este */
.filter-section {
    margin-bottom: 1.5rem;
    position: relative;
}

.search-box {
    position: relative;
    max-width: 400px;
    margin-bottom: 1rem;
}

.search-input {
    width: 100%;
    padding: 0.75rem;
    padding-left: 2.5rem;
    padding-right: 2.5rem;
    border: 2px solid var(--border);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;
    background: white;
}

.search-input:focus {
    border-color: var(--primary);
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 0, 139, 0.1);
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--accent);
    pointer-events: none;
    z-index: 2;
}

.clear-button {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: var(--accent);
    padding: 0.25rem;
    border-radius: 50%;
    transition: all 0.2s ease;
    z-index: 2;
    background: none;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
}

.clear-button:hover {
    background: var(--hover);
    color: var(--primary);
}

.filter-info {
    font-size: 0.9rem;
    color: var(--accent);
    margin-top: 0.5rem;
}

@media (max-width: 768px) {
    .search-box {
        max-width: 100%;
    }
}
</style>

    <!-- Estilos para o Modal de Carregamento -->
    <style>
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 2000; /* Mais alto que outros modais */
            color: white;
            text-align: center;
        }

        .loading-content h3 {
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .loading-content .fas {
            font-size: 2rem;
            margin-bottom: 1rem;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
<!-- Botão Fechar fixo -->
<button id="close-btn-fixed" title="Fechar janela">
    <i class="fas fa-times"></i>
</button>
<style>
    #close-btn-fixed {
        position: fixed;
        bottom: 24px;
        right: 24px;
        z-index: 9999;
        background: var(--primary);
        color: #fff;
        border: none;
        border-radius: 50%;
        width: 48px;
        height: 48px;
        padding: 0;
        box-shadow: 0 4px 16px var(--shadow-color);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: background 0.2s, color 0.2s, box-shadow 0.2s;
        font-size: 1.5rem;
    }
    #close-btn-fixed i {
        pointer-events: none;
    }
    #close-btn-fixed:hover {
        background: var(--primary-light, #3949ab);
        color: #fff;
        box-shadow: 0 6px 24px var(--shadow-color);
    }
    [data-theme="dark"] #close-btn-fixed {
        background: var(--primary);
        color: var(--text);
    }
    [data-theme="dark"] #close-btn-fixed:hover {
        background: var(--primary-light, #3949ab);
        color: var(--text);
    }

    .materias-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 0.75rem;
    margin-top: 1rem;
}
</style>

<style>
        .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    border-bottom: 1px solid var(--border);
    margin-bottom: 30px;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 2px 4px var(--shadow-color);
    background: linear-gradient(90deg, var(--primary) 60%, var(--primary) 100%);
}
.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}
.logo {
    position: relative;
    height: 40px;  /* Altura fixa igual à das imagens */
    width: auto;
    display: flex;
    align-items: center;
}

.logo img {
    position: absolute;
    top: 0;
    left: 0;
    height: 40px;
    width: auto;
    transition: opacity 0.3s ease;
}

/* Controle de visibilidade usando opacity ao invés de display */
.logo-light {
    opacity: 1;
}

.logo-dark {
    opacity: 0;
}

[data-theme="dark"] .logo-light {
    opacity: 0;
}

[data-theme="dark"] .logo-dark {
    opacity: 1;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}
.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    
    color: var(--hover);
}
[data-theme="dark"] .user-info {
    color: var(--text);
}
.user-info .fa-user-circle {
    font-size: 1.2rem;
}
.user-name {
    font-weight: 600;
}
.theme-toggle {
    margin-left: 15px;
}
.theme-btn {
    background: transparent;
    border: none;
    color: var(--hover);
    cursor: pointer;
    font-size: 1.2rem;
    padding: 8px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}
[data-theme="dark"] .theme-btn {
    color: var(--text);
}
.theme-btn:hover {
    background: var(--shadow-color);
}
@media (max-width: 768px) {
    .header {
        padding: 10px;
    }
    .logo img {
        height: 32px;
    }
    .user-info {
        font-size: 0.8rem;
        padding: 6px 12px;
    }
}
@media (max-width: 600px) {
    .header {
        flex-direction: column;
        align-items: flex-start;
        padding: 8px 4px;
    }
    .header-left, .header-right {
        gap: 8px;
    }
    .logo img {
        height: 24px;
    }
    .user-info {
        font-size: 0.75rem;
        padding: 4px 8px;
    }
    .theme-btn {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }
}
</style>
<script>
    document.getElementById('close-btn-fixed').onclick = function() {
        window.close();
    };
</script>

    <div id="app">
    <div class="header">
            <div class="header-left">
                <div class="logo">
                    <img src="../logo/logo_vertical.png" alt="Logo" class="logo-light">
                    <img src="../logo/logo_vertical.png" alt="Logo" class="logo-dark">
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span class="user-name"><?php echo htmlspecialchars($nome_usuario); ?></span>
                </div>
                <div class="theme-toggle">
                    <button id="theme-toggle-btn" class="theme-btn">
                        <i class="fas fa-moon" id="theme-icon"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="container">

            <!-- Formulário de informações básicas SEMPRE visível -->
            <form @submit.prevent="salvarPlanejamento">
                <div class="card">
                    <h2>
                        <i class="fas fa-info-circle"></i>
                        Informações Básicas
                    </h2>
                    <div class="form-group">
                        <label>Nome do Planejamento</label>
                        <input 
                            type="text" 
                            v-model="planejamento.nome" 
                            placeholder="Digite o nome do seu planejamento"
                            required
                        >
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label>Data de Início</label>
                            <input type="date" v-model="planejamento.data_inicio" required>
                        </div>
                        <div class="form-group">
                            <label>Data de Fim</label>
                            <input type="date" v-model="planejamento.data_fim" required>
                        </div>
                        <div class="form-group">
                            <label>Tempo Disponível Diário</label>
                            <input type="time" v-model="planejamento.tempo_planejamento" required>
                        </div>
                    </div>
                                    <!-- Botão para salvar informações do planejamento -->
                <div class="button-group" style="margin-bottom:2.2em;justify-content:flex-end;">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save"></i>
                        Salvar Informações do Planejamento
                    </button>
                </div>
                </div>



                <!-- Menu de abas -->
                <div class="tabs-menu" style="display:flex;gap:1.5em;margin-bottom:2.2em;justify-content:center;">
                    <button type="button" :class="['tab-btn', {active: abaAtiva==='materias'}]" @click="abaAtiva='materias'">
                        <i class="fas fa-book"></i> Matérias
                    </button>
                    <button type="button" :class="['tab-btn', {active: abaAtiva==='editais'}]" @click="abaAtiva='editais'">
                        <i class="fas fa-file-alt"></i> Editais
                    </button>
                    <button type="button" :class="['tab-btn', {active: abaAtiva==='cursos'}]" @click="abaAtiva='cursos'">
                        <i class="fas fa-graduation-cap"></i> Cursos
                    </button>
                </div>

                <!-- Só UM dos cards abaixo aparece -->
                <div v-show="abaAtiva==='materias'">
                    <div class="card">
                        <h2>
                            <i class="fas fa-book"></i>
                            Matérias de Modo Avulso
                        </h2>
                        <div class="filter-section">
                            <div class="search-box">
                                <input 
                                    type="text" 
                                    v-model="filtroMaterias" 
                                    placeholder="Buscar matérias..."
                                    class="search-input"
                                >
                                <span v-if="filtroMaterias" @click="limparFiltro" class="clear-button">
                                    <i class="fas fa-times"></i>
                                </span>
                            </div>
                            <div class="filter-info" v-if="materiasFiltradas.length !== materias.length">
                                Mostrando {{ materiasFiltradas.length }} de {{ materias.length }} matérias
                            </div>
                        </div>
                        <div class="subjects-grid">
                            <div 
                                v-for="materia in materiasFiltradas"
                                :key="materia.idmateria" 
                                class="subject-item"
                                :style="{
                                    backgroundColor: materia.cor || '#ccc',
                                    color: isColorDark(materia.cor) ? '#ffffff' : '#000000'
                                }"
                            >
                                <div class="checkbox-wrapper">
                                    <input 
                                        type="checkbox" 
                                        v-model="materiasSelecionadas" 
                                        :value="materia.idmateria"
                                    >
                                    <span>{{ materia.nome }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="button-group">
                            <button type="button" class="btn-primary" @click="salvarMaterias">
                                <i class="fas fa-save"></i>
                                Salvar Alterações
                            </button>
                        </div>
                    </div>
                </div>
                <div v-show="abaAtiva==='editais'">
                    <div class="card">
                        <h2>
                            <i class="fas fa-file-alt"></i>
                            Matérias por Edital
                        </h2>
                        <div class="filter-section">
                            <div class="search-box">
                                <input 
                                    type="text" 
                                    v-model="buscarEdital" 
                                    placeholder="Buscar edital..."
                                    class="search-input"
                                >
                                <span v-if="buscarEdital" @click="buscarEdital = ''" class="clear-button">
                                    <i class="fas fa-times"></i>
                                </span>
                            </div>
                            <div class="filter-info" v-if="editaisFiltrados.length !== editais.length">
                                Mostrando {{ editaisFiltrados.length }} de {{ editais.length }} editais
                            </div>
                        </div>
                        <div class="subjects-grid">
                            <div v-for="edital in editaisFiltrados" :key="edital.id_edital" class="card">
                                <div class="edital-info">
                                    <div class="edital-header" @click="toggleEdital(edital.id_edital)" style="cursor: pointer;">
                                        <div class="edital-header-content" style="display: flex; align-items: center; gap: 15px;">
                                            <!-- Adicionando a logo do edital -->
                                            <div class="edital-logo" style="width: 60px; height: 60px; min-width: 60px; border-radius: 50%; overflow: hidden; display: flex; align-items: center; justify-content: center; background-color: var(--secondary);">
                                                <template v-if="edital.logo_url">
                                                    <img :src="tratarCaminhoLogo(edital.logo_url)" 
                                                         :alt="'Logo ' + edital.nome"
                                                         style="max-width: 100%; max-height: 100%; object-fit: contain;"
                                                         @error="tratarErroImagem">
                                                </template>
                                                <template v-else>
                                                    <i class="fas fa-file-alt fa-2x" style="color: var(--primary);"></i>
                                                </template>
                                            </div>
                                            <div style="flex: 1;">
                                                <h3 class="edital-nome">{{ edital.nome }}</h3>
                                                <span class="edital-orgao">{{ edital.orgao }}</span>
                                                <p class="edital-descricao" v-if="edital.descricao">{{ edital.descricao }}</p>
                                                <span class="materias-count">
                                                    <i class="fas fa-book"></i> {{ edital.materias.length }} matérias
                                                </span>
                                            </div>
                                        </div>
                                        <div class="edital-toggle">
                                            <i class="fas" :class="editaisExpandidos.includes(edital.id_edital) ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
                                        </div>
                                    </div>
                                    <div class="materias-container" v-show="editaisExpandidos.includes(edital.id_edital)">
                                        <div style="margin-bottom: 0.7rem; display: flex; align-items: center; gap: 0.5rem;">
                                            <input type="checkbox"
                                                   :id="'selecionar-todas-' + edital.id_edital"
                                                   :checked="edital.materias.every(m => materiasSelecionadas.includes(Number(m.idmateria))) && edital.materias.length > 0"
                                                   @change="toggleSelecionarTodasMaterias(edital)"
                                            >
                                            <label :for="'selecionar-todas-' + edital.id_edital" style="font-weight: 500; cursor: pointer;">Selecionar todas</label>
                                        </div>
                                        <div class="materias-grid">
                                            <div v-for="materia in edital.materias" 
                                                 :key="materia.idmateria" 
                                                 class="subject-item"
                                                 :style="{
                                                     backgroundColor: materia.cor || '#ccc',
                                                     color: isColorDark(materia.cor) ? '#ffffff' : '#000000'
                                                 }">
                                                <div class="checkbox-wrapper">
                                                    <input 
                                                        type="checkbox" 
                                                        :id="'materia-edital-' + materia.idmateria" 
                                                        :value="Number(materia.idmateria)" 
                                                        v-model="materiasSelecionadas"
                                                    >
                                                    <span>{{ materia.nome }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="button-group">
                            <button type="button" class="btn-primary" @click="salvarMaterias">
                                <i class="fas fa-save"></i>
                                Salvar Alterações
                            </button>
                        </div>
                    </div>
                </div>
                <div v-show="abaAtiva==='cursos'">
                    <div class="card">
                        <h2>
                            <i class="fas fa-graduation-cap"></i>
                            Cursos
                        </h2>
                        <div class="filter-section">
                            <div class="search-box">
                                <input 
                                    type="text" 
                                    v-model="termoBuscaCurso" 
                                    placeholder="Buscar cursos..."
                                    class="search-input"
                                    @input="filtrarCursos"
                                >
                                <span v-if="termoBuscaCurso" @click="limparBuscaCurso" class="clear-button">
                                    <i class="fas fa-times"></i>
                                </span>
                            </div>
                            <div class="filter-info" v-if="cursosFiltrados.length !== cursos.length">
                                Mostrando {{ cursosFiltrados.length }} de {{ cursos.length }} cursos
                            </div>
                        </div>
                        <div class="subjects-grid">
                            <div 
                                v-for="curso in cursosFiltrados"
                                :key="curso.idcurso" 
                                class="subject-item"
                            >
                                <div class="checkbox-wrapper">
                                    <input 
                                        type="checkbox" 
                                        v-model="cursosSelecionados" 
                                        :value="curso.idcurso"
                                    >
                                    <img :src="
  curso.logo_url
    ? '/cadastros/img/cursos/' + curso.logo_url.split('/').pop()
    : '/logo/Estudo Off/favicon_32x32.png'"
    alt="Logo do curso" 
    class="curso-logo" 
    style="width:53.19px;height:53.19px;object-fit:contain;margin-right:8px;border-radius:5px;background:#fff;border:1px solid #eee;"
>
                                    <span>{{ curso.nome }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="button-group">
                            <button type="button" class="btn-primary" @click="salvarCursos">
                                <i class="fas fa-save"></i>
                                Salvar Cursos
                            </button>
                        </div>
                    </div>
                </div>


            <!-- Modal de Sucesso -->
            <div v-if="mostrarModal" class="modal-overlay">
                <div class="modal-content">
                    <h3 class="modal-title">
                        <i class="fas fa-check-circle"></i>
                        Sucesso!
                    </h3>
                    <p class="modal-message">Suas alterações foram salvas com sucesso.</p>
                    <button class="btn-primary" @click="fecharModalEJanela">
                        OK
                    </button>
                </div>
            </div>

            <!-- Modal de Sucesso ao salvar cursos -->
            <div v-if="mensagemCurso" class="modal-overlay">
                <div class="modal-content">
                    <h3 class="modal-title">
                        <i class="fas fa-check-circle"></i>
                        Sucesso!
                    </h3>
                    <p class="modal-message">{{ mensagemCurso }}</p>
                    <button class="btn-primary" @click="limparMensagemCurso">
                        OK
                    </button>
                </div>
            </div>
        </div>

        <!-- Modal de Carregamento -->
        <div v-if="isLoading" class="loading-overlay">
            <div class="loading-content">
                <i class="fas fa-spinner"></i>
                <h3>Salvando alterações...</h3>
                <p>Por favor, aguarde.</p>
            </div>
        </div>
    </div>

    <script>
    const { createApp } = Vue

    createApp({
    data() {
        // Converte os dados PHP para JSON de forma segura
        const planejamentoData = {
            idplanejamento: <?php echo isset($id_planejamento) ? $id_planejamento : 'null' ?>,
            nome: <?php echo isset($planejamento['nome']) ? json_encode($planejamento['nome']) : "''" ?>,
            data_inicio: <?php echo isset($planejamento['data_inicio']) ? json_encode($planejamento['data_inicio']) : "''" ?>,
            data_fim: <?php echo isset($planejamento['data_fim']) ? json_encode($planejamento['data_fim']) : "''" ?>,
            tempo_planejamento: <?php echo isset($planejamento['tempo_planejamento']) ? json_encode($planejamento['tempo_planejamento']) : "'00:00'" ?>
        };
        const materiasData = <?php echo isset($todas_materias) ? json_encode($todas_materias) : '[]' ?>;
        const materiasSelecionadasData = <?php echo !empty($materias_selecionadas) ? json_encode($materias_selecionadas) : '[]' ?>;
        const cursosData = <?php echo isset($todos_cursos) ? json_encode($todos_cursos) : '[]' ?>;
        const cursosSelecionadosData = <?php echo !empty($cursos_selecionados) ? json_encode($cursos_selecionados) : '[]' ?>;
        const editaisData = <?php echo isset($editais) ? json_encode($editais) : '[]' ?>;
        // Inicialização do tema escuro
        let temaEscuro = false;
        const temaSalvo = localStorage.getItem('temaEscuro');
        if (temaSalvo !== null) {
            temaEscuro = temaSalvo === 'true';
        } else {
            temaEscuro = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        }
        return {
            planejamento: planejamentoData,
            materias: materiasData,
            materiasSelecionadas: materiasSelecionadasData,
            filtroMaterias: '',
            mostrarModal: false,
            cursos: cursosData,
            cursosSelecionados: cursosSelecionadosData,
            termoBuscaCurso: '',
            cursosFiltrados: cursosData,
            mensagemCurso: null,
            salvandoCursos: false,
            abaAtiva: 'materias',
            temaEscuro: temaEscuro,
            editais: editaisData,
            buscarEdital: '',
            editaisExpandidos: [],
            isLoading: false // Nova propriedade para o estado de carregamento
        }
    },
    mounted() {
        this.filtrarCursos && this.filtrarCursos();
        this.aplicarTema();
    },
    computed: {
        materiasFiltradas() {
            const filtro = this.filtroMaterias.toLowerCase().trim();
            if (!filtro) return this.materias;
            return this.materias.filter(materia => 
                materia.nome.toLowerCase().includes(filtro)
            );
        },
        editaisFiltrados() {
            if (!this.buscarEdital) return this.editais;
            const busca = this.buscarEdital.toLowerCase();
            return this.editais.filter(edital => 
                (edital.descricao && edital.descricao.toLowerCase().includes(busca)) ||
                (edital.orgao && edital.orgao.toLowerCase().includes(busca))
            );
        }
    },
    methods: {
        alternarTema() {
            this.temaEscuro = !this.temaEscuro;
            localStorage.setItem('temaEscuro', this.temaEscuro);
            this.aplicarTema();
        },
        aplicarTema() {
            this.$nextTick(() => {
                if (this.temaEscuro) {
                    document.documentElement.setAttribute('data-theme', 'dark');
                } else {
                    document.documentElement.removeAttribute('data-theme');
                }
            });
        },
        async salvarPlanejamento() {
            this.isLoading = true;
            try {
                const formData = new FormData();
                formData.append('id_planejamento', this.planejamento.idplanejamento);
                formData.append('nome', this.planejamento.nome);
                formData.append('data_inicio', this.planejamento.data_inicio);
                formData.append('data_fim', this.planejamento.data_fim);
                formData.append('tempo_planejamento', this.planejamento.tempo_planejamento);
                formData.append('materias', JSON.stringify(this.materiasSelecionadas));

                const response = await fetch('salvar_planejamento.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok && data.sucesso) {
                    this.mostrarModal = true;
                    // Dispara evento para atualizar a página index
                    localStorage.setItem('atualizarPaginaInicial', new Date().getTime().toString());
                    localStorage.setItem('recarregarIndex', 'true');
                } else {
                    alert(data.erro || 'Erro ao salvar o planejamento');
                }
            } catch (error) {
                console.error('Erro:', error);
                alert('Erro ao salvar o planejamento');
            } finally {
                this.isLoading = false;
            }
        },
        fecharModal() {
            this.mostrarModal = false;
        },
        fecharJanela() {
            window.close();
        },
        fecharModalEJanela() {
            this.mostrarModal = false;
            if (window.opener) {
                window.opener.location.reload();
            }
        },
        limparFiltro() {
            this.filtroMaterias = '';
        },
        async salvarCursos() {
            this.isLoading = true;
            try {
                const response = await fetch('salvar_cursos_usuario.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        cursos: this.cursosSelecionados
                    })
                });

                const data = await response.json();
                
                if (data.success) {
                    this.mensagemCurso = 'Cursos salvos com sucesso!';
                    // Dispara evento para atualizar a página index
                    localStorage.setItem('atualizarPaginaInicial', new Date().getTime().toString());
                    localStorage.setItem('recarregarIndex', 'true');
                } else {
                    this.mensagemCurso = data.message || 'Erro ao salvar cursos.';
                }
            } catch (error) {
                console.error('Erro:', error);
                this.mensagemCurso = 'Erro ao salvar cursos.';
            } finally {
                this.isLoading = false;
                this.salvandoCursos = false;
                setTimeout(() => {
                    this.mensagemCurso = null;
                }, 2000);
            }
        },
        filtrarCursos() {
            const termo = this.termoBuscaCurso.toLowerCase().trim();
            if (!termo) {
                this.cursosFiltrados = this.cursos;
            } else {
                this.cursosFiltrados = this.cursos.filter(curso =>
                    curso.nome.toLowerCase().includes(termo)
                );
            }
        },
        limparBuscaCurso() {
            this.termoBuscaCurso = '';
            this.filtrarCursos();
        },
        limparMensagemCurso() {
            this.mensagemCurso = null;
        },
        hexToRgb(hex) {
            // Remove o # se existir
            hex = hex.replace('#', '');
            
            // Converte para RGB
            const r = parseInt(hex.substring(0, 2), 16);
            const g = parseInt(hex.substring(2, 4), 16);
            const b = parseInt(hex.substring(4, 6), 16);
            
            return `${r}, ${g}, ${b}`;
        },
        isColorDark(hex) {
            if (!hex) return false;
            hex = hex.replace('#', '');
            if (hex.length === 3) {
                hex = hex.split('').map(x => x+x).join('');
            }
            const r = parseInt(hex.substr(0,2), 16);
            const g = parseInt(hex.substr(2,2), 16);
            const b = parseInt(hex.substr(4,2), 16);
            return (0.299 * r + 0.587 * g + 0.114 * b) < 140;
        },
        toggleEdital(editalId) {
            const index = this.editaisExpandidos.indexOf(editalId);
            if (index === -1) {
                this.editaisExpandidos.push(editalId);
            } else {
                this.editaisExpandidos.splice(index, 1);
            }
        },
        toggleSelecionarTodasMaterias(edital) {
            const todasSelecionadas = edital.materias.every(m => this.materiasSelecionadas.includes(Number(m.idmateria)));
            if (todasSelecionadas) {
                // Desmarcar todas
                edital.materias.forEach(m => {
                    const idx = this.materiasSelecionadas.indexOf(Number(m.idmateria));
                    if (idx !== -1) this.materiasSelecionadas.splice(idx, 1);
                });
            } else {
                // Selecionar todas
                edital.materias.forEach(m => {
                    if (!this.materiasSelecionadas.includes(Number(m.idmateria))) {
                        this.materiasSelecionadas.push(Number(m.idmateria));
                    }
                });
            }
        },
        async salvarMaterias() {
            this.isLoading = true;
            try {
                const formData = new FormData();
                formData.append('id_planejamento', this.planejamento.idplanejamento);
                formData.append('materias', JSON.stringify(this.materiasSelecionadas));

                const response = await fetch('salvar_materias_planejamento.php', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (response.ok && data.sucesso) {
                    this.mostrarModal = true;
                    // Dispara evento para atualizar a página index
                    localStorage.setItem('atualizarPaginaInicial', new Date().getTime().toString());
                    localStorage.setItem('recarregarIndex', 'true');
                } else {
                    alert(data.erro || 'Erro ao salvar as matérias');
                }
            } catch (error) {
                console.error('Erro:', error);
                alert('Erro ao salvar as matérias');
            } finally {
                this.isLoading = false;
            }
        },
        tratarCaminhoLogo(logoUrl) {
            if (!logoUrl) return '';
            
            // Se o caminho já começar com /cronograma_inteligente/, mantém como está
            if (logoUrl.indexOf('/cronograma_inteligente/') === 0) {
                return logoUrl;
            }
            
            // Se o caminho não tiver o prefixo completo, adiciona
            // Remove qualquer ../ inicial se existir
            if (logoUrl.indexOf('../') === 0) {
                logoUrl = logoUrl.substring(3);
            }
            
            // Remove qualquer / inicial se existir
            if (logoUrl.indexOf('/') === 0) {
                logoUrl = logoUrl.substring(1);
            }
            
            // Adiciona o caminho base
            return '/cronograma_inteligente/' + logoUrl;
        },
        tratarErroImagem(event) {
            // Substitui a imagem por um ícone padrão em caso de erro
            event.target.style.display = 'none';
            const container = event.target.parentElement;
            container.innerHTML = '<i class="fas fa-file-alt fa-2x" style="color: var(--primary);"></i>';
        }
    }
}).mount('#app')
</script>
</body>
</html>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const themeBtn = document.getElementById('theme-toggle-btn');
    const themeIcon = document.getElementById('theme-icon');
    
    // Verifica tema salvo
    let isDark = localStorage.getItem('temaEscuro') === 'true';
    applyTheme(isDark);

    themeBtn.addEventListener('click', function() {
        isDark = !isDark;
        applyTheme(isDark);
        localStorage.setItem('temaEscuro', isDark);
    });

    function applyTheme(dark) {
        if (dark) {
            document.documentElement.setAttribute('data-theme', 'dark');
            themeIcon.classList.remove('fa-moon');
            themeIcon.classList.add('fa-sun');
        } else {
            document.documentElement.removeAttribute('data-theme');
            themeIcon.classList.remove('fa-sun');
            themeIcon.classList.add('fa-moon');
        }
    }
});
</script>

<style>
/* Adicione estilos ao seu CSS existente */
.theme-btn {
    background: transparent;
    border: none;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.theme-btn i {
    font-size: 1.2rem;
    color: white; /* Cor do ícone */
}

.theme-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Ajustes para o modo escuro */
[data-theme="dark"] .logo-light {
    display: none;
}

[data-theme="dark"] .logo-dark {
    display: block;
}

.logo-dark {
    display: none;
}

.logo-light {
    display: block;
}
</style>
</body>
</html>







