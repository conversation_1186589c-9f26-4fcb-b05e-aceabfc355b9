<?php
// Incluindo o arquivo de conexão com o banco de dados PostgreSQL
include 'processa_index.php';
include_once("conexao_POST.php");


// Verificar se a conexão foi estabelecida com sucesso
if (!$conexao) {
    die("Erro na conexão: " . pg_last_error());
}
//$id_planejamento = $_SESSION['idusuario'];
$id_usuario = $_SESSION['idusuario'];
//echo '<div class="caixa-titulo3">' . $titulo . '</div>';
//echo "<div class='caixa-titulo3'> Sua Agenda Pessoal --> $id_usuario <--</div>";
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> à Estudar</title>

    <style>
        body {
            font-family: 'Courier New', Courier, monospace;
        }

        table {
            margin: 0 auto;
            border-collapse: collapse;
            width: 80%;
            border: 1px solid #2b2723; /* Adiciona borda à tabela */
        }

        th {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #2b2723;
            border-right: 1px solid #2b2723; /* Adiciona borda à direita das células */
            white-space: nowrap; /* Evita quebras de linha */
            font-weight: bold; /* Negrito apenas para as células de cabeçalho */
        }

        td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #2b2723;
            border-right: 1px solid #2b2723; /* Adiciona borda à direita das células */
            white-space: nowrap; /* Evita quebras de linha */
        }

        th:first-child,
        td:first-child {
            border-left: 1px solid #2b2723; /* Adiciona borda à esquerda das células da primeira coluna */
        }


        .details {
            /* Remova a propriedade width e ajuste max-width conforme necessário */
            max-width: 60%;
            margin: 50px auto;
            background-color: rgba(222, 173, 69, 0.49);
            border: 2px solid #ccc;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            /* Adicione a propriedade word-wrap */
            word-wrap: break-word;
        }

        .details p {
            margin: 8px 0;
            line-height: 1.6;
            white-space: nowrap;
        }

        .details strong {
            width: 210px; /* Largura maior para a primeira coluna */
            display: inline-block;
            font-weight: bold;
        }
    </style>
</head>
<link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
<body>

<?php


// Query para contar o número de linhas na tabela planejamento_materia
//$query_contagem = "SELECT COUNT(*) FROM appEstudo.planejamento_materia";
//$query_contagem = "SELECT COUNT(*) FROM appEstudo.planejamento_materia WHERE planejamento_idplanejamento = '$id_planejamento'";

$query_contagem = "SELECT 
    idplanejamento,
    (
        SELECT COUNT(*) 
        FROM appEstudo.planejamento_materia 
        WHERE planejamento_idplanejamento = p.idplanejamento
    ) AS count_materia
FROM 
    appEstudo.planejamento p
WHERE 
    usuario_idusuario = $id_usuario;
";

$result = pg_query($conexao, $query_contagem);

if ($result) {
    $row = pg_fetch_assoc($result);

    if ($row && isset($row['count_materia'])) {
        $count_materia = $row['count_materia'];
        $id_planejamento = $row['idplanejamento'];
        //echo "O número de linhas na tabela planejamento_materia é: " . $count_materia. " ----  ";
    } else {
        echo "Nenhum resultado encontrado ou count_materia não está definido.";
    }
} else {
    // echo "Erro na consulta: " . pg_last_error($conn);
}

// Executar a query
//$resultado_contagem = pg_query($conexao, $query_contagem);

// Extrair o resultado como uma matriz associativa
//$linha_contagem = pg_fetch_array($resultado_contagem);

// Armazenar o resultado da contagem em uma variável
//$numero_de_linhas = $linha_contagem['count'] - 1;
$numero_de_linhas = $count_materia - 1;
//echo $numero_de_linhas;

// Exibir o número de linhas
//echo "Número de linhas na tabela: " . $numero_de_linhas;

// Montar a query para selecionar o ID da próxima matéria a ser estudada
$query_proxima_materia = "SELECT pm.*, m.nome AS nome_materia 
FROM appEstudo.planejamento_materia pm 
JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria 
WHERE pm.planejamento_idplanejamento = $id_planejamento 
AND pm.idplanejamento_materia = (
    SELECT CASE 
        WHEN pm.idplanejamento_materia + 1 > (SELECT MAX(pm2.idplanejamento_materia) FROM appEstudo.planejamento_materia pm2 WHERE pm2.planejamento_idplanejamento = $id_planejamento) 
        THEN (SELECT MIN(pm2.idplanejamento_materia) FROM appEstudo.planejamento_materia pm2 WHERE pm2.planejamento_idplanejamento = $id_planejamento)
        ELSE pm.idplanejamento_materia + 1
    END
    FROM appEstudo.planejamento_materia pm 
    JOIN appEstudo.estudos e ON pm.materia_idmateria = e.materia_idmateria
    WHERE e.planejamento_usuario_idusuario = $id_usuario
    ORDER BY e.idestudos DESC
    LIMIT 1
)
";

// Executar a query para obter os detalhes da próxima matéria
$resultado_proxima_materia = pg_query($conexao, $query_proxima_materia);

?>


<?php
if ($resultado_proxima_materia && pg_num_rows($resultado_proxima_materia) > 0) {
    // Exibir os detalhes da próxima matéria
    $linha_proxima_materia = pg_fetch_assoc($resultado_proxima_materia);

    // Obter o ID da próxima matéria
    $id_proxima_materia = $linha_proxima_materia['materia_idmateria'];
    $Nome_proxima_materia = $linha_proxima_materia['nome_materia'];

    // Montar a query para obter todos os campos do estudo relacionado à próxima matéria
    $query_estudo_proxima_materia = "SELECT e.*, m.nome AS nome_materia, c.nome AS nome_curso
                                  FROM appEstudo.estudos e 
                                  JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria 
                                  JOIN appEstudo.curso c ON e.idcurso = c.idcurso 
                                  WHERE e.planejamento_usuario_idusuario = '$id_usuario'
                                  AND e.materia_idmateria = $id_proxima_materia  
                                  ORDER BY e.idestudos DESC 
                                  LIMIT 1";
    //echo "usuario: " . $id_usuario;
    //echo " Materia: " . $id_proxima_materia;

    // Executar a query para obter os dados do estudo relacionado à próxima matéria
    $resultado_estudo_proxima_materia = pg_query($conexao, $query_estudo_proxima_materia);


    if ($resultado_estudo_proxima_materia && pg_num_rows($resultado_estudo_proxima_materia) > 0) {
        // Exibir os detalhes do estudo relacionado à próxima matéria dentro da tabela
        $linha_estudo_proxima_materia = pg_fetch_assoc($resultado_estudo_proxima_materia);
        echo '<div class="card text-black bg-warning mb-3" style="max-width;">
           <div class="card-header text-center"><h4><strong>' . $linha_estudo_proxima_materia['nome_materia'] . '</strong></h4></div>
        <div class="card-body">
        
        <h5 class="card-text text-center"> <b class="text-danger">' . $linha_estudo_proxima_materia['nome_curso'] . '</b></h5>
        <h5 class="card-text text-center"> Último Estudo:<strong> ' . $linha_estudo_proxima_materia['ponto_estudado'] . '</strong></h5>  
        <h5 class="card-text text-center ">Método:<strong> <b class="text-danger">' . $linha_estudo_proxima_materia['metodo'] . '</b></strong> </h5>

           
        <div class="row">
            <div class="col text-body">
                    <p class="card-text text-body "><strong>Bruto: </strong> ' . $linha_estudo_proxima_materia['tempo_bruto'] . '<strong> Perdido: </strong> ' . $linha_estudo_proxima_materia['tempo_perdido'] . ' </p>
            </div>
            <div class="col text-end">
            <p class="card-text text-end "><strong>Início: </strong> ' . $linha_estudo_proxima_materia['hora_inicio'] . '<strong> Fim: </strong> ' . $linha_estudo_proxima_materia['hora_fim'] . ' </p>
            </div>
        </div> 
       
<div class="row">
    <div class="col text-body">
        <h5 class="card-text"><strong>Líquido:</strong><b class="text-danger"> ' . $linha_estudo_proxima_materia['tempo_liquido'] . '</b></strong></h5>
    </div>
    <div class="col text-end">
        <h5 class="card-text text-end"><strong>Data: </strong><b class="text-danger">' . date('d/m/Y', strtotime($linha_estudo_proxima_materia['data'])) . '</b></h5>
    </div>
</div>       
     </div>
</div>';

    } else {
        //echo "<tr><td colspan='14'>Nenhum resultado encontrado para o estudo relacionado à próxima matéria.</td></tr>";
        echo '<div class="card text-black bg-warning mb-3" style="max-width;">
           <div class="card-header text-center"><H4><strong><span style="color: white; background-color: red;">ATENÇÃO</span> Muito Tempo sem Estudar</strong></h4></div>
        <div class="card-body">
        <h4 class="card-title text-center"><strong>' . $Nome_proxima_materia . '</strong></h4>
        <h5 class="card-title text-center"><strong><b class="text-danger">Começe ou Revise!</strong></b></h5>
        </div>
        </div>';
    }


} else {
    echo '<div class="card text-black bg-warning mb-3" style="max-width;" xmlns="http://www.w3.org/1999/html">
           <div class="card-header text-center"><h4><strong><b class="text-danger">Não Iniciou o Estudo!</strong></b></h4></div>
           </div>';
}


// Fechar a conexão com o banco de dados PostgreSQL
pg_close($conexao);
?>
