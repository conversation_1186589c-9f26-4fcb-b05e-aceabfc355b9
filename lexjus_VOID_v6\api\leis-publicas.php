<?php
/**
 * API Pública para informações básicas das leis
 * Não requer autenticação - usado na página inicial
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Tratar requisições OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

$metodo = $_SERVER['REQUEST_METHOD'];
$acao = $_GET['acao'] ?? 'listar';

try {
    switch ($metodo) {
        case 'GET':
            switch ($acao) {
                case 'listar':
                    listarLeisPublicas();
                    break;
                case 'preview':
                    obterPreviewLei();
                    break;
                default:
                    listarLeisPublicas();
                    break;
            }
            break;
        default:
            http_response_code(405);
            echo json_encode(['erro' => 'Método não permitido']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['erro' => 'Erro interno do servidor: ' . $e->getMessage()]);
}

/**
 * Lista todas as leis disponíveis (informações públicas)
 */
function listarLeisPublicas() {
    // Definir leis disponíveis com informações básicas
    $leis = [
        [
            'codigo' => 'CF',
            'nome' => 'Constituição Federal',
            'nome_completo' => 'Constituição da República Federativa do Brasil de 1988',
            'descricao' => 'A lei fundamental e suprema do Brasil, que estabelece os direitos e deveres dos cidadãos, a organização do Estado e os princípios fundamentais da República.',
            'arquivo_json' => 'CF.json',
            'icone' => 'fas fa-flag',
            'cor_tema' => '#e74c3c',
            'total_artigos' => 250,
            'capitulos' => 9,
            'secoes' => 45,
            'ativa' => true,
            'ordem_exibicao' => 1
        ],
        [
            'codigo' => 'CC',
            'nome' => 'Código Civil',
            'nome_completo' => 'Lei nº 10.406, de 10 de janeiro de 2002',
            'descricao' => 'Estabelece as normas de direito civil, regulamentando as relações jurídicas entre pessoas físicas e jurídicas, contratos, propriedade e família.',
            'arquivo_json' => 'codigo_civil_formato_lexjus_final.json',
            'icone' => 'fas fa-users',
            'cor_tema' => '#3498db',
            'total_artigos' => 2046,
            'capitulos' => 21,
            'secoes' => 156,
            'ativa' => true,
            'ordem_exibicao' => 2
        ],
        [
            'codigo' => 'CP',
            'nome' => 'Código Penal',
            'nome_completo' => 'Decreto-Lei nº 2.848, de 7 de dezembro de 1940',
            'descricao' => 'Define os crimes e estabelece as penas correspondentes, sendo a principal lei penal do Brasil.',
            'arquivo_json' => 'codigo_penal_limpo.json',
            'icone' => 'fas fa-gavel',
            'cor_tema' => '#e67e22',
            'total_artigos' => 361,
            'capitulos' => 12,
            'secoes' => 78,
            'ativa' => true,
            'ordem_exibicao' => 3
        ],
        [
            'codigo' => 'CPC',
            'nome' => 'Código de Processo Civil',
            'nome_completo' => 'Lei nº 13.105, de 16 de março de 2015',
            'descricao' => 'Estabelece as normas processuais civis, regulamentando os procedimentos judiciais em matéria civil.',
            'arquivo_json' => 'codigo_processo_civil_formato_lexjus.json',
            'icone' => 'fas fa-balance-scale',
            'cor_tema' => '#9b59b6',
            'total_artigos' => 1072,
            'capitulos' => 15,
            'secoes' => 89,
            'ativa' => true,
            'ordem_exibicao' => 4
        ],
        [
            'codigo' => 'CPP',
            'nome' => 'Código de Processo Penal',
            'nome_completo' => 'Decreto-Lei nº 3.689, de 3 de outubro de 1941',
            'descricao' => 'Regula o processo penal brasileiro, estabelecendo os procedimentos para investigação e julgamento de crimes.',
            'arquivo_json' => 'cpp_final_perfeito.json',
            'icone' => 'fas fa-search',
            'cor_tema' => '#27ae60',
            'total_artigos' => 811,
            'capitulos' => 18,
            'secoes' => 67,
            'ativa' => true,
            'ordem_exibicao' => 5
        ]
    ];

    // Filtrar apenas leis ativas
    $leis_ativas = array_filter($leis, function($lei) {
        return $lei['ativa'] === true;
    });

    // Ordenar por ordem de exibição
    usort($leis_ativas, function($a, $b) {
        return $a['ordem_exibicao'] - $b['ordem_exibicao'];
    });

    echo json_encode([
        'sucesso' => true,
        'leis' => array_values($leis_ativas),
        'total' => count($leis_ativas)
    ]);
}

/**
 * Obtém preview de uma lei específica (primeiros artigos)
 */
function obterPreviewLei() {
    $codigo = $_GET['codigo'] ?? '';

    if (empty($codigo)) {
        http_response_code(400);
        echo json_encode(['erro' => 'Código da lei é obrigatório']);
        return;
    }

    // Mapear códigos para arquivos
    $arquivos_leis = [
        'CF' => 'CF.json',
        'CC' => 'codigo_civil_formato_lexjus_final.json',
        'CP' => 'codigo_penal_limpo.json',
        'CPC' => 'codigo_processo_civil_formato_lexjus.json',
        'CPP' => 'cpp_final_perfeito.json'
    ];

    if (!isset($arquivos_leis[$codigo])) {
        http_response_code(404);
        echo json_encode(['erro' => 'Lei não encontrada']);
        return;
    }

    $arquivo_json = __DIR__ . '/../banco/' . $arquivos_leis[$codigo];

    if (!file_exists($arquivo_json)) {
        http_response_code(404);
        echo json_encode(['erro' => 'Arquivo da lei não encontrado']);
        return;
    }

    $artigos_json = file_get_contents($arquivo_json);
    $artigos = json_decode($artigos_json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        http_response_code(500);
        echo json_encode(['erro' => 'Erro ao decodificar JSON da lei']);
        return;
    }

    // Retornar apenas os primeiros 5 artigos para preview
    $preview_artigos = array_slice($artigos, 0, 5);

    // Simplificar estrutura para preview
    $artigos_simplificados = array_map(function($artigo) {
        return [
            'artigo' => $artigo['artigo'] ?? '',
            'caput' => $artigo['caput'] ?? '',
            'incisos_count' => count($artigo['incisos'] ?? []),
            'paragrafos_count' => count($artigo['paragrafos_numerados'] ?? [])
        ];
    }, $preview_artigos);

    echo json_encode([
        'sucesso' => true,
        'codigo' => $codigo,
        'artigos' => $artigos_simplificados,
        'total_artigos' => count($artigos),
        'preview_count' => count($artigos_simplificados)
    ]);
}

?>
