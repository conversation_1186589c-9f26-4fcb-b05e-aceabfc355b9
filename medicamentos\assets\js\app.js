// assets/js/app.js

document.addEventListener('DOMContentLoaded', function() {
  // Adicionar mensagens de sucesso/erro se existirem na URL
  const urlParams = new URLSearchParams(window.location.search);
  if (urlParams.has('success') || urlParams.has('error')) {
      const messageContainer = document.createElement('div');
      messageContainer.className = 'container mt-3';
      
      if (urlParams.has('success')) {
          const successType = urlParams.get('success');
          let message = 'Operação realizada com sucesso!';
          
          switch (successType) {
              case '1':
                  message = 'Medicamento adicionado com sucesso!';
                  break;
              case '2':
                  message = 'Medicamento atualizado com sucesso!';
                  break;
              case '3':
                  message = 'Uso de medicamento confirmado com sucesso!';
                  break;
              case '4':
                  message = 'Medicamento desativado com sucesso!';
                  break;
          }
          
          messageContainer.innerHTML = `
              <div class="alert alert-success alert-dismissible fade show" role="alert">
                  ${message}
                  <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
              </div>
          `;
      } else if (urlParams.has('error')) {
          const errorType = urlParams.get('error');
          let message = 'Ocorreu um erro inesperado.';
          
          if (errorType === '1') {
              message = 'Ocorreu um erro ao processar sua solicitação.';
          }
          
          messageContainer.innerHTML = `
              <div class="alert alert-danger alert-dismissible fade show" role="alert">
                  ${message}
                  <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fechar"></button>
              </div>
          `;
      }
      
      // Inserir após o header
      const header = document.querySelector('header');
      if (header && header.nextSibling) {
          header.parentNode.insertBefore(messageContainer, header.nextSibling);
      } else {
          document.body.insertBefore(messageContainer, document.body.firstChild);
      }
  }
  
  // Exibir alertas durante 5 segundos e depois fechar
  const alerts = document.querySelectorAll('.alert');
  alerts.forEach(function(alert) {
      setTimeout(function() {
          const bsAlert = new bootstrap.Alert(alert);
          bsAlert.close();
      }, 5000);
  });
  
  // Atualizar a página a cada 5 minutos para mostrar medicamentos pendentes
  setInterval(function() {
      location.reload();
  }, 300000); // 5 minutos
  
  // Verificar e notificar medicamentos pendentes
  checkPendingMedications();
});

// Função para verificar medicamentos pendentes
function checkPendingMedications() {
  // Verificar se o navegador suporta notificações
  if (!("Notification" in window)) {
      console.log("Este navegador não suporta notificações.");
      return;
  }
  
  // Verificar se já temos permissão
  if (Notification.permission === "granted") {
      // Encontrar medicamentos pendentes na página
      const pendingItems = document.querySelectorAll('.badge.bg-danger');
      
      if (pendingItems.length > 0) {
          // Notificar sobre medicamentos pendentes
          new Notification("Medicamentos Pendentes", {
              body: `Você tem ${pendingItems.length} medicamento(s) pendente(s) para tomar.`,
              icon: "/favicon.ico"
          });
      }
  } else if (Notification.permission !== "denied") {
      // Solicitar permissão
      Notification.requestPermission().then(function(permission) {
          // Se o usuário aceitar, vamos mostrar uma notificação
          if (permission === "granted") {
              const pendingItems = document.querySelectorAll('.badge.bg-danger');
              
              if (pendingItems.length > 0) {
                  new Notification("Medicamentos Pendentes", {
                      body: `Você tem ${pendingItems.length} medicamento(s) pendente(s) para tomar.`,
                      icon: "/favicon.ico"
                  });
              }
          }
      });
  }
}

// Função para confirmar ações importantes
function confirmarDesativacao(id) {
  if (confirm('Tem certeza que deseja desativar este medicamento?')) {
      window.location.href = 'desativar_medicamento.php?id=' + id;
  }
}

// Ativar tooltips do Bootstrap (se necessário)
if (typeof bootstrap !== 'undefined') {
  const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
  tooltipTriggerList.map(function(tooltipTriggerEl) {
      return new bootstrap.Tooltip(tooltipTriggerEl);
  });
}