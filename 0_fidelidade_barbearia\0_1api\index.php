<?php
/**
 * API Principal - Sistema de Fidelidade da Barbearia
 * Ponto de entrada para todas as requisições da API
 */

// Configurações de erro
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Incluir arquivos necessários
require_once 'config/database.php';
require_once 'config/response.php';

// Configurar timezone
date_default_timezone_set('America/Sao_Paulo');

// Obter método HTTP e URI
$method = $_SERVER['REQUEST_METHOD'];
$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$uri = trim($uri, '/');

// Remover o caminho base se presente
$basePath = 'fidelidade_barbearia/api';
if (strpos($uri, $basePath) !== false) {
    $uri = substr($uri, strpos($uri, $basePath) + strlen($basePath));
    $uri = trim($uri, '/');
}

// Se URI está vazia, usar parâmetro path do .htaccess
if (empty($uri) && isset($_GET['path'])) {
    $uri = trim($_GET['path'], '/');
}

// Dividir URI em segmentos
$segments = explode('/', $uri);
$version = $segments[0] ?? 'v1';
$resource = $segments[1] ?? '';
$action = $segments[2] ?? '';
$id = $segments[3] ?? '';

// Debug para desenvolvimento (remover em produção)
if (isset($_GET['debug'])) {
    echo "URI Original: " . $_SERVER['REQUEST_URI'] . "\n";
    echo "URI Processada: $uri\n";
    echo "Segmentos: " . implode(' | ', $segments) . "\n";
    echo "Version: $version, Resource: $resource, Action: $action, ID: $id\n";
    exit;
}

// Verificar versão da API
if ($version !== 'v1') {
    ApiResponse::error('Versão da API não suportada', 400);
}

// Obter dados do corpo da requisição
$input = json_decode(file_get_contents('php://input'), true);
if (json_last_error() !== JSON_ERROR_NONE && !empty(file_get_contents('php://input'))) {
    ApiResponse::error('JSON inválido', 400);
}

// Conectar ao banco de dados
try {
    $database = new Database();
    $db = $database->getConnection();
} catch (Exception $e) {
    ApiResponse::error('Erro de conexão com o banco de dados');
}

// Roteamento da API
try {
    switch ($resource) {
        case 'auth':
            require_once 'endpoints/auth.php';
            handleAuth($method, $action, $input, $db);
            break;
            
        case 'users':
            require_once 'endpoints/users.php';
            handleUsers($method, $action, $id, $input, $db);
            break;
            
        case 'fidelity':
            require_once 'endpoints/fidelity.php';
            handleFidelity($method, $action, $id, $input, $db);
            break;
            
        case 'rewards':
            require_once 'endpoints/rewards.php';
            handleRewards($method, $action, $id, $input, $db);
            break;
            
        case 'reports':
            require_once 'endpoints/reports.php';
            handleReports($method, $action, $id, $input, $db);
            break;
            
        case 'master-password':
            require_once 'endpoints/master_password.php';
            handleMasterPassword($method, $action, $input, $db);
            break;
            
        case 'procedure':
            require_once 'endpoints/procedures.php';
            handleProcedure($method, $action, $input, $db, $database);
            break;
            
        case 'function':
            require_once 'endpoints/functions.php';
            handleFunction($method, $action, $input, $db, $database);
            break;
            
        case 'health':
            // Endpoint de saúde da API
            ApiResponse::success([
                'status' => 'OK',
                'version' => 'v1',
                'database' => 'Connected',
                'server_time' => date('Y-m-d H:i:s')
            ], 'API funcionando corretamente');
            break;
            
        default:
            ApiResponse::notFound('Endpoint não encontrado');
    }
    
} catch (Exception $e) {
    error_log("API Error: " . $e->getMessage());
    ApiResponse::error('Erro interno do servidor');
}

// Fechar conexão com o banco
$database->closeConnection();
?>
