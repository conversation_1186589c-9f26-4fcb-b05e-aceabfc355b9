<?php
// helpers.php

function checkAdmin($conexao, $usuario_id) {
    $query = "SELECT is_admin FROM appestudo.usuario WHERE idusuario = $1";
    $result = pg_query_params($conexao, $query, array($usuario_id));
    
    if (!$result) {
        return false;
    }
    
    $user = pg_fetch_assoc($result);
    
    // Converte explicitamente para booleano
    return $user && ($user['is_admin'] === 't' || $user['is_admin'] === true || $user['is_admin'] === '1' || $user['is_admin'] === 1);
}

function redirectIfNotAdmin($conexao, $usuario_id) {
    if (!checkAdmin($conexao, $usuario_id)) {
        header("Location: flashcards.php");
        exit();
    }
}
?>