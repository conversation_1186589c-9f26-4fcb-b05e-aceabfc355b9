<?php
// api/registros.php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

require_once '../includes/functions.php';
require_once '../includes/registro.php';
require_once '../includes/medicamento.php';

// Instanciar objeto
$registro = new Registro();
$medicamento = new Medicamento();

// Método HTTP
$method = $_SERVER['REQUEST_METHOD'];

// Obter ID da URL se fornecido
$id = null;
if (isset($_GET['id'])) {
    $id = intval($_GET['id']);
}

// Obter ID do medicamento da URL se fornecido
$medicamento_id = null;
if (isset($_GET['medicamento_id'])) {
    $medicamento_id = intval($_GET['medicamento_id']);
}

// Processar requisição com base no método HTTP
switch ($method) {
    case 'GET':
        // Listar registros de um medicamento ou pendentes
        if ($medicamento_id) {
            // Verificar se o medicamento existe
            if (!$medicamento->obter($medicamento_id)) {
                http_response_code(404);
                echo json_encode(['message' => 'Medicamento não encontrado']);
                break;
            }
            
            // Listar registros do medicamento
            $stmt = $registro->listarPorMedicamento($medicamento_id);
            $registros = [];
            
            while ($row = $stmt->fetch()) {
                $registros[] = [
                    'id' => $row['id'],
                    'medicamento_id' => $row['medicamento_id'],
                    'nome_medicamento' => $row['nome'],
                    'dosagem' => $row['dosagem'],
                    'data_hora' => $row['data_hora'],
                    'confirmado' => $row['confirmado'] ? true : false,
                    'observacao' => $row['observacao']
                ];
            }
            
            echo json_encode($registros);
        } else if (isset($_GET['pendentes']) && $_GET['pendentes'] == 1) {
            // Listar registros pendentes
            $stmt = $registro->listarPendentes();
            $registros = [];
            
            while ($row = $stmt->fetch()) {
                $registros[] = [
                    'id' => $row['id'],
                    'medicamento_id' => $row['medicamento_id'],
                    'nome_medicamento' => $row['nome'],
                    'dosagem' => $row['dosagem'],
                    'data_hora' => $row['data_hora'],
                    'confirmado' => $row['confirmado'] ? true : false,
                    'observacao' => $row['observacao']
                ];
            }
            
            echo json_encode($registros);
        } else if ($id) {
            // Obter registro específico
            $data = $registro->obter($id);
            
            if ($data) {
                echo json_encode([
                    'id' => $data['id'],
                    'medicamento_id' => $data['medicamento_id'],
                    'nome_medicamento' => $data['nome'],
                    'dosagem' => $data['dosagem'],
                    'data_hora' => $data['data_hora'],
                    'confirmado' => $data['confirmado'] ? true : false,
                    'observacao' => $data['observacao']
                ]);
            } else {
                http_response_code(404);
                echo json_encode(['message' => 'Registro não encontrado']);
            }
        } else {
            http_response_code(400);
            echo json_encode(['message' => 'Parâmetros inválidos. Use id, medicamento_id ou pendentes=1']);
        }
        break;
        
    case 'POST':
        // Adicionar novo registro (não é comum, mas útil para testes)
        $data = json_decode(file_get_contents('php://input'), true);
        
        if (!$data || !isset($data['medicamento_id']) || !isset($data['data_hora'])) {
            http_response_code(400);
            echo json_encode(['message' => 'Dados inválidos']);
            break;
        }
        
        // Verificar se o medicamento existe
        if (!$medicamento->obter($data['medicamento_id'])) {
            http_response_code(404);
            echo json_encode(['message' => 'Medicamento não encontrado']);
            break;
        }
        
        // Definir valores
        $registro->medicamento_id = $data['medicamento_id'];
        $registro->data_hora = $data['data_hora'];
        $registro->observacao = $data['observacao'] ?? null;
        
        // Adicionar registro
        if ($registro->adicionar()) {
            http_response_code(201);
            echo json_encode(['message' => 'Registro adicionado com sucesso']);
        } else {
            http_response_code(500);
            echo json_encode(['message' => 'Erro ao adicionar registro']);
        }
        break;
        
    case 'PUT':
        // Confirmar uso (atualizar registro)
        if (!$id) {
            http_response_code(400);
            echo json_encode(['message' => 'ID não fornecido']);
            break;
        }
        
        // Obter dados do registro
        if (!$registro->obter($id)) {
            http_response_code(404);
            echo json_encode(['message' => 'Registro não encontrado']);
            break;
        }
        
        // Verificar se já está confirmado
        if ($registro->confirmado) {
            echo json_encode(['message' => 'Registro já confirmado']);
            break;
        }
        
        $data = json_decode(file_get_contents('php://input'), true);
        
        // Definir observação se fornecida
        if ($data && isset($data['observacao'])) {
            $registro->observacao = $data['observacao'];
        }
        
        // Confirmar registro
        if ($registro->confirmar()) {
            echo json_encode(['message' => 'Uso confirmado com sucesso']);
        } else {
            http_response_code(500);
            echo json_encode(['message' => 'Erro ao confirmar uso']);
        }
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['message' => 'Método não permitido']);
        break;
}
?>