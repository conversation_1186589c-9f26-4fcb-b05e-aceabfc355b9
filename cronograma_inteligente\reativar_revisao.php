<?php
// reativar_revisao.php
session_start();
include_once("assets/config.php");

if (!isset($_SESSION['idusuario'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Usuário não autenticado']);
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);

    if (!isset($data['revisao_id'])) {
        echo json_encode(['success' => false, 'error' => 'ID da revisão não fornecido']);
        exit();
    }

    $revisao_id = $data['revisao_id'];
    $usuario_id = $_SESSION['idusuario'];

    pg_query($conexao, "BEGIN");

    try {
        // Busca informações da revisão
        $query_info = "
            SELECT conteudo_id, nivel_revisao 
            FROM appestudo.revisoes 
            WHERE id = $revisao_id AND usuario_id = $usuario_id";
        $result_info = pg_query($conexao, $query_info);
        $revisao_info = pg_fetch_assoc($result_info);

        if (!$revisao_info) {
            throw new Exception('Revisão não encontrada');
        }

        // Atualiza o status da revisão para pendente e agenda próxima revisão
        $query = "
            UPDATE appestudo.revisoes 
            SET status_revisao = 'pendente',
                proxima_revisao = CURRENT_TIMESTAMP
            WHERE id = $revisao_id 
            AND usuario_id = $usuario_id";

        // Atualiza o status do conteúdo
        $query_status = "
            UPDATE appestudo.usuario_conteudo
            SET status_revisao = 'Pendente'
            WHERE conteudo_id = $revisao_info[conteudo_id]
            AND usuario_id = $usuario_id";

        $result = pg_query($conexao, $query);
        $result_status = pg_query($conexao, $query_status);

        if (!$result || !$result_status) {
            throw new Exception('Erro ao reativar revisão');
        }

        pg_query($conexao, "COMMIT");
        echo json_encode(['success' => true, 'message' => 'Revisão reativada com sucesso']);

    } catch (Exception $e) {
        pg_query($conexao, "ROLLBACK");
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
} else {
    echo json_encode(['error' => 'Método não permitido']);
}
?>
