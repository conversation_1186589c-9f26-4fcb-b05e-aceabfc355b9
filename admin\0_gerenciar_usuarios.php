<?php
session_start();
require_once('../includes/auth.php');
require_once('../conexao_POST.php');
require_once('../cadastros/includes/verify_admin.php');

// Verifica se é o super admin
verificarSuperAdmin($conexao, false);

// Buscar todos os usuários com informações de estudo
$query = "
    SELECT 
        u.idusuario,
        u.nome AS nome_usuario,
        u.usuario,
        u.email,
        u.senha,
        u.is_admin,
        u.status,
        p.idplanejamento,
        CASE 
            WHEN p.idplanejamento IS NOT NULL THEN 'Sim'
            ELSE 'Não'
        END AS possui_planejamento,
        (
            SELECT e.data 
            FROM appestudo.estudos e 
            WHERE e.planejamento_usuario_idusuario = u.idusuario 
            ORDER BY e.data DESC 
            LIMIT 1
        ) AS data_ultimo_estudo,
        (
            SELECT ls.data_evento
            FROM appestudo.log_seguranca ls
            WHERE ls.usuario_id = u.idusuario
            AND ls.tipo_evento = 'login_sucesso'
            ORDER BY ls.data_evento DESC
            LIMIT 1
        ) AS ultimo_acesso
    FROM appestudo.usuario u
    LEFT JOIN appestudo.planejamento p ON u.idusuario = p.usuario_idusuario
    ORDER BY data_ultimo_estudo DESC NULLS LAST, u.idusuario ASC";

$result = pg_query($conexao, $query);
if (!$result) {
    error_log("Erro na query: " . pg_last_error($conexao));
    die("Erro ao buscar usuários");
}

// Formatar as datas
while ($row = pg_fetch_assoc($result)) {
    if ($row['data_ultimo_estudo']) {
        $date = new DateTime($row['data_ultimo_estudo']);
        $row['data_ultimo_estudo'] = $date->format('d-m-Y');
    } else {
        $row['data_ultimo_estudo'] = 'N/A';
    }
    
    if ($row['ultimo_acesso']) {
        $date = new DateTime($row['ultimo_acesso']);
        $row['ultimo_acesso'] = $date->format('d-m-Y H:i');
    } else {
        $row['ultimo_acesso'] = 'N/A';
    }
    
    $usuarios[] = $row;
}

// Contar total de usuários
$total_usuarios = count($usuarios);

// Processar ações
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['toggle_status'])) {
        $id = intval($_POST['user_id']);
        $status = $_POST['status'] === 'ativo' ? 'inativo' : 'ativo';
        
        $query = "UPDATE appEstudo.usuario SET status = $1 WHERE idusuario = $2";
        pg_query_params($conexao, $query, array($status, $id));
        
        header('Location: gerenciar_usuarios.php');
        exit();
    }
    
    // Nova ação para alternar admin
    if (isset($_POST['toggle_admin'])) {
        $id = intval($_POST['user_id']);
        $is_admin = $_POST['is_admin'] === 't' ? 'f' : 't';
        
        // Não permite remover o status de admin do próprio usuário
        if ($id == $_SESSION['idusuario'] && $is_admin === 'f') {
            $_SESSION['erro'] = "Você não pode remover seu próprio status de administrador.";
        } else {
            $query = "UPDATE appEstudo.usuario SET is_admin = $1 WHERE idusuario = $2";
            pg_query_params($conexao, $query, array($is_admin, $id));
        }
        
        header('Location: gerenciar_usuarios.php');
        exit();
    }
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Usuários</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
            --primary-color: #00008B;
            --hover-color: #0000CD;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --admin-color: #6610f2;
            --warning-color: #ffc107;
            --light-bg: #f5f5f5;
            --white: #ffffff;
            --border-color: #ddd;
            --shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        body {
            font-family: 'Quicksand', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: var(--light-bg);
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: var(--white);
            padding: 20px;
            border-radius: 12px;
            box-shadow: var(--shadow);
        }

        .page-header {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 20px;
        }

        .page-header h1 {
    color: var(--primary-color);
    margin: 0;
}

        .btn-voltar {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            background-color: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            transition: all 0.3s ease;
            font-size: 0.9em;
            font-weight: 500;
        }

        .btn-voltar:hover {
            background-color: var(--hover-color);
        }

        .btn-voltar i {
            margin-right: 8px;
        }

        .title-section {
            flex-grow: 1;
        }

        .total-users {
            color: var(--primary-color);
            font-size: 0.9em;
            font-weight: 500;
            opacity: 0.8;
        }

        .search-container {
            position: relative;
            width: 300px;
        }

        .search-input {
            width: 100%;
            padding: 8px 2px 8px 10px;
            border: 2px solid var(--border-color);
            border-radius: 6px;
            font-size: 0.9em;
            transition: all 0.3s ease;
        }

        .search-input:focus {
            border-color: var(--primary-color);
            outline: none;
        }

        .search-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        @media (max-width: 768px) {
            .page-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .search-container {
                width: 100%;
            }
        }

        .table-container {
            overflow-x: auto;
            margin: 20px 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9em;
            background: var(--white);
            border-radius: 8px;
            overflow: hidden;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        th {
            background-color: var(--primary-color);
            color: var(--white);
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.85em;
            letter-spacing: 0.5px;
        }

        tr:hover {
            background-color: rgba(0,0,0,0.02);
        }

        .sem-planejamento {
            background-color: rgba(255, 193, 7, 0.1);
        }

        .btn {
            display: inline-block;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            font-size: 0.9em;
            text-transform: uppercase;
            transition: all 0.3s ease;
            margin: 2px;
        }

        .btn-status {
            background-color: var(--primary-color);
            color: var(--white);
        }

        .btn-status:hover {
            background-color: var(--hover-color);
        }

        .btn-admin {
            background-color: var(--admin-color);
            color: var(--white);
        }

        .btn-admin:hover {
            opacity: 0.9;
        }

        .btn-remove-admin {
            background-color: var(--danger-color);
            color: var(--white);
        }

        .btn-remove-admin:hover {
            opacity: 0.9;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 600;
        }

        .status-active {
            background-color: var(--success-color);
            color: var(--white);
        }

        .status-inactive {
            background-color: var(--danger-color);
            color: var(--white);
        }

        .admin-badge {
            background-color: var(--admin-color);
            color: var(--white);
            padding: 4px 10px;
            border-radius: 15px;
            font-size: 0.85em;
            font-weight: 600;
        }

        .acoes-cell {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
        }

        .acoes-cell form {
            margin: 0;
        }

        @media (max-width: 1200px) {
            .container {
                padding: 10px;
            }

            table {
                font-size: 0.8em;
            }

            .btn {
                padding: 6px 12px;
                font-size: 0.8em;
            }
        }

        .highlight {
            background-color: rgba(255, 255, 0, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <a href="index.php" class="btn-voltar">
                <i class="fas fa-arrow-left"></i> Voltar
            </a>
            <div class="title-section">
                <h1><i class="fas fa-users-cog"></i> Gerenciamento de Usuários</h1>
                <span class="total-users">Total de usuários: <?php echo $total_usuarios; ?></span>
            </div>
            <div class="search-container">
                <input type="text" id="searchInput" placeholder="Pesquisar usuário..." class="search-input">
                <i class="fas fa-search search-icon"></i>
            </div>
        </div>

        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nome</th>
                        <th>Usuário</th>
                        <th>Senha</th>
                        <th>E-mail</th>
                        <th>ID Plan</th>
                        <th>Plan?</th>
                        <th>Últ. Estudo</th>
                        <th>Últ. Acesso</th>
                        <th>Admin</th>
                        <th>Status</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($usuarios as $usuario): ?>
                        <tr <?php if ($usuario['possui_planejamento'] == 'Não') echo 'class="sem-planejamento"'; ?>>
                            <td><?php echo htmlspecialchars($usuario['idusuario']); ?></td>
                            <td><?php echo htmlspecialchars($usuario['nome_usuario']); ?></td>
                            <td><?php echo htmlspecialchars($usuario['usuario']); ?></td>
                            <td><?php echo htmlspecialchars($usuario['senha']); ?></td>
                            <td><?php echo htmlspecialchars($usuario['email']); ?></td>
                            <td><?php echo htmlspecialchars($usuario['idplanejamento']); ?></td>
                            <td><?php echo htmlspecialchars($usuario['possui_planejamento']); ?></td>
                            <td><?php echo htmlspecialchars($usuario['data_ultimo_estudo']); ?></td>
                            <td><?php echo htmlspecialchars($usuario['ultimo_acesso']); ?></td>
                            <td>
                                <?php if ($usuario['is_admin'] == 't'): ?>
                                    <span class="admin-badge">Admin</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="status-badge <?php echo $usuario['status'] === 'ativo' ? 'status-active' : 'status-inactive'; ?>">
                                    <?php echo ucfirst($usuario['status']); ?>
                                </span>
                            </td>
                            <td class="acoes-cell">
                                <form method="post">
                                    <input type="hidden" name="user_id" value="<?php echo $usuario['idusuario']; ?>">
                                    <input type="hidden" name="status" value="<?php echo $usuario['status']; ?>">
                                    <button type="submit" name="toggle_status" class="btn btn-status">
                                        <?php echo $usuario['status'] === 'ativo' ? 'Desativar' : 'Ativar'; ?>
                                    </button>
                                </form>
                                
                                <form method="post">
                                    <input type="hidden" name="user_id" value="<?php echo $usuario['idusuario']; ?>">
                                    <input type="hidden" name="is_admin" value="<?php echo $usuario['is_admin']; ?>">
                                    <button type="submit" name="toggle_admin" class="btn <?php echo $usuario['is_admin'] == 't' ? 'btn-remove-admin' : 'btn-admin'; ?>">
                                        <?php echo $usuario['is_admin'] == 't' ? 'Remover Admin' : 'Tornar Admin'; ?>
                                    </button>
                                </form>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            const tableRows = document.querySelectorAll('tbody tr');

            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();

                tableRows.forEach(row => {
                    const nome = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                    const usuario = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
                    const email = row.querySelector('td:nth-child(5)').textContent.toLowerCase();

                    const matches = nome.includes(searchTerm) || 
                                  usuario.includes(searchTerm) || 
                                  email.includes(searchTerm);

                    row.style.display = matches ? '' : 'none';

                    // Remove highlights anteriores
                    row.querySelectorAll('.highlight').forEach(el => {
                        el.classList.remove('highlight');
                    });

                    // Adiciona highlight se houver termo de busca
                    if (searchTerm && matches) {
                        [nome, usuario, email].forEach((text, index) => {
                            const cell = row.querySelector(`td:nth-child(${index === 0 ? 2 : index === 1 ? 3 : 5})`);
                            if (text.includes(searchTerm)) {
                                const regex = new RegExp(`(${searchTerm})`, 'gi');
                                cell.innerHTML = cell.textContent.replace(
                                    regex, 
                                    '<span class="highlight">$1</span>'
                                );
                            }
                        });
                    }
                });
            });
        });
    </script>
</body>
</html>



















