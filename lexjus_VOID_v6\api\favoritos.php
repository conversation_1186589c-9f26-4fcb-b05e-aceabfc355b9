<?php
session_start();
require_once '../../conexao_POST.php';

// Verificar se o usuário está autenticado
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit;
}

$usuario_id = $_SESSION['idusuario'];
$metodo = $_SERVER['REQUEST_METHOD'];

// Obter dados do corpo da requisição
$dados = json_decode(file_get_contents('php://input'), true);

// REMOVIDO: função obterLeiAtualUsuario() - sistema multi-leis não usa mais preferência de lei padrão

// Função para obter ID da lei por código
function obterIdLeiPorCodigo($conexao, $codigo_lei) {
    $query = "SELECT id FROM appestudo.lexjus_leis WHERE codigo = $1";
    $result = pg_query_params($conexao, $query, [$codigo_lei]);

    if ($result && pg_num_rows($result) > 0) {
        $row = pg_fetch_assoc($result);
        return $row['id'];
    }

    return null;
}

switch ($metodo) {
    case 'GET':
        // Listar favoritos do usuário para a lei especificada ou atual
        $lei_codigo = $_GET['lei'] ?? '';

        if (!empty($lei_codigo)) {
            // Usar lei especificada na URL
            $lei_id = obterIdLeiPorCodigo($conexao, $lei_codigo);
        } else {
            // ERRO: Lei deve ser especificada na URL
            http_response_code(400);
            echo json_encode(['erro' => 'Parâmetro lei é obrigatório']);
            exit;
        }

        if (!$lei_id) {
            http_response_code(500);
            echo json_encode(['erro' => 'Lei atual não encontrada']);
            exit;
        }

        $query = "SELECT artigo_numero, data_adicionado
                 FROM appestudo.lexjus_favoritos
                 WHERE usuario_id = $1 AND lei_id = $2
                 ORDER BY data_adicionado DESC";

        $result = pg_query_params($conexao, $query, [$usuario_id, $lei_id]);

        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao consultar favoritos']);
            exit;
        }

        $favoritos = [];
        while ($row = pg_fetch_assoc($result)) {
            $favoritos[] = [
                'artigo_numero' => $row['artigo_numero'],
                'data_adicionado' => $row['data_adicionado']
            ];
        }

        echo json_encode(['favoritos' => $favoritos]);
        break;
        
    case 'POST':
        // Adicionar artigo aos favoritos
        if (!isset($dados['artigo_numero'])) {
            http_response_code(400);
            echo json_encode(['erro' => 'Número do artigo não informado']);
            exit;
        }

        // Obter lei do corpo da requisição ou usar lei atual
        $lei_codigo = $dados['lei_codigo'] ?? '';

        if (!empty($lei_codigo)) {
            // Usar lei especificada na requisição
            $lei_id = obterIdLeiPorCodigo($conexao, $lei_codigo);
        } else {
            // ERRO: Lei deve ser especificada
            http_response_code(400);
            echo json_encode(['erro' => 'Parâmetro lei_codigo é obrigatório']);
            exit;
        }

        if (!$lei_id) {
            http_response_code(500);
            echo json_encode(['erro' => 'Lei não encontrada']);
            exit;
        }

        $artigo_numero = $dados['artigo_numero'];

        $query = "INSERT INTO appestudo.lexjus_favoritos (usuario_id, lei_id, artigo_numero)
                 VALUES ($1, $2, $3)
                 ON CONFLICT (usuario_id, lei_id, artigo_numero) DO NOTHING";

        $result = pg_query_params($conexao, $query, [$usuario_id, $lei_id, $artigo_numero]);

        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao adicionar favorito']);
            exit;
        }

        echo json_encode(['sucesso' => true, 'mensagem' => 'Artigo adicionado aos favoritos']);
        break;
        
    case 'DELETE':
        // Remover artigo dos favoritos
        if (!isset($dados['artigo_numero'])) {
            http_response_code(400);
            echo json_encode(['erro' => 'Número do artigo não informado']);
            exit;
        }

        // Obter lei do corpo da requisição ou usar lei atual
        $lei_codigo = $dados['lei_codigo'] ?? '';

        if (!empty($lei_codigo)) {
            // Usar lei especificada na requisição
            $lei_id = obterIdLeiPorCodigo($conexao, $lei_codigo);
        } else {
            // ERRO: Lei deve ser especificada
            http_response_code(400);
            echo json_encode(['erro' => 'Parâmetro lei_codigo é obrigatório']);
            exit;
        }

        if (!$lei_id) {
            http_response_code(500);
            echo json_encode(['erro' => 'Lei não encontrada']);
            exit;
        }

        $artigo_numero = $dados['artigo_numero'];

        $query = "DELETE FROM appestudo.lexjus_favoritos
                 WHERE usuario_id = $1 AND lei_id = $2 AND artigo_numero = $3";

        $result = pg_query_params($conexao, $query, [$usuario_id, $lei_id, $artigo_numero]);

        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao remover favorito']);
            exit;
        }

        echo json_encode(['sucesso' => true, 'mensagem' => 'Artigo removido dos favoritos']);
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['erro' => 'Método não permitido']);
}
?>