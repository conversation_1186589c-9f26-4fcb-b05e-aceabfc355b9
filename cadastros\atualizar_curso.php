<?php
session_start();
require_once("assets/config.php");
require_once("includes/verify_admin.php");

header('Content-Type: application/json');

try {
    // CORREÇÃO: Trocando verificarSessaoAdmin por verificarAcessoAdmin
    // já que esta página deve ser acessível a qualquer admin
    verificarAcessoAdmin($conexao, true);

    $cursoId = filter_input(INPUT_POST, 'cursoId', FILTER_VALIDATE_INT);
    if ($cursoId === null || $cursoId === false) {
        $cursoId = filter_input(INPUT_POST, 'idcurso', FILTER_VALIDATE_INT);
    }

    $nome = filter_input(INPUT_POST, 'nome', FILTER_SANITIZE_STRING);

    if ($cursoId === false || $cursoId === null) {
        throw new Exception('ID do curso inválido ou não fornecido');
    }

    if (empty($nome)) {
        throw new Exception('Nome do curso não pode estar vazio');
    }

    // Atualiza o curso no banco de dados
    $query = "UPDATE appEstudo.curso SET nome = $1";
    $params = [$nome];

    if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = 'img/cursos/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        $fileName = uniqid() . '_' . basename($_FILES['logo']['name']);
        $uploadFile = $uploadDir . $fileName;

        if (move_uploaded_file($_FILES['logo']['tmp_name'], $uploadFile)) {
            $logo_url = 'img/cursos/' . $fileName;
            $query .= ", logo_url = $" . (count($params) + 1);
            $params[] = $logo_url;
        }
    }

    $query .= " WHERE idcurso = $" . (count($params) + 1);
    $params[] = $cursoId;

    $resultado = pg_query_params($conexao, $query, $params);

    if (!$resultado) {
        throw new Exception('Erro ao atualizar o curso no banco de dados: ' . pg_last_error($conexao));
    }

    echo json_encode([
        'success' => true,
        'message' => [
            'titulo' => 'Sucesso!',
            'conteudo' => '
                <div class="modal-success-content">
                    <div class="success-icon">
                        <i class="fas fa-check-circle fa-3x"></i>
                    </div>
                    <div class="success-message">
                        <h4>Curso atualizado com sucesso!</h4>
                        <p>Todas as alterações foram salvas corretamente.</p>
                    </div>
                </div>
            '
        ]
    ]);

} catch (Exception $e) {
    error_log("Erro na atualização do curso: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => [
            'titulo' => 'Erro',
            'conteudo' => '
                <div class="modal-error-content">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-circle fa-3x"></i>
                    </div>
                    <div class="error-message">
                        <h4>Não foi possível atualizar o curso</h4>
                        <p>Por favor, tente novamente.</p>
                    </div>
                </div>
            '
        ]
    ]);
}
?>




