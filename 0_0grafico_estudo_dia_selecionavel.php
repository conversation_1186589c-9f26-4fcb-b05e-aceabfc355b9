<?php
//0grafico_estudo_dia_selecionavel.php
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

date_default_timezone_set('America/Sao_Paulo');

// Desabilitar a exibição de erros no output
ini_set('display_errors', 0);
error_reporting(0);

// Garantir que nenhum output seja enviado antes do header
ob_start();

// Definir o tipo de conteúdo como JSON
header('Content-Type: application/json');

// Limpar qualquer output anterior
ob_clean();

require_once("conexao_POST.php");

// Verifica se o usuário está logado
if (!isset($_SESSION['idusuario'])) {
    echo json_encode(['error' => 'session_expired']);
    exit;
}

$id_usuario = $_SESSION['idusuario'];

// Se for uma requisição AJAX para dados
if (isset($_GET['data']) && !empty($_GET['data'])) {
    try {
        $data_selecionada = $_GET['data'];
        
        // Validar formato da data
        if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $data_selecionada)) {
            throw new Exception('Formato de data inválido');
        }

        // Verificar se a data não é futura
        $data_atual = new DateTime('now', new DateTimeZone('America/Sao_Paulo'));
        $data_atual->setTime(0, 0, 0); // Zera as horas, minutos e segundos
        $ano_atual = $data_atual->format('Y');
        
        $data_selecionada_obj = new DateTime($data_selecionada, new DateTimeZone('America/Sao_Paulo'));
        $data_selecionada_obj->setTime(0, 0, 0); // Zera as horas, minutos e segundos
        $ano_selecionado = $data_selecionada_obj->format('Y');

        // Se o ano selecionado for maior que o atual, ajusta para o ano atual
        if ($ano_selecionado > $ano_atual) {
            $data_selecionada = $data_atual->format('Y') . substr($data_selecionada, 4);
            $data_selecionada_obj = new DateTime($data_selecionada);
            $data_selecionada_obj->setTime(0, 0, 0);
        }

        // Comparação adicional para verificar se a data é futura
        if ($data_selecionada_obj > $data_atual) {
            throw new Exception('Não é possível selecionar datas futuras');
        }

        // Extrair ano e mês da data selecionada
        $ano_mes = substr($data_selecionada, 0, 7);
        $primeiro_dia_mes = $ano_mes . '-01';
        $ultimo_dia_mes = date('Y-m-t', strtotime($data_selecionada));

        // Query para dados diários
        $query_diaria = "
            SELECT 
                e.tempo_liquido,
                e.ponto_estudado,
                m.nome AS nome_materia,
                m.cor AS cor_materia
            FROM appEstudo.estudos e
            LEFT JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria
            WHERE e.planejamento_usuario_idusuario = $id_usuario 
            AND DATE(e.data) = '$data_selecionada'
            ORDER BY e.hora_inicio";

        // Nova query para dados mensais
        $query_mensal = "
            WITH dias_estudados AS (
                SELECT 
                    m.nome AS nome_materia,
                    m.cor AS cor_materia,
                    COUNT(DISTINCT DATE(e.data)) as dias_estudados,
                    SUM(EXTRACT(EPOCH FROM e.tempo_liquido)) as tempo_total_segundos
                FROM appEstudo.estudos e
                LEFT JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria
                WHERE e.planejamento_usuario_idusuario = $id_usuario 
                AND DATE(e.data) BETWEEN '$primeiro_dia_mes' AND '$ultimo_dia_mes'
                GROUP BY m.nome, m.cor
            ),
            total_dias AS (
                SELECT COUNT(DISTINCT DATE(data)) as total_dias_estudados
                FROM appEstudo.estudos
                WHERE planejamento_usuario_idusuario = $id_usuario 
                AND DATE(data) BETWEEN '$primeiro_dia_mes' AND '$ultimo_dia_mes'
            )
            SELECT 
                d.*,
                t.total_dias_estudados
            FROM dias_estudados d
            CROSS JOIN total_dias t
            ORDER BY d.tempo_total_segundos DESC";

        // Executar queries
        $resultado_diario = pg_query($conexao, $query_diaria);
        $resultado_mensal = pg_query($conexao, $query_mensal);

        if (!$resultado_diario || !$resultado_mensal) {
            throw new Exception(pg_last_error());
        }

        // Processar dados diários
        $pontos_estudo_por_materia = array();
        $cores_materias = array();
        $tempo_total_por_materia = array();
        $houve_estudo = false;

        while ($registro = pg_fetch_assoc($resultado_diario)) {
            $houve_estudo = true;
            $materia = $registro['nome_materia'];
            $ponto_estudado = $registro['ponto_estudado'];
            $tempo_estudo = $registro['tempo_liquido'];
            
            $partes_tempo = explode(':', $tempo_estudo);
            $tempo_estudo_segundos = ($partes_tempo[0] * 3600) + ($partes_tempo[1] * 60) + $partes_tempo[2];

            if (!isset($pontos_estudo_por_materia[$materia])) {
                $pontos_estudo_por_materia[$materia] = array();
                $cores_materias[$materia] = $registro['cor_materia'];
            }

            if (!isset($pontos_estudo_por_materia[$materia][$ponto_estudado])) {
                $pontos_estudo_por_materia[$materia][$ponto_estudado] = 0;
            }
            $pontos_estudo_por_materia[$materia][$ponto_estudado] += $tempo_estudo_segundos;

            if (!isset($tempo_total_por_materia[$materia])) {
                $tempo_total_por_materia[$materia] = 0;
            }
            $tempo_total_por_materia[$materia] += $tempo_estudo_segundos;
        }

        // Processar dados mensais
        $dados_mensais = array();
        $total_tempo_mes = 0;
        $total_dias_estudados = 0;
        $dias_uteis_mes = date('t', strtotime($data_selecionada));

        while ($registro = pg_fetch_assoc($resultado_mensal)) {
            $materia = $registro['nome_materia'];
            $tempo_total = floatval($registro['tempo_total_segundos']);
            $dias_estudados = intval($registro['dias_estudados']);
            $total_dias_estudados = intval($registro['total_dias_estudados']); // Novo campo
            
            $dados_mensais[$materia] = array(
                'tempo_total' => $tempo_total,
                'dias_estudados' => $dias_estudados,
                'cor' => $registro['cor_materia']
            );
            
            $total_tempo_mes += $tempo_total;
        }

        // Buscar tempo planejamento
        $query_planejamento = "SELECT tempo_planejamento FROM appEstudo.planejamento WHERE usuario_idusuario = $id_usuario";
        $result_planejamento = pg_query($conexao, $query_planejamento);
        $tempoPlanejamento = 14400; // valor padrão (4 horas)
        
        if ($result_planejamento && $row = pg_fetch_assoc($result_planejamento)) {
            $tempo_parts = explode(':', $row['tempo_planejamento']);
            $tempoPlanejamento = ($tempo_parts[0] * 3600) + ($tempo_parts[1] * 60);
        }

        // No final, antes de enviar a resposta JSON
        ob_end_clean(); // Limpa qualquer output não desejado
        echo json_encode([
            'success' => true,
            'dados_diarios' => [
                'dados' => $pontos_estudo_por_materia,
                'cores' => $cores_materias,
                'tempo_total' => $tempo_total_por_materia,
                'houve_estudo' => $houve_estudo
            ],
            'dados_mensais' => [
                'dados' => $dados_mensais,
                'total_tempo' => $total_tempo_mes,
                'dias_estudados' => $total_dias_estudados, // Agora com o valor correto
                'dias_uteis' => $dias_uteis_mes,
                'mes' => date('m/Y', strtotime($data_selecionada))
            ],
            'data' => $data_selecionada,
            'tempo_planejamento' => $tempoPlanejamento
        ], JSON_THROW_ON_ERROR);
        exit;

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'error' => $e->getMessage()
        ]);
    }
    exit;
}

// Se não for AJAX, continua com o HTML normal...
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title>Análise de Tempo de Estudo</title>
    <!-- Adicione os scripts necessários -->
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/pt.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    
    <style>
/* Melhorias nos estilos dos gráficos e cards */
.date-selector-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    margin: 20px 0;
}

.date-input {
    text-align: center;
    padding: 10px 20px;
    border: 1px solid var(--border);
    border-radius: 8px;
    font-family: 'Quicksand', sans-serif;
    font-size: 1rem;
    width: 200px;
    background-color: white;
}

.navigation-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.nav-button {
    background-color: var(--primary);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-family: 'Quicksand', sans-serif;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.nav-button:hover {
    opacity: 0.9;
    transform: translateY(-2px);
}

.nav-button.hoje {
    background-color: var(--primary);
    font-weight: bold;
}

.graficos-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    padding: 25px;
    margin-bottom: 20px;
}

.grafico-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
   /* box-shadow: 0 4px 12px rgba(0,0,0,0.08); */
    transition: all 0.3s ease;
}

.grafico-card:hover {
    box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

.grafico-card h3 {
    font-family: 'Quicksand', sans-serif;
    font-weight: 600;
    color: #333;
    margin-top: 0;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #f0f0f0;
}

/* Melhorias para os cards de estatísticas */
.resumo-mensal {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 15px;
    margin-top: 30px;
}

.stat-card {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    border-top: 3px solid transparent;
}

/* Cores diferentes para cada tipo de estatística */
.stat-card:nth-child(1) {
    border-top-color: var(--primary);
}

.stat-card:nth-child(2) {
   border-top-color: var(--primary);
}

.stat-card:nth-child(3) {
    border-top-color: var(--primary);
}

.stat-card:nth-child(4) {
    border-top-color: var(--primary);
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
}

.stat-value {
    font-size: 28px;
    font-weight: bold;
    color: #333;
    margin-bottom: 8px;
    transition: all 0.2s ease;
    
}

.stat-label {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
}

/* Melhorias para responsividade */
@media (max-width: 768px) {
    .graficos-container {
        grid-template-columns: 1fr;
        padding: 15px;
    }
    
    .grafico-card {
        padding: 20px;
    }
    
    .resumo-mensal {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stat-value {
        font-size: 24px;
    }
}

/* Animação suave ao carregar elementos */
.resumo-mensal .stat-card {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(20px);
}

.resumo-mensal .stat-card:nth-child(1) { animation-delay: 0.1s; }
.resumo-mensal .stat-card:nth-child(2) { animation-delay: 0.2s; }
.resumo-mensal .stat-card:nth-child(3) { animation-delay: 0.3s; }
.resumo-mensal .stat-card:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
    </style>
</head>
<body>
<div class="date-selector-container">
    <input type="text" id="datePickerSelecionavel" class="date-input">
    <div class="navigation-buttons">
        <button class="nav-button" onclick="navegarDataSelecionavel(-1)">← Anterior</button>
        <button class="nav-button hoje" onclick="navegarDataSelecionavel(0)">Hoje</button>
        <button class="nav-button" onclick="navegarDataSelecionavel(1)">Próximo →</button>
    </div>
</div>
    <div class="graficos-container">
        <div class="grafico-card">
            <h3>Visão Diária</h3>
            <div id="graficoEstudoDiaSelecionavel"></div>
            <div class="resumo-diario">
                <div class="stat-card">
                    <div class="stat-value" id="totalHorasDia">0h</div>
                    <div class="stat-label">Total de Horas Estudadas no Dia</div>
                </div>
            </div>
        </div>
        
        <div class="grafico-card">
            <h3>Visão Mensal</h3>
            <div id="graficoEstudoMensal"></div>
            <div class="resumo-mensal">
                <div class="stat-card">
                    <div class="stat-value" id="totalHorasMes">0h</div>
                    <div class="stat-label">Total de Horas</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="diasEstudados">0/0</div>
                    <div class="stat-label">Dias Estudados</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="mediaHorasDia">0h</div>
                    <div class="stat-label">Média por Dia</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Inicialização do Flatpickr e funções de navegação
        let datePickerSelecionavel;

        document.addEventListener('DOMContentLoaded', function() {
            const hoje = new Date();
            hoje.setHours(0, 0, 0, 0); // Zera as horas
            const anoAtual = hoje.getFullYear();

            datePickerSelecionavel = flatpickr("#datePickerSelecionavel", {
                locale: "pt",
                dateFormat: "d-m-Y",
                defaultDate: hoje,
                maxDate: hoje,
                onChange: function(selectedDates) {
                    if (selectedDates.length > 0) {
                        let dataSelecionada = selectedDates[0];
                        dataSelecionada.setHours(0, 0, 0, 0); // Zera as horas
                        
                        // Força o ano atual se um ano futuro for selecionado
                        if (dataSelecionada.getFullYear() > anoAtual) {
                            dataSelecionada.setFullYear(anoAtual);
                            this.setDate(dataSelecionada);
                        }
                        
                        // Verifica se a data é futura
                        if (dataSelecionada > hoje) {
                            dataSelecionada = hoje;
                            this.setDate(hoje);
                        }
                        
                        carregarDadosEstudoSelecionavel(dataSelecionada);
                    }
                }
            });

            // Carregar dados iniciais com a data de hoje
            carregarDadosEstudoSelecionavel(hoje);
        });

        function navegarDataSelecionavel(direcao) {
            if (!datePickerSelecionavel) return;
            
            let novaData = new Date(datePickerSelecionavel.selectedDates[0]);
            
            if (direcao === 0) {
                novaData = new Date();
            } else {
                novaData.setDate(novaData.getDate() + direcao);
            }
            
            datePickerSelecionavel.setDate(novaData);
            carregarDadosEstudoSelecionavel(novaData); // Adicionar esta linha
        }

        function formatarDataSelecionavel(data) {
            // Criar uma nova data usando a data local
            const dataLocal = new Date(data);
            // Formatar para YYYY-MM-DD
            return dataLocal.getFullYear() + '-' + 
                   String(dataLocal.getMonth() + 1).padStart(2, '0') + '-' + 
                   String(dataLocal.getDate()).padStart(2, '0');
        }

        function carregarDadosEstudoSelecionavel(data) {
            const chartContainer = document.getElementById('graficoEstudoDiaSelecionavel');
            const mensalContainer = document.getElementById('graficoEstudoMensal');
            
            // Mostrar loading
            chartContainer.innerHTML = '<div class="loading-spinner">Carregando dados...</div>';
            mensalContainer.innerHTML = '<div class="loading-spinner">Carregando dados...</div>';

            // Garantir que estamos usando o ano atual
            const hoje = new Date();
            const anoAtual = hoje.getFullYear();
            if (data.getFullYear() > anoAtual) {
                data.setFullYear(anoAtual);
            }

            // Formatar a data para YYYY-MM-DD
            const dataFormatada = data.toISOString().split('T')[0];

            // Fazer a requisição AJAX
            fetch(`0grafico_estudo_dia_selecionavel.php?data=${dataFormatada}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text().then(text => {
                        try {
                            return JSON.parse(text);
                        } catch (e) {
                            console.error('Resposta recebida:', text);
                            throw new Error('Erro ao processar resposta do servidor');
                        }
                    });
                })
                .then(data => {
                    console.log('Dados recebidos:', data); // Debug
                    
                    if (!data.success) {
                        throw new Error(data.error || 'Erro desconhecido');
                    }

                    // Criar gráfico diário
                    if (!data.dados_diarios.houve_estudo) {
                        mostrarMensagemSemEstudoSelecionavel(dataFormatada);
                    } else {
                        criarGraficoSelecionavel(data.dados_diarios);
                    }

                    // Criar gráfico mensal
                    if (data.dados_mensais) {
                        criarGraficoMensal(data.dados_mensais);
                    }
                })
                .catch(error => {
                    console.error('Erro ao carregar dados:', error);
                    const errorMessage = `
                        <div class="error-message">
                            <i class="fas fa-exclamation-circle"></i>
                            Erro ao carregar os dados: ${error.message}
                        </div>`;
                    chartContainer.innerHTML = errorMessage;
                    mensalContainer.innerHTML = errorMessage;
                });
        }

        function criarGraficoSelecionavel(data) {
            const container = document.getElementById('graficoEstudoDiaSelecionavel');
            if (!container) return;

            // Calcular tempo total para percentuais
            const tempoTotalDia = Object.values(data.tempo_total).reduce((a, b) => a + b, 0);
            
            // Mostrar o resumo diário apenas quando houver estudo
            const resumoDiario = document.querySelector('.resumo-diario');
            resumoDiario.style.display = 'block';
            
            // Atualizar o stat-card com o tempo total
            document.getElementById('totalHorasDia').innerHTML = formatarTempoParaExibicao(tempoTotalDia);

            // Preparar dados para o gráfico
            const seriesData = [];
            for (let materia in data.dados) {
                const totalTempoEstudo = data.tempo_total[materia];
                const horas = Math.floor(totalTempoEstudo / 3600);
                const minutos = Math.floor((totalTempoEstudo % 3600) / 60);
                const formattedTime = horas + 'h ' + minutos + 'm';
                const percentual = ((totalTempoEstudo / tempoTotalDia) * 100).toFixed(1);

                seriesData.push({
                    name: materia,
                    y: totalTempoEstudo,
                    formattedTime: formattedTime,
                    percentage: percentual,
                    color: data.cores[materia]
                });
            }

            // Ordenar por tempo total (decrescente)
            seriesData.sort((a, b) => b.y - a.y);

            // Criar o gráfico
            Highcharts.chart(container, {
                chart: {
                    type: 'bar',
                    backgroundColor: 'transparent'
                },
                title: {
                    text: null
                },
                xAxis: {
                    type: 'category',
                    labels: {
                        style: {
                            fontSize: '13px',
                            fontFamily: 'Verdana, sans-serif'
                        }
                    }
                },
                yAxis: {
                    title: {
                        text: 'Tempo de Estudo'
                    },
                    labels: {
                        formatter: function() {
                            return formatarTempoParaExibicao(this.value);
                        }
                    }
                },
                legend: {
                    enabled: false
                },
                plotOptions: {
                    bar: {
                        borderRadius: 5,
                        dataLabels: {
                            enabled: true,
                            formatter: function() {
                                return `${this.point.formattedTime} (${this.point.percentage}%)`;
                            },
                            style: {
                                fontSize: '12px'
                            }
                        }
                    }
                },
                tooltip: {
                    formatter: function() {
                        return '<b>' + this.point.name + '</b><br/>' +
                               'Tempo total: ' + this.point.formattedTime + '<br/>' +
                               'Percentual: ' + this.point.percentage + '%';
                    }
                },
                series: [{
                    name: 'Tempo de Estudo',
                    data: seriesData,
                    colorByPoint: true
                }],
                credits: {
                    enabled: false
                }
            });
        }

        function mostrarMensagemSemEstudoSelecionavel(data) {
            const [ano, mes, dia] = data.split('-');
            const dataFormatada = `${dia}/${mes}/${ano}`;
            
            const container = document.getElementById('graficoEstudoDiaSelecionavel');
            // Esconder o resumo diário quando não houver estudo
            const resumoDiario = document.querySelector('.resumo-diario');
            resumoDiario.style.display = 'none';
            
            container.innerHTML = `
                <div class="sem-estudo-message">
                    <h3>Nenhum estudo registrado para ${dataFormatada}</h3>
                    <p>Selecione outra data ou registre um novo estudo.</p>
                </div>`;
        }

        function formatarDataParaExibicao(data) {
            const [ano, mes, dia] = data.split('-');
            return `${dia}/${mes}/${ano}`;
        }

        function formatarTempo(segundos) {
            const horas = Math.floor(segundos / 3600);
            const minutos = Math.floor((segundos % 3600) / 60);
            return `${horas}h${minutos > 0 ? ` ${minutos}m` : ''}`;
        }

// Função para criar o gráfico mensal melhorado
function criarGraficoMensal(dadosMensais) {
    if (!dadosMensais || !dadosMensais.dados) {
        console.error('Dados mensais inválidos:', dadosMensais);
        return;
    }

    const container = document.getElementById('graficoEstudoMensal');
    if (!container) return;

    // Atualizar estatísticas
    atualizarEstatisticasMensais(dadosMensais);

    // Preparar dados para o gráfico
    const seriesData = Object.entries(dadosMensais.dados).map(([materia, info]) => ({
        name: materia,
        y: info.tempo_total,
        color: info.cor,
        formattedTime: formatarTempoParaExibicao(info.tempo_total),
        diasEstudados: info.dias_estudados
    }));

    // Ordenar por tempo total (decrescente)
    seriesData.sort((a, b) => b.y - a.y);

    // Criar o gráfico
    Highcharts.chart('graficoEstudoMensal', {
        chart: {
            type: 'pie',
            backgroundColor: 'transparent',
            style: {
                fontFamily: 'Quicksand, sans-serif'
            },
            events: {
                // Adicionar animação suave ao carregar
                load: function() {
                    this.series[0].animate({
                        duration: 800
                    });
                }
            }
        },
        title: {
            text: `Distribuição do Tempo - ${dadosMensais.mes}`,
            style: {
                fontSize: '18px',
                fontWeight: '600'
            }
        },
        subtitle: {
            text: `Total: ${formatarTempoParaExibicao(dadosMensais.total_tempo)}`,
            style: {
                color: '#666'
            }
        },
        credits: {
            enabled: false
        },
        tooltip: {
            useHTML: true,
            headerFormat: '<span style="font-size: 14px">{point.key}</span><table>',
            pointFormat: '<tr><td style="color: {point.color}; padding: 0">Tempo: </td>' +
                         '<td style="padding: 0"><b>{point.formattedTime}</b></td></tr>' +
                         '<tr><td style="color: {point.color}; padding: 0">Registro de Estudo: </td>' +
                         '<td style="padding: 0"><b>{point.diasEstudados}</b></td></tr>' +
                         '<tr><td style="color: {point.color}; padding: 0">Porcentagem: </td>' +
                         '<td style="padding: 0"><b>{point.percentage:.1f}%</b></td></tr>',
            footerFormat: '</table>',
            followPointer: true,
            style: {
                fontFamily: 'Quicksand, sans-serif'
            }
        },
        plotOptions: {
            pie: {
                allowPointSelect: true,
                cursor: 'pointer',
                depth: 35,
                borderRadius: 5,
                borderWidth: 1,
                borderColor: '#ffffff',
                dataLabels: {
                    enabled: false, // Desabilita os rótulos com as linhas
                    distance: 20,
                    style: {
                        fontFamily: 'Quicksand, sans-serif',
                        fontSize: '0.9rem',
                        fontWeight: 'normal',
                        color: '#333333',
                        textOutline: 'none'
                    }
                },
                states: {
                    hover: {
                        brightness: 0.1
                    }
                },
                showInLegend: true // Garante que a legenda seja mostrada
            }
        },
        legend: {
            enabled: true,
            layout: 'vertical',
            align: 'right',
            verticalAlign: 'middle',
            itemMarginTop: 5,
            itemMarginBottom: 5,
            itemStyle: {
                fontFamily: 'Quicksand, sans-serif',
                fontWeight: 'normal'
            },
            labelFormatter: function() {
                return `${this.name}: ${formatarTempoParaExibicao(this.y)}`;
            }
        },
        series: [{
            name: 'Tempo Total',
            colorByPoint: true,
            innerSize: '50%',
            data: seriesData,
            animation: {
                duration: 1000
            }
        }],
        responsive: {
            rules: [{
                condition: {
                    maxWidth: 500
                },
                chartOptions: {
                    legend: {
                        align: 'center',
                        verticalAlign: 'bottom',
                        layout: 'horizontal'
                    }
                }
            }]
        }
    });
}

// Função para atualizar os cards de estatísticas mensais com design melhorado
function atualizarEstatisticasMensais(dadosMensais) {
    // Formatar tempo total
    const tempoTotal = formatarTempoParaExibicao(dadosMensais.total_tempo);
    document.getElementById('totalHorasMes').innerHTML = `<span>${tempoTotal}</span>`;
    
    // Calcular porcentagem de dias estudados
    const porcentagemDias = Math.round((dadosMensais.dias_estudados / dadosMensais.dias_uteis) * 100);
    document.getElementById('diasEstudados').innerHTML = 
        `<span>${dadosMensais.dias_estudados}/${dadosMensais.dias_uteis}</span>` +
        `<small style="font-size: 14px; color: #777"> (${porcentagemDias}%)</small>`;
    
    // Calcular média por dia
    const mediaTempoSegundos = dadosMensais.total_tempo / dadosMensais.dias_uteis;
    const mediaDiasEstudados = dadosMensais.dias_estudados > 0 ? 
        dadosMensais.total_tempo / dadosMensais.dias_estudados : 0;
    
    document.getElementById('mediaHorasDia').innerHTML = 
        `<span>${formatarTempoParaExibicao(mediaTempoSegundos)}</span>`;
    
    // Adicionar média por dia estudado (novo elemento)
    if (!document.getElementById('mediaDiasEstudados')) {
        const novoCard = document.createElement('div');
        novoCard.className = 'stat-card';
        novoCard.innerHTML = `
            <div class="stat-value" id="mediaDiasEstudados">0h</div>
            <div class="stat-label">Média por Dia Estudado</div>
        `;
        document.querySelector('.resumo-mensal').appendChild(novoCard);
    }
    
    document.getElementById('mediaDiasEstudados').innerHTML = 
        `<span>${formatarTempoParaExibicao(mediaDiasEstudados)}</span>`;
}

// Função melhorada para formatar tempo
function formatarTempoParaExibicao(segundos) {
    if (!segundos || segundos < 60) return '0h';
    
    const horas = Math.floor(segundos / 3600);
    const minutos = Math.floor((segundos % 3600) / 60);
    
    if (horas === 0) {
        return `${minutos}min`;
    }
    
    // Se os minutos forem menos de 10, adicionar um zero à frente
    const minutosFormatados = minutos < 10 ? `0${minutos}` : minutos;
    
    return minutos > 0 ? `${horas}h${minutosFormatados}min` : `${horas}h`;
}
    </script>
</body>
</html>











