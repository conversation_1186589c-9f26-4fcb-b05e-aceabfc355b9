
function carregaCF() {
    // URL de nosso proxy local
    var proxyBaseUrl = window.location.origin + '/dispositivo/';

    // Seleciona todos os elementos com a classe 'cstf'
    var elementos = document.getElementsByClassName('cstf');

    // Processa cada elemento
    for (var i = 0; i < elementos.length; i++) {
        var elemento = elementos[i];
        var artigo = elemento.getAttribute('name');

        if (artigo) {
            // Constrói a URL para o proxy local (URL amigável)
            var urlProxy = proxyBaseUrl + encodeURIComponent(artigo);

            // Configura o elemento
            elemento.setAttribute('href', urlProxy);
            elemento.setAttribute('target', '_blank');
            elemento.setAttribute('alt', 'Visualizar dispositivo');
            elemento.setAttribute('title', 'Visualizar dispositivo');
            elemento.innerHTML = '<i class="fa fa-balance-scale"></i>';
        }
    }

    // Adiciona estilos para anotações
    adicionarEstilosAnotacoes();

    // Adiciona botão para listar anotações
    adicionarBotaoListarAnotacoes();

    // Carrega marcações e anotações salvas
    carregarMarcacoes();
    carregarAnotacoes();

    // Configura eventos para dispositivos desktop e móveis
    // Para desktop
    document.addEventListener('mouseup', handleSelectionEnd);
    document.addEventListener('mousedown', handleTapOutside);

    // Para dispositivos móveis
    document.addEventListener('touchend', handleSelectionEnd);
    document.addEventListener('touchstart', handleTapOutside);
}

/* Função para lidar com o final de uma seleção (tanto mouse quanto toque)
function handleSelectionEnd(e) {
    // Pequeno timeout para permitir que a seleção seja processada
    setTimeout(function() {
        mostrarBotaoMarcacao(e);
    }, 200);
}   */

/* Função para lidar com cliques/toques fora da seleção
function handleTapOutside(e) {
    var botaoMarcacao = document.getElementById('botao-marcacao');
    if (!botaoMarcacao) return;

    // Verifica se o clique foi fora do botão de marcação
    var targetElement = e.target;
    while (targetElement != null) {
        if (targetElement === botaoMarcacao) {
            return; // O clique foi no botão, não faz nada
        }
        targetElement = targetElement.parentElement;
    }

    // O clique foi fora, remove o botão
    document.body.removeChild(botaoMarcacao);
} */

/* Função para mostrar o botão de marcação quando o texto é selecionado
function mostrarBotaoMarcacao(e) {
    const selecao = window.getSelection();
    const botaoExistente = document.getElementById('botao-marcacao');

    // Remove o botão se não houver texto selecionado
    if (selecao.toString().trim() === '') {
        if (botaoExistente) {
            botaoExistente.remove();
        }
        return;
    }

    // Cria o botão se não existir
    let botaoMarcacao = botaoExistente;
    if (!botaoMarcacao) {
        botaoMarcacao = document.createElement('div');
        botaoMarcacao.id = 'botao-marcacao';
        botaoMarcacao.className = 'botao-marcacao';
        botaoMarcacao.innerHTML = `
            <button id="marcar-amarelo" class="btn-marcacao" title="Marcar em amarelo">
                <i class="fas fa-highlighter" style="color: #ffffc3;"></i>
            </button>
            <button id="marcar-verde" class="btn-marcacao" title="Marcar em verde">
                <i class="fas fa-highlighter" style="color: #d4ffd4;"></i>
            </button>
            <button id="marcar-azul" class="btn-marcacao" title="Marcar em azul">
                <i class="fas fa-highlighter" style="color: #d4f4ff;"></i>
            </button>
            <button id="fazer-anotacao" class="btn-marcacao" title="Adicionar anotação">
                <i class="fas fa-comment-alt"></i>
            </button>
            <button id="remover-marcacao" class="btn-marcacao" title="Remover marcação">
                <i class="fas fa-eraser"></i>
            </button>
        `;

        // Adiciona eventos para os botões
        botaoMarcacao.addEventListener('click', function(e) {
            const target = e.target.closest('button');
            if (!target) return;

            switch(target.id) {
                case 'marcar-amarelo':
                    marcarSelecao('amarelo');
                    break;
                case 'marcar-verde':
                    marcarSelecao('verde');
                    break;
                case 'marcar-azul':
                    marcarSelecao('azul');
                    break;
                case 'fazer-anotacao':
                    criarAnotacao();
                    break;
                case 'remover-marcacao':
                    removerMarcacao();
                    break;
            }
        });

        document.body.appendChild(botaoMarcacao);
    }

    // Posiciona o botão próximo à seleção
    const range = selecao.getRangeAt(0);
    const rect = range.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    
    botaoMarcacao.style.top = (rect.top + scrollTop - 40) + 'px';
    botaoMarcacao.style.left = (rect.left + (rect.width / 2) - (botaoMarcacao.offsetWidth / 2)) + 'px';
}*/

// Função auxiliar para detectar dispositivos móveis
function isMobileDevice() {
    return window.innerWidth <= 768 || 
           (typeof window.orientation !== "undefined") || 
           (navigator.userAgent.indexOf('IEMobile') !== -1);
}

// Cache e controle de alterações
let marcacoesCache = new Map();
let alteracoesPendentes = new Set();
let salvamentoTimeout = null;
const DELAY_SALVAMENTO = 2000; // 2 segundos de delay para salvar

// Função para salvar marcações com throttling e debug detalhado
async function salvarMarcacoes() {
    if (salvamentoTimeout) {
        clearTimeout(salvamentoTimeout);
    }

    salvamentoTimeout = setTimeout(async () => {
        if (alteracoesPendentes.size === 0 && document.getElementsByClassName('marcacao-usuario').length === 0) {
            // Se não há alterações pendentes E não há marcações na página (caso de limpar tudo),
            // ainda assim pode ser necessário enviar um array vazio para o backend limpar o BD.
            // Se o backend espera sempre um array 'marcacoes', mesmo que vazio, ajuste aqui.
            // Por ora, se não há alterações e não há marcações, não faz nada.
            // Se você LIMPOU TODAS as marcações, 'alteracoesPendentes' pode não refletir isso,
            // e 'elementos' terá length 0. Precisamos garantir que um array vazio seja enviado
            // se a intenção era limpar todas as marcações do backend.
            // Esta lógica pode precisar de ajuste dependendo de como 'alteracoesPendentes' é gerenciado
            // ao remover *todas* as marcações.
            console.log('Nenhuma alteração pendente ou marcação para salvar.');
            // return; // Descomente se não for necessário enviar um array vazio em caso de limpeza total.
        }

        try {
            const marcacoesParaEnviar = [];
            const elementosMarcados = document.getElementsByClassName('marcacao-usuario');

            console.log(`Encontrados ${elementosMarcados.length} elementos com a classe 'marcacao-usuario'.`);

            for (let i = 0; i < elementosMarcados.length; i++) {
                const marcacaoElement = elementosMarcados[i];
                const parentElement = encontrarElementoPai(marcacaoElement);

                if (!parentElement?.id) {
                    console.warn('Marcação ignorada (sem elemento pai com ID):', marcacaoElement.outerHTML);
                    continue; 
                }

                const dadosDaMarcacao = {
                    elemento_id: parentElement.id,
                    tipo_marcacao: marcacaoElement.className.replace('marcacao-usuario', '').replace(/texto-anotado/g, '').trim(),
                    texto_marcado: marcacaoElement.textContent, // Coleta normalmente
                    pagina_url: window.location.pathname,
                    posicao_inicio: parseInt(marcacaoElement.dataset.posicaoInicio),
                    posicao_fim: parseInt(marcacaoElement.dataset.posicaoFim)
                };

                // Logs de depuração (manter para referência)
                // console.log('------------------------------------');
                // console.log(`Processando marcação ${i + 1} de ${elementosMarcados.length}:`);
                // ... (outros logs) ...
                // console.log('------------------------------------');

                // *** VERIFICAÇÃO E CORREÇÃO PRINCIPAL AQUI ***
                let dadosInvalidos = false;
                if (!dadosDaMarcacao.elemento_id) {
                    console.error('ALERTA JS: elemento_id NULO:', dadosDaMarcacao);
                    dadosInvalidos = true;
                }
                // Você pode decidir se tipo_marcacao vazio é um erro
                // if (dadosDaMarcacao.tipo_marcacao === '') {
                //     console.warn('ALERTA JS: tipo_marcacao VAZIO:', dadosDaMarcacao);
                //     dadosInvalidos = true; 
                // }
                if (isNaN(dadosDaMarcacao.posicao_inicio) || isNaN(dadosDaMarcacao.posicao_fim) ||
                    dadosDaMarcacao.posicao_inicio < 0 || dadosDaMarcacao.posicao_fim < 0 ||
                    dadosDaMarcacao.posicao_fim < dadosDaMarcacao.posicao_inicio) {
                    console.error('ALERTA JS: POSIÇÕES INVÁLIDAS:', dadosDaMarcacao);
                    dadosInvalidos = true;
                }

                // Se o texto marcado for vazio, NÃO ENVIAR ESTA MARCAÇÃO
                if (dadosDaMarcacao.texto_marcado.trim() === '') {
                    console.warn('ALERTA JS: Texto marcado está vazio. Esta marcação NÃO será enviada.', dadosDaMarcacao);
                    continue; // Pula para a próxima iteração do loop, não adiciona esta marcação
                }
                
                if (dadosInvalidos) {
                     console.error('PULANDO O ENVIO DESTA MARCAÇÃO DEVIDO A DADOS INVÁLIDOS (além de texto vazio, se aplicável).');
                     continue; 
                }

                marcacoesParaEnviar.push(dadosDaMarcacao);
            }
            
            // ... (resto da função: console.log dos dados finais, fetch, etc.) ...
            // Certifique-se que o fetch só acontece se marcacoesParaEnviar.length > 0 OU se você precisa
            // enviar um array vazio para limpar o backend.
            
            if (marcacoesParaEnviar.length === 0) {
                console.log('Nenhuma marcação válida para enviar ao backend.');
                // Se você precisa limpar o backend quando todas as marcações são removidas da página,
                // você pode querer enviar um array 'marcacoes' vazio aqui.
                // Exemplo: if (elementosMarcados.length === 0 && alteracoesPendentes.size > 0) { /* enviar array vazio */ }
                // Por ora, se não há o que enviar, apenas paramos.
                alteracoesPendentes.clear(); // Limpa se não há nada para enviar
                return;
            }

            console.log('Dados FINAIS sendo enviados para o backend:', { marcacoes: marcacoesParaEnviar, csrf_token: window.csrfToken });

            const response = await fetch('salvar_marcacao.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ marcacoes: marcacoesParaEnviar, csrf_token: window.csrfToken })
            });

            // ... (resto do tratamento da resposta) ...

        } catch (error) {
            console.error('Erro GERAL ao salvar marcações:', error);
        }
    }, DELAY_SALVAMENTO);
}

// Função para atualizar o cache local
function atualizarCache(marcacoes) {
    marcacoesCache.clear();
    marcacoes.forEach(marcacao => {
        marcacoesCache.set(marcacao.elemento_id, marcacao);
    });
}

// Função para marcar o texto selecionado
// Função para marcar o texto selecionado
function marcarSelecao(cor) {
    const selecao = window.getSelection();
    if (selecao.toString().trim() === '') return;

    const range = selecao.getRangeAt(0);
    const corCss = obterCorCss(cor);

    try {
        // Obtém o elemento pai e as posições
        const parentElement = encontrarElementoPai(range.startContainer);
        if (!parentElement?.id) return;

        // Calcula as posições relativas ao elemento pai
        const posicaoInicio = calcularPosicaoAbsoluta(parentElement, range.startContainer, range.startOffset);
        const posicaoFim = calcularPosicaoAbsoluta(parentElement, range.endContainer, range.endOffset);

        const span = document.createElement('span');
        span.className = `marcacao-usuario ${cor}`;
        span.style.backgroundColor = corCss;
        span.dataset.posicaoInicio = posicaoInicio;
        span.dataset.posicaoFim = posicaoFim;

        range.surroundContents(span);

        // Registra alteração pendente e salva
        alteracoesPendentes.add(parentElement.id);
        salvarMarcacoes();

        // Apenas remove a seleção
        selecao.removeAllRanges();
    } catch (e) {
        console.error('Erro ao marcar seleção:', e);
        alert('Não foi possível marcar este texto. Tente uma seleção mais simples.');
    }
}

// Função auxiliar para calcular a posição absoluta do texto
function calcularPosicaoAbsoluta(parentElement, container, offset) {
    let posicao = 0;
    const walker = document.createTreeWalker(
        parentElement,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );

    let node;
    while ((node = walker.nextNode())) {
        if (node === container) {
            return posicao + offset;
        }
        posicao += node.length;
    }
    return posicao;
}

// Função para aplicar marcação de forma segura
function aplicarMarcacaoSegura(elemento, inicio, fim, cor, corCss) {
    const walker = document.createTreeWalker(
        elemento,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );

    let posicaoAtual = 0;
    let noInicio = null;
    let noFim = null;
    let offsetInicio = 0;
    let offsetFim = 0;
    let node;

    // Encontra os nós de texto e suas posições
    while (node = walker.nextNode()) {
        const comprimentoTexto = node.textContent.length;
        
        if (!noInicio && posicaoAtual + comprimentoTexto > inicio) {
            noInicio = node;
            offsetInicio = inicio - posicaoAtual;
        }
        
        if (!noFim && posicaoAtual + comprimentoTexto >= fim) {
            noFim = node;
            offsetFim = fim - posicaoAtual;
            break;
        }
        
        posicaoAtual += comprimentoTexto;
    }

    if (!noInicio || !noFim) return null;

    // Cria o span para a marcação
    const spanMarcacao = document.createElement('span');
    spanMarcacao.className = `marcacao-usuario ${cor}`;
    spanMarcacao.style.backgroundColor = corCss;
    spanMarcacao.dataset.posicaoInicio = inicio;
    spanMarcacao.dataset.posicaoFim = fim;

    // Se a marcação está dentro de um único nó de texto
    if (noInicio === noFim) {
        const range = document.createRange();
        range.setStart(noInicio, offsetInicio);
        range.setEnd(noInicio, offsetFim);
        
        try {
            range.surroundContents(spanMarcacao);
            return spanMarcacao;
        } catch (e) {
            console.warn('Erro ao aplicar marcação em nó único:', e);
            return null;
        }
    }
    
    // Se a marcação atravessa múltiplos nós
    try {
        const textoMarcado = [];
        let nodeAtual = noInicio;
        let primeiroNo = true;
        
        while (nodeAtual) {
            const range = document.createRange();
            
            if (primeiroNo) {
                range.setStart(nodeAtual, offsetInicio);
                range.setEnd(nodeAtual, nodeAtual.length);
                primeiroNo = false;
            } else if (nodeAtual === noFim) {
                range.setStart(nodeAtual, 0);
                range.setEnd(nodeAtual, offsetFim);
            } else {
                range.setStart(nodeAtual, 0);
                range.setEnd(nodeAtual, nodeAtual.length);
            }
            
            const clone = spanMarcacao.cloneNode(true);
            range.surroundContents(clone);
            textoMarcado.push(clone);
            
            if (nodeAtual === noFim) break;
            nodeAtual = walker.nextNode();
        }
        
        return textoMarcado[0];
    } catch (e) {
        console.warn('Erro ao aplicar marcação em múltiplos nós:', e);
        return null;
    }
}

// Função para criar uma anotação para o texto selecionado
function criarAnotacao() {
    var selecao = window.getSelection();
    var textoSelecionado = selecao.toString().trim();

    if (textoSelecionado === '') {
        return;
    }

    var range = selecao.getRangeAt(0);
    var anotacaoId = 'anotacao-' + Date.now();

    try {
        const parentElement = encontrarElementoPai(range.startContainer);
        if (!parentElement?.id) {
            alert('Não foi possível identificar o elemento pai para esta seleção.');
            return;
        }

        // Salva o estado atual das marcações
        const marcacoesExistentes = Array.from(parentElement.querySelectorAll('.marcacao-usuario')).map(elem => ({
            posicaoInicio: parseInt(elem.dataset.posicaoInicio),
            posicaoFim: parseInt(elem.dataset.posicaoFim),
            cor: Array.from(elem.classList).find(c => c.startsWith('marcacao-')),
            corCss: elem.style.backgroundColor
        }));

        // Calcula as posições para a nova anotação
        const posicaoInicio = calcularPosicaoAbsoluta(parentElement, range.startContainer, range.startOffset);
        const posicaoFim = calcularPosicaoAbsoluta(parentElement, range.endContainer, range.endOffset);

        // Cria e aplica a nova anotação
        const span = document.createElement('span');
        span.className = 'texto-anotado';
        span.setAttribute('data-anotacao-id', anotacaoId);
        span.setAttribute('data-posicao-inicio', posicaoInicio);
        span.setAttribute('data-posicao-fim', posicaoFim);
        span.style.backgroundColor = '#FFE4B5';
        span.style.borderBottom = '1px dotted #FFA500';
        span.style.cursor = 'help';

        try {
            range.surroundContents(span);
        } catch (e) {
            console.warn('Erro ao aplicar anotação, tentando método alternativo');
            const novoSpan = aplicarMarcacaoSegura(
                parentElement,
                posicaoInicio,
                posicaoFim,
                'texto-anotado',
                '#FFE4B5'
            );
            if (!novoSpan) {
                throw new Error('Não foi possível aplicar a anotação');
            }
            novoSpan.setAttribute('data-anotacao-id', anotacaoId);
        }

        // Reaplica as marcações existentes
        marcacoesExistentes.forEach(marcacao => {
            aplicarMarcacaoSegura(
                parentElement,
                marcacao.posicaoInicio,
                marcacao.posicaoFim,
                marcacao.cor,
                marcacao.corCss
            );
        });

        // Adiciona evento de clique e mostra diálogo
        const novaAnotacao = document.querySelector(`[data-anotacao-id="${anotacaoId}"]`);
        if (novaAnotacao) {
            novaAnotacao.addEventListener('click', () => mostrarDialogoAnotacao(anotacaoId));
            mostrarDialogoAnotacao(anotacaoId, true, posicaoInicio, posicaoFim);
        }

        // Limpa a seleção e remove o botão de marcação
        const botaoMarcacao = document.getElementById('botao-marcacao');
        if (botaoMarcacao) {
            botaoMarcacao.remove();
        }
        selecao.removeAllRanges();

        // Registra alteração pendente
        alteracoesPendentes.add(parentElement.id);

    } catch (e) {
        console.error('Erro ao criar anotação:', e);
        alert('Não foi possível criar anotação para este texto. Tente uma seleção mais simples.');
    }
}

// Função auxiliar para encontrar a posição no texto
function encontrarPosicaoTexto(elemento, posicaoAbsoluta) {
    let posicaoAtual = 0;
    
    function percorrerNos(no) {
        if (no.nodeType === Node.TEXT_NODE) {
            if (posicaoAtual + no.length >= posicaoAbsoluta) {
                return {
                    node: no,
                    offset: posicaoAbsoluta - posicaoAtual
                };
            }
            posicaoAtual += no.length;
        } else {
            for (let filho of no.childNodes) {
                const resultado = percorrerNos(filho);
                if (resultado) return resultado;
            }
        }
        return null;
    }
    
    return percorrerNos(elemento);
}

// Função para mostrar mensagem estilizada para anotações
function mostrarMensagemAnotacao(msg, tipo = 'info') {
    // Remove mensagem anterior, se houver
    const antiga = document.getElementById('msg-anotacao-modal');
    if (antiga) antiga.remove();
    
    const modal = document.createElement('div');
    modal.id = 'msg-anotacao-modal';
    modal.style = 'position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:#fff;padding:24px 32px;border-radius:8px;box-shadow:0 2px 12px #0003;z-index:3000;min-width:280px;max-width:90vw;display:flex;flex-direction:column;align-items:center;gap:18px;';
    let cor = '#007bff';
    let icone = 'ℹ️';
    if (tipo === 'aviso') { cor = '#FFA500'; icone = '⚠️'; }
    if (tipo === 'erro') { cor = '#dc3545'; icone = '❌'; }
    modal.innerHTML = `<div style="font-size:2rem;">${icone}</div><div style="color:${cor};font-size:1.1rem;text-align:center;">${msg}</div><button id="fechar-msg-anotacao" style="margin-top:8px;padding:6px 16px;border:none;border-radius:4px;background:${cor};color:#fff;cursor:pointer;font-size:1rem;">Fechar</button>`;
    document.body.appendChild(modal);
    document.getElementById('fechar-msg-anotacao').onclick = () => modal.remove();
    setTimeout(() => { if (document.body.contains(modal)) modal.remove(); }, 3500);
}

// Função para mostrar o diálogo de anotação
async function mostrarDialogoAnotacao(anotacaoId, ehNova = false, posicaoInicio = null, posicaoFim = null) {
    try {
        let textoAnotacao = '';

        if (!ehNova) {
            let response = await fetch(`carregar_anotacoes.php?pagina_url=${encodeURIComponent(window.location.pathname)}`);
            if (!response.ok) {
                throw new Error('Erro ao carregar anotação');
            }
            let responseData = await response.json();
            if (!responseData.sucesso) {
                throw new Error(responseData.erro || 'Erro ao carregar anotação');
            }

            console.log('Dados recebidos do servidor:', responseData.dados);

            let anotacao = responseData.dados.find(a => String(a.id) === String(anotacaoId));
            if (anotacao) {
                console.log('Anotação encontrada:', anotacao);
                textoAnotacao = anotacao.texto_anotacao || '';
                posicaoInicio = anotacao.posicao_inicio;
                posicaoFim = anotacao.posicao_fim;
                console.log('Posições da anotação:', { posicaoInicio, posicaoFim });
            } else {
                // Mostra loader
                let loader = document.createElement('div');
                loader.id = 'anotacao-loader';
                loader.style = 'position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:#fff;padding:24px 32px;border-radius:8px;box-shadow:0 2px 8px #0002;z-index:2000;font-size:1.1rem;color:#333;';
                loader.innerText = 'Carregando anotação...';
                document.body.appendChild(loader);
                // Tenta recarregar anotações e buscar de novo
                if (typeof carregarAnotacoes === 'function') {
                    if (typeof marcacoesCache !== 'undefined') {
                        await carregarAnotacoes(marcacoesCache);
                    } else {
                        await carregarAnotacoes(new Map());
                    }
                }
                // Busca novamente
                response = await fetch(`carregar_anotacoes.php?pagina_url=${encodeURIComponent(window.location.pathname)}`);
                responseData = await response.json();
                anotacao = responseData.dados.find(a => String(a.id) === String(anotacaoId));
                document.body.removeChild(loader);
                if (anotacao) {
                    textoAnotacao = anotacao.texto_anotacao || '';
                    posicaoInicio = anotacao.posicao_inicio;
                    posicaoFim = anotacao.posicao_fim;
                    console.log('Anotação encontrada após recarregar:', anotacao);
                } else {
                    mostrarMensagemAnotacao('Anotação ainda não sincronizada. Tente novamente em alguns segundos.', 'aviso');
                    return;
                }
            }
        }

        // Remove diálogos existentes
        fecharDialogoAnotacao();

        // Cria o diálogo
        const dialogo = document.createElement('div');
        dialogo.id = 'dialogo-anotacao';
        dialogo.innerHTML = `
            <div class="dialogo-header">
                <h3>${ehNova ? 'Nova Anotação' : 'Editar Anotação'}</h3>
                <button id="fechar-anotacao" class="btn-fechar">&times;</button>
            </div>
            <div class="dialogo-body">
                <textarea id="texto-anotacao" placeholder="Digite sua anotação aqui...">${textoAnotacao}</textarea>
            </div>
            <div class="dialogo-footer">
                <button id="salvar-anotacao" class="btn-salvar">Salvar</button>
                ${!ehNova ? '<button id="excluir-anotacao" class="btn-excluir">Excluir</button>' : ''}
            </div>
        `;

        // Adiciona estilos ao diálogo
        Object.assign(dialogo.style, {
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            backgroundColor: 'white',
            borderRadius: '8px',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08)',
            zIndex: '1001',
            maxWidth: '500px',
            width: '90%',
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden'
        });

        // Cria e estiliza o overlay
        const overlay = document.createElement('div');
        overlay.id = 'overlay-anotacao';
        Object.assign(overlay.style, {
            position: 'fixed',
            top: '0',
            left: '0',
            width: '100%',
            height: '100%',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: '1000'
        });

        // Adiciona estilos CSS
        const style = document.createElement('style');
        style.textContent = `
            #dialogo-anotacao .dialogo-header {
                padding: 16px;
                background-color: #f8f9fa;
                border-bottom: 1px solid #dee2e6;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            #dialogo-anotacao .dialogo-header h3 {
                margin: 0;
                color: #333;
                font-size: 1.25rem;
            }

            #dialogo-anotacao .btn-fechar {
                background: none;
                border: none;
                font-size: 1.5rem;
                cursor: pointer;
                padding: 0 8px;
                color: #666;
            }

            #dialogo-anotacao .dialogo-body {
                padding: 16px;
            }

            #dialogo-anotacao textarea {
                width: 100%;
                min-height: 150px;
                padding: 12px;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-size: 1rem;
                resize: vertical;
            }

            #dialogo-anotacao .dialogo-footer {
                padding: 16px;
                background-color: #f8f9fa;
                border-top: 1px solid #dee2e6;
                display: flex;
                justify-content: flex-end;
                gap: 8px;
            }

            #dialogo-anotacao button {
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 0.875rem;
                font-weight: 500;
                border: none;
            }

            #dialogo-anotacao .btn-salvar {
                background-color: #00008B;
                color: white;
            }

            #dialogo-anotacao .btn-excluir {
                background-color: #dc3545;
                color: white;
            }

            #dialogo-anotacao button:hover {
                opacity: 0.9;
            }
        `;

        // Adiciona elementos ao DOM
        document.head.appendChild(style);
        document.body.appendChild(overlay);
        document.body.appendChild(dialogo);

        // Adiciona eventos
        const fecharBtn = document.getElementById('fechar-anotacao');
        const salvarBtn = document.getElementById('salvar-anotacao');
        const textarea = document.getElementById('texto-anotacao');

        fecharBtn.addEventListener('click', fecharDialogoAnotacao);
        overlay.addEventListener('click', fecharDialogoAnotacao);

        // Se for uma nova anotação e temos as posições, vamos armazená-las no elemento
        if (ehNova && posicaoInicio !== null && posicaoFim !== null) {
            const elementoAnotado = document.querySelector(`[data-anotacao-id="${anotacaoId}"]`);
            if (elementoAnotado) {
                elementoAnotado.setAttribute('data-posicao-inicio', posicaoInicio);
                elementoAnotado.setAttribute('data-posicao-fim', posicaoFim);
                console.log('Posições armazenadas no elemento:', { posicaoInicio, posicaoFim });
            }
        }

        salvarBtn.addEventListener('click', () => {
            salvarAnotacao(anotacaoId, textarea.value);
            fecharDialogoAnotacao();
        });

        if (!ehNova) {
            const excluirBtn = document.getElementById('excluir-anotacao');
            excluirBtn?.addEventListener('click', () => {
                excluirAnotacao(anotacaoId);
                fecharDialogoAnotacao();
            });
        }

        // Foca no textarea
        textarea.focus();

    } catch (error) {
        console.error('Erro ao mostrar diálogo de anotação:', error);
        alert('Erro ao carregar anotação. Por favor, tente novamente.');
    }
}

// Função para excluir uma anotação
async function excluirAnotacao(anotacaoId) {
    try {
        if (!window.csrfToken) {
            mostrarMensagemAnotacao('Token CSRF não encontrado.', 'erro');
            return;
        }
        const idNumerico = String(anotacaoId).replace('anotacao-', '');
        const response = await fetch('excluir_anotacao.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ anotacao_id: idNumerico, csrf_token: window.csrfToken })
        });
        const data = await response.json();
        if (data.sucesso) {
            mostrarMensagemAnotacao('Anotação excluída com sucesso!', 'info');
            
            // Encontra o elemento com a anotação
            const elemento = document.querySelector(`[data-anotacao-id="${anotacaoId}"]`);
            if (elemento) {
                // Preserva a marcação removendo apenas os atributos da anotação
                const classesMarcacao = Array.from(elemento.classList)
                    .filter(classe => ['verde', 'azul', 'amarela', 'amarelo', 'marcacao-usuario']
                    .includes(classe));
                
                const posicaoInicio = elemento.getAttribute('data-posicao-inicio');
                const posicaoFim = elemento.getAttribute('data-posicao-fim');
                
                // Remove atributos relacionados à anotação
                elemento.removeAttribute('data-anotacao');
                elemento.removeAttribute('data-anotacao-id');
                elemento.removeAttribute('title');
                
                // Mantém apenas as classes de marcação
                elemento.className = classesMarcacao.join(' ');
                
                // Mantém os atributos de posição
                if (posicaoInicio) elemento.setAttribute('data-posicao-inicio', posicaoInicio);
                if (posicaoFim) elemento.setAttribute('data-posicao-fim', posicaoFim);
            }

            // Processa as marcações existentes
            const marcacoesAtuais = new Map();
            document.querySelectorAll('.marcacao-usuario').forEach(marcacao => {
                const elementoPai = encontrarElementoPai(marcacao);
                if (elementoPai && elementoPai.id) {
                    if (!marcacoesAtuais.has(elementoPai.id)) {
                        marcacoesAtuais.set(elementoPai.id, []);
                    }
                    
                    let tipoMarcacao = null;
                    if (marcacao.classList.contains('verde')) {
                        tipoMarcacao = 'verde';
                    } else if (marcacao.classList.contains('azul')) {
                        tipoMarcacao = 'azul';
                    } else if (marcacao.classList.contains('amarelo') || marcacao.classList.contains('amarela')) {
                        tipoMarcacao = 'amarela';
                    }
                    
                    const posicaoInicio = marcacao.getAttribute('data-posicao-inicio');
                    const posicaoFim = marcacao.getAttribute('data-posicao-fim');
                    
                    if (posicaoInicio && posicaoFim && tipoMarcacao) {
                        marcacoesAtuais.get(elementoPai.id).push({
                            tipo_marcacao: tipoMarcacao,
                            posicao_inicio: parseInt(posicaoInicio),
                            posicao_fim: parseInt(posicaoFim)
                        });
                    }
                }
            });

            // Recarrega as anotações com as marcações atualizadas
            await carregarAnotacoes(marcacoesAtuais);
        } else {
            mostrarMensagemAnotacao(data.erro || 'Erro ao excluir anotação.', 'erro');
        }
    } catch (error) {
        mostrarMensagemAnotacao('Erro ao excluir anotação.', 'erro');
        console.error('Erro ao excluir anotação:', error);
    }
}


// Função para fechar o diálogo de anotação
function fecharDialogoAnotacao() {
    const dialogo = document.getElementById('dialogo-anotacao');
    const overlay = document.getElementById('overlay-anotacao');
    const style = document.querySelector('style[data-dialogo-style]');

    if (dialogo) dialogo.remove();
    if (overlay) overlay.remove();
    if (style) style.remove();
}

// Função para salvar a anotação
async function salvarAnotacao(anotacaoId, texto) {
    console.log('Iniciando salvamento da anotação:', anotacaoId);

    // Converter anotacaoId para string se não for
    anotacaoId = String(anotacaoId);

    const elementoAnotado = document.querySelector(`[data-anotacao-id="${anotacaoId}"]`);
    if (!elementoAnotado) {
        console.error('Elemento anotado não encontrado');
        return;
    }

    const parentElement = encontrarElementoPai(elementoAnotado);
    if (!parentElement || !parentElement.id) {
        console.error('Elemento pai não encontrado ou sem ID');
        return;
    }

    // Obter as posições de início e fim
    let posicaoInicio = elementoAnotado.getAttribute('data-posicao-inicio');
    let posicaoFim = elementoAnotado.getAttribute('data-posicao-fim');

    // Converter para números
    posicaoInicio = posicaoInicio ? parseInt(posicaoInicio) : null;
    posicaoFim = posicaoFim ? parseInt(posicaoFim) : null;

    // Verificar se as posições são válidas
    if (posicaoInicio === null || posicaoFim === null || isNaN(posicaoInicio) || isNaN(posicaoFim)) {
        console.warn('Posições inválidas:', { posicaoInicio, posicaoFim });

        // Calcular as posições manualmente
        const parentElement = encontrarElementoPai(elementoAnotado);
        if (parentElement) {
            const textoPlano = parentElement.textContent;
            const textoAnotado = elementoAnotado.textContent;

            // Encontrar a posição do texto anotado no texto completo
            const indice = textoPlano.indexOf(textoAnotado);
            if (indice !== -1) {
                posicaoInicio = indice;
                posicaoFim = indice + textoAnotado.length;
                console.log('Posições calculadas manualmente:', { posicaoInicio, posicaoFim });
            }
        }
    }

    console.log('Elemento pai:', parentElement.id);
    console.log('Texto da anotação:', texto);
    console.log('Posições:', { posicaoInicio, posicaoFim });

    try {
        // Extrair o ID numérico da anotação (remover o prefixo 'anotacao-')
        let idNumerico = anotacaoId.startsWith('anotacao-') ? anotacaoId.replace('anotacao-', '') : anotacaoId;

        // Verificar se o ID é um número válido
        if (isNaN(parseInt(idNumerico))) {
            console.warn('ID numérico inválido:', idNumerico);
            // Gerar um novo ID numérico baseado no timestamp atual
            idNumerico = Date.now().toString();
            console.log('Novo ID numérico gerado:', idNumerico);
        }

        const dadosEnvio = {
            anotacao_id: anotacaoId,  // Mantemos o ID original para compatibilidade
            id_numerico: idNumerico,  // Adicionamos o ID numérico para facilitar a busca no servidor
            elemento_id: parentElement.id,
            pagina_url: window.location.pathname,
            texto: texto,
            conteudo_anotado: elementoAnotado.textContent,
            posicao_inicio: posicaoInicio,
            posicao_fim: posicaoFim,
            csrf_token: window.csrfToken // Adicionado para proteção CSRF
        };

        console.log('Dados sendo enviados:', dadosEnvio);

        const response = await fetch('salvar_anotacao.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(dadosEnvio)
        });

        const responseText = await response.text();
        console.log('Resposta do servidor:', responseText);

        // Verifica se a resposta contém HTML (erro PHP)
        if (responseText.includes('<br />') || responseText.includes('<b>')) {
            console.error('Erro no servidor PHP:', responseText);
            alert('Erro no servidor. Verifique o console para mais detalhes.');
            return;
        }

        try {
            const responseData = JSON.parse(responseText);
            console.log('Resposta processada:', responseData);

            if (!response.ok) {
                throw new Error('Erro ao salvar anotação');
            }

            console.log('Anotação salva com sucesso!');

            // Mantém as marcações existentes usando marcacoesCache
            const marcacoesAtuais = new Map();
            
            // Função auxiliar para processar uma marcação
            function processarMarcacao(marcacao, elementoPai) {
                if (!marcacoesAtuais.has(elementoPai.id)) {
                    marcacoesAtuais.set(elementoPai.id, []);
                }
                
                // Determina o tipo de marcação baseado na classe
                let tipoMarcacao = null;
                if (marcacao.classList.contains('verde')) {
                    tipoMarcacao = 'verde';
                } else if (marcacao.classList.contains('azul')) {
                    tipoMarcacao = 'azul';
                } else if (marcacao.classList.contains('amarelo') || marcacao.classList.contains('amarela')) {
                    tipoMarcacao = 'amarela';
                }

                console.log('Classes do elemento:', Array.from(marcacao.classList));
                console.log('Tipo de marcação detectado:', tipoMarcacao);
                
                const posicaoInicio = marcacao.getAttribute('data-posicao-inicio');
                const posicaoFim = marcacao.getAttribute('data-posicao-fim');
                
                if (posicaoInicio && posicaoFim && tipoMarcacao) {
                    const novaMarcacao = {
                        tipo_marcacao: tipoMarcacao,
                        posicao_inicio: parseInt(posicaoInicio),
                        posicao_fim: parseInt(posicaoFim)
                    };
                    
                    // Verifica se já existe uma marcação idêntica
                    const marcacaoExistente = marcacoesAtuais.get(elementoPai.id).find(m => 
                        m.posicao_inicio === novaMarcacao.posicao_inicio && 
                        m.posicao_fim === novaMarcacao.posicao_fim &&
                        m.tipo_marcacao === novaMarcacao.tipo_marcacao
                    );
                    
                    if (!marcacaoExistente) {
                        marcacoesAtuais.get(elementoPai.id).push(novaMarcacao);
                    }
                }
            }

            // Processa todas as marcações existentes
            document.querySelectorAll('.marcacao-usuario').forEach(marcacao => {
                const elementoPai = encontrarElementoPai(marcacao);
                if (elementoPai && elementoPai.id) {
                    processarMarcacao(marcacao, elementoPai);
                }
            });

            console.log('Marcações atuais antes de carregar:', marcacoesAtuais);
            await carregarAnotacoes(marcacoesAtuais);

        } catch (parseError) {
            console.error('Erro ao processar resposta JSON:', parseError);
            console.error('Erro detalhado:', parseError.stack);
            alert('Erro ao processar a resposta. Verifique o console para mais detalhes.');
        }
    } catch (error) {
        console.error('Erro ao salvar anotação:', error);
        alert('Erro ao salvar anotação. Por favor, tente novamente.');
    }
}

// ...

// Obtenção automática do CSRF token ao carregar a página
document.addEventListener('DOMContentLoaded', function() {
    fetch('csrf_token.php')
        .then(res => res.json())
        .then(data => {
            window.csrfToken = data.csrf_token;
            inicializarSistemaMarcacao();
        });
});

// Função para mostrar a anotação
async function mostrarAnotacao(anotacao) {
    try {
        // Usa a função existente mostrarDialogoAnotacao
        await mostrarDialogoAnotacao(anotacao.id, false);
        
        // Destaca visualmente a anotação
        const elementoAnotado = document.querySelector(`[data-anotacao-id="${anotacao.id}"]`);
        if (elementoAnotado) {
            // Salva a cor original
            const corOriginal = elementoAnotado.style.backgroundColor;
            
            // Aplica destaque temporário
            elementoAnotado.style.backgroundColor = '#FFFF00';
            elementoAnotado.style.transition = 'background-color 0.5s';
            
            // Rola até o elemento
            elementoAnotado.scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
            
            // Retorna à cor original após 2 segundos
            setTimeout(() => {
                elementoAnotado.style.backgroundColor = corOriginal;
            }, 2000);
        }
    } catch (error) {
        console.error('Erro ao mostrar anotação:', error);
        notificarErro('Erro ao mostrar anotação.');
    }
}

// Função para mostrar a lista de todas as anotações
function mostrarListaAnotacoes() {
    var anotacoesSalvas = localStorage.getItem('constituicao-anotacoes');
    if (!anotacoesSalvas) {
        alert('Não há anotações salvas.');
        return;
    }

    var anotacoes = JSON.parse(anotacoesSalvas);
    if (Object.keys(anotacoes).length === 0) {
        alert('Não há anotações salvas.');
        return;
    }

    // Remove qualquer diálogo existente
    fecharDialogoAnotacao();

    // Cria o diálogo para listar anotações
    var dialogo = document.createElement('div');
    dialogo.id = 'dialogo-anotacao';
    dialogo.style.position = 'fixed';
    dialogo.style.top = '50%';
    dialogo.style.left = '50%';
    dialogo.style.transform = 'translate(-50%, -50%)';
    dialogo.style.backgroundColor = 'white';
    dialogo.style.padding = '20px';
    dialogo.style.borderRadius = '5px';
    dialogo.style.boxShadow = '0 0 10px rgba(0, 0, 0, 0.2)';
    dialogo.style.zIndex = '1001';
    dialogo.style.width = '80%';
    dialogo.style.maxWidth = '600px';
    dialogo.style.maxHeight = '80vh';
    dialogo.style.overflowY = 'auto';

    // Cria o cabeçalho do diálogo
    var header = document.createElement('div');
    header.style.display = 'flex';
    header.style.justifyContent = 'space-between';
    header.style.alignItems = 'center';
    header.style.marginBottom = '15px';
    header.style.borderBottom = '1px solid #ddd';
    header.style.paddingBottom = '10px';

    var titulo = document.createElement('h3');
    titulo.textContent = 'Todas as Anotações';
    titulo.style.margin = '0';

    var fecharBtn = document.createElement('button');
    fecharBtn.innerHTML = '×';
    fecharBtn.style.border = 'none';
    fecharBtn.style.background = 'none';
    fecharBtn.style.fontSize = '24px';
    fecharBtn.style.cursor = 'pointer';
    fecharBtn.addEventListener('click', fecharDialogoAnotacao);

    header.appendChild(titulo);
    header.appendChild(fecharBtn);
    dialogo.appendChild(header);

    // Lista de anotações
    var listaAnotacoes = document.createElement('div');

    // Ordena anotações da mais recente para a mais antiga
    var anotacoesArray = [];
    for (var id in anotacoes) {
        anotacoes[id].id = id;
        anotacoesArray.push(anotacoes[id]);
    }

    anotacoesArray.sort(function(a, b) {
        return new Date(b.data) - new Date(a.data);
    });

    // Adiciona cada anotação à lista
    anotacoesArray.forEach(function(anotacao) {
        var itemAnotacao = document.createElement('div');
        itemAnotacao.className = 'item-anotacao';
        itemAnotacao.style.marginBottom = '15px';
        itemAnotacao.style.padding = '10px';
        itemAnotacao.style.backgroundColor = '#f9f9f9';
        itemAnotacao.style.borderRadius = '4px';
        itemAnotacao.style.borderLeft = '3px solid #FFA500';

        // Adiciona o texto anotado
        var textoAnotado = document.createElement('div');
        textoAnotado.className = 'texto-citado';
        textoAnotado.textContent = anotacao.conteudoAnotado;
        textoAnotado.style.fontStyle = 'italic';
        textoAnotado.style.color = '#666';
        textoAnotado.style.padding = '5px';
        textoAnotado.style.backgroundColor = '#FFE4B5';
        textoAnotado.style.borderRadius = '3px';
        textoAnotado.style.marginBottom = '8px';
        textoAnotado.style.cursor = 'pointer';

        // Evento para localizar o texto no documento
        textoAnotado.addEventListener('click', function() {
            fecharDialogoAnotacao();
            localizarAnotacao(anotacao.id);
        });

        // Adiciona o conteúdo da anotação
        var conteudoAnotacao = document.createElement('div');
        conteudoAnotacao.className = 'conteudo-anotacao';
        conteudoAnotacao.textContent = anotacao.texto;
        conteudoAnotacao.style.padding = '5px';

        // Adiciona a data da anotação
        var dataAnotacao = document.createElement('div');
        dataAnotacao.className = 'data-anotacao';
        var data = new Date(anotacao.data);
        dataAnotacao.textContent = data.toLocaleDateString() + ' ' + data.toLocaleTimeString();
        dataAnotacao.style.fontSize = '12px';
        dataAnotacao.style.color = '#999';
        dataAnotacao.style.textAlign = 'right';
        dataAnotacao.style.marginTop = '5px';

        // Adiciona botões de ação
        var botoesAcao = document.createElement('div');
        botoesAcao.style.display = 'flex';
        botoesAcao.style.justifyContent = 'flex-end';
        botoesAcao.style.marginTop = '10px';

        var btnEditar = document.createElement('button');
        btnEditar.textContent = 'Editar';
        btnEditar.style.padding = '5px 10px';
        btnEditar.style.marginRight = '10px';
        btnEditar.style.backgroundColor = '#4CAF50';
        btnEditar.style.color = 'white';
        btnEditar.style.border = 'none';
        btnEditar.style.borderRadius = '3px';
        btnEditar.style.cursor = 'pointer';
        btnEditar.addEventListener('click', function() {
            fecharDialogoAnotacao();
            mostrarDialogoAnotacao(anotacao.id);
        });

        var btnExcluir = document.createElement('button');
        btnExcluir.textContent = 'Excluir';
        btnExcluir.style.padding = '5px 10px';
        btnExcluir.style.backgroundColor = '#f44336';
        btnExcluir.style.color = 'white';
        btnExcluir.style.border = 'none';
        btnExcluir.style.borderRadius = '3px';
        btnExcluir.style.cursor = 'pointer';
        btnExcluir.addEventListener('click', function() {
            if (confirm('Tem certeza que deseja excluir esta anotação?')) {
                excluirAnotacao(anotacao.id);
                fecharDialogoAnotacao();
                mostrarListaAnotacoes(); // Recarrega a lista
            }
        });

        botoesAcao.appendChild(btnEditar);
        botoesAcao.appendChild(btnExcluir);

        // Adiciona todos os elementos ao item
        itemAnotacao.appendChild(textoAnotado);
        itemAnotacao.appendChild(conteudoAnotacao);
        itemAnotacao.appendChild(dataAnotacao);
        itemAnotacao.appendChild(botoesAcao);

        // Adiciona o item à lista
        listaAnotacoes.appendChild(itemAnotacao);
    });

    dialogo.appendChild(listaAnotacoes);

    // Adiciona uma camada de fundo semi-transparente
    var overlay = document.createElement('div');
    overlay.id = 'overlay-anotacao';
    overlay.style.position = 'fixed';
    overlay.style.top = '0';
    overlay.style.left = '0';
    overlay.style.width = '100%';
    overlay.style.height = '100%';
    overlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
    overlay.style.zIndex = '1000';
    overlay.addEventListener('click', fecharDialogoAnotacao);

    // Adiciona os elementos ao corpo da página
    document.body.appendChild(overlay);
    document.body.appendChild(dialogo);
}

// Função para localizar uma anotação no texto
function localizarAnotacao(anotacaoId) {
    var elementoAnotado = document.querySelector(`[data-anotacao-id="${anotacaoId}"]`);
    if (!elementoAnotado) return;

    // Destaca temporariamente o elemento
    var corOriginal = elementoAnotado.style.backgroundColor;
    elementoAnotado.style.backgroundColor = '#FFFF00';
    elementoAnotado.style.transition = 'background-color 0.5s';

    // Rola até o elemento
    elementoAnotado.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
    });

    // Retorna à cor original após 2 segundos
    setTimeout(function() {
        elementoAnotado.style.backgroundColor = corOriginal;
    }, 2000);
}

// Função para carregar anotações
async function carregarAnotacoes(marcacoesPorElemento) {
    try {
        const response = await fetch(`carregar_anotacoes.php?pagina_url=${encodeURIComponent(window.location.pathname)}`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const responseData = await response.json();
        if (!responseData.sucesso) {
            throw new Error(responseData.erro || 'Erro ao carregar anotações');
        }

        const anotacoes = responseData.dados;
        if (anotacoes && anotacoes.length > 0) {
            processarAnotacoes(anotacoes, marcacoesPorElemento);
        }

    } catch (error) {
        console.error('Erro ao carregar anotações:', error);
        notificarErro('Erro ao carregar anotações. Por favor, recarregue a página.');
    }
}

// Função para remover a marcação
function removerMarcacao() {
    const selecao = window.getSelection();
    if (selecao.toString().trim() === '') return;

    const node = encontrarElementoMarcado(selecao.anchorNode);
    if (!node) return;

    const parentElement = encontrarElementoPai(node);
    if (parentElement?.id) {
        alteracoesPendentes.add(parentElement.id);

        // Remove a marcação do cache local
        marcacoesCache.delete(parentElement.id);
    }

    // Remove a marcação mantendo o conteúdo
    const parent = node.parentNode;
    while (node.firstChild) {
        parent.insertBefore(node.firstChild, node);
    }
    parent.removeChild(node);

    // Agenda salvamento com o servidor
    salvarMarcacoes();
   // removerBotaoMarcacao();
}

// Função para carregar marcações do servidor
async function carregarMarcacoes() {
    try {
        const response = await fetch('carregar_marcacoes.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                pagina_url: window.location.pathname
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const marcacoes = await response.json();
        
        // Processa e aplica as marcações
        const marcacoesPorElemento = processarMarcacoes(marcacoes);
        aplicarMarcacoes(marcacoesPorElemento);

        return marcacoesPorElemento;

    } catch (error) {
        console.error('Erro ao carregar marcações:', error);
        notificarErro('Erro ao carregar marcações. Por favor, recarregue a página.');
        return new Map();
    }
}

// Função para processar marcações
function processarMarcacoes(marcacoes) {
    const marcacoesPorElemento = new Map();

    marcacoes.forEach(marcacao => {
        if (!marcacoesPorElemento.has(marcacao.elemento_id)) {
            marcacoesPorElemento.set(marcacao.elemento_id, []);
        }
        marcacoesPorElemento.get(marcacao.elemento_id).push(marcacao);
    });

    return marcacoesPorElemento; // Retorna o Map para uso posterior
}

// Função para aplicar marcações
function aplicarMarcacoes(marcacoesPorElemento) {
    marcacoesPorElemento.forEach((marcacoes, elementoId) => {
        const elemento = document.getElementById(elementoId);
        if (!elemento) return;

        const textoOriginal = elemento.textContent;
        let resultado = textoOriginal;

        marcacoes
            .sort((a, b) => b.posicao_inicio - a.posicao_inicio)
            .forEach(marcacao => {
                resultado = aplicarMarcacao(resultado, marcacao);
            });

        elemento.innerHTML = resultado;
    });
}

// Função auxiliar para aplicar marcação individual
function aplicarMarcacao(texto, marcacao) {
    const inicio = parseInt(marcacao.posicao_inicio);
    const fim = parseInt(marcacao.posicao_fim);

    // Certifique-se de que inicio e fim são números válidos
    // Se não forem, pode ser um problema com os dados vindos do banco
    if (isNaN(inicio) || isNaN(fim)) {
        console.error('Posições inválidas recebidas para a marcação:', marcacao);
        // Decide como lidar com isso: retornar o texto original sem marcar, ou tentar um fallback
        return texto; // Exemplo: não aplica a marcação se as posições forem inválidas
    }

    const antes = texto.substring(0, inicio);
    const marcado = texto.substring(inicio, fim);
    const depois = texto.substring(fim);

    const tooltipText = marcacao.anotacao ? 
        marcacao.anotacao.replace(/"/g, '&quot;').replace(/'/g, "&#39;") : 
        (marcacao.texto_anotacao || 'Sem anotação'); // Usa texto_anotacao se data-anotacao for para o tooltip

    return antes +
        // LINHA MODIFICADA:
        `<span class="marcacao-usuario ${marcacao.tipo_marcacao}" data-anotacao="${tooltipText}" data-posicao-inicio="${inicio}" data-posicao-fim="${fim}">` +
        marcado +
        // O div.tooltip-anotacao dentro do span pode ser problemático para o TreeWalker.
        // A função processarAnotacoes já cria um tooltip global.
        // '<div class="tooltip-anotacao">' + tooltipText + '</div>' + // Considere remover esta linha se já tem tooltip global
        '</span>' +
        depois;
}

// Funções auxiliares
function encontrarElementoPai(elemento) {
    let parent = elemento.parentNode;
    while (parent && !parent.id) {
        parent = parent.parentNode;
    }
    return parent;
}

function encontrarElementoMarcado(node) {
    while (node && !node.classList?.contains('marcacao-usuario')) {
        node = node.parentNode;
    }
    return node;
}

function obterCorCss(tipo) {
    const cores = {
        'amarelo': '#ffff00',
        'verde': '#90EE90',
        'azul': '#ADD8E6'
    };
    return cores[tipo] || '#ffff00';
}

function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
/*
function removerBotaoMarcacao() {
    const botao = document.getElementById('botao-marcacao');
    if (botao) document.body.removeChild(botao);
}    */

// Adaptação específica para dispositivos móveis - melhora a experiência de seleção
function melhorarSelecaoMobile() {
    if (isMobileDevice()) {
        // Adiciona estilos específicos para melhorar a experiência em dispositivos móveis
        var style = document.createElement('style');
        style.textContent = `
            body {
                -webkit-user-select: text;
                user-select: text;
            }
            #botao-marcacao {
                transform: scale(1.2);
                transform-origin: top left;
            }
            /* Aumenta o tamanho do botão para facilitar o toque */
            #botao-marcacao button {
                min-width: 44px;
                min-height: 44px;
                margin: 0 4px;
            }
            /* Estilos adicionais para o diálogo de anotação em dispositivos móveis */
            #dialogo-anotacao {
                width: 90%;
                max-width: 500px;
            }
            #texto-anotacao {
                font-size: 16px;
                min-height: 120px;
            }
            .dialogo-botoes button {
                padding: 12px 20px !important;
                font-size: 16px !important;
            }
        `;
        document.head.appendChild(style);
    }
}

// Adiciona CSS para o sistema de anotações
function adicionarEstilosAnotacoes() {
    var style = document.createElement('style');
    style.textContent = `
        .texto-anotado {
            background-color: #FFE4B5;
            border-bottom: 1px dotted #FFA500;
            cursor: help;
            position: relative;
        }

        .texto-anotado:hover {
            background-color: #FFD700;
        }

        /* Removido os estilos do tooltip automático */
        /* .texto-anotado[data-anotacao] {
            position: relative;
        }

        .texto-anotado[data-anotacao]:hover:after {
            content: attr(data-anotacao);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            padding: 8px;
            background-color: rgba(0, 0, 0, 0.8);
            color: white;
            border-radius: 4px;
            font-size: 14px;
            white-space: pre-wrap;
            max-width: 300px;
            min-width: 150px;
            text-align: center;
            z-index: 1000;
            margin-bottom: 5px;
        }

        .texto-anotado[data-anotacao]:hover:before {
            content: '';
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 6px solid transparent;
            border-top-color: rgba(0, 0, 0, 0.8);
            margin-bottom: -1px;
        } */

        #dialogo-anotacao textarea {
            resize: vertical;
            font-family: Arial, sans-serif;
        }

        /* Adiciona ícone de dica visual */
        .texto-anotado::after {
            content: "\\f075";
            font-family: "Font Awesome 5 Free";
            font-size: 8px;
            position: absolute;
            top: -8px;
            right: -4px;
            color: #FFA500;
        }
    `;
    document.head.appendChild(style);
}

// Adiciona um botão para mostrar todas as anotações
function adicionarBotaoListarAnotacoes() {
    var barraFerramentas = document.getElementById('barra-ferramentas');
    if (!barraFerramentas) return;

    var botaoListar = document.createElement('button');
    botaoListar.id = 'listar-anotacoes';
    botaoListar.innerHTML = '<i class="fas fa-list-alt"></i> Ver todas anotações';
    botaoListar.title = 'Listar todas as anotações';
    botaoListar.style.backgroundColor = '#f8f8f8';
    botaoListar.style.border = '1px solid #ddd';
    botaoListar.style.padding = '5px 10px';
    botaoListar.style.borderRadius = '3px';
    botaoListar.style.cursor = 'pointer';
    botaoListar.style.display = 'flex';
    botaoListar.style.alignItems = 'center';
    botaoListar.style.fontSize = '14px';
    botaoListar.style.marginRight = '10px';

    botaoListar.querySelector('i').style.marginRight = '5px';
    botaoListar.querySelector('i').style.color = '#FFA500';

    botaoListar.addEventListener('click', mostrarListaAnotacoes);

    barraFerramentas.insertBefore(botaoListar, barraFerramentas.firstChild);
}

// Função para notificar erros
function notificarErro(mensagem) {
    const notification = document.createElement('div');
    notification.className = 'erro-notificacao';
    notification.textContent = mensagem;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #ff4444;
        color: white;
        padding: 10px 20px;
        border-radius: 4px;
        z-index: 1000;
    `;

    document.body.appendChild(notification);
    setTimeout(() => notification.remove(), 3000);
}

// Variável global para armazenar o CSRF token
window.csrfToken = null;

// Função para obter o CSRF token do backend
function obterCsrfToken() {
    return fetch('csrf_token.php')
        .then(res => res.json())
        .then(data => {
            window.csrfToken = data.csrf_token;
        });
}
// Função para verificar se as marcações foram aplicadas corretamente
function verificarMarcacoes() {
    const marcacoes = document.querySelectorAll('.marcacao-usuario');
    console.log(`Encontradas ${marcacoes.length} marcações`);
    
    marcacoes.forEach((marcacao, index) => {
        console.log(`Marcação ${index + 1}:`, {
            texto: marcacao.textContent,
            anotacao: marcacao.dataset.anotacao,
            classe: marcacao.className
        });
    });
}

// 1. Adicionar verificação de inicialização
// Agora, garantir que o CSRF token seja obtido antes de inicializar o sistema

document.addEventListener('DOMContentLoaded', function() {
    obterCsrfToken().then(() => {
        inicializarSistemaMarcacao();
    });
});

// Manter uma versão simplificada da função gerenciarSelecao
function gerenciarSelecao(e) {
    // Agora só verifica se há texto selecionado, sem mostrar botão flutuante
    const selecao = window.getSelection();
    const textoSelecionado = selecao.toString().trim();

    // Se não houver texto selecionado, não faz nada
    if (!textoSelecionado) {
        return;
    }
}

// Manter uma versão simplificada do handleSelectionEnd
function handleSelectionEnd(e) {
    // Função mantida para compatibilidade, mas sem funcionalidade
    return;
}

// Manter handleTapOutside para fechar diálogos se necessário
function handleTapOutside(e) {
    const dialogos = document.querySelectorAll('.dialogo-anotacao');
    dialogos.forEach(dialogo => {
        if (!dialogo.contains(e.target)) {
            dialogo.style.display = 'none';
        }
    });
}

// Atualizar a função de inicialização
function inicializarSistemaMarcacao() {
    console.log('Inicializando sistema de marcação...');
    
    criarBarraLateral();
    
    // Manter os event listeners necessários
    document.addEventListener('mouseup', gerenciarSelecao);
    document.addEventListener('touchend', gerenciarSelecao);
    
    // Event listeners para cliques fora
    document.addEventListener('mousedown', handleTapOutside);
    document.addEventListener('touchstart', handleTapOutside);
    
    // Atualizar quando houver redimensionamento
    let timeout;
    window.addEventListener('resize', () => {
        clearTimeout(timeout);
        timeout = setTimeout(criarBarraLateral, 250);
    });

    // Atalho de teclado para esconder/mostrar a barra (Ctrl/Cmd + Shift + H)
    document.addEventListener('keydown', (e) => {
        if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key.toLowerCase() === 'h') {
            e.preventDefault();
            const barra = document.getElementById('barra-marcacao');
            if (barra) {
                barra.style.display = barra.style.display === 'none' ? 'block' : 'none';
            }
        }
    });

    // Carrega as marcações existentes
    carregarMarcacoes().then(marcacoesPorElemento => {
        console.log('Marcações carregadas:', marcacoesPorElemento);
        
        // Carrega as anotações
        if (typeof carregarAnotacoes === 'function') {
            carregarAnotacoes(marcacoesPorElemento);
        }
    }).catch(error => {
        console.error('Erro ao carregar marcações:', error);
    });
}

// Atualizar a inicialização do sistema
document.addEventListener('DOMContentLoaded', () => {
    obterCsrfToken().then(() => {
        inicializarSistemaMarcacao();
    });
});    

// Função auxiliar para processar anotações
function processarAnotacoes(anotacoes, marcacoesPorElemento = new Map()) {
    console.log('Processando anotações:', anotacoes);

    // Remover TODOS os tooltips existentes
    document.querySelectorAll('.tooltip-anotacao').forEach(tooltip => tooltip.remove());

    // Criar um único tooltip que será reutilizado
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip-anotacao';
    document.body.appendChild(tooltip);

    // Funções auxiliares para tooltip
    function mostrarTooltip(e, span) {
        const rect = span.getBoundingClientRect();
        const anotacao = span.dataset.anotacao || '';
        
        if (anotacao) {
            tooltip.textContent = anotacao;
            tooltip.style.display = 'block';
            
            const tooltipRect = tooltip.getBoundingClientRect();
            const top = rect.top - tooltipRect.height - 10;
            const left = rect.left + (rect.width - tooltipRect.width) / 2;
            
            tooltip.style.top = `${top}px`;
            tooltip.style.left = `${left}px`;
        }
    }

    function esconderTooltip() {
        tooltip.style.display = 'none';
    }

    // Agrupar anotações e marcações por elemento_id
    const elementosModificados = new Map();
    
    // Primeiro, agrupe todas as anotações
    anotacoes.forEach(anotacao => {
        if (!elementosModificados.has(anotacao.elemento_id)) {
            elementosModificados.set(anotacao.elemento_id, {
                anotacoes: [],
                marcacoes: []
            });
        }
        elementosModificados.get(anotacao.elemento_id).anotacoes.push(anotacao);
    });

    // Depois, agrupe todas as marcações
    marcacoesPorElemento.forEach((marcacoes, elemento_id) => {
        if (!elementosModificados.has(elemento_id)) {
            elementosModificados.set(elemento_id, {
                anotacoes: [],
                marcacoes: []
            });
        }
        elementosModificados.get(elemento_id).marcacoes.push(...marcacoes);
    });

    // Processa cada elemento
    elementosModificados.forEach((dados, elemento_id) => {
        const elemento = document.getElementById(elemento_id);
        if (!elemento) return;

        const textoOriginal = elemento.textContent;
        let ranges = [];

        // Adiciona ranges para marcações
        dados.marcacoes.forEach(marcacao => {
            ranges.push({
                inicio: parseInt(marcacao.posicao_inicio),
                fim: parseInt(marcacao.posicao_fim),
                html: `<span class="marcacao-usuario ${marcacao.tipo_marcacao}" 
                       style="background-color: ${obterCorCss(marcacao.tipo_marcacao)};"
                       data-posicao-inicio="${marcacao.posicao_inicio}" 
                       data-posicao-fim="${marcacao.posicao_fim}">`,
                isStart: true,
                prioridade: 1
            });
            ranges.push({
                inicio: parseInt(marcacao.posicao_fim),
                html: '</span>',
                isStart: false,
                prioridade: 1
            });
        });

        // Adiciona ranges para anotações
        dados.anotacoes.forEach(anotacao => {
            ranges.push({
                inicio: parseInt(anotacao.posicao_inicio),
                fim: parseInt(anotacao.posicao_fim),
                html: `<span class="texto-anotado" 
                       data-anotacao="${anotacao.texto_anotacao}"
                       data-anotacao-id="${anotacao.id}">`,
                isStart: true,
                prioridade: 2
            });
            ranges.push({
                inicio: parseInt(anotacao.posicao_fim),
                html: '</span>',
                isStart: false,
                prioridade: 2
            });
        });

        // Ordena os ranges considerando prioridade e posição
        ranges.sort((a, b) => {
            if (a.inicio !== b.inicio) return b.inicio - a.inicio;
            if (a.prioridade !== b.prioridade) return a.prioridade - b.prioridade;
            return a.isStart ? -1 : 1;
        });

        // Aplica os ranges
        let html = textoOriginal;
        ranges.forEach(range => {
            html = html.slice(0, range.inicio) + range.html + html.slice(range.inicio);
        });

        elemento.innerHTML = html;

        // Adiciona eventos para as anotações
        elemento.querySelectorAll('.texto-anotado').forEach(span => {
            span.removeAttribute('title');
            span.removeAttribute('data-original-title');
            
            span.addEventListener('mouseenter', (e) => mostrarTooltip(e, span));
            span.addEventListener('mouseleave', esconderTooltip);
            
            span.addEventListener('click', () => {
                const anotacaoId = span.getAttribute('data-anotacao-id');
                if (anotacaoId) {
                    mostrarDialogoAnotacao(anotacaoId);
                }
            });
        });
    });
}

// Estilos atualizados
// Estilos atualizados (com variáveis CSS)
const styles = `
    .texto-anotado {
        background-color: #FFE4B5; /* Mantém isso, pois é específico da anotação */
        border-bottom: 1px dotted #FFA500;
        cursor: help;
        transition: background-color 0.2s;
    }

    .texto-anotado:hover {
        background-color: #FFD700;
    }

    .tooltip-anotacao {
        position: fixed;
        display: none;
        /* USA VARIÁVEIS CSS (ou Padrões Claros) */
        background-color: var(--tooltip-bg, #f9f9f9); 
        color: var(--tooltip-text, #333);
        border: 1px solid var(--tooltip-border, #ccc); /* Adiciona borda para modo claro */
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 14px;
        max-width: 300px;
        z-index: 1000;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        pointer-events: none;
        transition: opacity 0.2s, background-color 0.3s, color 0.3s, border-color 0.3s;
        opacity: 0.95;
        word-wrap: break-word;
    }

    .tooltip-anotacao::after {
        content: '';
        position: absolute;
        bottom: -6px;
        left: 50%;
        transform: translateX(-50%);
        border-width: 6px 6px 0;
        border-style: solid;
        /* USA VARIÁVEL CSS (ou Padrão Claro) */
        border-color: var(--tooltip-bg, #f9f9f9) transparent transparent transparent; 
        transition: border-color 0.3s;
    }
`;

// Adicionar estilos ao documento
const styleSheet = document.createElement('style');
styleSheet.textContent = styles;
document.head.appendChild(styleSheet);

// Função para carregar marcações
async function carregarMarcacoes() {
    try {
        const response = await fetch('carregar_marcacoes.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                pagina_url: window.location.pathname
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const marcacoes = await response.json();
        console.log('Marcações carregadas:', marcacoes);
        // Apenas processa e retorna agrupado, não aplica no DOM
        const marcacoesPorElemento = processarMarcacoes(marcacoes);
        return marcacoesPorElemento;

    } catch (error) {
        console.error('Erro ao carregar marcações:', error);
        notificarErro('Erro ao carregar marcações. Por favor, recarregue a página.');
        return new Map();
    }
}

// Função para carregar anotações
async function carregarAnotacoes(marcacoesPorElemento = new Map()) {
    try {
        const response = await fetch(`carregar_anotacoes.php?pagina_url=${encodeURIComponent(window.location.pathname)}`);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const responseData = await response.json();
        if (!responseData.sucesso) {
            throw new Error(responseData.erro || 'Erro ao carregar anotações');
        }

        const anotacoes = responseData.dados;
        // Sempre chama processarAnotacoes, mesmo se não houver anotações
        processarAnotacoes(anotacoes || [], marcacoesPorElemento);
    } catch (error) {
        console.error('Erro ao carregar anotações:', error);
        notificarErro('Erro ao carregar anotações. Por favor, recarregue a página.');
    }
}

// Função principal para carregar dados
async function carregarDados() {
    try {
        // Primeiro carrega as marcações agrupadas
        const marcacoesPorElemento = await carregarMarcacoes();
        console.log('Marcações processadas:', marcacoesPorElemento);
        // Depois carrega as anotações e aplica tudo junto
        await carregarAnotacoes(marcacoesPorElemento);
    } catch (error) {
        console.error('Erro ao carregar dados:', error);
        notificarErro('Erro ao carregar dados. Por favor, recarregue a página.');
    }
}

// Event listener para carregar quando a página estiver pronta
document.addEventListener('DOMContentLoaded', carregarDados);

function aplicarAnotacaoNoTexto(parentElement, anotacao, anotacaoElement, posicoesOcupadas) {
    const textNodes = [];
    const walker = document.createTreeWalker(
        parentElement,
        NodeFilter.SHOW_TEXT,
        null,
        false
    );

    let node;
    while (node = walker.nextNode()) {
        textNodes.push(node);
    }

    let posicaoAtual = 0;
    for (let i = 0; i < textNodes.length; i++) {
        const node = textNodes[i];
        const texto = node.textContent;
        
        if (posicaoAtual + texto.length >= anotacao.posicao_inicio) {
            const inicio = anotacao.posicao_inicio - posicaoAtual;
            const fim = Math.min(texto.length, anotacao.posicao_fim - posicaoAtual);
            
            if (inicio >= 0 && fim > inicio) {
                const range = document.createRange();
                range.setStart(node, inicio);
                range.setEnd(node, fim);
                range.surroundContents(anotacaoElement);
                break;
            }
        }
        posicaoAtual += texto.length;
    }
}

// Função para mostrar mensagem elegante
function mostrarMensagem(mensagem, tipo = 'sucesso') {
    // Remove mensagem anterior se existir
    const mensagemAnterior = document.querySelector('.mensagem-flutuante');
    if (mensagemAnterior) {
        mensagemAnterior.remove();
    }

    const div = document.createElement('div');
    div.className = 'mensagem-flutuante';
    
    // Estilos base
    let estilos = `
        fixed top-4 right-4 px-6 py-4 rounded-lg shadow-lg
        flex items-center gap-3 z-50
        transform transition-all duration-300 ease-in-out
        animate-slide-in
    `;
    
    // Estilos específicos por tipo
    if (tipo === 'sucesso') {
        estilos += `
            bg-green-50 text-green-800 border-l-4 border-green-500
        `;
    } else {
        estilos += `
            bg-red-50 text-red-800 border-l-4 border-red-500
        `;
    }
    
    div.className = estilos;

    // Ícone
    const icone = tipo === 'sucesso' ? '✓' : '✕';
    
    div.innerHTML = `
        <span class="text-xl font-bold">${icone}</span>
        <p class="font-medium">${mensagem}</p>
    `;

    document.body.appendChild(div);

    // Adicionar estilos de animação
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
        
        .animate-slide-in {
            animation: slideIn 0.3s ease-out forwards;
        }
        
        .animate-slide-out {
            animation: slideOut 0.3s ease-in forwards;
        }
    `;
    document.head.appendChild(style);

    // Remover após 3 segundos
    setTimeout(() => {
        div.classList.add('animate-slide-out');
        setTimeout(() => {
            div.remove();
            style.remove();
        }, 300);
    }, 3000);
}

// Implementação da função debounce
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Agora podemos usar o debounce para salvar marcações
const salvarMarcacoesDebounced = debounce(async () => {
    const marcacoes = [];
    alteracoesPendentes.forEach(elementoId => {
        const elemento = document.getElementById(elementoId);
        if (elemento) {
            elemento.querySelectorAll('.marcacao-usuario').forEach(marcacao => {
                marcacoes.push({
                    elemento_id: elementoId,
                    posicao_inicio: parseInt(marcacao.dataset.posicaoInicio),
                    posicao_fim: parseInt(marcacao.dataset.posicaoFim),
                    tipo_marcacao: Array.from(marcacao.classList)
                        .find(c => ['verde', 'azul', 'amarela'].includes(c))
                });
            });
        }
    });
    
    if (marcacoes.length > 0) {
        await salvarMarcacoes(marcacoes);
        alteracoesPendentes.clear();
    }
}, 1000);

// Adicionar atalhos de teclado
document.addEventListener('keydown', (e) => {
    // Ctrl/Cmd + 1/2/3 para diferentes cores de marcação
    if ((e.ctrlKey || e.metaKey) && ['1', '2', '3'].includes(e.key)) {
        e.preventDefault();
        const cores = {
            '1': 'amarela',
            '2': 'verde',
            '3': 'azul'
        };
        marcarTexto(cores[e.key]);
    }
    
    // Ctrl/Cmd + A para adicionar anotação
    if ((e.ctrlKey || e.metaKey) && e.key === 'a') {
        e.preventDefault();
        const selecao = window.getSelection();
        if (selecao.toString().trim()) {
            criarAnotacao();
        }
    }
});

// Adicionar tooltip com preview da anotação
function mostrarTooltipAnotacao(elemento) {
    const tooltip = document.createElement('div');
    tooltip.className = 'tooltip-anotacao';
    tooltip.innerHTML = `
        <div class="tooltip-content">
            <div class="tooltip-text">${elemento.dataset.anotacao || 'Sem texto'}</div>
            <div class="tooltip-actions">
                <button onclick="editarAnotacao('${elemento.dataset.anotacaoId}')">
                    <i class="fas fa-edit"></i>
                </button>
                <button onclick="excluirAnotacao('${elemento.dataset.anotacaoId}')">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `;
    document.body.appendChild(tooltip);
    
    // Posicionar tooltip
    const rect = elemento.getBoundingClientRect();
    tooltip.style.top = `${rect.bottom + 5}px`;
    tooltip.style.left = `${rect.left}px`;
}

// Adicionar sistema de tags para anotações
async function adicionarTagAnotacao(anotacaoId, tag) {
    const tags = new Set(elemento.dataset.tags?.split(',') || []);
    tags.add(tag);
    elemento.dataset.tags = Array.from(tags).join(',');
    await salvarTags(anotacaoId, Array.from(tags));
}

// Adicionar busca por texto nas anotações


// Adicionar histórico de alterações
function registrarAlteracao(tipo, dados) {
    const historico = JSON.parse(localStorage.getItem('historico_anotacoes') || '[]');
    historico.push({
        tipo,
        dados,
        timestamp: new Date().toISOString()
    });
    localStorage.setItem('historico_anotacoes', JSON.stringify(historico));
}

// Adicionar o HTML do campo de busca (pode ser inserido via JavaScript)
function adicionarCampoBusca() {
    const searchContainer = document.createElement('div');
    searchContainer.className = 'search-container';
    searchContainer.innerHTML = `
        <div class="search-box">
            <input 
                type="text" 
                id="busca-anotacoes" 
                placeholder="Buscar em anotações..."
                class="search-input"
            >
            <i class="fas fa-search search-icon"></i>
        </div>
    `;
    
    // Inserir no início do documento ou em um local específico
    document.body.insertBefore(searchContainer, document.body.firstChild);
}

// Melhorar a função de busca com mais recursos
function buscarAnotacoes(termo) {
    if (!termo.trim()) {
        // Se a busca estiver vazia, restaura todas as anotações ao estado original
        document.querySelectorAll('[data-anotacao-id]').forEach(anotacao => {
            anotacao.style.backgroundColor = '';
            anotacao.style.display = '';
        });
        return;
    }

    const anotacoes = document.querySelectorAll('[data-anotacao-id]');
    let encontradas = 0;

    anotacoes.forEach(anotacao => {
        const textoAnotado = anotacao.textContent.toLowerCase();
        const textoComentario = anotacao.dataset.anotacao?.toLowerCase() || '';
        const tags = anotacao.dataset.tags?.toLowerCase() || '';
        
        const visivel = textoAnotado.includes(termo.toLowerCase()) || 
                       textoComentario.includes(termo.toLowerCase()) || 
                       tags.includes(termo.toLowerCase());
        
        anotacao.style.backgroundColor = visivel ? '#ffeb3b' : '';
        anotacao.style.display = visivel ? '' : 'none';
        
        if (visivel) encontradas++;
    });

    // Mostrar feedback da busca
    mostrarMensagem(`${encontradas} anotação(ões) encontrada(s)`, 'info');
}

// Adicionar eventos de busca
function inicializarBusca() {
    adicionarCampoBusca();
    
    const inputBusca = document.getElementById('busca-anotacoes');
    
    // Usar debounce para não sobrecarregar com muitas buscas
    const buscarComDebounce = debounce((termo) => {
        buscarAnotacoes(termo);
    }, 300);

    inputBusca.addEventListener('input', (e) => {
        buscarComDebounce(e.target.value);
    });

    // Adicionar atalho de teclado (Ctrl/Cmd + F)
    document.addEventListener('keydown', (e) => {
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            inputBusca.focus();
        }
    });
}

// Estilos CSS para o campo de busca
const estilos = `
    .search-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        background-color: var(--toolbar-bg);
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 10px;
    }

    .search-box {
        position: relative;
        display: flex;
        align-items: center;
    }

    .search-input {
        padding: 8px 35px 8px 15px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        width: 250px;
        transition: all 0.3s ease;
    }

    .search-input:focus {
        outline: none;
        border-color: #4a90e2;
        box-shadow: 0 0 0 2px rgba(74,144,226,0.2);
    }

    .search-icon {
        position: absolute;
        right: 10px;
        color: #666;
        pointer-events: none;
    }
`;

// Adicionar os estilos ao documento
function adicionarEstilos() {
    const style = document.createElement('style');
    style.textContent = estilos;
    document.head.appendChild(style);
}

// Inicializar o sistema de busca
document.addEventListener('DOMContentLoaded', () => {
    adicionarEstilos();
    inicializarBusca();
});

// Função para criar barra fixa de marcação para mobile
function criarBarraFixaMobile() {
    // Remove barra existente se houver
    const barraExistente = document.getElementById('barra-marcacao-mobile');
    if (barraExistente) {
        barraExistente.remove();
    }

    const barraFixa = document.createElement('div');
    barraFixa.id = 'barra-marcacao-mobile';
    
    // Estilos da barra
    Object.assign(barraFixa.style, {
        position: 'fixed',
        top: '50%',
        transform: 'translateY(-50%)',
        right: '0',
        zIndex: '9999',
        backgroundColor: '#ffffff',
        boxShadow: '-2px 0 10px rgba(0,0,0,0.1)',
        padding: '10px',
        borderRadius: '10px 0 0 10px'
    });

    // HTML da barra com os botões
    barraFixa.innerHTML = `
        <div class="botoes-marcacao-mobile" style="display: flex; flex-direction: column; gap: 15px;">
            <button id="marcar-amarelo" class="btn-marcacao-mobile" title="Marcar em amarelo">
                <i class="fas fa-highlighter" style="color: #ffffc3;"></i>
            </button>
            <button id="marcar-verde" class="btn-marcacao-mobile" title="Marcar em verde">
                <i class="fas fa-highlighter" style="color: #d4ffd4;"></i>
            </button>
            <button id="marcar-azul" class="btn-marcacao-mobile" title="Marcar em azul">
                <i class="fas fa-highlighter" style="color: #d4f4ff;"></i>
            </button>
            <button id="fazer-anotacao" class="btn-marcacao-mobile" title="Adicionar anotação">
                <i class="fas fa-comment-alt"></i>
            </button>
            <button id="remover-marcacao" class="btn-marcacao-mobile" title="Remover marcação">
                <i class="fas fa-eraser"></i>
            </button>
        </div>
    `;

    // Estiliza os botões
    const botoes = barraFixa.getElementsByClassName('btn-marcacao-mobile');
    Array.from(botoes).forEach(botao => {
        Object.assign(botao.style, {
            width: '40px',
            height: '40px',
            borderRadius: '20px',
            border: 'none',
            backgroundColor: '#f5f5f5',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '16px',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            margin: '0'
        });

        botao.onmouseover = function() {
            this.style.backgroundColor = '#e0e0e0';
            this.style.transform = 'scale(1.1)';
        };
        
        botao.onmouseout = function() {
            this.style.backgroundColor = '#f5f5f5';
            this.style.transform = 'scale(1)';
        };
    });

    // Adiciona os event listeners usando suas funções existentes
    barraFixa.addEventListener('click', function(e) {
        const target = e.target.closest('button');
        if (!target) return;

        const selecao = window.getSelection();
        if (!selecao.toString().trim()) {
            mostrarMensagem('Selecione um texto primeiro', 'info');
            return;
        }

        switch(target.id) {
            case 'marcar-amarelo':
                marcarSelecao('amarelo');
                break;
            case 'marcar-verde':
                marcarSelecao('verde');
                break;
            case 'marcar-azul':
                marcarSelecao('azul');
                break;
            case 'fazer-anotacao':
                criarAnotacao();
                break;
            case 'remover-marcacao':
                removerMarcacao();
                break;
        }
    });

    document.body.appendChild(barraFixa);
}

// Função para inicializar a barra
function inicializarBarraLateral() {
    criarBarraFixaMobile();
    
    // Atualizar quando houver redimensionamento
    window.addEventListener('resize', () => {
        criarBarraFixaMobile();
    });
}

// Adicionar à inicialização existente
document.addEventListener('DOMContentLoaded', () => {
    inicializarBarraLateral();
});

function criarBarraLateral() {
    // Remove barra existente se houver
    const barraExistente = document.getElementById('barra-marcacao');
    if (barraExistente) {
        barraExistente.remove();
    }

    const barraFixa = document.createElement('div');
    barraFixa.id = 'barra-marcacao';
    
    // Estilos da barra
    Object.assign(barraFixa.style, {
        position: 'fixed',
        top: '50%',
        transform: 'translateY(-50%)',
        right: '0',
        zIndex: '9999',
       
        boxShadow: '-2px 0 10px rgba(0,0,0,0.1)',
        padding: '10px',
        borderRadius: '10px 0 0 10px',
        transition: 'all 0.3s ease'
    });

    // HTML da barra com os botões
    barraFixa.innerHTML = `
        <div class="botoes-marcacao" style="display: flex; flex-direction: column; gap: 15px;">
            <button id="marcar-amarelo" class="btn-marcacao" title="Marcar em amarelo">
                <i class="fas fa-highlighter" style="color: #FFD700;"></i>
            </button>
            <button id="marcar-verde" class="btn-marcacao" title="Marcar em verde">
                <i class="fas fa-highlighter" style="color: #90EE90;"></i>
            </button>
            <button id="marcar-azul" class="btn-marcacao" title="Marcar em azul">
                <i class="fas fa-highlighter" style="color: #87CEEB;"></i>
            </button>
            <button id="fazer-anotacao" class="btn-marcacao" title="Adicionar anotação">
                <i class="fas fa-comment-alt" style="color: #666;"></i>
            </button>
            <button id="remover-marcacao" class="btn-marcacao" title="Remover marcação">
                <i class="fas fa-eraser" style="color: #FF6B6B;"></i>
            </button>
        </div>
    `;

    // Adiciona estilos CSS
    const style = document.createElement('style');
    style.textContent = `
        .btn-marcacao {
            width: 40px;
            height: 40px;
            border-radius: 20px;
            border: none;
            background-color: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .btn-marcacao:hover {
            background-color: #e0e0e0;
            transform: scale(1.1);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .btn-marcacao:active {
            transform: scale(0.95);
        }

        .btn-marcacao i {
            transition: all 0.3s ease;
        }

        .btn-marcacao:hover i {
            transform: rotate(-10deg);
        }

        @media (max-width: 768px) {
            #barra-marcacao {
                padding: 8px;
            }
            
            .btn-marcacao {
                width: 35px;
                height: 35px;
                font-size: 14px;
            }
        }
    `;
    document.head.appendChild(style);

    // Adiciona os event listeners
    barraFixa.addEventListener('click', function(e) {
        const target = e.target.closest('button');
        if (!target) return;

        const selecao = window.getSelection();
        if (!selecao.toString().trim()) {
            mostrarMensagem('Selecione um texto primeiro', 'info');
            return;
        }

        // Efeito de clique no botão
        target.style.transform = 'scale(0.95)';
        setTimeout(() => {
            target.style.transform = 'scale(1)';
        }, 100);

        switch(target.id) {
            case 'marcar-amarelo':
                marcarSelecao('amarelo');
                break;
            case 'marcar-verde':
                marcarSelecao('verde');
                break;
            case 'marcar-azul':
                marcarSelecao('azul');
                break;
            case 'fazer-anotacao':
                criarAnotacao();
                break;
            case 'remover-marcacao':
                removerMarcacao();
                break;
        }
    });

    document.body.appendChild(barraFixa);
}

/* Função para inicializar
function inicializarSistemaMarcacao() {
    criarBarraLateral();
    
    // Atualizar quando houver redimensionamento
    let timeout;
    window.addEventListener('resize', () => {
        clearTimeout(timeout);
        timeout = setTimeout(criarBarraLateral, 250);
    });

    // Atalho de teclado para esconder/mostrar a barra (Ctrl/Cmd + Shift + H)
    document.addEventListener('keydown', (e) => {
        if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key.toLowerCase() === 'h') {
            e.preventDefault();
            const barra = document.getElementById('barra-marcacao');
            if (barra) {
                barra.style.display = barra.style.display === 'none' ? 'block' : 'none';
            }
        }
    });
} */

// Inicializar quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', inicializarSistemaMarcacao);





