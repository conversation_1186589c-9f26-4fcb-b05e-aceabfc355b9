<?php
include_once 'funcoes.php';

if (!isset($id_planejamento) || !isset($id_usuario)) {
    die("ID do planejamento ou ID do usuário não definido.");
}

$query_consultar_materias_planejamento = "
    SELECT pm.materia_idmateria, m.nome AS nome_materia, m.cor AS cor_materia
    FROM appEstudo.planejamento_materia pm
    INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    WHERE pm.planejamento_idplanejamento = $id_planejamento";

$resultado_materias_planejamento = pg_query($conexao, $query_consultar_materias_planejamento);

$materias_no_planejamento = array();
$cores_materias = array();

while ($row = pg_fetch_assoc($resultado_materias_planejamento)) {
    $materia_id = $row['materia_idmateria'];
    $nome_materia = $row['nome_materia'];
    $cor_materia = $row['cor_materia'];

    $materias_no_planejamento[$materia_id] = $nome_materia;
    $cores_materias[$nome_materia] = $cor_materia;
}

$query_consulta_questoes = "
    SELECT e.materia_idmateria, 
           m.nome AS nome_materia,
           SUM(e.q_total) AS total_questoes,
           SUM(e.q_certa) AS total_certas, 
           SUM(e.q_errada) AS total_erradas
    FROM appEstudo.estudos e
    INNER JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria
    WHERE e.planejamento_usuario_idusuario = $id_usuario
      AND e.metodo = 'Questões'
    GROUP BY e.materia_idmateria, m.nome";

$resultado_consulta_questoes = pg_query($conexao, $query_consulta_questoes);

$questoes_por_materia = array();

while ($row = pg_fetch_assoc($resultado_consulta_questoes)) {
    $materia = $row['nome_materia'];
    $total_questoes = $row['total_questoes'];
    $total_certas = $row['total_certas'];
    $total_erradas = $row['total_erradas'];

    $questoes_por_materia[$materia] = array(
        'total' => (int)$total_questoes,
        'certas' => (int)$total_certas,
        'erradas' => (int)$total_erradas
    );
}

$dados_json = json_encode($questoes_por_materia);
$cores_json = json_encode($cores_materias);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        .container {
            max-width: 1400px;
            margin: 0 auto;
            border-radius: 15px;
        }

        .estatistica-header {
            background: #800020;
            padding: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }

        .estatistica-titulo {
            font-size: 1.2rem;
            font-weight: 600;
            letter-spacing: 0.5px;
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            margin-bottom: 0.5rem;
        }

        .estatistica-subtitulo {
            font-size: 0.9rem;
            opacity: 0.8;
            font-family: 'Courier Prime', monospace;
        }

        .chart-container {

            padding: 2rem;

        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-top: 2rem;
            padding: 2rem;
            /*background: rgba(255, 255, 255, 0.95);*/
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .stat-card {
            background: var(--hover);
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            font-family: 'Quicksand', sans-serif;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-card-materia {
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: left;
            font-family: 'Quicksand', sans-serif;
            transition: all 0.3s ease;
            border-left-width: 4px;
            border-left-style: solid;
            margin-bottom: 1rem;
            box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }

        .stat-card-materia:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .stat-card-materia::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                45deg,
                transparent,
                rgba(255, 255, 255, 0.3),
                transparent
            );
            transform: translateX(-100%);
            transition: transform 0.6s;
        }

        .stat-card-materia:hover::after {
            transform: translateX(100%);
        }

        .stat-title {
            display: flex;
            align-items: center;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            cursor: pointer;
        }

        .stat-value {
            font-size: 1.3rem;
            font-weight: 700;
            margin: 0.5rem 0;
            color: #2C3345;
        }

        .stat-value.positive {
            color: #2ecc71;
        }

        .stat-value.negative {
            color: #e74c3c;
        }

        .toggle-all-btn {
            font-family: 'Varela Round', sans-serif;
            background: var(--primary-blue);
            border: none;
            color: white;
            padding: 1rem 2rem;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            grid-column: 1 / -1;
            justify-self: center;
            font-weight: 600;
            letter-spacing: 1px;
        }

        .toggle-all-btn:hover {
            transform: translateY(-2px);
           
        }

        .materias-container {
            display: none;
            grid-column: 1 / -1;
            gap: 1rem;
            margin-top: 1rem;
        }

        .materias-container.visible {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }

        .stat-card.materia-card {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .materia-card .stat-title {
            position: relative;
            padding-right: 1.5rem;
        }

        .materia-card .stat-title::after {
            content: '▼';
            position: absolute;
            right: 0.5rem;
            top: 50%;
            transform: translateY(-50%);
            font-size: 0.8rem;
            transition: transform 0.3s ease;
        }

        .materia-card.expanded .stat-title::after {
            transform: translateY(-50%) rotate(180deg);
        }

        .materia-details {
            max-height: 0;
            overflow: hidden;
            opacity: 0;
            transition: all 0.3s ease;
            margin-top: 0;
            padding-left: 3rem;
        }

        .materia-card.expanded .materia-details {
            max-height: 200px;
            opacity: 1;
            margin-top: 1rem;
        }

        .taxa-badge {
            display: inline-block;
            padding: 0.4rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
            margin-top: 0.5rem;
            border-width: 2px;
            border-style: solid;
            transition: all 0.3s ease;
        }

        .taxa-badge.taxa-excelente {
            background-color: rgba(212, 237, 218, 0.7);
            color: #155724;
            border-color: #155724;
        }

        .taxa-badge.taxa-boa {
            background-color: rgba(255, 243, 205, 0.7);
            color: #856404;
            border-color: #856404;
        }

        .taxa-badge.taxa-regular {
            background-color: rgba(248, 215, 218, 0.7);
            color: #721c24;
            border-color: #721c24;
        }

        .stat-card .taxa-badge {
            font-size: 2rem;
            padding: 0.5rem 1.5rem;
            display: inline-block;
            margin: 0;
        }

        .ordinal-number {
            opacity: 0.8;
            font-weight: 700;
            font-size: 0.9em;
            margin-right: 1rem;
            min-width: 35px;
            text-align: left;
            display: inline-block;
        }
    </style>
</head>
<body>

    
       
            <div id="grafico" style="min-height: 600px;"></div>
            <div id="estatisticas-container" class="stats-grid"></div>
       
    


<script>
    function getOrdinal(n) {
        return n + "º";
    }

    function getTaxaClass(percentagem) {
        if (percentagem >= 80) return 'taxa-excelente';
        if (percentagem >= 60) return 'taxa-boa';
        return 'taxa-regular';
    }

    function toggleMaterias() {
        const container = document.getElementById('materias-container');
        const btn = document.querySelector('.toggle-all-btn');
        const isShowing = container.classList.toggle('visible');
        btn.textContent = isShowing ? 'Ocultar Matérias' : 'Mostrar Matérias';
        if (!isShowing) {
            document.querySelectorAll('.materia-card').forEach(card => {
                card.classList.remove('expanded');
            });
        }
    }

    function toggleMateriaDetails(card) {
        card.classList.toggle('expanded');
    }

    function initializeQuestoesGraph() {
        var questoesPorMateria = <?php echo $dados_json; ?>;
        var coresMaterias = <?php echo $cores_json; ?>;

        var materias = Object.keys(questoesPorMateria);
        var dadosProcessados = materias.map(materia => {
            let dados = questoesPorMateria[materia];
            let total = dados.total;
            let percentagemCertas = total > 0 ? Number((dados.certas / total * 100).toFixed(2)) : 0;

            return {
                materia: materia,
                certas: dados.certas,
                erradas: dados.erradas,
                total: total,
                percentagemCertas: percentagemCertas
            };
        }).sort((a, b) => b.percentagemCertas - a.percentagemCertas);

        Highcharts.chart('grafico', {
            chart: {
                type: 'column',
                backgroundColor: 'transparent',
                style: {
                    fontFamily: 'Quicksand, sans-serif'
                }
            },
            title: {
                text: null
            },
            subtitle: {
                text: null
            },
            xAxis: {
                categories: dadosProcessados.map((d, index) => `${getOrdinal(index + 1)} ${d.materia}`),
                labels: {
                    style: {
                        fontFamily: 'Quicksand, sans-serif',
                        fontSize: '12px'
                    }
                }
            },
            yAxis: [{
                title: {
                    text: 'Quantidade de Questões',
                    style: {
                        color: '#800020',
                        fontFamily: 'Varela Round, Quicksand, sans-serif'
                    }
                },
                labels: {
                    style: {
                        fontFamily: 'Quicksand, sans-serif'
                    }
                }
            }, {
                title: {
                    text: 'Percentual de Acerto',
                    style: {
                        color: '#800020',
                        fontFamily: 'Varela Round, Quicksand, sans-serif'
                    }
                },
                opposite: true,
                labels: {
                    format: '{value}%',
                    style: {
                        fontFamily: 'Quicksand, sans-serif'
                    }
                }
            }],
            tooltip: {
                shared: true,
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                borderColor: '#b8860b',
                borderWidth: 1,
                useHTML: true,
                headerFormat: '<div style="font-size: 12px; font-weight: bold; padding: 8px">{point.key}</div>',
                pointFormatter: function() {
                    let total = (this.point && this.point.options && this.point.options.total) || this.stackTotal || 0;
                    let percent = total > 0 ? (this.y / total * 100).toFixed(2) : null;

                    return `<div style="color: ${this.color}; padding: 4px">
        <b>${this.series.name}:</b> ${this.y} ${percent !== null && percent > 0 ? `(${percent}%)` : ''}<br/>
        ${total > 0 ? `` : ''}
    </div>`;
                }



,
                style: {
                    fontFamily: 'Quicksand, sans-serif'
                }
            },
            plotOptions: {
                column: {
                    stacking: 'normal',
                    borderWidth: 0,
                    borderRadius: 4,
                    dataLabels: {
                        enabled: true,
                        formatter: function() {
                            let total = this.total || this.point.stackTotal;
                            let percent = total > 0 ? (this.y / total * 100).toFixed(2) : null;

                            // Retorna o valor com % apenas se percent não for null e maior que zero
                            return percent !== null && percent > 0 ? `${this.y} (${percent}%)` : this.y;
                        },
                        style: {
                            fontFamily: 'Quicksand, sans-serif',
                            textOutline: 'none'
                        }
                    }



                }
            },
            series: [{
                name: 'Acertos',
                data: dadosProcessados.map(d => ({
                    y: d.certas,
                    color: 'rgba(40, 167, 69, 0.7)'
                }))
            }, {
                name: 'Erros',
                data: dadosProcessados.map(d => ({
                    y: d.erradas,
                    color: 'rgba(220, 53, 69, 0.7)'
                }))
            }, {
                name: 'Taxa de Acerto',
                type: 'spline',
                yAxis: 1,
                data: dadosProcessados.map(d => ({
                    y: d.percentagemCertas,
                    color: '#b8860b'
                })),
                marker: {
                    lineWidth: 2,
                    lineColor: '#b8860b',
                    fillColor: 'white'
                }
            }],
            credits: {
                enabled: false
            }
        });

        const statsContainer = document.querySelector('.stats-grid');
        const totalQuestoes = dadosProcessados.reduce((acc, curr) => acc + curr.total, 0);
        const totalCertas = dadosProcessados.reduce((acc, curr) => acc + curr.certas, 0);
        const mediaAcertos = totalQuestoes > 0 ? (totalCertas / totalQuestoes * 100).toFixed(2) : 0;

        statsContainer.innerHTML = `
            <div class="stat-card">
                <div class="stat-title">Total de Questões</div>
                <div class="stat-value">${totalQuestoes.toLocaleString()}</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Acertos Totais</div>
                <div class="stat-value positive">${totalCertas.toLocaleString()}</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Erros Totais</div>
                <div class="stat-value negative">${(totalQuestoes - totalCertas).toLocaleString()}</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Taxa de Acerto Geral</div>
                <div class="stat-value">
                    <div class="taxa-badge ${getTaxaClass(mediaAcertos)}">
                        ${mediaAcertos}%
                    </div>
                </div>
            </div>
            <button class="toggle-all-btn" onclick="toggleMaterias()">Mostrar Matérias</button>
            <div id="materias-container" class="materias-container">
                ${dadosProcessados.map((d, index) => `
                    <div class="stat-card-materia materia-card" style="border-left-color: ${coresMaterias[d.materia]}">
                        <div class="stat-title" onclick="toggleMateriaDetails(this.parentElement)">
                            <span class="ordinal-number">${getOrdinal(index + 1)}</span>
                            ${d.materia}
                        </div>
                        <div class="materia-details">
                            <div class="stat-value">
                                <div>Total: <strong>${d.total.toLocaleString()}</strong></div>
                                <div>Acertos: <strong class="positive">${d.certas.toLocaleString()}</strong></div>
                                <div>Erros: <strong class="negative">${d.erradas.toLocaleString()}</strong></div>
                                <div style="display: flex; flex-direction: column; align-items: center; margin-top: 8px;">
                                    <div style="font-weight: 500; margin-bottom: 4px;">Taxa de Acerto</div>
                                    <div class="taxa-badge ${getTaxaClass(d.percentagemCertas)}" 
                                         style="border-color: ${coresMaterias[d.materia]}">
                                        ${d.percentagemCertas}%
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('')}
            </div>
        `;
    }

    document.addEventListener('DOMContentLoaded', initializeQuestoesGraph);
</script>

</body>
</html>
