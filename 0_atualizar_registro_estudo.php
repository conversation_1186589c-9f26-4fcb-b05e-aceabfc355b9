<?php
    // Conexão com o banco de dados
    include_once 'conexao_POST.php';
header('Content-Type: application/json');


try {
    // Verificar se todos os campos necessários estão presentes
    $required_fields = ['idestudo', 'data', 'ponto_estudado', 'tempo_liquido'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field])) {
            throw new Exception("Campo obrigatório ausente: $field");
        }
    }

    // Debug
    error_log("Dados recebidos: " . print_r($_POST, true));



    // Preparar os dados
    $id = $_POST['idestudo'];
    $data = $_POST['data'];
    $ponto = $_POST['ponto_estudado'];
    $tempo = $_POST['tempo_liquido'];
    $q_total = $_POST['q_total'] ? $_POST['q_total'] : 0;
    $q_certa = $_POST['q_certa'] ? $_POST['q_certa'] : 0;
    $metodo = $_POST['metodo'];
    // Adicionar o campo descrição, permitindo que seja nulo ou uma string vazia
    $descricao = isset($_POST['descricao']) ? $_POST['descricao'] : null;
    // Adicionar o campo link_conteudo, permitindo que seja nulo ou uma string vazia
    $link_conteudo = isset($_POST['link_conteudo']) ? $_POST['link_conteudo'] : null;

    // Query de atualização
    $query = "UPDATE appEstudo.estudos
              SET data = $1::date,
                  ponto_estudado = $2,
                  tempo_liquido = $3::interval,
                  q_total = $4,
                  q_certa = $5,
                  metodo = $6,
                  descricao = $7, -- Adicionado campo descrição
                  link_conteudo = $8 -- Adicionado campo link_conteudo
              WHERE idestudos = $9 -- O índice do idestudos agora é 9
              RETURNING materia_idmateria";

    $result = pg_query_params($conexao, $query, [
        $data,
        $ponto,
        $tempo,
        $q_total,
        $q_certa,
        $metodo,
        $descricao, // Adicionado parâmetro descrição
        $link_conteudo, // Adicionado parâmetro link_conteudo
        $id
    ]);

    if (!$result) {
        throw new Exception(pg_last_error($conexao));
    }

    $row = pg_fetch_assoc($result);
    
    // Buscar o nome da matéria
    $materia_query = "SELECT nome FROM appEstudo.materia WHERE idmateria = $1";
    $materia_result = pg_query_params($conexao, $materia_query, [$row['materia_idmateria']]);
    $materia_row = pg_fetch_assoc($materia_result);

    echo json_encode([
        'success' => true,
        'message' => 'Registro atualizado com sucesso',
        'materia' => $materia_row['nome']
    ]);

} catch (Exception $e) {
    error_log("Erro na atualização: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>

