<?php
// Retorna apenas a posição do usuário logado (para uso no navbar)
session_start();
include_once "../conexao_POST.php";

if (!isset($_SESSION['idusuario'])) {
    echo '-';
    exit();
}
$id_usuario_logado = (int)$_SESSION['idusuario'];
$query = "SELECT idusuario, SUM(EXTRACT(EPOCH FROM tempo_liquido::time))/3600 AS horas_liquidas
FROM appEstudo.estudos e
JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
WHERE tempo_liquido IS NOT NULL
GROUP BY idusuario
ORDER BY horas_liquidas DESC";
$result = pg_query($conexao, $query);
if (!$result) {
    echo '-';
    exit();
}
$posicao = 1;
while ($row = pg_fetch_assoc($result)) {
    if ($row['idusuario'] == $id_usuario_logado) {
        echo $posicao;
        exit();
    }
    $posicao++;
}
echo '-';
