document.addEventListener('DOMContentLoaded', () => {
    initKanban();
    carregarTarefas(); // Carrega tarefas primeiro
    aplicarEstadoSalvoColunas(); // Aplica estados salvos ANTES de inicializar os ícones
    initCollapseIcons(); // Adicionar inicialização dos ícones de recolher
});

// Aplicar o estado salvo das colunas (expandido/recolhido) do localStorage
function aplicarEstadoSalvoColunas() {
    console.log("aplicarEstadoSalvoColunas chamada");
    const colunas = document.querySelectorAll('.kanban-column');
    colunas.forEach(coluna => {
        const status = coluna.dataset.status;
        const isRecolhida = localStorage.getItem(`coluna-${status}-recolhida`) === 'true';
        const iconElement = coluna.querySelector('.collapse-icon i');

        if (isRecolhida) {
            coluna.classList.add('coluna-recolhida');
            if (iconElement) {
                iconElement.classList.remove('fa-minus');
                iconElement.classList.add('fa-plus');
            }
            console.log(`Coluna ${status} carregada como recolhida`);
        } else {
            // Por padrão, as colunas começam expandidas (sem a classe 'coluna-recolhida')
            // e com o ícone 'fa-minus', então não precisamos fazer nada aqui 
            // a menos que queiramos explicitamente remover a classe e definir o ícone.
            coluna.classList.remove('coluna-recolhida');
            if (iconElement) {
                iconElement.classList.remove('fa-plus');
                iconElement.classList.add('fa-minus');
            }
             console.log(`Coluna ${status} carregada como expandida`);
        }
    });
}

// Inicializar ícones de expandir/recolher colunas
function initCollapseIcons() {
    console.log("initCollapseIcons chamada"); // Log 1
    const collapseIcons = document.querySelectorAll('.collapse-icon');
    console.log("Ícones encontrados:", collapseIcons.length, collapseIcons); // Log 2

    collapseIcons.forEach(iconSpan => { // Renomeado 'icon' para 'iconSpan' para clareza
        iconSpan.addEventListener('click', () => {
            console.log("Ícone clicado:", iconSpan); // Log 3
            const column = iconSpan.closest('.kanban-column');
            const iconElement = iconSpan.querySelector('i');
            
            console.log("Coluna encontrada:", column); // Log 4
            console.log("Elemento <i> encontrado:", iconElement); // Log 5
            
            if (!column) {
                console.error("ERRO: Elemento .kanban-column pai não encontrado.");
                return;
            }
            if (!iconElement) {
                console.error("ERRO: Elemento <i> dentro do ícone não encontrado.");
                return;
            }
            
            column.classList.toggle('coluna-recolhida');
            console.log("Classe 'coluna-recolhida' alternada. Classes da coluna:", column.classList.toString()); // Log 6
            
            if (column.classList.contains('coluna-recolhida')) {
                iconElement.classList.remove('fa-minus');
                iconElement.classList.add('fa-plus');
                localStorage.setItem(`coluna-${column.dataset.status}-recolhida`, 'true');
                console.log("Ícone alterado para plus, salvo como recolhida");
            } else {
                iconElement.classList.remove('fa-plus');
                iconElement.classList.add('fa-minus');
                localStorage.setItem(`coluna-${column.dataset.status}-recolhida`, 'false');
                console.log("Ícone alterado para minus, salvo como expandida");
            }
        });
    });
}

// Inicialização do Sortable.js para cada coluna
function initKanban() {
    document.querySelectorAll('.task-list').forEach(taskList => {
        new Sortable(taskList, {
            group: 'shared',
            animation: 150,
            ghostClass: 'task-ghost',
            onEnd: function(evt) {
                const taskId = evt.item.dataset.taskId;
                const novoStatus = evt.to.parentElement.dataset.status;
                atualizarStatusTarefa(taskId, novoStatus);
            }
        });
    });
}

// Carregar tarefas do servidor
async function carregarTarefas() {
    try {
        const response = await fetch('backend/listar_tarefas.php');
        if (!response.ok) {
            throw new Error('Erro ao carregar tarefas');
        }

        const tarefas = await response.json();
        //console.log('Tarefas carregadas:', tarefas); // Debug

        // Limpar listas existentes
        document.querySelectorAll('.task-list').forEach(list => list.innerHTML = '');
        
        // Distribuir tarefas nas colunas apropriadas
        if (tarefas.dados) {
            tarefas.dados.forEach(tarefa => {
                const taskElement = criarElementoTarefa(tarefa);
                const statusElement = document.querySelector(`[data-status="${tarefa.status}"] .task-list`);
                if (statusElement) {
                    statusElement.appendChild(taskElement);
                } else {
                    console.error('Coluna não encontrada para status:', tarefa.status);
                }
            });
        }
        
        // Atualizar contadores
        atualizarContadores();
    } catch (error) {
        console.error('Erro ao carregar tarefas:', error);
        mostrarNotificacao('Erro ao carregar tarefas: ' + error.message, 'error');
    }
}

// Criar elemento HTML para uma tarefa
function criarElementoTarefa(tarefa) {
    const div = document.createElement('div');
    div.className = 'task-card';
    div.dataset.taskId = tarefa.id;
    
    const prioridadeCores = {
        alta: '#ff4444',
        media: '#ffbb33',
        baixa: '#00C851'
    };
    
    // Calcular dias restantes
    const diasRestantes = calcularDiasRestantes(tarefa.data_limite);
    const classeUrgencia = obterClasseUrgencia(diasRestantes);
    
    // Criar um elemento temporário para limpar o HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = tarefa.descricao || '';
    
    // Remover scripts por segurança
    const scripts = tempDiv.querySelectorAll('script');
    scripts.forEach(script => script.remove());
    
    div.innerHTML = `
        <h3>${tarefa.titulo}</h3>
        <div class="task-content">
            <div class="task-description">${tempDiv.innerHTML}</div>
            <div class="task-meta">
                <span class="task-priority" style="color: ${prioridadeCores[tarefa.prioridade]}">
                    <i class="fas fa-flag"></i> ${tarefa.prioridade}
                </span>
                <span class="task-date">
                    <i class="far fa-calendar"></i> ${formatarData(tarefa.data_limite)}
                </span>
                <span class="task-deadline ${classeUrgencia}">
                    <i class="fas fa-hourglass-half"></i> ${formatarDiasRestantes(diasRestantes)}
                </span>
            </div>
        </div>
        <div class="task-actions">
            <button onclick="editarTarefa(${tarefa.id})" class="btn-icon">
                <i class="fas fa-edit"></i>
            </button>
            <button onclick="excluirTarefa(${tarefa.id})" class="btn-icon">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    
    return div;
}

// Atualizar status de uma tarefa
async function atualizarStatusTarefa(taskId, novoStatus) {
    try {
        const response = await fetch('backend/atualizar_status.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
                id: taskId, 
                status: novoStatus 
            })
        });
        
        const data = await response.json();
        
        if (!response.ok || !data.success) {
            throw new Error(data.erro || 'Erro ao atualizar status');
        }
        
        atualizarContadores();
        mostrarNotificacao('Status atualizado com sucesso');
    } catch (error) {
        console.error('Erro:', error);
        mostrarNotificacao('Erro ao atualizar status', 'error');
        
        // Recarregar tarefas para reverter alterações visuais em caso de erro
        await carregarTarefas();
    }
}

// Abrir modal para nova tarefa
function abrirModalTarefa() {
    abrirModalTarefaComDados(null, null);
}

// Função para fechar o modal - melhorada para limpar listeners
function fecharModal() {
    // Remover a instância do TinyMCE se existir
    if (tinymce.get('descricao')) {
        tinymce.remove('#descricao');
    }
    
    const modalOverlay = document.querySelector('.modal-overlay');
    if (modalOverlay) {
        // Remover o listener de tecla ESC se existir
        if (modalOverlay.escKeyListener) {
            document.removeEventListener('keydown', modalOverlay.escKeyListener);
        }
        
        // Remover o modal do DOM
        modalOverlay.remove();
    }
}

// Funções auxiliares
function formatarData(dataString) {
    if (!dataString) return 'Sem data';
    
    // Ajuste para o fuso horário do Brasil (UTC-3)
    const [ano, mes, dia] = dataString.split('-');
    const data = new Date(ano, mes - 1, dia);
    
    // Formatar no padrão brasileiro
    return data.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        timeZone: 'America/Sao_Paulo'
    });
}

function atualizarContadores() {
    const colunas = ['backlog', 'em_andamento', 'concluido', 'arquivado'];
    
    colunas.forEach(status => {
        const coluna = document.querySelector(`[data-status="${status}"]`);
        if (coluna) {
            const contador = coluna.querySelector('.task-count');
            const quantidade = coluna.querySelector('.task-list').children.length;
            if (contador) {
                contador.textContent = quantidade;
            }
        }
    });
}

function mostrarNotificacao(mensagem, tipo = 'success') {
    const notificacao = document.createElement('div');
    notificacao.className = `notificacao ${tipo}`;
    notificacao.textContent = mensagem;
    document.body.appendChild(notificacao);
    
    setTimeout(() => {
        notificacao.remove();
    }, 3000);
}

// Salvar tarefa (criar nova ou atualizar existente)
async function salvarTarefa(event) {
    event.preventDefault();
    
    // Obter os valores do formulário
    const form = document.getElementById('tarefaForm');
    const tarefaId = document.getElementById('tarefa_id') ? document.getElementById('tarefa_id').value : null;
    const titulo = document.getElementById('titulo').value;
    
    // Obter conteúdo do TinyMCE
    const descricao = tinymce.get('descricao') ? tinymce.get('descricao').getContent() : '';
    
    const prioridade = document.getElementById('prioridade').value;
    const dataLimite = document.getElementById('data_limite').value;
    
    try {
        const response = await fetch('backend/salvar_tarefa.php', {
            method: 'POST',
            body: JSON.stringify({
                id: tarefaId,
                titulo: titulo,
                descricao: descricao,
                prioridade: prioridade,
                data_limite: dataLimite,
                status: tarefaId ? null : 'backlog' // Se for edição, mantém o status atual
            }),
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();
        
        if (!response.ok || !data.success) {
            throw new Error(data.erro || 'Erro ao salvar tarefa');
        }

        fecharModal();
        await carregarTarefas();
        
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'Sucesso!',
                text: 'Tarefa salva com sucesso',
                icon: 'success',
                timer: 1500,
                showConfirmButton: false
            });
        } else {
            alert('Tarefa salva com sucesso');
        }
    } catch (error) {
        console.error('Erro:', error);
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'Erro!',
                text: 'Erro ao salvar tarefa: ' + error.message,
                icon: 'error'
            });
        } else {
            alert('Erro ao salvar tarefa: ' + error.message);
        }
    }
}

// Carregar dados de uma tarefa para edição
async function carregarDadosTarefa(tarefaId) {
    try {
        const response = await fetch(`backend/get_tarefa.php?id=${tarefaId}`);
        const data = await response.json();
        
        if (!data || data.erro) {
            throw new Error(data.erro || 'Erro ao carregar dados da tarefa');
        }
        
        // Adicionar campo oculto para o ID da tarefa
        const form = document.getElementById('tarefaForm');
        let idInput = document.getElementById('tarefa_id');
        
        if (!idInput) {
            idInput = document.createElement('input');
            idInput.type = 'hidden';
            idInput.id = 'tarefa_id';
            idInput.name = 'tarefa_id';
            form.appendChild(idInput);
        }
        
        idInput.value = tarefaId;
        
        // Preencher os campos do formulário
        document.getElementById('titulo').value = data.titulo || '';
        document.getElementById('descricao').value = data.descricao || '';
        document.getElementById('prioridade').value = data.prioridade || 'media';
        
        if (data.data_limite) {
            document.getElementById('data_limite').value = data.data_limite;
        }
        
    } catch (error) {
        console.error('Erro:', error);
        alert('Erro ao carregar dados da tarefa: ' + error.message);
    }
}

// Excluir tarefa
async function excluirTarefa(tarefaId) {
    const result = await Swal.fire({
        title: 'Confirmar exclusão',
        text: 'Tem certeza que deseja excluir esta tarefa?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Sim, excluir!',
        cancelButtonText: 'Cancelar',
        background: '#fff',
        borderRadius: '10px',
        customClass: {
            popup: 'swal-custom-popup',
            title: 'swal-custom-title',
            content: 'swal-custom-content',
            confirmButton: 'swal-custom-confirm',
            cancelButton: 'swal-custom-cancel'
        }
    });

    if (!result.isConfirmed) return;
    
    try {
        const response = await fetch('backend/excluir_tarefa.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ id: tarefaId })
        });

        const data = await response.json();
        
        if (!response.ok || !data.success) {
            throw new Error(data.erro || 'Erro ao excluir tarefa');
        }

        // Remove o elemento da tarefa do DOM
        const tarefaElement = document.querySelector(`[data-task-id="${tarefaId}"]`);
        if (tarefaElement) {
            tarefaElement.remove();
        }
        
        // Atualiza os contadores
        atualizarContadores();
        
        // Mostra mensagem de sucesso
        Swal.fire({
            title: 'Sucesso!',
            text: 'Tarefa excluída com sucesso',
            icon: 'success',
            timer: 1500,
            showConfirmButton: false,
            background: '#fff',
            customClass: {
                popup: 'swal-custom-popup'
            }
        });
    } catch (error) {
        console.error('Erro:', error);
        Swal.fire({
            title: 'Erro!',
            text: 'Erro ao excluir tarefa',
            icon: 'error',
            confirmButtonColor: '#3085d6',
            background: '#fff',
            customClass: {
                popup: 'swal-custom-popup'
            }
        });
    }
}

// Função para editar tarefa - versão melhorada
async function editarTarefa(tarefaId) {
    try {
        // Mostrar indicador de carregamento
        const loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'loading-overlay';
        loadingOverlay.innerHTML = `
            <div class="loading-spinner">
                <i class="fas fa-spinner fa-spin"></i>
                <span>Carregando...</span>
            </div>
        `;
        document.body.appendChild(loadingOverlay);
        
        // Buscar dados da tarefa
        const response = await fetch(`backend/get_tarefa.php?id=${tarefaId}`);
        const data = await response.json();
        
        // Remover indicador de carregamento
        document.body.removeChild(loadingOverlay);
        
        if (!data || data.erro) {
            throw new Error(data.erro || 'Erro ao carregar dados da tarefa');
        }
        
        // Abrir modal com os dados já carregados
        abrirModalTarefaComDados(tarefaId, data);
        
    } catch (error) {
        console.error('Erro:', error);
        // Remover indicador de carregamento se ainda existir
        const loadingOverlay = document.querySelector('.loading-overlay');
        if (loadingOverlay) {
            document.body.removeChild(loadingOverlay);
        }
        
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'Erro!',
                text: 'Erro ao carregar dados da tarefa: ' + error.message,
                icon: 'error'
            });
        } else {
            alert('Erro ao carregar dados da tarefa: ' + error.message);
        }
    }
}

// Função para abrir modal com dados já carregados
function abrirModalTarefaComDados(tarefaId, dadosTarefa) {
    // Remover qualquer modal existente antes de criar um novo
    fecharModal();
    
    const modalOverlay = document.createElement('div');
    modalOverlay.className = 'modal-overlay';
    
    const modal = document.createElement('div');
    modal.className = 'modal';
    
    modal.innerHTML = `
        <div class="modal-header">
            <h2><i class="fas fa-${tarefaId ? 'edit' : 'plus-circle'}"></i> ${tarefaId ? 'Editar' : 'Nova'} Tarefa</h2>
            <button class="modal-close" onclick="fecharModal()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <form id="tarefaForm" class="form-modern">
                <input type="hidden" id="tarefa_id" name="tarefa_id" value="${tarefaId || ''}">
                <div class="form-group">
                    <label for="titulo">Título</label>
                    <input type="text" id="titulo" name="titulo" class="form-control" 
                           value="${dadosTarefa ? dadosTarefa.titulo || '' : ''}"
                           placeholder="Digite o título da tarefa" required>
                </div>
                <div class="form-group">
                    <label for="descricao">Descrição</label>
                    <textarea id="descricao" name="descricao">${dadosTarefa ? dadosTarefa.descricao || '' : ''}</textarea>
                </div>

                <div class="form-row">
                    <div class="form-group col-md-6">
                        <label for="prioridade">Prioridade</label>
                        <select id="prioridade" name="prioridade" class="form-control" required>
                            <option value="baixa" ${dadosTarefa && dadosTarefa.prioridade === 'baixa' ? 'selected' : ''}>Baixa</option>
                            <option value="media" ${!dadosTarefa || dadosTarefa.prioridade === 'media' ? 'selected' : ''}>Média</option>
                            <option value="alta" ${dadosTarefa && dadosTarefa.prioridade === 'alta' ? 'selected' : ''}>Alta</option>
                        </select>
                    </div>
                    
                    <div class="form-group col-md-6">
                        <label for="data_limite">Data Limite</label>
                        <input type="date" id="data_limite" name="data_limite" 
                               value="${dadosTarefa && dadosTarefa.data_limite ? dadosTarefa.data_limite : ''}"
                               class="form-control">
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="fecharModal()">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Salvar
                    </button>
                </div>
            </form>
        </div>
    `;
    
    // Adicionar o modal ao DOM de uma vez só
    modalOverlay.appendChild(modal);
    document.body.appendChild(modalOverlay);
    
    // Inicializar o TinyMCE
    tinymce.init({
        selector: '#descricao',
        height: 300,
        menubar: false,
        language: 'pt_BR',
        language_url: 'https://cdn.tiny.cloud/1/no-api-key/tinymce/6/langs/pt_BR.js', // URL correta para o arquivo de idioma
        plugins: [
            'advlist', 'autolink', 'lists', 'link', 'charmap',
            'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
            'insertdatetime', 'table', 'wordcount'
        ],
        toolbar: 'undo redo | styles | bold italic | alignleft aligncenter alignright alignjustify | ' +
                'bullist numlist | link | forecolor backcolor',
        content_style: 'body { font-family: "Quicksand", sans-serif; font-size: 14px }',
        style_formats: [
            { title: 'Parágrafo', format: 'p' },
            { title: 'Título 1', format: 'h1' },
            { title: 'Título 2', format: 'h2' },
            { title: 'Título 3', format: 'h3' }
        ],
        branding: false,
        promotion: false,
        setup: function(editor) {
            editor.on('init', function() {
                // Armazenar a instância do editor para uso posterior
                window.tinyMCEInstance = editor;
            });
        }
    });
    
    // Fechar modal ao clicar fora
    modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
            fecharModal();
        }
    });
    
    // Fechar modal com tecla ESC - usando uma função nomeada para poder remover o listener depois
    const handleEscKey = (e) => {
        if (e.key === 'Escape') {
            fecharModal();
        }
    };
    document.addEventListener('keydown', handleEscKey);
    
    // Armazenar a referência ao listener para remover quando o modal for fechado
    modalOverlay.escKeyListener = handleEscKey;
    
    // Adicionar evento de submit ao formulário
    document.getElementById('tarefaForm').addEventListener('submit', salvarTarefa);
}

// Função para calcular dias restantes - versão corrigida
function calcularDiasRestantes(dataLimite) {
    if (!dataLimite) return null;
    
    // Obter a data atual no fuso horário local
    const hoje = new Date();
    hoje.setHours(0, 0, 0, 0);
    
    // Converter a string de data para objeto Date
    const [ano, mes, dia] = dataLimite.split('-');
    const dataFinal = new Date(ano, mes - 1, dia);
    dataFinal.setHours(0, 0, 0, 0);
    
    // Calcular a diferença em dias
    const diffTempo = dataFinal.getTime() - hoje.getTime();
    const diffDias = Math.floor(diffTempo / (1000 * 60 * 60 * 24));
    
    return diffDias;
}

// Função para verificar se uma data é hoje
function isHoje(dataString) {
    if (!dataString) return false;
    
    const hoje = new Date();
    hoje.setHours(0, 0, 0, 0);
    
    const [ano, mes, dia] = dataString.split('-');
    const data = new Date(ano, mes - 1, dia);
    data.setHours(0, 0, 0, 0);
    
    return data.getTime() === hoje.getTime();
}

// Função para formatar os dias restantes
function formatarDiasRestantes(dias) {
    if (dias === null) return 'Sem prazo';
    if (dias < 0) return `Atrasado ${Math.abs(dias)} dia${Math.abs(dias) !== 1 ? 's' : ''}`;
    if (dias === 0) return 'Vence hoje';
    return `${dias} dia${dias !== 1 ? 's' : ''} restante${dias !== 1 ? 's' : ''}`;
}

// Função para obter classe CSS baseada na urgência
function obterClasseUrgencia(dias) {
    if (dias === null) return '';
    if (dias < 0) return 'deadline-expired';
    if (dias === 0) return 'deadline-today'; // Vence hoje
    if (dias <= 3) return 'deadline-soon';
    return 'deadline-future';
}






