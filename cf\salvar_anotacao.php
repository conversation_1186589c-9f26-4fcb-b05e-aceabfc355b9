<?php
session_start();
require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/csrf.php';

header('Content-Type: application/json');
// Headers de segurança HTTP
header('X-Frame-Options: DENY');
header('X-Content-Type-Options: nosniff');
header('Referrer-Policy: no-referrer');
header('Permissions-Policy: interest-cohort=()');
header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');

try {
    // Log para depuração
    error_log("Iniciando salvamento de anotação");

    // Obter conexão com o banco de dados
    $pdo = getDbConnection();
    error_log("Conexão com o banco de dados estabelecida");

    // Verifica se o usuário está logado
    if (!isset($_SESSION['idusuario'])) {
        http_response_code(401);
        echo json_encode(['erro' => 'Usuário não autenticado', 'redirect' => '../login_index.php']);
        exit;
    }

    // Usa o ID do usuário da sessão
    $usuario_id = $_SESSION['idusuario'];
    error_log("Usando ID do usuário da sessão: {$usuario_id}");

    // Recebe os dados enviados
    $dados = json_decode(file_get_contents('php://input'), true);

    // Log dos dados recebidos
    error_log("Dados recebidos: " . json_encode($dados));

    if (!$dados) {
        throw new Exception('Dados inválidos');
    }

    // CSRF token obrigatório
    if (!isset($dados['csrf_token']) || !validateCsrfToken($dados['csrf_token'])) {
        http_response_code(403);
        echo json_encode(['erro' => 'CSRF token inválido ou ausente']);
        exit;
    }

    // Sanitização básica
    define('MAX_TEXT_SIZE', 2048);
    $elemento_id = filter_var($dados['elemento_id'] ?? '', FILTER_SANITIZE_STRING);
    $pagina_url = filter_var($dados['pagina_url'] ?? '', FILTER_SANITIZE_URL);
    $texto = filter_var($dados['texto'] ?? '', FILTER_SANITIZE_STRING);
    $conteudo_anotado = filter_var($dados['conteudo_anotado'] ?? '', FILTER_SANITIZE_STRING);
    $posicao_inicio = isset($dados['posicao_inicio']) ? intval($dados['posicao_inicio']) : null;
    $posicao_fim = isset($dados['posicao_fim']) ? intval($dados['posicao_fim']) : null;

    if (empty($elemento_id) || empty($pagina_url) || empty($texto) || empty($conteudo_anotado)) {
        throw new Exception('Dados incompletos');
    }
    if (mb_strlen($texto) > MAX_TEXT_SIZE || mb_strlen($conteudo_anotado) > MAX_TEXT_SIZE) {
        throw new Exception('Texto excede limite permitido.');
    }

    // Verifica se a tabela existe
    try {
        $check_table = $pdo->query("SELECT to_regclass('appestudo.anotacoes')");
        $table_exists = $check_table->fetchColumn();
    } catch (Exception $e) {
        error_log("Erro ao verificar tabela: " . $e->getMessage());
        $table_exists = false;
    }

    error_log("Verificando tabela: " . ($table_exists ? "Tabela existe" : "Tabela não existe"));

    if (!$table_exists) {
        // Cria a tabela se não existir
        error_log("Criando tabela anotacoes");
        try {
            // Primeiro, verifica se o esquema existe
            $pdo->exec("CREATE SCHEMA IF NOT EXISTS appestudo");
            error_log("Esquema appestudo criado ou já existente");

            // Depois cria a tabela
            $pdo->exec("CREATE TABLE IF NOT EXISTS appestudo.anotacoes (
                id SERIAL PRIMARY KEY,
                usuario_id INTEGER NOT NULL,
                pagina_url TEXT NOT NULL,
                elemento_id TEXT NOT NULL,
                texto_anotacao TEXT NOT NULL,
                conteudo_anotado TEXT NOT NULL,
                posicao_inicio INTEGER,
                posicao_fim INTEGER,
                data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                data_atualizacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )");
            error_log("Tabela anotacoes criada com sucesso");
        } catch (Exception $e) {
            error_log("Erro ao criar tabela: " . $e->getMessage());
            throw new Exception('Erro ao criar tabela: ' . $e->getMessage());
        }
    }

    // Verifica se já existe uma anotação com o mesmo ID
    $anotacao_id = $dados['anotacao_id'] ?? '';

    // Usa o ID numérico fornecido pelo cliente ou extrai do anotacao_id
    $id_numerico = $dados['id_numerico'] ?? str_replace('anotacao-', '', $anotacao_id);

    error_log("ID da anotação: {$anotacao_id}, ID numérico: {$id_numerico}");

    // Log das posições recebidas
    error_log("Posições recebidas: inicio={$posicao_inicio}, fim={$posicao_fim}");

    // Verifica se já existe uma anotação com as mesmas posições no mesmo elemento
    if (!empty($elemento_id) && $posicao_inicio !== null && $posicao_fim !== null) {
        // Busca por anotações existentes com as mesmas posições
        $sql = "SELECT id FROM appestudo.anotacoes
                WHERE usuario_id = :usuario_id
                AND pagina_url = :pagina_url
                AND elemento_id = :elemento_id
                AND posicao_inicio = :posicao_inicio
                AND posicao_fim = :posicao_fim";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':usuario_id' => $usuario_id,
            ':pagina_url' => $pagina_url,
            ':elemento_id' => $elemento_id,
            ':posicao_inicio' => $posicao_inicio,
            ':posicao_fim' => $posicao_fim
        ]);

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($row) {
            // Se encontrou uma anotação existente, atualiza
            $id = $row['id'];
            error_log("Anotação encontrada com as mesmas posições: {$id}");

            $sql = "UPDATE appestudo.anotacoes
                    SET texto_anotacao = :texto,
                        conteudo_anotado = :conteudo_anotado,
                        data_atualizacao = CURRENT_TIMESTAMP
                    WHERE id = :id";

            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                ':id' => $id,
                ':texto' => $texto,
                ':conteudo_anotado' => $conteudo_anotado
            ]);

            error_log("Anotação atualizada com sucesso");
            echo json_encode([
                'id' => (int)$id,
                'mensagem' => htmlspecialchars('Anotação salva com sucesso.', ENT_QUOTES | ENT_HTML5, 'UTF-8')
            ]);
            exit;
        } else {
            // Insere nova anotação
            error_log("Inserindo nova anotação");

            $sql = "INSERT INTO appestudo.anotacoes
                    (usuario_id, pagina_url, elemento_id, texto_anotacao, conteudo_anotado, posicao_inicio, posicao_fim)
                    VALUES
                    (:usuario_id, :pagina_url, :elemento_id, :texto, :conteudo_anotado, :posicao_inicio, :posicao_fim)";

            $stmt = $pdo->prepare($sql);
            $stmt->execute([
                ':usuario_id' => $usuario_id,
                ':pagina_url' => $pagina_url,
                ':elemento_id' => $elemento_id,
                ':texto' => $texto,
                ':conteudo_anotado' => $conteudo_anotado,
                ':posicao_inicio' => $posicao_inicio,
                ':posicao_fim' => $posicao_fim
            ]);

            error_log("Nova anotação inserida com sucesso");
        }
    } else {
        // Insere nova anotação se não tiver posições válidas
        error_log("Inserindo nova anotação sem posições válidas");

        $sql = "INSERT INTO appestudo.anotacoes
                (usuario_id, pagina_url, elemento_id, texto_anotacao, conteudo_anotado, posicao_inicio, posicao_fim)
                VALUES
                (:usuario_id, :pagina_url, :elemento_id, :texto, :conteudo_anotado, :posicao_inicio, :posicao_fim)";

        $stmt = $pdo->prepare($sql);
        $stmt->execute([
            ':usuario_id' => $usuario_id,
            ':pagina_url' => $pagina_url,
            ':elemento_id' => $elemento_id,
            ':texto' => $texto,
            ':conteudo_anotado' => $conteudo_anotado,
            ':posicao_inicio' => $posicao_inicio,
            ':posicao_fim' => $posicao_fim
        ]);

        error_log("Nova anotação sem posições válidas inserida com sucesso");
    }

    // Resposta de sucesso
    error_log("Operação concluída com sucesso");

    // Log após a execução da query
    error_log("Query executada com sucesso");

    echo json_encode(['sucesso' => true]);

} catch (Exception $e) {
    error_log("Erro em salvar_anotacao.php: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['erro' => $e->getMessage()]);
}
