let dashboardInstance = null;

const sistemaConquistas = {
    // Criar marcos para cada card até 100
    marcos: Array.from({ length: 100 }, (_, i) => {
        const numero = i + 1;
        return {
            id: `nivel_${numero}`,
            quantidade: numero,
            nome: `Nível ${numero}`,
            icone: numero <= 20 ? '🌱' : 
                   numero <= 40 ? '🌿' : 
                   numero <= 60 ? '🌳' : 
                   numero <= 80 ? '🌟' : '🏆',
            pontos: numero * 10
        };
    }),

    progresso: {
        cardsEstudados: 0,
        conquistasDesbloqueadas: new Set(),
        pontosTotais: 0
    },

    calcularProgresso() {
        this.progresso = {
            cardsEstudados: 0,
            conquistasDesbloqueadas: new Set(),
            pontosTotais: 0
        };

        const cardsChecked = document.querySelectorAll('.conteudo-checkbox:checked').length;
        this.progresso.cardsEstudados = cardsChecked;

        this.marcos.forEach(marco => {
            if (cardsChecked >= marco.quantidade) {
                this.progresso.conquistasDesbloqueadas.add(marco.id);
                this.progresso.pontosTotais += marco.pontos;
            }
        });

        if (dashboardInstance) {
            dashboardInstance.atualizar();
        }
    },

    // Adicione estas funções ao objeto sistemaConquistas
encontrarProximoMarco() {
    // Encontra o primeiro marco que ainda não foi alcançado
    return this.marcos.find(marco => this.progresso.cardsEstudados < marco.quantidade);
},

encontrarNivelAtual() {
    // Se não atingiu nem o primeiro nível, retorna null
    if (this.progresso.cardsEstudados < this.marcos[0].quantidade) {
        return null;
    }
    
    // Caso contrário, encontra o último nível completado
    return [...this.marcos]
        .filter(marco => this.progresso.cardsEstudados >= marco.quantidade)
        .pop();
},

    mostrarNotificacaoProgresso(mensagem, tipo = 'info') {
        const notificacao = document.createElement('div');
        notificacao.className = 'conquista-notificacao';
        
        const icone = tipo === 'conquista' ? '🎉' : 
                     tipo === 'warning' ? '⚠️' : 'ℹ️';

        notificacao.innerHTML = `
            <div class="conquista-icone">${icone}</div>
            <div class="conquista-info">
                <h4>${tipo === 'conquista' ? 'Nova Conquista!' : 'Progresso Atualizado'}</h4>
                <p>${mensagem}</p>
            </div>
        `;

        document.body.appendChild(notificacao);
        setTimeout(() => {
            notificacao.classList.add('mostrar');
            setTimeout(() => {
                notificacao.classList.remove('mostrar');
                setTimeout(() => notificacao.remove(), 300);
            }, 3000);
        }, 100);
    }
};

class DashboardMotivacional {
    constructor() {
        this.dashboard = document.createElement('div');
        this.dashboard.className = 'dashboard-motivacional';
        this.inicializarEstilos();
    }

    inicializarEstilos() {
        const estilos = document.createElement('div');
        estilos.innerHTML = `
            <style>
                .dashboard-motivacional {
                    font-family: 'Quicksand', sans-serif;
                    padding: 20px;
                }

                .progresso-container {
                    background: var(--paper-color);
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                }

                .cards-total {
                    text-align: center;
                    font-size: 2em;
                    margin: 20px 0;
                    color: var(--primary-color);
                }

                .cards-total .numero {
                    font-weight: bold;
                    color: var(--primary-color);
                }

                .barra-progresso {
                    height: 10px;
                    background: var(--color-info-item);
                    border-radius: 5px;
                    margin: 15px 0;
                    overflow: hidden;
                }

                .barra-progresso .progresso {
                    height: 100%;
                    background: var(--primary-color);
                    transition: width 0.3s ease;
                }

                .info-nivel {
                    background: var(--color-info-item);
                    padding: 15px;
                    border-radius: 8px;
                    margin-top: 15px;
                    text-align: center;
                }

                .info-nivel .icone {
                    font-size: 1.5em;
                    margin-right: 10px;
                }

                .pontos-container {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    gap: 10px;
                    margin-top: 15px;
                    padding: 10px;
                    background: rgba(var(--primary-color-rgb), 0.1);
                    border-radius: 20px;
                }

                .conquista-notificacao {
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    background: var(--paper-color);
                    padding: 15px 25px;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    transform: translateY(100px);
                    opacity: 0;
                    transition: all 0.3s ease;
                    z-index: 1000;
                    display: flex;
                    align-items: center;
                    gap: 15px;
                    border: 2px solid var(--border-color);
                }

                .conquista-notificacao.mostrar {
                    transform: translateY(0);
                    opacity: 1;
                }

                .conquista-completa {
                    text-align: center;
                    padding: 20px;
                    background: linear-gradient(135deg, var(--primary-color) 0%, #4a90e2 100%);
                    color: white;
                    border-radius: 12px;
                    margin-bottom: 20px;
                    animation: celebrar 2s ease-in-out;
                }

                .conquista-header {
                    margin-bottom: 20px;
                }

                .conquista-header .icone-grande {
                    font-size: 4em;
                    display: block;
                    margin-bottom: 10px;
                    animation: flutuar 2s ease-in-out infinite;
                }

                .conquista-header h2 {
                    font-size: 2em;
                    margin: 0;
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
                }

                .conquista-mensagem {
                    font-size: 1.1em;
                    line-height: 1.6;
                }

                .conquista-destaque {
                    font-size: 1.2em;
                    font-weight: bold;
                    margin: 15px 0;
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
                }

                .conquista-detalhes {
                    background: rgba(255,255,255,0.1);
                    padding: 15px;
                    border-radius: 8px;
                    margin-top: 20px;
                }

                .conquista-detalhes p {
                    margin: 10px 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 10px;
                }

                .conquista-detalhes i {
                    font-size: 1.2em;
                }

                @keyframes celebrar {
                    0% { transform: scale(0.8); opacity: 0; }
                    50% { transform: scale(1.05); }
                    100% { transform: scale(1); opacity: 1; }
                }

                @keyframes flutuar {
                    0%, 100% { transform: translateY(0); }
                    50% { transform: translateY(-10px); }
                }
            </style>
        `;
        this.dashboard.appendChild(estilos);
    }

    gerarHTML() {
        const { cardsEstudados } = sistemaConquistas.progresso;
        const totalCards = document.querySelectorAll('.conteudo-checkbox').length;
        const percentualConcluido = (cardsEstudados / totalCards) * 100;

        // Usar as novas funções corrigidas
        const nivelAtual = sistemaConquistas.encontrarNivelAtual();
        const proximoMarco = sistemaConquistas.encontrarProximoMarco();

        // Verifica se completou todos os cards
        const completouTudo = cardsEstudados === totalCards;

        return `
            <div class="progresso-container">
                ${completouTudo ? `
                    <div class="conquista-completa">
                        <div class="conquista-header">
                            <span class="icone-grande">🎓</span>
                            <h2>Parabéns, Mestre!</h2>
                        </div>
                        <div class="conquista-mensagem">
                            <p>Você completou todos os tópicos do seu plano de estudos!</p>
                            <p class="conquista-destaque">Este é um momento histórico na sua jornada de conhecimento.</p>
                            <div class="conquista-detalhes">
                                <p><i class="fas fa-check-circle"></i> Total de tópicos dominados: ${totalCards}</p>
                                <p><i class="fas fa-star"></i> Pontos conquistados: ${sistemaConquistas.progresso.pontosTotais}</p>
                                <p><i class="fas fa-trophy"></i> Nível alcançado: Mestre Supremo</p>
                            </div>
                        </div>
                    </div>
                ` : `
                    <div class="cards-total">
                        <span class="numero">${cardsEstudados}</span> Tópicos estudados de <span class="numero">${totalCards}</span> Total
                    </div>

                    <div class="barra-progresso">
                        <div class="progresso" style="width: ${percentualConcluido}%"></div>
                    </div>

                    <div class="info-nivel">
                        ${nivelAtual ? `
                            <div class="nivel-atual">
                                <span class="icone">${nivelAtual.icone}</span>
                                Nível Atual: ${nivelAtual.nome}
                            </div>
                        ` : `
                            <div class="sem-nivel">
                                <span class="icone">🔍</span>
                                Você ainda não atingiu o primeiro nível
                            </div>
                        `}
                        
                        ${proximoMarco ? `
                            <div class="proximo-nivel">
                                <span class="icone">${proximoMarco.icone}</span>
                                Próximo Nível: ${proximoMarco.nome}
                                <br>
                                <small>Faltam ${proximoMarco.quantidade - cardsEstudados} Tópicos</small>
                            </div>
                        ` : `
                            <div class="nivel-maximo">
                                <span class="icone">🏆</span>
                                Nível Máximo Alcançado: Mestre!
                            </div>
                        `}

                        <div class="pontos-container">
                            <span>✨ ${sistemaConquistas.progresso.pontosTotais} pontos</span>
                        </div>
                    </div>
                `}
            </div>
        `;
    }

    atualizar() {
        const conteudo = document.createElement('div');
        conteudo.innerHTML = this.gerarHTML();
        
        // Limpa o conteúdo anterior mantendo os estilos
        const estilos = this.dashboard.querySelector('style').parentElement;
        this.dashboard.innerHTML = '';
        this.dashboard.appendChild(estilos);
        this.dashboard.appendChild(conteudo);
    }
}

// Função auxiliar para definir nomes dos marcos
function definirNomeMarco(percentual) {
    if (percentual <= 20) {
        return {
            nome: "Iniciante",
            icone: "🌱"
        };
    } else if (percentual <= 40) {
        return {
            nome: "Dedicado",
            icone: "🌿"
        };
    } else if (percentual <= 60) {
        return {
            nome: "Constante",
            icone: "🌳"
        };
    } else if (percentual <= 80) {
        return {
            nome: "Avançado",
            icone: "🌟"
        };
    } else {
        return {
            nome: "Mestre",
            icone: "🏆"
        };
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const totalCards = document.querySelectorAll('.conteudo-checkbox').length;
    
    // Define marcos específicos em vez de um para cada card
    sistemaConquistas.marcos = [
        {
            id: 'iniciante',
            quantidade: Math.ceil(totalCards * 0.1), // 10% dos cards
            nome: 'Iniciante',
            icone: '🌱',
            pontos: 100
        },
        {
            id: 'dedicado',
            quantidade: Math.ceil(totalCards * 0.25), // 25% dos cards
            nome: 'Dedicado',
            icone: '🌿',
            pontos: 250
        },
        {
            id: 'constante',
            quantidade: Math.ceil(totalCards * 0.5), // 50% dos cards
            nome: 'Constante',
            icone: '🌳',
            pontos: 500
        },
        {
            id: 'avancado',
            quantidade: Math.ceil(totalCards * 0.75), // 75% dos cards
            nome: 'Avançado',
            icone: '🌟',
            pontos: 750
        },
        {
            id: 'mestre',
            quantidade: totalCards, // 100% dos cards
            nome: 'Mestre',
            icone: '🏆',
            pontos: 1000
        }
    ];

    const container = document.getElementById('dashboard-container');
    if (container) {
        dashboardInstance = new DashboardMotivacional();
        container.appendChild(dashboardInstance.dashboard);
        sistemaConquistas.calcularProgresso();
    }

    // Listener para os checkboxes
    document.querySelectorAll('.conteudo-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const conquistasAntes = new Set(sistemaConquistas.progresso.conquistasDesbloqueadas);
            sistemaConquistas.calcularProgresso();

            // Verifica novas conquistas
            sistemaConquistas.marcos.forEach(marco => {
                if (sistemaConquistas.progresso.conquistasDesbloqueadas.has(marco.id) && 
                    !conquistasAntes.has(marco.id)) {
                    sistemaConquistas.mostrarNotificacaoProgresso(
                        `${marco.nome} alcançado! +${marco.pontos} pontos`,
                        'conquista'
                    );
                }
            });
        });
    });
});