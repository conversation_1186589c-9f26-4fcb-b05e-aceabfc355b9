<?php
//agenda_atualizar_evento.php
ob_clean(); 
include_once("conexao_POST.php");

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $eventoId = $_POST['eventoId'] ?? null;
    $realizado = $_POST['realizado'] ?? null;
    $titulo = $_POST['titulo'] ?? null;
    $detalhes = $_POST['detalhes'] ?? null;
    $data_inicio = $_POST['data_inicio'] ?? null;
    $data_fim = $_POST['data_fim'] ?? null;

    if ($eventoId !== null) {
        try {
            $realizadoValue = ($realizado === 'true' || $realizado === true) ? 't' : 'f';
            
            // Converter a data do formato d/m/Y para Y-m-d
            if ($data_inicio) {
                $data_arr = explode('/', $data_inicio);
                if (count($data_arr) === 3) {
                    $data_inicio = "{$data_arr[2]}-{$data_arr[1]}-{$data_arr[0]}";
                    $data_fim = $data_inicio; // Usar mesma data para fim
                }
            }

            // Construir a query base
            $updateFields = ["realizado = $1"];
            $params = [$realizadoValue];
            $paramCount = 1;
            
            // Adicionar campos opcionais se fornecidos
            if ($titulo !== null) {
                $paramCount++;
                $updateFields[] = "titulo = $$paramCount";
                $params[] = $titulo;
            }
            
            if ($detalhes !== null) {
                $paramCount++;
                $updateFields[] = "detalhes = $$paramCount";
                $params[] = $detalhes;
            }
            
            if ($data_inicio !== null) {
                $paramCount++;
                $updateFields[] = "data_inicio = $$paramCount";
                $params[] = $data_inicio;
            }
            
            if ($data_fim !== null) {
                $paramCount++;
                $updateFields[] = "data_fim = $$paramCount";
                $params[] = $data_fim;
            }
            
            $paramCount++;
            $params[] = $eventoId;
            
            $query = "UPDATE appEstudo.agenda SET " . implode(", ", $updateFields) . " WHERE id = $$paramCount RETURNING id";
            
            $resultado = pg_query_params($conexao, $query, $params);

            if ($resultado && pg_num_rows($resultado) > 0) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Evento atualizado com sucesso'
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'Evento não encontrado'
                ]);
            }
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Erro ao atualizar: ' . $e->getMessage()
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'ID do evento não fornecido'
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Método não permitido'
    ]);
}

exit;
?>