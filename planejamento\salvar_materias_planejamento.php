<?php
session_start();
include_once("../assets/config.php");

if (!$conexao) {
    die(json_encode(['erro' => 'Erro na conexão com o banco de dados']));
}

$id_planejamento = $_POST['id_planejamento'] ?? null;
$materias = isset($_POST['materias']) ? json_decode($_POST['materias'], true) : [];

if (!$id_planejamento || !is_array($materias)) {
    echo json_encode(['sucesso' => false, 'erro' => 'Dados inválidos.']);
    exit;
}

pg_query($conexao, "BEGIN");
try {
    // Remove todas as matérias antigas
    $query_limpar = "DELETE FROM appEstudo.planejamento_materia WHERE planejamento_idplanejamento = $1";
    $resultado = pg_query_params($conexao, $query_limpar, [$id_planejamento]);
    if (!$resultado) {
        throw new Exception("Erro ao limpar matérias antigas");
    }
    // Insere as novas matérias selecionadas
    foreach ($materias as $id_materia) {
        $query_inserir = "INSERT INTO appEstudo.planejamento_materia (planejamento_idplanejamento, materia_idmateria) VALUES ($1, $2)";
        $resultado = pg_query_params($conexao, $query_inserir, [$id_planejamento, $id_materia]);
        if (!$resultado) {
            throw new Exception("Erro ao inserir matéria");
        }
    }
    pg_query($conexao, "COMMIT");
    echo json_encode(['sucesso' => true]);
} catch (Exception $e) {
    pg_query($conexao, "ROLLBACK");
    echo json_encode(['sucesso' => false, 'erro' => $e->getMessage()]);
} 