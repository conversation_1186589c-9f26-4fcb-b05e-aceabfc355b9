<?php
// Iniciar sessão apenas se não estiver ativa
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Headers para CORS e JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Tratar requisições OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

require_once '../../conexao_POST.php';

// Verificar se o usuário está autenticado
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit;
}

$usuario_id = $_SESSION['idusuario'];
$metodo = $_SERVER['REQUEST_METHOD'];

// Obter dados do corpo da requisição
$dados = json_decode(file_get_contents('php://input'), true);

// Função para obter ID da lei atual do usuário
function obterLeiAtualUsuario($conexao, $usuario_id) {
    $query = "SELECT l.id
              FROM appestudo.lexjus_user_preferencias p
              JOIN appestudo.lexjus_leis l ON l.codigo = p.lei_atual
              WHERE p.usuario_id = $1";

    $result = pg_query_params($conexao, $query, [$usuario_id]);

    if ($result && pg_num_rows($result) > 0) {
        $row = pg_fetch_assoc($result);
        return $row['id'];
    }

    // Fallback: retornar ID da Constituição Federal
    $query_cf = "SELECT id FROM appestudo.lexjus_leis WHERE codigo = 'CF'";
    $result_cf = pg_query($conexao, $query_cf);

    if ($result_cf && pg_num_rows($result_cf) > 0) {
        $row_cf = pg_fetch_assoc($result_cf);
        return $row_cf['id'];
    }

    return null;
}

// Função para obter ID da lei por código
function obterIdLeiPorCodigo($conexao, $codigo_lei) {
    $query = "SELECT id FROM appestudo.lexjus_leis WHERE codigo = $1";
    $result = pg_query_params($conexao, $query, [$codigo_lei]);

    if ($result && pg_num_rows($result) > 0) {
        $row = pg_fetch_assoc($result);
        return $row['id'];
    }

    return null;
}

switch ($metodo) {
    case 'GET':
        // Obter progresso de leitura do usuário para a lei especificada ou atual
        $lei_codigo = $_GET['lei'] ?? '';

        if (!empty($lei_codigo)) {
            // Usar lei especificada na URL
            $lei_id = obterIdLeiPorCodigo($conexao, $lei_codigo);
        } else {
            // Fallback para lei atual do usuário
            $lei_id = obterLeiAtualUsuario($conexao, $usuario_id);
        }

        if (!$lei_id) {
            http_response_code(500);
            echo json_encode(['erro' => 'Lei não encontrada']);
            exit;
        }

        $query = "SELECT artigo_numero, lido, data_leitura
                 FROM appestudo.lexjus_progresso
                 WHERE usuario_id = $1 AND lei_id = $2";

        $result = pg_query_params($conexao, $query, [$usuario_id, $lei_id]);

        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao consultar progresso']);
            exit;
        }

        $progresso = [];
        while ($row = pg_fetch_assoc($result)) {
            $progresso[$row['artigo_numero']] = [
                'lido' => (bool)$row['lido'],
                'data_leitura' => $row['data_leitura']
            ];
        }

        echo json_encode(['progresso' => $progresso]);
        break;

    case 'POST':
        // Marcar artigo como lido/não lido
        if (!isset($dados['artigo_numero']) || !isset($dados['lido'])) {
            http_response_code(400);
            echo json_encode(['erro' => 'Dados incompletos']);
            exit;
        }

        // Obter lei do corpo da requisição ou usar lei atual
        $lei_codigo = $dados['lei_codigo'] ?? '';

        if (!empty($lei_codigo)) {
            // Usar lei especificada na requisição
            $lei_id = obterIdLeiPorCodigo($conexao, $lei_codigo);
        } else {
            // Fallback para lei atual do usuário
            $lei_id = obterLeiAtualUsuario($conexao, $usuario_id);
        }

        if (!$lei_id) {
            http_response_code(500);
            echo json_encode(['erro' => 'Lei não encontrada']);
            exit;
        }

        $artigo_numero = $dados['artigo_numero'];
        $lido = $dados['lido'] ? true : false;

        if ($lido) {
            // Se está marcando como lido, insere ou atualiza
            $query = "INSERT INTO appestudo.lexjus_progresso (usuario_id, lei_id, artigo_numero, lido)
                     VALUES ($1, $2, $3, $4)
                     ON CONFLICT (usuario_id, lei_id, artigo_numero)
                     DO UPDATE SET lido = $4, data_leitura = CURRENT_TIMESTAMP";

            $result = pg_query_params($conexao, $query, [$usuario_id, $lei_id, $artigo_numero, $lido]);
        } else {
            // Se está desmarcando como lido, remove o registro
            $query = "DELETE FROM appestudo.lexjus_progresso
                     WHERE usuario_id = $1 AND lei_id = $2 AND artigo_numero = $3";

            $result = pg_query_params($conexao, $query, [$usuario_id, $lei_id, $artigo_numero]);
        }

        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao atualizar progresso']);
            exit;
        }

        echo json_encode(['sucesso' => true]);
        break;

    default:
        http_response_code(405);
        echo json_encode(['erro' => 'Método não permitido']);
}
?>