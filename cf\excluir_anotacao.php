<?php
header('Content-Type: application/json');
// Headers de segurança HTTP
header('X-Frame-Options: DENY');
header('X-Content-Type-Options: nosniff');
header('Referrer-Policy: no-referrer');
header('Permissions-Policy: interest-cohort=()');
header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');

require_once __DIR__ . '/config/database.php';
require_once __DIR__ . '/csrf.php';

try {
    // Recebe e decodifica os dados JSON
    $dados = json_decode(file_get_contents('php://input'), true);

    if (!isset($dados['anotacao_id'])) {
        throw new Exception('ID da anotação não fornecido');
    }
    // CSRF token obrigatório
    if (!isset($dados['csrf_token']) || !validateCsrfToken($dados['csrf_token'])) {
        http_response_code(403);
        echo json_encode(['erro' => 'CSRF token inválido ou ausente']);
        exit;
    }
    // Sanitização da entrada
    $anotacao_id = filter_var($dados['anotacao_id'], FILTER_SANITIZE_NUMBER_INT);
    if (empty($anotacao_id)) {
        throw new Exception('ID da anotação inválido');
    }

    $pdo = getDbConnection();
    
    $stmt = $pdo->prepare("
        DELETE FROM appestudo.anotacoes 
        WHERE id = :anotacao_id
    ");
    
    $stmt->execute([
        ':anotacao_id' => $dados['anotacao_id']
    ]);

    echo json_encode([
        'sucesso' => true,
        'mensagem' => htmlspecialchars('Anotação excluída com sucesso.', ENT_QUOTES | ENT_HTML5, 'UTF-8')
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'sucesso' => false,
        'erro' => 'Erro ao excluir anotação: ' . $e->getMessage()
    ]);
}