<?php
session_start();
if (!isset($_SESSION['idusuario']) || $_SESSION['idusuario'] != 1) {
    header("Location: login.php");
    exit();
}

include_once("assets/config.php");

$usuario_id = $_SESSION['idusuario'];

// Consulta para obter o progresso de estudo por matéria
$query = "
    SELECT 
        m.idmateria,
        m.nome AS materia_nome,
        m.cor,
        uc.status_estudo,
        COUNT(*) AS total
    FROM 
        appestudo.usuario_conteudo AS uc
    JOIN 
        appestudo.conteudo_edital AS ce ON uc.conteudo_id = ce.id_conteudo
    JOIN 
        appestudo.materia AS m ON ce.materia_id = m.idmateria
    WHERE 
        uc.usuario_id = $usuario_id
    GROUP BY 
        m.idmateria, m.nome, m.cor, uc.status_estudo
    ORDER BY 
        m.nome;
";

$result = pg_query($conexao, $query);
if (!$result) {
    die("Erro ao buscar o progresso por estudo.");
}

// Organizar os dados para exibição agrupada por matéria
$progresso_por_estudo = [];
while ($row = pg_fetch_assoc($result)) {
    $materia_id = $row['idmateria'];
    if (!isset($progresso_por_estudo[$materia_id])) {
        $progresso_por_estudo[$materia_id] = [
            'nome' => $row['materia_nome'],
            'cor' => $row['cor'],
            'status' => [
                'Não Estudado' => 0,
                'Estudando' => 0,
                'Estudado' => 0
            ]
        ];
    }
    $progresso_por_estudo[$materia_id]['status'][$row['status_estudo']] = $row['total'];
}
?>


<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Progresso de Estudo por Matéria</title>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier Prime', monospace;
            min-height: 100vh;
            background: linear-gradient(135deg, #8B0000, #B22222);
            padding: 40px 20px;
            line-height: 1.6;
            color: #2c1810;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .btn-voltar {
            position: fixed;
            top: 20px;
            left: 20px;
            background: linear-gradient(145deg, #8b4513, #a0522d);
            color: #fff;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
            text-decoration: none;
            z-index: 1000;
        }

        .btn-voltar:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            background: linear-gradient(145deg, #a0522d, #8b4513);
        }

        .header-vintage {
            text-align: center;
            color: #fff;
            margin-bottom: 40px;
        }

        .header-vintage h2 {
            font-family: 'Cinzel', serif;
            font-size: 2.5rem;
            font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 2px;
            margin-bottom: 10px;
        }

        .materias-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
            width: 100%;
        }

        .materia {
            background: #fff;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
            margin-bottom: 25px;
            width: 100%;
        }

        .materia:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .materia-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid;
            width: 100%;
        }

        .materia-icon {
            width: 50px;
            height: 50px;
            min-width: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            color: #fff;
            font-size: 1.4rem;
            transition: transform 0.3s ease;
        }

        .materia:hover .materia-icon {
            transform: scale(1.1) rotate(5deg);
        }

        .materia-nome {
            font-family: 'Cinzel', serif;
            font-size: 1.3rem;
            color: #2c1810;
            font-weight: 600;
        }

        .materia-content {
            width: 100%;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .progress-label {
            font-family: 'Cinzel', serif;
            color: #2c1810;
            font-size: 1rem;
        }

        .progress-percentage {
            font-family: 'Courier Prime', monospace;
            font-weight: bold;
            font-size: 1.2rem;
            color: #8B4513;
        }

        .progress-bar {
            height: 10px;
            background: #f4f1ea;
            border-radius: 5px;
            overflow: hidden;
            margin-bottom: 20px;
            width: 100%;
        }

        .progress-fill {
            height: 100%;
            border-radius: 5px;
            transition: width 1s ease-in-out;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            width: 100%;
        }

        .status-card {
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e8e0d8;
            transition: all 0.3s ease;
            background: #fff;
        }

        .status-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .status-icon {
            font-size: 1.5rem;
            margin-bottom: 10px;
        }

        .status-number {
            font-size: 1.5rem;
            font-weight: bold;
            margin: 5px 0;
            color: #2c1810;
        }

        .status-label {
            font-size: 0.85rem;
            color: #666;
            white-space: nowrap;
        }

        /* Status específicos */
        .nao-estudado {
            border-left: 4px solid #d32f2f;
        }

        .nao-estudado .status-icon {
            color: #d32f2f;
        }

        .estudando {
            border-left: 4px solid #f57c00;
        }

        .estudando .status-icon {
            color: #f57c00;
        }

        .estudado {
            border-left: 4px solid #388e3c;
        }

        .estudado .status-icon {
            color: #388e3c;
        }

        /* Animações */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsividade */
        @media (max-width: 768px) {
            body {
                padding: 15px 10px;
            }

            .btn-voltar {
                top: 10px;
                left: 10px;
                width: 40px;
                height: 40px;
            }

            .header-vintage h2 {
                font-size: 2rem;
            }

            .materias-container {
                padding: 20px;
            }

            .materia {
                padding: 15px;
            }

            .status-grid {
                gap: 10px;
            }

            .status-card {
                padding: 10px;
            }

            .status-number {
                font-size: 1.2rem;
            }

            .status-label {
                font-size: 0.75rem;
            }

            .materia-icon {
                width: 40px;
                height: 40px;
                min-width: 40px;
                font-size: 1.2rem;
            }

            .materia-nome {
                font-size: 1.1rem;
            }

            .progress-label, .progress-percentage {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
<a href="https://concurseirooff.com.br/edital_verticalizado/index.php" class="btn-voltar">
    <i class="fas fa-arrow-left"></i>
</a>

<div class="container">
    <div class="header-vintage">
        <h2>Progresso de Estudo por Matéria</h2>
    </div>

    <div class="materias-container">
        <?php foreach ($progresso_por_estudo as $materia): ?>
            <?php
            $total = $materia['status']['Não Estudado'] +
                $materia['status']['Estudando'] +
                $materia['status']['Estudado'];

            $percentual_estudado = ($total > 0) ?
                ($materia['status']['Estudado'] / $total) * 100 : 0;
            ?>
            <div class="materia">
                <div class="materia-header" style="border-color: <?= htmlspecialchars($materia['cor']) ?>">
                    <div class="materia-icon" style="background-color: <?= htmlspecialchars($materia['cor']) ?>">
                        <i class="fas fa-book"></i>
                    </div>
                    <div class="materia-nome"><?= htmlspecialchars($materia['nome']) ?></div>
                </div>

                <div class="materia-content">
                    <div class="progress-info">
                        <span class="progress-label">Progresso Total</span>
                        <span class="progress-percentage"><?= number_format($percentual_estudado, 1) ?>%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill"
                             style="width: <?= $percentual_estudado ?>%;
                                     background-color: <?= htmlspecialchars($materia['cor']) ?>">
                        </div>
                    </div>

                    <div class="status-grid">
                        <div class="status-card nao-estudado">
                            <div class="status-icon">
                                <i class="fas fa-hourglass-start"></i>
                            </div>
                            <div class="status-number"><?= $materia['status']['Não Estudado'] ?></div>
                            <div class="status-label">Não Estudado</div>
                        </div>

                        <div class="status-card estudando">
                            <div class="status-icon">
                                <i class="fas fa-sync-alt"></i>
                            </div>
                            <div class="status-number"><?= $materia['status']['Estudando'] ?></div>
                            <div class="status-label">Estudando</div>
                        </div>

                        <div class="status-card estudado">
                            <div class="status-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="status-number"><?= $materia['status']['Estudado'] ?></div>
                            <div class="status-label">Estudado</div>
                        </div>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
</div>
</body>
</html>
