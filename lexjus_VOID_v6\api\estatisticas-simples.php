<?php
/**
 * API de Estatísticas Simplificada
 * Versão compatível com pg_query para funcionar com a estrutura existente
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Tratar requisições OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Verificar se o usuário está logado
session_start();
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit();
}

// Incluir conexão existente
require_once '../../conexao_POST.php';

$usuario_id = $_SESSION['idusuario'];
$metodo = $_SERVER['REQUEST_METHOD'];
$acao = $_GET['acao'] ?? '';

try {
    switch ($metodo) {
        case 'GET':
            switch ($acao) {
                case 'dashboard':
                    $lei_filtro = $_GET['lei'] ?? '';
                    $periodo_filtro = $_GET['periodo'] ?? '30';
                    obterDashboardSimples($conexao, $usuario_id, $lei_filtro, $periodo_filtro);
                    break;
                case 'comparativo':
                    $lei_filtro = $_GET['lei'] ?? '';
                    $periodo_filtro = $_GET['periodo'] ?? '30';
                    obterComparativoSimples($conexao, $usuario_id, $lei_filtro, $periodo_filtro);
                    break;
                case 'ranking':
                    $lei_filtro = $_GET['lei'] ?? '';
                    $periodo_filtro = $_GET['periodo'] ?? '30';
                    obterRankingSimples($conexao, $usuario_id, $lei_filtro, $periodo_filtro);
                    break;
                case 'tempo_estudo':
                    $lei_filtro = $_GET['lei'] ?? '';
                    $periodo_filtro = $_GET['periodo'] ?? '30';
                    obterTempoEstudoSimples($conexao, $usuario_id, $lei_filtro, $periodo_filtro);
                    break;
                default:
                    obterDashboardSimples($conexao, $usuario_id);
                    break;
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['erro' => 'Método não permitido']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['erro' => 'Erro interno do servidor: ' . $e->getMessage()]);
}

/**
 * Obtém dashboard simplificado
 */
function obterDashboardSimples($conexao, $usuario_id, $lei_filtro = '', $periodo_filtro = '30') {
    // Log para debug
    error_log("🔧 Dashboard API: lei_filtro='$lei_filtro', periodo_filtro='$periodo_filtro'");

    // SEMPRE obter TODAS as leis para popular o filtro
    $query_todas_leis = "
        SELECT id, codigo, nome, nome_completo, cor_tema, icone, total_artigos, ativa
        FROM appestudo.lexjus_leis
        WHERE ativa = true
        ORDER BY ordem_exibicao";

    $result_todas_leis = pg_query($conexao, $query_todas_leis);
    if (!$result_todas_leis) {
        throw new Exception('Erro ao buscar todas as leis: ' . pg_last_error($conexao));
    }

    // Obter leis para calcular estatísticas (pode ser filtrada)
    $query_leis_dados = "
        SELECT id, codigo, nome, nome_completo, cor_tema, icone, total_artigos, ativa
        FROM appestudo.lexjus_leis
        WHERE ativa = true";

    // Aplicar filtro apenas nos dados se especificado
    if (!empty($lei_filtro)) {
        $query_leis_dados .= " AND codigo = '" . pg_escape_string($conexao, $lei_filtro) . "'";
        error_log("🔧 Dashboard API: Aplicando filtro nos dados: $lei_filtro");
    }

    $query_leis_dados .= " ORDER BY ordem_exibicao";
    error_log("🔧 Dashboard API: Query dados: $query_leis_dados");

    $result_leis_dados = pg_query($conexao, $query_leis_dados);
    if (!$result_leis_dados) {
        throw new Exception('Erro ao buscar leis para dados: ' . pg_last_error($conexao));
    }

    // Processar dados das leis (filtradas)
    $leis_dados = [];
    while ($lei = pg_fetch_assoc($result_leis_dados)) {
        $lei_id = $lei['id'];
        
        // Estatísticas de progresso
        $query_progresso = "
            SELECT COUNT(*) as artigos_lidos
            FROM appestudo.lexjus_progresso 
            WHERE usuario_id = $1 AND lei_id = $2 AND lido = true";
        
        $result_progresso = pg_query_params($conexao, $query_progresso, [$usuario_id, $lei_id]);
        $progresso = pg_fetch_assoc($result_progresso);
        $artigos_lidos = (int)$progresso['artigos_lidos'];
        
        // Estatísticas de revisão
        $query_revisao = "
            SELECT 
                COUNT(*) as total_revisoes,
                COUNT(CASE WHEN data_proxima_revisao <= CURRENT_TIMESTAMP THEN 1 END) as pendentes,
                COUNT(CASE WHEN status = 'aprendendo' THEN 1 END) as aprendendo,
                COUNT(CASE WHEN status = 'revisando' THEN 1 END) as revisando,
                COUNT(CASE WHEN status = 'dominado' THEN 1 END) as dominados,
                COUNT(CASE WHEN status = 'dificil' THEN 1 END) as dificeis,
                ROUND(AVG(facilidade), 2) as facilidade_media
            FROM appestudo.lexjus_revisoes 
            WHERE usuario_id = $1 AND lei_id = $2";
        
        $result_revisao = pg_query_params($conexao, $query_revisao, [$usuario_id, $lei_id]);
        $revisao = pg_fetch_assoc($result_revisao);
        
        // Outras estatísticas
        $query_favoritos = "
            SELECT COUNT(*) as favoritos
            FROM appestudo.lexjus_favoritos 
            WHERE usuario_id = $1 AND lei_id = $2";
        
        $result_favoritos = pg_query_params($conexao, $query_favoritos, [$usuario_id, $lei_id]);
        $favoritos = pg_fetch_assoc($result_favoritos);
        
        $query_listas = "
            SELECT COUNT(*) as listas
            FROM appestudo.lexjus_listas 
            WHERE usuario_id = $1 AND lei_id = $2";
        
        $result_listas = pg_query_params($conexao, $query_listas, [$usuario_id, $lei_id]);
        $listas = pg_fetch_assoc($result_listas);
        
        $query_anotacoes = "
            SELECT COUNT(*) as anotacoes
            FROM appestudo.lexjus_anotacoes 
            WHERE usuario_id = $1 AND lei_id = $2";
        
        $result_anotacoes = pg_query_params($conexao, $query_anotacoes, [$usuario_id, $lei_id]);
        $anotacoes = pg_fetch_assoc($result_anotacoes);
        
        // Calcular percentual
        $total_artigos = (int)$lei['total_artigos'];
        $percentual_progresso = $total_artigos > 0 ? round(($artigos_lidos * 100.0) / $total_artigos, 1) : 0;
        
        $leis_dados[] = [
            'codigo' => $lei['codigo'],
            'nome' => $lei['nome'],
            'nome_completo' => $lei['nome_completo'],
            'cor_tema' => $lei['cor_tema'],
            'icone' => $lei['icone'],
            'total_artigos' => $total_artigos,
            'artigos_lidos' => $artigos_lidos,
            'percentual_progresso' => $percentual_progresso,
            'revisao' => [
                'total' => (int)($revisao['total_revisoes'] ?? 0),
                'pendentes' => (int)($revisao['pendentes'] ?? 0),
                'aprendendo' => (int)($revisao['aprendendo'] ?? 0),
                'revisando' => (int)($revisao['revisando'] ?? 0),
                'dominados' => (int)($revisao['dominados'] ?? 0),
                'dificeis' => (int)($revisao['dificeis'] ?? 0),
                'facilidade_media' => (float)($revisao['facilidade_media'] ?? 0)
            ],
            'favoritos' => (int)($favoritos['favoritos'] ?? 0),
            'listas' => (int)($listas['listas'] ?? 0),
            'anotacoes' => (int)($anotacoes['anotacoes'] ?? 0),
            'tempo_estudo' => [
                'total_segundos' => 0,
                'sessoes' => 0,
                'media_por_sessao' => 0
            ]
        ];
    }

    // Criar lista de TODAS as leis para o filtro (sem estatísticas detalhadas)
    $todas_leis = [];
    while ($lei = pg_fetch_assoc($result_todas_leis)) {
        $todas_leis[] = [
            'codigo' => $lei['codigo'],
            'nome' => $lei['nome'],
            'nome_completo' => $lei['nome_completo'],
            'cor_tema' => $lei['cor_tema'],
            'icone' => $lei['icone'],
            'total_artigos' => (int)$lei['total_artigos']
        ];
    }

    // Calcular resumo geral baseado nos dados filtrados
    $total_artigos_lidos = array_sum(array_column($leis_dados, 'artigos_lidos'));
    $total_artigos_disponiveis = array_sum(array_column($leis_dados, 'total_artigos'));
    $total_revisoes = array_sum(array_map(function($lei) { return $lei['revisao']['total']; }, $leis_dados));
    $total_pendentes = array_sum(array_map(function($lei) { return $lei['revisao']['pendentes']; }, $leis_dados));
    $total_favoritos = array_sum(array_column($leis_dados, 'favoritos'));
    $total_listas = array_sum(array_column($leis_dados, 'listas'));
    $total_anotacoes = array_sum(array_column($leis_dados, 'anotacoes'));

    $percentual_geral = $total_artigos_disponiveis > 0 ?
        round(($total_artigos_lidos * 100.0) / $total_artigos_disponiveis, 1) : 0;

    error_log("🔧 Dashboard API: Calculado para " . count($leis_dados) . " lei(s) filtrada(s)");
    error_log("🔧 Dashboard API: Total artigos lidos: $total_artigos_lidos de $total_artigos_disponiveis");

    $dashboard = [
        'leis' => $todas_leis, // TODAS as leis para o filtro
        'leis_dados' => $leis_dados, // Dados filtrados para exibição
        'resumo_geral' => [
            'total_artigos_lidos' => $total_artigos_lidos,
            'total_artigos_disponiveis' => $total_artigos_disponiveis,
            'percentual_geral' => $percentual_geral,
            'total_revisoes' => $total_revisoes,
            'total_pendentes' => $total_pendentes,
            'total_favoritos' => $total_favoritos,
            'total_listas' => $total_listas,
            'total_anotacoes' => $total_anotacoes,
            'tempo_total_estudo' => 0,
            'facilidade_media_global' => 0
        ]
    ];
    
    echo json_encode([
        'sucesso' => true,
        'dashboard' => $dashboard
    ]);
}

/**
 * Obtém comparativo simplificado entre leis
 */
function obterComparativoSimples($conexao, $usuario_id) {
    $query = "
        SELECT 
            l.codigo,
            l.nome,
            l.cor_tema,
            l.icone,
            l.total_artigos,
            COALESCE(p.artigos_lidos, 0) as artigos_lidos,
            CASE 
                WHEN l.total_artigos > 0 THEN 
                    ROUND((COALESCE(p.artigos_lidos, 0) * 100.0 / l.total_artigos), 1)
                ELSE 0 
            END as percentual_progresso,
            COALESCE(r.total_revisoes, 0) as total_revisoes,
            COALESCE(r.dominados, 0) as dominados,
            COALESCE(r.facilidade_media, 0) as facilidade_media,
            COALESCE(f.favoritos, 0) as favoritos
        FROM appestudo.lexjus_leis l
        LEFT JOIN (
            SELECT lei_id, COUNT(*) as artigos_lidos
            FROM appestudo.lexjus_progresso 
            WHERE usuario_id = $1 AND lido = true
            GROUP BY lei_id
        ) p ON l.id = p.lei_id
        LEFT JOIN (
            SELECT 
                lei_id,
                COUNT(*) as total_revisoes,
                COUNT(CASE WHEN status = 'dominado' THEN 1 END) as dominados,
                ROUND(AVG(facilidade), 2) as facilidade_media
            FROM appestudo.lexjus_revisoes 
            WHERE usuario_id = $1
            GROUP BY lei_id
        ) r ON l.id = r.lei_id
        LEFT JOIN (
            SELECT lei_id, COUNT(*) as favoritos
            FROM appestudo.lexjus_favoritos 
            WHERE usuario_id = $1
            GROUP BY lei_id
        ) f ON l.id = f.lei_id
        WHERE l.ativa = true
        ORDER BY percentual_progresso DESC, l.ordem_exibicao
    ";
    
    $result = pg_query_params($conexao, $query, [$usuario_id]);
    if (!$result) {
        throw new Exception('Erro ao buscar comparativo: ' . pg_last_error($conexao));
    }
    
    $leis = [];
    while ($row = pg_fetch_assoc($result)) {
        $leis[] = [
            'codigo' => $row['codigo'],
            'nome' => $row['nome'],
            'cor_tema' => $row['cor_tema'],
            'icone' => $row['icone'],
            'total_artigos' => (int)$row['total_artigos'],
            'artigos_lidos' => (int)$row['artigos_lidos'],
            'percentual_progresso' => (float)$row['percentual_progresso'],
            'total_revisoes' => (int)$row['total_revisoes'],
            'dominados' => (int)$row['dominados'],
            'facilidade_media' => (float)$row['facilidade_media'],
            'favoritos' => (int)$row['favoritos']
        ];
    }
    
    echo json_encode([
        'sucesso' => true,
        'comparativo' => $leis
    ]);
}

/**
 * Obtém ranking simplificado de leis
 */
function obterRankingSimples($conexao, $usuario_id) {
    // Usar a mesma query do comparativo para o ranking
    obterComparativoSimples($conexao, $usuario_id);
    
    // Converter resposta para formato de ranking
    $response = json_decode(ob_get_contents(), true);
    ob_clean();
    
    if ($response && $response['sucesso']) {
        $leis = $response['comparativo'];
        
        // Criar diferentes rankings
        $rankings = [
            'por_progresso' => $leis,
            'por_facilidade' => $leis,
            'por_dominancia' => $leis,
            'por_atividade' => $leis
        ];
        
        // Ordenar por facilidade
        usort($rankings['por_facilidade'], function($a, $b) {
            return $b['facilidade_media'] <=> $a['facilidade_media'];
        });
        
        // Ordenar por dominância
        usort($rankings['por_dominancia'], function($a, $b) {
            $percentual_a = $a['total_revisoes'] > 0 ? ($a['dominados'] / $a['total_revisoes']) * 100 : 0;
            $percentual_b = $b['total_revisoes'] > 0 ? ($b['dominados'] / $b['total_revisoes']) * 100 : 0;
            return $percentual_b <=> $percentual_a;
        });
        
        echo json_encode([
            'sucesso' => true,
            'rankings' => $rankings
        ]);
    } else {
        echo json_encode([
            'sucesso' => false,
            'erro' => 'Erro ao gerar ranking'
        ]);
    }
}

/**
 * Obtém dados de tempo de estudo simplificados
 */
function obterTempoEstudoSimples($conexao, $usuario_id) {
    // Por enquanto, retornar dados vazios
    // Pode ser implementado quando houver dados de tempo
    
    echo json_encode([
        'sucesso' => true,
        'tempo_estudo' => [
            'por_lei' => [],
            'historico_diario' => []
        ]
    ]);
}
?>
