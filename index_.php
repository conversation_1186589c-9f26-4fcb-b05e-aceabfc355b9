<?php
// index.php

// O header.php já foi incluído no início deste arquivo e fez:
// - session_start()
// - include conexao_POST.php
// - include auth.php
// - verificarAutenticacao($conexao);
// - include processa_index.php (que carrega dados do usuário)

// Definimos a constante aqui para que os dashboards incluídos saibam que estão sendo carregados corretamente.
define('MEU_SISTEMA_PHP_DASHBOARD_VALIDO', true); // Usando um nome de constante específico para dashboards

// O header é incluído ANTES desta lógica geralmente.
// Se o seu index.php tem o include 'includes/header.php'; no topo,
// a constante deve ser definida APÓS o header, mas ANTES dos includes dos dashboards.
// Vou assumir que a estrutura do seu index.php é:
// include header.php
// [HTML e JS das abas]
// include dashboard-inicio.php
// include dashboard-agenda.php
// etc.
// include footer.php

// Então, o local para definir a constante é logo antes dos includes dos dashboards.
// Se a sua estrutura for diferente, ajuste.
?>

<?php include_once 'includes/header.php'; ?>
<?php
// DEFINIR A CONSTANTE AQUI, APÓS O HEADER E ANTES DOS DASHBOARDS
if (!defined('MEU_SISTEMA_PHP_DASHBOARD_VALIDO')) { // Para evitar redefinir se já existir por algum motivo
    define('MEU_SISTEMA_PHP_DASHBOARD_VALIDO', true);
}
?>

<main class="tabs-container">
        <div class="tabs-wrapper">
            <nav class="tabs">
                <button class="tab active" data-tab="inicio">
                    <i class="fas fa-home"></i> Início
                </button>
                <button class="tab" data-tab="estudo">
                    <i class="fas fa-book"></i> Estudo
                </button>
                <button class="tab" data-tab="agenda">
                    <i class="fas fa-calendar"></i> Agenda
                </button>
                <button class="tab" data-tab="estatistica">
                    <i class="fas fa-chart-line"></i> Estatística
                </button>
                <!-- Adicione as outras abas -->
            </nav>
        </div>

    <!-- Conteúdo das abas -->
    <div id="inicio" class="content active">
        <?php include 'dashboard-inicio.php'; // Este arquivo verificará a constante ?>
    </div>

    <div id="agenda" class="content">
        <?php include 'dashboard-agenda.php'; // Este arquivo verificará a constante ?>
    </div>

    <div id="estatistica" class="content">
        <?php include 'dashboard-estatistica.php'; // Este arquivo verificará a constante ?>
    </div>

    <div id="estudo" class="content">
        <?php include 'dashboard-estudo.php'; // Este arquivo verificará a constante ?>
    </div>
    <!-- Adicione as outras seções -->
</main>

<?php include 'includes/footer.php'; ?>
<!-- Seus scripts JS ... -->


    
    <script>
    (function() {
        const menuToggle = document.querySelector('.menu-toggle');
        const tabsWrapper = document.querySelector('.tabs-wrapper');
        const tabs = document.querySelectorAll('.tab');
        const contents = document.querySelectorAll('.content');

        // Adiciona botão de menu mobile se não existir
        if (!menuToggle) {
            const button = document.createElement('button');
            button.className = 'menu-toggle';
            button.innerHTML = '<i class="fas fa-bars"></i>';
            document.body.appendChild(button);
        }

        // Toggle menu mobile
        document.querySelector('.menu-toggle').addEventListener('click', () => {
            tabsWrapper.classList.toggle('active');
        });

        // Fecha menu ao clicar fora
        document.addEventListener('click', (e) => {
            if (!tabsWrapper.contains(e.target) && !e.target.classList.contains('menu-toggle')) {
                tabsWrapper.classList.remove('active');
            }
        });

        // Navegação das abas
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const target = tab.dataset.tab;
                
                // Remove classes ativas
                tabs.forEach(t => t.classList.remove('active'));
                contents.forEach(c => c.classList.remove('active'));
                
                // Adiciona classes ativas
                tab.classList.add('active');
                document.getElementById(target).classList.add('active');

                // Fecha menu mobile após selecionar aba
                if (window.innerWidth <= 768) {
                    tabsWrapper.classList.remove('active');
                }

                // Dispara evento de resize para ajustar gráficos
                setTimeout(() => {
                    window.dispatchEvent(new Event('resize'));
                }, 100);
            });
        });

        // Ajusta layout em resize
        const mobileLayoutHandler = (() => {
            let timer;
            return () => {
                clearTimeout(timer);
                timer = setTimeout(() => {
                    if (window.innerWidth > 768) {
                        tabsWrapper.classList.remove('active');
                    }
                }, 250);
            };
        })();

        window.addEventListener('resize', mobileLayoutHandler);
    })();
    </script>

    <script>
    // Função para verificar se esta é a página inicial do sistema
    
    /* Ambiente de Teste
    function isPaginaInicial() {
        const path = window.location.pathname;
        const validPaths = [
            '/meu_sistema_php/sistema_principal/index.php',
            '/meu_sistema_php/sistema_principal/'
        ];
        return validPaths.includes(path);
    } */

    //Ambiente de Produção
    function isPaginaInicial() {
    const path = window.location.pathname;
    const validPaths = [
        '/index.php',
        '/',
        'https://planejaaqui.com.br/index.php',
        'https://planejaaqui.com.br/'
    ];
    return validPaths.includes(path);
}

    // Só executa o código de atualização se for realmente a página inicial
    if (isPaginaInicial()) {
        console.log('Script de atualização iniciado na página inicial');
        let ultimaAtualizacaoInicial = localStorage.getItem('atualizarPaginaInicial');

        setInterval(function() {
            const novaAtualizacao = localStorage.getItem('atualizarPaginaInicial');
            
            if (novaAtualizacao && novaAtualizacao !== ultimaAtualizacaoInicial) {
                ultimaAtualizacaoInicial = novaAtualizacao;
                console.log('Atualizando página inicial do sistema');
                window.location.reload();
            }
        }, 1000);
    } else {
        console.log('Esta não é a página inicial - script de atualização não iniciado');
    }

    
    </script>

    <script>
    window.addEventListener('storage', function(event) {
        if (event.key === 'recarregarIndex') {
            window.location.reload();
        }
    });
    </script>
