<?php
//plano_estudo_logic.php

/**
 * Algoritmo de balanceamento inteligente baseado em pesos e prioridades
 */
function balancearConteudosInteligente($conteudos, $conexao, $usuario_id) {
    // 1. Buscar prova ativa do usuário
    $query_prova_ativa = "SELECT id FROM appestudo.provas
                          WHERE usuario_id = $1 AND status = true
                          ORDER BY created_at DESC LIMIT 1";
    $result_prova = pg_query_params($conexao, $query_prova_ativa, array($usuario_id));

    if (!$result_prova || pg_num_rows($result_prova) == 0) {
        // Se não há prova ativa, retornar sem balanceamento
        return $conteudos;
    }

    $prova_ativa = pg_fetch_assoc($result_prova);
    $prova_id = $prova_ativa['id'];

    // 2. Buscar pesos e prioridades específicos da prova ativa
    $query_pesos = "SELECT
        m.idmateria,
        m.nome as materia_nome,
        COALESCE(pm.peso, 1) as peso_configurado,
        COALESCE(pm.nivel_dificuldade, 3) as dificuldade_configurada
    FROM appestudo.materia m
    LEFT JOIN appestudo.pesos_materias pm ON m.idmateria = pm.materia_id AND pm.prova_id = $1
    WHERE m.idmateria IN (
        SELECT DISTINCT ce.materia_id
        FROM appestudo.conteudo_edital ce
        JOIN appestudo.usuario_edital ue ON ce.edital_id = ue.edital_id
        WHERE ue.usuario_id = $2
    )
    ORDER BY m.nome";

    $result_pesos = pg_query_params($conexao, $query_pesos, array($prova_id, $usuario_id));

    // 3. Processar pesos das matérias
    $pesos_materias = [];
    if ($result_pesos) {
        while ($row = pg_fetch_assoc($result_pesos)) {
            $prioridade = $row['peso_configurado'] * $row['dificuldade_configurada'];
            $pesos_materias[$row['idmateria']] = [
                'nome' => $row['materia_nome'],
                'peso' => $row['peso_configurado'],
                'dificuldade' => $row['dificuldade_configurada'],
                'prioridade' => $prioridade
            ];
        }
    }

    // 4. Agrupar conteúdos por matéria
    $conteudos_por_materia = [];
    foreach ($conteudos as $conteudo) {
        $materia_id = $conteudo['idmateria'];
        if (!isset($conteudos_por_materia[$materia_id])) {
            $conteudos_por_materia[$materia_id] = [];
        }
        $conteudos_por_materia[$materia_id][] = $conteudo;
    }

    // 5. Calcular distribuição baseada em prioridades
    $total_prioridade = 0;
    foreach ($conteudos_por_materia as $materia_id => $conteudos_materia) {
        $prioridade = isset($pesos_materias[$materia_id]) ? $pesos_materias[$materia_id]['prioridade'] : 1;
        $total_prioridade += $prioridade * count($conteudos_materia);
    }

    // 6. Distribuir conteúdos proporcionalmente
    $conteudos_balanceados = [];
    foreach ($conteudos_por_materia as $materia_id => $conteudos_materia) {
        $prioridade = isset($pesos_materias[$materia_id]) ? $pesos_materias[$materia_id]['prioridade'] : 1;

        // Adicionar informação de prioridade a cada conteúdo
        foreach ($conteudos_materia as $conteudo) {
            $conteudo['prioridade_materia'] = $prioridade;
            $conteudo['peso_materia'] = isset($pesos_materias[$materia_id]) ? $pesos_materias[$materia_id]['peso'] : 1;
            $conteudo['dificuldade_materia'] = isset($pesos_materias[$materia_id]) ? $pesos_materias[$materia_id]['dificuldade'] : 1;
            $conteudos_balanceados[] = $conteudo;
        }
    }

    // 7. Aplicar intercalação inteligente
    $conteudos_intercalados = intercalarMateriasInteligente($conteudos_balanceados);

    return $conteudos_intercalados;
}

/**
 * Intercala matérias de forma inteligente com frequência proporcional à prioridade
 */
function intercalarMateriasInteligente($conteudos) {
    // 1. Agrupar por matéria e calcular frequências
    $materias_dados = [];
    foreach ($conteudos as $conteudo) {
        $materia = $conteudo['materia_nome'];
        $prioridade = $conteudo['prioridade_materia'];

        if (!isset($materias_dados[$materia])) {
            $materias_dados[$materia] = [
                'conteudos' => [],
                'prioridade' => $prioridade,
                'frequencia' => 0,
                'contador' => 0
            ];
        }

        $materias_dados[$materia]['conteudos'][] = $conteudo;
    }

    // 2. Calcular frequência proporcional baseada na prioridade
    $prioridade_total = 0;
    foreach ($materias_dados as $dados) {
        $prioridade_total += $dados['prioridade'] * count($dados['conteudos']);
    }

    foreach ($materias_dados as $materia => $dados) {
        $peso_materia = $dados['prioridade'] * count($dados['conteudos']);
        $materias_dados[$materia]['frequencia'] = $peso_materia / $prioridade_total;
    }

    // 3. Intercalar usando algoritmo de distribuição proporcional
    $resultado = [];
    $total_conteudos = count($conteudos);
    $max_consecutivos = 2; // Máximo 2 itens consecutivos da mesma matéria

    $materia_anterior = null;
    $consecutivos_atual = 0;

    for ($i = 0; $i < $total_conteudos; $i++) {
        $melhor_materia = null;
        $melhor_score = -1;

        // Encontrar a matéria que mais "merece" aparecer agora
        foreach ($materias_dados as $materia => $dados) {
            // Pular se não há mais conteúdos desta matéria
            if (empty($dados['conteudos'])) {
                continue;
            }

            // Pular se excederia o limite de consecutivos
            if ($materia === $materia_anterior && $consecutivos_atual >= $max_consecutivos) {
                continue;
            }

            // Calcular score: frequência esperada vs atual
            $frequencia_esperada = $dados['frequencia'] * ($i + 1);
            $frequencia_atual = $dados['contador'];
            $score = $frequencia_esperada - $frequencia_atual;

            // Bonus para quebrar sequências longas
            if ($materia !== $materia_anterior) {
                $score += 0.1;
            }

            if ($score > $melhor_score) {
                $melhor_score = $score;
                $melhor_materia = $materia;
            }
        }

        // Adicionar conteúdo da matéria escolhida
        if ($melhor_materia) {
            $conteudo = array_shift($materias_dados[$melhor_materia]['conteudos']);
            $resultado[] = $conteudo;
            $materias_dados[$melhor_materia]['contador']++;

            // Atualizar controle de consecutivos
            if ($melhor_materia === $materia_anterior) {
                $consecutivos_atual++;
            } else {
                $consecutivos_atual = 1;
                $materia_anterior = $melhor_materia;
            }
        }
    }

    return $resultado;
}

/**
 * Remove pais que vão aparecer junto com seus filhos para evitar duplicação visual
 */
function removerPaisComFilhos($conteudos) {
    $conteudosSemDuplicacao = [];
    foreach ($conteudos as $conteudo) {
        $capitulo = $conteudo['capitulo'];
        $materia_id = $conteudo['idmateria'];

        // Verificar se este capítulo é pai de algum outro nos conteúdos
        $temFilhoNaLista = false;
        foreach ($conteudos as $possivel_filho) {
            if ($possivel_filho['idmateria'] == $materia_id &&
                $possivel_filho['capitulo'] !== $capitulo &&
                strpos($possivel_filho['capitulo'], $capitulo . '.') === 0) {
                $temFilhoNaLista = true;
                break;
            }
        }

        // Se não tem filho na lista, pode incluir
        if (!$temFilhoNaLista) {
            $conteudosSemDuplicacao[] = $conteudo;
        }
    }

    return $conteudosSemDuplicacao;
}

/**
 * Filtra todos os conteúdos de forma inteligente baseada na estrutura da matéria
 * NOVA LÓGICA: Analisa a estrutura e decide se deve manter todos os itens ou apenas folhas
 */
function filtrarTodosConteudos($conteudos) {
    // Agrupar conteúdos por matéria
    $por_materia = [];
    foreach ($conteudos as $conteudo) {
        $materia = $conteudo['materia_nome'] ?? $conteudo['idmateria'] ?? 'default';
        $por_materia[$materia][] = $conteudo;
    }

    $filtrados = [];

    foreach ($por_materia as $materia => $conteudos_materia) {
        // Analisar a estrutura da matéria para decidir a estratégia
        $estrategia = analisarEstruturaMaterias($conteudos_materia, $materia);

        if ($estrategia === 'manter_todos') {
            // Manter TODOS os itens (caso Economia, Português, etc.)
            $filtrados = array_merge($filtrados, $conteudos_materia);
        } else {
            // Aplicar filtro tradicional (remover pais que têm filhos)
            $filtrados = array_merge($filtrados, filtrarTradicional($conteudos_materia, $materia));
        }
    }

    return $filtrados;
}

/**
 * Analisa a estrutura de uma matéria para decidir a estratégia de filtro
 */
function analisarEstruturaMaterias($conteudos, $materia) {
    // Contar níveis de hierarquia
    $niveis = [];
    $total_itens = count($conteudos);

    foreach ($conteudos as $conteudo) {
        $partes = explode('.', $conteudo['capitulo']);
        $nivel = count($partes);
        $niveis[$nivel] = ($niveis[$nivel] ?? 0) + 1;
    }

    // Estratégias baseadas na estrutura:

    // 1. Para matérias específicas que devem usar filtro tradicional (remover pais)
    $materias_filtrar_tradicional = ['economia'];
    foreach ($materias_filtrar_tradicional as $mat) {
        if (stripos($materia, $mat) !== false) {
            return 'filtrar_tradicional';
        }
    }

    // 2. Se tem poucos itens (≤ 20) e estrutura simples (máximo 3 níveis), manter todos
    if ($total_itens <= 20 && count($niveis) <= 3) {
        return 'manter_todos';
    }

    // 3. Para matérias específicas que sabemos que devem manter todos
    $materias_manter_todos = ['português', 'direito constitucional'];
    foreach ($materias_manter_todos as $mat) {
        if (stripos($materia, $mat) !== false) {
            return 'manter_todos';
        }
    }

    // 4. Se tem muitos itens (> 50) e muitos níveis (> 3), usar filtro tradicional
    if ($total_itens > 50 && count($niveis) > 3) {
        return 'filtrar_tradicional';
    }

    // 5. Padrão: manter todos (mais conservador)
    return 'manter_todos';
}

/**
 * Aplica o filtro tradicional (remove pais que têm filhos)
 */
function filtrarTradicional($conteudos, $materia) {
    $temFilhos = [];

    foreach ($conteudos as $conteudo) {
        $capitulo = $conteudo['capitulo'];
        $partes = explode('.', $capitulo);

        if (count($partes) > 1) {
            array_pop($partes);
            $pai = implode('.', $partes);
            $chave_pai = $materia . '|' . $pai;
            $temFilhos[$chave_pai] = true;
        }
    }

    $filtrados = [];
    foreach ($conteudos as $conteudo) {
        $chave_conteudo = $materia . '|' . $conteudo['capitulo'];
        if (!isset($temFilhos[$chave_conteudo])) {
            $filtrados[] = $conteudo;
        }
    }

    return $filtrados;
}

function debugOutput($label, $data) {
    echo "<div style='background:#f8f9fa;padding:10px;margin:10px;border:1px solid #ddd;'>";
    echo "<strong>$label:</strong><br>";
    echo "<pre>";
    print_r($data);
    echo "</pre>";
    echo "</div>";
}
class PlanoEstudoLogic {
    private $conexao;
    private $usuario_id;
    private $prova;
    private $conteudos;
    private $info_estudo;

    private $dias_estudo = [];

    public function __construct($conexao) {
        $this->conexao = $conexao;
        $this->usuario_id = $this->validarUsuario();
        $this->carregarDiasEstudo();
        $this->prova = $this->getProvaAtiva();
        $this->info_estudo = $this->getInfoEstudo();
    }

    private function carregarDiasEstudo() {
        $query = "SELECT dia_semana, horas_estudo 
                 FROM appestudo.usuario_dias_estudo 
                 WHERE usuario_id = $1 
                 AND ativo = true 
                 ORDER BY dia_semana";
        $result = pg_query_params($this->conexao, $query, array($this->usuario_id));
        
        $this->dias_estudo = [];
        while ($row = pg_fetch_assoc($result)) {
            $this->dias_estudo[intval($row['dia_semana'])] = floatval($row['horas_estudo']);
        }
        
        if (empty($this->dias_estudo)) {
            for ($i = 1; $i <= 5; $i++) {
                $this->dias_estudo[$i] = 3.0;
            }
        }
    }

    
    public function getDiasEstudo() {
        return array_keys($this->dias_estudo); // Retorna array como [1, 2, 3, ...]
    }

    private function validarUsuario() {
        $usuario_id = filter_var($_SESSION['idusuario'], FILTER_VALIDATE_INT);
        if (!$usuario_id || $usuario_id <= 0) {
            header('Location: /login_index.php');
            exit();
        }
        return $usuario_id;
    }

    public function getProvaAtiva() {
        // Verificar se estamos em um contexto de API
        $is_api_context = defined('IS_API_CONTEXT') && IS_API_CONTEXT;
    
        $query = "SELECT p.*, ap.horas_estudo_dia, 
                 COALESCE(p.replanejado, false) as replanejado 
                 FROM appestudo.provas p
                 LEFT JOIN appestudo.ajustes_plano ap ON p.id = ap.prova_id
                 WHERE p.usuario_id = $1 
                 AND p.status = true 
                 ORDER BY p.created_at DESC 
                 LIMIT 1";
        
        // Log a consulta SQL para debug (apenas se não estivermos em um contexto de API)
        if (!$is_api_context) {
            error_log("Query getProvaAtiva: " . $query);
        }
        
        $result = pg_query_params($this->conexao, $query, array($this->usuario_id));
        
        // Verifica erros na consulta
        if (!$result) {
            error_log("Erro na consulta getProvaAtiva: " . pg_last_error($this->conexao));
        }
        
        $prova = pg_fetch_assoc($result);
    
        // Exibe o resultado bruto para debug
        if (!$is_api_context) {
            error_log("Resultado bruto da prova: " . print_r($prova, true));
        }
    
        if (!$prova) {
            if (!$is_api_context) {
                header('Location: configurar_prova.php');
                exit();
            } else {
                return null;
            }
        }
    
        // Valor original para debug
        $original_value = $prova['replanejado'];
        
        // Converte explicitamente o valor para booleano, considerando todos os formatos possíveis
        $prova['replanejado'] = (
            $prova['replanejado'] === 't' || 
            $prova['replanejado'] === true || 
            $prova['replanejado'] === 1 || 
            $prova['replanejado'] === '1' || 
            $prova['replanejado'] === 'true'
        );
        
        // Debug para verificar o valor da flag (apenas se não estivermos em um contexto de API)
        if (!$is_api_context) {
            error_log("Valor original da flag replanejado: " . var_export($original_value, true));
            error_log("Tipo original: " . gettype($original_value));
            error_log("Valor normalizado da flag replanejado: " . var_export($prova['replanejado'], true));
            error_log("Tipo após normalização: " . gettype($prova['replanejado']));
            
            // Adiciona um console.log para debug no navegador
            echo "<script>
                //console.log('DADOS DA PROVA (getProvaAtiva):');
                //console.log('Flag replanejado - valor original: ', " . json_encode($original_value) . ");
                //console.log('Flag replanejado - valor normalizado: ', " . json_encode($prova['replanejado']) . ");
            </script>";
        }
        
        return $prova;
    }

    public function getConteudos() {
        $query = "
        WITH pesos_calculados AS (
            SELECT DISTINCT
                m.idmateria,
                COALESCE(pm.peso, 1.0) as peso_base,
                -- AQUI: Elevamos o peso ao quadrado (POWER(..., 2)) para dar mais destaque ao Prio 5
                -- Usamos GREATEST(1.0, ...) para garantir que a prioridade nunca seja < 1
                GREATEST(1.0, POWER(COALESCE(pm.peso, 1.0), 2) * COALESCE(pm.nivel_dificuldade, 1.0) * CASE
                    WHEN (SELECT data_prova::date - CURRENT_DATE <= 7
                            FROM appestudo.provas
                            WHERE usuario_id = $1 AND status = true LIMIT 1)
                    THEN 2.0
                    ELSE 1.0
                END) as prioridade_calculada
            FROM appestudo.materia m
            LEFT JOIN appestudo.pesos_materias pm ON m.idmateria = pm.materia_id
            LEFT JOIN appestudo.provas p ON pm.prova_id = p.id AND p.status = true
            WHERE p.usuario_id = $1
        ),
        conteudo_base AS (
            SELECT DISTINCT
                uc.id,
                m.idmateria,
                m.nome AS materia_nome,
                m.cor,
                ce.descricao,
                ce.capitulo,
                pc.prioridade_calculada,
                uc.replanejado_em,
    
                -- Nível 1 (Principal)
                CASE
                    WHEN strpos(ce.capitulo, '.') > 0 THEN
                        (SELECT tn.capitulo || '. ' || tn.descricao
                            FROM appestudo.conteudo_edital tn
                            WHERE tn.materia_id = ce.materia_id
                            AND tn.capitulo = split_part(ce.capitulo, '.', 1)
                            LIMIT 1)
                    ELSE NULL
                END as descricao_capitulo_principal,
    
                -- Nível 2
                CASE
                    WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 2 THEN
                        (SELECT cp.capitulo || '. ' || cp.descricao
                            FROM appestudo.conteudo_edital cp
                            WHERE cp.materia_id = ce.materia_id
                            AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || split_part(ce.capitulo, '.', 2)
                            LIMIT 1)
                    ELSE NULL
                END as descricao_capitulo_secundario,
    
                -- Nível 3
                CASE
                    WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 3 THEN
                        (SELECT cp.capitulo || '. ' || cp.descricao
                            FROM appestudo.conteudo_edital cp
                            WHERE cp.materia_id = ce.materia_id
                            AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' ||
                                    split_part(ce.capitulo, '.', 2) || '.' ||
                                    split_part(ce.capitulo, '.', 3)
                            LIMIT 1)
                    ELSE NULL
                END as descricao_capitulo_terciario,
    
                -- Nível 4
                CASE
                    WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 4 THEN
                        (SELECT cp.capitulo || '. ' || cp.descricao
                            FROM appestudo.conteudo_edital cp
                            WHERE cp.materia_id = ce.materia_id
                            AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' ||
                                    split_part(ce.capitulo, '.', 2) || '.' ||
                                    split_part(ce.capitulo, '.', 3) || '.' ||
                                    split_part(ce.capitulo, '.', 4)
                            LIMIT 1)
                    ELSE NULL
                END as descricao_capitulo_quaternario,
    
                -- Nível 5
                CASE
                    WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 5 THEN
                        (SELECT cp.capitulo || '. ' || cp.descricao
                            FROM appestudo.conteudo_edital cp
                            WHERE cp.materia_id = ce.materia_id
                            AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' ||
                                    split_part(ce.capitulo, '.', 2) || '.' ||
                                    split_part(ce.capitulo, '.', 3) || '.' ||
                                    split_part(ce.capitulo, '.', 4) || '.' ||
                                    split_part(ce.capitulo, '.', 5)
                            LIMIT 1)
                    ELSE NULL
                END as descricao_capitulo_quinario,
    
                -- Nível 6
                CASE
                    WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 6 THEN
                        (SELECT cp.capitulo || '. ' || cp.descricao
                            FROM appestudo.conteudo_edital cp
                            WHERE cp.materia_id = ce.materia_id
                            AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' ||
                                    split_part(ce.capitulo, '.', 2) || '.' ||
                                    split_part(ce.capitulo, '.', 3) || '.' ||
                                    split_part(ce.capitulo, '.', 4) || '.' ||
                                    split_part(ce.capitulo, '.', 5) || '.' ||
                                    split_part(ce.capitulo, '.', 6)
                            LIMIT 1)
                    ELSE NULL
                END as descricao_capitulo_sexto,
    
                -- Nível 7
                CASE
                    WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 7 THEN
                        (SELECT cp.capitulo || '. ' || cp.descricao
                            FROM appestudo.conteudo_edital cp
                            WHERE cp.materia_id = ce.materia_id
                            AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' ||
                                    split_part(ce.capitulo, '.', 2) || '.' ||
                                    split_part(ce.capitulo, '.', 3) || '.' ||
                                    split_part(ce.capitulo, '.', 4) || '.' ||
                                    split_part(ce.capitulo, '.', 5) || '.' ||
                                    split_part(ce.capitulo, '.', 6) || '.' ||
                                    split_part(ce.capitulo, '.', 7)
                            LIMIT 1)
                    ELSE NULL
                END as descricao_capitulo_setimo,
    
                uc.status_estudo,
                CAST(split_part(ce.capitulo, '.', 1) AS INTEGER) AS capitulo_principal,
                COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 2), '') AS INTEGER), 0) AS subnivel1,
                COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 3), '') AS INTEGER), 0) AS subnivel2,
                COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 4), '') AS INTEGER), 0) AS subnivel3,
                COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 5), '') AS INTEGER), 0) AS subnivel4,
                COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 6), '') AS INTEGER), 0) AS subnivel5,
                COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 7), '') AS INTEGER), 0) AS subnivel6,
                ce.ordem,
                ROW_NUMBER() OVER (
                    PARTITION BY ce.materia_id
                    ORDER BY
                        CAST(split_part(ce.capitulo, '.', 1) AS INTEGER),
                        COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 2), '') AS INTEGER), 0),
                        COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 3), '') AS INTEGER), 0),
                        COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 4), '') AS INTEGER), 0),
                        COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 5), '') AS INTEGER), 0),
                        COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 6), '') AS INTEGER), 0),
                        COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 7), '') AS INTEGER), 0)
                ) as ordem_na_materia
            FROM
                appestudo.usuario_conteudo AS uc
            JOIN
                appestudo.conteudo_edital AS ce ON uc.conteudo_id = ce.id_conteudo
            JOIN
                appestudo.materia AS m ON ce.materia_id = m.idmateria
            JOIN
                pesos_calculados pc ON m.idmateria = pc.idmateria -- Junta com pesos_calculados (agora turbinado)
            WHERE
                uc.usuario_id = $1
                AND uc.status = true
                AND ( -- Garante que o formato do capítulo seja numérico/hierárquico
                    ce.capitulo ~ '^\\d+$' OR
                    ce.capitulo ~ '^\\d+\\.\\d+$' OR
                    ce.capitulo ~ '^\\d+\\.\\d+\\.\\d+$' OR
                    ce.capitulo ~ '^\\d+\\.\\d+\\.\\d+\\.\\d+$' OR
                    ce.capitulo ~ '^\\d+\\.\\d+\\.\\d+\\.\\d+\\.\\d+$' OR
                    ce.capitulo ~ '^\\d+\\.\\d+\\.\\d+\\.\\d+\\.\\d+\\.\\d+$' OR
                    ce.capitulo ~ '^\\d+\\.\\d+\\.\\d+\\.\\d+\\.\\d+\\.\\d+\\.\\d+$'
                )
        )
        -- SELECT final
        SELECT
            cb.id,
            cb.idmateria,
            cb.materia_nome,
            cb.cor,
            cb.descricao,
            cb.capitulo,
            cb.descricao_capitulo_principal,
            cb.descricao_capitulo_secundario,
            cb.descricao_capitulo_terciario,
            cb.descricao_capitulo_quaternario,
            cb.descricao_capitulo_quinario,
            cb.descricao_capitulo_sexto,
            cb.descricao_capitulo_setimo,
            cb.status_estudo,
            cb.capitulo_principal,
            cb.subnivel1,
            cb.subnivel2,
            cb.subnivel3,
            cb.subnivel4,
            cb.subnivel5,
            cb.subnivel6,
            cb.ordem,
            cb.ordem_na_materia,
            cb.prioridade_calculada,
            cb.replanejado_em
        FROM conteudo_base cb
        -- A ORDENAÇÃO PONDERADA (usando a prioridade turbinada):
        ORDER BY
            (cb.ordem_na_materia / cb.prioridade_calculada) ASC;
        ";
    
        // Executa a consulta e busca todos os resultados
        $result = pg_query_params($this->conexao, $query, array($this->usuario_id));
        $this->conteudos = pg_fetch_all($result);
    
        // Retorna os conteúdos ordenados
        return $this->conteudos;
    }

    public function getInfoEstudo() {
        // Garante que os conteúdos foram carregados
        if ($this->conteudos === null) {
            $this->getConteudos();
        }
        
        // Filtrar conteúdos que têm filhos
        $conteudosFiltrados = filtrarTodosConteudos($this->conteudos);
        $total_conteudos = count($conteudosFiltrados);
        
        $calculo = new Calculos($this->prova, $total_conteudos, $this->conexao);
        
        // Pega os conteúdos organizados por semana (já filtrados)
        $conteudosPorSemana = $this->organizarConteudosPorSemanaFiltrados();
        
        // Inicializa array para contar cards por dia da semana
        $cards_por_dia = array_fill(0, count($this->dias_estudo), 0);
        
        // Soma os cards de cada dia através de todas as semanas
        foreach ($conteudosPorSemana as $semana) {
            foreach ($semana as $diaIndex => $conteudosDia) {
                if (!empty($conteudosDia)) {  // Verifica se há conteúdos para este dia
                    $cards_por_dia[$diaIndex] += count($conteudosDia);
                }
            }
        }
        
        // Remove índices vazios (dias sem conteúdo)
        $cards_por_dia = array_values(array_filter($cards_por_dia));
        
        return [
            'total_conteudos' => $total_conteudos,
            'dias_ate_prova' => $calculo->getDiasParaProva(),
            'dias_uteis' => $calculo->getDiasUteis(),
            'cards_por_dia' => $cards_por_dia,
            'semanas_necessarias' => $calculo->getTotalSemanas()
        ];
    }

    public function gerarSemanasDatas() {
        $semanas_datas = [];
        $data_inicio = new DateTime($this->prova['data_inicio_estudo']); // 10/02/2025
        $data_prova = new DateTime($this->prova['data_prova']); // 02/03/2025
        
        // Encontra a segunda-feira da primeira semana
        $primeira_segunda = clone $data_inicio;
        while ($primeira_segunda->format('N') > 1) {
            $primeira_segunda->modify('-1 day');
        }
        
        // Se a data de início não for segunda-feira, ajusta para começar da data correta
        $inicio_real = clone $data_inicio;
        
        // Inicializa a data atual para iteração
        $data_atual = clone $primeira_segunda;
        
        while ($data_atual < $data_prova) {
            $fim_semana = clone $data_atual;
            $fim_semana->modify('+6 days'); // Vai até domingo
            
            // Se o fim da semana ultrapassar a data da prova, ajusta
            if ($fim_semana > $data_prova) {
                $fim_semana = clone $data_prova;
            }
            
            // Determina as datas válidas para esta semana
            $datas_validas = [];
            $data_temp = clone $data_atual;
            
            while ($data_temp <= $fim_semana) {
                $dia_semana = (int)$data_temp->format('N');
                
                // Só inclui a data se:
                // 1. É um dia configurado para estudo
                // 2. É depois ou igual à data de início
                // 3. É antes da data da prova
                if (isset($this->dias_estudo[$dia_semana]) && 
                    $data_temp >= $inicio_real && 
                    $data_temp < $data_prova) {
                    $datas_validas[] = clone $data_temp;
                }
                $data_temp->modify('+1 day');
            }
            
            // Só adiciona a semana se tiver datas válidas
            if (!empty($datas_validas)) {
                $semanas_datas[] = [
                    'inicio' => clone $data_atual,
                    'fim' => clone $fim_semana,
                    'datas' => $datas_validas,
                    'primeiro_card' => clone $datas_validas[0],
                    'ultimo_card' => clone $datas_validas[count($datas_validas) - 1]
                ];
            }
            
            // Avança para a próxima semana
            $data_atual->modify('+7 days');
        }
        
        return $semanas_datas;
    }
    
    public function organizarConteudosPorSemana() {
        $total_conteudos = count($this->conteudos);
        $semanas_datas = $this->gerarSemanasDatas();
        $total_semanas = count($semanas_datas);
        
        // Dias de estudo ordenados
        $dias_estudo = array_keys($this->dias_estudo);
        sort($dias_estudo);
        
        $conteudosPorSemana = [];
        $conteudoIndex = 0;
        
        for ($semana = 0; $semana < $total_semanas; $semana++) {
            $semanaAtual = array_fill(0, count($dias_estudo), []);
            
            // Se for a última semana (semana da prova), não distribui conteúdos
            if ($semana === $total_semanas - 1) {
                $conteudosPorSemana[] = $semanaAtual;
                continue;
            }
            
            $datas_validas = $semanas_datas[$semana]['datas'];
            
            // Calcula conteúdos para esta semana
            $conteudos_restantes = $total_conteudos - $conteudoIndex;
            $semanas_restantes = $total_semanas - $semana - 1;
            
            $conteudos_desta_semana = $semanas_restantes > 0 
                ? ceil($conteudos_restantes / $semanas_restantes)
                : $conteudos_restantes;
            
            // Calcula total de horas da semana
            $total_horas_semana = 0;
            foreach ($datas_validas as $data) {
                $dia_semana = (int)$data->format('N');
                $total_horas_semana += $this->dias_estudo[$dia_semana];
            }
            
            // Distribui os conteúdos
            if ($total_horas_semana > 0 && $conteudos_desta_semana > 0) {
                foreach ($datas_validas as $data) {
                    $dia_semana = (int)$data->format('N');
                    $dia_index = array_search($dia_semana, $dias_estudo);
                    
                    if ($dia_index !== false) {
                        $horas_dia = $this->dias_estudo[$dia_semana];
                        $proporcao = $horas_dia / $total_horas_semana;
                        $cards_dia = ceil($conteudos_desta_semana * $proporcao);
                        
                        for ($j = 0; $j < $cards_dia && $conteudoIndex < $total_conteudos; $j++) {
                            $conteudo = $this->conteudos[$conteudoIndex++];
                            // Adiciona a data prevista ao conteúdo
                            $conteudo['data_prevista'] = $data->format('Y-m-d');
                            $semanaAtual[$dia_index][] = $conteudo;
                        }
                    }
                }
            }
            
            $conteudosPorSemana[] = $semanaAtual;
        }
        
        return $conteudosPorSemana;
    }


    public function replanejarEstudo($novaDataInicio) {
        if (!isset($this->prova['id'])) {
            throw new Exception('Prova não encontrada');
        }
    
        try {
            pg_query($this->conexao, 'BEGIN');
    
            // Atualiza a data de início da prova
            $query = "UPDATE appestudo.provas 
                     SET data_inicio_estudo = $1 
                     WHERE id = $2";
            $result = pg_query_params($this->conexao, $query, array(
                $novaDataInicio,
                $this->prova['id']
            ));
    
            if (!$result) {
                throw new Exception('Erro ao atualizar data de início da prova');
            }
    
            // Recalcula as datas para cada conteúdo
            $dataInicio = new DateTime($novaDataInicio);
            $conteudos = $this->getConteudos();
            $info = $this->getInfoEstudo();
            $cardsPorDia = $info['cards_por_dia'];
    
            $dataAtual = clone $dataInicio;
            $conteudoIndex = 0;
    
            foreach ($conteudos as $conteudo) {
                // Pula para o próximo dia útil quando atingir o limite de cards
                if ($conteudoIndex >= $cardsPorDia) {
                    $dataAtual->modify('+1 day');
                    $conteudoIndex = 0;
    
                    // Pula finais de semana
                    while ($dataAtual->format('N') >= 6) {
                        $dataAtual->modify('+1 day');
                    }
                }
    
                // Atualiza a data de estudo do conteúdo
                $query = "UPDATE appestudo.usuario_conteudo 
                         SET data_estudo = $1 
                         WHERE id = $2 AND usuario_id = $3";
                
                $result = pg_query_params($this->conexao, $query, array(
                    $dataAtual->format('Y-m-d'),
                    $conteudo['id'],
                    $this->usuario_id
                ));
    
                if (!$result) {
                    throw new Exception('Erro ao atualizar conteúdo: ' . pg_last_error($this->conexao));
                }
    
                $conteudoIndex++;
            }
    
            pg_query($this->conexao, 'COMMIT');
    
            return [
                'nova_data_inicio' => $novaDataInicio,
                'conteudos_atualizados' => count($conteudos),
                'cards_por_dia' => $cardsPorDia
            ];
    
        } catch (Exception $e) {
            pg_query($this->conexao, 'ROLLBACK');
            throw $e;
        }
    }
    public function getPlanejamentoAtivo() {
        $query = "SELECT p.idplanejamento 
                  FROM appEstudo.planejamento p 
                  WHERE p.usuario_idusuario = $1 
                  ORDER BY p.data_inicio DESC 
                LIMIT 1";
    
        $resultado = pg_query_params($this->conexao, $query, array($this->usuario_id));

        if ($resultado && pg_num_rows($resultado) > 0) {
            $planejamento = pg_fetch_assoc($resultado);
            return $planejamento['idplanejamento'];
        }
    
        return null;
    }

    public function adicionarMateriaPlanejamento($materia_nome) {
        try {
            // Primeiro, pegar o ID da matéria
            $query_materia = "SELECT idmateria FROM appEstudo.materia WHERE nome = $1";
            $result_materia = pg_query_params($this->conexao, $query_materia, array($materia_nome));
            
            if (!$result_materia || pg_num_rows($result_materia) === 0) {
                error_log("Matéria não encontrada: " . $materia_nome);
                return false;
            }
            
            $materia = pg_fetch_assoc($result_materia);
            $id_materia = $materia['idmateria'];
            
            $id_planejamento = $this->getPlanejamentoAtivo();
            if (!$id_planejamento) {
                error_log("Planejamento ativo não encontrado");
                return false;
            }
            
            // Verificar se já existe essa relação
            $query_check = "SELECT 1 FROM appEstudo.planejamento_materia 
                           WHERE planejamento_idplanejamento = $1 
                           AND materia_idmateria = $2";
            
            $result_check = pg_query_params($this->conexao, $query_check, array(
                $id_planejamento,
                $id_materia
            ));
            
            if (pg_num_rows($result_check) > 0) {
                error_log("Matéria já existe no planejamento");
                return true; // Já existe, consideramos sucesso
            }
            
            // Inserir na tabela planejamento_materia
            $query_insert = "INSERT INTO appEstudo.planejamento_materia 
                            (planejamento_idplanejamento, materia_idmateria) 
                            VALUES ($1, $2)";
                            
            $result_insert = pg_query_params($this->conexao, $query_insert, array(
                $id_planejamento,
                $id_materia
            ));
            
            if (!$result_insert) {
                error_log("Erro ao inserir matéria: " . pg_last_error($this->conexao));
                return false;
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Erro ao adicionar matéria: " . $e->getMessage());
            return false;
        }
    }

    public function getQuantidadeDiasEstudo() {
        // Se dias_estudo ainda não foi carregado, carrega
        if (empty($this->dias_estudo)) {
            $this->carregarDiasEstudo();
        }
        
        // Retorna a quantidade de dias no array
        return count($this->dias_estudo);
    }

    /**
 * Retorna apenas os conteúdos que não têm filhos (conteúdos finais)
 */
public function getConteudosFiltrados() {
    if ($this->conteudos === null) {
        $this->getConteudos();
    }
    
    return filtrarTodosConteudos($this->conteudos);
}

public function organizarConteudosPorSemanaFiltrados() {
    // Usar os conteúdos já filtrados se disponíveis, senão filtrar
    if ($this->conteudos === null) {
        $this->getConteudos();
    }
    $conteudosFiltrados = filtrarTodosConteudos($this->conteudos);

    // NOVA LÓGICA: Remover pais que vão aparecer junto com filhos
    $conteudosFiltrados = removerPaisComFilhos($conteudosFiltrados);

    // BALANCEAMENTO INTELIGENTE: Aplicar algoritmo baseado em pesos e prioridades
    global $conexao;
    $conteudosFiltrados = balancearConteudosInteligente($conteudosFiltrados, $conexao, $this->usuario_id);

    // Se a prova foi replanejada, removemos apenas os conteúdos estudados ANTES do replanejamento
    if (isset($this->prova['replanejado']) && $this->prova['replanejado'] === true) {
        // Removemos do cronograma principal APENAS os conteúdos que:
        // 1. Estão marcados como estudados E
        // 2. Têm o campo replanejado_em preenchido (foram estudados antes do replanejamento)
        $conteudosFiltrados = array_filter($conteudosFiltrados, function($item) {
            // Se não for estudado OU se for estudado mas não tiver replanejado_em, mantém no cronograma
            return $item['status_estudo'] !== 'Estudado' ||
                   !isset($item['replanejado_em']) ||
                   empty($item['replanejado_em']);
        });
    }

    // Continuação da função original
    $total_conteudos = count($conteudosFiltrados);
    $semanas_datas = $this->gerarSemanasDatas();
    $total_semanas = count($semanas_datas);
    
    // Dias de estudo ordenados
    $dias_estudo = array_keys($this->dias_estudo);
    sort($dias_estudo);
    
    $conteudosPorSemana = [];
    $conteudoIndex = 0;
    
    for ($semana = 0; $semana < $total_semanas; $semana++) {
        $semanaAtual = array_fill(0, count($dias_estudo), []);
        
        // Se for a última semana (semana da prova), não distribui conteúdos
        if ($semana === $total_semanas - 1) {
            $conteudosPorSemana[] = $semanaAtual;
            continue;
        }
        
        $datas_validas = $semanas_datas[$semana]['datas'];
        
        // Calcula conteúdos para esta semana
        $conteudos_restantes = $total_conteudos - $conteudoIndex;
        $semanas_restantes = $total_semanas - $semana - 1;
        
        $conteudos_desta_semana = $semanas_restantes > 0 
            ? ceil($conteudos_restantes / $semanas_restantes)
            : $conteudos_restantes;
        
        // Calcula total de horas da semana
        $total_horas_semana = 0;
        foreach ($datas_validas as $data) {
            $dia_semana = (int)$data->format('N');
            $total_horas_semana += $this->dias_estudo[$dia_semana];
        }
        
        // Distribui os conteúdos
        if ($total_horas_semana > 0 && $conteudos_desta_semana > 0) {
            foreach ($datas_validas as $data) {
                $dia_semana = (int)$data->format('N');
                $dia_index = array_search($dia_semana, $dias_estudo);
                
                if ($dia_index !== false) {
                    $horas_dia = $this->dias_estudo[$dia_semana];
                    $proporcao = $horas_dia / $total_horas_semana;
                    $cards_dia = ceil($conteudos_desta_semana * $proporcao);
                    
                    // Convertemos para array indexado para acesso preciso pelo índice
                    $conteudosFiltradosArray = array_values($conteudosFiltrados);

                    for ($j = 0; $j < $cards_dia && $conteudoIndex < $total_conteudos; $j++) {
                        if (isset($conteudosFiltradosArray[$conteudoIndex])) {
                            $conteudo = $conteudosFiltradosArray[$conteudoIndex++];
                            // Adiciona a data prevista ao conteúdo
                            $conteudo['data_prevista'] = $data->format('Y-m-d');
                            $semanaAtual[$dia_index][] = $conteudo;
                        }
                    }
                }
            }
        }
        
        $conteudosPorSemana[] = $semanaAtual;
    }
    
    return $conteudosPorSemana;
}

}

/**
 * Verifica se um capítulo possui filhos na lista de conteúdos
 * @param string $capitulo O número do capítulo a verificar
 * @param array $todosConteudos Lista de todos os conteúdos
 * @return boolean
 */
function possuiFilhos($capitulo, $todosConteudos) {
    foreach ($todosConteudos as $c) {
        // Verifica se o conteúdo é filho direto do capítulo
        if (strpos($c['capitulo'], $capitulo . '.') === 0) {
            // Garante que é um filho direto (um nível abaixo)
            $restante = substr($c['capitulo'], strlen($capitulo) + 1);
            if (strpos($restante, '.') === false) {
                return true;
            }
        }
    }
    return false;
}

/**
 * Filtra conteúdos que possuem filhos da lista
 * @param array $conteudos Lista de conteúdos para filtrar
 * @return array Lista filtrada sem os pais que possuem filhos
 */
function filtrarConteudosComFilhos($conteudos) {
    $resultado = [];
    foreach ($conteudos as $conteudo) {
        if (!possuiFilhos($conteudo['capitulo'], $conteudos)) {
            $resultado[] = $conteudo;
        }
    }
    return $resultado;
}

