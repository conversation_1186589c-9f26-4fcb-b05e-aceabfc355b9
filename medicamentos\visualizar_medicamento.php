<?php
// visualizar_medicamento.php
require_once 'includes/functions.php';
require_once 'includes/medicamento.php';
require_once 'includes/registro.php';

// Verificar se o ID foi fornecido
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: index.php");
    exit();
}

// Instanciar objetos
$medicamento = new Medicamento();
$registro = new Registro();

// Obter dados do medicamento
if (!$medicamento->obter($_GET['id'])) {
    header("Location: index.php");
    exit();
}

// Obter os registros de uso
$registros = $registro->listarPorMedicamento($medicamento->id);

// Calcular progresso
$data_inicio = new DateTime($medicamento->data_inicio);
$data_fim = clone $data_inicio;
$data_fim->modify('+' . $medicamento->dias_tratamento . ' days');
$hoje = new DateTime();

$total_dias = $medicamento->dias_tratamento;
$dias_passados = $data_inicio->diff($hoje)->days;
$progresso = min(100, max(0, round(($dias_passados / $total_dias) * 100)));
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualizar Medicamento - Controle de Medicamentos</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="container my-4">
        <header class="mb-4">
            <h1 class="text-center"><?= htmlspecialchars($medicamento->nome) ?></h1>
        </header>
        
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h2 class="h5 mb-0">Informações do Medicamento</h2>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>Dosagem:</strong> <?= htmlspecialchars($medicamento->dosagem) ?></p>
                        <p><strong>Intervalo:</strong> <?= $medicamento->intervalo_horas ?> horas</p>
                        <p><strong>Data de Início:</strong> <?= date('d/m/Y', strtotime($medicamento->data_inicio)) ?></p>
                        <p><strong>Duração:</strong> <?= $medicamento->dias_tratamento ?> dias</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>Horário Inicial:</strong> <?= date('H:i', strtotime($medicamento->horario_inicial)) ?></p>
                        <p><strong>Data de Término:</strong> <?= $data_fim->format('d/m/Y') ?></p>
                        <?php if (!empty($medicamento->observacoes)): ?>
                            <p><strong>Observações:</strong> <?= nl2br(htmlspecialchars($medicamento->observacoes)) ?></p>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="mt-3">
                    <h5>Progresso do Tratamento</h5>
                    <div class="progress">
                        <div class="progress-bar" role="progressbar" style="width: <?= $progresso ?>%;" 
                             aria-valuenow="<?= $progresso ?>" aria-valuemin="0" aria-valuemax="100">
                            <?= $progresso ?>%
                        </div>
                    </div>
                    <small class="text-muted mt-1 d-block">
                        <?= $dias_passados ?> dias de <?= $total_dias ?> dias
                    </small>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between">
                <a href="index.php" class="btn btn-secondary">Voltar</a>
                <a href="editar_medicamento.php?id=<?= $medicamento->id ?>" class="btn btn-warning">Editar</a>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header bg-info text-white">
                <h2 class="h5 mb-0">Histórico de Uso</h2>
            </div>
            <div class="card-body">
                <?php if ($registros->rowCount() > 0): ?>
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Data e Hora</th>
                                    <th>Status</th>
                                    <th>Observação</th>
                                    <th>Ação</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php while ($item = $registros->fetch()): ?>
                                    <tr>
                                        <td><?= date('d/m/Y H:i', strtotime($item['data_hora'])) ?></td>
                                        <td>
                                            <?php if ($item['confirmado']): ?>
                                                <span class="badge bg-success">Confirmado</span>
                                            <?php elseif (strtotime($item['data_hora']) > time()): ?>
                                                <span class="badge bg-info">Agendado</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Pendente</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?= !empty($item['observacao']) ? htmlspecialchars($item['observacao']) : '-' ?></td>
                                        <td>
                                            <?php if (!$item['confirmado'] && strtotime($item['data_hora']) <= time()): ?>
                                                <a href="confirmar_uso.php?id=<?= $item['id'] ?>" class="btn btn-sm btn-success">Confirmar</a>
                                            <?php else: ?>
                                                -
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <p class="text-center">Nenhum registro de uso encontrado.</p>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>