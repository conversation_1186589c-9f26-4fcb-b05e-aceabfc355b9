<?php
session_start();
include_once '../../conexao_POST.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['idusuario'])) {
    echo json_encode(['success' => false, 'erro' => 'Usuário não autenticado']);
    exit;
}

// Receber e decodificar os dados JSON
$dados = json_decode(file_get_contents('php://input'), true);

// Validar dados recebidos
if (!isset($dados['titulo']) || empty($dados['titulo'])) {
    echo json_encode(['success' => false, 'erro' => 'Título é obrigatório']);
    exit;
}

try {
    $usuario_id = $_SESSION['idusuario'];
    $titulo = $dados['titulo'];
    $descricao = $dados['descricao'] ?? '';
    $status = $dados['status'] ?? 'backlog';
    $prioridade = $dados['prioridade'] ?? 'media';
    $data_limite = !empty($dados['data_limite']) ? $dados['data_limite'] : null;
    
    // Ajustar formato da data se necessário
    if ($data_limite) {
        // Garantir que a data está no formato correto para o PostgreSQL (YYYY-MM-DD)
        $data_obj = new DateTime($data_limite);
        $data_limite = $data_obj->format('Y-m-d');
    }
    
    // Verificar se é uma atualização ou inserção
    if (isset($dados['id']) && !empty($dados['id'])) {
        // Atualização
        $id = $dados['id'];
        
        // Verificar se o status foi fornecido
        $status_query = isset($dados['status']) ? ", status = '$status'" : "";
        
        $query = "UPDATE appEstudo.kanban_tarefas 
                 SET titulo = $1, 
                     descricao = $2, 
                     prioridade = $3, 
                     data_limite = $4
                     $status_query
                 WHERE id = $5 AND usuario_id = $6";
        
        $result = pg_query_params(
            $conexao, 
            $query, 
            [$titulo, $descricao, $prioridade, $data_limite, $id, $usuario_id]
        );
    } else {
        // Inserção
        $query = "INSERT INTO appEstudo.kanban_tarefas 
                 (titulo, descricao, status, prioridade, data_limite, usuario_id) 
                 VALUES ($1, $2, $3, $4, $5, $6) 
                 RETURNING id";
        
        $result = pg_query_params(
            $conexao, 
            $query, 
            [$titulo, $descricao, $status, $prioridade, $data_limite, $usuario_id]
        );
    }
    
    if (!$result) {
        throw new Exception(pg_last_error($conexao));
    }
    
    echo json_encode(['success' => true]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'erro' => $e->getMessage()]);
}
?>



