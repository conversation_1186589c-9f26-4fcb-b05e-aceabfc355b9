<?php
session_start();

// Configurar headers de segurança
header("X-Frame-Options: DENY");
header("X-Content-Type-Options: nosniff");
header("X-XSS-Protection: 1; mode=block");
header("Referrer-Policy: strict-origin-when-cross-origin");

// Verificar se o usuário já está logado
if (isset($_SESSION['idusuario'])) {
    // Se estiver logado, redireciona para a página principal (index.php ou painel.php)
    header('Location: index.php');
    exit(); // Importante para parar a execução do script de cadastro
}

include 'conexao_POST.php';
include 'painel_funcoes.php';

// Inicializar token CSRF se não existir
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$mensagem = '';
$tipo_mensagem = '';

// Inicializar variáveis vazias para o formulário
$nome = '';
$usuario = '';
$email = '';

// Implementar rate limiting básico
$rate_limit_key = 'cadastro_' . $_SERVER['REMOTE_ADDR'];
$now = time();
$rate_limit_window = 300; // 5 minutos
$max_attempts = 5; // 5 tentativas por janela de tempo

if (!isset($_SESSION[$rate_limit_key])) {
    $_SESSION[$rate_limit_key] = [
        'count' => 0,
        'window_start' => $now
    ];
}

if ($now - $_SESSION[$rate_limit_key]['window_start'] > $rate_limit_window) {
    $_SESSION[$rate_limit_key] = [
        'count' => 0,
        'window_start' => $now
    ];
} 

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Verificar token CSRF
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        $mensagem = "Erro de validação do formulário. Por favor, tente novamente.";
        $tipo_mensagem = "erro";
    } 
    // Verificar rate limiting
    else if ($_SESSION[$rate_limit_key]['count'] >= $max_attempts) {
        $tempo_restante = $rate_limit_window - ($now - $_SESSION[$rate_limit_key]['window_start']);
        $minutos = ceil($tempo_restante / 60);
        $mensagem = "Muitas tentativas de cadastro. Por favor, tente novamente em $minutos minutos.";
        $tipo_mensagem = "erro";
    } 
    else {
        $_SESSION[$rate_limit_key]['count']++;
        
        $nome = trim($_POST['nome'] ?? '');
        $usuario = trim($_POST['usuario'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $senha = $_POST['senha'] ?? '';
        $confirmar_senha = $_POST['confirmar_senha'] ?? '';

        // Sanitizar entradas
        $nome = htmlspecialchars($nome, ENT_QUOTES, 'UTF-8');
        $usuario = strtolower(htmlspecialchars($usuario, ENT_QUOTES, 'UTF-8')); // Converter para minúsculas
        $email = filter_var($email, FILTER_SANITIZE_EMAIL);

        // Validações
        $erros = [];

        if(empty($nome) || empty($usuario) || empty($email) || empty($senha) || empty($confirmar_senha)) {
            $erros[] = "Todos os campos são obrigatórios";
        }

        if(!preg_match('/^[a-z0-9_]+$/', $usuario)) {
            $erros[] = "O usuário deve conter apenas letras minúsculas, números e underscore";
        }

        if(!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $erros[] = "Email inválido";
        }

        // Validação de senha mais forte (mantendo o mínimo de 6 caracteres)
        if(strlen($senha) < 6) {
            $erros[] = "A senha deve ter pelo menos 6 caracteres";
        } else if(!preg_match('/[A-Z]/', $senha)) {
            $erros[] = "A senha deve conter pelo menos uma letra maiúscula";
        } else if(!preg_match('/[0-9]/', $senha)) {
            $erros[] = "A senha deve conter pelo menos um número";
        }

        if($senha !== $confirmar_senha) {
            $erros[] = "As senhas não coincidem";
        }

        // Verificar se usuário ou email já existem
        $sql = "SELECT usuario, email FROM usuario WHERE usuario = $1 OR email = $2";
        $result = pg_query_params($conexao, $sql, array($usuario, $email));

        while($row = pg_fetch_assoc($result)) {
            if($row['usuario'] == $usuario) {
                $erros[] = "Este nome de usuário já está em uso";
            }
            if($row['email'] == $email) {
                $erros[] = "Este email já está cadastrado";
            }
        }

        if(empty($erros)) {
            try {
                $sql = "INSERT INTO usuario (nome, senha, usuario, email) VALUES ($1, $2, $3, $4)";
                $result = pg_query_params($conexao, $sql, array($nome, $senha, $usuario, $email));

                if($result) {
                    // Registrar atividade de cadastro bem-sucedido na tabela log_seguranca
                    try {
                        $ip = $_SERVER['REMOTE_ADDR'];
                        $data = date('Y-m-d H:i:s');
                        $detalhes = json_encode([
                            'email' => $email,
                            'acao' => 'cadastro_usuario',
                            'browser' => $_SERVER['HTTP_USER_AGENT'] ?? 'desconhecido'
                        ]);
                        
                        $log_sql = "INSERT INTO appestudo.log_seguranca (usuario_id, tipo_evento, ip, data_evento, detalhes) 
                                    VALUES ($1, $2, $3, $4, $5)";
                        pg_query_params($conexao, $log_sql, array(null, 'cadastro_sucesso', $ip, $data, $detalhes));
                    } catch (Exception $e) {
                        // Ignorar erros de log, não deve afetar o fluxo principal
                    }
                    
                    $mensagem = "Cadastro realizado com sucesso! Redirecionando para o login...";
                    $tipo_mensagem = "sucesso";
                    
                    // Regenerar sessão após cadastro bem-sucedido
                    session_regenerate_id(true);
                    
                    // Limpar os campos do formulário após cadastro bem-sucedido
                    $nome = '';
                    $usuario = '';
                    $email = '';
                    
                    header("refresh:2;url=login_index.php");
                } else {
                    throw new Exception("Erro ao cadastrar");
                }
            } catch(Exception $e) {
                // Registrar erro de cadastro na tabela log_seguranca
                try {
                    $ip = $_SERVER['REMOTE_ADDR'];
                    $data = date('Y-m-d H:i:s');
                    $detalhes = json_encode([
                        'email' => $email,
                        'acao' => 'cadastro_usuario',
                        'erro' => $e->getMessage(),
                        'browser' => $_SERVER['HTTP_USER_AGENT'] ?? 'desconhecido'
                    ]);
                    
                    $log_sql = "INSERT INTO appestudo.log_seguranca (usuario_id, tipo_evento, ip, data_evento, detalhes) 
                                VALUES ($1, $2, $3, $4, $5)";
                    pg_query_params($conexao, $log_sql, array(null, 'cadastro_falha', $ip, $data, $detalhes));
                } catch (Exception $logError) {
                    // Ignorar erros de log
                }
                
                $mensagem = "Erro ao realizar cadastro. Tente novamente.";
                $tipo_mensagem = "erro";
            }
        } else {
            $mensagem = implode("<br>", $erros);
            $tipo_mensagem = "erro";
        }
    }
}

// Gerar novo token CSRF após processamento
$_SESSION['csrf_token'] = bin2hex(random_bytes(32));
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cadastro - Sistema de Estudos</title>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700&family=Quicksand:wght@400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Quicksand:wght@300..700&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Tektur:wght@400..900&family=Varela+Round&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">

    <style>
        :root {
            /* Cores modo claro (baseado em cf/index.php) */
            --primary: #00008B;
            --primary-light: #3949ab;
            --secondary: #f8f0e3;
            --accent: #4a4a4a;
            --border: #e0e0e0;
            --text: #2c3e50;
            --active: #00008B;
            --hover: #f5f5f5;
            --primary-blue: #00008B;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --background: #f5f5f5;
            --card-background: white;
            --danger: #ef476f;
            --success: #06d6a0;
            --warning: #ffd166;
        }

        [data-theme="dark"] {
            --primary: #4169E1;
            --secondary: #1a1a2e;
            --accent: #6c7293;
            --border: #2d2d42;
            --text: #e4e6f0;
            --active: #4169E1;
            --hover: #232338;
            --primary-blue: #4169E1;
            --shadow-color: rgba(0, 0, 0, 0.3);
            --background: #0a0a1f;
            --card-background: #13132b;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            background: var(--background);
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            color: var(--text);
            line-height: 1.6;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid var(--border);
            margin-bottom: 30px;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 2px 4px var(--shadow-color);
            background: linear-gradient(90deg, var(--primary) 60%, var(--primary) 100%);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo img {
            height: 50px;
            width: auto;
            transition: opacity 0.3s ease;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .theme-toggle {
            margin-left: 15px;
        }

        .theme-btn {
            background: transparent;
            border: none;
            color: var(--hover);
            cursor: pointer;
            font-size: 1.2rem;
            padding: 8px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        [data-theme="dark"] .theme-btn {
            color: var(--text);
        }

        .theme-btn:hover {
            background: var(--hover);
        }

        .logo {
            position: relative;
        }

        .logo-dark {
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
        }

        [data-theme="dark"] .logo-light {
            opacity: 0;
        }

        [data-theme="dark"] .logo-dark {
            opacity: 1;
        }

        .container {
            width: 100%;
            max-width: 800px; /* Aumentado de 480px para 600px */
            margin: 0 auto;
           padding: 1rem 1rem;
            flex: 1;
        }

        .card {
            background: var(--card-background);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 6px var(--shadow-color);
            transition: all 0.3s ease;
            border: 1px solid var(--border);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px -5px var(--shadow-color);
        }

        .card-header {
            background: var(--primary);
            padding: 1rem;
            color: white;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 60%);
            transform: rotate(30deg);
        }

        .card-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            position: relative;
            font-family: 'Varela Round', 'Quicksand', sans-serif;
        }

        .card-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            font-weight: 300;
            letter-spacing: 1px;
        }

        .card-body {
            padding: 2.5rem;
        }

        .alert {
            padding: 1rem;
            border-radius: 12px;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            font-size: 0.95rem;
        }

        .alert i {
            margin-right: 12px;
            font-size: 1.2rem;
        }

        .alert-success {
            background: rgba(6, 214, 160, 0.1);
            color: var(--success);
            border: 1px solid rgba(6, 214, 160, 0.2);
        }

        .alert-error {
            background: rgba(239, 71, 111, 0.1);
            color: var(--danger);
            border: 1px solid rgba(239, 71, 111, 0.2);
        }

        .form-group {
            margin-bottom: 1.8rem;
            position: relative;
        }

        .form-label {
            display: block;
            margin-bottom: 0.6rem;
            font-weight: 500;
            color: var(--text);
            font-size: 0.95rem;
        }

        .form-input {
            width: 100%;
            padding: 14px 16px;
            border: 1px solid var(--border);
            border-radius: 12px;
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            font-size: 1rem;
            background-color: var(--card-background);
            transition: all 0.3s ease;
            color: var(--text);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(0, 0, 139, 0.15);
        }

        .form-input::placeholder {
            color: var(--accent);
            opacity: 0.7;
        }

        .form-help {
            font-size: 0.85rem;
            color: var(--accent);
            margin-top: 0.5rem;
        }

        .validacao-mensagem {
            font-size: 0.85rem;
            margin-top: 0.5rem;
            display: flex;
            align-items: center;
        }

        .validacao-mensagem::before {
            font-family: "Font Awesome 6 Free";
            font-weight: 900;
            margin-right: 6px;
        }

        .disponivel { 
            color: var(--success); 
        }
        
        .disponivel::before {
            content: "\f058"; /* check-circle */
        }
        
        .indisponivel { 
            color: var(--danger); 
        }
        
        .indisponivel::before {
            content: "\f057"; /* times-circle */
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: var(--primary);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-family: 'Quicksand', sans-serif;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            width: 100%;
            font-size: 1rem;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
            opacity: 0.9;
        }

        .login-link {
            text-align: center;
            margin-top: 1.8rem;
            font-size: 0.95rem;
            color: var(--accent);
        }

        .login-link a {
            color: var(--primary);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .login-link a:hover {
            color: var(--primary-light);
            text-decoration: underline;
        }

        .password-strength {
            margin-top: 0.8rem;
            height: 6px;
            border-radius: 6px;
            background: var(--border);
            overflow: hidden;
        }

        .password-strength-bar {
            height: 100%;
            width: 0;
            transition: width 0.3s ease;
            border-radius: 6px;
        }

        .strength-weak { 
            width: 33%; 
            background: var(--danger); 
        }
        
        .strength-medium { 
            width: 66%; 
            background: var(--warning); 
        }
        
        .strength-strong { 
            width: 100%; 
            background: var(--success); 
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            color: white;
            background-color: var(--primary-light);
            text-decoration: none;
            font-weight: 500;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            font-size: 0.95rem;
            padding: 10px 16px;
            border-radius: 8px;
            /*width: 100%;*/
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .back-link:hover {
            background-color: var(--primary);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        .back-link i {
            margin-right: 8px;
        }

        .input-group {
            position: relative;
        }

        .input-icon {
            position: absolute;
            top: 50%;
            right: 16px;
            transform: translateY(-50%);
            color: var(--accent);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .input-icon:hover {
            color: var(--primary);
        }

        .footer {
            text-align: center;
            padding: 1rem;
            background: linear-gradient(90deg, var(--primary) 60%, var(--primary) 100%);
            color: var(--hover);
            margin-top: auto;
        }

        [data-theme="dark"] .footer {
            background: var(--primary);
            color: var(--text);
        }

        .footer p {
            font-size: 0.9rem;
        }

        @media (max-width: 480px) {
            .card-body {
                padding: 1.8rem;
            }
            
            .card-header {
                padding: 1.5rem;
            }
            
            .card-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <div class="logo">
                <img src="logo/logo_vertical.png" alt="Logo" class="logo-light">
                <img src="logo/logo_vertical.png" alt="Logo" class="logo-dark">
            </div>
        </div>
        <div class="header-right">
            <div class="theme-toggle">
                <button id="theme-toggle-btn" class="theme-btn">
                    <i id="theme-icon" class="fas fa-moon"></i>
                </button>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="card">
            <div class="card-header">
                <h1 class="card-title">Crie sua Conta</h1>
                
            </div>

            <div class="card-body">
                <?php if($mensagem && $_SERVER['REQUEST_METHOD'] == 'POST'): ?>
                    <div class="alert alert-<?php echo $tipo_mensagem === 'erro' ? 'error' : 'success'; ?>">
                        <i class="fas fa-<?php echo $tipo_mensagem === 'erro' ? 'exclamation-circle' : 'check-circle'; ?>"></i>
                        <?php echo $mensagem; ?>
                    </div>
                <?php endif; ?>

                <a href="login_index.php" class="back-link">
                    <i class="fas fa-arrow-left"></i> Voltar para Login
                </a>

                <form method="POST" action="" id="cadastroForm" autocomplete="off">
                    <!-- Token CSRF -->
                    <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                    
                    <div class="form-group">
                        <label class="form-label" for="nome">Nome Completo</label>
                        <input type="text" id="nome" name="nome" class="form-input"
                               value="<?php echo htmlspecialchars($nome); ?>" required
                               maxlength="100" autocomplete="new-nome"
                               placeholder="Digite seu nome completo">
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="usuario">Nome de Usuário</label>
                        <input type="text" 
                               class="form-input" 
                               id="usuario" 
                               name="usuario" 
                               value="<?php echo htmlspecialchars($usuario); ?>"
                               pattern="^[a-z0-9_]+$"
                               style="text-transform: lowercase;"
                               oninput="this.value = this.value.toLowerCase().replace(/[^a-z0-9_]/g, ''); validarUsuario(this.value);"
                               title="Use apenas letras minúsculas, números e underscore"
                               placeholder="Digite seu nome de usuário">
                        <div class="form-help">Apenas letras minúsculas, números e underscore são permitidos</div>
                        <div id="usuario-validacao" class="validacao-mensagem"></div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="email">E-mail</label>
                        <input type="email" 
                               class="form-input" 
                               id="email" 
                               name="email" 
                               value="<?php echo htmlspecialchars($email); ?>"
                               onblur="validarEmail(this.value)"
                               placeholder="Digite seu e-mail">
                        <div id="email-validacao" class="validacao-mensagem"></div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="senha">Senha</label>
                        <div class="input-group">
                            <input type="password" id="senha" name="senha" class="form-input" 
                                   required
                                   minlength="6"
                                   autocomplete="new-password"
                                   placeholder="Crie uma senha forte">
                            <i class="fas fa-eye-slash input-icon" id="toggleSenha"></i>
                        </div>
                        <div class="form-help">A senha deve ter pelo menos 6 caracteres, incluir uma letra maiúscula e um número</div>
                        <div class="password-strength">
                            <div class="password-strength-bar" id="passwordStrengthBar"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="form-label" for="confirmar_senha">Confirmar Senha</label>
                        <div class="input-group">
                            <input type="password" id="confirmar_senha" name="confirmar_senha" class="form-input" 
                                   required
                                   autocomplete="new-password"
                                   placeholder="Confirme sua senha">
                            <i class="fas fa-eye-slash input-icon" id="toggleConfirmarSenha"></i>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Criar Conta
                    </button>
                </form>

                <div class="login-link">
                    Já tem uma conta? <a href="login_index.php">Faça login</a>
                </div>
            </div>
        </div>
    </div>

<script>
    // Função para aplicar tema salvo ou preferência do sistema
    function applyTheme(theme) {
        if (theme === 'dark') {
            document.documentElement.setAttribute('data-theme', 'dark');
        } else {
            document.documentElement.setAttribute('data-theme', '');
        }
    }
    
    // Detecta preferência do sistema
    function getPreferredTheme() {
        if (localStorage.getItem('theme')) {
            return localStorage.getItem('theme');
        }
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    
    // Função para atualizar o ícone do tema
    function updateThemeIcon(theme) {
        var icon = document.getElementById('theme-icon');
        if (icon) {
            if (theme === 'dark') {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
            }
        }
    }
    
    // Aplica tema ao carregar
    document.addEventListener('DOMContentLoaded', function() {
        var currentTheme = getPreferredTheme();
        applyTheme(currentTheme);
        updateThemeIcon(currentTheme);
        
        var btn = document.getElementById('theme-toggle-btn');
        if (btn) {
            btn.addEventListener('click', function() {
                var currentTheme = document.documentElement.getAttribute('data-theme');
                var newTheme = (currentTheme === 'dark') ? 'light' : 'dark';
                applyTheme(newTheme);
                updateThemeIcon(newTheme);
                localStorage.setItem('theme', newTheme);
            });
        }
    });
    
    // Atualiza se preferência do sistema mudar
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
        if (!localStorage.getItem('theme')) {
            applyTheme(e.matches ? 'dark' : 'light');
        }
    });

    // Código existente para verificação de disponibilidade
    let timeouts = {};

    function debounce(func, wait) {
        return function(...args) {
            const context = this;
            clearTimeout(timeouts[args[0].name]);
            timeouts[args[0].name] = setTimeout(() => func.apply(context, args), wait);
        };
    }

    function verificarDisponibilidade(input) {
        const tipo = input.name;
        const valor = input.value.trim();
        const mensagemElement = document.getElementById(`${tipo}-mensagem`);

        if (valor.length === 0) {
            mensagemElement.textContent = '';
            mensagemElement.className = 'validacao-mensagem';
            return;
        }

        fetch('verificar_disponibilidade.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ tipo, valor })
        })
            .then(response => response.json())
            .then(data => {
                mensagemElement.textContent = data.disponivel ?
                    `${tipo === 'usuario' ? 'Usuário' : 'Email'} disponível` :
                    `Este ${tipo} já está em uso`;
                mensagemElement.className = `validacao-mensagem ${data.disponivel ? 'disponivel' : 'indisponivel'}`;
            })
            .catch(() => {
                mensagemElement.textContent = 'Erro ao verificar disponibilidade';
                mensagemElement.className = 'validacao-mensagem indisponivel';
            });
    }

    const verificarComDebounce = debounce(verificarDisponibilidade, 500);

    document.querySelector('input[name="usuario"]').addEventListener('input', function() {
        verificarComDebounce(this);
    });

    document.querySelector('input[name="email"]').addEventListener('input', function() {
        verificarComDebounce(this);
    });
    
    // Adicionar verificador de força de senha
    document.getElementById('senha').addEventListener('input', function() {
        const senha = this.value;
        const strengthBar = document.getElementById('passwordStrengthBar');
        
        // Remover classes existentes
        strengthBar.className = 'password-strength-bar';
        
        if (senha.length === 0) {
            strengthBar.style.width = '0';
            return;
        }
        
        // Verificar força da senha
        let strength = 0;
        
        // Comprimento mínimo
        if (senha.length >= 6) strength += 1;
        
        // Letra maiúscula
        if (/[A-Z]/.test(senha)) strength += 1;
        
        // Número
        if (/[0-9]/.test(senha)) strength += 1;
        
        // Caractere especial
        if (/[^A-Za-z0-9]/.test(senha)) strength += 1;
        
        // Definir classe com base na força
        if (strength <= 2) {
            strengthBar.classList.add('strength-weak');
        } else if (strength === 3) {
            strengthBar.classList.add('strength-medium');
        } else {
            strengthBar.classList.add('strength-strong');
        }
    });
    
    // Adicionar funcionalidade para mostrar/ocultar senha
    document
        .querySelectorAll('.input-icon')
        .forEach(icon => {
            icon.addEventListener('click', function() {
                const input = this.previousElementSibling;
                const type = input.getAttribute('type');
                input.setAttribute('type', type === 'password' ? 'text' : 'password');
                this.classList.toggle('fa-eye');
                this.classList.toggle('fa-eye-slash');
            });
        });


function validarUsuario(usuario) {
    const mensagemElement = document.getElementById('usuario-validacao');
    
    // Validar formato primeiro (apenas letras minúsculas, números e underscore)
    if (!usuario) {
        mensagemElement.textContent = '';
        return;
    }

    if (!/^[a-z0-9_]+$/.test(usuario)) {
        mensagemElement.textContent = 'Use apenas letras minúsculas, números e underscore';
        mensagemElement.className = 'validacao-mensagem indisponivel';
        return;
    }

    // Fazer requisição AJAX para verificar disponibilidade
    fetch('verificar_usuario.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'usuario=' + encodeURIComponent(usuario)
    })
    .then(response => response.json())
    .then(data => {
        if (data.disponivel) {
            mensagemElement.textContent = 'Nome de usuário disponível';
            mensagemElement.className = 'validacao-mensagem disponivel';
        } else {
            mensagemElement.textContent = 'Este nome de usuário já está em uso';
            mensagemElement.className = 'validacao-mensagem indisponivel';
        }
    })
    .catch(error => {
        console.error('Erro:', error);
    });
}

function validarEmail(email) {
    const mensagemElement = document.getElementById('email-validacao');
    
    // Limpa a mensagem se o campo estiver vazio
    if (!email) {
        mensagemElement.textContent = '';
        return;
    }

    // Validar formato do email primeiro
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        mensagemElement.textContent = 'Formato de e-mail inválido';
        mensagemElement.className = 'validacao-mensagem indisponivel';
        return;
    }

    // Fazer requisição AJAX apenas se o formato do email estiver correto
    fetch('verificar_email.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'email=' + encodeURIComponent(email)
    })
    .then(response => response.json())
    .then(data => {
        if (data.disponivel) {
            mensagemElement.textContent = 'E-mail disponível';
            mensagemElement.className = 'validacao-mensagem disponivel';
        } else {
            mensagemElement.textContent = 'Este e-mail já está cadastrado';
            mensagemElement.className = 'validacao-mensagem indisponivel';
        }
    })
    .catch(error => {
        console.error('Erro:', error);
    });
}
</script>
</body>
</html>
