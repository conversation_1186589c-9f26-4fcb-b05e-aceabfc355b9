<?php
session_start();
require_once('../includes/auth.php');
require_once('../conexao_POST.php');
require_once('../cadastros/includes/verify_admin.php');

// Verifica se é o super admin
verificarSuperAdmin($conexao, false);

// Consulta para obter todos os logins
$query = "
    SELECT 
        ls.data_evento,
        u.nome as nome_usuario,
        u.usuario as username,
        ls.tipo_evento,
        ls.ip,
        CASE 
            WHEN ls.tipo_evento = 'login_sucesso' THEN 'Sucesso'
            WHEN ls.tipo_evento = 'login_falha' THEN 'Falha'
            ELSE ls.tipo_evento
        END as status
    FROM appEstudo.log_seguranca ls
    JOIN appEstudo.usuario u ON ls.usuario_id = u.idusuario
    WHERE ls.tipo_evento IN ('login_sucesso', 'login_falha')
    ORDER BY ls.data_evento DESC
";

$result = pg_query($conexao, $query);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Registro de Logins</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        body {
            font-family: 'Quicksand', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #00008B;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        .success {
            color: #28a745;
        }
        .failure {
            color: #dc3545;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #6c757d;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.3s ease;
            font-family: 'Quicksand', sans-serif;
            border: none;
            cursor: pointer;
        }

        .btn:hover {
            background-color: #5a6268;
        }

        .btn i {
            margin-right: 8px;
        }

        .actions-container {
            margin-top: 20px;
            padding: 20px 0;
            border-top: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Registro de Logins</h1>
        <table>
            <thead>
                <tr>
                    <th>Data/Hora</th>
                    <th>Usuário</th>
                    <th>Username</th>
                    <th>Status</th>
                    <th>IP</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($row = pg_fetch_assoc($result)): ?>
                    <tr>
                        <td><?php echo date('d/m/Y H:i:s', strtotime($row['data_evento'])); ?></td>
                        <td><?php echo htmlspecialchars($row['nome_usuario']); ?></td>
                        <td><?php echo htmlspecialchars($row['username']); ?></td>
                        <td class="<?php echo $row['status'] === 'Sucesso' ? 'success' : 'failure'; ?>">
                            <?php echo $row['status']; ?>
                        </td>
                        <td><?php echo htmlspecialchars($row['ip']); ?></td>
                    </tr>
                <?php endwhile; ?>
            </tbody>
        </table>

        <div class="actions-container">
            <a href="index.php" class="btn">
                <i class="fas fa-arrow-left"></i> Voltar
            </a>
        </div>
    </div>
</body>
</html>

