<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mat<PERSON><PERSON> à Estudar</title>

    <style>
        body {
            font-family: 'Courier New', Courier, monospace;
        }

        table {
            margin: 0 auto;
            border-collapse: collapse;
            width: 80%;
            border: 1px solid #2b2723; /* Adiciona borda à tabela */
        }

        th {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #2b2723;
            border-right: 1px solid #2b2723; /* Adiciona borda à direita das células */
            white-space: nowrap; /* Evita quebras de linha */
            font-weight: bold; /* Negrito apenas para as células de cabeçalho */
        }

        td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #2b2723;
            border-right: 1px solid #2b2723; /* Adiciona borda à direita das células */
            white-space: nowrap; /* Evita quebras de linha */
        }

        th:first-child,
        td:first-child {
            border-left: 1px solid #2b2723; /* Adiciona borda à esquerda das células da primeira coluna */
        }


        .details {
            /* Remova a propriedade width e ajuste max-width conforme necessário */
            max-width: 60%;
            margin: 50px auto;
            background-color: rgba(222, 173, 69, 0.49);
            border: 2px solid #ccc;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            /* Adicione a propriedade word-wrap */
            word-wrap: break-word;
        }

        .details p {
            margin: 8px 0;
            line-height: 1.6;
            white-space: nowrap;
        }

        .details strong {
            width: 210px; /* Largura maior para a primeira coluna */
            display: inline-block;
            font-weight: bold;
        }
    </style>
</head>
<link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
<body>

<?php
include 'consulta_banco_ultima_proxima.php';
// Consultar os dados do último estudo
$query_consultar_ultimo_estudo = "
    SELECT e.*, 
           m_estudo.nome AS nome_materia_estudo, 
           m_estudo.cor AS cor_materia_estudo
    FROM appEstudo.estudos e
    INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
    INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
    LEFT JOIN appEstudo.materia m_estudo ON e.materia_idmateria = m_estudo.idmateria
    WHERE u.idusuario = $id_usuario
    ORDER BY e.data DESC
    LIMIT 1";

$resultado_ultimo_estudo = pg_query($conexao, $query_consultar_ultimo_estudo);

// Verificar se a consulta retornou resultados
if (pg_num_rows($resultado_ultimo_estudo) > 0) {
// Extrair os detalhes do último estudo
    $ultimo_estudo = pg_fetch_assoc($resultado_ultimo_estudo);
// Adicione mais detalhes conforme necessário
    $idMateriaUltimoEstudo = $ultimo_estudo['materia_idmateria'];
} else {
    echo "<p>Nenhum estudo encontrado.</p>";
}


// Query para contar o número de linhas na tabela planejamento_materia
//$query_contagem = "SELECT COUNT(*) FROM appEstudo.planejamento_materia";
//$query_contagem = "SELECT COUNT(*) FROM appEstudo.planejamento_materia WHERE planejamento_idplanejamento = '$id_planejamento'";

$query_contagem = "SELECT 
    idplanejamento,
    (
        SELECT COUNT(*) 
        FROM appEstudo.planejamento_materia 
        WHERE planejamento_idplanejamento = p.idplanejamento
    ) AS count_materia
FROM 
    appEstudo.planejamento p
WHERE 
    usuario_idusuario = $id_usuario;
";

$result = pg_query($conexao, $query_contagem);

if ($result) {
    $row = pg_fetch_assoc($result);

    if ($row && isset($row['count_materia'])) {
        $count_materia = $row['count_materia'];
        $id_planejamento = $row['idplanejamento'];
        //echo "O número de linhas na tabela planejamento_materia é: " . $count_materia. " ----  ";
    } else {
        echo "Nenhum resultado encontrado ou count_materia não está definido.";
    }
} else {
    // echo "Erro na consulta: " . pg_last_error($conn);
}

// Executar a query
//$resultado_contagem = pg_query($conexao, $query_contagem);

// Extrair o resultado como uma matriz associativa
//$linha_contagem = pg_fetch_array($resultado_contagem);

// Armazenar o resultado da contagem em uma variável
//$numero_de_linhas = $linha_contagem['count'] - 1;
$numero_de_linhas = $count_materia - 1;
//echo $numero_de_linhas;

// Exibir o número de linhas
//echo "Número de linhas na tabela: " . $numero_de_linhas;

// Montar a query para selecionar o ID da próxima matéria a ser estudada
$query_proxima_materia = "SELECT COALESCE(
    (SELECT materia_idmateria
     FROM appEstudo.planejamento_materia
     WHERE materia_idmateria > $idMateriaUltimoEstudo AND planejamento_idplanejamento = $id_planejamento
     ORDER BY materia_idmateria
     LIMIT 1),
    (SELECT MIN(materia_idmateria)
     FROM appEstudo.planejamento_materia
     WHERE planejamento_idplanejamento = $id_planejamento)
) AS proximo_idmateria;
";

// Executar a query para obter os detalhes da próxima matéria
$resultado_proxima_materia = pg_query($conexao, $query_proxima_materia);

?>


<?php
$proximaMateriaEstudada = null;
$proximoMateriaNome = null;
if ($resultado_proxima_materia && pg_num_rows($resultado_proxima_materia) > 0) {
    // Exibir os detalhes da próxima matéria
    $linha_proxima_materia = pg_fetch_assoc($resultado_proxima_materia);

    // Obter o ID da próxima matéria
    $id_proxima_materia = $linha_proxima_materia['proximo_idmateria'];


    // Montar a query para obter todos os campos do estudo relacionado à próxima matéria
    $query_estudo_proxima_materia = "
    SELECT e.*, m.nome AS nome_materia, c.nome AS nome_curso
    FROM appEstudo.materia m
    LEFT JOIN appEstudo.estudos e ON e.materia_idmateria = m.idmateria 
    LEFT JOIN appEstudo.curso c ON e.idcurso = c.idcurso 
    WHERE m.idmateria = $id_proxima_materia
    AND (e.planejamento_usuario_idusuario = '$id_usuario' OR e.planejamento_usuario_idusuario IS NULL)
    ORDER BY e.idestudos DESC 
    LIMIT 1
";

    //echo "usuario: " . $id_usuario;
    //echo " Materia: " . $id_proxima_materia;

    // Executar a query para obter os dados do estudo relacionado à próxima matéria
    $resultado_estudo_proxima_materia = pg_query($conexao, $query_estudo_proxima_materia);


    if ($resultado_estudo_proxima_materia && pg_num_rows($resultado_estudo_proxima_materia) > 0) {
        // Exibir os detalhes do estudo relacionado à próxima matéria dentro da tabela
        $linha_estudo_proxima_materia = pg_fetch_assoc($resultado_estudo_proxima_materia);
        $proximaMateriaEstudada = $linha_estudo_proxima_materia['nome_materia'];
        if($linha_estudo_proxima_materia['hora_inicio'] != null) {

            echo '<div class="card text-black bg-warning mb-3" style="max-width: 100%;">
        <h3>Próxima Matéria a Estudar</h3>
        <div class="card-body">
            <div class="card-header text-center"><h4><strong>' . $linha_estudo_proxima_materia['nome_materia'] . '</strong></h4></div>
            <h5 class="card-text text-center"> <b class="text-danger">' . $linha_estudo_proxima_materia['nome_curso'] . '</b></h5>
            <h5 class="card-text text-center"> Último Estudo:<strong> ' . $linha_estudo_proxima_materia['ponto_estudado'] . '</strong></h5>
            <h5 class="card-text text-center">Método:<strong> <b class="text-danger">' . $linha_estudo_proxima_materia['metodo'] . '</b></strong> </h5>

            <div class="row">
                <div class="col text-body">
                    <p class="card-text text-body "><strong>Bruto: </strong> ' . $linha_estudo_proxima_materia['tempo_bruto'] . '<strong> Perdido: </strong> ' . $linha_estudo_proxima_materia['tempo_perdido'] . ' </p>
                </div>
                <div class="col text-end">
                    <p class="card-text text-end "><strong>Início: </strong> ' . $linha_estudo_proxima_materia['hora_inicio'] . '<strong> Fim: </strong> ' . $linha_estudo_proxima_materia['hora_fim'] . ' </p>
                </div>
            </div>

            <div class="row">
                <div class="col text-body">
                    <h5 class="card-text"><strong>Líquido:</strong><b class="text-danger"> ' . $linha_estudo_proxima_materia['tempo_liquido'] . '</b></strong></h5>
                </div>
                <div class="col text-end">
                    <h5 class="card-text text-end"><strong>Data: </strong><b class="text-danger">' . date('d/m/Y', strtotime($linha_estudo_proxima_materia['data'])) . '</b></h5>
                </div>
            </div>
        </div>
    </div>';
        }else{
            echo '<div class="card text-black bg-warning mb-3" style="max-width: 100%;">
        <h3>Próxima Matéria a Estudar</h3>
        <div class="card-body">
            <div class="card-header text-center"><h4><strong><span style="color: white; background-color: red;">ATENÇÃO</span> Ainda não Estudou Essa Matéria</strong></h4></div>
            <h4 class="card-title text-center"><strong>' . $linha_estudo_proxima_materia['nome_materia'] . '</strong></h4>
            <h5 class="card-title text-center"><strong><b class="text-danger">Comece!</strong></b></h5>
        </div>
    </div>';

        }
    } else {
        if ($proximaMateriaEstudada === null) {
            // Se a primeira consulta retornar null, executar uma nova consulta para pegar a primeira linha da tabela
            $query_primeira_materia = "SELECT pm.materia_idmateria, m.nome AS nome_materia
    FROM appEstudo.planejamento_materia pm
    JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    WHERE pm.planejamento_idplanejamento = $id_planejamento
    ORDER BY pm.materia_idmateria ASC
    LIMIT 1;
";

            $resultado_primeira_materia = pg_query($conexao, $query_primeira_materia);

            if ($resultado_primeira_materia && pg_num_rows($resultado_primeira_materia) > 0) {
                $linha_primeira_materia = pg_fetch_assoc($resultado_primeira_materia);
                $proximoMateriaNome = $linha_primeira_materia['nome_materia'];
                //echo "Próxima matéria a ser estudada (primeira linha): " . $proximoIdMateria;


                echo '<div class="card text-black bg-warning mb-3" style="max-width: 100%;">
        <h3>Próxima Matéria a Estudar</h3>
        <div class="card-body">
            <div class="card-header text-center"><h4><strong><span style="color: white; background-color: red;">ATENÇÃO</span> Muito Tempo sem Estudar</strong></h4></div>
            <h4 class="card-title text-center"><strong>' . $proximoMateriaNome . '</strong></h4>
            <h5 class="card-title text-center"><strong><b class="text-danger">Comece ou Revise!</strong></b></h5>
        </div>
    </div>';
            } else {
                echo "Nenhuma matéria encontrada no planejamento.";
            }


        } else {
            echo '<div class="card text-black bg-warning mb-3" style="max-width;" xmlns="http://www.w3.org/1999/html">
           <div class="card-header text-center"><h4><strong><b class="text-danger">Não Iniciou o Estudo!</strong></b></h4></div>
           </div>';

        }
    }
}

//$proximaMateriaEstudada = $linha_estudo_proxima_materia['nome_materia'];
// Fechar a conexão com o banco de dados PostgreSQL
//pg_close($conexao);
?>