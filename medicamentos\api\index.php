<?php
// api/index.php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

// Obter a rota da requisição
$route = isset($_GET['route']) ? $_GET['route'] : '';

// Roteamento baseado no parâmetro 'route'
switch ($route) {
    case 'login':
        require_once './auth_handler.php';
        break;
    case 'medicamentos':
        require_once './medicamentos.php';
        break;
    case 'medicamento': // ADICIONE ESTA LINHA PARA DETALHES DE UM MEDICAMENTO ESPECÍFICO
        require_once './medicamentos.php';
        break;
    case 'pendentes':
        require_once './medicamentos.php';
        break;
    case 'proximos':
        require_once './medicamentos.php';
        break;
    case 'confirmar':
        require_once './medicamentos.php';
        break;
    case 'adicionar_medicamento':
        require_once './medicamentos.php';
        break;
    case 'registros':
        require_once './registros.php';
        break;

    // Adicione esta linha no switch case do arquivo index.php
    case 'listar_todos':
        require_once './medicamentos.php';
        break;
    default:
        http_response_code(404);
        echo json_encode(['message' => 'Rota não encontrada']);
        break;
}
?>