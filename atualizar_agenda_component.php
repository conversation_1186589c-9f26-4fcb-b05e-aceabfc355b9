<?php
//atualizar_agenda_component.php
session_start();
include 'conexao_POST.php';

if (!isset($_SESSION['idusuario'])) {
  die(json_encode(['erro' => 'Usuário não logado']));
}

$idUsuario = $_SESSION['idusuario'];

// Buscar eventos pendentes
$query_pendentes = "SELECT id, titulo FROM appEstudo.agenda 
                 WHERE usuario_idusuario = $1 
                 AND realizado = 'f' 
                 AND data_fim < CURRENT_DATE
                 ORDER BY data_fim ASC";
$result_pendentes = pg_query_params($conexao, $query_pendentes, array($idUsuario));
$eventosPendentes = pg_fetch_all($result_pendentes);

// Buscar eventos do dia
$query_dia = "SELECT id, titulo FROM appEstudo.agenda 
            WHERE usuario_idusuario = $1 
            AND date(data_inicio) = CURRENT_DATE
            AND realizado = 'f'
            ORDER BY data_inicio ASC";
$result_dia = pg_query_params($conexao, $query_dia, array($idUsuario));
$eventos_do_Dia = pg_fetch_all($result_dia);

// Buscar próxima data com eventos
$query_proxima_data = "SELECT MIN(date(data_inicio)) as proxima_data 
                     FROM appEstudo.agenda 
                     WHERE usuario_idusuario = $1 
                     AND data_inicio > CURRENT_DATE 
                     AND realizado = 'f'";
                     
$result_proxima_data = pg_query_params($conexao, $query_proxima_data, array($idUsuario));
$row_proxima_data = pg_fetch_assoc($result_proxima_data);

$eventosProximos = [];
$evento_proximo = null;
$diasAteProximoEvento = 0;

// Buscar todos os eventos da próxima data
if ($row_proxima_data && $row_proxima_data['proxima_data']) {
  $query_proximos = "SELECT id, titulo, data_inicio 
                    FROM appEstudo.agenda 
                    WHERE usuario_idusuario = $1
                    AND date(data_inicio) = $2
                    AND realizado = 'f'  
                    ORDER BY data_inicio ASC";
  
  $result_proximos = pg_query_params($conexao, $query_proximos, 
      array($idUsuario, $row_proxima_data['proxima_data'])
  );
  
  $eventosProximos = pg_fetch_all($result_proximos);
  
  if (!empty($eventosProximos)) {
   $evento_proximo = strtotime($eventosProximos[0]['data_inicio']);
   $hoje = new DateTime('today');
   $data_evento = new DateTime($eventosProximos[0]['data_inicio']);
   $data_evento->setTime(0, 0);
   
   // Calcula a diferença em dias
   $diasAteProximoEvento = $hoje->diff($data_evento)->days;
   
   // Se o evento é hoje mas ainda não passou, mantém como 1 dia
   if ($diasAteProximoEvento == 0 && $data_evento >= $hoje) {
       $diasAteProximoEvento = 1;
   }
   // Se o evento é para hoje mas já passou, mostra como 0 dias
   else if ($diasAteProximoEvento == 0 && $data_evento < $hoje) {
       $diasAteProximoEvento = 0;
   }
  }
}

if (empty($eventosPendentes) && empty($eventos_do_Dia) && empty($evento_proximo)) {
  echo '<div class="sem-eventos" style="text-align: center; padding: 20px; background: #f9f9f9; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">';
  echo '<i class="fas fa-calendar-times" style="font-size: 48px; color: #ccc; margin-bottom: 10px;"></i>';
  echo '<p style="color: #666; font-size: 18px;">Não há eventos agendados</p>';
  echo '</div>';
} else {
  // Eventos Pendentes
  if (!empty($eventosPendentes)) {
      echo '<div class="quadrado_pendente" style="margin: 10px 0; padding: 10px; background: #fff5f5; border-left: 4px solid red; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">';
      echo "<strong style='font-size: 14px;'>📍 Pendente: ";
      $eventos = array_map(function($evento) {
          return sprintf(
              "<span class='evento-link-dashboard' data-id='%s' style='color: red; cursor: pointer; text-decoration: underline;'>%s</span>",
              htmlspecialchars($evento['id']),
              htmlspecialchars($evento['titulo'])
          );
      }, $eventosPendentes);
      echo implode("<strong style='color: #666;'> • </strong>", $eventos);
      echo "</strong></div>";
  }

  // Eventos do Dia
  if (!empty($eventos_do_Dia)) {
      echo '<div class="quadrado_hoje" style="margin: 10px 0; padding: 10px; background: #f0fff4; border-left: 4px solid green; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">';
      echo "<strong style='font-size: 14px;'>⌚ Hoje: ";
      $eventos = array_map(function($evento) {
          return sprintf(
              "<span class='evento-link-dashboard' data-id='%s' style='color: green; cursor: pointer; text-decoration: underline;'>%s</span>",
              htmlspecialchars($evento['id']),
              htmlspecialchars($evento['titulo'])
          );
      }, $eventos_do_Dia);
      echo implode("<strong style='color: #666;'> • </strong>", $eventos);
      echo "</strong></div>";
  }

  // Próximo Evento
  if ($evento_proximo !== null) {
      echo '<div class="quadrado_proximo_1" style="margin: 10px 0; padding: 10px; background: #fff9f0; border-left: 4px solid #df7201; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">';
      echo "<strong style='font-size: 14px;'>🗓️ Data Mais Próxima: <span style='color: #df7201;'>" . date('d-m-Y', $evento_proximo) . "</span></strong><br>";
      echo "<strong style='font-size: 14px;'>📌 Eventos: ";
      $eventos = array_map(function($evento) {
          return sprintf(
              "<span class='evento-link-dashboard' data-id='%s' style='color: #df7201; cursor: pointer; text-decoration: underline;'>%s</span>",
              htmlspecialchars($evento['id']),
              htmlspecialchars($evento['titulo'])
          );
      }, $eventosProximos);
      echo implode("<span style='color: #666;'> • </span>", $eventos);
      echo "</strong><br>";
      $palavraDias = ($diasAteProximoEvento <= 1) ? "Dia" : "Dias";
      echo "<strong style='font-size: 14px;'>⏳ Falta: <span style='color: #df7201;'>$diasAteProximoEvento $palavraDias</span></strong>";
      echo '</div>';
  }
}
?>