<?php
// =====================================================
// CONFIGURAÇÕES DO SISTEMA BARBEARIA
// =====================================================

// Configurações do Banco MySQL
define('DB_HOST', 'barbeiro_br.mysql.dbaas.com.br');
define('DB_NAME', 'barbeiro_br');
define('DB_USER', 'barbeiro_br');
define('DB_PASS', 'Lucasb90#'); // ALTERE AQUI para sua senha MySQL

// Chave de segurança para páginas administrativas
// IMPORTANTE: Altere esta chave para algo único e seguro
define('ADMIN_KEY', 'bandoleiro90'); // ALTERE AQUI

// Configurações de segurança
define('PASSWORD_MIN_LENGTH', 6);
define('CPF_LENGTH', 11);

// Função para conectar ao banco
function conectarBanco() {
    try {
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4";
        $pdo = new PDO($dsn, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        return $pdo;
    } catch (PDOException $e) {
        throw new Exception("Erro de conexão com o banco: " . $e->getMessage());
    }
}

// Função para verificar chave de admin
function verificarChaveAdmin($chave) {
    return $chave === ADMIN_KEY;
}

// Função para validar CPF
function validarCPF($cpf) {
    $cpf = preg_replace('/\D/', '', $cpf);
    return strlen($cpf) === CPF_LENGTH && ctype_digit($cpf);
}

// Função para validar senha
function validarSenha($senha) {
    return strlen($senha) >= PASSWORD_MIN_LENGTH;
}

// Função para gerar ID do usuário
function gerarIdUsuario($nome, $cpf, $tipo = 'cliente') {
    $sufixo = $tipo === 'barbeiro' ? 'ADM' : 'CLT';
    return $nome . '-' . $sufixo . $cpf;
}

// Função para verificar se já existe dono
function jaExisteDono($pdo) {
    $stmt = $pdo->query("SELECT COUNT(*) FROM usuarios WHERE is_owner = TRUE");
    return $stmt->fetchColumn() > 0;
}

// Função para buscar informações do dono
function buscarInfoDono($pdo) {
    $stmt = $pdo->query("
        SELECT u.id, u.nome, u.cpf, u.data_criacao,
               bm.senha_mestra, bm.data_criacao as data_senha_mestra
        FROM usuarios u
        LEFT JOIN barbeiro_master bm ON u.id = bm.dono_id AND bm.ativo = TRUE
        WHERE u.is_owner = TRUE
        LIMIT 1
    ");
    return $stmt->fetch();
}

// Função para cadastrar primeiro dono
function cadastrarPrimeiroDono($pdo, $nome, $cpf, $senha, $senhaMestra) {
    // Validações
    if (empty($nome) || empty($cpf) || empty($senha) || empty($senhaMestra)) {
        throw new Exception("Todos os campos são obrigatórios.");
    }
    
    if (!validarCPF($cpf)) {
        throw new Exception("CPF deve ter 11 dígitos.");
    }
    
    if (!validarSenha($senha)) {
        throw new Exception("Senha deve ter pelo menos " . PASSWORD_MIN_LENGTH . " caracteres.");
    }
    
    if (!validarSenha($senhaMestra)) {
        throw new Exception("Senha mestra deve ter pelo menos " . PASSWORD_MIN_LENGTH . " caracteres.");
    }
    
    // Verificar se já existe dono
    if (jaExisteDono($pdo)) {
        throw new Exception("Já existe um dono cadastrado na barbearia.");
    }
    
    try {
        $pdo->beginTransaction();
        
        // Gerar ID do dono
        $donoId = gerarIdUsuario($nome, $cpf, 'barbeiro');
        
        // Inserir usuário dono
        $stmt = $pdo->prepare("
            INSERT INTO usuarios (id, cpf, nome, senha, tipo, is_owner) 
            VALUES (?, ?, ?, ?, 'barbeiro', TRUE)
        ");
        $stmt->execute([$donoId, $cpf, $nome, password_hash($senha, PASSWORD_DEFAULT)]);
        
        // Inserir senha mestra
        $stmt = $pdo->prepare("
            INSERT INTO barbeiro_master (dono_id, senha_mestra, ativo) 
            VALUES (?, ?, TRUE)
        ");
        $stmt->execute([$donoId, $senhaMestra]);
        
        $pdo->commit();
        return $donoId;
        
    } catch (PDOException $e) {
        $pdo->rollBack();
        throw new Exception("Erro ao cadastrar: " . $e->getMessage());
    }
}

// Função para formatar data brasileira
function formatarDataBR($data) {
    return date('d/m/Y H:i', strtotime($data));
}

// Função para limpar entrada
function limparEntrada($valor) {
    return htmlspecialchars(trim($valor), ENT_QUOTES, 'UTF-8');
}

// Função para gerar resposta JSON
function responderJSON($sucesso, $mensagem, $dados = null) {
    header('Content-Type: application/json');
    echo json_encode([
        'sucesso' => $sucesso,
        'mensagem' => $mensagem,
        'dados' => $dados
    ]);
    exit;
}

// Configurações de timezone
date_default_timezone_set('America/Sao_Paulo');

// Configurações de erro (desabilitar em produção)
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
?>
