<?php
// Topo de dashboard-agenda.php

if (!defined('MEU_SISTEMA_PHP_DASHBOARD_VALIDO')) {
    // Se a constante não estiver definida, o arquivo foi acessado diretamente.
    // Mostra a mensagem SweetAlert e redireciona.

    echo '<!DOCTYPE html>
    <html>
    <head>
        <title>Acesso Negado - Redirecionando...</title>
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <style>
            body { margin: 0; padding: 0; font-family: "Quicksand", sans-serif; display: flex; align-items: center; justify-content: center; min-height: 100vh; background-color: #f0f2f5; }
            .swal2-popup { font-family: "Quicksand", sans-serif !important; }
        </style>
    </head>
    <body>
        <script>
            document.addEventListener("DOMContentLoaded", function() {
                Swal.fire({
                    title: "Acesso Direto Negado",
                    text: "Esta página não pode ser acessada diretamente. Você será redirecionado.",
                    icon: "error",
                    confirmButtonText: "OK",
                    confirmButtonColor: "#00008B",
                    allowOutsideClick: false
                }).then((result) => {
                    window.location.href = "index.php"; // Redireciona para a página principal
                });
            });
        </script>
    </body>
    </html>';
    exit; // Para a execução do script.
}

// O restante do código original de dashboard-inicio.php continua aqui...
// Ex: echo "<h1>Conteúdo do Dashboard de Início</h1>";
// Suas consultas e lógica específica para este dashboard...
?>

<?php
//dashboard-agenda.php

// Consultar os dados do planejamento relacionado ao usuario logado
$query_consultar_idplanejamento = "SELECT p.idplanejamento FROM appEstudo.planejamento p WHERE p.usuario_idusuario = $1";
$resultado_idplanejamento = pg_prepare($conexao, "consulta_planejamento", $query_consultar_idplanejamento);
$resultado_idplanejamento = pg_execute($conexao, "consulta_planejamento", array($id_usuario));

if ($resultado_idplanejamento) {
$row = pg_fetch_assoc($resultado_idplanejamento);
$id_planejamento = $row['idplanejamento'];

// Consultar as matérias relacionadas ao planejamento do usuario logado
$query_consultar_materias = "SELECT m.* FROM appEstudo.materia m INNER JOIN appEstudo.planejamento_materia pm ON m.idmateria = pm.materia_idmateria WHERE pm.planejamento_idplanejamento = $1";
$resultado_materias = pg_prepare($conexao, "consulta_materias", $query_consultar_materias);
$resultado_materias = pg_execute($conexao, "consulta_materias", array($id_planejamento));

$materias = array();
while ($row = pg_fetch_assoc($resultado_materias)) {
    $materias[] = $row['nome'];
}

// Consultar os Cursos Registrados
$query_consultar_cursos = "SELECT c.nome FROM appEstudo.curso c INNER JOIN appEstudo.usuario_has_curso uc ON c.idcurso = uc.curso_idcurso WHERE uc.usuario_idusuario = $1";
$resultado_cursos = pg_prepare($conexao, "consulta_cursos", $query_consultar_cursos);
$resultado_cursos = pg_execute($conexao, "consulta_cursos", array($id_usuario));

$cursos = array();
while ($row = pg_fetch_assoc($resultado_cursos)) {
    $cursos[] = $row['nome'];
}

// Consultar os Cursos Registrados
$query_consultar_cursos = "SELECT c.nome 
                           FROM appEstudo.curso c
                           INNER JOIN appEstudo.usuario_has_curso uc
                           ON c.idcurso = uc.curso_idcurso
                           WHERE uc.usuario_idusuario = $id_usuario";
$resultado_cursos = pg_query($conexao, $query_consultar_cursos);

$cursos = array();
while ($row = pg_fetch_assoc($resultado_cursos)) {
    $cursos[] = $row['nome'];
}

// Processar requisições AJAX
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verificar se é uma requisição para excluir/atualizar evento
    if(isset($_POST['eventoIdexcluir']) || isset($_POST['eventoId'])) {
        $eventoIdExcluir = $_POST['eventoIdexcluir'] ?? null;
        $eventoId = $_POST['eventoId'] ?? null;
        $estadoCheckbox = $_POST['estadoCheckbox'] ?? null;

        if ($eventoIdExcluir !== null) {
            $query_excluir = "DELETE FROM appEstudo.agenda WHERE id = $eventoIdExcluir";
            $resultado_excluir = pg_query($conexao, $query_excluir);
            echo json_encode([
                'success' => $resultado_excluir ? true : false,
                'message' => $resultado_excluir ? '' : 'Erro ao excluir o evento'
            ]);
            exit;
        } elseif ($eventoId !== null && $estadoCheckbox !== null) {
            $realizado = ($estadoCheckbox === 'true') ? "'t'" : "'f'";
            $query_atualizar_estado = "UPDATE appEstudo.agenda SET realizado = $realizado WHERE id = $eventoId";
            $resultado_atualizacao = pg_query($conexao, $query_atualizar_estado);
            echo json_encode([
                'success' => $resultado_atualizacao ? true : false,
                'message' => $resultado_atualizacao ? '' : 'Erro ao atualizar o estado'
            ]);
            exit;
        }
    }
    

}

// Endpoint para listar eventos
if(isset($_GET['listar_eventos'])) {
    $query_consultar_agenda = "SELECT * FROM appEstudo.agenda WHERE usuario_idusuario = $id_usuario";
    $resultado_agenda = pg_query($conexao, $query_consultar_agenda);
    $events = array();

    while ($registro = pg_fetch_assoc($resultado_agenda)) {
        // Definir cor baseado no tipo de evento
        switch($registro['tipo_evento']) {
            case 'Faculdade':
                $cor = '#1976D2';
                break;
            case 'Trabalho':
                $cor = 'gray';
                break;
            case 'Concurso':
                $cor = 'blue';
                break;
            case 'Pessoal':
                $cor = '#00796B';
                break;
            case 'Planejamento':
                $cor = '#FFD700';
                $textColor = 'black';
                break;
            default:
                $cor = '#000000';
                $textColor = '';
        }

        $corBorda = $registro['realizado'] == "f" ? 'red' : 'green';

        $event = array(
            'id' => $registro['id'],
            'title' => $registro['titulo'],
            'start' => date('Y-m-d H:i', strtotime($registro['data_inicio'])),
            'end' => date('Y-m-d H:i', strtotime($registro['data_fim'])),
            'detalhes' => $registro['detalhes'],
            'tipo' => $registro['tipo_evento'],
            'realizado' => $registro['realizado'],
            'color' => $cor,
            'borderColor' => $corBorda,
            'textColor' => $textColor ?? ''
        );

        $events[] = $event;
    }

    header('Content-Type: application/json');
    echo json_encode($events);
    exit;
}
} else {
    echo "Erro ao executar a consulta.";
}
?>

<!-- HTML -->
<div class="dashboard-grid-umaColuna">
    <div class="cards-stack">
            <div class="legenda-item">
                <div class="legenda-cor" style="background-color: #1976D2;"></div>
                <div class="legenda-texto">Faculdade</div>

                <div class="legenda-cor" style="background-color: gray;"></div>
                <div class="legenda-texto">Trabalho</div>

                <div class="legenda-cor" style="background-color: blue;"></div>
                <div class="legenda-texto">Concurso</div>

                <div class="legenda-cor" style="background-color: #00796B;"></div>
                <div class="legenda-texto">Pessoal</div>

                <div class="legenda-cor" style="background-color: #FFD700;"></div>
                <div class="legenda-texto">Planejamento</div>

                <div class="legenda-cor legenda-pendente"></div>
                <div class="legenda-texto">Evento Pendente</div>

                <div class="legenda-cor legenda-realizado"></div>
                <div class="legenda-texto">Evento Realizado</div>
            </div>
    </div>
</div>

<div class="dashboard-grid-duasColunas-agenda">
    <div class="cards-stack">
        <div class="card-planejamento">
            <div class="card-header" style="margin-bottom: 15px;">
                <h3>Calendário Pessoal</h3>
            </div>
            <div id="calendario_agenda"></div>
        </div>
    </div>

    <div class="cards-stack">
                    <!-- Nova seção para listar eventos futuros -->

    <div class="card-planejamento">
        <div class="card-header" style="margin-bottom: 15px;">
            <h3>Próximos Eventos</h3>
        </div>
        <div class="eventos-futuros-container">
            <?php
            // Consultar eventos futuros
            $query_eventos_futuros = "SELECT * FROM appEstudo.agenda 
                                     WHERE usuario_idusuario = $id_usuario 
                                     AND data_inicio > CURRENT_TIMESTAMP 
                                     ORDER BY data_inicio ASC";
            $resultado_eventos_futuros = pg_query($conexao, $query_eventos_futuros);
            
            if (pg_num_rows($resultado_eventos_futuros) > 0) {
                echo '<div class="lista-eventos">';
                while ($evento = pg_fetch_assoc($resultado_eventos_futuros)) {
                    // Definir cor baseado no tipo de evento
                    switch($evento['tipo_evento']) {
                        case 'Faculdade':
                            $cor = '#1976D2';
                            break;
                        case 'Trabalho':
                            $cor = 'gray';
                            break;
                        case 'Concurso':
                            $cor = 'blue';
                            break;
                        case 'Pessoal':
                            $cor = '#00796B';
                            break;
                        case 'Planejamento':
                            $cor = '#FFD700';
                            break;
                        default:
                            $cor = '#000000';
                    }
                    
                    $data_formatada = date('d/m/Y', strtotime($evento['data_inicio']));
                    $hora_formatada = date('H:i', strtotime($evento['data_inicio']));
                    $dias_restantes = ceil((strtotime($evento['data_inicio']) - time()) / (60 * 60 * 24));
                    $texto_dias = $dias_restantes == 1 ? "Falta 1 dia" : "Faltam $dias_restantes dias";
                    
                    echo "<div class='evento-item' data-id='{$evento['id']}'>
                            <div class='evento-marcador' style='background-color: $cor'></div>
                            <div class='evento-conteudo'>
                                <div class='evento-titulo'>{$evento['titulo']}</div>
                                <div class='evento-data'>
                                    <i class='fas fa-calendar-alt'></i> $data_formatada 
                                   
                                </div>
                                <div class='evento-contagem'>
                                    <i class='fas fa-hourglass-half'></i> $texto_dias
                                </div>
                            </div>
                        </div>";
                }
                echo '</div>';
            } else {
                echo '<div class="sem-eventos">
                        <i class="fas fa-calendar-times"></i>
                        <p>Não há eventos futuros agendados</p>
                      </div>';
            }
            ?>
        </div>
    </div>
        <div class="card-planejamento">
            <div class="card-header" style="margin-bottom: 15px;">
                <h3>Criar Evento</h3>
            </div>
            <!-- Seu formulário atual -->

                <div class="bg-white rounded-lg shadow-lg p-6">
                    <form id="eventoForm" class="space-y-4">
                        <!-- Tipo de Evento -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">Tipo de Evento:</label>
                            <select id="tipo_evento" name="tipo_evento" required
                                    class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Selecione...</option>
                                <option value="Faculdade">Faculdade</option>
                                <option value="Trabalho">Trabalho</option>
                                <option value="Concurso">Concurso</option>
                                <option value="Pessoal">Pessoal</option>
                                <option value="Planejamento">Planejamento</option>
                            </select>
                        </div>

                        <!-- Campo Matéria -->
                        <div id="campo_materia" class="space-y-2 hidden">
                            <label class="block text-sm font-medium text-gray-700">Matéria</label>
                            <select id="materia" name="materia"
                                    class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="">Selecione uma matéria...</option>
                                <?php foreach ($materias as $materia): ?>
                                    <option value="<?php echo htmlspecialchars($materia); ?>">
                                        <?php echo htmlspecialchars($materia); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Campo Título -->
                        <div id="campo_titulo" class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">Título</label>
                            <input type="text" id="titulo" name="titulo"
                                   placeholder="Título do evento"
                                   class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <!-- Data -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">Dia:</label>
                            <input type="date" id="data_inicio" name="data_inicio" required
                                   class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <!-- Detalhes -->
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">Detalhes:</label>
                            <textarea id="detalhes" name="detalhes"
                                      placeholder="Detalhes do evento"
                                      class="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 h-32"></textarea>
                        </div>

                        <!-- Botão Submit -->
                        <button type="submit"
                                class="w-full bg-gradient-to-r from-blue-500 to-blue-700 hover:from-blue-600 hover:to-blue-800 text-white py-3 rounded-lg shadow-lg transform hover:scale-105 transition-all duration-200 flex items-center justify-center gap-2">
                            <i class="fas fa-save"></i>
                            <span class="font-semibold">Criar Evento</span>
                        </button>
                    </form>
                </div>
            </div>
            </div>


</div>


</div>
</div>

<!-- Modal -->
<div id="modalEvento" class="modal-especifico" style="display: none;">
    <div class="modal-especifico-overlay"></div>
    <div class="modal-especifico-content">
        <button class="modal-especifico-close"><i class="fas fa-times"></i></button>

        <!-- Checkbox para habilitar edição -->
        <div class="modo-edicao-container" style="margin-bottom: 1rem;">
            <label class="switch">
                <input type="checkbox" id="habilitarEdicao">
                <span class="slider round"></span>
            </label>
            <span class="modo-edicao-texto">Modo Edição</span>
        </div>

        <!-- Campos editáveis -->
        <input type="text" class="modal-especifico-titulo-input" disabled style="width: 100%; margin-bottom: 1rem;">

        <div class="status-section modal-especifico-status-section">
            <span class="status-badge realizado modal-especifico-badge">
                <i class="fas fa-check-circle"></i> Realizado
            </span>
            <span class="status-badge pendente modal-especifico-badge">
                <i class="fas fa-clock"></i> Pendente
            </span>
        </div>

        <div class="date-editor" style="margin: 1rem 0;">
            <label>Data:</label>
            <input type="date" class="modal-especifico-data" disabled style="width: 100%;">
        </div>

        <div class="details-container">
            <textarea class="modal-especifico-detalhes-input" disabled style="width: 100%; min-height: 100px;"></textarea>
        </div>

        <div id="eventoRealizadoCheckboxContainer" style="margin: 1rem 0;">
            <label>
                <input type="checkbox" id="eventoRealizadoCheckbox"> Marcar como realizado
            </label>
        </div>

        <div class="button-group">
            <button class="btn btn-success salvarEventoModal" disabled>
                <i class="fas fa-save"></i> Salvar
            </button>
            <button class="btn btn-danger excluirEventoModal">
                <i class="fas fa-trash"></i> Excluir
            </button>
        </div>
    </div>
</div>

<!-- JavaScript -->


<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Formulário
        document.getElementById('tipo_evento').addEventListener('change', function() {
            const campoMateria = document.getElementById('campo_materia');
            const campoTitulo = document.getElementById('campo_titulo');
            const materiaSelect = document.getElementById('materia');
            const tituloInput = document.getElementById('titulo');

            if (this.value === 'Planejamento') {
                campoMateria.classList.remove('hidden');
                campoTitulo.style.display = 'none';
                materiaSelect.setAttribute('required', 'required');
                tituloInput.removeAttribute('required');
            } else {
                campoMateria.classList.add('hidden');
                campoTitulo.style.display = 'block';
                materiaSelect.removeAttribute('required');
                tituloInput.setAttribute('required', 'required');
            }
        });

        // Função para atualizar a lista de eventos futuros
        window.atualizarListaEventos = function() {
            console.log('Iniciando atualização da lista de eventos...');
            $.ajax({
                url: 'buscar_eventos_futuros.php',
                method: 'GET',
                dataType: 'json',
                success: function(response) {
                    console.log('Resposta recebida:', response);
                    const container = document.querySelector('.eventos-futuros-container');
                    
                    if (!container) {
                        console.error('Container de eventos não encontrado');
                        return;
                    }
                    
                    // Verificar se response é um array
                    if (Array.isArray(response)) {
                        console.log('Atualizando lista com', response.length, 'eventos');
                        let html = '<div class="lista-eventos">';
                        
                        if (response.length > 0) {
                            response.forEach(evento => {
                                // Definir cor baseado no tipo de evento
                                let cor;
                                switch(evento.tipo_evento) {
                                    case 'Faculdade':
                                        cor = '#1976D2';
                                        break;
                                    case 'Trabalho':
                                        cor = 'gray';
                                        break;
                                    case 'Concurso':
                                        cor = 'blue';
                                        break;
                                    case 'Pessoal':
                                        cor = '#00796B';
                                        break;
                                    case 'Planejamento':
                                        cor = '#FFD700';
                                        break;
                                    default:
                                        cor = '#000000';
                                }
                                
                                const data_formatada = new Date(evento.data_inicio).toLocaleDateString('pt-BR');
                                const dias_restantes = Math.ceil((new Date(evento.data_inicio) - new Date()) / (1000 * 60 * 60 * 24));
                                const texto_dias = dias_restantes == 1 ? "Falta 1 dia" : `Faltam ${dias_restantes} dias`;
                                
                                html += `<div class='evento-item' data-id='${evento.id}'>
                                    <div class='evento-marcador' style='background-color: ${cor}'></div>
                                    <div class='evento-conteudo'>
                                        <div class='evento-titulo'>${evento.titulo}</div>
                                        <div class='evento-data'>
                                            <i class='fas fa-calendar-alt'></i> ${data_formatada}
                                        </div>
                                        <div class='evento-contagem'>
                                            <i class='fas fa-hourglass-half'></i> ${texto_dias}
                                        </div>
                                    </div>
                                </div>`;
                            });
                        } else {
                            html = `<div class="sem-eventos">
                                <i class="fas fa-calendar-times"></i>
                                <p>Não há eventos futuros agendados</p>
                            </div>`;
                        }
                        
                        container.innerHTML = html;
                        console.log('Lista atualizada com sucesso');
                        
                        // Reativar os event listeners para os novos itens
                        document.querySelectorAll('.evento-item').forEach(item => {
                            item.addEventListener('click', function() {
                                const eventoId = this.getAttribute('data-id');
                                if (window.eventManager) {
                                    window.eventManager.carregarDetalhesEvento(eventoId);
                                }
                            });
                        });
                    } else {
                        console.error('Resposta inválida:', response);
                        container.innerHTML = `<div class="sem-eventos">
                            <i class="fas fa-exclamation-triangle"></i>
                            <p>Erro ao carregar eventos</p>
                        </div>`;
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erro ao atualizar lista de eventos:', error);
                    console.log('Resposta do servidor:', xhr.responseText);
                    const container = document.querySelector('.eventos-futuros-container');
                    if (container) {
                        container.innerHTML = `<div class="sem-eventos">
                            <i class="fas fa-exclamation-triangle"></i>
                            <p>Erro ao carregar eventos</p>
                        </div>`;
                    }
                }
            });
        };

        // Modificar o evento de submit do formulário
        document.getElementById('eventoForm').addEventListener('submit', function(e) {
            e.preventDefault();
            const formData = new FormData(this);

            // Validações básicas
            const tipoEvento = formData.get('tipo_evento');
            const titulo = formData.get('titulo');
            const materia = formData.get('materia');
            const dataInicio = formData.get('data_inicio');

            if (!tipoEvento) {
                alert('Selecione o tipo do evento');
                return;
            }

            if (tipoEvento === 'Planejamento' && !materia) {
                alert('Selecione uma matéria');
                return;
            }

            if (tipoEvento !== 'Planejamento' && !titulo) {
                alert('Digite um título para o evento');
                return;
            }

            if (!dataInicio) {
                alert('Selecione uma data');
                return;
            }

            $.ajax({
                url: 'processa_novo_evento.php',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: async function(response) {
                    try {
                        if (response.success) {
                            // Limpar formulário
                            e.target.reset();

                            // Atualizar a lista de eventos
                            atualizarListaEventos();

                            // Se o EventManager existe
                            if (window.eventManager) {
                                // Atualizar a interface
                                await window.eventManager.atualizarInterface();
                            }

                            // Atualizar calendário
                            const calendarEl = document.getElementById('calendario_agenda');
                            if (calendarEl && calendarEl.fcCalendar) {
                                calendarEl.fcCalendar.refetchEvents();
                            }
                        } else {
                            throw new Error(response.message || 'Erro ao criar evento');
                        }
                    } catch (error) {
                        console.error('Erro ao processar resposta:', error);
                        alert(error.message || 'Erro ao criar evento');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erro na requisição:', {xhr, status, error});
                    let errorMessage = 'Erro ao criar evento';
                    
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMessage = response.message;
                        }
                    } catch(e) {
                        console.error('Erro ao interpretar resposta:', e);
                    }
                    
                    alert(errorMessage);
                }
            });
        });

        // Adicionar evento para o botão de excluir no modal
        document.querySelector('.excluirEventoModal').addEventListener('click', function() {
            console.log('Botão excluir clicado');
            const modal = document.getElementById('modalEvento');
            const eventoId = modal.dataset.eventoId;
            console.log('ID do evento a ser excluído:', eventoId);

            if (eventoId) {
                // Confirmar antes de excluir
                Swal.fire({
                    title: 'Confirmar exclusão',
                    text: 'Tem certeza que deseja excluir este evento?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Sim, excluir',
                    cancelButtonText: 'Cancelar'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Mostrar loading
                        Swal.fire({
                            title: 'Excluindo...',
                            text: 'Por favor, aguarde',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });

                        $.ajax({
                            url: 'processa_novo_evento.php',
                            method: 'POST',
                            data: {
                                eventoIdexcluir: eventoId
                            },
                            success: function(response) {
                                console.log('Resposta da exclusão:', response);
                                
                                if (response.success) {
                                    // Fechar o modal
                                    modal.style.display = 'none';
                                    
                                    // Remover o evento da lista imediatamente
                                    const eventoItem = document.querySelector(`.evento-item[data-id="${eventoId}"]`);
                                    if (eventoItem) {
                                        console.log('Removendo evento da lista:', eventoId);
                                        eventoItem.remove();
                                    }
                                    
                                    // Atualizar a lista completa de eventos
                                    console.log('Atualizando lista de eventos...');
                                    atualizarListaEventos();
                                    
                                    // Atualizar o calendário
                                    const calendarEl = document.getElementById('calendario_agenda');
                                    if (calendarEl && calendarEl.fcCalendar) {
                                        console.log('Atualizando calendário...');
                                        calendarEl.fcCalendar.refetchEvents();
                                    }
                                    
                                    // Mostrar mensagem de sucesso
                                    Swal.fire({
                                        icon: 'success',
                                        title: 'Sucesso!',
                                        text: 'Evento excluído com sucesso!',
                                        timer: 1500,
                                        showConfirmButton: false
                                    });
                                } else {
                                    console.error('Erro na exclusão:', response.message);
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Erro!',
                                        text: response.message || 'Erro ao excluir o evento'
                                    });
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('Erro ao excluir evento:', error);
                                console.log('Resposta do servidor:', xhr.responseText);
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Erro!',
                                    text: 'Erro ao excluir o evento. Por favor, tente novamente.'
                                });
                            }
                        });
                    }
                });
            } else {
                console.error('ID do evento não encontrado no modal');
                Swal.fire({
                    icon: 'error',
                    title: 'Erro!',
                    text: 'ID do evento não encontrado'
                });
            }
        });

        // Adicionar evento para o checkbox de realizado no modal
        document.getElementById('eventoRealizadoCheckbox').addEventListener('change', function() {
            const modal = document.getElementById('modalEvento');
            const eventoId = modal.dataset.eventoId;
            const estadoCheckbox = this.checked;

            if (eventoId) {
                $.ajax({
                    url: 'processa_novo_evento.php',
                    method: 'POST',
                    data: {
                        eventoId: eventoId,
                        estadoCheckbox: estadoCheckbox
                    },
                    success: function(response) {
                        if (response.success) {
                            // Atualizar a lista de eventos
                            atualizarListaEventos();

                            // Atualizar o calendário
                            const calendarEl = document.getElementById('calendario_agenda');
                            if (calendarEl && calendarEl.fcCalendar) {
                                calendarEl.fcCalendar.refetchEvents();
                            }
                        } else {
                            alert(response.message || 'Erro ao atualizar o estado do evento');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Erro ao atualizar estado do evento:', error);
                        alert('Erro ao atualizar o estado do evento');
                    }
                });
            }
        });
    });
</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Código existente para o formulário e calendário...
        
        // Adicionar evento de clique para os itens da lista de eventos futuros
        document.querySelectorAll('.evento-item').forEach(item => {
            item.addEventListener('click', function() {
                const eventoId = this.getAttribute('data-id');
                
                // Verificar se o EventManager está disponível
                if (window.eventManager) {
                    window.eventManager.carregarDetalhesEvento(eventoId);
                } else {
                    // Alternativa: buscar dados do evento diretamente
                    $.ajax({
                        url: 'buscar_detalhes_evento.php',
                        method: 'POST',
                        data: { evento_id: eventoId },
                        dataType: 'json',
                        success: function(response) {
                            if (response.erro) {
                                console.error('Erro:', response.erro);
                                return;
                            }
                            
                            // Usar o modal existente
                            const modal = document.getElementById('modalEvento');
                            
                            // Preencher os campos do modal com os dados do evento
                            modal.querySelector('.modal-especifico-titulo-input').value = response.titulo;
                            
                            // Atualizar badges de status
                            const badgeRealizado = modal.querySelector('.status-badge.realizado');
                            const badgePendente = modal.querySelector('.status-badge.pendente');
                            
                            if (response.realizado === 't') {
                                badgeRealizado.style.display = 'inline-block';
                                badgePendente.style.display = 'none';
                            } else {
                                badgeRealizado.style.display = 'none';
                                badgePendente.style.display = 'inline-block';
                            }
                            
                            // Atualizar data
                            const dataInput = modal.querySelector('.modal-especifico-data');
                            dataInput.value = response.data_inicio.split(' ')[0]; // Formato YYYY-MM-DD
                            
                            // Atualizar detalhes
                            modal.querySelector('.modal-especifico-detalhes-input').value = response.detalhes || '';
                            
                            // Atualizar checkbox "Marcar como realizado"
                            const checkbox = document.getElementById('eventoRealizadoCheckbox');
                            checkbox.checked = response.realizado === 't';
                            
                            // Armazenar o ID do evento para uso nas ações
                            modal.dataset.eventoId = response.id;
                            
                            // Exibir o modal
                            modal.style.display = 'flex';
                            
                            // Adicionar eventos de fechamento
                            const closeButton = modal.querySelector('.modal-especifico-close');
                            const overlay = modal.querySelector('.modal-especifico-overlay');
                            
                            closeButton.onclick = function() {
                                modal.style.display = 'none';
                            };
                            
                            overlay.onclick = function() {
                                modal.style.display = 'none';
                            };
                        },
                        error: function(xhr, status, error) {
                            console.error('Erro ao buscar detalhes do evento:', error);
                        }
                    });
                }
            });
        });
    });
</script>

<!-- Estilos -->
<style>
    /* Adicione estes estilos ao seu CSS existente */
    .switch {
        position: relative;
        display: inline-block;
        width: 60px;
        height: 34px;
    }

    .switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #ccc;
        transition: .4s;
    }

    .slider:before {
        position: absolute;
        content: "";
        height: 26px;
        width: 26px;
        left: 4px;
        bottom: 4px;
        background-color: white;
        transition: .4s;
    }

    input:checked + .slider {
        background-color: var(--burgundy);
    }

    input:checked + .slider:before {
        transform: translateX(26px);
    }

    .slider.round {
        border-radius: 34px;
    }

    .slider.round:before {
        border-radius: 50%;
    }

    .modo-edicao-container {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .modo-edicao-texto {
        font-family: 'Quicksand', sans-serif;
        color: var(--dark-ink);
    }

    /* Estilo para campos desabilitados */
    input:disabled,
    textarea:disabled {
        background-color: #f5f5f5;
        cursor: not-allowed;
        opacity: 0.7;
    }

    .modal-agenda {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 9999;
        visibility: hidden;
        opacity: 0;
        transition: opacity 0.3s ease, visibility 0.3s ease;
    }

    .modal-agenda.ativo {
        visibility: visible;
        opacity: 1;
    }
    .dashboard-grid-duasColunas-agenda {
        display: grid;
        grid-template-columns: 6fr 3fr;
        gap: 1.5rem;
        max-width: 1400px;
        margin: 0 auto;
        padding: 1rem;
    }

    /* Modal */
    #modalEvento.modal-especifico {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        justify-content: center;
        align-items: center;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
    }

    .modal-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        backdrop-filter: blur(3px);
    }

    .modal-especifico-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
    }

    .modal-especifico-content {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(0.9);
        background: var(--parchment);
        width: 90%;
        max-width: 800px;
        max-height: 90vh;
        border-radius: 12px;
        padding: 2rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        transition: transform 0.3s ease;
        overflow: hidden;
    }

    .modal-especifico-close {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: var(--dark-bg);
        border: none;
        width: 32px;
        height: 32px;
        border-radius: 50%;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .modal-especifico-close:hover {
        transform: rotate(90deg);
        background: #aa0000;
    }

    .modal-especifico-titulo {
        font-family: 'Cinzel', serif;
        color: var(--dark-bg);
        margin-bottom: 1.5rem;
        font-size: 1.5rem;
        background: rgba(0, 0, 0, 0.1);
        border-bottom: 2px solid var(--vintage-gold);
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0.8rem 0;
        border-radius: 4px;
    }

    /* Badges de Status */
    .modal-especifico-badge {
        padding: 5px 10px;
        border-radius: 4px;
        display: inline-block;
        margin: 5px;
    }

    .modal-especifico-badge.realizado {
        background-color: green;
        color: white;
    }

    .modal-especifico-badge.pendente {
        background-color: red;
        color: white;
    }

    /* Calendário */
    .fc {
        min-height: 700px !important;
        font-size: 1.1rem !important;
    }

    .fc-daygrid-day {
        min-height: 120px !important;
    }

    .fc-col-header-cell {
        padding: 10px !important;
        font-size: 1.2rem !important;
    }

    /* Cores dos Eventos */
    .fc-event[data-tipo="Faculdade"] {
        background-color: #1976D2 !important;
        color: white !important;
    }

    .fc-event[data-tipo="Trabalho"] {
        background-color: gray !important;
        color: white !important;
    }

    .fc-event[data-tipo="Concurso"] {
        background-color: blue !important;
        color: white !important;
    }

    .fc-event[data-tipo="Pessoal"] {
        background-color: #00796B !important;
        color: white !important;
    }

    .fc-event[data-tipo="Planejamento"] {
        background-color: #FFD700 !important;
        color: black !important;
    }

    /* Bordas de Status */
    .fc-event[data-realizado="true"] {
        border: 2px solid #008000 !important;
    }

    .fc-event[data-realizado="false"] {
        border: 2px solid #FF0000 !important;
    }

    /* Legenda */
    .legenda-eventos {
        margin-top: 1.5rem;
        padding: 1rem;
        background: rgba(255, 255, 255, 0.7);
        border: 1px solid var(--vintage-gold);
        border-radius: 4px;
    }

    .legenda-item {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        gap: 1rem;
        margin: 0.5rem 0;
    }

    .legenda-cor {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        display: inline-block;
        margin-right: 5px;
        border: 1px solid rgba(0, 0, 0, 0.1);
    }

    .legenda-texto {
        font-family: 'Quicksand', sans-serif;
        color: var(--dark-ink);
        margin-right: 15px;
    }

    .legenda-pendente {
        background-color: white;
        border: 2px solid #FF0000;
    }

    .legenda-realizado {
        background-color: white;
        border: 2px solid #008000;
    }

    /* Responsividade */
    @media (max-width: 1200px) {
        .dashboard-grid-duasColunas-agenda {
            grid-template-columns: 1fr;
        }

        #calendario_agenda {
            max-width: 95% !important;
        }
    }

    @media (max-width: 768px) {
        .fc {
            font-size: 1rem !important;
        }

        .fc-col-header-cell {
            font-size: 1.1rem !important;
        }

        .modal-especifico-content {
            width: 90%;
            margin: 20px;
        }
    }

    /* Formulário */
    .space-y-2 > * + * {
        margin-top: 0.5rem;
    }

    .space-y-4 > * + * {
        margin-top: 1rem;
    }
 /*
    input[type="text"],
    input[type="date"],
    select,
    textarea {
        width: 100%;
        padding: 0.75rem 1rem;
       
        background: rgba(255, 255, 255, 0.7);
        font-family: 'Quicksand', sans-serif;
        font-size: 1rem;
        border-radius: 4px;
        transition: all 0.3s ease;
    }
         */

    input:focus,
    select:focus,
    textarea:focus {
        outline: none;
        border-color: var(--burgundy);
        box-shadow: 0 0 0 2px rgba(128, 0, 32, 0.1);
    }

    .button-group {
        display: flex;
        gap: 1rem;
        margin-top: 2rem;
    }

    .btn-elegant {
        font-family: 'Cinzel', serif;
        background: transparent;
        border: 2px solid var(--vintage-gold);
        color: var(--dark-ink);
        padding: 1rem 2rem;
        font-size: 1.1rem;
        text-transform: uppercase;
        letter-spacing: 2px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        z-index: 1;
        flex: 1;
        transition: all 0.4s ease;
        border-radius: 4px;
    }

    .btn-elegant:hover {
        background: var(--vintage-gold);
        color: white;
    }

    /* Estilos para a lista de eventos futuros */
    .eventos-futuros-container {
        max-height: 400px;
        overflow-y: auto;
        padding: 10px;
    }
    
    .lista-eventos {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    
    .evento-item {
        display: flex;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        overflow: hidden;
        transition: transform 0.2s ease;
        cursor: pointer;
    }
    
    .evento-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .evento-marcador {
        width: 6px;
        min-height: 100%;
    }
    
    .evento-conteudo {
        padding: 12px;
        flex: 1;
    }
    
    .evento-titulo {
        font-weight: bold;
        margin-bottom: 5px;
        color: #333;
    }
    
    .evento-data, .evento-contagem {
        font-size: 0.9rem;
        color: #666;
        display: flex;
        align-items: center;
        gap: 5px;
    }
    
    .evento-contagem {
        margin-top: 5px;
        font-weight: 500;
    }
    
    .sem-eventos {
        text-align: center;
        padding: 30px;
        color: #999;
    }
    
    .sem-eventos i {
        font-size: 48px;
        margin-bottom: 10px;
        color: #ddd;
    }
    
    .ml-2 {
        margin-left: 0.5rem;
    }
    
    .mt-4 {
        /*margin-top: 1rem;*/
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
</style>
