<?php

if (!isset($_SESSION['username'])) {
    header('Location: login.php');
    exit;
}

include 'conexao_POST.php';
$id_usuario = $_SESSION['idusuario'];

// Buscar os últimos 30 dias de estudo
function buscarDiasEstudo($conexao, $id_usuario) {
    $query = "
        WITH dias AS (
            SELECT generate_series(
                CURRENT_DATE - INTERVAL '29 days',
                CURRENT_DATE,
                '1 day'::interval
            )::date as data
        )
        SELECT 
            d.data,
            COALESCE(SUM(EXTRACT(EPOCH FROM tempo_liquido)/3600), 0) as horas_estudo,
            CASE WHEN SUM(EXTRACT(EPOCH FROM tempo_liquido)) IS NOT NULL THEN true ELSE false END as estudou
        FROM dias d
        LEFT JOIN (
            SELECT DISTINCT data::date, tempo_liquido
            FROM appEstudo.estudos
            WHERE planejamento_usuario_idusuario = $id_usuario
        ) e ON d.data = e.data
        GROUP BY d.data
        ORDER BY d.data ASC;
    ";

    $resultado = pg_query($conexao, $query);
    return pg_fetch_all($resultado);
}

// ADICIONAR ESSA NOVA FUNÇÃO AQUI
function calcularMaiorSequencia($conexao, $id_usuario) {
    $query = "
        WITH dias_unicos AS (
            SELECT DISTINCT data::date as data_estudo
            FROM appEstudo.estudos
            WHERE planejamento_usuario_idusuario = $id_usuario
            ORDER BY data_estudo
        ),
        sequencias AS (
            SELECT 
                data_estudo,
                CASE 
                    WHEN data_estudo - LAG(data_estudo) OVER (ORDER BY data_estudo) = 1 
                    THEN 0 
                    ELSE 1 
                END as nova_sequencia
            FROM dias_unicos
        ),
        grupos AS (
            SELECT 
                data_estudo,
                SUM(nova_sequencia) OVER (ORDER BY data_estudo) as grupo_sequencia
            FROM sequencias
        ),
        sequencias_agrupadas AS (
            SELECT 
                grupo_sequencia,
                COUNT(*) as tamanho_sequencia,
                MIN(data_estudo) as inicio_sequencia,
                MAX(data_estudo) as fim_sequencia
            FROM grupos
            GROUP BY grupo_sequencia
        )
        SELECT 
            tamanho_sequencia,
            to_char(inicio_sequencia, 'DD/MM/YYYY') as inicio_sequencia,
            to_char(fim_sequencia, 'DD/MM/YYYY') as fim_sequencia
        FROM sequencias_agrupadas
        ORDER BY tamanho_sequencia DESC
        LIMIT 1";

    $resultado = pg_query($conexao, $query);
    if (!$resultado) {
        error_log(pg_last_error($conexao));  // Para debug
        return [
            'tamanho' => 0,
            'periodo' => ''
        ];
    }

    $row = pg_fetch_assoc($resultado);
    if (!$row) {
        return [
            'tamanho' => 0,
            'periodo' => ''
        ];
    }

    return [
        'tamanho' => intval($row['tamanho_sequencia']),
        'periodo' => "de {$row['inicio_sequencia']} até {$row['fim_sequencia']}"
    ];
}


$dias_estudo = buscarDiasEstudo($conexao, $id_usuario);
$hoje_constacia = date('Y-m-d');
$sequencia_atual = 0;
$estudou_hoje = false;
$esta_estudando = false;

// Primeiro verifica se estudou hoje
foreach ($dias_estudo as $dia) {
    if ($dia['data'] == $hoje_constacia && $dia['estudou'] == 't') {
        $estudou_hoje = true;
        break;
    }
}

// Agora calcula a sequência
for ($i = count($dias_estudo) - 1; $i >= 0; $i--) {
    $data_atual = $dias_estudo[$i]['data'];

    // Se for hoje
    if ($data_atual == $hoje_constacia) {
        if ($dias_estudo[$i]['estudou'] == 't') {
            $sequencia_atual++;
        }
        continue; // Pula para o próximo dia independente de ter estudado ou não
    }

    // Para os dias anteriores
    if ($dias_estudo[$i]['estudou'] == 't') {
        $sequencia_atual++;
        $esta_estudando = true; // Indica que há um estudo ativo
    } else {
        break; // Para a contagem se encontrar um dia sem estudo
    }
}

// Mensagens motivacionais humoradas baseadas na sequência
function getMensagemMotivacional($sequencia) {
    $mensagens = [
        0 => [
            "título" => "Modo Preguiça Ativado! 🦥",
            "mensagem" => "Tá mais parado que internet discada! Bora estudar?"
        ],
        1 => [
            "título" => "Primeiro Passo! 🌱",
            "mensagem" => "Como diria Lao Tsé: toda jornada começa com um primeiro passo... e você já deu o seu!"
        ],
        2 => [
            "título" => "Dois é Demais! 🎯",
            "mensagem" => "Igual miojo: 2 minutos pra fazer, 2 dias de dedicação!"
        ],
        3 => [
            "título" => "Hat-trick! ⚽",
            "mensagem" => "3 dias seguidos! Tá jogando mais que Pelé!"
        ],
        4 => [
            "título" => "Quarteto Fantástico! 💫",
            "mensagem" => "4 dias! Tá mais constante que conta de luz!"
        ],
        5 => [
            "título" => "High Five! 🖐️",
            "mensagem" => "5 dias! Tá mais dedicado que formiga em piquenique!"
        ],
        6 => [
            "título" => "Six Pack de Estudos! 💪",
            "mensagem" => "6 dias! Tá mais definido que barriga de crossfiteiro!"
        ],
        7 => [
            "título" => "Semana Completa! 🎉",
            "mensagem" => "7 dias! Nem a Record tem tanta programação seguida!"
        ],
        8 => [
            "título" => "Oito ou 80! 🎱",
            "mensagem" => "8 dias! Tá fazendo speedrun de conhecimento!"
        ],
        9 => [
            "título" => "Nine Lives! 😺",
            "mensagem" => "9 dias! Tá com mais vidas que gato em liquidação!"
        ],
        10 => [
            "título" => "Dezena da Sorte! 🍀",
            "mensagem" => "10 dias! Mais sortudo que ganhar na loteria... e com mais futuro também!"
        ],
        11 => [
            "título" => "Onze Guerreiros! ⚔️",
            "mensagem" => "11 dias! Um time completo de vitórias nos estudos!"
        ],
        12 => [
            "título" => "Dúzia de Sucessos! 🥚",
            "mensagem" => "12 dias! Tá vendendo mais que pão na padaria!"
        ],
        13 => [
            "título" => "Sorte no Estudo! 🎲",
            "mensagem" => "13 dias! Quem disse que 13 é número de azar?"
        ],
        14 => [
            "título" => "Duas Semanas de Poder! 💪",
            "mensagem" => "14 dias! Tá mais firme que promessa de Ano Novo em dezembro!"
        ],
        15 => [
            "título" => "Guerreiro Quinzenal! 🗡️",
            "mensagem" => "15 dias! Tá com mais disciplina que monge shaolin!"
        ],
        16 => [
            "título" => "Sweet Sixteen! 🎂",
            "mensagem" => "16 dias! A festa da constância é sua!"
        ],
        17 => [
            "título" => "Dezessete e Tudo Bem! 🌈",
            "mensagem" => "17 dias! Tá mais constante que série da Netflix!"
        ],
        18 => [
            "título" => "Dezoito Com Estilo! 🎓",
            "mensagem" => "18 dias! Maior de idade na constância!"
        ],
        19 => [
            "título" => "Prime Time! 🌟",
            "mensagem" => "19 dias! Tá mais exclusivo que Amazon Prime!"
        ],
        20 => [
            "título" => "Vingador Nível 20! 🦸‍♂️",
            "mensagem" => "20 dias! Nem o Thanos teria tanto poder assim!"
        ],
        21 => [
            "título" => "Black Jack! 🎰",
            "mensagem" => "21 dias! Apostou alto nos estudos e tá ganhando!"
        ],
        22 => [
            "título" => "Dupla Precisão! 2️⃣2️⃣",
            "mensagem" => "22 dias! Tá mais certeiro que GPS sem bugs!"
        ],
        23 => [
            "título" => "Jordan dos Estudos! 🏀",
            "mensagem" => "23 dias! Fez até o Michael Jordan parecer amador!"
        ],
        24 => [
            "título" => "24 Horas de Glória! ⏰",
            "mensagem" => "24 dias! Tá mais ativo que café extra-forte!"
        ],
        25 => [
            "título" => "Quarter Century! 🎯",
            "mensagem" => "25 dias! 25% do caminho para ser uma lenda!"
        ],
        26 => [
            "título" => "Alfabeto Completado! 📚",
            "mensagem" => "26 dias! Uma letra do alfabeto para cada dia de dedicação!"
        ],
        27 => [
            "título" => "Clube dos 27! 🎸",
            "mensagem" => "27 dias! Você é uma estrela do rock dos estudos!"
        ],
        28 => [
            "título" => "Ciclo Lunar! 🌕",
            "mensagem" => "28 dias! Sua dedicação é mais certa que as fases da lua!"
        ],
        29 => [
            "título" => "Quase Trinta! 📅",
            "mensagem" => "29 dias! Falta pouco para a marca histórica!"
        ],
        30 => [
            "título" => "LENDÁRIO! 👑",
            "mensagem" => "30 DIAS! Chuck Norris pede seu autógrafo!"
        ],
        31 => [
            "título" => "Mês Completo! 📆",
            "mensagem" => "31 dias! Nem os meses mais longos te seguram!"
        ],
        32 => [
            "título" => "Febre de Estudos! 🌡️",
            "mensagem" => "32 dias! Mais quente que água no microondas!"
        ],
        33 => [
            "título" => "Idade de Cristo! 🙏",
            "mensagem" => "33 dias! Abençoado seja seu conhecimento!"
        ],
        34 => [
            "título" => "34 Street! 🏃‍♂️",
            "mensagem" => "34 dias! Correndo atrás do conhecimento sem parar!"
        ],
        35 => [
            "título" => "Metade dos 70! 🎭",
            "mensagem" => "35 dias! Meio caminho andado para a sabedoria total!"
        ],
        36 => [
            "título" => "360 Graus! 🔄",
            "mensagem" => "36 dias! Dando uma volta completa no mundo do conhecimento!"
        ],
        37 => [
            "título" => "Febre de Saber! 🤒",
            "mensagem" => "37 dias! Tá com febre de conhecimento!"
        ],
        38 => [
            "título" => "Trinta e Oito! 🎪",
            "mensagem" => "38 dias! Fazendo mais malabarismo que artista de circo!"
        ],
        39 => [
            "título" => "Na Porta dos 40! 🚪",
            "mensagem" => "39 dias! Quase entrando no clube dos quarentões!"
        ],
        40 => [
            "título" => "Quarentena de Estudos! 😷",
            "mensagem" => "40 dias! Isolado no mundo do conhecimento!"
        ],
        41 => [
            "título" => "Soma e Segue! ➕",
            "mensagem" => "41 dias! Somando conhecimento dia após dia!"
        ],
        42 => [
            "título" => "Resposta Universal! 🌌",
            "mensagem" => "42 dias! A resposta para a vida, o universo e tudo mais!"
        ],
        43 => [
            "título" => "Quarenta e Três! 🎨",
            "mensagem" => "43 dias! Pintando sua história com as cores do sucesso!"
        ],
        44 => [
            "título" => "Duplo 4! 🎲",
            "mensagem" => "44 dias! Double trouble nos estudos!"
        ],
        45 => [
            "título" => "SUPER SAYAJIN! 🔥",
            "mensagem" => "45 dias! Seu poder de estudo está over 9000!"
        ],
        46 => [
            "título" => "Quarenta e Seis! 🎭",
            "mensagem" => "46 dias! Protagonista da própria história!"
        ],
        47 => [
            "título" => "Agent 47! 🕴️",
            "mensagem" => "47 dias! Eliminando a ignorância com precisão!"
        ],
        48 => [
            "título" => "Duplo 24! ⚡",
            "mensagem" => "48 dias! Estudando em dobro, aprendendo em dobro!"
        ],
        49 => [
            "título" => "Sete Semanas! 🎲",
            "mensagem" => "49 dias! 7x7 de pura dedicação!"
        ],
        50 => [
            "título" => "Meio Centenário! 🏆",
            "mensagem" => "50 dias! Metade do caminho para a imortalidade!"
        ],
        51 => [
            "título" => "Área 51! 👽",
            "mensagem" => "51 dias! Seu conhecimento é de outro mundo!"
        ],
        52 => [
            "título" => "Baralho Completo! 🃏",
            "mensagem" => "52 dias! Um deck inteiro de conquistas!"
        ],
        53 => [
            "título" => "Herbie! 🚗",
            "mensagem" => "53 dias! Acelerando rumo ao conhecimento!"
        ],
        54 => [
            "título" => "Carta na Manga! 🎴",
            "mensagem" => "54 dias! Com todas as cartas do sucesso!"
        ],
        55 => [
            "título" => "Duplo Cinco! 🎲",
            "mensagem" => "55 dias! High five duas vezes!"
        ],
        56 => [
            "título" => "Oito Semanas! 📅",
            "mensagem" => "56 dias! Dois meses de pura dedicação!"
        ],
        57 => [
            "título" => "Heinz! 5️⃣7️⃣",
            "mensagem" => "57 dias! Variedades de conhecimento!"
        ],
        58 => [
            "título" => "Velocidade Máxima! 🏎️",
            "mensagem" => "58 dias! Acelerando no caminho do sucesso!"
        ],
        59 => [
            "título" => "Quase 60! ⏰",
            "mensagem" => "59 dias! Contagem regressiva para a glória!"
        ],
        60 => [
            "título" => "MESTRE DOS MESTRES! 🎓",
            "mensagem" => "60 DIAS! Einstein tá pedindo dicas no WhatsApp!"
        ],
        66 => [
            "título" => "Rota 66! 🛣️",
            "mensagem" => "Na estrada do conhecimento!"
        ],
        67 => [
            "título" => "Agente 67! 🕵️",
            "mensagem" => "Investigando cada detalhe do conhecimento!"
        ],
        68 => [
            "título" => "68 Razões! 📝",
            "mensagem" => "68 razões pra continuar estudando!"
        ],
        69 => [
            "título" => "Nice! 😎",
            "mensagem" => "69 dias! Mantenha esse nice streak!"
        ],
        70 => [
            "título" => "Setenta Show! 🎭",
            "mensagem" => "70 dias! Mais show que programa de auditório!"
        ],
        71 => [
            "título" => "71 Degraus! 🪜",
            "mensagem" => "Subindo a escada do sucesso!"
        ],
        72 => [
            "título" => "6 Dúzias! 🥚",
            "mensagem" => "72 dias! Meia grosa de conquistas!"
        ],
        73 => [
            "título" => "73 Graus! 📐",
            "mensagem" => "Mais ângulos que compasso de matemática!"
        ],
        74 => [
            "título" => "74 Páginas! 📖",
            "mensagem" => "Escrevendo sua história de sucesso!"
        ],
        75 => [
            "título" => "75%! 📊",
            "mensagem" => "Três quartos do caminho andado!"
        ],
        76 => [
            "título" => "76 Trombones! 🎺",
            "mensagem" => "Fazendo mais barulho que banda marcial!"
        ],
        77 => [
            "título" => "77 da Sorte! 🍀",
            "mensagem" => "Sorte? Que nada! É dedicação!"
        ],
        78 => [
            "título" => "78 RPM! 💿",
            "mensagem" => "Girando na velocidade do sucesso!"
        ],
        79 => [
            "título" => "79 Steps! 👣",
            "mensagem" => "Cada passo é uma conquista!"
        ],
        80 => [
            "título" => "80 Tesouros! 💎",
            "mensagem" => "80 dias! Mais rico de conhecimento que cofre de banco!"
        ],
        81 => [
            "título" => "9x9! ✖️",
            "mensagem" => "81 dias! Potência máxima nos estudos!"
        ],
        82 => [
            "título" => "82 Elementos! ⚗️",
            "mensagem" => "Mais elementos que tabela periódica!"
        ],
        83 => [
            "título" => "83 Vitórias! 🏆",
            "mensagem" => "Colecionando mais vitórias que série invicta!"
        ],
        84 => [
            "título" => "7x12! 📊",
            "mensagem" => "84 dias! Doze semanas de pura dedicação!"
        ],
        85 => [
            "título" => "85 km/h! 🚀",
            "mensagem" => "Velocidade máxima no caminho do sucesso!"
        ],
        86 => [
            "título" => "86 Circuitos! 💡",
            "mensagem" => "Mais conectado que rede de computadores!"
        ],
        87 => [
            "título" => "87 Melodias! 🎵",
            "mensagem" => "Sua dedicação tá mais harmoniosa que orquestra!"
        ],
        88 => [
            "título" => "Piano Completo! 🎹",
            "mensagem" => "88 dias! Um dia pra cada tecla do piano!"
        ],
        89 => [
            "título" => "89 Conquistas! 🌟",
            "mensagem" => "Brilhando mais que céu estrelado!"
        ],
        90 => [
            "título" => "90 Graus! 📐",
            "mensagem" => "Ângulo reto nos estudos! Perfeição total!"
        ],
        91 => [
            "título" => "91 Capítulos! 📚",
            "mensagem" => "Sua história tá mais envolvente que Netflix!"
        ],
        92 => [
            "título" => "92 Elementos! ⚛️",
            "mensagem" => "Mais raro que urânio na tabela periódica!"
        ],
        93 => [
            "título" => "93 Milhões! ☀️",
            "mensagem" => "Tão brilhante quanto o Sol (que tá a 93M milhas)!"
        ],
        94 => [
            "título" => "94 Gols! ⚽",
            "mensagem" => "Fazendo mais gols que atacante artilheiro!"
        ],
        95 => [
            "título" => "95 Teses! 📜",
            "mensagem" => "Mais revolucionário que Martinho Lutero!"
        ],
        96 => [
            "título" => "8x12! 🎯",
            "mensagem" => "96 dias! Multiplicando sucesso todo dia!"
        ],
        97 => [
            "título" => "97% Loading... ⏳",
            "mensagem" => "Quase lá! Carregando conhecimento máximo!"
        ],
        98 => [
            "título" => "98 Teclas! ⌨️",
            "mensagem" => "Digitando sua história de sucesso!"
        ],
        99 => [
            "título" => "99 Red Balloons! 🎈",
            "mensagem" => "Voando mais alto que balão em festa!"
        ],
        100 => [
            "título" => "💯 CENTENA PERFEITA! 🎇",
            "mensagem" => "100 DIAS! VOCÊ É OFICIALMENTE UMA LENDA DOS ESTUDOS!"
        ]
    ];

    // Encontrar a mensagem mais apropriada
    $ultima_mensagem = $mensagens[0];
    foreach ($mensagens as $dias => $msg) {
        if ($sequencia >= $dias) {
            $ultima_mensagem = $msg;
        } else {
            break;
        }
    }

    return $ultima_mensagem;
}

$mensagem = getMensagemMotivacional($sequencia_atual);

// ADICIONAR ESSA LINHA AQUI, antes do HTML
$maior_sequencia = calcularMaiorSequencia($conexao, $id_usuario);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Indie+Flower&display=swap" rel="stylesheet">
</head>
<body>


<style>
    /* Estilos da maior sequência */
    .maior-sequencia {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px dashed var(--vintage-gold);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        opacity: 0.8;
        transition: opacity 0.3s ease;
    }

    .maior-sequencia:hover {
        opacity: 1;
    }

    .maior-sequencia-numero {
        font-family: 'Quicksand', sans-serif;
        font-size: 0.9rem;
        color: var(--text);
        cursor: help;
    }

    .maior-sequencia-label {
        margin-right: 0.5rem;
        font-size: 0.8rem;
        opacity: 0.8;
    }

    .maior-sequencia-trofeu {
        font-size: 1.2rem;
        animation: bounce 1s infinite alternate;
    }

    /* Container do calendário */
    .calendario-wrapper {
        background: var(--parchment);
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid var(--vintage-gold);
        margin-bottom: 1.5rem;
        box-shadow: inset 0 0 10px rgba(184, 134, 11, 0.1);
        overflow: visible; /* Permite que elementos filhos fiquem visíveis fora do container */
    }

    /* Grid dos dias */

    /* Estilo da barra de rolagem */
    .dias-grid::-webkit-scrollbar {
        height: 6px;
    }

    .dias-grid::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    .dias-grid::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 3px;
    }


    /* Estilos dos estados dos dias */


    .dia-item.dia-atual {
        background: #FFF;
        border: 2px dashed #666;
    }

    /* Mensagem do dia atual */
    .dia-item.dia-atual::after {
        content: "Hoje";
        position: absolute;
        top: -25px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 0.7rem;
        color: var(--text);
        background: var(--parchment);
        padding: 4px 12px; /* Aumentado padding horizontal */
        border-radius: 4px;
        border: 1px solid var(--vintage-gold);
        white-space: nowrap;
        min-width: max-content;
        z-index: 20;
        width: auto; /* Permite que a largura se ajuste ao conteúdo */
        display: flex; /* Garante que o background cubra todo o conteúdo */
        justify-content: center;
        align-items: center;
    }

    /* Ícones para dias estudados/não estudados */
    .dia-item.estudou::before {
        content: '\f00c'; /* ícone de check do Font Awesome */
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        color: white;
        font-size: 0.8rem;
        position: absolute;
    }

    .dia-item.nao-estudou::before {
        content: '\f00d'; /* ícone de X do Font Awesome */
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        color: white;
        font-size: 0.8rem;
        position: absolute;
    }

    .dia-item.dia-atual:hover::after {
        content: "Vamos estudar hoje? 📚";
        width: auto;
        min-width: 130px;
        text-align: center;
        padding: 4px 12px;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    /* Ícones para dias estudados/não estudados */
    .dia-item.estudou::after {
        content: '\f00c';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        color: white;
        font-size: 0.8rem;
    }

    .dia-item.nao-estudou::after {
        content: '\f00d';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        color: white;
        font-size: 0.8rem;
    }

    /* Número do dia */
    .dia-numero {
        font-size: 0.65rem;
        position: absolute;
        top: 1px;
        left: 2px;
        z-index: 5;
    }

    .dia-item.estudou .dia-numero,
    .dia-item.nao-estudou .dia-numero {
        color: rgba(255, 255, 255, 0.8);
    }

    .dia-item.dia-atual .dia-numero {
        color: #666;
    }

    /* Removendo o emoji */
    .dia-emoji {
        display: none;
    }

    /* Mensagem motivacional */
    .mensagem-motivacional {
        text-align: center;
        margin-bottom: 1.5rem;
        padding: 1rem;
        background: var(--parchment);
        border-radius: 8px;
        border: 1px solid var(--vintage-gold);
        position: relative;
        overflow: hidden;
    }

    .mensagem-motivacional::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent, rgba(184, 134, 11, 0.1), transparent);
        animation: shine 2s infinite;
    }

    .titulo-mensagem {
        font-family: 'Varela Round', 'Quicksand', sans-serif;
        font-size: 1.5rem;
        color: var(--text);
        margin-bottom: 0.5rem;
        text-shadow: 1px 1px 0 rgba(184, 134, 11, 0.2);
    }

    .texto-mensagem {
        font-family: 'Quicksand', sans-serif;
        font-size: 1rem;
        color: var(--text);
        opacity: 0.9;
    }

    /* Streak Counter */
    .streak-counter {
        text-align: center;
        background: var(--parchment);
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid var(--vintage-gold);
        position: relative;
        overflow: hidden;
    }

    .streak-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: var(--text);
        font-family: 'Cinzel', serif;
        line-height: 1;
        margin-bottom: 0.5rem;
    }

    .streak-label {
        display: block;
        font-size: 0.9rem;
        color: var(--text);
        opacity: 0.8;
        font-family: 'Quicksand', sans-serif;
    }

    .streak-flames {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 0.5rem;
    }

    .flame {
        font-size: 1.5rem;
        animation: dance 1s infinite alternate;
    }

    /* Constância container */
    .constancia-container {
        width: 100%;
        margin-top: 1.5rem;
        border-top: 1px solid rgba(128, 0, 32, 0.1);
        padding-top: 1.5rem;
    }

    /* Tooltip personalizado */
    .dia-item[title]::after {
        content: attr(title);
        position: absolute;
        bottom: 120%;
        left: 50%;
        transform: translateX(-50%);
        padding: 0.3rem 0.6rem;
        background: var(--text);
        color: white;
        font-size: 0.7rem;
        border-radius: 4px;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
        z-index: 10;
        font-family: 'Quicksand', sans-serif;
    }

    .dia-item[title]:hover::after {
        opacity: 1;
        visibility: visible;
        bottom: 130%;
    }

    /* Animações */
    @keyframes bounce {
        from { transform: scale(1); }
        to { transform: scale(1.2); }
    }

    @keyframes dance {
        from { transform: translateY(0); }
        to { transform: translateY(-3px); }
    }

    @keyframes shine {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    /* Media Queries */
    @media (max-width: 768px) {
        .dia-item {
            flex: 0 0 30px;
            height: 30px;
        }

        .titulo-mensagem {
            font-size: 1.1rem;
        }

        .texto-mensagem {
            font-size: 0.9rem;
        }
    }
</style>

</body>
</html>