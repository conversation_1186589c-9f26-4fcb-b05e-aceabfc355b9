<?php
function mostrarMensagens() {
    if (isset($_SESSION['erro'])) {
        echo "<div class='mensagem erro'>";
        echo "<i class='fas fa-exclamation-circle'></i>";
        echo "<span>{$_SESSION['erro']}</span>";
        echo "</div>";
        unset($_SESSION['erro']);
    }

    if (isset($_SESSION['sucesso'])) {
        echo "<div class='mensagem sucesso'>";
        echo "<i class='fas fa-check-circle'></i>";
        echo "<span>{$_SESSION['sucesso']}</span>";
        echo "</div>";
        unset($_SESSION['sucesso']);
    }
}
?>

<style>
    .mensagem {
        padding: 15px 20px;
        border-radius: 8px;
        margin: 20px 0;
        display: flex;
        align-items: center;
        gap: 10px;
        font-family: 'Old Standard TT', serif;
        animation: slideIn 0.3s ease-out;
    }

    .mensagem i {
        font-size: 1.2rem;
    }

    .mensagem.erro {
        background: rgba(184, 92, 92, 0.1);
        border: 2px solid var(--primary-color);
        color: var(--primary-color);
    }

    .mensagem.sucesso {
        background: rgba(40, 167, 69, 0.1);
        border: 2px solid #28a745;
        color: #28a745;
    }

    @keyframes slideIn {
        from {
            transform: translateY(-20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }
</style>