<?php
/**
 * Endpoint de Recompensas
 * Sistema de Fidelidade da Barbearia
 */

/**
 * Validar formato do ID do cliente
 * Formato esperado: CPF-CLIENTE (ex: 12345678901-CLIENTE)
 */
function validateClientIdRewards($clienteId) {
    return preg_match('/^[0-9]{11}-CLIENTE$/', $clienteId);
}

function handleRewards($method, $action, $id, $input, $db) {
    switch ($method) {
        case 'GET':
            if ($action === 'pending' && !empty($id)) {
                getPendingRewards($id, $db);
            } elseif ($action === 'pending' && $id === 'count') {
                countPendingRewards($db);
            } elseif ($action === 'pending') {
                getAllPendingRewards($db);
            } elseif ($action === 'pending-grouped') {
                getAllPendingRewardsGrouped($db);
            } elseif ($action === 'delivered' && !empty($id)) {
                getDeliveredRewards($id, $db);
            } elseif ($action === 'redeemed' && !empty($id)) {
                getRedeemedRewards($id, $db);
            } elseif ($action === 'stats') {
                getRewardsStats($db);
            } else {
                ApiResponse::error('Ação não especificada', 400);
            }
            break;
            
        case 'POST':
            if ($action === 'pending') {
                getPendingRewardsByPost($input, $db);
            } elseif ($action === 'redeem') {
                redeemSpecialReward($input, $db);
            } else {
                ApiResponse::error('Ação não especificada', 400);
            }
            break;
            
        case 'PUT':
            if ($action === 'deliver') {
                deliverReward($input, $db);
            } else {
                ApiResponse::error('Ação não especificada', 400);
            }
            break;
            
        default:
            ApiResponse::methodNotAllowed();
    }
}

/**
 * Buscar brindes pendentes de um cliente
 * Cliente ID no formato: CPF-CLIENTE
 */
function getPendingRewards($clienteId, $db) {
    // Validar formato do ID do cliente
    if (!validateClientIdRewards($clienteId)) {
        ApiResponse::validation(['cliente_id'], 'ID do cliente deve estar no formato CPF-CLIENTE');
    }

    try {
        $sql = "SELECT bp.*, u.nome as cliente_nome
                FROM brindes_pendentes bp
                JOIN usuarios u ON bp.cliente_id = u.id
                WHERE bp.cliente_id = ?
                ORDER BY bp.data_ganho DESC";

        $stmt = $db->prepare($sql);
        $stmt->execute([$clienteId]);
        $rewards = $stmt->fetchAll();

        ApiResponse::success($rewards);

    } catch (PDOException $e) {
        error_log("Get pending rewards error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar brindes pendentes');
    }
}

/**
 * Buscar brindes pendentes de um cliente via POST
 * Cliente ID no formato: CPF-CLIENTE
 */
function getPendingRewardsByPost($input, $db) {
    if (empty($input['cliente_id'])) {
        ApiResponse::validation(['cliente_id'], 'ID do cliente é obrigatório');
    }

    // Validar formato do ID do cliente
    if (!validateClientIdRewards($input['cliente_id'])) {
        ApiResponse::validation(['cliente_id'], 'ID do cliente deve estar no formato CPF-CLIENTE');
    }

    try {
        $sql = "SELECT
                    bp.id,
                    bp.cliente_id,
                    bp.tipo_brinde,
                    bp.data_ganho,
                    bp.data_criacao,
                    u.nome as cliente_nome
                FROM brindes_pendentes bp
                JOIN usuarios u ON bp.cliente_id = u.id
                WHERE bp.cliente_id = ?
                ORDER BY bp.data_ganho DESC";

        $stmt = $db->prepare($sql);
        $stmt->execute([$input['cliente_id']]);
        $rewards = $stmt->fetchAll();

        ApiResponse::success($rewards);

    } catch (PDOException $e) {
        error_log("Get pending rewards by POST error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar brindes pendentes');
    }
}

/**
 * Buscar todos os brindes pendentes
 */
function getAllPendingRewards($db) {
    try {
        $sql = "SELECT bp.*, u.nome as cliente_nome
                FROM brindes_pendentes bp
                JOIN usuarios u ON bp.cliente_id = u.id
                ORDER BY bp.data_ganho DESC";

        $stmt = $db->prepare($sql);
        $stmt->execute();
        $rewards = $stmt->fetchAll();

        ApiResponse::success($rewards);

    } catch (PDOException $e) {
        error_log("Get all pending rewards error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar brindes pendentes');
    }
}

/**
 * Buscar todos os brindes pendentes agrupados por cliente
 */
function getAllPendingRewardsGrouped($db) {
    try {
        $sql = "SELECT
                    bp.cliente_id,
                    u.nome as cliente_nome,
                    COUNT(*) as total_brindes,
                    GROUP_CONCAT(
                        CONCAT(bp.id, '|', bp.tipo_brinde, '|', bp.data_ganho, '|', bp.data_criacao)
                        ORDER BY bp.data_ganho DESC
                        SEPARATOR ';;'
                    ) as brindes_detalhes
                FROM brindes_pendentes bp
                JOIN usuarios u ON bp.cliente_id = u.id
                GROUP BY bp.cliente_id, u.nome
                ORDER BY total_brindes DESC, u.nome ASC";

        $stmt = $db->prepare($sql);
        $stmt->execute();
        $grouped = $stmt->fetchAll();

        // Processar os dados agrupados
        $result = [];
        foreach ($grouped as $group) {
            $brindes = [];
            if (!empty($group['brindes_detalhes'])) {
                $brindesRaw = explode(';;', $group['brindes_detalhes']);
                foreach ($brindesRaw as $brindeRaw) {
                    $parts = explode('|', $brindeRaw);
                    if (count($parts) >= 4) {
                        $brindes[] = [
                            'id' => (int)$parts[0],
                            'cliente_id' => $group['cliente_id'],
                            'tipo_brinde' => $parts[1],
                            'data_ganho' => $parts[2],
                            'data_criacao' => $parts[3],
                            'cliente_nome' => $group['cliente_nome']
                        ];
                    }
                }
            }

            $result[] = [
                'cliente_id' => $group['cliente_id'],
                'cliente_nome' => $group['cliente_nome'],
                'total_brindes' => (int)$group['total_brindes'],
                'brindes' => $brindes
            ];
        }

        ApiResponse::success($result);

    } catch (PDOException $e) {
        error_log("Get all pending rewards grouped error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar brindes pendentes agrupados');
    }
}

/**
 * Contar brindes pendentes
 */
function countPendingRewards($db) {
    try {
        $sql = "SELECT COUNT(*) as count FROM brindes_pendentes";
        
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();
        
        ApiResponse::success(['count' => (int)$result['count']]);
        
    } catch (PDOException $e) {
        error_log("Count pending rewards error: " . $e->getMessage());
        ApiResponse::error('Erro ao contar brindes pendentes');
    }
}

/**
 * Marcar brinde como entregue
 * Cliente ID no formato: CPF-CLIENTE
 */
function deliverReward($input, $db) {
    if (empty($input['reward_id']) || empty($input['cliente_id'])) {
        ApiResponse::validation(['reward_id', 'cliente_id'], 'ID do brinde e cliente são obrigatórios');
    }

    // Validar formato do ID do cliente
    if (!validateClientIdRewards($input['cliente_id'])) {
        ApiResponse::validation(['cliente_id'], 'ID do cliente deve estar no formato CPF-CLIENTE');
    }

    try {
        $db->beginTransaction();
        
        // Buscar brinde pendente
        $sql = "SELECT * FROM brindes_pendentes WHERE id = ? AND cliente_id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$input['reward_id'], $input['cliente_id']]);
        $brinde = $stmt->fetch();
        
        if (!$brinde) {
            $db->rollBack();
            ApiResponse::notFound('Brinde não encontrado');
        }
        
        // Mover para histórico de entregues
        $sql = "INSERT INTO historico_brindes_entregues (cliente_id, tipo_brinde, data_ganho, data_entrega) 
                VALUES (?, ?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $brinde['cliente_id'],
            $brinde['tipo_brinde'],
            $brinde['data_ganho'],
            $input['data_entrega'] ?? date('Y-m-d H:i:s')
        ]);
        
        // Remover da lista de pendentes
        $sql = "DELETE FROM brindes_pendentes WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$input['reward_id']]);
        
        $db->commit();
        
        ApiResponse::success(null, 'Brinde marcado como entregue');
        
    } catch (PDOException $e) {
        $db->rollBack();
        error_log("Deliver reward error: " . $e->getMessage());
        ApiResponse::error('Erro ao entregar brinde');
    }
}

/**
 * Buscar histórico de brindes entregues
 */
function getDeliveredRewards($clienteId, $db) {
    try {
        $sql = "SELECT hbe.*, u.nome as cliente_nome 
                FROM historico_brindes_entregues hbe 
                JOIN usuarios u ON hbe.cliente_id = u.id 
                WHERE hbe.cliente_id = ? 
                ORDER BY hbe.data_entrega DESC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$clienteId]);
        $rewards = $stmt->fetchAll();
        
        ApiResponse::success($rewards);
        
    } catch (PDOException $e) {
        error_log("Get delivered rewards error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar histórico de brindes');
    }
}

/**
 * Resgatar recompensa especial
 * Cliente ID no formato: CPF-CLIENTE
 */
function redeemSpecialReward($input, $db) {
    if (empty($input['cliente_id']) || empty($input['titulo_recompensa'])) {
        ApiResponse::validation(['cliente_id', 'titulo_recompensa'], 'Cliente ID e título da recompensa são obrigatórios');
    }

    // Validar formato do ID do cliente
    if (!validateClientIdRewards($input['cliente_id'])) {
        ApiResponse::validation(['cliente_id'], 'ID do cliente deve estar no formato CPF-CLIENTE');
    }

    try {
        $sql = "INSERT INTO recompensas_resgatadas (cliente_id, titulo_recompensa, data_resgate) 
                VALUES (?, ?, ?)";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $input['cliente_id'],
            $input['titulo_recompensa'],
            $input['data_resgate'] ?? date('Y-m-d H:i:s')
        ]);
        
        ApiResponse::success(['id' => $db->lastInsertId()], 'Recompensa resgatada com sucesso', 201);
        
    } catch (PDOException $e) {
        error_log("Redeem special reward error: " . $e->getMessage());
        ApiResponse::error('Erro ao resgatar recompensa');
    }
}

/**
 * Buscar recompensas resgatadas
 */
function getRedeemedRewards($clienteId, $db) {
    try {
        $sql = "SELECT rr.*, u.nome as cliente_nome 
                FROM recompensas_resgatadas rr 
                JOIN usuarios u ON rr.cliente_id = u.id 
                WHERE rr.cliente_id = ? 
                ORDER BY rr.data_resgate DESC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$clienteId]);
        $rewards = $stmt->fetchAll();
        
        ApiResponse::success($rewards);
        
    } catch (PDOException $e) {
        error_log("Get redeemed rewards error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar recompensas resgatadas');
    }
}

/**
 * Buscar estatísticas de recompensas
 */
function getRewardsStats($db) {
    try {
        $stats = [];
        
        // Brindes pendentes
        $sql = "SELECT COUNT(*) as total FROM brindes_pendentes";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $stats['brindes_pendentes'] = $stmt->fetch()['total'];
        
        // Brindes entregues
        $sql = "SELECT COUNT(*) as total FROM historico_brindes_entregues";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $stats['brindes_entregues'] = $stmt->fetch()['total'];
        
        // Recompensas resgatadas
        $sql = "SELECT COUNT(*) as total FROM recompensas_resgatadas";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $stats['recompensas_resgatadas'] = $stmt->fetch()['total'];
        
        // Brindes por tipo
        $sql = "SELECT tipo_brinde, COUNT(*) as total 
                FROM brindes_pendentes 
                GROUP BY tipo_brinde";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $stats['brindes_por_tipo'] = $stmt->fetchAll();
        
        ApiResponse::success($stats);
        
    } catch (PDOException $e) {
        error_log("Get rewards stats error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar estatísticas de recompensas');
    }
}
?>
