<?php
session_start();
include_once("conexao_POST.php");

if (!$conexao) {
    die("Erro na conexão: " . pg_last_error());
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $id_usuario = $_SESSION['idusuario'];
    $cursos = $_POST['cursos'];

    // Remover cursos antigos do usuário
    $query_remover_cursos = "DELETE FROM appEstudo.usuario_has_curso WHERE usuario_idusuario = $id_usuario";
    pg_query($conexao, $query_remover_cursos);

    // Adicionar cursos selecionados
    if (!empty($cursos)) {
        foreach ($cursos as $curso_id) {
            $query_adicionar_cursos = "INSERT INTO appEstudo.usuario_has_curso (usuario_idusuario, curso_idcurso) 
                                       VALUES ($id_usuario, $curso_id)";
            pg_query($conexao, $query_adicionar_cursos);
        }
    }

    echo "Seleção salva com sucesso!";
    echo "<br><button onclick='window.close()'>Fechar Janela</button>";
}

pg_close($conexao);
// Redirecionar para alguma página de sucesso ou exibir uma mensagem
header("Location: index_0Anterior.php");
exit();
?>
