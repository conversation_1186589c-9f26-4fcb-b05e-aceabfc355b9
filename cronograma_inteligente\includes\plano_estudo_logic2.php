<?php
//plano_estudo_logic.php
require_once 'CacheManager.php';
function debugOutput($label, $data) {
    echo "<div style='background:#f8f9fa;padding:10px;margin:10px;border:1px solid #ddd;'>";
    echo "<strong>$label:</strong><br>";
    echo "<pre>";
    print_r($data);
    echo "</pre>";
    echo "</div>";
}

class PlanoEstudoLogic {
    private $conexao;
    private $usuario_id;
    private $prova;
    private $conteudos;
    private $info_estudo;

    private $dias_estudo = [];

    private $cache; // Adicione esta linha

     
      public function __construct($conexao) {
          $this->conexao = $conexao;
          $this->cache = new CacheManager();
          $this->usuario_id = $this->validarUsuario();
      }
      
      public function getConteudos() {
          $cacheKey = "conteudos:{$this->usuario_id}";
          
          // Tenta obter do cache
          $cached = $this->cache->get($cacheKey);
          if ($cached !== null) {
              return $cached;
          }
          
          // Se não está em cache, executa a query
          $query = "-- Adicionando suporte ao 5º nível
  WITH niveis_do_edital AS (
      SELECT 
          edital_id,
          materia_id,
          MIN(capitulo) as primeiro_capitulo
      FROM 
          appestudo.conteudo_edital
      GROUP BY 
          edital_id, materia_id
  ),
  pesos_calculados AS (
      SELECT 
          m.idmateria,
          COALESCE(pm.peso, 1) * COALESCE(pm.nivel_dificuldade, 1) * 
          CASE 
              WHEN (SELECT data_prova::date - CURRENT_DATE <= 7 
                    FROM appestudo.provas 
                    WHERE usuario_id = $1 AND status = true) 
              THEN 2 
              ELSE 1 
          END as prioridade_calculada
      FROM appestudo.materia m
      LEFT JOIN appestudo.pesos_materias pm ON m.idmateria = pm.materia_id
      LEFT JOIN appestudo.provas p ON pm.prova_id = p.id AND p.status = true
      WHERE p.usuario_id = $1
  ),
  conteudo_base AS (
      SELECT 
          uc.id, 
          m.idmateria,
          m.nome AS materia_nome, 
          m.cor, 
          ce.descricao, 
          ce.capitulo,
          pc.prioridade_calculada,
          
          -- Nível 1 (Principal)
          CASE 
              WHEN strpos(ce.capitulo, '.') > 0 THEN 
                  (SELECT tn.capitulo || '. ' || tn.descricao
                   FROM appestudo.conteudo_edital tn
                   WHERE tn.materia_id = ce.materia_id
                   AND tn.capitulo = split_part(ce.capitulo, '.', 1)
                   LIMIT 1)
              ELSE NULL
          END as descricao_capitulo_principal,
          
          -- Nível 2
          CASE 
              WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 2 THEN 
                  (SELECT cp.capitulo || '. ' || cp.descricao
                   FROM appestudo.conteudo_edital cp
                   WHERE cp.materia_id = ce.materia_id
                   AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || split_part(ce.capitulo, '.', 2)
                   LIMIT 1)
              ELSE NULL
          END as descricao_capitulo_secundario,
          
          -- Nível 3
          CASE 
              WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 3 THEN 
                  (SELECT cp.capitulo || '. ' || cp.descricao
                   FROM appestudo.conteudo_edital cp
                   WHERE cp.materia_id = ce.materia_id
                   AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || 
                       split_part(ce.capitulo, '.', 2) || '.' ||
                       split_part(ce.capitulo, '.', 3)
                   LIMIT 1)
              ELSE NULL
          END as descricao_capitulo_terciario,
          
          -- Nível 4
          CASE 
              WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 4 THEN 
                  (SELECT cp.capitulo || '. ' || cp.descricao
                   FROM appestudo.conteudo_edital cp
                   WHERE cp.materia_id = ce.materia_id
                   AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || 
                       split_part(ce.capitulo, '.', 2) || '.' ||
                       split_part(ce.capitulo, '.', 3) || '.' ||
                       split_part(ce.capitulo, '.', 4)
                   LIMIT 1)
              ELSE NULL
          END as descricao_capitulo_quaternario,
  
          -- Nível 5 (NOVO)
          CASE 
              WHEN array_length(string_to_array(ce.capitulo, '.'), 1) > 5 THEN 
                  (SELECT cp.capitulo || '. ' || cp.descricao
                   FROM appestudo.conteudo_edital cp
                   WHERE cp.materia_id = ce.materia_id
                   AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || 
                       split_part(ce.capitulo, '.', 2) || '.' ||
                       split_part(ce.capitulo, '.', 3) || '.' ||
                       split_part(ce.capitulo, '.', 4) || '.' ||
                       split_part(ce.capitulo, '.', 5)
                   LIMIT 1)
              ELSE NULL
          END as descricao_capitulo_quinario,
          
          uc.status_estudo,
          CAST(split_part(ce.capitulo, '.', 1) AS INTEGER) AS capitulo_principal,
          COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 2), '') AS INTEGER), 0) AS subnivel1,
          COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 3), '') AS INTEGER), 0) AS subnivel2,
          COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 4), '') AS INTEGER), 0) AS subnivel3,
          COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 5), '') AS INTEGER), 0) AS subnivel4,
          ce.ordem,
          ROW_NUMBER() OVER (
              PARTITION BY ce.materia_id 
              ORDER BY 
                  CAST(split_part(ce.capitulo, '.', 1) AS INTEGER),
                  COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 2), '') AS INTEGER), 0),
                  COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 3), '') AS INTEGER), 0),
                  COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 4), '') AS INTEGER), 0),
                  COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 5), '') AS INTEGER), 0)
          ) as ordem_na_materia
      FROM 
          appestudo.usuario_conteudo AS uc
      JOIN 
          appestudo.conteudo_edital AS ce ON uc.conteudo_id = ce.id_conteudo
      JOIN 
          appestudo.materia AS m ON ce.materia_id = m.idmateria
      JOIN
          pesos_calculados pc ON m.idmateria = pc.idmateria
      WHERE 
          uc.usuario_id = $1
          AND uc.status = true
          AND ce.capitulo ~ '^[0-9.]+$'
  )
          -- Adicionar o SELECT principal aqui
  SELECT 
      cb.id,
      cb.idmateria,
      cb.materia_nome,
      cb.cor,
      cb.descricao,
      cb.capitulo,
      cb.descricao_capitulo_principal,
      cb.descricao_capitulo_secundario,
      cb.descricao_capitulo_terciario,
      cb.descricao_capitulo_quaternario,
      cb.descricao_capitulo_quinario,
      cb.status_estudo,
      cb.capitulo_principal,
      cb.subnivel1,
      cb.subnivel2,
      cb.subnivel3,
      cb.subnivel4,
      cb.ordem,
      cb.ordem_na_materia,
      cb.prioridade_calculada * 
      CASE 
          WHEN (SELECT data_prova::date - CURRENT_DATE <= 7 
                FROM appestudo.provas 
                WHERE usuario_id = $1 AND status = true)
          THEN 2 
          ELSE 1 
      END as prioridade_final
  FROM conteudo_base cb
  ORDER BY cb.prioridade_calculada DESC, cb.ordem_na_materia;";
          $result = pg_query_params($this->conexao, $query, array($this->usuario_id));
          
          if (!$result) {
              error_log("Erro na query: " . pg_last_error($this->conexao));
              throw new Exception('Erro ao executar query de conteúdos');
          }
          
          $conteudos = pg_fetch_all($result);
          
          // Salva no cache por 1 hora
          $this->cache->set($cacheKey, $conteudos, 3600);
          
          return $conteudos;
      }
      
      public function getProvaAtiva() {
          $cacheKey = "prova_ativa:{$this->usuario_id}";
          
          $cached = $this->cache->get($cacheKey);
          if ($cached !== null) {
              return $cached;
          }
          
          $query = "SELECT p.*, ap.horas_estudo_dia 
                   FROM appestudo.provas p
                   LEFT JOIN appestudo.ajustes_plano ap ON p.id = ap.prova_id
                   WHERE p.usuario_id = $1 
                   AND p.status = true 
                   ORDER BY p.created_at DESC 
                   LIMIT 1";
          
          $result = pg_query_params($this->conexao, $query, array($this->usuario_id));
          $prova = pg_fetch_assoc($result);
          
          if ($prova) {
              $this->cache->set($cacheKey, $prova, 3600);
          }
          
          return $prova;
      }
      
      public function clearCache() {
          $this->cache->clear();
      }
      
      // Adicione este método para atualizar o cache quando houver mudanças
      private function invalidateCache() {
          $this->cache->delete("conteudos:{$this->usuario_id}");
          $this->cache->delete("prova_ativa:{$this->usuario_id}");
          // Adicione outros caches que precisam ser invalidados
      }
  
    // Adicione estes dois métodos
    private function getFromCache($key) {
        return isset($this->cache[$key]) ? $this->cache[$key] : null;
    }

    private function setCache($key, $value) {
        $this->cache[$key] = $value;
        return $value;
    }

    private function carregarDiasEstudo() {
        $query = "SELECT dia_semana, horas_estudo 
                 FROM appestudo.usuario_dias_estudo 
                 WHERE usuario_id = $1 
                 AND ativo = true 
                 ORDER BY dia_semana";
        $result = pg_query_params($this->conexao, $query, array($this->usuario_id));
        
        $this->dias_estudo = [];
        while ($row = pg_fetch_assoc($result)) {
            $this->dias_estudo[intval($row['dia_semana'])] = floatval($row['horas_estudo']);
        }
        
        if (empty($this->dias_estudo)) {
            for ($i = 1; $i <= 5; $i++) {
                $this->dias_estudo[$i] = 3.0;
            }
        }
    }

    
    public function getDiasEstudo() {
        return array_keys($this->dias_estudo); // Retorna array como [1, 2, 3, ...]
    }

    private function validarUsuario() {
        $usuario_id = filter_var($_SESSION['idusuario'], FILTER_VALIDATE_INT);
        if (!$usuario_id || $usuario_id <= 0) {
            header('Location: /login_index.php');
            exit();
        }
        return $usuario_id;
    }



/**
 * Versão modificada de getConteudos() com query SQL aprimorada
 */




/**
 * Versão otimizada da função getConteudos() para garantir que todos os níveis hierárquicos
 * sejam corretamente processados e incluídos no resultado.
 */


// Função auxiliar para buscar descrições de níveis faltantes
// Função auxiliar para buscar descrições de níveis faltantes
private function buscarDescricaoNivel(&$conteudo, $topico, $campo, $nivel = 1) {
    $query = "SELECT capitulo, descricao 
             FROM appestudo.conteudo_edital 
             WHERE materia_id = $1 
             AND capitulo = $2 
             LIMIT 1";
    
    $result = pg_query_params($this->conexao, $query, array(
        $conteudo['idmateria'],
        $topico
    ));
    
    if ($result && pg_num_rows($result) > 0) {
        $row = pg_fetch_assoc($result);
        $conteudo[$campo] = $row['capitulo'] . '. ' . $row['descricao'];
    } else {
        // Fallback genérico para este nível
        $conteudo[$campo] = $topico . '. Subtópico nível ' . $nivel;
    }
}

    public function getInfoEstudo() {
        // Garante que os conteúdos foram carregados
        if ($this->conteudos === null) {
            $this->getConteudos();
        }
        
        $total_conteudos = count($this->conteudos);
        $calculo = new Calculos($this->prova, $total_conteudos, $this->conexao);
        
        // Pega os conteúdos organizados por semana
        $conteudosPorSemana = $this->organizarConteudosPorSemana();
        
        // Inicializa array para contar cards por dia da semana
        $cards_por_dia = array_fill(0, count($this->dias_estudo), 0);
        
        // Soma os cards de cada dia através de todas as semanas
        foreach ($conteudosPorSemana as $semana) {
            foreach ($semana as $diaIndex => $conteudosDia) {
                if (!empty($conteudosDia)) {  // Verifica se há conteúdos para este dia
                    $cards_por_dia[$diaIndex] += count($conteudosDia);
                }
            }
        }
        
        // Remove índices vazios (dias sem conteúdo)
        $cards_por_dia = array_values(array_filter($cards_por_dia));
        
        return [
            'total_conteudos' => $total_conteudos,
            'dias_ate_prova' => $calculo->getDiasParaProva(),
            'dias_uteis' => $calculo->getDiasUteis(),
            'cards_por_dia' => $cards_por_dia,
            'semanas_necessarias' => $calculo->getTotalSemanas()
        ];
    }

    public function gerarSemanasDatas() {
        $semanas_datas = [];
        $data_inicio = new DateTime($this->prova['data_inicio_estudo']); // 10/02/2025
        $data_prova = new DateTime($this->prova['data_prova']); // 02/03/2025
        
        // Encontra a segunda-feira da primeira semana
        $primeira_segunda = clone $data_inicio;
        while ($primeira_segunda->format('N') > 1) {
            $primeira_segunda->modify('-1 day');
        }
        
        // Se a data de início não for segunda-feira, ajusta para começar da data correta
        $inicio_real = clone $data_inicio;
        
        // Inicializa a data atual para iteração
        $data_atual = clone $primeira_segunda;
        
        while ($data_atual < $data_prova) {
            $fim_semana = clone $data_atual;
            $fim_semana->modify('+6 days'); // Vai até domingo
            
            // Se o fim da semana ultrapassar a data da prova, ajusta
            if ($fim_semana > $data_prova) {
                $fim_semana = clone $data_prova;
            }
            
            // Determina as datas válidas para esta semana
            $datas_validas = [];
            $data_temp = clone $data_atual;
            
            while ($data_temp <= $fim_semana) {
                $dia_semana = (int)$data_temp->format('N');
                
                // Só inclui a data se:
                // 1. É um dia configurado para estudo
                // 2. É depois ou igual à data de início
                // 3. É antes da data da prova
                if (isset($this->dias_estudo[$dia_semana]) && 
                    $data_temp >= $inicio_real && 
                    $data_temp < $data_prova) {
                    $datas_validas[] = clone $data_temp;
                }
                $data_temp->modify('+1 day');
            }
            
            // Só adiciona a semana se tiver datas válidas
            if (!empty($datas_validas)) {
                $semanas_datas[] = [
                    'inicio' => clone $data_atual,
                    'fim' => clone $fim_semana,
                    'datas' => $datas_validas,
                    'primeiro_card' => clone $datas_validas[0],
                    'ultimo_card' => clone $datas_validas[count($datas_validas) - 1]
                ];
            }
            
            // Avança para a próxima semana
            $data_atual->modify('+7 days');
        }
        
        return $semanas_datas;
    }
    
    public function organizarConteudosPorSemana() {
        $total_conteudos = count($this->conteudos);
        $semanas_datas = $this->gerarSemanasDatas();
        $total_semanas = count($semanas_datas);
        
        // Dias de estudo ordenados
        $dias_estudo = array_keys($this->dias_estudo);
        sort($dias_estudo);
        
        $conteudosPorSemana = [];
        $conteudoIndex = 0;
        
        for ($semana = 0; $semana < $total_semanas; $semana++) {
            $semanaAtual = array_fill(0, count($dias_estudo), []);
            
            // Se for a última semana (semana da prova), não distribui conteúdos
            if ($semana === $total_semanas - 1) {
                $conteudosPorSemana[] = $semanaAtual;
                continue;
            }
            
            $datas_validas = $semanas_datas[$semana]['datas'];
            
            // Calcula conteúdos para esta semana
            $conteudos_restantes = $total_conteudos - $conteudoIndex;
            $semanas_restantes = $total_semanas - $semana - 1;
            
            $conteudos_desta_semana = $semanas_restantes > 0 
                ? ceil($conteudos_restantes / $semanas_restantes)
                : $conteudos_restantes;
            
            // Calcula total de horas da semana
            $total_horas_semana = 0;
            foreach ($datas_validas as $data) {
                $dia_semana = (int)$data->format('N');
                $total_horas_semana += $this->dias_estudo[$dia_semana];
            }
            
            // Distribui os conteúdos
            if ($total_horas_semana > 0 && $conteudos_desta_semana > 0) {
                foreach ($datas_validas as $data) {
                    $dia_semana = (int)$data->format('N');
                    $dia_index = array_search($dia_semana, $dias_estudo);
                    
                    if ($dia_index !== false) {
                        $horas_dia = $this->dias_estudo[$dia_semana];
                        $proporcao = $horas_dia / $total_horas_semana;
                        $cards_dia = ceil($conteudos_desta_semana * $proporcao);
                        
                        for ($j = 0; $j < $cards_dia && $conteudoIndex < $total_conteudos; $j++) {
                            $conteudo = $this->conteudos[$conteudoIndex++];
                            // Adiciona a data prevista ao conteúdo
                            $conteudo['data_prevista'] = $data->format('Y-m-d');
                            $semanaAtual[$dia_index][] = $conteudo;
                        }
                    }
                }
            }
            
            $conteudosPorSemana[] = $semanaAtual;
        }
        
        return $conteudosPorSemana;
    }


    public function replanejarEstudo($novaDataInicio) {
        if (!isset($this->prova['id'])) {
            throw new Exception('Prova não encontrada');
        }
    
        try {
            pg_query($this->conexao, 'BEGIN');
    
            // Atualiza a data de início da prova
            $query = "UPDATE appestudo.provas 
                     SET data_inicio_estudo = $1 
                     WHERE id = $2";
            $result = pg_query_params($this->conexao, $query, array(
                $novaDataInicio,
                $this->prova['id']
            ));
    
            if (!$result) {
                throw new Exception('Erro ao atualizar data de início da prova');
            }
    
            // Recalcula as datas para cada conteúdo
            $dataInicio = new DateTime($novaDataInicio);
            $conteudos = $this->getConteudos();
            $info = $this->getInfoEstudo();
            $cardsPorDia = $info['cards_por_dia'];
    
            $dataAtual = clone $dataInicio;
            $conteudoIndex = 0;
    
            foreach ($conteudos as $conteudo) {
                // Pula para o próximo dia útil quando atingir o limite de cards
                if ($conteudoIndex >= $cardsPorDia) {
                    $dataAtual->modify('+1 day');
                    $conteudoIndex = 0;
    
                    // Pula finais de semana
                    while ($dataAtual->format('N') >= 6) {
                        $dataAtual->modify('+1 day');
                    }
                }
    
                // Atualiza a data de estudo do conteúdo
                $query = "UPDATE appestudo.usuario_conteudo 
                         SET data_estudo = $1 
                         WHERE id = $2 AND usuario_id = $3";
                
                $result = pg_query_params($this->conexao, $query, array(
                    $dataAtual->format('Y-m-d'),
                    $conteudo['id'],
                    $this->usuario_id
                ));
    
                if (!$result) {
                    throw new Exception('Erro ao atualizar conteúdo: ' . pg_last_error($this->conexao));
                }
    
                $conteudoIndex++;
            }
    
            pg_query($this->conexao, 'COMMIT');
    
            return [
                'nova_data_inicio' => $novaDataInicio,
                'conteudos_atualizados' => count($conteudos),
                'cards_por_dia' => $cardsPorDia
            ];
    
        } catch (Exception $e) {
            pg_query($this->conexao, 'ROLLBACK');
            throw $e;
        }
    }
    public function getPlanejamentoAtivo() {
        $query = "SELECT p.idplanejamento 
                  FROM appEstudo.planejamento p 
                  WHERE p.usuario_idusuario = $1 
                  ORDER BY p.data_inicio DESC 
                LIMIT 1";
    
        $resultado = pg_query_params($this->conexao, $query, array($this->usuario_id));

        if ($resultado && pg_num_rows($resultado) > 0) {
            $planejamento = pg_fetch_assoc($resultado);
            return $planejamento['idplanejamento'];
        }
    
        return null;
    }

    public function adicionarMateriaPlanejamento($materia_nome) {
        try {
            // Primeiro, pegar o ID da matéria
            $query_materia = "SELECT idmateria FROM appEstudo.materia WHERE nome = $1";
            $result_materia = pg_query_params($this->conexao, $query_materia, array($materia_nome));
            
            if (!$result_materia || pg_num_rows($result_materia) === 0) {
                error_log("Matéria não encontrada: " . $materia_nome);
                return false;
            }
            
            $materia = pg_fetch_assoc($result_materia);
            $id_materia = $materia['idmateria'];
            
            $id_planejamento = $this->getPlanejamentoAtivo();
            if (!$id_planejamento) {
                error_log("Planejamento ativo não encontrado");
                return false;
            }
            
            // Verificar se já existe essa relação
            $query_check = "SELECT 1 FROM appEstudo.planejamento_materia 
                           WHERE planejamento_idplanejamento = $1 
                           AND materia_idmateria = $2";
            
            $result_check = pg_query_params($this->conexao, $query_check, array(
                $id_planejamento,
                $id_materia
            ));
            
            if (pg_num_rows($result_check) > 0) {
                error_log("Matéria já existe no planejamento");
                return true; // Já existe, consideramos sucesso
            }
            
            // Inserir na tabela planejamento_materia
            $query_insert = "INSERT INTO appEstudo.planejamento_materia 
                            (planejamento_idplanejamento, materia_idmateria) 
                            VALUES ($1, $2)";
                            
            $result_insert = pg_query_params($this->conexao, $query_insert, array(
                $id_planejamento,
                $id_materia
            ));
            
            if (!$result_insert) {
                error_log("Erro ao inserir matéria: " . pg_last_error($this->conexao));
                return false;
            }
            
            return true;
        } catch (Exception $e) {
            error_log("Erro ao adicionar matéria: " . $e->getMessage());
            return false;
        }
    }

    public function getQuantidadeDiasEstudo() {
        // Se dias_estudo ainda não foi carregado, carrega
        if (empty($this->dias_estudo)) {
            $this->carregarDiasEstudo();
        }
        
        // Retorna a quantidade de dias no array
        return count($this->dias_estudo);
    }

    // Função atualizada para exibir corretamente os níveis hierárquicos dos tópicos

// Função simplificada para processar os conteúdos
 function processarTopicosHierarquicos($conteudos, $conexao) {
    foreach ($conteudos as &$conteudo) {
      // Pula se for tópico de primeiro nível (sem pontos)
      if (strpos($conteudo['capitulo'], '.') === false) {
          continue;
      }
      
      // Obtém todos os níveis de tópicos para este conteúdo
      $niveis = obterTodosNiveisTopicos($conteudo, $conexao);
      
      // Remove o tópico principal genérico se foi criado
      if (!empty($conteudo['descricao_capitulo_principal']) && 
          (strpos($conteudo['descricao_capitulo_principal'], $conteudo['materia_nome']) !== false ||
           strpos($conteudo['descricao_capitulo_principal'], 'Tópico principal') !== false)) {
          $conteudo['descricao_capitulo_principal'] = null;
      }
      
      // Se existir um nível 1 real no banco, atribuímos
      if (isset($niveis[0])) {
          $conteudo['descricao_capitulo_principal'] = $niveis[0];
      }
      
      // Se existir um nível 2 real (para tópicos de nível 3 ou 4), atribuímos
      if (isset($niveis[1])) {
          $conteudo['descricao_capitulo_secundario'] = $niveis[1];
      }
      
      // Se existir um nível 3 real (para tópicos de nível 4), atribuímos
      if (isset($niveis[2])) {
          $conteudo['descricao_capitulo_terciario'] = $niveis[2];
      }
    }
  
    return $conteudos;
  }

// Adicione esta função ao final da classe PlanoEstudoLogic para processar 
// corretamente todos os níveis hierárquicos

/**
 * Função que completa a hierarquia de tópicos para cada conteúdo
 * Preenche todas as descrições de tópicos pais para cada nível
 */
/**
 * Versão otimizada da função para processar hierarquias completas
 * Evita consultas desnecessárias ao banco e utiliza cache para melhorar performance
 */
public function processarHierarquiasCompletas() {
  // Garantir que os conteúdos foram carregados
  if ($this->conteudos === null) {
      $this->getConteudos();
  }
  
  // Cache para evitar consultas repetidas ao banco
  $cacheCapitulos = [];
  
  // Primeiro passo: pré-carregar todos os capítulos possíveis em uma única consulta
  $materias = [];
  $capitulos = [];
  
  foreach ($this->conteudos as $conteudo) {
      if (!in_array($conteudo['idmateria'], $materias)) {
          $materias[] = $conteudo['idmateria'];
      }
      
      // Se for um subtópico, adiciona todos os capítulos pais possíveis ao array
      if (strpos($conteudo['capitulo'], '.') !== false) {
          $partes = explode('.', $conteudo['capitulo']);
          
          // Nível 1
          if (!in_array($partes[0], $capitulos)) {
              $capitulos[] = $partes[0];
          }
          
          // Nível 2
          if (isset($partes[1]) && !empty($partes[1])) {
              $nivel2 = $partes[0] . '.' . $partes[1];
              if (!in_array($nivel2, $capitulos)) {
                  $capitulos[] = $nivel2;
              }
          }
          
          // Nível 3
          if (isset($partes[2]) && !empty($partes[2])) {
              $nivel3 = $partes[0] . '.' . $partes[1] . '.' . $partes[2];
              if (!in_array($nivel3, $capitulos)) {
                  $capitulos[] = $nivel3;
              }
          }
      }
  }
  
  // Para cada matéria, carrega todos os capítulos possíveis de uma vez
  foreach ($materias as $materiaId) {
      // Prepara a consulta com placeholders para os capítulos
      $placeholders = implode(',', array_fill(0, count($capitulos), '?'));
      
      if (empty($placeholders)) {
          continue; // Pula se não tiver capítulos para esta matéria
      }
      
      $query = "SELECT capitulo, descricao 
               FROM appestudo.conteudo_edital 
               WHERE materia_id = ? 
               AND capitulo IN ($placeholders)";
      
      // Prepara os parâmetros
      $params = array_merge([$materiaId], $capitulos);
      
      // Consulta mais eficiente usando prepared statements
      $stmt = pg_prepare($this->conexao, "query_capitulos_$materiaId", $query);
      $result = pg_execute($this->conexao, "query_capitulos_$materiaId", $params);
      
      // Armazena no cache
      while ($row = pg_fetch_assoc($result)) {
          $cacheKey = $materiaId . '_' . $row['capitulo'];
          $cacheCapitulos[$cacheKey] = $row['descricao'];
      }
  }
  
  // Segundo passo: atribuir os níveis hierárquicos a cada conteúdo usando o cache
  foreach ($this->conteudos as &$conteudo) {
      // Pular tópicos de nível 1 (sem ponto)
      if (strpos($conteudo['capitulo'], '.') === false) {
          continue;
      }
      
      $materiaId = $conteudo['idmateria'];
      $partes = explode('.', $conteudo['capitulo']);
      
      // Verificar nível 1 (ex: "2")
      if (isset($partes[0]) && !empty($partes[0])) {
          $nivel1 = $partes[0];
          $cacheKey = $materiaId . '_' . $nivel1;
          
          if (isset($cacheCapitulos[$cacheKey])) {
              $conteudo['descricao_capitulo_principal'] = $nivel1 . '. ' . $cacheCapitulos[$cacheKey];
          }
      }
      
      // Verificar nível 2 (ex: "2.2")
      if (isset($partes[0]) && isset($partes[1]) && !empty($partes[1])) {
          $nivel2 = $partes[0] . '.' . $partes[1];
          $cacheKey = $materiaId . '_' . $nivel2;
          
          if (isset($cacheCapitulos[$cacheKey])) {
              $conteudo['descricao_capitulo_secundario'] = $nivel2 . '. ' . $cacheCapitulos[$cacheKey];
          }
      }
      
      // Verificar nível 3 (ex: "2.2.3")
      if (isset($partes[0]) && isset($partes[1]) && isset($partes[2]) && !empty($partes[2])) {
          $nivel3 = $partes[0] . '.' . $partes[1] . '.' . $partes[2];
          
          // Não adicionar se for o próprio tópico atual
          if ($nivel3 != $conteudo['capitulo']) {
              $cacheKey = $materiaId . '_' . $nivel3;
              
              if (isset($cacheCapitulos[$cacheKey])) {
                  $conteudo['descricao_capitulo_terciario'] = $nivel3 . '. ' . $cacheCapitulos[$cacheKey];
              }
          }
      }
  }
  
  return $this->conteudos;
}

/**
 * Versão ultra simplificada com foco na eficiência
 * Esta versão sacrifica algumas funcionalidades para garantir que sempre termine em tempo hábil
 */
/**
 * Versão ultra simplificada com foco na eficiência
 * Esta versão não usa consultas SQL complexas para evitar erros
 */
/**
 * Versão corrigida que garante a exibição correta de todos os níveis
 */
public function processarHierarquiasRapido() {
  if ($this->conteudos === null) {
      $this->getConteudos();
  }
  
  // Nome único para o prepared statement
  $stmtName = "get_descricao_" . uniqid();
  
  // Prepara o statement
  $prepareResult = pg_prepare($this->conexao, $stmtName, 
      "SELECT descricao 
       FROM appestudo.conteudo_edital 
       WHERE materia_id = $1 
       AND capitulo = $2 
       LIMIT 1");
       
  if (!$prepareResult) {
      error_log("Erro ao preparar statement: " . pg_last_error($this->conexao));
      throw new Exception('Erro ao preparar consulta');
  }
  
  foreach ($this->conteudos as &$conteudo) {
      if (strpos($conteudo['capitulo'], '.') === false) {
          continue;
      }
      
      $partes = explode('.', $conteudo['capitulo']);
      $this->processaNivel($conteudo, $partes, $stmtName);
  }
  
  return $this->conteudos;
}

private function processaNivel(&$conteudo, $partes, $stmtName) {
  $materiaId = $conteudo['idmateria'];
  
  for ($i = 1; $i <= count($partes); $i++) {
      $nivelAtual = implode('.', array_slice($partes, 0, $i));
      
      if ($nivelAtual === $conteudo['capitulo']) {
          continue;
      }
      
      $result = pg_execute($this->conexao, $stmtName, array(
          $materiaId,
          $nivelAtual
      ));
      
      if ($result && pg_num_rows($result) > 0) {
          $row = pg_fetch_assoc($result);
          $campo = $this->getNivelCampo($i);
          if ($campo) {
              $conteudo[$campo] = $nivelAtual . '. ' . $row['descricao'];
          }
      }
  }
}

private function getNivelCampo($nivel) {
  $campos = [
      1 => 'descricao_capitulo_principal',
      2 => 'descricao_capitulo_secundario',
      3 => 'descricao_capitulo_terciario',
      4 => 'descricao_capitulo_quaternario',
      5 => 'descricao_capitulo_quinario'
  ];
  return $campos[$nivel] ?? null;
}

// Adicione estes dois métodos novos


/**
 * Versão emergencial que não faz nenhuma consulta SQL
 * Útil para quando houver problemas com o banco de dados
 */
public function processarHierarquiasEmergencia() {
  // Garantir que os conteúdos foram carregados
  if ($this->conteudos === null) {
      $this->getConteudos();
  }
  
  // Armazenar todos os capítulos encontrados nos conteúdos
  $capitulos = [];
  
  // Criar um índice dos capítulos existentes
  foreach ($this->conteudos as $conteudo) {
      $materiaId = $conteudo['idmateria'];
      $capitulo = $conteudo['capitulo'];
      $descricao = $conteudo['descricao'];
      
      if (!isset($capitulos[$materiaId])) {
          $capitulos[$materiaId] = [];
      }
      
      $capitulos[$materiaId][$capitulo] = $descricao;
      
      // Remove descrições que contêm a matéria
      if (!empty($conteudo['descricao_capitulo_principal'])) {
          if (strpos($conteudo['descricao_capitulo_principal'], $conteudo['materia_nome']) !== false ||
              strpos($conteudo['descricao_capitulo_principal'], 'Tópico principal') !== false) {
              $conteudo['descricao_capitulo_principal'] = null;
          }
      }
  }
  
  // Processar cada conteúdo
  foreach ($this->conteudos as &$conteudo) {
      // Pular tópicos de nível 1 (sem ponto)
      if (strpos($conteudo['capitulo'], '.') === false) {
          continue;
      }
      
      $materiaId = $conteudo['idmateria'];
      $partes = explode('.', $conteudo['capitulo']);
      
      // Verificar nível 1 (ex: "2")
      if (isset($partes[0]) && !empty($partes[0])) {
          $nivel1 = $partes[0];
          
          if (isset($capitulos[$materiaId][$nivel1])) {
              $conteudo['descricao_capitulo_principal'] = $nivel1 . '. ' . $capitulos[$materiaId][$nivel1];
          }
      }
      
      // Verificar nível 2 (ex: "2.2")
      if (isset($partes[0]) && isset($partes[1]) && !empty($partes[1])) {
          $nivel2 = $partes[0] . '.' . $partes[1];
          
          if (isset($capitulos[$materiaId][$nivel2])) {
              $conteudo['descricao_capitulo_secundario'] = $nivel2 . '. ' . $capitulos[$materiaId][$nivel2];
          }
      }
      
      // Verificar nível 3 (ex: "2.2.3")
      if (isset($partes[0]) && isset($partes[1]) && isset($partes[2]) && !empty($partes[2])) {
          $nivel3 = $partes[0] . '.' . $partes[1] . '.' . $partes[2];
          
          // Não adicionar se for o próprio tópico atual
          if ($nivel3 != $conteudo['capitulo'] && isset($capitulos[$materiaId][$nivel3])) {
              $conteudo['descricao_capitulo_terciario'] = $nivel3 . '. ' . $capitulos[$materiaId][$nivel3];
          }
      }
  }
  
  return $this->conteudos;
}



}