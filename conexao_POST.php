<?php
// Inclui o arquivo com as credenciais
require_once __DIR__ . '/assets/db_credencial.php'; 

// Conexão original com pg_connect (para compatibilidade com código existente)
$conexao = pg_connect("host=$hostname port=$port dbname=$database user=$user password=$password");

if (!$conexao) {
    // Logar o erro para que você (desenvolvedor) possa vê-lo
    error_log("Falha na conexão com o banco de dados (pg_connect): " . pg_last_error());
    // Mostrar uma mensagem amigável para o usuário e parar a execução
    die("Desculpe, estamos enfrentando problemas técnicos (pg_connect). Por favor, tente novamente mais tarde.");
}

// Nova conexão com PDO
$dsn_pdo = "pgsql:host=$hostname;port=$port;dbname=$database";
$options_pdo = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION, // Lança exceções em erros
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,       // Define o modo de fetch padrão para associativo
    PDO::ATTR_EMULATE_PREPARES   => false,                  // Usa prepared statements nativos
];

try {
    $pdo = new PDO($dsn_pdo, $user, $password, $options_pdo);
} catch (PDOException $e) {
    // Logar o erro para que você (desenvolvedor) possa vê-lo
    error_log("Falha na conexão com o banco de dados (PDO): " . $e->getMessage());
    // Mostrar uma mensagem amigável para o usuário e parar a execução
    die("Desculpe, estamos enfrentando problemas técnicos (PDO). Por favor, tente novamente mais tarde.");
}

?>
