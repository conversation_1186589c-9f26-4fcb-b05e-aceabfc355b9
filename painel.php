<?php
session_start();

include 'conexao_POST.php';
include 'painel_funcoes.php';

if (!isset($_SESSION['idusuario']) || $_SESSION['idusuario'] != 1) {
    echo "Acesso negado!!!";
    exit;
}

$mensagens = array();

function adicionarMensagem($texto, $tipo) {
    global $mensagens;
    $mensagens[] = array('texto' => $texto, 'tipo' => $tipo);
}

// Verifica se é uma requisição POST antes de executar as ações
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['cadastrar'])) {
        if (cadastrarUsuario($conexao, $_POST['nome'], $_POST['senha'], $_POST['usuario'], $_POST['email'])) {
            adicionarMensagem('Usuário cadastrado com sucesso!', 'sucesso');
        } else {
            adicionarMensagem('Erro ao cadastrar usuário.', 'erro');
        }
    }

    if (isset($_POST['atualizar'])) {
        $resultado = atualizarUsuario($conexao, $_POST['id'], $_POST['nome'], $_POST['senha'], $_POST['usuario'], $_POST['email']);
        if ($resultado) {
            adicionarMensagem('Usuário atualizado com sucesso!', 'sucesso');
        } else {
            adicionarMensagem('Erro ao atualizar usuário.', 'erro');
        }
    }

    if (isset($_POST['excluir'])) {
        if (excluirUsuario($conexao, $_POST['id_excluir'])) {
            adicionarMensagem('Usuário excluído com sucesso!', 'sucesso');
        } else {
            adicionarMensagem('Erro ao excluir usuário.', 'erro');
        }
    }

    // Armazena mensagens na sessão para exibição
    $_SESSION['mensagens'] = $mensagens;
    $_SESSION['mensagem_exibida'] = false;
    header("Location: painel.php");
    exit;
}

$usuarios = listarUsuarios($conexao);
?>



    <!DOCTYPE html>
<html lang="pt-br">
<head>
    <title>Gerenciamento de Usuários</title>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <!-- Aqui vai todo o CSS que você me enviou primeiro, sem alteração -->
    <style>
        /* Sistema de Mensagens */
        .mensagem-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            animation: slideIn 0.5s ease-out;
        }

        .mensagem {
            padding: 1rem 2rem;
            border-radius: 8px;
            margin-bottom: 10px;
            color: white;
            font-family: 'Quicksand', sans-serif;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            display: flex;
            align-items: center;
            min-width: 300px;
        }

        .mensagem-sucesso {
            background-color: #4CAF50;
            border-left: 4px solid #2E7D32;
        }

        .mensagem-erro {
            background-color: #f44336;
            border-left: 4px solid #c62828;
        }

        .mensagem-icone {
            margin-right: 10px;
            font-weight: bold;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes fadeOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        .mensagem-saindo {
            animation: fadeOut 0.5s ease-in forwards;
        }
        /* Variáveis */
        :root {
            --parchment: #f8f0e3;
            --vintage-gold: #b8860b;
            --burgundy: #800020;
            --gold-accent: #daa520;
            --shadow-color: rgba(0, 0, 0, 0.2);
            --elegant-border: linear-gradient(45deg, var(--vintage-gold), #ffd700, var(--vintage-gold));
        }

        /* Reset e configurações básicas */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background-color: var(--parchment);
            font-family: 'Quicksand', sans-serif;
            min-height: 100vh;
            padding: 2rem;
            position: relative;
        }

        body::before {
            content: "";
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('lei_seca/img/ConcurseiroOff_v1_2.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            opacity: 0.15;
            z-index: -1;
            pointer-events: none;
        }

        /* Header */
        .page-header {
            max-width: 1200px;
            margin: 0 auto 3rem;
            text-align: center;
            position: relative;
            padding-bottom: 2rem;
        }

        .page-header::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 3px;
            background: var(--elegant-border);
        }

        .page-title {
            font-family: 'Cinzel', serif;
            font-size: 2.5rem;
            color: var(--burgundy);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
        }

        /* Container principal */
        .historico-container {
            max-width: 95%;
            margin: 2rem auto;
            background: white;
            padding: 1.5rem;
            border-radius: 12px;
            box-shadow: 0 0 0 1px var(--vintage-gold),
            0 0 0 8px white,
            0 0 0 9px var(--vintage-gold),
            0 4px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .historico-title {
            font-family: 'Cinzel', serif;
            color: var(--burgundy);
            text-align: center;
            font-size: 2rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid var(--vintage-gold);
        }

        /* Container da tabela */
        .tabela-container {
            width: 100%;
            overflow-x: auto;
            margin: 0;
            padding-bottom: 1rem;
        }

        /* Tabela */
        .historico-table {
            min-width: 1200px;
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin: 0;
            font-family: 'Quicksand', sans-serif;
        }

        .historico-table th,
        .historico-table td {
            padding: 0.75rem;
            border: 1px solid var(--vintage-gold);
            text-align: left;
            vertical-align: middle;
        }

        .historico-table th {
            background: var(--vintage-gold);
            color: white;
            font-family: 'Cinzel', serif;
            font-weight: 600;
        }

        /* Larguras das colunas */
        .historico-table th:nth-child(1),
        .historico-table td:nth-child(1) { width: 60px; text-align: center; }

        .historico-table th:nth-child(2),
        .historico-table td:nth-child(2) { width: 60px; text-align: center; }

        .historico-table th:nth-child(3),
        .historico-table td:nth-child(3) { width: 180px; }

        .historico-table th:nth-child(4),
        .historico-table td:nth-child(4) { width: 120px; }

        .historico-table th:nth-child(5),
        .historico-table td:nth-child(5) { width: 180px; }

        .historico-table th:nth-child(6),
        .historico-table td:nth-child(6) { width: 80px; text-align: center; }

        .historico-table th:nth-child(7),
        .historico-table td:nth-child(7) { width: 80px; text-align: center; }

        .historico-table th:nth-child(8),
        .historico-table td:nth-child(8) { width: 120px; text-align: center; }

        .historico-table th:nth-child(9),
        .historico-table td:nth-child(9) { width: 160px; text-align: center; }

        /* Linhas alternadas */
        .historico-table tr:nth-child(even) {
            background: rgba(248, 240, 227, 0.3);
        }

        /* Destaque sem planejamento */
        .sem-planejamento {
            background-color: rgba(255, 0, 0, 0.1) !important;
            color: var(--burgundy) !important;
        }

        .sem-planejamento td {
            border-color: rgba(255, 0, 0, 0.3) !important;
        }

        .historico-table tr.sem-planejamento:nth-child(even) {
            background-color: rgba(255, 0, 0, 0.15) !important;
        }

        .historico-table tr.sem-planejamento:hover {
            background-color: rgba(255, 0, 0, 0.2) !important;
        }

        /* Inputs */
        .edit-input {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--vintage-gold);
            border-radius: 4px;
            font-family: 'Quicksand', sans-serif;
            background-color: white;
            max-width: 300px;
            margin: 0.5rem;
        }

        .edit-input:focus {
            outline: none;
            border-color: var(--burgundy);
            box-shadow: 0 0 3px var(--vintage-gold);
        }

        /* Botões */
        .btn-edit,
        .btn-save,
        .btn-cancel {
            font-family: 'Cinzel', serif;
            padding: 0.5rem 1rem;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 0 0.25rem;
            background: transparent;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-size: 0.8rem;
            border-radius: 4px;
        }

        .btn-edit {
            color: var(--vintage-gold);
            border-color: var(--vintage-gold);
        }

        .btn-edit:hover {
            background: var(--vintage-gold);
            color: white;
        }

        .btn-save {
            color: #2e7d32;
            border-color: #2e7d32;
        }

        .btn-save:hover {
            background: #2e7d32;
            color: white;
        }

        .btn-cancel {
            color: #c62828;
            border-color: #c62828;
        }

        .btn-cancel:hover {
            background: #c62828;
            color: white;
        }

        /* Botões na tabela */
        .historico-table .btn-edit,
        .historico-table .btn-cancel {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
            margin: 0.2rem;
            min-width: 70px;
        }

        /* Mensagens de feedback */
        .mensagem-success {
            background-color: #4CAF50 !important;
            color: white;
        }

        .mensagem-error {
            background-color: #f44336 !important;
            color: white;
        }

        /* Scroll personalizado */
        .tabela-container::-webkit-scrollbar {
            height: 8px;
        }

        .tabela-container::-webkit-scrollbar-track {
            background: var(--parchment);
            border-radius: 4px;
        }

        .tabela-container::-webkit-scrollbar-thumb {
            background: var(--vintage-gold);
            border-radius: 4px;
        }

        .tabela-container::-webkit-scrollbar-thumb:hover {
            background: var(--burgundy);
        }

        /* Responsividade */
        @media (max-width: 1400px) {
            .historico-container {
                max-width: 98%;
                padding: 1rem;
            }
        }

        @media (max-width: 768px) {
            .page-title {
                font-size: 2rem;
            }

            .historico-container {
                padding: 0.5rem;
                margin: 1rem auto;
            }
        }

        @media (max-width: 480px) {
            body {
                padding: 1rem;
            }

            .page-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
<header class="page-header">
    <h1 class="page-title">Gerenciamento de Usuários</h1>
</header>

<!-- Container de Cadastro -->
<div class="historico-container">
    <h2 class="historico-title">Cadastrar Usuário</h2>
    <form method="post" style="text-align: center;">
        <input type="text" name="nome" placeholder="Nome" required class="edit-input" style="max-width: 300px; margin: 0.5rem;">
        <input type="text" name="usuario" placeholder="Usuário" required class="edit-input" style="max-width: 300px; margin: 0.5rem;">
        <input type="text" name="senha" placeholder="Senha" required class="edit-input" style="max-width: 300px; margin: 0.5rem;">
        <input type="email" name="email" placeholder="E-mail" required class="edit-input" style="max-width: 300px; margin: 0.5rem;">
        <br>
        <button type="submit" name="cadastrar" class="btn-edit">Cadastrar</button>
    </form>
</div>

<!-- Container de Listagem -->
<div class="historico-container">
    <h2 class="historico-title">Lista de Usuários</h2>
    <div class="tabela-container">
        <table class="historico-table">
            <thead>
            <tr>
                <th>Nº</th>
                <th>ID</th>
                <th>Nome</th>
                <th>Usuário</th>
                <th>Senha</th>
                <th>E-mail</th>
                <th>ID Plan</th>
                <th>Plan?</th>
                <th>Últ. Estudo</th>
                <th>Ações</th>
            </tr>
            </thead>
            <tbody>
            <?php
            $linha = 1;
            foreach ($usuarios as $usuario): ?>
                <tr <?php if ($usuario['possui_planejamento'] == 'Não') echo 'class="sem-planejamento"'; ?>>
                    <td><?php echo $linha++; ?></td>
                    <td><?php echo htmlspecialchars($usuario['idusuario']); ?></td>
                    <td><?php echo htmlspecialchars($usuario['nome_usuario']); ?></td>
                    <td><?php echo htmlspecialchars($usuario['usuario']); ?></td>
                    <td><?php echo htmlspecialchars($usuario['senha']); ?></td>
                    <td><?php echo htmlspecialchars($usuario['email']); ?></td>
                    <td><?php echo htmlspecialchars($usuario['idplanejamento']); ?></td>
                    <td><?php echo htmlspecialchars($usuario['possui_planejamento']); ?></td>
                    <td><?php echo htmlspecialchars($usuario['data_ultimo_estudo']); ?></td>
                    <td class="acoes-cell">
                        <button onclick="editarUsuario(
                                '<?php echo $usuario['idusuario']; ?>',
                                '<?php echo htmlspecialchars($usuario['nome_usuario'], ENT_QUOTES); ?>',
                                '<?php echo htmlspecialchars($usuario['usuario'], ENT_QUOTES); ?>',
                                '<?php echo htmlspecialchars($usuario['senha'], ENT_QUOTES); ?>',
                                '<?php echo htmlspecialchars($usuario['email'], ENT_QUOTES); ?>'
                                )" class="btn-edit">
                            Editar
                        </button>
                        <form method="post" style="display: inline;">
                            <input type="hidden" name="id_excluir" value="<?php echo $usuario['idusuario']; ?>">
                            <button type="submit" name="excluir" class="btn-cancel">Excluir</button>
                        </form>
                    </td>
                </tr>
            <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- Container de Atualização -->
<div class="historico-container">
    <div class="historico-container" id="atualizar-container">
        <h2 class="historico-title">Atualizar Usuário</h2>
        <form method="post" style="text-align: center;">
        <input type="hidden" name="id" id="id" required>
        <input type="text" name="nome" id="nome" placeholder="Nome" required class="edit-input" style="max-width: 300px; margin: 0.5rem;">
        <input type="text" name="usuario" id="usuario" placeholder="Usuário" required class="edit-input" style="max-width: 300px; margin: 0.5rem;">
        <input type="text" name="senha" id="senha" placeholder="Senha" class="edit-input" style="max-width: 300px; margin: 0.5rem;">
        <input type="email" name="email" id="email" placeholder="E-mail" required class="edit-input" style="max-width: 300px; margin: 0.5rem;">
        <br>
        <button type="submit" name="atualizar" class="btn-save">Atualizar</button>
    </form>
    </div>
</div>

<div id="mensagem-container" class="mensagem-container"></div>

<script>
    // Primeiro script - para edição
    function editarUsuario(id, nome, usuario, senha, email) {
        document.getElementById('id').value = id;
        document.getElementById('nome').value = nome;
        document.getElementById('usuario').value = usuario;
        document.getElementById('senha').value = senha;
        document.getElementById('email').value = email;

        const containerAtualizacao = document.querySelector('.historico-container:last-of-type');
        if (containerAtualizacao) {
            containerAtualizacao.scrollIntoView({
                behavior: 'smooth'
            });
        } else {
            console.error("O container de atualização não foi encontrado.");
        }
    }


    // Segundo script - para mensagens
    document.addEventListener('DOMContentLoaded', function() {
        function mostrarMensagem(texto, tipo) {
            const container = document.getElementById('mensagem-container');
            if (!container) return;

            const mensagem = document.createElement('div');
            mensagem.className = `mensagem mensagem-${tipo}`;

            const icone = document.createElement('span');
            icone.className = 'mensagem-icone';
            icone.innerHTML = tipo === 'sucesso' ? '✓' : '✕';

            const texto_mensagem = document.createElement('span');
            texto_mensagem.textContent = texto;

            mensagem.appendChild(icone);
            mensagem.appendChild(texto_mensagem);
            container.appendChild(mensagem);

            setTimeout(() => {
                mensagem.classList.add('mensagem-saindo');
                setTimeout(() => {
                    mensagem.remove();
                }, 500);
            }, 3000);
        }

        <?php if (isset($_SESSION['mensagens']) && !$_SESSION['mensagem_exibida']): ?>
        <?php foreach ($_SESSION['mensagens'] as $msg): ?>
        mostrarMensagem('<?php echo addslashes($msg['texto']); ?>', '<?php echo $msg['tipo']; ?>');
        <?php endforeach; ?>
        <?php $_SESSION['mensagem_exibida'] = true; ?>
        <?php unset($_SESSION['mensagens']); ?>
        <?php endif; ?>
    });
</script>
</body>
</html>