<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Planeja AQUI - Sua Plataforma de Estudos Inteligente</title>
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Quantico:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #00008B;
            --primary-light: #1a1a9d;
            --secondary: #f8f0e3;
            --accent: #FF6B6B;
            --accent-dark: #FF4949;
            --success: #a645c4;
            --success-dark: #801a9f;
            --border: #e0e0e0;
            --text: #2c3e50;
            --hover: #f5f5f5;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --white: #ffffff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Quicksand', sans-serif;
            color: var(--text);
            line-height: 1.6;
        }

        /* Header/Navegação */
        .header {
            background: var(--primary);
            box-shadow: 0 2px 10px var(--shadow-color);
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            padding: 1rem 0;
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo img {
            height: 50px;
        }

        .nav-buttons {
            display: flex;
            gap: 1rem;
        }

        /* Hero Section */
        .hero {
            background: linear-gradient(135deg, var(--white), #ffffff);
            color: var(--primary);
            padding: 8rem 2rem 4rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .hero-content {
            max-width: 800px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .hero h1 {
            font-family: 'Quantico', serif;
            font-size: 2.8rem;
            margin-bottom: 1.5rem;
        }

        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        /* Estatísticas */
        .stats {
            background: var(--white);
            padding: 4rem 2rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
            text-align: center;
        }

        .stat-item h3 {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 0.5rem;
        }

        /* Recursos */
        .features {
            padding: 4rem 2rem;
            background: var(--hover);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        .feature-card {
            background: var(--white);
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 6px var(--shadow-color);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 1rem;
        }

        /* Botões */
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: var(--success);
            color: var(--white);
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
            background: var(--success-dark);
        }

        .btn-outline {
            background: transparent;
            border: 2px solid var(--white);
        }

        .btn-outline:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .btn-accent {
            background: var(--accent);
        }

        .btn-accent:hover {
            background: var(--accent-dark);
        }

        /* Botões específicos por seção */
        .header .btn {
            background: var(--accent);
        }

        .header .btn:hover {
            background: var(--accent-dark);
        }

        .header .btn-accent {
            background: var(--success);
        }

        .header .btn-accent:hover {
            background: var(--success-dark);
        }

        .hero .btn {
            background: var(--success);
        }

        .hero .btn:hover {
            background: var(--success-dark);
        }

        .cta .btn {
            background: var(--white);
            color: var(--primary);
        }

        .cta .btn:hover {
            background: var(--hover);
        }

        .footer .btn {
            background: var(--accent);
        }

        .footer .btn:hover {
            background: var(--accent-dark);
        }

        /* CTA Section */
        .cta {
            background: var(--primary);
            color: var(--white);
            padding: 4rem 2rem;
            text-align: center;
        }

        .cta-content {
            max-width: 600px;
            margin: 0 auto;
        }

        /* Footer */
        .footer {
            background: var(--white);
            padding: 3rem 2rem;
            text-align: center;
        }

        .contact-info {
            max-width: 600px;
            margin: 0 auto;
        }

        .contact-info a {
            color: var(--primary);
            text-decoration: none;
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2rem;
            }

            .nav-container {
                flex-direction: column;
                gap: 1rem;
            }

            .nav-buttons {
                width: 100%;
                justify-content: center;
            }

            .btn {
                padding: 10px 20px;
                font-size: 0.9rem;
            }
        }
    </style>
    <style>
                .plans {
                    padding: 4rem 2rem;
                    background: var(--white);
                }
                
                .plans-title {
                    text-align: center;
                    margin-bottom: 3rem;
                }
                
                .plans-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                    gap: 2rem;
                    max-width: 1200px;
                    margin: 0 auto;
                }
                
                .plan-card {
                border: 2px solid var(--primary);
                border-radius: 0.5rem;
                padding: 2rem;
                text-align: center;
                position: relative;
                transition: all 0.3s ease;
                cursor: pointer;
                transform: translateY(0);
            }
    
            .plan-card:hover {
                transform: translateY(-10px);
                box-shadow: 0 10px 20px rgba(0, 0, 139, 0.2);
            }
    
            .plan-card.popular {
                background-color: var(--primary);
                color: var(--white);
                border: 2px solid transparent;
            }
    
            .plan-card.popular:hover {
                background-color: #000099;
            }
    
            .plan-card.popular .btn {
                background-color: var(--white);
                color: var(--primary);
            }
    
            .plan-card.popular .btn:hover {
                background-color: #f0f0f0;
            }
    
            /* Efeito de brilho nos cards */
            .plan-card::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
                background-size: 200% 200%;
                animation: shine 3s infinite;
                opacity: 0;
                transition: opacity 0.3s ease;
                pointer-events: none;
            }
    
            .plan-card:hover::after {
                opacity: 1;
            }
                
                .popular-badge {
                    position: absolute;
                    top: -12px;
                    right: 20px;
                    background: var(--success);
                    color: var(--white);
                    padding: 4px 12px;
                    border-radius: 20px;
                    font-size: 0.8rem;
                    font-weight: bold;
                }
                
                .plan-price {
                font-size: 2.5rem;
                font-weight: 700;
                margin: 1rem 0;
                position: relative;
                display: inline-block;
            }
    
            .plan-price::after {
                content: '';
                position: absolute;
                bottom: -5px;
                left: 0;
                width: 100%;
                height: 2px;
                background: currentColor;
                opacity: 0.2;
                font-size: 1rem;
            }
    
                
                .plan-price span {
                    font-size: 1rem;
                    color: currentColor;
                }
                
                .plan-features {
                    list-style: none;
                    margin: 1.5rem 0;
                    text-align: left;
                }
                
                .plan-features li {
                    margin: 0.8rem 0;
                    padding-left: 1.5rem;
                    position: relative;
                }
                
                .plan-features li i {
                    position: absolute;
                    left: 0;
                    top: 5px;
                    color: var(--success);
                }
                
                .save-badge {
                    background: var(--accent);
                    color: var(--white);
                    padding: 4px 12px;
                    border-radius: 20px;
                    font-size: 0.8rem;
                    display: inline-block;
                    margin-bottom: 1rem;
                }
                .hero-logo {
            max-width: 700px;
            height: auto;
            margin-bottom: 2rem;
        }
         /* Responsividade */
         @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }

            .container {
                padding: 0 1rem;
            }
            
            .hero-logo {
                max-width: 200px;
            }

            .back-btn {
                top: 10px;
                left: 10px;
            }
        }
    </style>
</head>
<body>
    <!-- Header/Navegação -->
    <header class="header">
        <div class="nav-container">
            <div class="logo">
                <img src="../logo/logo_vertical.png" alt="Planeja AQUI Logo">
            </div>
            <div class="nav-buttons">
                <a href="login_index.php" class="btn">
                    <i class="fas fa-sign-in-alt"></i> Login
                </a>
                <a href="cadastro.php" class="btn btn-accent">
                    <i class="fas fa-user-plus"></i> Cadastre-se
                </a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <img src="img/cerebro_azul_logo.png" alt="Planeja Aqui Logo" class="hero-logo">
            <h1>Transformando seu Jeito de Estudar</h1>
            <h2>Planeje Mais, Preocupe-se Menos</h2>
            <p><strong>Planeja AQUI</strong> é a plataforma que vai revolucionar sua forma de estudar, com ferramentas inteligentes e personalizadas para seu sucesso.<br>Venha planejar seu Futuro com <strong>Planeja AQUI</strong></p>
            <div style="display: flex; gap: 1rem; justify-content: center;">
                <a href="#planos" class="btn btn-outline">Conheça Nossos Planos</a>
                <a href="#recursos" class="btn btn-outline">Conheça os Recursos</a>
            </div>
        </div>
    </section>

    <!-- Estatísticas 
    <section class="stats">
        <div class="stats-grid">
            <div class="stat-item">
                <h3>10.000+</h3>
                <p>Estudantes Ativos</p>
            </div>
            <div class="stat-item">
                <h3>95%</h3>
                <p>Taxa de Aprovação</p>
            </div>
            <div class="stat-item">
                <h3>4.9/5</h3>
                <p>Avaliação dos Usuários</p>
            </div>
        </div>
    </section>
    -->

    <!-- Recursos -->
    <section id="recursos" class="features">
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-brain"></i>
                </div>
                <h3>Cronograma Inteligente</h3>
                <p>Sistema que se adapta ao seu ritmo e otimiza seu tempo de estudo</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-layer-group"></i>
                </div>
                <h3>Flashcards Inteligentes</h3>
                <p>Memorize de forma eficiente com nosso sistema de repetição espaçada</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                <h3>Agenda Pessoal</h3>
                <p>Organize suas atividades e compromissos com nossa agenda integrada ao cronograma</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h3>Análise de Desempenho</h3>
                <p>Acompanhe seu progresso com métricas detalhadas e insights valiosos</p>
            </div>
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-mobile-alt"></i>
                </div>
                <h3>App Mobile</h3>
                <p>Acesse seus estudos de qualquer lugar, a qualquer momento</p>
            </div>
        </div>
    </section>

<!-- Planos -->
<section id="planos" class="plans">        
    <div class="plans-title">
        <h2>Escolha o Plano Ideal para Você</h2>
        <p style="max-width: 600px; margin: 1rem auto;">
            Comece com 7 dias grátis e transforme sua forma de estudar
        </p>
    </div>
    
    <div class="plans-grid">
        <!-- Plano Mensal -->
        <div class="plan-card">
            <h3>Plano Mensal</h3>
            <div class="plan-price">
                mensal<span>/mês</span>
            </div>
            <p>7 dias de teste grátis</p>
            <ul class="plan-features">
                <li><i class="fas fa-check-circle"></i> Suporte online</li>
                <li><i class="fas fa-check-circle"></i> Acesso à Comunidade</li>
                <li><i class="fas fa-check-circle"></i> Acesso ao Cronograma Inteligente</li>
                <li><i class="fas fa-check-circle"></i> Acesso ao Flashcards Inteligentes</li>
                <li><i class="fas fa-check-circle"></i> Planejamento Personalizado</li>
                <li><i class="fas fa-check-circle"></i> Revisões Programadas</li>
                <li><i class="fas fa-check-circle"></i> Progresso do Estudo</li>
                <li><i class="fas fa-check-circle"></i> App Mobile</li>
            </ul>
            <a href="cadastro.php" class="btn">Começar Teste Grátis</a>
        </div>

        <!-- Plano Anual -->
        <div class="plan-card popular">
            <span class="popular-badge">Mais Vantajoso</span>
            <h3>Plano Anual</h3>
            <div style="margin: 0rem 0;">
                <div class="plan-price">
                    parcelado<span style="font-size: 1rem;"> x12</span>
                </div>
                <div style="margin-top: 1rem; font-size: 1.2rem;">
                    ou <strong>avista</strong> de <strong style="color: var(--success);">anual</strong>
                </div>
            </div>
            <span class="save-badge">Economize 20%</span>
            <ul class="plan-features">
                <li><i class="fas fa-check-circle"></i> Suporte online</li>
                <li><i class="fas fa-check-circle"></i> Acesso à Comunidade</li>
                <li><i class="fas fa-check-circle"></i> Acesso ao Cronograma Inteligente</li>
                <li><i class="fas fa-check-circle"></i> Acesso ao Flashcards Inteligentes</li>
                <li><i class="fas fa-check-circle"></i> Planejamento Personalizado</li>
                <li><i class="fas fa-check-circle"></i> Revisões Programadas</li>
                <li><i class="fas fa-check-circle"></i> Progresso do Estudo</li>
                <li><i class="fas fa-check-circle"></i> App Mobile</li>
                <li><i class="fas fa-check-circle"></i> Economia de 20%</li>
            </ul>
            <a href="cadastro.php" class="btn btn-success">Começar Teste Grátis</a>
        </div>
    </div>
</section>

    <!-- CTA Section -->
    <section class="cta">
        <div class="cta-content">
            <h2>Comece sua Jornada de Sucesso</h2>
            <p>Junte-se a milhares de estudantes que já transformaram sua forma de estudar</p>
            <div style="margin-top: 2rem;">
                <a href="cadastro.php" class="btn">Criar Conta Grátis</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="contact-info">
            <p>
                <i class="fas fa-envelope"></i>
                Contato: <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
            <p style="margin-top: 1rem;">
                <i class="fas fa-bug"></i>
                Encontrou algum erro ou tem sugestões? Entre em contato conosco
            </p>
            <p style="margin-top: 1rem;">
                <a href="#" class="btn btn-accent">
                    <i class="fas fa-mobile-alt"></i> Baixar APP Android
                </a>
            </p>
        </div>
    </footer>

    <script>
        // Smooth scroll para links internos
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Animação do header no scroll
        window.addEventListener('scroll', function() {
            const header = document.querySelector('.header');
            if (window.scrollY > 50) {
                header.style.padding = '0.5rem 0';
            } else {
                header.style.padding = '1rem 0';
            }
        });
        // Adicione isso ao seu <script> existente
const precos = {
    mensal: 12.00,
    anual: 120.00,
    parcelado:10.00,
    desconto: "20%"
};

// Função para formatar o preço em reais
function formatarPreco(valor) {
    return valor.toLocaleString('pt-BR', {
        style: 'currency',
        currency: 'BRL'
    });
}

// Atualizar os preços na página
// Atualizar os preços na página
document.addEventListener('DOMContentLoaded', function() {
    // Atualizar preço mensal
    const precoMensalElement = document.querySelector('.plan-card:first-child .plan-price');
    precoMensalElement.innerHTML = `${formatarPreco(precos.mensal)}<span>/mês</span>`;

    // Atualizar preço anual
    const divPrecos = document.querySelector('.plan-card.popular div[style="margin: 0rem 0;"]');
    if (divPrecos) {
        divPrecos.innerHTML = `
            <div class="plan-price">
                ${formatarPreco(precos.parcelado)}<span style="font-size: 1rem;"> x12</span>
            </div>
            <div style="margin-top: 1rem; font-size: 1.2rem;">
                ou <strong>à vista</strong> por <strong style="color: var(--success);">${formatarPreco(precos.anual)}</strong>
            </div>
        `;
    }

    // Atualizar badge de desconto
    const descontoBadge = document.querySelector('.save-badge');
    if (descontoBadge) {
        descontoBadge.textContent = `Economize ${precos.desconto}`;
    }
});
    </script>
</body>
</html>