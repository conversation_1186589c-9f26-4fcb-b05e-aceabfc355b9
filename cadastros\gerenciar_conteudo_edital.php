<?php
session_start();
require_once("assets/config.php");
require_once('includes/verify_admin.php');

// Verifica se é admin
verificarAcessoAdmin($conexao, false);

// Headers de segurança (similares aos de gerenciar_editais.php)
header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com https://code.jquery.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data:;");
header("X-Frame-Options: DENY");
header("X-Content-Type-Options: nosniff");
header("X-XSS-Protection: 1; mode=block");
header("Referrer-Policy: strict-origin-when-cross-origin");

if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$id_edital = null;
$nome_edital = "Edital não encontrado";
$descricao_edital = ""; // Variável para armazenar a descrição do edital

if (isset($_GET['id_edital']) && is_numeric($_GET['id_edital'])) {
    $id_edital = (int)$_GET['id_edital'];

    // Buscar nome e descrição do edital
    $query_edital = "SELECT nome, descricao FROM appestudo.edital WHERE id_edital = $1";
    $resultado_edital = pg_query_params($conexao, $query_edital, array($id_edital));

    if ($resultado_edital && pg_num_rows($resultado_edital) > 0) {
        $edital_info = pg_fetch_assoc($resultado_edital);
        $nome_edital = $edital_info['nome'];
        $descricao_edital = $edital_info['descricao']; // Armazena a descrição
    } else {
        // Tratar caso o edital não seja encontrado
        // Poderia redirecionar ou mostrar uma mensagem mais elaborada
        $id_edital = null; // Invalida o ID se não encontrado
    }
} else {
    // Tratar ausência ou ID inválido do edital na URL
    // Pode redirecionar para gerenciar_editais.php ou mostrar erro
    echo "ID do edital inválido ou não fornecido.";
    // exit;
}

$conteudos = [];
if ($id_edital) {
    // Buscar conteúdos do edital, ordenados pela matéria e depois pela ordem ou capítulo
    $query_conteudos = "SELECT ce.id_conteudo, ce.materia_id, ce.descricao, ce.capitulo, ce.ordem, m.nome as nome_materia
                        FROM appestudo.conteudo_edital ce
                    JOIN appestudo.materia m ON ce.materia_id = m.idmateria
                    WHERE ce.edital_id = $1
                    ORDER BY m.nome, string_to_array(NULLIF(TRIM(ce.capitulo), ''), '.')::int[], ce.ordem";
    $resultado_conteudos = pg_query_params($conexao, $query_conteudos, array($id_edital));

    if ($resultado_conteudos) {
        $conteudos_raw = pg_fetch_all($resultado_conteudos);
        if ($conteudos_raw === false) { // pg_fetch_all retorna false se não houver linhas
            $conteudos_raw = [];
        }
        
        // Agrupar conteudos por matéria
        $conteudos_agrupados_temp = [];
        foreach ($conteudos_raw as $item) {
            $conteudos_agrupados_temp[$item['nome_materia']][] = $item;
        }
        $conteudos = $conteudos_agrupados_temp; // $conteudos agora é o array agrupado

    } else {
        // Tratar erro na busca de conteúdos, se necessário
        echo "Erro ao buscar conteúdos do edital: " . pg_last_error($conexao);
        $conteudos = []; // Garante que $conteudos seja um array em caso de erro
    }
}

// Buscar todas as matérias para o formulário de novo conteúdo
$query_materias = "SELECT idmateria, nome FROM appestudo.materia ORDER BY nome";
$resultado_materias = pg_query($conexao, $query_materias);
$materias_disponiveis = [];
if ($resultado_materias) {
    $materias_disponiveis = pg_fetch_all($resultado_materias);
    if ($materias_disponiveis === false) {
        $materias_disponiveis = [];
    }
} else {
    // Tratar erro se necessário, mas permite que a página carregue
    echo "<!-- Erro ao buscar matérias: " . pg_last_error($conexao) . " -->";
}

?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conteúdo do Edital: <?php echo htmlspecialchars($nome_edital); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Varela+Round&family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert2/11.7.32/sweetalert2.min.css">
    <link rel="stylesheet" href="assets/css/style.css"> <!-- Supondo que você tenha um CSS compartilhado -->
    <style>
        /* Estilos básicos para esta página - podem ser movidos para style.css */
        body {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            background-color: #f4f7f6;
            color: #333;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }
        .header_global {
            background-color: #00008B; /* Azul escuro */
            color: white;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header_global .logo img {
            height: 40px;
        }
        .container_conteudo {
            max-width: 1000px;
            margin: 30px auto;
            padding: 20px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
        }
        .titulo_pagina {
            font-size: 1.8rem;
            color: #00008B;
            margin-bottom: 20px;
            border-bottom: 2px solid #00008B;
            padding-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .titulo_pagina i {
            margin-right: 10px;
        }
        .btn_voltar {
            display: inline-flex;
            align-items: center;
            padding: 10px 15px;
            background: #6c757d; /* Cinza */
            color: white;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-size: 0.9rem;
            gap: 8px;
            transition: background-color 0.3s ease;
        }
        .btn_voltar:hover {
            background: #5a6268;
        }
        .lista_conteudo {
            list-style: none;
            padding: 0;
        }
        .item_conteudo {
            background-color: #f9f9f9;
            border: 1px solid #eee;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 5px;
        }
        .item_conteudo h3 {
            margin-top: 0;
            font-size: 1.2rem;
            color: #00008B;
        }
        .item_conteudo p {
            font-size: 0.95rem;
            color: #555;
        }
        .nenhum_conteudo {
            text-align: center;
            padding: 20px;
            color: #777;
        }

        /* Estilos essenciais para Modal - garantir que sobreponha */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6); /* Fundo um pouco mais escuro */
            backdrop-filter: blur(4px);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            overflow-y: auto; 
            /* z-index já está sendo aplicado inline nos elementos do modal, mas pode ser definido aqui também */
        }

        .hidden {
            display: none !important; /* !important para garantir que sobrescreva outros displays */
        }

        /* Ajustes no modal-content se necessário, mas o principal é o .modal */
        /* .modal-content já tem estilos inline, mas você pode centralizar aqui */

        /* Estilos para os campos de formulário dentro dos modais para evitar transparência */
        .modal .modal-content .form-input,
        .modal .modal-content select.form-input, /* Mais específico para o select */
        .modal .modal-content textarea.form-input { /* Mais específico para o textarea */
            background-color: #ffffff !important; 
            color: #212529 !important; /* Cor de texto padrão bootstrap para contraste */
            border: 1px solid #ced4da !important; 
        }

        /* Estilo adicional para o placeholder (opcional, mas melhora) */
        .modal .modal-content .form-input::placeholder {
            color: #6c757d;
            opacity: 1; /* Firefox */
        }
        .modal .modal-content .form-input:-ms-input-placeholder { /* Internet Explorer 10-11 */
            color: #6c757d;
        }
        .modal .modal-content .form-input::-ms-input-placeholder { /* Microsoft Edge */
            color: #6c757d;
        }

        /* Estilização Elegante para Modais de Conteúdo */
        .modal .modal-content {
            background: var(--card-background, white); /* Usa variável de tema ou branco */
            border-radius: 12px; /* Bordas mais arredondadas */
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border: 1px solid var(--border, #dee2e6);
        }

        .modal .modal-header {
            padding: 20px 25px;
            border-bottom: 1px solid var(--border, #dee2e6);
            background-color: var(--primary, #00008B); /* Cor primária do tema */
            color: white;
            border-top-left-radius: 11px; /* Ajustar com o border-radius do modal-content */
            border-top-right-radius: 11px;
            display: flex; /* Garante que é um flex container */
            justify-content: space-between; /* Coloca espaço entre o título e o botão */
            align-items: center; /* Alinha verticalmente ao centro */
        }

        .modal .modal-header h2 {
            font-size: 1.4rem; /* Tamanho do título */
            color: white; /* Garante que o título seja branco no header primário */
            margin: 0;
            font-weight: 600;
        }

        .modal .modal-header .close-btn {
            background: none; /* Remove o fundo padrão do botão */
            border: none;     /* Remove a borda padrão do botão */
            color: white;     /* Cor do ícone */
            opacity: 0.75;    /* Levemente translúcido */
            font-size: 1.6rem; /* Tamanho do ícone 'X' */
            font-weight: bold; /* Torna o 'X' um pouco mais grosso */
            padding: 5px;      /* Pequeno padding para área de clique */
            margin: -5px;     /* Compensa o padding para alinhamento visual */
            line-height: 1;   /* Alinha melhor o ícone verticalmente */
            cursor: pointer;
            transition: opacity 0.2s ease-out, transform 0.2s ease-out;
        }
        .modal .modal-header .close-btn:hover {
            opacity: 1;       /* Opacidade total no hover */
            transform: scale(1.1); /* Leve aumento no hover */
        }

        .modal .modal-body {
            padding: 25px 30px;
        }

        .modal .form-label {
            font-weight: 500;
            margin-bottom: 8px;
            color: var(--text, #333);
            font-size: 0.95rem;
        }

        .modal .form-input, 
        .modal select.form-input, 
        .modal textarea.form-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border, #ced4da);
            border-radius: 6px;
            background-color: var(--card-background, #ffffff) !important; 
            color: var(--text, #212529) !important;
            font-size: 1rem;
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }
        .modal .form-input:focus {
            border-color: var(--primary, #00008B);
            box-shadow: 0 0 0 0.2rem rgba(var(--primary-rgb, 0,0,139), 0.25); /* Adapte --primary-rgb */
            outline: none;
        }

        .modal .modal-footer {
            padding: 20px 30px;
            border-top: 1px solid var(--border, #e9ecef);
            display: flex;
            justify-content: flex-end;
            gap: 12px; /* Espaço entre botões */
            background-color: var(--secondary, #f8f9fa); /* Fundo leve para o footer */
            border-bottom-left-radius: 11px;
            border-bottom-right-radius: 11px;
        }

        /* Estilos base para botões no modal, inspirados nas suas classes .btn */
        .modal .modal-footer button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 0.95rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .modal .modal-footer .btn-primary {
            background-color: var(--primary, #00008B);
            color: white;
        }
        .modal .modal-footer .btn-primary:hover {
            background-color: var(--primary-light, #0000CD); /* Um tom mais claro do azul */
            transform: translateY(-1px);
        }

        .modal .modal-footer .btn-secondary {
            background-color: #6c757d; /* Cinza padrão Bootstrap */
            color: white;
        }
        .modal .modal-footer .btn-secondary:hover {
            background-color: #5a6268;
            transform: translateY(-1px);
        }

    </style>
</head>
<body>
    <div class="header_global">
        <div class="logo">
            <img src="../logo/logo_vertical.png" alt="Logo">
        </div>
        <!-- Poderia adicionar o toggle de tema ou outros elementos do header aqui se necessário -->
    </div>

    <div class="container_conteudo">
        <div style="margin-bottom: 20px;">
            <a href="gerenciar_editais.php" class="btn_voltar">
                <i class="fas fa-arrow-left"></i> Voltar para Editais
            </a>
        </div>

        <h1 class="titulo_pagina">
            <i class="fas fa-list-ul"></i> Conteúdo do Edital: <?php echo htmlspecialchars($nome_edital); ?>
        </h1>
        <?php if (!empty($descricao_edital)): ?>
            <p class="descricao_edital_subtitulo" style="margin-top: -10px; margin-bottom: 25px; font-size: 0.9rem; color: #555; padding-left: 5px; border-left: 3px solid var(--primary, #00008B);">
                <?php echo nl2br(htmlspecialchars($descricao_edital)); ?>
            </p>
        <?php endif; ?>

        <?php if (!$id_edital): ?>
            <p class="nenhum_conteudo">Edital não encontrado ou ID inválido.</p>
        <?php else: ?>
            <!-- Área para listar os conteúdos -->
            <div class="conteudo_section">
                <h2>Conteúdos Programáticos</h2>
                <?php if (!empty($conteudos)):
                    $materia_idx = 0;
                    foreach ($conteudos as $nome_materia => $itens_materia):
                        $materia_id_slug = "materia-" . $materia_idx++; // Gera um ID único simples
                ?>
                    <div class="materia_grupo" style="margin-bottom: 15px; border: 1px solid #ddd; border-radius: 5px; overflow: hidden;">
                        <h3 class="materia_titulo_expansivel" 
                            style="font-size: 1.3rem; color: white; background-color: #00008B; padding: 12px 15px; margin: 0; font-weight: 600; cursor: pointer; display: flex; justify-content: space-between; align-items: center;"
                            onclick="toggleConteudo('<?php echo $materia_id_slug; ?>')">
                            <span><i class="fas fa-folder" style="margin-right: 10px;"></i><?php echo htmlspecialchars($nome_materia); ?></span>
                            <i id="icon-<?php echo $materia_id_slug; ?>" class="fas fa-chevron-down" style="transition: transform 0.3s ease;"></i>
                        </h3>
                        <ul id="<?php echo $materia_id_slug; ?>" class="lista_conteudo" style="padding: 15px 15px 15px 25px; list-style-type: none; margin-top: 0; display: none; background-color: #f9f9f9;">
                            <?php if (empty($itens_materia)): ?>
                                <p style="color: #777; font-style: italic;">Nenhum conteúdo específico listado para esta matéria.</p>
                            <?php else:
                                foreach ($itens_materia as $conteudo):
                            ?>
                                <li class="item_conteudo" style="background-color: transparent; border: none; padding: 5px 0; margin-bottom: 10px; line-height: 1.5;">
                                    <?php if (!empty($conteudo['capitulo'])) : ?>
                                        <p style="font-size: 0.95em; color: #333; margin-bottom: 2px;"><strong><?php echo htmlspecialchars($conteudo['capitulo']); ?></strong></p>
                                    <?php endif; ?>
                                    <p style="font-size: 1em; color: #444; margin-bottom: 2px; margin-left: 5px;"><?php echo htmlspecialchars($conteudo['descricao']); ?></p>
                                    <?php if (isset($conteudo['ordem']) && $conteudo['ordem'] !== null) : ?>
                                        <p style="font-size: 0.85em; color: #777; margin-left: 5px;"><small>Ordem: <?php echo htmlspecialchars($conteudo['ordem']); ?></small></p>
                                    <?php endif; ?>
                                    <div class="conteudo_actions" style="margin-top: 5px; margin-left: 5px;">
                                        <?php
                                            $id_conteudo_js = $conteudo['id_conteudo'];
                                            $materia_id_js = $conteudo['materia_id'];
                                            // Usar json_encode para strings garante que sejam seguras para JS
                                            $descricao_js = json_encode((string)$conteudo['descricao'], JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
                                            $capitulo_js = json_encode((string)($conteudo['capitulo'] ?? ''), JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
                                            // Ordem é numérica, mas pode ser passada como string para consistência na função JS se desejado, ou como número.
                                            // Se a função JS espera uma string para ordem, mantenha as aspas. Se espera número, remova.
                                            $ordem_js = json_encode((string)($conteudo['ordem'] ?? '')); // Passando como string via json_encode também

                                            $onclick_attr = "abrirModalEditarConteudo({$id_conteudo_js}, {$materia_id_js}, {$descricao_js}, {$capitulo_js}, {$ordem_js})";
                                        ?>
                                        <button class="btn-editar-conteudo" 
                                                style="background: none; border: none; color: #00008B; cursor: pointer; font-size: 0.85em; padding: 3px 5px;"
                                                onclick="<?php echo htmlspecialchars($onclick_attr, ENT_QUOTES, 'UTF-8'); ?>">
                                            <i class="fas fa-edit"></i> Editar
                                        </button>
                                        <button class="btn-excluir-conteudo"
                                                style="background: none; border: none; color: #dc3545; cursor: pointer; font-size: 0.85em; padding: 3px 5px; margin-left: 8px;"
                                                onclick="confirmarExclusaoConteudo(<?php echo $conteudo['id_conteudo']; ?>, this)">
                                            <i class="fas fa-trash-alt"></i> Excluir
                                        </button>
                                    </div>
                                </li>
                            <?php 
                                endforeach; // Fim do loop de itens_materia
                            endif; // Fim do if empty($itens_materia)
                            ?>
                        </ul>
                    </div>
                <?php 
                    endforeach; // Fim do loop de $conteudos (matérias)
                else: // Caso $conteudos esteja vazio
                ?>
                    <p class="nenhum_conteudo">Nenhum conteúdo cadastrado para este edital.</p>
                <?php endif; ?>
            </div>

            <?php if ($id_edital): // Só mostra o botão de adicionar se o edital for válido ?>
            <div style="margin-top: 30px; text-align: right; padding-top: 20px; border-top: 1px solid #eee;">
                <button onclick="abrirModalNovoConteudo()" class="btn_voltar" style="background-color: #00008B; color: white;">
                    <i class="fas fa-plus-circle"></i> Adicionar Novo Conteúdo
                </button>
            </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>

    <!-- Modal para Adicionar Novo Conteúdo -->
    <div id="modalNovoConteudo" class="modal hidden" style="z-index: 1001;"> <!-- z-index maior se sobrepor modal de resposta -->
        <div class="modal-content" style="max-width: 650px;">
            <div class="modal-header" style="background-color: #00008B; color: white;">
                <h2 style="color: white;"><i class="fas fa-plus-circle"></i> Adicionar Novo Conteúdo ao Edital</h2>
                <button onclick="fecharModalNovoConteudo()" class="close-btn" style="color: white; font-size: 1.6rem;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="formNovoConteudo" class="modal-body">
                <input type="hidden" name="id_edital_fk" value="<?php echo $id_edital; ?>">
                <input type="hidden" name="csrf_token_conteudo" value="<?php echo $_SESSION['csrf_token']; // Usar um token CSRF ?>">
                
                <div class="form-grid" style="gap: 20px;">
                    <div class="form-group">
                        <label for="materia_id" class="form-label">Matéria <span style="color: red;">*</span></label>
                        <select id="materia_id" name="materia_id" class="form-input" required>
                            <option value="">Selecione uma matéria...</option>
                            <?php if (!empty($materias_disponiveis)): ?>
                                <?php foreach ($materias_disponiveis as $materia): ?>
                                    <option value="<?php echo $materia['idmateria']; ?>">
                                        <?php echo htmlspecialchars($materia['nome']); ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <option value="" disabled>Nenhuma matéria cadastrada</option>
                            <?php endif; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="descricao_conteudo" class="form-label">Descrição do Conteúdo <span style="color: red;">*</span></label>
                        <textarea id="descricao_conteudo" name="descricao" class="form-input" rows="4" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="capitulo_conteudo" class="form-label">Capítulo/Tópico (ex: 1.1, 2.A)</label>
                        <input type="text" id="capitulo_conteudo" name="capitulo" class="form-input">
                    </div>

                    <div class="form-group">
                        <label for="ordem_conteudo" class="form-label">Ordem (número para ordenação)</label>
                        <input type="number" id="ordem_conteudo" name="ordem" class="form-input" min="0">
                    </div>
                </div>

                <div class="modal-footer" style="margin-top: 15px;">
                    <button type="button" onclick="fecharModalNovoConteudo()" class="btn-secondary">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button type="submit" class="btn-primary" style="background-color: #00008B;">
                        <i class="fas fa-save"></i> Salvar Conteúdo
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal para Editar Conteúdo -->
    <div id="modalEditarConteudo" class="modal hidden" style="z-index: 1002;"> 
        <div class="modal-content" style="max-width: 650px;">
            <div class="modal-header" style="background-color: #0056b3; color: white;">
                <h2 style="color: white;"><i class="fas fa-edit"></i> Editar Conteúdo do Edital</h2>
                <button onclick="fecharModalEditarConteudo()" class="close-btn" style="color: white; font-size: 1.6rem;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="formEditarConteudo" class="modal-body">
                <input type="hidden" name="id_conteudo_editar" id="id_conteudo_editar">
                <input type="hidden" name="id_edital_fk" value="<?php echo $id_edital; // Mantém o ID do edital pai ?>">
                <input type="hidden" name="csrf_token_conteudo_editar" value="<?php echo $_SESSION['csrf_token']; ?>">
                <input type="hidden" name="acao" value="editar"> <!-- Indica a ação para o backend -->

                <div class="form-grid" style="gap: 20px;">
                    <div class="form-group">
                        <label for="materia_id_editar" class="form-label">Matéria <span style="color: red;">*</span></label>
                        <select id="materia_id_editar" name="materia_id" class="form-input" required>
                            <option value="">Selecione uma matéria...</option>
                            <?php if (!empty($materias_disponiveis)):
                                foreach ($materias_disponiveis as $materia):
                            ?>
                                    <option value="<?php echo $materia['idmateria']; ?>">
                                        <?php echo htmlspecialchars($materia['nome']); ?>
                                    </option>
                            <?php 
                                endforeach;
                            endif; ?>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="descricao_conteudo_editar" class="form-label">Descrição do Conteúdo <span style="color: red;">*</span></label>
                        <textarea id="descricao_conteudo_editar" name="descricao" class="form-input" rows="4" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="capitulo_conteudo_editar" class="form-label">Capítulo/Tópico</label>
                        <input type="text" id="capitulo_conteudo_editar" name="capitulo" class="form-input">
                    </div>

                    <div class="form-group">
                        <label for="ordem_conteudo_editar" class="form-label">Ordem</label>
                        <input type="number" id="ordem_conteudo_editar" name="ordem" class="form-input" min="0">
                    </div>
                </div>

                <div class="modal-footer" style="margin-top: 15px;">
                    <button type="button" onclick="fecharModalEditarConteudo()" class="btn-secondary">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button type="submit" class="btn-primary" style="background-color: #0056b3;">
                        <i class="fas fa-save"></i> Salvar Alterações
                    </button>
                </div>
            </form>
        </div>
    </div>


    <!-- Incluir JS se necessário -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert2/11.7.32/sweetalert2.all.min.js"></script>
    <script>
        function toggleConteudo(materiaId) {
            const ulConteudo = document.getElementById(materiaId);
            const icon = document.getElementById('icon-' + materiaId);

            if (ulConteudo) {
                if (ulConteudo.style.display === 'none' || ulConteudo.style.display === '') {
                    ulConteudo.style.display = 'block'; // Ou 'list-item' se preferir, mas block é comum para ULs
                    if (icon) {
                        icon.style.transform = 'rotate(180deg)';
                    }
                } else {
                    ulConteudo.style.display = 'none';
                    if (icon) {
                        icon.style.transform = 'rotate(0deg)';
                    }
                }
            }
        }

        // Funções para o modal de Novo Conteúdo
        function abrirModalNovoConteudo() {
            document.getElementById('formNovoConteudo').reset(); // Limpa o formulário
            document.getElementById('modalNovoConteudo').classList.remove('hidden');
        }

        function fecharModalNovoConteudo() {
            document.getElementById('modalNovoConteudo').classList.add('hidden');
        }

        // Lidar com o submit do formulário de novo conteúdo
        document.getElementById('formNovoConteudo').addEventListener('submit', async function(event) {
            event.preventDefault();
            const formData = new FormData(this);
            const idEdital = formData.get('id_edital_fk');
            
            // Adicionar explicitamente id_edital se não estiver já no FormData com esse nome exato
            // O input hidden já é id_edital_fk, então processar_conteudo_edital.php precisará ler esse campo.

            console.log("Dados do formulário de novo conteúdo:");
            for (let [key, value] of formData.entries()) {
                console.log(key + ": " + value);
            }

            try {
                const response = await fetch('processar_conteudo_edital.php', {
                    method: 'POST',
                    body: formData
                });
                const responseText = await response.text(); // Ler como texto primeiro
                console.log("Resposta do servidor (processar_conteudo_edital.php):", responseText);
                let result;
                try {
                    result = JSON.parse(responseText); // Tentar parsear como JSON
                } catch (e) {
                    mostrarModalRespostaSimples("Erro", "Resposta inválida do servidor: " + responseText);
                    return;
                }

                if (result.success) {
                    fecharModalNovoConteudo();
                    Swal.fire({
                        title: 'Sucesso!',
                        text: result.message || "Conteúdo adicionado com sucesso!",
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false,
                        willClose: () => {
                            window.location.reload();
                        }
                    });
                } else {
                    Swal.fire({
                        title: 'Erro ao adicionar conteúdo',
                        text: result.message || "Ocorreu um erro desconhecido.",
                        icon: 'error'
                    });
                }
            } catch (error) {
                console.error("Erro ao enviar formulário de adicionar conteúdo:", error);
                Swal.fire(
                    'Erro de Conexão',
                    'Não foi possível conectar ao servidor para adicionar o conteúdo. Verifique sua conexão e tente novamente.',
                    'error'
                );
            }
        });

        // A função mostrarModalRespostaSimples pode ser removida se não for mais usada.
        // Por enquanto, vou deixá-la comentada caso seja útil para outros erros não tratados por Swal.
        /*
        function mostrarModalRespostaSimples(titulo, mensagem) {
            alert(titulo + "\n\n" + mensagem);
        }
        */

        // Funções para o modal de Editar Conteúdo
        function abrirModalEditarConteudo(idConteudo, materiaId, descricao, capitulo, ordem) {
            // Preencher o formulário de edição
            document.getElementById('id_conteudo_editar').value = idConteudo;
            document.getElementById('materia_id_editar').value = materiaId;
            document.getElementById('descricao_conteudo_editar').value = descricao;
            document.getElementById('capitulo_conteudo_editar').value = capitulo;
            document.getElementById('ordem_conteudo_editar').value = ordem;
            
            // Atualizar o token CSRF se ele for gerado por requisição ou mudar dinamicamente
            // Neste caso, estamos usando o mesmo token da sessão carregado na página.
            // document.getElementById('csrf_token_conteudo_editar').value = 'NOVO_TOKEN_SE_NECESSARIO';

            document.getElementById('modalEditarConteudo').classList.remove('hidden');
        }

        function fecharModalEditarConteudo() {
            document.getElementById('modalEditarConteudo').classList.add('hidden');
        }

        // Lidar com o submit do formulário de editar conteúdo
        document.getElementById('formEditarConteudo').addEventListener('submit', async function(event) {
            event.preventDefault();
            const formData = new FormData(this);
            
            console.log("Dados do formulário de edição de conteúdo:");
            for (let [key, value] of formData.entries()) {
                console.log(key + ": " + value);
            }

            try {
                const response = await fetch('processar_conteudo_edital.php', {
                    method: 'POST',
                    body: formData // O campo acao="editar" já está no formData
                });
                const responseText = await response.text(); 
                console.log("Resposta do servidor (editar conteúdo):", responseText);
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (e) {
                    Swal.fire('Erro', 'Resposta inválida do servidor: ' + responseText, 'error');
                    return;
                }

                if (result.success) {
                    fecharModalEditarConteudo();
                    Swal.fire({
                        title: 'Sucesso!',
                        text: result.message || "Conteúdo atualizado com sucesso!",
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false,
                        willClose: () => {
                            window.location.reload();
                        }
                    });
                } else {
                    Swal.fire(
                        'Erro ao atualizar conteúdo',
                        result.message || "Ocorreu um erro desconhecido.",
                        'error'
                    );
                }
            } catch (error) {
                console.error("Erro ao enviar formulário de edição:", error);
                Swal.fire(
                    'Erro de Conexão',
                    'Não foi possível conectar ao servidor para atualizar o conteúdo. Verifique sua conexão e tente novamente.',
                    'error'
                );
            }
        });

        // Funções para Excluir Conteúdo
        function confirmarExclusaoConteudo(idConteudo, botaoElemento) {
            Swal.fire({
                title: 'Tem certeza?',
                text: "Esta ação não poderá ser revertida e o conteúdo será excluído permanentemente!",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Sim, excluir!',
                cancelButtonText: 'Cancelar'
            }).then((result) => {
                if (result.isConfirmed) {
                    procederComExclusaoConteudo(idConteudo, botaoElemento);
                }
            });
        }

        async function procederComExclusaoConteudo(idConteudo, botaoElemento) {
            const formData = new FormData();
            formData.append('id_conteudo_excluir', idConteudo);
            formData.append('acao', 'excluir');
            // Precisamos de um token CSRF para a exclusão também.
            // Vamos assumir que o token da sessão é válido e o mesmo para todas as ações na página.
            // O formulário de edição envia 'csrf_token_conteudo_editar'.
            // O formulário de adição envia 'csrf_token_conteudo'.
            // Para consistência, vamos pegar o valor de um deles se existir, ou direto da sessão se pudermos acessá-lo em JS (o que não podemos diretamente).
            // A maneira mais fácil é ter um input hidden com o token CSRF geral da página e usá-lo.
            // Por agora, vou pegar do formulário de edição (se existir) ou de novo conteúdo.
            let csrfToken = document.querySelector('input[name="csrf_token_conteudo_editar"]');
            if (!csrfToken) {
                csrfToken = document.querySelector('input[name="csrf_token_conteudo"]');
            }
            if (csrfToken && csrfToken.value) {
                formData.append('csrf_token_op', csrfToken.value); // Usar um nome genérico para o backend
            } else {
                // Fallback ou erro se não encontrar token. Idealmente, a página sempre tem um token global.
                // Para este exemplo, se não encontrar, a requisição pode falhar no CSRF do backend.
                // Adicionaremos um campo CSRF genérico no PHP depois.
                console.warn("Token CSRF não encontrado nos formulários para a ação de excluir.");
                // Poderia pegar de um campo hidden global se você adicionar um.
                 formData.append('csrf_token_op', '<?php echo $_SESSION["csrf_token"]; ?>'); // Isso não funciona diretamente em JS puro, precisa ser impresso pelo PHP.
            }

            try {
                const response = await fetch('processar_conteudo_edital.php', {
                    method: 'POST',
                    body: formData
                });
                const responseText = await response.text(); 
                let result;
                try {
                    result = JSON.parse(responseText);
                } catch (e) {
                    Swal.fire('Erro', 'Resposta inválida do servidor: ' + responseText, 'error');
                    return;
                }

                if (result.success) {
                    Swal.fire('Excluído!', result.message || 'Conteúdo excluído com sucesso.', 'success');
                    // Remover o item da lista no DOM
                    const listItem = botaoElemento.closest('li.item_conteudo');
                    if (listItem) {
                        listItem.style.transition = 'opacity 0.5s ease';
                        listItem.style.opacity = '0';
                        setTimeout(() => {
                            listItem.remove();
                            // Verificar se a UL ficou vazia e mostrar mensagem se necessário
                            const ulPai = listItem.parentNode;
                            if (ulPai && ulPai.children.length === 0) {
                                const divMateriaGrupo = ulPai.closest('.materia_grupo');
                                if(divMateriaGrupo){
                                    // Poderia adicionar a mensagem "Nenhum conteúdo..." aqui
                                    // Mas um recarregamento da página é mais simples após várias exclusões
                                }
                            }
                        }, 500);
                    }
                } else {
                    Swal.fire('Erro ao excluir', result.message || 'Ocorreu um erro desconhecido.', 'error');
                }
            } catch (error) {
                console.error("Erro ao excluir conteúdo:", error);
                Swal.fire('Erro de Conexão', 'Não foi possível conectar ao servidor para excluir o conteúdo.', 'error');
            }
        }

    </script>
</body>
</html>
