<?php
function realizarBackup($conexao) {
    try {
        // Definir timezone no início da função
        date_default_timezone_set('America/Sao_Paulo');
        
        // Aumentar limite de tempo e memória
        set_time_limit(300);
        ini_set('memory_limit', '512M');
        
        $data = date('Y-m-d_H-i-s');
        $diretorio_backup = '../backups/';
        
        if (!file_exists($diretorio_backup)) {
            mkdir($diretorio_backup, 0755, true);
        }

        $arquivo_backup = $diretorio_backup . "backup_completo_{$data}.sql";
        
        // Usar pg_dump diretamente em vez de PHP
        $comando = sprintf(
            '"%s" -h %s -p %s -U %s -d %s --clean --if-exists -f "%s"',
            'C:\Program Files\PostgreSQL\17\bin\pg_dump.exe',
            'app_estudo.postgresql.dbaas.com.br',
            '5432',
            'app_estudo',
            'app_estudo',
            $arquivo_backup
        );

        // Criar arquivo temporário com a senha
        $pgpass_temp = tempnam(sys_get_temp_dir(), 'pgpass');
        file_put_contents($pgpass_temp, "app_estudo.postgresql.dbaas.com.br:5432:app_estudo:app_estudo:Lucasb90#");
        putenv("PGPASSFILE=" . $pgpass_temp);

        // Executar comando
        $output = [];
        $return_var = 0;
        exec($comando . " 2>&1", $output, $return_var);

        // Remover arquivo temporário de senha
        unlink($pgpass_temp);

        if ($return_var !== 0) {
            throw new Exception("Erro ao executar pg_dump: " . implode("\n", $output));
        }

        // Compactar o backup
        $zip = new ZipArchive();
        $arquivo_zip = $diretorio_backup . "backup_completo_{$data}.zip";
        
        if ($zip->open($arquivo_zip, ZipArchive::CREATE) === TRUE) {
            $zip->addFile($arquivo_backup, basename($arquivo_backup));
            $zip->close();
            
            // Remove o arquivo SQL original após compactação
            unlink($arquivo_backup);
            
            return [
                'sucesso' => true,
                'arquivo' => $arquivo_zip,
                'data' => $data
            ];
        }

        throw new Exception("Erro ao compactar arquivo de backup");

    } catch (Exception $e) {
        error_log("Erro no backup: " . $e->getMessage());
        return [
            'sucesso' => false,
            'erro' => $e->getMessage()
        ];
    }
}

?>



