<?php
session_start();
include_once "../conexao_POST.php";

if (!isset($_SESSION['idusuario'])) {
    echo '<div style="color:#c00;text-align:center;">Usuário não autenticado.</div>';
    exit();
}

// Consulta SQL para ranking de cursos
$query = "SELECT 
    c.nome as nome_curso,
    c.logo_url as logo_curso,
    COUNT(DISTINCT e.planejamento_usuario_idusuario) as total_usuarios,
    SUM(EXTRACT(EPOCH FROM e.tempo_liquido::time)) / 3600 as horas_totais
FROM appEstudo.estudos e
JOIN appEstudo.curso c ON e.idcurso = c.idcurso
WHERE e.idcurso IS NOT NULL
GROUP BY c.idcurso, c.nome, c.logo_url
ORDER BY total_usuarios DESC, horas_totais DESC
LIMIT 20";

$result = pg_query($conexao, $query);
if (!$result) {
    error_log("Erro ao executar consulta de ranking: " . pg_last_error($conexao));
    exit("Erro ao gerar ranking. Tente novamente mais tarde.");
}

$ranking = [];
while ($row = pg_fetch_assoc($result)) {
    $ranking[] = $row;
}

// Se for chamada AJAX, retorna apenas a parte da tabela
if (isset($_GET['partial'])) {
    ?>
    <table class="ranking-table">
        <thead>
            <tr>
                <th style="width: 100px">Posição</th>
                <th>Curso</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach ($ranking as $i => $curso): ?>
                <tr>
                    <td class="posicao">
                        <?php
                        if ($i === 0) echo '<i class="fas fa-medal medal gold"></i>';
                        else if ($i === 1) echo '<i class="fas fa-medal medal silver"></i>';
                        else if ($i === 2) echo '<i class="fas fa-medal medal bronze"></i>';
                        echo ($i + 1) . 'º';
                        ?>
                    </td>
                    <td><?php echo htmlspecialchars($curso['nome_curso']); ?></td>
                </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    <?php
    exit();
}

// Caso contrário, exibe a página completa
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ranking de Cursos</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&family=Varela+Round&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .container {
            max-width: 720px;
            margin: 0 auto;
            padding: 2rem;
        }
        
        .header_ranking {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        h1 {
            color: var(--primary-blue);
            font-size: 2.1rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .subtitle {
            /*color: #666;*/
            font-size: 1.1rem;
        }
        
        .ranking-table {
            width: 100%;
            background: #fff;
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 2px 16px rgba(0, 0, 139, 0.08);
        }
        
        .ranking-table thead {
           background: var(--primary-blue);
            color: #fff;
        }
        
        .ranking-table th {
            padding: 1rem;
            font-weight: 600;
            font-size: 1.1rem;
            text-align: left;
        }
        
        .ranking-table td {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
            font-size: 1.05rem;
        }
        
        .ranking-table tr:last-child td {
            border-bottom: none;
        }
        
        .ranking-table tr:nth-child(even) {
            background: #f8faff;
        }
        
        .posicao {
            font-weight: 700;
            color: var(--primary-blue);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .medal {
            font-size: 1.2rem;
        }
        
        .gold { color: #FFD700; }
        .silver { color: #C0C0C0; }
        .bronze { color: #CD7F32; }
        
        .info-section {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(0, 0, 139, 0.1);
        }

        .info-card {
            background: linear-gradient(145deg, #ffffff, #f8faff);
            border-radius: 16px;
            padding: 2rem;
            box-shadow: 0 2px 16px rgba(0, 0, 139, 0.08);
            border: 1px solid rgba(0, 0, 139, 0.1);
        }

        .info-card h2 {
            color: var(--primary-blue);
            font-size: 1.4rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            gap: 0.8rem;
        }

        .info-card h2 i {
            color: #3c6fd9;
        }

        .info-content {
            color: #444;
            font-size: 1.05rem;
            line-height: 1.6;
        }

        .info-content ul {
            margin: 1.2rem 0;
            padding-left: 1.5rem;
        }

        .info-content li {
            margin: 0.8rem 0;
        }

        .info-content strong {
            color: var(--primary-blue);
            font-weight: 600;
        }

        .note {
            margin-top: 1.5rem;
            padding: 1rem 1.2rem;
            background: rgba(0, 0, 139, 0.05);
            border-radius: 8px;
            font-size: 0.95rem;
            color: #666;
            border-left: 4px solid var(--primary-blue);
        }

        @media (max-width: 600px) {
            .container {
                padding: 1rem;
            }
            
            h1 {
                font-size: 1.6rem;
            }
            
            .ranking-table th,
            .ranking-table td {
                padding: 0.8rem;
                font-size: 1rem;
            }

            .info-card {
                padding: 1.5rem;
            }

            .info-content {
                font-size: 1rem;
            }

            .info-card h2 {
                font-size: 1.2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header_ranking">
            <h1>Ranking de Cursos</h1>
            <p class="subtitle">Os cursos mais populares entre nossos usuários</p>
        </div>
        
        <table class="ranking-table">
            <thead>
                <tr>
                    <th style="width: 80px">Posição</th>
                    <th style="width: 100px">Logo</th>
                    <th>Curso</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($ranking as $i => $curso): ?>
                    <tr>
                        <td class="posicao">
                            <?php
                            if ($i === 0) echo '<i class="fas fa-medal medal gold"></i>';
                            else if ($i === 1) echo '<i class="fas fa-medal medal silver"></i>';
                            else if ($i === 2) echo '<i class="fas fa-medal medal bronze"></i>';
                            echo ($i + 1) . 'º';
                            ?>
                        </td>
                        <td class="logo-curso">
                            <div class="curso-logo">
                                <img src="<?php 
                                    echo !empty($curso['logo_curso']) 
                                        ? '/cadastros/img/cursos/' . basename(htmlspecialchars($curso['logo_curso']))
                                        : '/logo/Estudo Off/favicon_32x32.png';
                                    ?>"
                                    alt="Logo <?php echo htmlspecialchars($curso['nome_curso']); ?>"
                                    class="curso-logo-img">
                            </div>
                        </td>
                        <td><?php echo htmlspecialchars($curso['nome_curso']); ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <div class="info-section">
            <div class="info-card">
                <h2><i class="fas fa-info-circle"></i> Como este ranking é calculado?</h2>
                <div class="info-content">
                    <p style="text-align: justify;">O ranking de cursos é gerado considerando os seguintes critérios:</p>
                    <ul style="text-align: justify;">
                        <li><strong>Número de Usuários:</strong> Quantidade total de estudantes ativos em cada curso</li>
                        <li><strong>Sessões de Estudo:</strong> Total de sessões de estudo registradas</li>
                        <li><strong>Tempo Total:</strong> Soma do tempo líquido de estudo de todos os usuários</li>
                    </ul>
                    <p style="text-align: justify;">A ordem é determinada primariamente pelo número de usuários ativos, seguido pelo tempo total de estudo, 
                       garantindo que os cursos mais populares e com maior engajamento apareçam no topo.</p>
                    <p class="note">Nota: Apenas o tempo líquido é contabilizado, desconsiderando pausas e interrupções.</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

<style>
.curso-logo {
    width: 60px;
    height: 60px;
    margin: 0 auto;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background);
    border: 1px solid var(--border);
}

.curso-logo-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.curso-logo-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    color: var(--primary);
    font-size: 24px;
}

.text-center {
    text-align: center;
}

.ranking-table td {
    vertical-align: middle;
}
</style>

