<?php
function cadastrarUsuario($conexao, $nome, $senha, $usuario, $email) {
    $query = "INSERT INTO appestudo.usuario (nome, senha, usuario, email) 
              VALUES ($1, $2, $3, $4)";

    $result = pg_query_params($conexao, $query, array(
        $nome,
        $senha,
        $usuario,
        $email
    ));

    if (!$result) {
        error_log("Erro ao cadastrar usuário: " . pg_last_error($conexao));
        return false;
    }
    return true;
}

function atualizarUsuario($conexao, $id, $nome, $senha, $usuario, $email) {
    try {
        if (!empty($senha)) {
            $query = "UPDATE appestudo.usuario 
                     SET nome = $1, senha = $2, usuario = $3, email = $4 
                     WHERE idusuario = $5";
            $params = array($nome, $senha, $usuario, $email, $id);
        } else {
            $query = "UPDATE appestudo.usuario 
                     SET nome = $1, usuario = $2, email = $3 
                     WHERE idusuario = $4";
            $params = array($nome, $usuario, $email, $id);
        }

        error_log("Query de atualização: " . $query);
        error_log("Parâmetros: " . print_r($params, true));

        $result = pg_query_params($conexao, $query, $params);

        if (!$result) {
            error_log("Erro ao atualizar usuário: " . pg_last_error($conexao));
            return false;
        }

        // Verifica se alguma linha foi afetada
        $affected_rows = pg_affected_rows($result);
        error_log("Linhas afetadas: " . $affected_rows);

        return $affected_rows > 0;

    } catch (Exception $e) {
        error_log("Exceção ao atualizar usuário: " . $e->getMessage());
        return false;
    }
}

function excluirUsuario($conexao, $id) {
    $query = "DELETE FROM appestudo.usuario WHERE idusuario = $1";
    $result = pg_query_params($conexao, $query, array($id));

    if (!$result) {
        error_log("Erro ao excluir usuário: " . pg_last_error($conexao));
        return false;
    }
    return true;
}

function listarUsuarios($conexao) {
    $query = "
        SELECT 
            u.idusuario,
            u.nome AS nome_usuario,
            u.usuario,
            u.email,
            u.senha,
            p.idplanejamento,
            CASE 
                WHEN p.idplanejamento IS NOT NULL THEN 'Sim'
                ELSE 'Não'
            END AS possui_planejamento,
            (
                SELECT e.data 
                FROM appestudo.estudos e 
                WHERE e.planejamento_usuario_idusuario = u.idusuario 
                ORDER BY e.data DESC 
                LIMIT 1
            ) AS data_ultimo_estudo
        FROM appestudo.usuario u
        LEFT JOIN appestudo.planejamento p ON u.idusuario = p.usuario_idusuario
        ORDER BY data_ultimo_estudo DESC NULLS LAST, u.idusuario ASC
    ";

    $result = pg_query($conexao, $query);
    if (!$result) {
        error_log("Erro ao listar usuários: " . pg_last_error($conexao));
        return array();
    }

    $usuarios = array();
    while ($row = pg_fetch_assoc($result)) {
        if ($row['data_ultimo_estudo']) {
            $date = new DateTime($row['data_ultimo_estudo']);
            $row['data_ultimo_estudo'] = $date->format('d-m-Y');
        } else {
            $row['data_ultimo_estudo'] = 'N/A';
        }
        $usuarios[] = $row;
    }

    return $usuarios;
}
?>