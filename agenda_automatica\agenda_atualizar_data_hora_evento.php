<?php
//agenda_atualizar_data_hora_evento.php

session_start();
include_once("../conexao_POST.php");

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_SESSION['idusuario'])) {
    $idUsuario = $_SESSION['idusuario'];

    // Verificar se os dados necessários foram recebidos
    if (!isset($_POST['eventoId']) || !isset($_POST['start'])) {
        echo json_encode(['success' => false, 'message' => 'Dados incompletos']);
        exit;
    }

    $eventoId = $_POST['eventoId'];
    $start = $_POST['start'];
    $end = $_POST['end'] ?? $start; // Se não houver end, usa o start

    try {
        // Processar as datas recebidas
        $startDate = new DateTime($start);
        $endDate = new DateTime($end);

        $startFormatted = $startDate->format('Y-m-d H:i:s');
        $endFormatted = $endDate->format('Y-m-d H:i:s');

        // Preparar e executar a query usando prepared statements
        $query = "UPDATE appEstudo.agenda 
                 SET data_inicio = $1, 
                     data_fim = $2 
                 WHERE id = $3 
                 AND usuario_idusuario = $4";

        $stmt = pg_prepare($conexao, "", $query);
        $result = pg_execute($conexao, "", array(
            $startFormatted,
            $endFormatted,
            $eventoId,
            $idUsuario
        ));

        if ($result) {
            echo json_encode(['success' => true, 'message' => 'Evento atualizado com sucesso']);
        } else {
            echo json_encode(['success' => false, 'message' => 'Erro ao atualizar evento']);
        }

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Erro ao processar datas: ' . $e->getMessage()
        ]);
    }

} else {
    echo json_encode(['success' => false, 'message' => 'Acesso não autorizado']);
}
?>