<?php
// usuario.php
session_start();
include 'conexao_POST.php';

// Verifica se o usuário está logado
if (!isset($_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit;
}

$id_usuario = $_SESSION['idusuario'];

// Consulta para buscar os dados do usuário
$query_usuario = "SELECT nome, usuario, email FROM appEstudo.usuario WHERE idusuario = $id_usuario";
$resultado_usuario = pg_query($conexao, $query_usuario);
$usuario = pg_fetch_assoc($resultado_usuario);

// Inicializa as mensagens
$mensagens = [];

// Processa o formulário de atualização de perfil
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['atualizar_perfil'])) {
        $nome = pg_escape_string($conexao, trim($_POST['nome']));
        // Converter para minúsculas e sanitizar o nome de usuário
        $username = strtolower(trim($_POST['usuario']));
        $username = pg_escape_string($conexao, preg_replace('/[^a-z0-9_]/', '', $username)); // Remove caracteres inválidos restantes
        $email = pg_escape_string($conexao, trim($_POST['email']));
        
        $erros_perfil = [];

        // Validação do formato do nome de usuário
        if (!preg_match('/^[a-z0-9_]+$/', $username)) {
            $erros_perfil[] = 'Usuário deve conter apenas letras minúsculas, números e underscore.';
        }
        
        // Validação do email (simples, pode ser mais robusta se necessário)
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $erros_perfil[] = 'Formato de e-mail inválido.';
        }

        // Verificar se o nome de usuário ou email já existem (se foram alterados)
        if (empty($erros_perfil)) {
            if ($username !== $usuario['usuario']) { // Verifica apenas se o username foi alterado
                $query_verificar_usuario = "SELECT idusuario FROM appEstudo.usuario WHERE usuario = '$username' AND idusuario != $id_usuario";
                $resultado_verificar_usuario = pg_query($conexao, $query_verificar_usuario);
                if (pg_num_rows($resultado_verificar_usuario) > 0) {
                    $erros_perfil[] = 'Este nome de usuário já está em uso por outra conta.';
                }
            }
            if ($email !== $usuario['email']) { // Verifica apenas se o email foi alterado
                $query_verificar_email = "SELECT idusuario FROM appEstudo.usuario WHERE email = '$email' AND idusuario != $id_usuario";
                $resultado_verificar_email = pg_query($conexao, $query_verificar_email);
                if (pg_num_rows($resultado_verificar_email) > 0) {
                    $erros_perfil[] = 'Este e-mail já está em uso por outra conta.';
                }
            }
        }

        if (!empty($erros_perfil)) {
            foreach ($erros_perfil as $erro) {
                $mensagens[] = ['tipo' => 'erro', 'texto' => $erro];
            }
        } else {
            // Atualiza os dados do usuário
            $query_atualizar = "UPDATE appEstudo.usuario SET 
                                nome = '$nome', 
                                usuario = '$username', 
                                email = '$email' 
                                WHERE idusuario = $id_usuario";
            
            $resultado_atualizar = pg_query($conexao, $query_atualizar);
            
            if ($resultado_atualizar) {
                $mensagens[] = ['tipo' => 'sucesso', 'texto' => 'Perfil atualizado com sucesso!'];
                
                // Atualiza a sessão com o novo nome e nome de usuário, se necessário
                $_SESSION['nome_usuario'] = $nome; 
                // Se você armazena o nome de usuário na sessão e ele é usado em outros lugares, atualize-o também.
                // Ex: $_SESSION['username'] = $username; 
                
                // Atualiza os dados exibidos na página atual
                $usuario['nome'] = $nome;
                $usuario['usuario'] = $username;
                $usuario['email'] = $email;
            } else {
                $mensagens[] = ['tipo' => 'erro', 'texto' => 'Erro ao atualizar o perfil: ' . pg_last_error($conexao)];
            }
        }
    }
    
    // Processa o formulário de alteração de senha
    if (isset($_POST['alterar_senha'])) {
        $senha_atual = pg_escape_string($conexao, $_POST['senha_atual']);
        $nova_senha = pg_escape_string($conexao, $_POST['nova_senha']);
        $confirmar_senha = pg_escape_string($conexao, $_POST['confirmar_senha']);
        
        // Primeiro, verifica se a senha atual está correta
        $query_verificar_senha = "SELECT senha FROM appEstudo.usuario WHERE idusuario = $id_usuario";
        $resultado_senha = pg_query($conexao, $query_verificar_senha);
        $row_senha = pg_fetch_assoc($resultado_senha);
        
        if ($senha_atual !== $row_senha['senha']) {
            $mensagens[] = ['tipo' => 'erro', 'texto' => 'Senha atual incorreta.'];
        } else if ($nova_senha !== $confirmar_senha) {
            $mensagens[] = ['tipo' => 'erro', 'texto' => 'As novas senhas não coincidem.'];
        } else {
            // Atualiza a senha
            $query_atualizar_senha = "UPDATE appEstudo.usuario SET senha = '$nova_senha' WHERE idusuario = $id_usuario";
            $resultado_atualizar_senha = pg_query($conexao, $query_atualizar_senha);
            
            if ($resultado_atualizar_senha) {
                $mensagens[] = ['tipo' => 'sucesso', 'texto' => 'Senha alterada com sucesso!'];
            } else {
                $mensagens[] = ['tipo' => 'erro', 'texto' => 'Erro ao alterar a senha: ' . pg_last_error($conexao)];
            }
        }
    }
}

// Consulta para estatísticas do usuário
$query_estatisticas = "SELECT 
                        COUNT(*) as total_estudos,
                        SUM(EXTRACT(EPOCH FROM tempo_liquido::time)) / 3600 as horas_estudadas,
                        MAX(data) as ultimo_estudo
                      FROM appEstudo.estudos
                      WHERE planejamento_usuario_idusuario = $id_usuario";
                      
$resultado_estatisticas = pg_query($conexao, $query_estatisticas);
$estatisticas = pg_fetch_assoc($resultado_estatisticas);

// Formatação dos dados de estatísticas
$total_estudos = $estatisticas['total_estudos'] ?? 0;
$horas_estudadas = round($estatisticas['horas_estudadas'] ?? 0, 1);
$ultimo_estudo = isset($estatisticas['ultimo_estudo']) ? date('d/m/Y', strtotime($estatisticas['ultimo_estudo'])) : 'Nunca';

// Consulta para as matérias mais estudadas
$query_materias = "SELECT 
                    m.nome as nome_materia,
                    COUNT(*) as total_sessoes,
                    SUM(EXTRACT(EPOCH FROM e.tempo_liquido::time)) / 3600 as horas_estudadas,
                    m.cor as cor_materia
                  FROM appEstudo.estudos e
                  JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria
                  WHERE e.planejamento_usuario_idusuario = $id_usuario
                  GROUP BY m.nome, m.cor
                  ORDER BY horas_estudadas DESC
                  LIMIT 5";
                  
$resultado_materias = pg_query($conexao, $query_materias);
$materias_estudadas = [];

while ($row = pg_fetch_assoc($resultado_materias)) {
    $materias_estudadas[] = $row;
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Perfil do Usuário</title>
    
    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Quicksand:wght@300..700&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Tektur:wght@400..900&family=Varela+Round&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">
    
    <!-- Ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    
    <style>
        :root {
            --primary: #00008B;
            --secondary: #f8f0e3;
            --accent: #4a4a4a;
            --border: #e0e0e0;
            --text: #2c3e50;
            --active: #00008B;
            --hover: #f5f5f5;
            --primary-blue: #00008B;
            --white: #ffffff;
            --shadow: rgba(0, 0, 0, 0.1);
            --gradient-primary: linear-gradient(135deg, #00008B, #0000CD);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Quicksand', sans-serif;
            background: #f8f9fa;
            color: #2d3436;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            max-width: 1200px;
            margin: 0 auto 2rem;
            text-align: center;
            position: relative;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid rgba(0, 0, 139, 0.1);
        }

        .page-header::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 3px;
            background: var(--gradient-primary);
            border-radius: 3px;
        }

        .page-title {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            font-size: 2.8rem;
            color: var(--primary);
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.05);
            margin-bottom: 0.5rem;
            letter-spacing: 1px;
        }

        .back-link {
            display: inline-block;
            color: white;
            text-decoration: none;
            margin-bottom: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            background: var(--gradient-primary);
            padding: 10px 20px;
            border-radius: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 139, 0.2);
            font-family: 'Quicksand', sans-serif;
            letter-spacing: 0.5px;
        }

        .back-link:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 139, 0.25);
        }

        .back-link i {
            margin-right: 8px;
        }

        .alert {
            padding: 15px 20px;
            margin-bottom: 25px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            display: flex;
            align-items: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            animation: slideIn 0.5s ease;
            border-left: 5px solid;
        }

        @keyframes slideIn {
            from {
                transform: translateY(-20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .alert i {
            margin-right: 10px;
            font-size: 1.2rem;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }

        .alert-error {
            background-color: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }

        .profile-grid {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 25px;
            margin-bottom: 30px;
        }

        @media (max-width: 768px) {
            .profile-grid {
                grid-template-columns: 1fr;
            }
        }

        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.07);
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .profile-info {
            text-align: center;
            padding: 30px 20px;
            position: relative;
        }

        .profile-info::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 120px;
            background: var(--gradient-primary);
            z-index: 1;
        }

        .profile-avatar {
            width: 130px;
            height: 130px;
            border-radius: 50%;
            background: var(--primary);
            color: white;
            font-size: 3.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            border: 5px solid white;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
            position: relative;
            z-index: 2;
        }

        .profile-name {
          font-family: 'Varela Round', 'Quicksand', sans-serif;
            font-size: 2rem;
            color: var(--primary);
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
            margin-top: 20px;
        }

        .profile-username {
            color: #666;
            margin-bottom: 15px;
            font-size: 1.1rem;
            position: relative;
            z-index: 2;
        }

        .profile-email {
            color: #666;
            font-style: italic;
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-top: 10px;
            font-size: 1.05rem;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-top: 30px;
            padding: 0px;
            position: relative;
            z-index: 2;
        }

        .stat-card {
            padding: 20px 15px;
            text-align: center;
            background: #f8f9fa;
            border-radius: 10px;
            box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-number {
            font-size: 1.3rem;
            font-weight: 700;
            color: var(--primary);
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.95rem;
            color: #666;
            font-weight: 500;
        }

        .tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 25px;
            overflow-x: auto;
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
        }

        .tab {
            padding: 15px 25px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            font-weight: 600;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .tab:hover {
            color: var(--primary);
            background-color: rgba(0, 0, 139, 0.03);
        }

        .tab.active {
            border-bottom-color: var(--primary);
            color: var(--primary);
        }

        .tab-content {
            display: none;
            padding: 10px;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        .form-title {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            font-size: 1.4rem;
            color: var(--primary);
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
            position: relative;
        }

        .form-title::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 80px;
            height: 2px;
            background: var(--primary);
            border-radius: 2px;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #444;
        }

        .form-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-family: 'Quicksand', sans-serif;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(0, 0, 139, 0.1);
            transform: translateY(-2px);
        }

        .btn {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
            font-size: 1rem;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: 0 4px 6px rgba(0, 0, 139, 0.2);
            font-family: 'Quicksand', sans-serif;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #0000CD, #00008B);
            transform: translateY(-3px);
            box-shadow: 0 6px 12px rgba(0, 0, 139, 0.25);
        }

        .materias-section {
            margin-top: 40px;
        }

        .materias-title {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            font-size: 1.8rem;
            color: var(--primary);
            margin-bottom: 25px;
            text-align: center;
            position: relative;
            padding-bottom: 10px;
        }

        .materias-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 2px;
            background: var(--gradient-primary);
            border-radius: 2px;
        }

        .materias-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 25px;
        }

        .materia-card {
            position: relative;
            padding: 20px;
            border-left: 5px solid #ccc;
            transition: all 0.3s ease;
        }

        .materia-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }

        .materia-name {
            font-weight: 700;
            margin-bottom: 15px;
            color: #333;
            font-size: 1.2rem;
        }

        .materia-stat {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 10px;
            color: #666;
            font-size: 1.05rem;
        }

        .materia-stat i {
            color: var(--primary);
            width: 20px;
        }

        @media (max-width: 480px) {
            .page-title {
                font-size: 2rem;
            }
            .stats-grid {
                grid-template-columns: 1fr 1fr;
            }
            .materias-grid {
                grid-template-columns: 1fr;
            }
            .profile-avatar {
                width: 100px;
                height: 100px;
                font-size: 2.5rem;
            }
            .profile-info::before {
                height: 100px;
            }
            .form-input {
                padding: 10px;
            }
            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="page-header">
            <h1 class="page-title">Meu Perfil</h1>
        </div>
        
        <a href="index.php" class="back-link">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
        
        <?php if (!empty($mensagens)): ?>
            <?php foreach ($mensagens as $mensagem): ?>
                <div class="alert alert-<?php echo $mensagem['tipo'] === 'sucesso' ? 'success' : 'error'; ?>">
                    <i class="fas fa-<?php echo $mensagem['tipo'] === 'sucesso' ? 'check-circle' : 'exclamation-circle'; ?>"></i>
                    <?php echo $mensagem['texto']; ?>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
        
        <div class="profile-grid">
            <div class="card">
                <div class="profile-info">
                    <div class="profile-avatar">
                        <?php echo strtoupper(substr($usuario['nome'] ?? 'U', 0, 1)); ?>
                    </div>
                    <h2 class="profile-name"><?php echo $usuario['nome']; ?></h2>
                    <p class="profile-username">@<?php echo $usuario['usuario']; ?></p>
                    <p class="profile-email"><i class="fas fa-envelope"></i> <?php echo $usuario['email'] ?? 'Não informado'; ?></p>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $total_estudos; ?></div>
                            <div class="stat-label">Estudos</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $horas_estudadas; ?></div>
                            <div class="stat-label">Horas</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number"><?php echo $ultimo_estudo; ?></div>
                            <div class="stat-label">Último</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <div class="tabs">
                    <div class="tab active" data-tab="perfil">Informações do Perfil</div>
                    <div class="tab" data-tab="senha">Alterar Senha</div>
                </div>
                
                <div class="tab-content active" id="perfil-tab">
                    <h3 class="form-title">Editar Perfil</h3>
                    <form method="POST" action="">
                        <div class="form-group">
                            <label for="nome" class="form-label">Nome</label>
                            <input type="text" id="nome" name="nome" class="form-input" value="<?php echo $usuario['nome']; ?>" required>
                        </div>
                        <div class="form-group">
                            <label for="usuario" class="form-label">Usuário</label>
                            <input type="text" 
                                   id="usuario" 
                                   name="usuario" 
                                   class="form-input" 
                                   value="<?php echo htmlspecialchars($usuario['usuario']); ?>" 
                                   required
                                   pattern="^[a-z0-9_]+$"
                                   style="text-transform: lowercase;" 
                                   oninput="this.value = this.value.toLowerCase().replace(/[^a-z0-9_]/g, '')"
                                   title="Use apenas letras minúsculas, números e underscore. Sem espaços.">
                            <div class="form-help">Apenas letras minúsculas, números e underscore (_). Sem espaços.</div>
                        </div>
                        <div class="form-group">
                            <label for="email" class="form-label">E-mail</label>
                            <input type="email" id="email" name="email" class="form-input" value="<?php echo $usuario['email'] ?? ''; ?>">
                        </div>
                        <button type="submit" name="atualizar_perfil" class="btn btn-primary">Salvar Alterações</button>
                    </form>
                </div>
                
                <div class="tab-content" id="senha-tab">
                    <h3 class="form-title">Alterar Senha</h3>
                    <form method="POST" action="">
                        <div class="form-group">
                            <label for="senha_atual" class="form-label">Senha Atual</label>
                            <input type="password" id="senha_atual" name="senha_atual" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label for="nova_senha" class="form-label">Nova Senha</label>
                            <input type="password" id="nova_senha" name="nova_senha" class="form-input" required>
                        </div>
                        <div class="form-group">
                            <label for="confirmar_senha" class="form-label">Confirmar Nova Senha</label>
                            <input type="password" id="confirmar_senha" name="confirmar_senha" class="form-input" required>
                        </div>
                        <button type="submit" name="alterar_senha" class="btn btn-primary">Alterar Senha</button>
                    </form>
                </div>
            </div>
        </div>
        
        <?php if (!empty($materias_estudadas)): ?>
        <div class="materias-section">
            <h3 class="materias-title">Matérias Mais Estudadas</h3>
            <div class="materias-grid">
                <?php foreach ($materias_estudadas as $materia): ?>
                <div class="card materia-card" style="border-left-color: <?php echo $materia['cor_materia']; ?>">
                    <h4 class="materia-name"><?php echo $materia['nome_materia']; ?></h4>
                    <div class="materia-stat">
                        <i class="fas fa-clock"></i>
                        <span><?php echo round($materia['horas_estudadas'], 1); ?> horas</span>
                    </div>
                    <div class="materia-stat">
                        <i class="fas fa-book-open"></i>
                        <span><?php echo $materia['total_sessoes']; ?> sessões</span>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Alternar entre as abas
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    // Remove a classe 'active' de todas as abas
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));
                    
                    // Adiciona a classe 'active' na aba clicada
                    this.classList.add('active');
                    
                    // Mostra o conteúdo da aba
                    const tabName = this.getAttribute('data-tab');
                    document.getElementById(`${tabName}-tab`).classList.add('active');
                });
            });
            
            // Ocultar mensagens de alerta após 5 segundos
            const alerts = document.querySelectorAll('.alert');
            if (alerts.length > 0) {
                setTimeout(() => {
                    alerts.forEach(alert => {
                        alert.style.opacity = '0';
                        alert.style.transform = 'translateY(-20px)';
                        setTimeout(() => {
                            alert.remove();
                        }, 300);
                    });
                }, 5000);
            }
        });
    </script>
</body>
</html>
