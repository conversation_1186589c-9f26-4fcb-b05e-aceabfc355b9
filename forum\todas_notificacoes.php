<?php
include_once("../session_config.php");
include_once("../assets/config.php");

if (!verificarSessaoValida()) {
    header("Location: ../login_index.php");
    exit();
}

$idusuario = $_SESSION['idusuario'];
// Buscar nome do usuário
$query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = $1";
$resultado_nome = pg_query_params($conexao, $query_buscar_nome, [$idusuario]);
$nome_usuario = ($resultado_nome && pg_num_rows($resultado_nome) > 0)
    ? pg_fetch_assoc($resultado_nome)['nome']
    : "Usuário";

$query = "
    SELECT id, mensagem, lida, data_criada, topico_id
    FROM appestudo.forum_notificacoes
    WHERE usuario_id = $1
    ORDER BY data_criada DESC
    LIMIT 100
";
$result = pg_query_params($conexao, $query, [$idusuario]);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Minhas Notificações</title>
    <link rel="stylesheet" href="css/forum.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            
            font-family: 'Inter', Arial, sans-serif;
        }
        .main-header {
            background: var(--header-bg, #fff);
            box-shadow: 0 2px 8px #0001;
            padding: 0;
        }
        .header-container {
            max-width: 1100px;
            margin: 0 auto;
            padding: 0 24px;
            display: flex;
            align-items: center;
            height: 64px;
            justify-content: space-between;
        }
        .logo {
            font-size: 1.3em;
            font-weight: 700;
            color: var(--accent-color, #2a7ae4);
            text-decoration: none;
        }
        .user-info {
            font-size: 1em;
            color: var(--text-color, #222);
            margin-right: 18px;
            font-weight: 500;
        }
        .icon-button {
            background: none;
            border: none;
            color: var(--text-color, #222);
            font-size: 1.2em;
            cursor: pointer;
            margin-left: 10px;
        }
        .notificacoes-container {
            max-width: 700px;
            margin: 40px auto 0 auto;
            background: var(--bg-color, #fff);
            border-radius: 10px;
            box-shadow: 0 2px 12px #0001;
            padding: 0 0 24px 0;
        }
        .btn-voltar-notificacoes {
            display: inline-flex;
            align-items: center;
            gap: 7px;
            margin: 24px 0 0 32px;
            padding: 7px 18px;
            background: var(--accent-color, #2a7ae4);
            color: #fff;
            border: none;
            border-radius: 6px;
            font-size: 1em;
            font-weight: 500;
            text-decoration: none;
            box-shadow: 0 2px 8px #0001;
            transition: background 0.18s;
        }
        .btn-voltar-notificacoes:hover {
            background: #1956a3;
            color: #fff;
            text-decoration: none;
        }
        .notificacoes-header {
            padding: 28px 32px 18px 32px;
            border-bottom: 1px solid var(--border, #e0e0e0);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .notificacoes-header h2 {
            margin: 0;
            font-size: 1.5em;
            color: var(--text-color, #222);
            font-weight: 700;
        }
        .notificacoes-lista {
            padding: 0 32px;
        }
        .notificacao-item {
            padding: 20px 0 18px 0;
            border-bottom: 1px solid var(--border, #f0f0f0);
            display: flex;
            align-items: flex-start;
            gap: 16px;
            transition: background 0.18s;
            background: var(--bg-color, #fff);
        }
        .notificacao-item:last-child {
            border-bottom: none;
        }
        .notificacao-item.lida {
            background: #f7f7f7;
            color: #888;
        }
        .notificacao-icon {
            font-size: 1.3em;
            color: var(--accent-color, #2a7ae4);
            margin-top: 2px;
        }
        .notificacao-conteudo {
            flex: 1;
        }
        .notificacao-item a {
            color: var(--accent-color, #2a7ae4);
            text-decoration: none;
            font-weight: 500;
        }
        .notificacao-item a:hover {
            text-decoration: underline;
        }
        .notificacao-mensagem {
            font-size: 1.05em;
            margin-bottom: 4px;
        }
        .notificacao-data {
            font-size: 0.93em;
            color: #888;
        }
        @media (max-width: 700px) {
            .notificacoes-container { max-width: 98vw; }
            .notificacoes-header, .notificacoes-lista { padding-left: 12px; padding-right: 12px; }
            .btn-voltar-notificacoes { margin-left: 12px; }
        }
    </style>
</head>
<body>
    <header class="main-header">
        <div class="header-container">
            <a href="index.php" class="logo">
                <i class="fas fa-bell"></i> PlanejaAqui <span style="font-weight:400; color:#444;">Fórum</span>
            </a>
            <div style="display:flex; align-items:center; gap:10px;">
                <span class="user-info"><i class="fas fa-user"></i> <?php echo htmlspecialchars($nome_usuario); ?></span>
                <button class="icon-button" id="toggleDarkMode" title="Alternar modo escuro">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </div>
    </header>
    <div class="notificacoes-container">
        <a href="index.php" class="btn-voltar-notificacoes"><i class="fas fa-arrow-left"></i> Voltar ao Fórum</a>
        <div class="notificacoes-header">
            <i class="fas fa-bell notificacao-icon"></i>
            <h2>Minhas Notificações</h2>
        </div>
        <div class="notificacoes-lista">
            <?php if ($result && pg_num_rows($result) > 0): ?>
                <?php while ($n = pg_fetch_assoc($result)): ?>
                    <div class="notificacao-item<?php echo $n['lida'] === 't' ? ' lida' : ''; ?>">
                        <div class="notificacao-conteudo">
                            <div class="notificacao-mensagem">
                                <?php if ($n['topico_id']): ?>
                                    <a href="ver_topico.php?id=<?php echo $n['topico_id']; ?>">
                                        <?php echo htmlspecialchars($n['mensagem']); ?>
                                    </a>
                                <?php else: ?>
                                    <?php echo htmlspecialchars($n['mensagem']); ?>
                                <?php endif; ?>
                            </div>
                            <div class="notificacao-data">
                                <i class="far fa-clock"></i> <?php echo date('d/m/Y H:i', strtotime($n['data_criada'])); ?>
                            </div>
                        </div>
                    </div>
                <?php endwhile; ?>
            <?php else: ?>
                <p style="padding: 32px 0; text-align: center; color: #888;">Você não possui notificações.</p>
            <?php endif; ?>
        </div>
    </div>
    <script>
        // Função para controlar o modo escuro
        function toggleDarkMode() {
            const body = document.body;
            const icon = document.querySelector('#toggleDarkMode i');
            body.classList.toggle('dark-mode');
            if (body.classList.contains('dark-mode')) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
                localStorage.setItem('darkMode', 'enabled');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
                localStorage.setItem('darkMode', 'disabled');
            }
        }
        // Verifica se o modo escuro estava ativado anteriormente
        document.addEventListener('DOMContentLoaded', function() {
            const darkMode = localStorage.getItem('darkMode');
            const icon = document.querySelector('#toggleDarkMode i');
            if (darkMode === 'enabled') {
                document.body.classList.add('dark-mode');
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            }
            document.getElementById('toggleDarkMode').addEventListener('click', function(e) {
                e.preventDefault();
                toggleDarkMode();
            });
        });
    </script>
</body>
</html> 