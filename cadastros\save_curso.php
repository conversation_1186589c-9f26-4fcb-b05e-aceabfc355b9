<?php
session_start();
require_once("assets/config.php");
require_once("includes/verify_admin.php");

header('Content-Type: application/json');

try {
    verificarAcessoAdmin($conexao, true);

    $nome = filter_input(INPUT_POST, 'nome', FILTER_SANITIZE_STRING);

    if (empty($nome)) {
        throw new Exception('Nome do curso não pode estar vazio');
    }

    // Prepara a query base
    $query = "INSERT INTO appEstudo.curso (nome";
    $params = [$nome];
    $values = "$1";

    // Processa o upload da logo se existir
    if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
        $uploadDir = 'img/cursos/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }

        $fileName = uniqid() . '_' . basename($_FILES['logo']['name']);
        $uploadFile = $uploadDir . $fileName;

        if (move_uploaded_file($_FILES['logo']['tmp_name'], $uploadFile)) {
            $logo_url = 'img/cursos/' . $fileName;
            $query .= ", logo_url";
            $values .= ", $" . (count($params) + 1);
            $params[] = $logo_url;
        }
    }

    // Finaliza a query
    $query .= ") VALUES (" . $values . ")";

    $resultado = pg_query_params($conexao, $query, $params);

    if (!$resultado) {
        throw new Exception('Erro ao cadastrar o curso no banco de dados: ' . pg_last_error($conexao));
    }

    echo json_encode([
        'success' => true,
        'message' => [
            'titulo' => 'Sucesso!',
            'conteudo' => '
                <div class="modal-success-content">
                    <div class="success-icon">
                        <i class="fas fa-check-circle fa-3x"></i>
                    </div>
                    <div class="success-message">
                        <h4>Curso cadastrado com sucesso!</h4>
                        <p>O novo curso foi adicionado corretamente.</p>
                    </div>
                </div>
            '
        ]
    ]);

} catch (Exception $e) {
    error_log("Erro no cadastro do curso: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => [
            'titulo' => 'Erro',
            'conteudo' => '
                <div class="modal-error-content">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-circle fa-3x"></i>
                    </div>
                    <div class="error-message">
                        <h4>Não foi possível cadastrar o curso</h4>
                        <p>Por favor, tente novamente.</p>
                    </div>
                </div>
            '
        ]
    ]);
}
?>

