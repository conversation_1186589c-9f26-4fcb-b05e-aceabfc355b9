<?php
session_start();
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Planeja AQUI - Login</title>
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #00008B;
            --secondary: #f8f0e3;
            --accent: #4a4a4a;
            --border: #e0e0e0;
            --text: #2c3e50;
            --hover: #f5f5f5;
            --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Quicksand', sans-serif;
            background: var(--hover);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow-x: hidden;
        }

        .slideshow-container {
            position: relative;
            width: 100%;
            min-height: 100vh;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.03);
        }

        .contact-container {
            position: relative;
            z-index: 2;
            text-align: center;
            padding: 2.5rem;
            width: min(90%, 380px);
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .login-title {
            font-family: 'Cinzel', serif;
            color: var(--primary);
            margin-bottom: 1.5rem;
            font-size: 1.8rem;
            font-weight: 600;
            position: relative;
            padding-bottom: 10px;
        }

        .login-title::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 2px;
            background: var(--primary);
        }

        .logo {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 0.5rem auto;
        }

        .logo img {
            width: auto;
            height: 45px;
            transition: var(--transition);
        }

        .logo img:hover {
            transform: scale(1.05);
        }

        .menu-icon {
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary);
            border-radius: 50%;
            margin: 0 auto 20px;
            color: white;
            font-size: 1.5rem;
            box-shadow: var(--shadow-md);
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            color: var(--text);
            font-weight: 600;
            font-size: 0.9rem;
            text-align: left;
        }

        .form-group input {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid var(--border);
            background: white;
            border-radius: 8px;
            font-family: 'Quicksand', sans-serif;
            font-size: 1rem;
            color: var(--text);
            transition: var(--transition);
        }

        .password-container {
            position: relative;
            width: 100%;
        }

        .toggle-password {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: var(--accent);
            transition: var(--transition);
            padding: 5px;
        }

        .toggle-password:hover {
            color: var(--primary);
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: var(--shadow-sm);
        }

        .btn {
            display: inline-block;
            width: 100%;
            padding: 12px 24px;
            font-family: 'Quicksand', sans-serif;
            font-weight: 600;
            text-align: center;
            transition: var(--transition);
            border-radius: 8px;
            cursor: pointer;
            margin: 0.5rem 0;
            border: none;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            opacity: 0.9;
        }

        .btn-secondary {
            background: var(--accent);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            opacity: 0.9;
        }

        .alert {
            padding: 1rem;
            margin-bottom: 1rem;
            border-radius: 8px;
            display: none;
        }
        .alert-danger {
            background-color: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        .alert-success {
            background-color: #dcfce7;
            border: 1px solid #22c55e;
            color: #166534;
        }

        @media (max-width: 480px) {
            .contact-container {
                margin: 1rem;
                padding: 1.5rem;
            }

            .form-group {
                margin-bottom: 1rem;
            }

            .form-group input {
                padding: 0.7rem;
            }

            .btn {
                padding: 10px 20px;
            }
        }
    </style>
</head>
<body>
<div class="slideshow-container">
    <div class="contact-container">
        <div class="logo">
            <img src="../logo/logo_vertical_azul.png" alt="Logo Planeja AQUI">
        </div>

        <h1 class="login-title">Faça seu Login</h1>

        <!-- Área de mensagens -->
        <?php if (isset($_SESSION['erro'])): ?>
            <div class="alert alert-danger" style="display: block;">
                <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($_SESSION['erro']); ?>
            </div>
            <?php unset($_SESSION['erro']); ?>
        <?php endif; ?>

        <?php if (isset($_SESSION['sucesso'])): ?>
            <div class="alert alert-success" style="display: block;">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($_SESSION['sucesso']); ?>
            </div>
            <?php unset($_SESSION['sucesso']); ?>
        <?php endif; ?>

        <?php if (isset($_GET['erro']) && $_GET['erro'] === 'conta_inativa'): ?>
            <div class="alert alert-danger" style="display: block;">
                <i class="fas fa-exclamation-circle"></i> Sua conta foi desativada. Entre em contato com o administrador.
            </div>
        <?php endif; ?>

        <form method="POST" action="valida_index.php" id="loginForm">
            <div class="form-group">
                <label for="username">Usuário</label>
                <input type="text" 
                       id="username" 
                       name="username" 
                       required
                       onkeypress="return event.charCode !== 32"
                       autocomplete="username" 
                       spellcheck="false">
            </div>
            <div class="form-group">
                <label for="password">Senha</label>
                <div class="password-container">
                    <input type="password" id="password" name="password" required
                           autocomplete="current-password">
                    <i class="fas fa-eye-slash toggle-password" id="togglePassword"></i>
                </div>
            </div>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-sign-in-alt"></i> Entrar
            </button>
        </form>

        <button onclick="window.location.href='index.html'" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Voltar
        </button>
    </div>
</div>

<script>
document.getElementById('username').addEventListener('input', function(e) {
    this.value = this.value.replace(/\s/g, '');
});

document.getElementById('togglePassword').addEventListener('click', function() {
    const passwordInput = document.getElementById('password');
    const icon = this;
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    } else {
        passwordInput.type = 'password';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    }
});

document.getElementById('loginForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    try {
        // Remove qualquer alerta existente
        const alertasExistentes = document.querySelectorAll('.alert');
        alertasExistentes.forEach(alerta => alerta.remove());
        
        // Desabilita o botão de submit durante o processo
        const submitButton = this.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Entrando...';
        
        const response = await fetch('valida_index.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Adiciona mensagem de sucesso antes do redirecionamento
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success';
            alertDiv.style.display = 'block';
            alertDiv.innerHTML = '<i class="fas fa-check-circle"></i> Login bem-sucedido! Redirecionando...';
            
            const form = document.getElementById('loginForm');
            form.insertBefore(alertDiv, form.firstChild);
            
            // Redireciona após um breve delay
            setTimeout(() => {
                window.location.href = result.redirect;
            }, 500);
        } else {
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-danger';
            alertDiv.style.display = 'block';
            alertDiv.innerHTML = `<i class="fas fa-exclamation-circle"></i> ${result.message}`;
            
            const form = document.getElementById('loginForm');
            form.insertBefore(alertDiv, form.firstChild);
            
            // Limpa apenas o campo de senha
            document.getElementById('password').value = '';
        }
    } catch (error) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger';
        alertDiv.style.display = 'block';
        alertDiv.innerHTML = '<i class="fas fa-exclamation-circle"></i> Erro ao conectar com o servidor';
        
        const form = document.getElementById('loginForm');
        form.insertBefore(alertDiv, form.firstChild);
    } finally {
        // Reabilita o botão de submit
        const submitButton = this.querySelector('button[type="submit"]');
        submitButton.disabled = false;
        submitButton.innerHTML = '<i class="fas fa-sign-in-alt"></i> Entrar';
    }
});
</script>
</body>
</html>
