<?php
session_start();
include_once 'conexao_POST.php';

// Define a constante para validar a execução através de um ponto de entrada esperado por processa_index.php
// Se processa_index.php não for estritamente necessário, esta inclusão pode ser removida no futuro.
define('MEU_SISTEMA_PHP_EXECUCAO_VALIDA', true);
include_once 'processa_index.php'; // ATENÇÃO: Se este script fizer saídas HTML ou redirecionamentos inesperados, pode quebrar o JSON.

// Verificar se o usuário está logado
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Usuário não autenticado']);
    exit;
}

// Configurar cabeçalhos
header('Content-Type: application/json');
error_reporting(0); // Desabilita a exibição de erros PHP

if (!isset($_GET['materia'])) {
    http_response_code(400);
    echo json_encode(['error' => 'Matéria não especificada']);
    exit;
}

$materia = $_GET['materia'];
$id_usuario = (int)$_SESSION['idusuario'];

try {
    // Usar a mesma consulta que está no historico.php
    $query = "
        SELECT
            e.idestudos,
            to_char(e.data, 'DD/MM/YYYY') AS data_estudo,
            e.ponto_estudado,
            e.tempo_liquido,
            e.q_total,
            e.q_certa,
            e.q_errada,
            c.nome AS nome_curso,
            e.metodo,
            e.link_conteudo -- Adicionando a coluna link_conteudo
        FROM appEstudo.estudos e
        LEFT JOIN appEstudo.curso c ON e.idcurso = c.idcurso
        WHERE e.planejamento_usuario_idusuario = $1 
        AND e.materia_idmateria = (
            SELECT idmateria 
            FROM appEstudo.materia 
            WHERE nome = $2
        )
        ORDER BY e.data DESC";

    $result = pg_query_params($conexao, $query, array($id_usuario, $materia));

    if (!$result) {
        throw new Exception(pg_last_error($conexao));
    }

    $estudos = array();
    while ($row = pg_fetch_assoc($result)) {
        // Formatar o tempo
        if (!empty($row['tempo_liquido'])) {
            if (strpos($row['tempo_liquido'], ':') !== false) {
                $partes = explode(':', $row['tempo_liquido']);
                $segundos = ($partes[0] * 3600) + ($partes[1] * 60) + $partes[2];
                
                $horas = floor($segundos / 3600);
                $minutos = floor(($segundos % 3600) / 60);
                
                $row['tempo_liquido'] = $horas > 0 ? 
                    sprintf("%dh %02dmin", $horas, $minutos) : 
                    sprintf("%dmin", $minutos);
            }
        }
        
        $estudos[] = $row;
    }

    echo json_encode($estudos);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erro ao buscar dados: ' . $e->getMessage()]);
}

