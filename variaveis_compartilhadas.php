<?php
// Inclui a configuração do banco de dados
require_once 'cronograma_inteligente/assets/config.php';

// Verifica se existe uma sessão de usuário
if (!isset($_SESSION)) {
    session_start();
}

if (isset($_SESSION['idusuario'])) {
    $id_usuario = $_SESSION['idusuario'];

    // Busca o tempo de planejamento do banco de dados
    $query = "SELECT tempo_planejamento 
              FROM appEstudo.planejamento 
              WHERE usuario_idusuario = $id_usuario";
              
    $result = pg_query($conexao, $query);
    
    if ($result) {
        $row = pg_fetch_assoc($result);
        // Converte o tempo do formato hh:mm para segundos
        if (isset($row['tempo_planejamento'])) {
            $tempo = explode(':', $row['tempo_planejamento']);
            $tempoPlanejamento = (intval($tempo[0]) * 3600) + (intval($tempo[1]) * 60);
        } else {
            $tempoPlanejamento = 7200; // valor padrão de 2 horas em segundos
        }
    } else {
        $tempoPlanejamento = 7200; // valor padrão caso a consulta falhe
    }
} else {
    $tempoPlanejamento = 7200; // valor padrão caso não haja usuário logado
}

// Garante que $tempoPlanejamento seja numérico
$tempoPlanejamento = intval($tempoPlanejamento);

// Outras variáveis compartilhadas que você precisa
$metadeTempoPlanejamentoSegundos = $tempoPlanejamento / 2;

// Adicione aqui outras variáveis que são usadas em múltiplos arquivos
?>

