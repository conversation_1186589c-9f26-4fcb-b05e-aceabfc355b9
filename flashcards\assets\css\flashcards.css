/* Dark mode & theme variables */
:root {
  --primary-color: #000080;
  --paper-color: #FFFFFF;
  --secondary-color: #4169E1;
  --background-color: #E8ECF3;
  --text-color: #2C3345;
  --border-color: #B8C2CC;
  --hover-color: #f0f5ff;
  --shadow-color: rgba(0, 0, 128, 0.1);
  --success-color: #2e7d32;
  --error-color: #b71c1c;
  --warning-color: #f0ad4e;
}

/* Dark theme variables */
[data-theme="dark"] {
  --primary-color: #4169E1;
  --paper-color: #1e2130;
  --secondary-color: #6c92ff;
  --background-color: #121420;
  --text-color: #e4e8f0;
  --border-color: #3a4056;
  --hover-color: #2c3251;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --success-color: #4caf50;
  --error-color: #f44336;
  --warning-color: #ffb74d;
}

body {
  background-color: var(--background-color);
  font-family: 'Nunito', 'Quicksand', 'Varela Round', sans-serif;
  color: var(--text-color);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Header styles for dark mode */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 2rem;
  background-color: var(--paper-color);
  box-shadow: 0 3px 8px var(--shadow-color);
  margin-bottom: 2rem;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  z-index: 100;
}

.header-left, .header-right {
  display: flex;
  align-items: center;
  height: 60px;
}

.logo {
  display: flex;
  align-items: center;
  height: 60px;
  padding: 5px 0;
}

.logo img {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
  transition: opacity 0.3s ease;
}

/* Show/hide logo based on theme */
.logo-light {
  display: block;
  opacity: 1;
}

.logo-dark {
  display: none;
  opacity: 0;
}

[data-theme="dark"] .logo-light {
  display: none;
  opacity: 0;
}

[data-theme="dark"] .logo-dark {
  display: block;
  opacity: 1;
}

.user-info {
  display: flex;
  align-items: center;
  margin-right: 1.5rem;
  background-color: rgba(0, 0, 128, 0.08);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  transition: all 0.3s ease;
}

[data-theme="dark"] .user-info {
  background-color: rgba(65, 105, 225, 0.15);
}

.user-info:hover {
  background-color: rgba(0, 0, 128, 0.12);
}

[data-theme="dark"] .user-info:hover {
  background-color: rgba(65, 105, 225, 0.25);
}

.user-info i {
  font-size: 1.5rem;
  margin-right: 0.8rem;
  color: var(--primary-color);
  transition: color 0.3s ease;
}

.user-name {
  font-weight: 600;
  color: var(--text-color);
  transition: color 0.3s ease;
}

.theme-toggle {
  margin-left: 1rem;
  position: relative;
}

.theme-btn {
  background-color: var(--paper-color);
  border: 2px solid var(--primary-color);
  font-size: 1.2rem;
  color: var(--primary-color);
  cursor: pointer;
  padding: 0.6rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px var(--shadow-color);
}

[data-theme="dark"] .theme-btn {
  color: var(--warning-color);
  border-color: var(--secondary-color);
  background-color: var(--hover-color);
}

.theme-btn:hover {
  background-color: var(--hover-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

[data-theme="dark"] .theme-btn:hover {
  background-color: rgba(255, 183, 77, 0.15);
  color: var(--warning-color);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
}

.clean-container {
  background-color: var(--paper-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px var(--shadow-color);
  padding: 30px;
  margin-bottom: 30px;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.header-icon {
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color);
  border-radius: 50%;
  margin: 0 auto 20px;
  color: white;
  font-size: 1.5rem;
  box-shadow: 0 4px 8px var(--shadow-color);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

h1 {
  font-family: 'Varela Round', 'Quicksand', sans-serif;
  text-align: center;
  margin: 0 0 40px 0;
  font-size: 2.2rem;
  color: var(--primary-color);
  padding-bottom: 10px;
  position: relative;
  transition: color 0.3s ease;
}

h1::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 2px;
  background: var(--secondary-color);
  transition: background-color 0.3s ease;
}

.controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: var(--paper-color);
  border-radius: 8px;
  box-shadow: 0 2px 6px var(--shadow-color);
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.control-group {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-family: 'Nunito', 'Quicksand', sans-serif;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 4px var(--shadow-color);
}

.btn:hover {
  background: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

.btn-secondary {
  background: var(--paper-color);
  color: var(--primary-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--hover-color);
  color: var(--secondary-color);
  border-color: var(--secondary-color);
}

[data-theme="dark"] .btn-secondary {
  color: var(--secondary-color);
}

[data-theme="dark"] .btn-secondary:hover {
  background-color: rgba(108, 146, 255, 0.15);
}

.categories {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.category-card {
  background: var(--paper-color);
  border-radius: 8px;
  padding: 0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px var(--shadow-color);
  border: 1px solid var(--border-color);
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px var(--shadow-color);
}

.category-header {
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin: 0;
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
  background: var(--paper-color);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.category-title {
  font-family: 'Varela Round', 'Quicksand', sans-serif;
  color: var(--primary-color);
  font-size: 1.2rem;
  margin: 0;
  position: relative;
  padding-bottom: 5px;
  transition: color 0.3s ease;
}

.category-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--secondary-color);
  transition: width 0.3s ease, background-color 0.3s ease;
}

.category-card:hover .category-title::after {
  width: 60px;
}

.category-stats {
  display: flex;
  gap: 15px;
  font-size: 0.9rem;
  color: var(--secondary-color);
  transition: color 0.3s ease;
}

.category-stats span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.category-description {
  color: var(--text-color);
  margin: 0;
  padding: 20px;
  font-style: italic;
  font-size: 0.95rem;
  line-height: 1.6;
  border-bottom: 1px solid var(--border-color);
  min-height: 60px;
  background: var(--paper-color);
  transition: color 0.3s ease, background-color 0.3s ease, border-color 0.3s ease;
}

.category-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  padding: 20px;
  transition: background-color 0.3s ease;
}

.category-actions .btn {
  flex: 1;
  justify-content: center;
}

.no-categories {
  text-align: center;
  padding: 60px 40px;
  background: var(--paper-color);
  border-radius: 8px;
  border: 1px dashed var(--border-color);
  margin-top: 30px;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.no-categories i {
  font-size: 3rem;
  color: var(--secondary-color);
  opacity: 0.7;
  margin-bottom: 25px;
  transition: color 0.3s ease;
}

.no-categories h3 {
  font-family: 'Varela Round', 'Quicksand', sans-serif;
  color: var(--primary-color);
  font-size: 1.5rem;
  margin-bottom: 15px;
  transition: color 0.3s ease;
}

.no-categories p {
  color: var(--text-color);
  margin-bottom: 25px;
  font-style: italic;
  transition: color 0.3s ease;
}

.btn-back {
  position: fixed;
  top: 102px;
  left: 20px;
  width: 40px;
  height: 40px;
  background: var(--paper-color);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  text-decoration: none;
  font-size: 1rem;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: 0 2px 6px var(--shadow-color);
}

.btn-back:hover {
  transform: translateY(-2px);
  background: var(--primary-color);
  color: white;
  box-shadow: 0 4px 8px var(--shadow-color);
}

.loading-shimmer {
  background: linear-gradient(
    90deg,
    var(--background-color) 0%,
    var(--paper-color) 50%,
    var(--background-color) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

[data-theme="dark"] .loading-shimmer {
  background: linear-gradient(
    90deg,
    rgba(30, 33, 48, 0.7) 0%,
    rgba(65, 105, 225, 0.1) 50%,
    rgba(30, 33, 48, 0.7) 100%
  );
  background-size: 200% 100%;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeInUp 0.5s ease forwards;
  animation-delay: calc(var(--index) * 0.1s);
  opacity: 0;
}

[v-cloak] {
  display: none;
}

.search input {
  padding: 10px 15px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  width: 250px;
  font-family: 'Nunito', 'Quicksand', sans-serif;
  color: var(--text-color);
  background-color: var(--paper-color);
  transition: all 0.3s ease;
}

[data-theme="dark"] .search input {
  background-color: rgba(30, 33, 48, 0.7);
  color: var(--text-color);
  border-color: var(--border-color);
}

.search input:focus {
  outline: none;
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 2px rgba(65, 105, 225, 0.2);
}

[data-theme="dark"] .search input:focus {
  border-color: var(--secondary-color);
  box-shadow: 0 0 0 2px rgba(108, 146, 255, 0.2);
}

.footer {
  text-align: center;
  margin-top: 20px;
  font-size: 0.9rem;
  color: var(--text-color);
  opacity: 0.7;
  transition: color 0.3s ease;
}

@media (max-width: 768px) {
  .container {
    padding: 20px 15px;
    padding-top: 60px;
  }
  
  .header {
    padding: 0.8rem 1rem;
  }
  
  .logo img {
    max-height: 40px;
  }
  
  .user-name {
    font-size: 0.9rem;
  }
  
  .controls {
    flex-direction: column;
    gap: 15px;
  }
  
  .control-group {
    width: 100%;
    justify-content: center;
  }
  
  .control-group .btn {
    width: 100%;
    justify-content: center;
  }
  
  .category-actions {
    flex-direction: column;
  }
  
  .btn-back {
    top: 10px;
    left: 10px;
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
  }
  
  .search input {
    width: 100%;
  }
}