
const timer=document.querySelector("#timer")

const btn_iniciar=document.querySelector("#btn_iniciar")
const btn_pausar=document.querySelector("#btn_pausar")
const btn_zerar=document.querySelector("#btn_zerar")
const btn_gravar=document.querySelector("#btn_gravar")
const btn_continuar=document.querySelector("#btn_continuar")

document.getElementById('btn_continuar').style.display = 'none';
document.getElementById('btn_pausar').style.display = 'none';
document.getElementById('btn_gravar').style.display = 'none';
document.getElementById('btn_zerar').style.display = 'none';

let intervalo=null
let tmp_ini=null
let tmp_atual=null
let tmp_pausa=false
let pause=null
let seg=null
let tmp_total=null
let tmp_pausado=null
let tmp_inicio_estudo=null
let tmp_fim_estudo=null

// Adicione aqui o novo código
document.getElementById('checkboxSalvar').addEventListener('change', function() {
    if (this.checked) {
        // Zera os campos quando ativa o modo manual
        document.getElementById("tempo").value = converter(0);
        document.getElementById("tempoTotal").value = converter(0);
        document.getElementById("tempoPausado").value = converter(0);
        document.getElementById("tempoInicialEstudo").value = converter(0);
        document.getElementById("tempoFinalEstudo").value = converter(0);
    }
});

// Função para controlar visibilidade do cronômetro e botões
function controlarVisibilidadeCronometro(checked) {
    const divTimer = document.querySelector('.timer-display');
    const buttonGroup = document.querySelector('.button-group');
    const buttonFull = document.querySelector('.fullscreen-toggle-btn');
    const logoP = document.querySelector('.logoP');
    

    if (checked) {
        // Se o checkbox estiver marcado, esconde o cronômetro e botões
        divTimer.style.display = 'none';
        buttonGroup.style.display = 'none';
        buttonFull.style.display = 'none';
        logoP.style.display = 'flex';
    } else {
        // Se o checkbox estiver desmarcado, mostra o cronômetro e botões
        divTimer.style.display = 'block';
        buttonGroup.style.display = 'flex';
        buttonFull.style.display = 'flex';
        logoP.style.display = 'none';
    }
}

const contador=()=>{
    if(tmp_pausa==false){
        tmp_atual=Date.now()
        seg=((tmp_atual-tmp_ini)/1000)+pause
        timer.innerHTML=converter(seg)
    }
    tmp_atual=Date.now()
}

const converter=(seg)=>{
    let hora=Math.floor(seg/3600)
    let resto=seg%3600
    let minuto=Math.floor(resto/60)
    let segundo=Math.floor(resto%60)
    let tempoFormatado=(hora<10?"0"+hora:hora)+":"+(minuto<10?"0"+minuto:minuto)+":"+(segundo<10?"0"+segundo:segundo)

    return tempoFormatado
}

function converterParaSegundos(tempo) {
    if (!tempo) return 0;
    const [horas, minutos, segundos] = tempo.split(':').map(Number);
    return (horas * 3600) + (minutos * 60) + segundos;
}

function mostrarAlerta(mensagem) {
    const alertHTML = `
        <div class="alert-overlay" style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            backdrop-filter: blur(5px);
        ">
            <div class="alert-container" style="
                background: #ffffff;
                border: 2px solid #00008B;
                border-radius: 10px;
                padding: 2rem;
                text-align: center;
                position: relative;
                max-width: 500px;
                margin: 1rem;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            ">
                <!-- Ornamentos de canto -->
                <div style="position: absolute; top: 10px; left: 10px; width: 20px; height: 20px; border-top: 2px solid #00008B; border-left: 2px solid #00008B;"></div>
                <div style="position: absolute; top: 10px; right: 10px; width: 20px; height: 20px; border-top: 2px solid #00008B; border-right: 2px solid #00008B;"></div>
                <div style="position: absolute; bottom: 10px; left: 10px; width: 20px; height: 20px; border-bottom: 2px solid #00008B; border-left: 2px solid #00008B;"></div>
                <div style="position: absolute; bottom: 10px; right: 10px; width: 20px; height: 20px; border-bottom: 2px solid #00008B; border-right: 2px solid #00008B;"></div>

                <h4 style="
                    color: red;
                    font-family: 'Cinzel', serif;
                    margin: 1rem 0;
                    font-size: 1.5rem;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                    font-weight: bold;
                ">
                    Atenção
                </h4>
                
                <!-- Mensagem -->
                <div style="
                    position: relative;
                    //margin: 2rem 0;
                    //padding: 1.5rem;
                    //border: 1px solid #00008B;
                    background: #ffffff;
                ">
                    <p style="
                        color: #000000;
                        margin: 0;
                        font-family: 'Quicksand', sans-serif;
                        font-size: 1.2rem;
                        line-height: 1.5;
                        font-weight: 500;
                    ">
                        ${mensagem}
                    </p>
                </div>

                <!-- Botão -->
                <button class="btn-elegant" onclick="this.closest('.alert-overlay').remove();" style="
                    font-family: 'Cinzel', serif;
                    margin-top: 1rem;
                    
                    border: none;
                    color: #ffffff;
                    padding: 0.8rem 2rem;
                    font-size: 1rem;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    border-radius: 5px;
                    font-weight: bold;
                ">
                    Compreendi
                </button>
            </div>
        </div>
    `;
    
    document.getElementById('alert-placeholder').innerHTML = alertHTML;
}

function validarTempos() {
    const tempo = document.getElementById("tempo");
    const tempoTotal = document.getElementById("tempoTotal");
    const tempoPausado = document.getElementById("tempoPausado");
    const checkboxSalvar = document.getElementById("checkboxSalvar");
    const pontoEstudado = document.querySelector('textarea[name="ponto"]');

    if(!tempo.value || !tempoTotal.value || !tempoPausado.value) {
        mostrarAlerta(checkboxSalvar.checked ?
            'Por favor, preencha todos os campos de tempo no registro manual.' :
            'Os tempos não foram gerados. Por favor, utilize o cronômetro para gerar os tempos.');
        return false;
    }

    if(checkboxSalvar.checked) {
        const tempoLiquido = converterParaSegundos(tempo.value);
        const tempoBruto = converterParaSegundos(tempoTotal.value);

        if(tempoLiquido > tempoBruto) {
            mostrarAlerta('O tempo <b>LÍQUIDO</b> deve ser <strong>MENOR</strong> ou <strong>IGUAL</strong> ao tempo <b>BRUTO</b>.');
            return false;
        }

        if(tempoLiquido <= 0) {
            mostrarAlerta('O tempo <b>LÍQUIDO</b> não pode estar <strong>ZERADO</strong>.');
            return false;
        }

        if (!pontoEstudado.value.trim()) {
            mostrarAlertaElegante('Por favor, descreva o conteúdo estudado.');
            pontoEstudado.classList.add('input-error');
            pontoEstudado.focus();
            return false;
        }
    }

    // Remove a classe de erro se estiver tudo OK
    pontoEstudado.classList.remove('input-error');

    return true;
}

const zerarCronometro = () => {
    clearInterval(intervalo);
    tmp_atual=null;
    tmp_ini=null;
    pause=null;
    tmp_pausa=false;
    seg=null;
    tmp_total=null;
    timer.innerHTML="00:00:00";
    document.getElementById('btn_iniciar').style.display = 'inline';
    document.getElementById('btn_continuar').style.display = 'none';
    document.getElementById('btn_pausar').style.display = 'none';
    document.getElementById('btn_gravar').style.display = 'none';
    document.getElementById('btn_zerar').style.display = 'none';

        // Zera também o contador de tempo perdido
        clearInterval(lostTimeInterval);
        lostTimeStart = null;
        totalLostTime = 0;
        lostTimeCounter.innerHTML = "00:00:00";
        lostTimeCounter.style.display = 'none';
}

btn_iniciar.addEventListener("click", (evt)=>{
    var dataAtual = new Date();
    var horaAtual = dataAtual.getHours();
    var minutosAtuais = dataAtual.getMinutes();
    var segundosAtuais = dataAtual.getSeconds();

    tmp_inicio_estudo = horaAtual + ":" + minutosAtuais + ":" + segundosAtuais;

    tmp_ini=Date.now()
    tmp_total=tmp_ini

    intervalo=setInterval(contador,1000)
    document.getElementById('btn_iniciar').style.display = 'none';
    document.getElementById('btn_pausar').style.display = 'inline';
    document.getElementById('btn_zerar').style.display = 'inline';

    timer.innerHTML="00:00:00"

    document.getElementById("tempo").value=null
    document.getElementById("tempoTotal").value=null
    document.getElementById("tempoPausado").value=null

     // Inicialize também o contador de tempo perdido como 0
     totalLostTime = 0;
     lostTimeCounter.innerHTML = "00:00:00";
     lostTimeCounter.style.display = 'none'; // Esconde inicialmente
})

btn_continuar.addEventListener("click", (evt)=>{
    document.getElementById('btn_continuar').style.display = 'none';
    document.getElementById('btn_gravar').style.display = 'none';
    document.getElementById('btn_pausar').style.display = 'inline';
    tmp_pausa=false
    tmp_ini=Date.now()

        // Para a contagem de tempo perdido
        stopLostTimeCounter();
})

btn_pausar.addEventListener("click", (evt)=>{
    document.getElementById('btn_gravar').style.display = 'inline';
    document.getElementById('btn_pausar').style.display = 'none';
    document.getElementById('btn_continuar').style.display = 'inline';
    tmp_pausa=true
    pause=seg

        // Inicia a contagem de tempo perdido
        startLostTimeCounter();
})

btn_zerar.addEventListener("click", (evt)=>{
    showModal();
})

btn_gravar.addEventListener("click", (evt)=>{
    var dataAtual = new Date();
    var horaAtual = dataAtual.getHours();
    var minutosAtuais = dataAtual.getMinutes();
    var segundosAtuais = dataAtual.getSeconds();
    tmp_fim_estudo = horaAtual + ":" + minutosAtuais + ":" + segundosAtuais;

    document.getElementById('btn_iniciar').style.display = 'inline';
    document.getElementById('btn_continuar').style.display = 'none';
    document.getElementById('btn_pausar').style.display = 'none';
    document.getElementById('btn_gravar').style.display = 'none';
    document.getElementById('btn_zerar').style.display = 'none';
    clearInterval(intervalo)

    document.getElementById("tempo").value=timer.innerHTML

    tmp_total=(tmp_atual-tmp_total)/1000

    tmp_pausado=(tmp_total-seg)

    document.getElementById("tempoTotal").value=converter(tmp_total)
    
    document.getElementById("tempoInicialEstudo").value = tmp_inicio_estudo
    document.getElementById("tempoFinalEstudo").value = tmp_fim_estudo

    tmp_atual=null
    tmp_ini=null
    pause=null
    tmp_pausa=false
    seg=null
    tmp_total=null

    // Para a contagem de tempo perdido se estiver ativa
    if (lostTimeInterval) {
        stopLostTimeCounter();
    }

    // Use o tempo total perdido calculado
    tmp_pausado = totalLostTime;
    document.getElementById("tempoPausado").value = converter(tmp_pausado);

    totalLostTime = 0;
})

function inicializarValidacaoTempoReal() {
    const tempo = document.getElementById("tempo");
    const tempoTotal = document.getElementById("tempoTotal");
    const checkboxSalvar = document.getElementById("checkboxSalvar");

    function validarTempoReal() {
        if(checkboxSalvar.checked && tempo.value && tempoTotal.value) {
            const tempoLiquido = converterParaSegundos(tempo.value);
            const tempoBruto = converterParaSegundos(tempoTotal.value);

            tempo.classList.toggle('input-error', tempoLiquido > tempoBruto);
            tempoTotal.classList.toggle('input-error', tempoLiquido > tempoBruto);
        } else {
            tempo.classList.remove('input-error');
            tempoTotal.classList.remove('input-error');
        }
    }

    // Adicionar evento para controlar visibilidade
    checkboxSalvar.addEventListener('change', function(e) {
        controlarVisibilidadeCronometro(this.checked);
        validarTempoReal();
    });

    tempo.addEventListener('input', validarTempoReal);
    tempoTotal.addEventListener('input', validarTempoReal);
}

function showModal() {
    document.getElementById('confirmModal').style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

function closeModal() {
    document.getElementById('confirmModal').style.display = 'none';
    document.body.style.overflow = 'auto';
}

function confirmarZerar() {
    zerarCronometro();
    closeModal();
}

document.getElementById('confirmModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

document.querySelector('form').addEventListener('submit', function(e) {
    const pontoEstudado = document.querySelector('textarea[name="ponto"]');

    if (!pontoEstudado.value.trim()) {
        e.preventDefault();
        mostrarAlertaElegante('Por favor, descreva o conteúdo estudado.');
        pontoEstudado.classList.add('input-error');
        pontoEstudado.focus();
        return false;
    }

    // Remove a classe de erro se estiver tudo OK
    pontoEstudado.classList.remove('input-error');

    // Continua com as outras validações
    if (!validarTempos()) {
        e.preventDefault();
        return false;
    }
});

// Adicione este evento para remover o destaque de erro quando o usuário começar a digitar
document.querySelector('textarea[name="ponto"]').addEventListener('input', function() {
    this.classList.remove('input-error');
});

// Inicializar validação em tempo real quando o documento carregar
document.addEventListener('DOMContentLoaded', function() {
    inicializarValidacaoTempoReal();
    // Configura estado inicial baseado no checkbox
    const checkboxSalvar = document.getElementById("checkboxSalvar");
    controlarVisibilidadeCronometro(checkboxSalvar.checked);
});

function controlarVisibilidadeCronometro(checked) {
    const divTimer = document.querySelector('.timer-display');
    const buttonGroup = document.querySelector('.button-group');
    const tempoInputs = document.querySelectorAll('.time-inputs input[type="time"]');
    const buttonFull = document.querySelector('.fullscreen-toggle-btn');
    const logoP = document.querySelector('.logoP');

    if (checked) {
        // Se o checkbox estiver marcado, esconde o cronômetro e botões
        divTimer.style.display = 'none';
        buttonGroup.style.display = 'none';
        buttonFull.style.display = 'none';
        logoP.style.display = 'flex';

        // Inicializa os campos de tempo com 00:00:00
        tempoInputs.forEach(input => {
            input.value = '00:00:00';
            // Remove readonly para permitir edição
            input.removeAttribute('readonly');
            // Define o valor mínimo para evitar valores negativos
            input.setAttribute('min', '00:00:00');
            // Define o step para 1 segundo
            input.setAttribute('step', '1');
        });
    } else {
        // Se o checkbox estiver desmarcado, mostra o cronômetro e botões
        divTimer.style.display = 'block';
        buttonGroup.style.display = 'flex';
        buttonFull.style.display = 'flex';
        logoP.style.display = 'none';

        // Limpa os campos quando sair do modo manual
        tempoInputs.forEach(input => {
            input.value = '';
            // Adiciona readonly para prevenir edição no modo automático
            input.setAttribute('readonly', '');
        });
    }
}

function mostrarAlertaElegante(mensagem, tipo = 'campo') {
    const alertHTML = `
        <div class="alert-overlay" style="
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100vh;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        ">
            <div class="alert-container" style="
                display: flex;
                justify-content: center;
                align-items: center;
                width: 100%;
                height: 100%;
            ">
                <div class="alert" style="
                    background: #ffffff;
                    border: 2px solid #00008B;
                    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
                    padding: 2rem;
                    text-align: center;
                    position: relative;
                    max-width: 500px;
                    width: 90%;
                    margin: 1rem;
                    border-radius: 10px;
                ">
                    <!-- Ornamentos de canto -->
                    <div style="position: absolute; top: 10px; left: 10px; width: 20px; height: 20px; border-top: 2px solid #00008B; border-left: 2px solid #00008B;"></div>
                    <div style="position: absolute; top: 10px; right: 10px; width: 20px; height: 20px; border-top: 2px solid #00008B; border-right: 2px solid #00008B;"></div>
                    <div style="position: absolute; bottom: 10px; left: 10px; width: 20px; height: 20px; border-bottom: 2px solid #00008B; border-left: 2px solid #00008B;"></div>
                    <div style="position: absolute; bottom: 10px; right: 10px; width: 20px; height: 20px; border-bottom: 2px solid #00008B; border-right: 2px solid #00008B;"></div>

                    <h4 style="color: red; font-family: 'Cinzel', serif; margin: 1rem 0; font-size: 1.5rem;">
                        ${tipo === 'campo' ? 'Campo Obrigatório' : 'Atenção'}
                    </h4>
                    
                    <div style="
                        position: relative;
                        
                        
                        
                        background: #ffffff;
                    ">
                        <p style="
                            color: #000000;
                            margin: 0;
                            font-family: 'Quicksand', sans-serif;
                            font-size: 1.2rem;
                            line-height: 1.5;
                            font-weight: 500;
                        ">
                            ${mensagem}
                        </p>
                    </div>

                    <button class="btn-elegant" onclick="this.closest('.alert-overlay').remove();" style="
                        font-family: 'Cinzel', serif;
                        margin-top: 1rem;
                        background: #00008B;
                        border: none;
                        color: #ffffff;
                        padding: 0.8rem 2rem;
                        font-size: 1rem;
                        text-transform: uppercase;
                        letter-spacing: 2px;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        border-radius: 5px;
                        font-weight: bold;
                    ">
                        Compreendi
                    </button>
                </div>
            </div>
        </div>
    `;

    document.getElementById('alert-placeholder').innerHTML = alertHTML;
}

// Adicione estas variáveis no início do arquivo
let lostTimeInterval = null;
let lostTimeStart = null;
let totalLostTime = 0;
const lostTimeCounter = document.getElementById('lost-time-counter');

// Função para formatar o tempo perdido
const formatLostTime = (seconds) => {
    let hours = Math.floor(seconds / 3600);
    let remainder = seconds % 3600;
    let minutes = Math.floor(remainder / 60);
    let secs = Math.floor(remainder % 60);
    return (hours < 10 ? "0" + hours : hours) + ":" +
           (minutes < 10 ? "0" + minutes : minutes) + ":" +
           (secs < 10 ? "0" + secs : secs);
};

// Função para iniciar a contagem de tempo perdido
const startLostTimeCounter = () => {
    lostTimeStart = Date.now();
    lostTimeInterval = setInterval(() => {
        const currentLostTime = Math.floor((Date.now() - lostTimeStart) / 1000) + totalLostTime;
        lostTimeCounter.innerHTML = formatLostTime(currentLostTime);
        lostTimeCounter.style.display = 'block';
    }, 1000);
};

// Função para parar a contagem de tempo perdido
const stopLostTimeCounter = () => {
    if (lostTimeStart) {
        totalLostTime += Math.floor((Date.now() - lostTimeStart) / 1000);
        lostTimeStart = null;
    }
    clearInterval(lostTimeInterval);
};

// Adicione esta função ao JavaScript existente
function setupAutomaticLostTime() {
    const checkboxSalvar = document.getElementById('checkboxSalvar');
    const tempoLiquido = document.getElementById('tempo');
    const tempoBruto = document.getElementById('tempoTotal');
    const tempoPerdido = document.getElementById('tempoPausado');

    function calculateLostTime() {
        if (checkboxSalvar.checked && tempoLiquido.value && tempoBruto.value) {
            const liquidoSeconds = convertTimeToSeconds(tempoLiquido.value);
            const brutoSeconds = convertTimeToSeconds(tempoBruto.value);
            
            // Verificar se tempo bruto é maior que tempo líquido
            if (brutoSeconds >= liquidoSeconds) {
                const lostSeconds = brutoSeconds - liquidoSeconds;
                tempoPerdido.value = convertSecondsToTime(lostSeconds);
            } else {
                // Se tempo líquido for maior que bruto, não calcula
                tempoPerdido.value = '00:00:00';
            }
        }
    }

    // Função para converter HH:MM:SS para segundos
    function convertTimeToSeconds(timeString) {
        const [hours, minutes, seconds] = timeString.split(':').map(Number);
        return (hours * 3600) + (minutes * 60) + seconds;
    }

    // Função para converter segundos para HH:MM:SS
    function convertSecondsToTime(totalSeconds) {
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
        
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    // Adicionar event listeners
    tempoLiquido.addEventListener('input', calculateLostTime);
    tempoBruto.addEventListener('input', calculateLostTime);
    
    // Também calcular quando o checkbox for marcado
    checkboxSalvar.addEventListener('change', function() {
        if (this.checked) {
            calculateLostTime();
        }
    });
}

// Chamar a função quando o documento estiver pronto
document.addEventListener('DOMContentLoaded', function() {
    setupAutomaticLostTime();
});

// Adicionar código para o modo tela cheia
document.addEventListener('DOMContentLoaded', function() {
    const fullscreenToggleBtn = document.getElementById('fullscreen-toggle-btn');
    const fullscreenIcon = document.getElementById('fullscreen-icon');
    
    if (fullscreenToggleBtn) {
        fullscreenToggleBtn.addEventListener('click', toggleFullscreenMode);
    }
    
    // Variável global para rastrear se estamos em modo tela cheia
    window.isInFullscreenMode = false;
    
    function toggleFullscreenMode() {
        const container = document.querySelector('.container');
        const timerDisplay = document.querySelector('.timer-display');
        const buttonGroup = document.querySelector('.button-group');
        
        // Criar o elemento de modo tela cheia
        const fullscreenElement = document.createElement('div');
        fullscreenElement.className = 'fullscreen-mode';
        fullscreenElement.id = 'fullscreen-mode';
        
        // Adicionar controles de saída
        const controlsDiv = document.createElement('div');
        controlsDiv.className = 'fullscreen-controls';
        
        // Botão de tema no modo tela cheia
        const themeBtn = document.createElement('button');
        themeBtn.className = 'exit-fullscreen-btn theme-btn';
        themeBtn.innerHTML = document.documentElement.getAttribute('data-theme') === 'dark' 
            ? '<i class="fas fa-sun"></i>' 
            : '<i class="fas fa-moon"></i>';
        themeBtn.addEventListener('click', function() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            // Atualizar ícone
            this.innerHTML = newTheme === 'dark' 
                ? '<i class="fas fa-sun"></i>' 
                : '<i class="fas fa-moon"></i>';
        });
        
        // Botão de sair do modo tela cheia
        const exitBtn = document.createElement('button');
        exitBtn.className = 'exit-fullscreen-btn';
        exitBtn.innerHTML = '<i class="fas fa-compress"></i>';
        exitBtn.addEventListener('click', exitFullscreenMode);
        
        controlsDiv.appendChild(themeBtn);
        controlsDiv.appendChild(exitBtn);
        
        // Clonar elementos para o modo tela cheia
        const timerClone = timerDisplay.cloneNode(true);
        const buttonsClone = buttonGroup.cloneNode(true);
        
        // Adicionar elementos ao modo tela cheia
        fullscreenElement.appendChild(controlsDiv);
        fullscreenElement.appendChild(timerClone);
        fullscreenElement.appendChild(buttonsClone);
        
        // Adicionar ao body
        document.body.appendChild(fullscreenElement);
        
        // Esconder o container original
        container.style.display = 'none';
        
        // Marcar que estamos em modo tela cheia
        window.isInFullscreenMode = true;
        
        // Adicionar eventos aos botões clonados
        const btnIniciar = fullscreenElement.querySelector('#btn_iniciar');
        const btnContinuar = fullscreenElement.querySelector('#btn_continuar');
        const btnPausar = fullscreenElement.querySelector('#btn_pausar');
        const btnZerar = fullscreenElement.querySelector('#btn_zerar');
        const btnGravar = fullscreenElement.querySelector('#btn_gravar');
        
        if (btnIniciar) btnIniciar.addEventListener('click', function() {
            document.getElementById('btn_iniciar').click();
            updateFullscreenButtons();
        });
        
        if (btnContinuar) btnContinuar.addEventListener('click', function() {
            document.getElementById('btn_continuar').click();
            updateFullscreenButtons();
        });
        
        if (btnPausar) btnPausar.addEventListener('click', function() {
            document.getElementById('btn_pausar').click();
            updateFullscreenButtons();
        });
        
        if (btnZerar) btnZerar.addEventListener('click', function() {
            // Ao invés de clicar no botão original, mostramos o modal diretamente no modo tela cheia
            showFullscreenModal();
        });
        
        if (btnGravar) btnGravar.addEventListener('click', function() {
            document.getElementById('btn_gravar').click();
            exitFullscreenMode();
        });
        
        // Ativar modo tela cheia do navegador
        requestFullscreen(fullscreenElement);
        
        // Iniciar atualização do timer
        startFullscreenTimerUpdate();
        
        // Adicionar evento para detectar saída do modo tela cheia
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('mozfullscreenchange', handleFullscreenChange);
        document.addEventListener('MSFullscreenChange', handleFullscreenChange);
    }
    
    // Função para mostrar o modal no modo tela cheia
    function showFullscreenModal() {
        // Verificar se já existe um modal no modo tela cheia
        if (document.getElementById('fullscreenConfirmModal')) {
            return;
        }
        
        // Criar o modal para o modo tela cheia
        const modalOverlay = document.createElement('div');
        modalOverlay.className = 'modal-overlay';
        modalOverlay.id = 'fullscreenConfirmModal';
        modalOverlay.style.display = 'flex';
        modalOverlay.style.zIndex = '10000'; // Garantir que fique acima de tudo
        
        modalOverlay.innerHTML = `
            <div class="modal-content">
                <div class="modal-corner modal-corner-tl"></div>
                <div class="modal-corner modal-corner-tr"></div>
                <div class="modal-corner modal-corner-bl"></div>
                <div class="modal-corner modal-corner-br"></div>
                
                <h3 class="modal-title">Confirmar Ação</h3>
                <p class="modal-message">Tem certeza que deseja zerar o cronômetro?</p>
                
                <div class="modal-buttons">
                    <button class="modal-btn modal-btn-confirm" id="fullscreenConfirmBtn">Confirmar</button>
                    <button class="modal-btn modal-btn-cancel" id="fullscreenCancelBtn">Cancelar</button>
                </div>
            </div>
        `;
        
        // Adicionar ao elemento de tela cheia
        document.getElementById('fullscreen-mode').appendChild(modalOverlay);
        
        // Adicionar eventos aos botões
        document.getElementById('fullscreenConfirmBtn').addEventListener('click', function() {
            // Fechar o modal
            closeFullscreenModal();
            // Executar a ação de zerar
            zerarCronometro();
            // Atualizar os botões
            updateFullscreenButtons();
        });
        
        document.getElementById('fullscreenCancelBtn').addEventListener('click', closeFullscreenModal);
        
        // Fechar o modal ao clicar fora
        modalOverlay.addEventListener('click', function(e) {
            if (e.target === modalOverlay) {
                closeFullscreenModal();
            }
        });
    }
    
    // Função para fechar o modal no modo tela cheia
    function closeFullscreenModal() {
        const modal = document.getElementById('fullscreenConfirmModal');
        if (modal) {
            modal.remove();
        }
    }
    
    function handleFullscreenChange() {
        // Se saiu do modo tela cheia pelo ESC, também sair do nosso modo personalizado
        if (!document.fullscreenElement && 
            !document.webkitFullscreenElement && 
            !document.mozFullScreenElement && 
            !document.msFullscreenElement) {
            
            const fullscreenElement = document.getElementById('fullscreen-mode');
            if (fullscreenElement) {
                exitFullscreenMode();
            }
        }
    }
    
    function requestFullscreen(element) {
        if (element.requestFullscreen) {
            element.requestFullscreen();
        } else if (element.webkitRequestFullscreen) { /* Safari */
            element.webkitRequestFullscreen();
        } else if (element.msRequestFullscreen) { /* IE11 */
            element.msRequestFullscreen();
        } else if (element.mozRequestFullScreen) { /* Firefox */
            element.mozRequestFullScreen();
        }
    }
    
    function exitFullscreen() {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        } else if (document.webkitExitFullscreen) { /* Safari */
            document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) { /* IE11 */
            document.msExitFullscreen();
        } else if (document.mozCancelFullScreen) { /* Firefox */
            document.mozCancelFullScreen();
        }
    }
    
    function exitFullscreenMode() {
        // Sair do modo tela cheia do navegador
        exitFullscreen();
        
        const fullscreenElement = document.getElementById('fullscreen-mode');
        const container = document.querySelector('.container');
        
        if (fullscreenElement) {
            fullscreenElement.remove();
        }
        
        container.style.display = 'block';
        stopFullscreenTimerUpdate();
        
        // Marcar que não estamos mais em modo tela cheia
        window.isInFullscreenMode = false;
        
        // Remover os event listeners
        document.removeEventListener('fullscreenchange', handleFullscreenChange);
        document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
        document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
    }
    
    // Sobrescrever a função showModal original para verificar se estamos em modo tela cheia
    const originalShowModal = window.showModal;
    window.showModal = function() {
        if (window.isInFullscreenMode) {
            showFullscreenModal();
        } else if (typeof originalShowModal === 'function') {
            originalShowModal();
        } else {
            document.getElementById('confirmModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }
    };
    
    let fullscreenTimerInterval;
    
    function startFullscreenTimerUpdate() {
        // Atualizar o timer a cada segundo
        fullscreenTimerInterval = setInterval(function() {
            const mainTimer = document.querySelector('.container #timer');
            const fullscreenTimer = document.querySelector('#fullscreen-mode #timer');
            const mainLostTime = document.querySelector('.container #lost-time-counter');
            const fullscreenLostTime = document.querySelector('#fullscreen-mode #lost-time-counter');
            
            if (mainTimer && fullscreenTimer) {
                fullscreenTimer.textContent = mainTimer.textContent;
            }
            
            if (mainLostTime && fullscreenLostTime) {
                fullscreenLostTime.textContent = mainLostTime.textContent;
                fullscreenLostTime.style.display = mainLostTime.style.display;
            }
            
            updateFullscreenButtons();
        }, 100);
    }
    
    function stopFullscreenTimerUpdate() {
        clearInterval(fullscreenTimerInterval);
    }
    
    function updateFullscreenButtons() {
        const mainBtnIniciar = document.querySelector('.container #btn_iniciar');
        const mainBtnContinuar = document.querySelector('.container #btn_continuar');
        const mainBtnPausar = document.querySelector('.container #btn_pausar');
        const mainBtnZerar = document.querySelector('.container #btn_zerar');
        const mainBtnGravar = document.querySelector('.container #btn_gravar');
        
        const fullscreenBtnIniciar = document.querySelector('#fullscreen-mode #btn_iniciar');
        const fullscreenBtnContinuar = document.querySelector('#fullscreen-mode #btn_continuar');
        const fullscreenBtnPausar = document.querySelector('#fullscreen-mode #btn_pausar');
        const fullscreenBtnZerar = document.querySelector('#fullscreen-mode #btn_zerar');
        const fullscreenBtnGravar = document.querySelector('#fullscreen-mode #btn_gravar');
        
        if (mainBtnIniciar && fullscreenBtnIniciar) {
            fullscreenBtnIniciar.style.display = mainBtnIniciar.style.display;
        }
        
        if (mainBtnContinuar && fullscreenBtnContinuar) {
            fullscreenBtnContinuar.style.display = mainBtnContinuar.style.display;
        }
        
        if (mainBtnPausar && fullscreenBtnPausar) {
            fullscreenBtnPausar.style.display = mainBtnPausar.style.display;
        }
        
        if (mainBtnZerar && fullscreenBtnZerar) {
            fullscreenBtnZerar.style.display = mainBtnZerar.style.display;
        }
        
        if (mainBtnGravar && fullscreenBtnGravar) {
            fullscreenBtnGravar.style.display = mainBtnGravar.style.display;
        }
    }
});
