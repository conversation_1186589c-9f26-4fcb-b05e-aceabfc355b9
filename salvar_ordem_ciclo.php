<?php
session_start();
include_once("conexao_POST.php");

// Verificar se recebeu os dados necessários
if (!isset($_POST['ordem']) || !isset($_POST['planejamento_id'])) {
    http_response_code(400);
    exit('Dados inválidos');
}

$ordem = json_decode($_POST['ordem'], true);
$planejamento_id = $_POST['planejamento_id'];

// Debug para verificar os dados recebidos
error_log("Ordem recebida: " . print_r($ordem, true));
error_log("Planejamento ID: " . $planejamento_id);

// Iniciar transação
pg_query($conexao, "BEGIN");

try {
    foreach ($ordem as $item) {
        $materia_id = $item['materia_id'];
        $nova_ordem = $item['ordem'];

        $sql = "UPDATE appEstudo.planejamento_materia 
                SET ordem = $nova_ordem 
                WHERE planejamento_idplanejamento = $planejamento_id 
                AND materia_idmateria = $materia_id";

        $result = pg_query($conexao, $sql);

        if (!$result) {
            throw new Exception("Erro ao atualizar ordem: " . pg_last_error($conexao));
        }

        // Debug para verificar cada atualização
        error_log("Atualizando matéria $materia_id para ordem $nova_ordem");
    }

    pg_query($conexao, "COMMIT");
    echo json_encode(['success' => true]);

} catch (Exception $e) {
    pg_query($conexao, "ROLLBACK");
    error_log("Erro na atualização da ordem: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
?>