<?php
// upload_mindmap.php
session_start();
include_once("assets/config.php");

if (!isset($_SESSION['idusuario'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Usuário não autenticado']);
    exit();
}

// Recebe os dados do POST
$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['flashcardId']) || !isset($data['imageData'])) {
    echo json_encode(['error' => 'Dados incompletos']);
    exit();
}

$flashcard_id = (int)$data['flashcardId'];
$image_data = $data['imageData'];

// Verificar se o flashcard pertence ao usuário
$query_check = "
    SELECT f.id 
    FROM appestudo.flashcards f
    JOIN appestudo.flashcard_decks d ON d.id = f.deck_id
    WHERE f.id = $1 AND d.usuario_id = $2";

$result_check = pg_query_params($conexao, $query_check, array($flashcard_id, $_SESSION['idusuario']));

if (!pg_fetch_assoc($result_check)) {
    echo json_encode(['error' => 'Card não encontrado ou sem permissão']);
    exit();
}

// Remove mapa mental antigo se existir
$query_delete = "DELETE FROM appestudo.flashcard_mindmaps WHERE flashcard_id = $1";
pg_query_params($conexao, $query_delete, array($flashcard_id));

// Insere novo mapa mental
$query_insert = "
    INSERT INTO appestudo.flashcard_mindmaps (flashcard_id, imagem_base64) 
    VALUES ($1, $2)";

$result = pg_query_params($conexao, $query_insert, array($flashcard_id, $image_data));

if ($result) {
    echo json_encode(['success' => true, 'message' => 'Mapa mental salvo com sucesso']);
} else {
    echo json_encode(['error' => 'Erro ao salvar mapa mental']);
}
?>