<?php
// Verificar se a sessão está ativa
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}


include_once '../conexao_POST.php'; // Fornece $conexao
include_once '../includes/auth.php';   // Fornece verificarAutenticacao()

// Verifica a autenticação do usuário. Se não for válido, o script é encerrado em verificarAutenticacao().
verificarAutenticacao($conexao);

// Define a constante para permitir a inclusão segura de processa_index.php
define('MEU_SISTEMA_PHP_EXECUCAO_VALIDA', true);
// processa_index.php usa $_SESSION['idusuario'] (já validado) e define $id_planejamento.
// Ele também lida com o caso de planejamento não encontrado, encerrando o script se necessário.
include_once '../processa_index.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['idusuario']) || !isset($_SESSION['validacao']) || $_SESSION['validacao'] !== true) {
    // Registrar informações de depuração
    error_log("Redirecionamento para login: idusuario=" . (isset($_SESSION['idusuario']) ? $_SESSION['idusuario'] : 'não definido') . 
              ", validacao=" . (isset($_SESSION['validacao']) ? $_SESSION['validacao'] : 'não definida'));
    
    // Redirecionar para a página de login
    echo '<script>
        alert("Sessão expirada ou inválida. Por favor, faça login novamente.");
        window.location.href = "../login_index.php";
    </script>';
    exit;
}


// Garantir que o ID do usuário está disponível
$id_usuario = $_SESSION['idusuario'];

// Consultar os dados do planejamento relacionado ao usuario logado
$query_consultar_idplanejamento = "SELECT p.idplanejamento
        FROM appEstudo.planejamento p
        WHERE p.usuario_idusuario = $1";
$resultado_idplanejamento = pg_query_params($conexao, $query_consultar_idplanejamento, array($id_usuario));

$id_planejamento = null; // Inicializa a variável
if ($resultado_idplanejamento && pg_num_rows($resultado_idplanejamento) > 0) {
    $row = pg_fetch_assoc($resultado_idplanejamento);
    $id_planejamento = $row['idplanejamento'];
} else {
    // Tratar erro ou ausência de planejamento
    error_log("Erro ao buscar planejamento ou nenhum planejamento encontrado para o usuário ID: " . $id_usuario);
    echo "Erro ao buscar planejamento ou nenhum planejamento encontrado para o usuário.";
    exit;
}

// --- MOVER A CONTAGEM DE MATÉRIAS PARA CÁ ---
$total_materias = 0; // Inicializa a contagem
if ($id_planejamento) {
    // Query para contar as matérias associadas ao planejamento
    $query_contar_materias = "SELECT COUNT(m.idmateria) as total
                              FROM appEstudo.materia m
                              INNER JOIN appEstudo.planejamento_materia pm ON m.idmateria = pm.materia_idmateria
                              WHERE pm.planejamento_idplanejamento = $1";
    $resultado_contagem = pg_query_params($conexao, $query_contar_materias, array($id_planejamento));

    if ($resultado_contagem && pg_num_rows($resultado_contagem) > 0) {
        $row_contagem = pg_fetch_assoc($resultado_contagem);
        $total_materias = (int)$row_contagem['total']; // Converte para inteiro
    } else {
        // Tratar erro na contagem, se necessário
        error_log("Erro ao contar matérias para o planejamento ID: " . $id_planejamento . " Erro: " . pg_last_error($conexao));
    }
}
// --- FIM DA CONTAGEM DE MATÉRIAS ---

// Consultar as matérias relacionadas ao planejamento (para a lista de checkboxes - esta query pode ser removida se não for usada em outro lugar antes do loop)
// $query_consultar_materias = "SELECT m.idmateria, m.nome ... "; // Query original que pode ser redundante agora

?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src='../fullcalendar-6.1.8/dist/index.global.js'></script>
    <script src='../fullcalendar-6.1.8/packages/core/locales/pt-br.global.js'></script>
    <!-- Adicionar SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <!-- Adicionando a fonte Quicksand -->
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Anton&family=Orbitron:wght@400;500;600;700;800;900&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Ysabeau+SC:wght@1;100;200;300;400;500;600;700;800;900;1000&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courier+Prime:ital,wght@0,400;0,700;1,400;1,700&display=swap"
          rel="stylesheet">
    <!-- Adicionar Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.6/jquery.inputmask.min.js"></script>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css"
          integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"
            integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy"
            crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js"></script>
    <title>Calendário - Agenda Pessoal</title>
    <link rel="stylesheet" href="./index_agenda.css">
    <script src="./caminho_para_fullcalendar.js"></script>
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">

</head>

<div class="header">
        <div class="header-left">
            <div class="logo">
                <img src="../logo/logo_vertical.png" alt="Logo" class="logo-light">
                <img src="../logo/logo_vertical.png" alt="Logo" class="logo-dark">
            </div>
        </div>
        <div class="header-right">
            <div class="user-info">
                <i class="fas fa-user-circle"></i>
                <span class="user-name">        <?php
        // A variável $nome_usuario precisa ser definida antes daqui, talvez buscando do banco ou da sessão.
        // Exemplo: $nome_usuario = $_SESSION['nome_usuario'] ?? 'Usuário';
        echo "$nome_usuario";
        ?></span>
            </div>
            <div class="theme-toggle">
                <button id="theme-toggle-btn" class="theme-btn" title="Alternar tema">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </div>
    </div>

<body>
    <!-- Overlay de carregamento -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="spinner"></div>
        <div class="loading-text">
            Processando eventos...<br>
            <span id="loadingMessage">Preparando calendário...</span>
        </div>
    </div>

    <!-- ... existing code ... -->

<?php
// --- Exibir Mensagem Flash da Sessão ---
if (isset($_SESSION['flash_message'])):
    $flash_message = $_SESSION['flash_message'];
    $status_class = '';
    switch ($flash_message['status']) {
        case 'success':
            $status_class = 'alert-success';
            break;
        case 'warning':
            $status_class = 'alert-warning';
            break;
        case 'danger':
            $status_class = 'alert-danger';
            break;
        default:
            $status_class = 'alert-info';
    }
?>
    <div id="flash-alert" class="alert <?php echo $status_class; ?> alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; left: 50%; transform: translateX(-50%); z-index: 1050; width: auto; max-width: 80%;">
        <?php echo htmlspecialchars($flash_message['message']); ?>
        
        <!-- Botão de fechar corrigido para Bootstrap 4 -->
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
<?php
    // Remover a mensagem da sessão para que não apareça novamente
    unset($_SESSION['flash_message']);
endif;
// --- Fim da Mensagem Flash ---

// Remover ou comentar o bloco antigo que lia $_GET['status'] e $_GET['message']
/*
<?php if (isset($_GET['status']) && isset($_GET['message'])): ?>
    // ... código antigo do alerta baseado em GET ...
<?php endif; ?>
*/
?>


<div class="container">
<header class="header_centro">
            <div class="menu-icon">
                <i class="fas fa-robot fa-3x" style="color: var(--primary); margin-bottom: 15px;"></i>
            </div>
            <h1>Criar Estudo Automático na Agenda</h1>
            <p>Distribua suas matérias de forma equilibrada e alcance melhores resultados</p>
        </header>
    <div id="header" style="border: 2px solid #2b2723; padding: 20px; border-radius: 10px; box-shadow: 0px 4px 8px rgb(43,39,35);">
        <!-- Formulário com método POST e inclusão do ID do usuário como campo oculto -->
        <form method="POST" action="processar_calendario_automatico.php" id="formCalendario">
            <!-- Adicionar campos ocultos para manter a sessão -->
            <input type="hidden" name="id_usuario" value="<?php echo $id_usuario; ?>">
            <input type="hidden" name="id_planejamento" value="<?php echo $id_planejamento; ?>">
            <input type="hidden" name="sessao_token" value="<?php echo session_id(); ?>">

            <label for="tipo_evento" class="titulo">Tipo de Evento:</label>
            <select id="tipo_evento" name="tipo_evento" class="campoTipo" required>
                <option value="Planejamento">Planejamento</option>
            </select>

            <input type="hidden" name="autoFill" value="true">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px;">
                <div>
                    <label for="start_date" class="titulo">Data de Início:</label>
                    <input class="titulo" type="date" id="start_date" name="start_date" required>
                </div>
                <div>
                    <label for="end_date" class="titulo">Data de Término (Máximo 15 Dias):</label>
                    <input class="titulo" type="date" id="end_date" name="end_date" required>
                </div>
                <div>
                    <!-- Label e Input atualizados - AGORA $total_materias ESTÁ DEFINIDA -->
                    <label for="materias_per_day" class="titulo">Matérias por Dia (Máx: <?php echo $total_materias > 0 ? $total_materias : 1; ?>):</label>
                    <input class="titulo" type="number" id="materias_per_day" name="materias_per_day" min="1" max="<?php echo $total_materias > 0 ? $total_materias : 1; ?>" required>
                </div>
            </div>

            <div style="margin-top: 20px;" class="materias-container">
                <label class="titulo"><strong>Matérias </strong>(Selecione Quais Deseja Utilizar Para Preenchimento)<strong>:</strong></label><br>
                
                <!-- Botões para Selecionar/Limpar Todas (Configuração Básica) -->
                <div class="botoes-selecao mb-2">
                    <button type="button" class="btn-selecionar btn-sm" onclick="selecionarTodasMateriasBasico()">Selecionar Todas</button>
                    <button type="button" class="btn-limpar btn-sm" onclick="deselecionarTodasMateriasBasico()">Limpar Seleção</button>
                </div>
                
                <?php
                // Query para listar as matérias para seleção
                $query_consultar_materias_lista = "SELECT m.nome, m.idmateria, m.cor
                                             FROM appEstudo.materia m
                                             INNER JOIN appEstudo.planejamento_materia pm ON m.idmateria = pm.materia_idmateria
                                             WHERE pm.planejamento_idplanejamento = $1";
                $resultado_materias_lista = pg_query_params($conexao, $query_consultar_materias_lista, array($id_planejamento));

                // Não precisamos mais calcular $total_materias aqui

                // Exibir as matérias com caixas de seleção
                if ($resultado_materias_lista && pg_num_rows($resultado_materias_lista) > 0) {
                    // Adicionar um container para facilitar a seleção via JS
                    echo "<div id='materias-basico-container'>"; 
                    while ($row = pg_fetch_assoc($resultado_materias_lista)) {
                        $nomeMateria = htmlspecialchars($row['nome']); // Usar htmlspecialchars para segurança
                        // Adicionar uma classe comum para fácil seleção
                        echo "<input type='checkbox' name='materias_selecionadas[]' value='$nomeMateria' class='materia-basico-checkbox'> <span class='titulo'>$nomeMateria</span><br>";
                    }
                    echo "</div>"; // Fechar o container
                } else {
                    // Se $total_materias foi 0, esta mensagem será exibida
                    echo "<p>Nenhuma matéria encontrada para este planejamento.</p>";
                }
                ?>
            </div>

            <div id="config_basica" class="mt-4">
                <div style="margin-top: 15px;">
                    <label class="titulo">Dias da semana para estudo:</label>
                    <div class="dias-semana-container">
                        <label><input type="checkbox" name="dias_semana[]" value="1"> Segunda</label>
                        <label><input type="checkbox" name="dias_semana[]" value="2"> Terça</label>
                        <label><input type="checkbox" name="dias_semana[]" value="3"> Quarta</label>
                        <label><input type="checkbox" name="dias_semana[]" value="4"> Quinta</label>
                        <label><input type="checkbox" name="dias_semana[]" value="5"> Sexta</label>
                        <label><input type="checkbox" name="dias_semana[]" value="6"> Sábado</label>
                        <label><input type="checkbox" name="dias_semana[]" value="0"> Domingo</label>
                    </div>
                </div>
            </div>

            <div style="margin-top: 15px;">
                <label class="titulo">Configuração avançada de dias e matérias:</label>
                <div class="config-avancada-toggle">
                    <input type="checkbox" id="usar_config_avancada" name="usar_config_avancada" onchange="toggleConfigAvancada()">
                    <label for="usar_config_avancada">Usar configuração avançada (associar matérias a dias específicos)</label>
                    <button type="button" class="btn-help" onclick="mostrarAjudaConfigAvancada()" title="Ajuda sobre configuração avançada">
                        <i class="fas fa-question-circle"></i>
                    </button>
                </div>

                <!-- Modal de ajuda -->
                <div class="modal fade" id="modalAjudaConfigAvancada" tabindex="-1" role="dialog" aria-labelledby="modalAjudaLabel" aria-hidden="true">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                            <h5 class="modal-title text-center w-100" id="modalAjudaLabel">Como usar a Configuração Avançada</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Fechar">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                <p>A configuração avançada permite que você associe matérias específicas a dias específicos da semana.</p>
                                <ol>
                                    <li>Ative a opção "Usar configuração avançada".</li>
                                    <li>Para cada dia da semana, marque a caixa "Ativar este dia" se deseja estudar nesse dia.</li>
                                    <li>Selecione as matérias que deseja estudar em cada dia ativado.</li>
                                    <li>O sistema criará eventos apenas para as matérias selecionadas nos dias específicos.</li>
                                </ol>
                                <p><strong>Exemplo:</strong> Se você quiser estudar Matemática e Física às segundas-feiras, e História e Geografia às terças-feiras, esta configuração permite isso.</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Entendi</button>
                            </div>
                        </div>
                    </div>
                </div>

                <script>
                    function mostrarAjudaConfigAvancada() {
                        $('#modalAjudaConfigAvancada').modal('show');
                    }
                </script>

                <div id="config_avancada" class="config-avancada-container" style="display: none;">
                    <h3 class="titulo">Selecione as matérias para cada dia da semana</h3>
                    <p>
                        Ative os dias que deseja estudar e selecione as matérias específicas para cada dia
                    </p>
                    
                    <div class="dias-semana-avancado">
                        <?php
                        $dias_nomes = [

                            1 => 'Segunda-feira',
                            2 => 'Terça-feira',
                            3 => 'Quarta-feira',
                            4 => 'Quinta-feira',
                            5 => 'Sexta-feira',
                            6 => 'Sábado',
                            0 => 'Domingo'
                        ];
                        
                        foreach ($dias_nomes as $num_dia => $nome_dia) {
                            echo "<div class='dia-config-avancada' id='dia-config-avancada-$num_dia'>";
                            echo "<h5>$nome_dia</h5>";
                            echo "<div class='dia-materias'>";
                            echo "<input type='checkbox' id='dia_ativo_$num_dia' name='dias_ativos[]' value='$num_dia' onchange=\"toggleDiaMaterias($num_dia)\">";
                            echo "<label for='dia_ativo_$num_dia'>Ativar este dia</label>";
                            echo "</div>";
                            
                            echo "<div id='materias_dia_$num_dia' class='materias-dia' style='display: none; opacity: 0; max-height: 0; overflow: hidden;'>";
                            echo "<div class='botoes-selecao'>";
                            echo "<button type='button' class='btn-selecionar' onclick='selecionarTodasMaterias($num_dia)'>Selecionar</button>";
                            echo "<button type='button' class='btn-limpar' onclick='deselecionarTodasMaterias($num_dia)'>Limpar</button>";
                            echo "</div>";
                            
                            // Consultar as matérias do usuário
                            $query_consultar_materias = "SELECT m.nome FROM appEstudo.materia m 
                                     INNER JOIN appEstudo.planejamento_materia pm ON m.idmateria = pm.materia_idmateria
                                     WHERE pm.planejamento_idplanejamento = (SELECT p.idplanejamento FROM appEstudo.planejamento p WHERE p.usuario_idusuario = $id_usuario)";
                            $resultado_materias = pg_query($conexao, $query_consultar_materias);
                            
                            if (pg_num_rows($resultado_materias) > 0) {
                                echo "<div class='materias-lista'>";
                                while ($row = pg_fetch_assoc($resultado_materias)) {
                                    $nomeMateria = htmlspecialchars($row['nome']);
                                    echo "<div class='materia-checkbox'>";
                                    echo "<input type='checkbox' id='materia_{$num_dia}_{$nomeMateria}' name='materias_dia[$num_dia][]' value='$nomeMateria'>";
                                    echo "<label for='materia_{$num_dia}_{$nomeMateria}'>$nomeMateria</label>";
                                    echo "</div>";
                                }
                                echo "</div>";
                            } else {
                                echo "<p>Nenhuma matéria encontrada.</p>";
                            }
                            
                            echo "</div>"; // Fim de materias_dia
                            echo "</div>"; // Fim de dia-config-avancada
                        }
                        ?>
                    </div>
                </div>
            </div>

            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 20px;">
     
                <input type="hidden" id="refresh_page" name="refresh_page" value="../index.php">
                <button type="submit" class="btn-submit-agenda btn-pulse" onclick="refreshPrincipal()">
                    <i class="fas fa-calendar-check"></i> Preencher Agenda
                </button>
                <button class="btn-fechar-pagina" onclick="window.close()">
  <i class="fas fa-times"></i> Fechar Página
</button>
                </div>
   
            </div>
        </form>


        <!-- Legenda de tipos de evento -->
        <div class="legenda-eventos" style="display: flex; justify-content: center; align-items: justify; margin-top: 10px;">
            <div class="legenda-item" style="margin-right: 20px;">
                <div class="legenda-cor" style="background-color: #1976D2;"></div>
                <div class="legenda-texto">Faculdade</div>

                <div class="legenda-cor" style="background-color: gray;"></div>
                <div class="legenda-texto">Trabalho</div>

                <div class="legenda-cor" style="background-color: blue;"></div>
                <div class="legenda-texto">Concurso</div>

                <div class="legenda-cor" style="background-color: #00796B;"></div>
                <div class="legenda-texto">Pessoal</div>

                <div class="legenda-cor" style="background-color: #FFD700;"></div>
                <div class="legenda-texto">Planejamento</div>

                <div class="legenda-cor legenda-pendente"></div>
                <div class="legenda-texto">Evento Pendente</div>

                <div class="legenda-cor legenda-realizado"></div>
                <div class="legenda-texto">Evento Realizado</div>
            </div>
        </div>

    </div>
</div>

<div style="display: flex; justify-content: center; align-items: center;" id="calendar-container">
    <div id="calendar"></div>
</div>

<!-- Modal para exibir detalhes do evento -->
<div id="modalEvento" class="modal-especifico" style="display: none;">
    <div class="modal-especifico-overlay"></div>
    <div class="modal-especifico-content">
        <button class="modal-especifico-close"><i class="fas fa-times"></i></button>

        <h3 class="modal-especifico-titulo">Título do Evento</h3>

        <div class="status-section">
            <span class="modal-especifico-badge realizado">
                <i class="fas fa-check-circle"></i> Realizado
            </span>
            <span class="modal-especifico-badge pendente">
                <i class="fas fa-clock"></i> Pendente
            </span>
        </div>

        <div class="modal-especifico-detalhes">
            <!-- Detalhes do evento serão inseridos aqui -->
        </div>

        <!-- Removido: Container do checkbox "Marcar como realizado" -->

        <div class="modal-footer d-flex justify-content-center">
    <button class="btn btn-danger excluirEventoModal">
        <i class="fas fa-trash"></i> Excluir
    </button>
</div>
    </div>
</div>

<!-- Adicionar estilos para o modal -->
<style>
.modal-especifico {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-especifico-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-especifico-content {
    position: relative;
    background-color: var(--white);
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    width: 90%;
    z-index: 1001;
}

.modal-especifico-close {
    position: absolute;
    top: 10px;
    right: 10px;
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
}

.modal-especifico-titulo {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.modal-especifico-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 4px;
    margin-bottom: 15px;
    font-weight: bold;
}

.modal-especifico-badge.realizado {
    background-color: #28a745;
    color: white;
}

.modal-especifico-badge.pendente {
    background-color: var(--danger);
    color: white;
}

.button-group {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.button-group button {
    padding: 8px 15px;
}
</style>

<script>
// Adicionar evento para fechar o modal ao clicar no botão de fechar
document.addEventListener('DOMContentLoaded', function() {
    const closeButton = document.querySelector('.modal-especifico-close');
    if (closeButton) {
        closeButton.addEventListener('click', closeModal);
    }
    
    // Fechar o modal ao clicar fora dele (no overlay)
    const modalOverlay = document.querySelector('.modal-especifico-overlay');
    if (modalOverlay) {
        modalOverlay.addEventListener('click', closeModal);
    }
});
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Capturar o formulário
    const form = document.getElementById('formCalendario');

    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault(); // Impedir o envio tradicional do formulário

            // --- INÍCIO DA VALIDAÇÃO ---
            const usarConfigAvancada = document.getElementById('usar_config_avancada').checked;
            const materiasPerDayInput = document.getElementById('materias_per_day');
            const materiasSelecionadasCheckboxes = form.querySelectorAll('input[name="materias_selecionadas[]"]:checked');
            const numMateriasSelecionadas = materiasSelecionadasCheckboxes.length;
            const numMateriasPorDia = parseInt(materiasPerDayInput.value, 10);

            // Validação das datas
            const startDate = document.getElementById('start_date').value;
            const endDate = document.getElementById('end_date').value;
            if (!startDate || !endDate) {
                 Swal.fire({
                    title: 'Erro de Validação',
                    text: 'Por favor, preencha as datas de início e término.',
                    icon: 'warning',
                    confirmButtonText: 'OK',
                    confirmButtonColor: '#dc3545'
                });
                return;
            }

            // Validação de matérias
            if (!usarConfigAvancada) {
                // Validações para configuração básica...
                if (isNaN(numMateriasPorDia) || numMateriasPorDia <= 0) {
                    Swal.fire({
                        title: 'Erro de Validação',
                        text: 'Por favor, informe um número válido de matérias por dia.',
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#dc3545'
                    });
                    return;
                }

                if (numMateriasSelecionadas !== numMateriasPorDia) {
                    Swal.fire({
                        title: 'Erro de Validação',
                        text: `Você informou ${numMateriasPorDia} matérias por dia, mas selecionou ${numMateriasSelecionadas}. A quantidade deve ser a mesma.`,
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#dc3545'
                    });
                    return;
                }
                
                // Validação de dias da semana selecionados
                const diasSemanaCheckboxes = form.querySelectorAll('input[name="dias_semana[]"]:checked');
                if (diasSemanaCheckboxes.length === 0) {
                    Swal.fire({
                        title: 'Erro de Validação',
                        text: 'Por favor, selecione pelo menos um dia da semana para estudo.',
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#dc3545'
                    });
                    return;
                }
                
                console.log("Validação básica de matérias OK.");
            } else {
                // Validações para configuração avançada...
                const diasAtivosCheckboxes = form.querySelectorAll('input[name="dias_ativos[]"]:checked');
                if (diasAtivosCheckboxes.length === 0) {
                    Swal.fire({
                        title: 'Erro de Validação',
                        text: 'Por favor, ative pelo menos um dia da semana na configuração avançada.',
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#dc3545'
                    });
                    return;
                }

                // Verificar se cada dia ativo tem matérias selecionadas
                let diaAtivoSemMateria = false;
                diasAtivosCheckboxes.forEach(checkbox => {
                    const diaNum = checkbox.value;
                    const materiasDoDia = form.querySelectorAll(`input[name="materias_dia[${diaNum}][]"]:checked`);
                    if (materiasDoDia.length === 0) {
                        diaAtivoSemMateria = true;
                    }
                });

                if (diaAtivoSemMateria) {
                     Swal.fire({
                        title: 'Erro de Validação',
                        text: 'Um ou mais dias ativados na configuração avançada não possuem matérias selecionadas. Por favor, verifique.',
                        icon: 'warning',
                        confirmButtonText: 'OK',
                        confirmButtonColor: '#dc3545'
                    });
                    return;
                }
                console.log("Validação avançada de matérias OK.");
            }
            // --- FIM DA VALIDAÇÃO ---

            // Se todas as validações passaram, mostrar o overlay de carregamento
            showLoading();
            
            // Atualizar a mensagem após 1 segundo
            setTimeout(function() {
                document.getElementById('loadingMessage').textContent = 'Criando eventos no calendário...';
            }, 1000);
            
            // Atualizar a mensagem após 3 segundos
            setTimeout(function() {
                document.getElementById('loadingMessage').textContent = 'Salvando no banco de dados...';
            }, 3000);

            // Disparar atualização automática da página index.php
            localStorage.setItem('recarregarIndex', Date.now().toString());

            // Enviar o formulário
            form.removeEventListener('submit', arguments.callee);
            form.submit();
        });
    }
    
    // --- Auto-fechar Alerta Flash ---
    const flashAlert = document.getElementById('flash-alert');
    if (flashAlert) {
        setTimeout(() => {
            const alertInstance = new bootstrap.Alert(flashAlert);
            if (alertInstance) {
                alertInstance.close();
            }
        }, 5000);
    }
});
</script>

</body>
</html>


<script>
function atualizarPaginaPrincipal() {
    // Tenta encontrar a página principal em outras abas
    var paginasPrincipais = window.opener ? window.opener.document.getElementsByTagName('iframe') : [];
    
    // Procura por todas as abas abertas
    if (window.opener) {
        // Atualiza a página principal se encontrada
        window.opener.location.href = '../index.php';
    }
}

// Adiciona o evento de submit ao formulário
document.getElementById('formCalendario').addEventListener('submit', function(e) {
    // Não previne o envio do formulário
    // Atualiza a página principal
    atualizarPaginaPrincipal();
});
</script>
</script>
<script>
document.addEventListener('DOMContentLoaded', () => {
    const themeToggleBtn = document.getElementById('theme-toggle-btn');
    const themeIcon = themeToggleBtn.querySelector('i');
    
    // Verifica se há preferência salva
    const currentTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', currentTheme);
    updateIcon(currentTheme);
    
    themeToggleBtn.addEventListener('click', () => {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        updateIcon(newTheme);
    });
    
    function updateIcon(theme) {
        if (theme === 'dark') {
            themeIcon.classList.remove('fa-moon');
            themeIcon.classList.add('fa-sun');
        } else {
            themeIcon.classList.remove('fa-sun');
            themeIcon.classList.add('fa-moon');
        }
    }
});
</script>
</script>