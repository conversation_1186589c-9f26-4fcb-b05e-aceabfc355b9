<?php
// ver_baralhos.php
session_start();
include_once("assets/config.php");
include_once("helpers.php");

if (!isset($_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit();
}

if (!isset($_GET['categoria'])) {
    header("Location: flashcards.php");
    exit();
}

$categoria_id = (int)$_GET['categoria'];

// Buscar informações da categoria
$query_categoria = "SELECT nome FROM appestudo.flashcard_categories WHERE id = $1";
$result_categoria = pg_query_params($conexao, $query_categoria, array($categoria_id));
$categoria = pg_fetch_assoc($result_categoria);

if (!$categoria) {
    header("Location: flashcards.php");
    exit();
}

// Consulta para buscar o nome do usuário com base no ID
$query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = " . $_SESSION['idusuario'];
$resultado_nome = pg_query($conexao, $query_buscar_nome);

if ($resultado_nome && pg_num_rows($resultado_nome) > 0) {
    $row = pg_fetch_assoc($resultado_nome);
    $nome_usuario = $row['nome'];
} else {
    $nome_usuario = "Usuário";
}

// Verificar se o usuário é administrador
$is_admin = checkAdmin($conexao, $_SESSION['idusuario']);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Baralhos - <?php echo htmlspecialchars($categoria['nome']); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700&family=Quicksand:wght@400;500;600;700&family=Varela+Round&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/ver_baralhos.css">
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
</head>
<body>
    <div class="header">
        <div class="header-left">
            <div class="logo">
                <img src="../logo/logo_vertical_azul.png" alt="Estudo Off" class="logo-light">
                <img src="../logo/logo_vertical.png" alt="Estudo Off" class="logo-dark">
            </div>
        </div>
        <div class="header-right">
            <div class="user-info">
                <i class="fas fa-user-circle"></i>
                <span class="user-name"><?php echo htmlspecialchars($nome_usuario); ?></span>
            </div>

            <div class="theme-toggle">
                <button id="theme-toggle-btn" class="theme-btn" aria-label="Alternar tema">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </div>
    </div>

    <a href="flashcards.php" class="btn-back" title="Voltar">
        <i class="fas fa-arrow-left"></i>
    </a>

    <div class="container" id="app" v-cloak>
        <div class="breadcrumb">
            <i class="fas fa-home"></i>
            <span>Flashcards</span>
            <i class="fas fa-chevron-right"></i>
            <span><?php echo htmlspecialchars($categoria['nome']); ?></span>
        </div>

        <div class="clean-container">
            <!-- Header -->
            <div class="clean-header">
                <h1>Baralhos de <?php echo htmlspecialchars($categoria['nome']); ?></h1>
                <?php if($is_admin): ?>
                <a href="criar_baralho.php?categoria=<?php echo $categoria_id; ?>" class="btn-novo">
                    <i class="fas fa-plus"></i> Novo Baralho
                </a>
                <?php endif; ?>
            </div>

            <!-- Lista de Baralhos -->
            <div v-if="baralhos.length > 0" class="baralhos-grid">
                <div v-for="(baralho, index) in baralhos" 
                     :key="baralho.id" 
                     class="baralho-card fade-in"
                     :style="{'--index': index}">
                    <div class="baralho-header" :style="{ backgroundColor: baralho.cor_materia }">
                        <i class="fas fa-book"></i>
                        {{ baralho.nome_materia }}
                    </div>
                    <div class="baralho-body">
                        <div class="baralho-stats">
                            <div class="stat-item">
                                <div class="stat-number">{{ baralho.total_topicos }}</div>
                                <div class="stat-label">Tópicos</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">{{ baralho.total_cards }}</div>
                                <div class="stat-label">Cards</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number">{{ baralho.total_estudantes }}</div>
                                <div class="stat-label">Estudantes</div>
                            </div>
                        </div>
                        <div class="baralho-actions">
                            <a :href="'ver_topicos.php?baralho=' + baralho.id" class="btn btn-primary">
                                <i class="fas fa-eye"></i>Tópicos
                            </a>
                            <a :href="'estudar.php?baralho=' + baralho.id + '&categoria=' + categoriaId" class="btn btn-secondary">
                                <i class="fas fa-graduation-cap"></i> Estudar
                            </a>
                            <a v-if="isAdmin" :href="'editar_baralho.php?id=' + baralho.id" class="btn btn-secondary">
                                <i class="fas fa-edit"></i> Editar
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Estado Vazio -->
            <div v-else-if="!isLoading" class="empty-state fade-in" style="--index: 0">
                <i class="fas fa-book-open"></i>
                <p>Nenhum baralho cadastrado ainda nesta categoria.</p>
                <?php if($is_admin): ?>
                <a href="criar_baralho.php?categoria=<?php echo $categoria_id; ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Criar Primeiro Baralho
                </a>
                <?php endif; ?>
            </div>

            <!-- Estado de Carregamento -->
            <div v-if="isLoading" class="baralhos-grid">
                <div v-for="i in 3" :key="i" class="baralho-card loading-shimmer" style="height: 220px;"></div>
            </div>
        </div>
        
        <div class="footer">
            <p>© <?php echo date('Y'); ?> Sistema de Flashcards | Todos os direitos reservados</p>
        </div>
    </div>

    <!-- Vue.js via CDN -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/vue/3.2.47/vue.global.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            try {
                // 1. Aplica o tema salvo, se existir
                const savedTheme = localStorage.getItem('theme');
                if (savedTheme) {
                    document.documentElement.setAttribute('data-theme', savedTheme);
                    updateThemeIcon(savedTheme);
                }
                
                // 2. Função de atualização do ícone
                function updateThemeIcon(theme) {
                    const icon = document.querySelector('#theme-toggle-btn i');
                    if (icon) {
                        if (theme === 'dark') {
                            icon.className = 'fas fa-sun';
                        } else {
                            icon.className = 'fas fa-moon';
                        }
                    }
                }
                
                // 3. Função simples de alternância de tema
                function toggleTheme() {
                    const html = document.documentElement;
                    const currentTheme = html.getAttribute('data-theme') || 'light';
                    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                    
                    console.log(`Alternando tema: ${currentTheme} → ${newTheme}`);
                    
                    // Aplica o tema
                    html.setAttribute('data-theme', newTheme);
                    localStorage.setItem('theme', newTheme);
                    updateThemeIcon(newTheme);
                }
                
                // 4. Configura o botão de tema - com tempo suficiente para o DOM carregar
                setTimeout(function() {
                    const themeBtn = document.getElementById('theme-toggle-btn');
                    if (themeBtn) {
                        console.log('Botão de tema encontrado e configurado');
                        themeBtn.onclick = toggleTheme;
                    } else {
                        console.error('Botão de tema não encontrado!');
                    }
                }, 100);
            } catch (e) {
                console.error('Erro ao configurar tema:', e);
            }
        });

        // Dados PHP para Vue
        const categoriaId = <?php echo $categoria_id; ?>;
        const isAdmin = <?php echo $is_admin ? 'true' : 'false'; ?>;
        
        // Inicialização do Vue
        const { createApp, ref, onMounted } = Vue;
        
        createApp({
            setup() {
                const baralhos = ref([]);
                const isLoading = ref(true);
                const error = ref(null);
                
                // Função para carregar os baralhos
                const carregarBaralhos = async () => {
                    try {
                        isLoading.value = true;
                        const response = await fetch(`baralhos_api.php?categoria=${categoriaId}`);
                        
                        if (!response.ok) {
                            throw new Error('Falha ao carregar baralhos');
                        }
                        
                        const data = await response.json();
                        baralhos.value = data;
                    } catch (err) {
                        console.error('Erro:', err);
                        error.value = 'Não foi possível carregar os baralhos. Por favor, tente novamente mais tarde.';
                    } finally {
                        isLoading.value = false;
                    }
                };
                
                onMounted(() => {
                    carregarBaralhos();
                });
                
                return {
                    baralhos,
                    isLoading,
                    error,
                    categoriaId,
                    isAdmin
                };
            }
        }).mount('#app');
    </script>
</body>
</html>