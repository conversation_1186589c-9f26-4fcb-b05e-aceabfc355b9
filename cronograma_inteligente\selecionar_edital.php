<?php
session_start();
require_once 'includes/verificar_modulo.php';
require_once 'includes/mensagens.php';
verificarModulo();
if (!isset($_SESSION['idusuario'])) {
    header("Location: ../login_index.php");
    exit();
}

include_once("assets/config.php");

$usuario_id = $_SESSION['idusuario'];

// Primeiro, buscar o edital atual do usuário
$query_edital_atual = "
    SELECT e.*, ue.data_inscricao
    FROM appestudo.usuario_edital ue
    JOIN appestudo.edital e ON ue.edital_id = e.id_edital
    WHERE ue.usuario_id = $1
    ORDER BY ue.data_inscricao DESC
    LIMIT 1";

$result_edital_atual = pg_query_params($conexao, $query_edital_atual, array($usuario_id));
$edital_atual = pg_fetch_assoc($result_edital_atual);
$edital_atual_id = $edital_atual ? $edital_atual['id_edital'] : null;

// Depois, buscar todos os editais
$query_editais = "
   SELECT 
    e.*,
     e.logo_url,
    (
        SELECT COUNT(DISTINCT ce.materia_id) 
        FROM appestudo.conteudo_edital ce 
        WHERE ce.edital_id = e.id_edital
    ) as total_materias,
    (
        SELECT COUNT(*)
        FROM (
            SELECT ce.id_conteudo
            FROM appestudo.conteudo_edital ce
            WHERE ce.edital_id = e.id_edital
            AND ce.capitulo ~ '^[0-9.]+$'
        ) conteudos_validos
    ) as total_itens
FROM appestudo.edital e
ORDER BY e.ano DESC, e.nome;";

$result_editais = pg_query($conexao, $query_editais);

// Consulta para buscar o nome do usuário com base no ID
$query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = $usuario_id";
$resultado_nome = pg_query($conexao, $query_buscar_nome);

if ($resultado_nome && pg_num_rows($resultado_nome) > 0) {
    $row = pg_fetch_assoc($resultado_nome);
    $nome_usuario = $row['nome'];
} else {
    echo "Usuário não encontrado.";
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Selecionar Edital - Plano Inteligente</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Old+Standard+TT:wght@400;700&family=Crimson+Text:wght@400;600&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <style>
:root {
    /* Cores modo claro (já existentes) */
    --primary: #00008B;
    --secondary: #f8f0e3;
    --accent: #4a4a4a;
    --border: #e0e0e0;
    --text: #2c3e50;
    --active: #00008B;
    --hover: #f5f5f5;
    --primary-blue: #00008B;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --background: #f5f5f5;
    --card-background: white;
}

/* Variáveis para modo escuro */
[data-theme="dark"] {
    --primary: #4169E1;          /* Azul royal mais claro */
    --secondary: #1a1a2e;        /* Azul escuro quase preto */
    --accent: #6c7293;          /* Cinza azulado */
    --border: #2d2d42;          /* Borda escura com tom azulado */
    --text: #e4e6f0;           /* Texto claro com tom azulado */
    --active: #4169E1;         /* Mesmo que primary */
    --hover: #232338;          /* Fundo hover com tom azulado */
    --primary-blue: #4169E1;    /* Mesmo que primary */
    --shadow-color: rgba(0, 0, 0, 0.3);
    --background: #0a0a1f;      /* Fundo principal escuro com tom azulado */
    --card-background: #13132b;  /* Fundo dos cards escuro com tom azulado */
}

        body {
            font-family: 'Quicksand', sans-serif;
            background-color: var(--hover);
            color: var(--text);
            line-height: 1.6;
            margin: 0;
            min-height: 100vh;
            box-sizing: border-box;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: white;
            border-bottom: 1px solid var(--border);
            margin-bottom: 30px;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo img {
            height: 50px;
            width: auto;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 15px;
            background: var(--hover);
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .user-info i {
            color: var(--primary);
            font-size: 1.2rem;
        }

        .user-name {
            color: var(--text);
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .header {
                padding: 10px;
            }

            .logo img {
                height: 40px;
            }

            .user-info {
                font-size: 0.8rem;
                padding: 6px 12px;
            }
        }

        .btn-voltar {
            position: fixed;
            top: 140px;
            left: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: white;
            border: 1px solid var(--border);
            border-radius: 50%;
            box-shadow: 0 2px 4px var(--shadow-color);
            color: var(--primary);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .btn-voltar:hover {
            background: var(--primary);
            color: white;
            transform: scale(1.1);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        h1 {
            font-family: 'Quicksand', sans-serif;
            text-align: center;
            color: var(--primary);
            font-size: 2rem;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .search-section {
            background: white;
            padding: 20px;
            margin: 0 0 30px 0;
            border: 1px solid var(--border);
            box-shadow: 0 2px 4px var(--shadow-color);
            border-radius: 8px;
            overflow: visible;
        }

        .search-input {
            width: calc(100% - 24px);
            padding: 12px;
            border: 1px solid var(--border);
            border-radius: 8px;
            font-family: 'Quicksand', sans-serif;
            font-size: 1rem;
            margin-bottom: 15px;
            background-color: white;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(0, 0, 139, 0.1);
        }

        .editais-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .edital-card {
            background: white;
            border: 1px solid var(--border);
            padding: 20px;
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .edital-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        .edital-card.selected {
            border-color: var(--primary);
            background: linear-gradient(45deg, white, var(--hover));
        }

        .edital-card.selected::before {
            content: "✓ Selecionado";
            position: absolute;
            top: 10px;
            right: 10px;
            background: var(--primary);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.8rem;
            font-family: 'Quicksand', sans-serif;
        }

        .edital-header {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(139, 69, 19, 0.2);
        }

        .edital-title {
            font-family: 'Quicksand', sans-serif;
            color: var(--primary);
            font-size: 1.2rem;
            margin: 0;
            font-weight: 600;
        }

        .edital-info {
            margin-bottom: 15px;
        }

        .edital-info p {
            margin: 5px 0;
            font-size: 0.95rem;
        }

        .edital-stats {
            display: flex;
            justify-content: space-between;
            padding-top: 10px;
            border-top: 1px solid rgba(139, 69, 19, 0.2);
            font-size: 0.9rem;
        }

        .btn-select {
            width: 100%;
            padding: 12px;
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 8px;
            font-family: 'Quicksand', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 15px;
            font-weight: 600;
        }

        .btn-select:hover {
            background: var(--primary-blue);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        .btn-select:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        .current-edital {
            background: white;
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid var(--primary);
            border-radius: 8px;
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .current-edital h2 {
            color: var(--primary);
            font-family: 'Quicksand', sans-serif;
            margin-top: 0;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            body {
                padding: 20px;
            }

            .editais-grid {
                grid-template-columns: 1fr;
            }

            .btn-voltar {
                top: 10px;
                left: 10px;
                width: 40px;
                height: 40px;
            }
        }

        .flow-info {
            background: white;
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid var(--border);
            box-shadow: 0 2px 4px var(--shadow-color);
            border-radius: 8px;
        }

        .steps-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
            max-width: 800px;
            margin: 0 auto;
            position: relative;
        }

        .steps-container::before {
            content: "";
            position: absolute;
            top: 25px;
            left: 50px;
            right: 50px;
            height: 2px;
            background: rgba(139, 69, 19, 0.2);
            z-index: 0;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            z-index: 1;
        }

        .step-number {
            width: 50px;
            height: 50px;
            background: white;
            border: 1px solid var(--border);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Quicksand', sans-serif;
            font-size: 1.2rem;
            color: var(--text);
        }

        .step.active .step-number {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        .step-text {
            font-family: 'Quicksand', sans-serif;
            color: var(--text);
            font-size: 0.9rem;
            text-align: center;
        }

        .step.active .step-text {
            color: var(--primary);
            font-weight: 600;
        }

        .intro-text {
            text-align: center;
            margin-bottom: 30px;
            font-family: 'Quicksand', sans-serif;
            color: var(--text);
            font-style: italic;
        }

        @media (max-width: 768px) {
            .steps-container {
                flex-direction: column;
                gap: 10px;
            }

            .steps-container::before {
                width: 2px;
                height: auto;
                top: 50px;
                bottom: 50px;
                left: 24px;
                right: auto;
            }

            .step {
                width: 100%;
                flex-direction: row;
                gap: 20px;
            }

            .step-text {
                text-align: left;
            }
        }

        /* Estilos para o botão de tema */
.theme-toggle {
    margin-left: 15px;
}

.theme-btn {
    background: transparent;
    border: none;
    color: var(--primary);
    cursor: pointer;
    font-size: 1.2rem;
    padding: 8px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.theme-btn:hover {
    background: var(--hover);
}

/* Atualize os componentes existentes para usar as variáveis */
body {
    background-color: var(--background);
    color: var(--text);
}

.header {
    background: var(--card-background);
    border-color: var(--border);
}

.edital-card {
    background: var(--card-background);
    border-color: var(--border);
    color: var(--text);
}

.search-section {
    background: var(--card-background);
    border-color: var(--border);
}

.search-input {
    background: var(--card-background);
    border-color: var(--border);
    color: var(--text);
}

.current-edital {
    background: var(--card-background);
    border-color: var(--primary);
}

.flow-info {
    background: var(--card-background);
    border-color: var(--border);
}

.step-number {
    background: var(--card-background);
    border-color: var(--border);
    color: var(--text);
}

.edital-card.selected {
    background: linear-gradient(45deg, var(--card-background), var(--hover));
}

.user-info {
    background: var(--hover);
    color: var(--text);
}

.btn-voltar {
    background: var(--card-background);
    border-color: var(--border);
}

/* Ajuste para textos e cores específicas */
.edital-title {
    color: var(--primary);
}

.step.active .step-text {
    color: var(--primary);
}

.intro-text {
    color: var(--text);
}

/* Estilos para os logos */
.logo {
    position: relative;
}

.logo img {
    height: 50px;
    width: auto;
    transition: opacity 0.3s ease;
}

.logo-dark {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
}

/* Controle de visibilidade baseado no tema */
[data-theme="dark"] .logo-light {
    opacity: 0;
}

[data-theme="dark"] .logo-dark {
    opacity: 1;
}

.btn-select .fas {
    transition: transform 0.3s ease;
}

.btn-select:hover .fas {
    transform: translateX(5px);
}

.btn-select.selected {
    background: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.edital-logo {
    width: 100%;
    height: 120px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--card-background);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--border);
}

.edital-logo-img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    padding: 10px;
}

.edital-logo-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--hover);
    color: var(--primary);
    font-size: 2rem;
}

/* Ajuste o layout do card para acomodar a logo */
.edital-card {
    display: flex;
    flex-direction: column;
}

/* Adicione uma animação suave na logo */
.edital-logo-img {
    transition: transform 0.3s ease;
}

.edital-card:hover .edital-logo-img {
    transform: scale(1.05);
}
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <div class="logo">
                <img src="../logo/logo_vertical_azul.png" alt="Logo" class="logo-light">
            <img src="../logo/logo_vertical.png" alt="Logo" class="logo-dark">
            </div>
        </div>
        <div class="header-right">
            <div class="user-info">
                <i class="fas fa-user-circle"></i>
                <span class="user-name"><?php echo htmlspecialchars($nome_usuario); ?></span>
            </div>

            <div class="theme-toggle">
    <button id="theme-toggle-btn" class="theme-btn">
        <i class="fas fa-moon"></i>
    </button>
</div>
        </div>
    </div>
    <a href="index.php" class="btn-voltar">
    <i class="fas fa-arrow-left"></i>
</a>

<div class="container">
    <h1>Selecionar Edital</h1>

    <!-- Adicionar aqui os novos elementos -->
    <div class="flow-info">
        <div class="steps-container">
            <div class="step active">
                <div class="step-number">1</div>
                <div class="step-text">Selecionar Edital</div>
            </div>
            <div class="step">
                <div class="step-number">2</div>
                <div class="step-text">Selecionar Conteúdos</div>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-text">Configurar Plano</div>
            </div>
            <div class="step">
                 <div class="step-number">4</div>
                  <div class="step-text">Definir Horários</div>
            </div>
        </div>
    </div>

    <div class="intro-text">
        <p>Selecione o edital do concurso que você está se preparando.
            Isso nos ajudará a criar um plano de estudos personalizado baseado no conteúdo programático oficial.</p>
    </div>

    <?php
    include_once("components/mensagens.php");
    mostrarMensagens();
    ?>

    <?php if ($edital_atual): ?>
        <div class="current-edital">
            <h2>Edital Atual</h2>
            <p><strong>Concurso:</strong> <?= htmlspecialchars($edital_atual['nome']) ?></p>
            <p><strong>Órgão:</strong> <?= htmlspecialchars($edital_atual['orgao']) ?></p>
            <p><strong>Selecionado em:</strong> <?= date('d/m/Y', strtotime($edital_atual['data_inscricao'])) ?></p>
        </div>
    <?php endif; ?>

    <div class="search-section">
        <input type="text" id="searchEdital" class="search-input"
               placeholder="Buscar por descrição do edital...">
    </div>

    <div class="editais-grid">
        <?php while ($edital = pg_fetch_assoc($result_editais)):
            $is_selected = ($edital['id_edital'] == $edital_atual_id);
            ?>
<div class="edital-card <?= $is_selected ? 'selected' : '' ?>"
     data-search="<?= strtolower(htmlspecialchars($edital['descricao'] ?? '')) ?>">
    
    <!-- Área da Logo -->
    <div class="edital-logo">
        <?php if (!empty($edital['logo_url'])): ?>
            <img src="<?= htmlspecialchars($edital['logo_url']) ?>" 
                 alt="Logo <?= htmlspecialchars($edital['nome']) ?>" 
                 class="edital-logo-img">
        <?php else: ?>
            <div class="edital-logo-placeholder">
                <i class="fas fa-university"></i>
            </div>
        <?php endif; ?>
    </div>

    <div class="edital-header">
        <h3 class="edital-title"><?= htmlspecialchars($edital['nome']) ?></h3>
    </div>

                <div class="edital-info">
                    <p><strong>Órgão:</strong> <?= htmlspecialchars($edital['orgao']) ?></p>
                    <p><strong>Ano:</strong> <?= $edital['ano'] ?></p>
                    <?php if ($edital['descricao']): ?>
                        <p><?= htmlspecialchars($edital['descricao']) ?></p>
                    <?php endif; ?>
                </div>

                <div class="edital-stats">
                    <span><?= $edital['total_materias'] ?> matérias</span>
                    <span><?= $edital['total_itens'] ?> itens</span>
                </div>

                <?php if (!$is_selected): ?>
    <form action="processar_edital.php" method="POST">
        <input type="hidden" name="edital_id" value="<?= $edital['id_edital'] ?>">
        <button type="submit" class="btn-select">Selecionar Edital</button>
    </form>
<?php else: ?>
    <form action="processar_edital.php" method="POST">
        <input type="hidden" name="edital_id" value="<?= $edital['id_edital'] ?>">
        <button type="submit" class="btn-select" style="background: var(--primary);">
            Continuar para Conteúdos
            <i class="fas fa-arrow-right" style="margin-left: 8px;"></i>
        </button>
    </form>
<?php endif; ?>
            </div>
        <?php endwhile; ?>
    </div>
</div>

<script>
    // Funcionalidade de busca
    document.getElementById('searchEdital').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        document.querySelectorAll('.edital-card').forEach(card => {
            const searchText = card.dataset.search;
            card.style.display = searchText.includes(searchTerm) ? 'block' : 'none';
        });
    });

    // Adicione o script do tema
document.addEventListener('DOMContentLoaded', () => {
    const themeToggleBtn = document.getElementById('theme-toggle-btn');
    const themeIcon = themeToggleBtn.querySelector('i');
    
    // Verifica se há preferência salva
    const currentTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', currentTheme);
    updateIcon(currentTheme);
    
    themeToggleBtn.addEventListener('click', () => {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        updateIcon(newTheme);
    });
    
    function updateIcon(theme) {
        if (theme === 'dark') {
            themeIcon.classList.remove('fa-moon');
            themeIcon.classList.add('fa-sun');
        } else {
            themeIcon.classList.remove('fa-sun');
            themeIcon.classList.add('fa-moon');
        }
    }
});
</script>
</body>
</html>
