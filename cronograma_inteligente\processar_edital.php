<?php
session_start();
if (!isset($_SESSION['idusuario'])) {
    header("Location: /login_index.php");
    exit();
}

include_once("assets/config.php");

$usuario_id = $_SESSION['idusuario'];
$edital_id = isset($_POST['edital_id']) ? intval($_POST['edital_id']) : 0;

if (!$edital_id) {
    $_SESSION['erro'] = "Edital inválido!";
    header("Location: selecionar_edital.php");
    exit();
}

try {
    // Iniciar transação
    pg_query($conexao, "BEGIN");

    // 1. Inativar prova atual (se existir)
    $query_inativar_prova = "
        UPDATE appestudo.provas 
        SET status = false 
        WHERE usuario_id = $1 
        AND status = true";
    pg_query_params($conexao, $query_inativar_prova, array($usuario_id));

    // 2. Remover edital anterior (se existir)
    $query_remover_edital = "
        DELETE FROM appestudo.usuario_edital 
        WHERE usuario_id = $1";
    pg_query_params($conexao, $query_remover_edital, array($usuario_id));

    // 3. Inserir novo edital selecionado
    $query_inserir_edital = "
        INSERT INTO appestudo.usuario_edital (usuario_id, edital_id) 
        VALUES ($1, $2)";
    pg_query_params($conexao, $query_inserir_edital, array($usuario_id, $edital_id));

    // Commit da transação
    pg_query($conexao, "COMMIT");

    // Redirecionar para seleção de conteúdos
    $_SESSION['sucesso'] = "Edital selecionado com sucesso! Agora selecione os conteúdos que deseja estudar.";
    header("Location: selecionar_conteudos.php");
    exit();

} catch (Exception $e) {
    // Em caso de erro, fazer rollback
    pg_query($conexao, "ROLLBACK");

    $_SESSION['erro'] = "Erro ao selecionar edital: " . $e->getMessage();
    header("Location: selecionar_edital.php");
    exit();
}
?>