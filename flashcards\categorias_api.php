<?php
// categorias_api.php
session_start();
include_once("assets/config.php");
include_once("helpers.php");

// Verifica autenticação
if (!isset($_SESSION['idusuario'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Usuário não autenticado']);
    exit();
}

// Buscar todas as categorias
$query_categories = "
    SELECT 
        c.id,
        c.nome as categoria,
        c.descricao,
        (
            SELECT COUNT(DISTINCT d.id)
            FROM appestudo.flashcard_decks d
            WHERE d.category_id = c.id
        ) as total_baralhos,
        (
            SELECT COUNT(DISTINCT fta.flashcard_id)
            FROM appestudo.flashcard_decks d
            JOIN appestudo.flashcard_topics t ON t.deck_id = d.id
            JOIN appestudo.flashcard_topic_association fta ON fta.topic_id = t.id
            WHERE d.category_id = c.id
        ) as total_cards
    FROM appestudo.flashcard_categories c
    WHERE c.status = true
    GROUP BY c.id, c.nome, c.descricao
    ORDER BY c.nome";

try {
    $result_categories = pg_query($conexao, $query_categories);
    
    if (!$result_categories) {
        throw new Exception("Erro na consulta: " . pg_last_error($conexao));
    }
    
    $categories = [];
    while ($row = pg_fetch_assoc($result_categories)) {
        // Converter valores numéricos de string para números
        $row['total_baralhos'] = (int)$row['total_baralhos'];
        $row['total_cards'] = (int)$row['total_cards'];
        $categories[] = $row;
    }
    
    // Retornar JSON
    header('Content-Type: application/json');
    echo json_encode($categories);
    
} catch (Exception $e) {
    // Retornar erro
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}