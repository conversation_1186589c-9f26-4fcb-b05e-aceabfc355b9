<!DOCTYPE html>
<html>
<head>
    <title>Seleção de Cursos</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            margin: 0;
            padding: 0;
        }
        .quadro-cinza {
            background-color: #f5deb3; /* Cor cinza claro */
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #ccc;
            margin: 20px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Sombra para relevo */
            font-family: 'Indie Flower', cursive;
        }
        .quadro-cinza h2 {
            font-family: "Courier Prime", monospace;
            font-weight: bold;
            color: #DEB887; /* Cor do texto do cabeçalho do quadro */
            text-align: center;
            padding: 10px;
            background-color: #2b2723;
            border-radius: 10px;
            margin: -20px -20px 20px -20px; /* Remover o padding adicional */
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Sombra para relevo */
        }
        .quadro-titulo {
            background-color: #f5deb3; /* Cor cinza claro */
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #ccc;
            margin: 0;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Sombra para relevo */
            text-align: center;
        }
        .quadro-titulo h1 {
            font-family: "Courier Prime", monospace;
            font-weight: bold;
            color: #333;
            margin: 0;
        }
    </style>
</head>
<link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
<body>
<div class="quadro-titulo">
    <h1>Seleção de Cursos</h1>
</div>

<?php
session_start();
include_once("conexao_POST.php");

if (!$conexao) {
    die("Erro na conexão: " . pg_last_error());
}

if (isset($_SESSION['idusuario'])) {
    $id_usuario = $_SESSION['idusuario'];

    // Consultar todos os cursos
    $query_consultar_todos_cursos = "SELECT * FROM appEstudo.curso";
    $resultado_todos_cursos = pg_query($conexao, $query_consultar_todos_cursos);

    if ($resultado_todos_cursos) {
        echo "<div class='quadro-cinza'>";
        echo "<h2>Escolha os Cursos que Você Utiliza:</h2>";
        echo "<form action='salvar_cursos_usuario.php' method='post'>";

        while ($curso = pg_fetch_assoc($resultado_todos_cursos)) {
            $checked = '';
            // Verificar se o curso está relacionado ao usuário
            $query_verificar_relacionamento = "SELECT * FROM appEstudo.usuario_has_curso 
                                               WHERE usuario_idusuario = $id_usuario 
                                               AND curso_idcurso = " . $curso['idcurso'];
            $resultado_relacionamento = pg_query($conexao, $query_verificar_relacionamento);
            if (pg_num_rows($resultado_relacionamento) > 0) {
                $checked = 'checked';
            }

            echo "<label>";
            echo "<input type='checkbox' name='cursos[]' value='" . $curso['idcurso'] . "' $checked>";
            echo $curso['nome'];
            echo "</label><br>";
        }

        echo "<button type='submit'>Salvar Seleção</button>";
        echo "</form>";
        echo "</div>";
    } else {
        echo "<p>Nenhum curso encontrado.</p>";
    }
}

pg_close($conexao);
?>

<br>

</body>
<script>
    // Função para recarregar a página "index.php" quando o pop-up for fechado
    window.onunload = function () {
        if (window.opener) {
            window.opener.location.reload();
        }
    };
</script>
</html>
