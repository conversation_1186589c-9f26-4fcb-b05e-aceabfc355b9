<?php
/**
 * API de Migração de Dados para Sistema Multi-Leis
 * Sistema completo de migração com backup, verificação e rollback
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Tratar requisições OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Verificar se o usuário está logado e é admin
session_start();
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit();
}

// Incluir conexão existente
require_once '../../conexao_POST.php';

$usuario_id = $_SESSION['idusuario'];
$metodo = $_SERVER['REQUEST_METHOD'];
$acao = $_GET['acao'] ?? '';

try {
    switch ($metodo) {
        case 'GET':
            switch ($acao) {
                case 'status':
                    verificarStatusMigracao($conexao);
                    break;
                case 'backup':
                    criarBackup($conexao);
                    break;
                case 'verificar':
                    verificarIntegridade($conexao);
                    break;
                case 'relatorio':
                    gerarRelatorio($conexao);
                    break;
                default:
                    verificarStatusMigracao($conexao);
                    break;
            }
            break;
            
        case 'POST':
            switch ($acao) {
                case 'executar':
                    executarMigracao($conexao);
                    break;
                case 'rollback':
                    executarRollback($conexao);
                    break;
                default:
                    http_response_code(400);
                    echo json_encode(['erro' => 'Ação não reconhecida']);
                    break;
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['erro' => 'Método não permitido']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['erro' => 'Erro interno do servidor: ' . $e->getMessage()]);
}

/**
 * Verifica o status atual da migração
 */
function verificarStatusMigracao($conexao) {
    $status = [
        'estrutura_criada' => false,
        'dados_migrados' => false,
        'backup_existe' => false,
        'integridade_ok' => false,
        'tabelas_verificadas' => [],
        'usuarios_afetados' => 0,
        'registros_por_tabela' => []
    ];
    
    try {
        // Verificar se estrutura multi-leis existe
        $query_estrutura = "
            SELECT EXISTS (
                SELECT 1 FROM information_schema.tables 
                WHERE table_schema = 'appestudo' AND table_name = 'lexjus_leis'
            ) as estrutura_existe";
        
        $result = pg_query($conexao, $query_estrutura);
        $row = pg_fetch_assoc($result);
        $status['estrutura_criada'] = $row['estrutura_existe'] === 't';
        
        if ($status['estrutura_criada']) {
            // Verificar se dados foram migrados
            $tabelas = ['lexjus_revisoes', 'lexjus_favoritos', 'lexjus_listas', 'lexjus_progresso', 'lexjus_anotacoes'];
            
            foreach ($tabelas as $tabela) {
                $query_migrados = "
                    SELECT 
                        COUNT(*) as total,
                        COUNT(CASE WHEN lei_id IS NOT NULL THEN 1 END) as migrados,
                        COUNT(CASE WHEN lei_id IS NULL THEN 1 END) as nao_migrados
                    FROM appestudo.$tabela";
                
                $result = pg_query($conexao, $query_migrados);
                if ($result) {
                    $dados = pg_fetch_assoc($result);
                    $status['registros_por_tabela'][$tabela] = [
                        'total' => (int)$dados['total'],
                        'migrados' => (int)$dados['migrados'],
                        'nao_migrados' => (int)$dados['nao_migrados'],
                        'percentual' => $dados['total'] > 0 ? round(($dados['migrados'] / $dados['total']) * 100, 1) : 0
                    ];
                }
            }
            
            // Verificar se todos os dados foram migrados
            $total_nao_migrados = array_sum(array_column(array_column($status['registros_por_tabela'], 'nao_migrados'), 0));
            $status['dados_migrados'] = $total_nao_migrados === 0;
            
            // Contar usuários afetados
            $query_usuarios = "
                SELECT COUNT(DISTINCT usuario_id) as usuarios
                FROM (
                    SELECT usuario_id FROM appestudo.lexjus_revisoes
                    UNION
                    SELECT usuario_id FROM appestudo.lexjus_favoritos
                    UNION
                    SELECT usuario_id FROM appestudo.lexjus_listas
                    UNION
                    SELECT usuario_id FROM appestudo.lexjus_progresso
                    UNION
                    SELECT usuario_id FROM appestudo.lexjus_anotacoes
                ) u";
            
            $result = pg_query($conexao, $query_usuarios);
            if ($result) {
                $row = pg_fetch_assoc($result);
                $status['usuarios_afetados'] = (int)$row['usuarios'];
            }
            
            // Verificar backup
            $status['backup_existe'] = verificarBackupExiste();
            
            // Verificar integridade
            $status['integridade_ok'] = verificarIntegridadeRapida($conexao);
        }
        
    } catch (Exception $e) {
        $status['erro'] = $e->getMessage();
    }
    
    echo json_encode([
        'sucesso' => true,
        'status' => $status
    ]);
}

/**
 * Cria backup dos dados antes da migração
 */
function criarBackup($conexao) {
    try {
        $timestamp = date('Y-m-d_H-i-s');
        $backup_dir = '../backups/';
        
        // Criar diretório se não existir
        if (!is_dir($backup_dir)) {
            mkdir($backup_dir, 0755, true);
        }
        
        $backup_file = $backup_dir . "backup_pre_migracao_$timestamp.sql";
        
        // Comando pg_dump para backup
        $tabelas = [
            'appestudo.lexjus_revisoes',
            'appestudo.lexjus_historico_revisoes',
            'appestudo.lexjus_favoritos',
            'appestudo.lexjus_listas',
            'appestudo.lexjus_lista_artigos',
            'appestudo.lexjus_progresso',
            'appestudo.lexjus_anotacoes'
        ];
        
        $backup_content = "-- Backup automático antes da migração multi-leis\n";
        $backup_content .= "-- Data: $timestamp\n\n";
        
        foreach ($tabelas as $tabela) {
            $query = "SELECT * FROM $tabela";
            $result = pg_query($conexao, $query);
            
            if ($result) {
                $backup_content .= "\n-- Backup da tabela $tabela\n";
                $backup_content .= "-- Total de registros: " . pg_num_rows($result) . "\n\n";
                
                while ($row = pg_fetch_assoc($result)) {
                    $columns = array_keys($row);
                    $values = array_map(function($val) {
                        return $val === null ? 'NULL' : "'" . pg_escape_string($val) . "'";
                    }, array_values($row));
                    
                    $backup_content .= "INSERT INTO $tabela (" . implode(', ', $columns) . ") VALUES (" . implode(', ', $values) . ");\n";
                }
            }
        }
        
        file_put_contents($backup_file, $backup_content);
        
        echo json_encode([
            'sucesso' => true,
            'backup_file' => basename($backup_file),
            'tamanho' => filesize($backup_file),
            'mensagem' => 'Backup criado com sucesso'
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'sucesso' => false,
            'erro' => 'Erro ao criar backup: ' . $e->getMessage()
        ]);
    }
}

/**
 * Executa a migração completa
 */
function executarMigracao($conexao) {
    try {
        // Iniciar transação
        pg_query($conexao, "BEGIN");
        
        $log = [];
        $log[] = "Iniciando migração para sistema multi-leis...";
        
        // 1. Verificar se estrutura existe
        $query_estrutura = "
            SELECT EXISTS (
                SELECT 1 FROM information_schema.tables 
                WHERE table_schema = 'appestudo' AND table_name = 'lexjus_leis'
            ) as existe";
        
        $result = pg_query($conexao, $query_estrutura);
        $row = pg_fetch_assoc($result);
        
        if ($row['existe'] !== 't') {
            throw new Exception('Estrutura multi-leis não encontrada. Execute primeiro o script adicionar_sistema_multileis.sql');
        }
        
        // 2. Obter ID da Constituição Federal
        $query_cf = "SELECT id FROM appestudo.lexjus_leis WHERE codigo = 'CF'";
        $result = pg_query($conexao, $query_cf);
        
        if (!$result || pg_num_rows($result) === 0) {
            throw new Exception('Lei CF não encontrada na tabela lexjus_leis');
        }
        
        $row = pg_fetch_assoc($result);
        $cf_id = $row['id'];
        $log[] = "ID da Constituição Federal: $cf_id";
        
        // 3. Migrar dados de cada tabela
        $tabelas = [
            'lexjus_revisoes' => 'Revisões',
            'lexjus_historico_revisoes' => 'Histórico de revisões',
            'lexjus_favoritos' => 'Favoritos',
            'lexjus_listas' => 'Listas',
            'lexjus_progresso' => 'Progresso',
            'lexjus_anotacoes' => 'Anotações'
        ];
        
        foreach ($tabelas as $tabela => $descricao) {
            $query_update = "
                UPDATE appestudo.$tabela 
                SET lei_id = $1 
                WHERE lei_id IS NULL";
            
            $result = pg_query_params($conexao, $query_update, [$cf_id]);
            
            if (!$result) {
                throw new Exception("Erro ao migrar $descricao: " . pg_last_error($conexao));
            }
            
            $registros_migrados = pg_affected_rows($result);
            $log[] = "$descricao migrados: $registros_migrados registros";
        }
        
        // 4. Criar preferências para usuários existentes
        $query_preferencias = "
            INSERT INTO appestudo.lexjus_user_preferencias (usuario_id, lei_atual)
            SELECT DISTINCT idusuario, 'CF'
            FROM appestudo.usuario 
            WHERE idusuario NOT IN (
                SELECT usuario_id 
                FROM appestudo.lexjus_user_preferencias
            )
            ON CONFLICT (usuario_id) DO NOTHING";
        
        $result = pg_query($conexao, $query_preferencias);
        if ($result) {
            $usuarios_atualizados = pg_affected_rows($result);
            $log[] = "Preferências criadas para $usuarios_atualizados usuários";
        }
        
        // 5. Atualizar total de artigos
        $query_total_artigos = "
            UPDATE appestudo.lexjus_leis 
            SET total_artigos = (
                SELECT COUNT(DISTINCT artigo_numero) 
                FROM appestudo.lexjus_progresso 
                WHERE lei_id = $1
            )
            WHERE codigo = 'CF' AND total_artigos = 0";
        
        pg_query_params($conexao, $query_total_artigos, [$cf_id]);
        
        // Confirmar transação
        pg_query($conexao, "COMMIT");
        
        $log[] = "=== MIGRAÇÃO CONCLUÍDA COM SUCESSO ===";
        
        echo json_encode([
            'sucesso' => true,
            'log' => $log,
            'mensagem' => 'Migração executada com sucesso'
        ]);
        
    } catch (Exception $e) {
        // Rollback em caso de erro
        pg_query($conexao, "ROLLBACK");
        
        echo json_encode([
            'sucesso' => false,
            'erro' => $e->getMessage(),
            'log' => $log ?? []
        ]);
    }
}

/**
 * Verifica integridade dos dados migrados
 */
function verificarIntegridade($conexao) {
    $problemas = [];
    $estatisticas = [];
    
    try {
        // Verificar registros órfãos
        $tabelas = ['lexjus_revisoes', 'lexjus_favoritos', 'lexjus_listas', 'lexjus_progresso', 'lexjus_anotacoes'];
        
        foreach ($tabelas as $tabela) {
            // Verificar registros sem lei_id
            $query_sem_lei = "SELECT COUNT(*) as count FROM appestudo.$tabela WHERE lei_id IS NULL";
            $result = pg_query($conexao, $query_sem_lei);
            $row = pg_fetch_assoc($result);
            
            if ($row['count'] > 0) {
                $problemas[] = "Tabela $tabela tem {$row['count']} registros sem lei_id";
            }
            
            // Verificar registros com lei_id inválido
            $query_lei_invalida = "
                SELECT COUNT(*) as count 
                FROM appestudo.$tabela t
                LEFT JOIN appestudo.lexjus_leis l ON t.lei_id = l.id
                WHERE t.lei_id IS NOT NULL AND l.id IS NULL";
            
            $result = pg_query($conexao, $query_lei_invalida);
            $row = pg_fetch_assoc($result);
            
            if ($row['count'] > 0) {
                $problemas[] = "Tabela $tabela tem {$row['count']} registros com lei_id inválido";
            }
            
            // Estatísticas da tabela
            $query_stats = "
                SELECT 
                    COUNT(*) as total,
                    COUNT(CASE WHEN lei_id IS NOT NULL THEN 1 END) as com_lei_id,
                    COUNT(DISTINCT lei_id) as leis_diferentes
                FROM appestudo.$tabela";
            
            $result = pg_query($conexao, $query_stats);
            $stats = pg_fetch_assoc($result);
            $estatisticas[$tabela] = $stats;
        }
        
        echo json_encode([
            'sucesso' => true,
            'integridade_ok' => empty($problemas),
            'problemas' => $problemas,
            'estatisticas' => $estatisticas
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'sucesso' => false,
            'erro' => $e->getMessage()
        ]);
    }
}

/**
 * Gera relatório completo da migração
 */
function gerarRelatorio($conexao) {
    try {
        $relatorio = [
            'data_geracao' => date('Y-m-d H:i:s'),
            'leis_disponiveis' => [],
            'distribuicao_dados' => [],
            'usuarios_com_preferencias' => 0,
            'resumo_geral' => []
        ];
        
        // Leis disponíveis
        $query_leis = "
            SELECT codigo, nome, total_artigos, ativa, cor_tema
            FROM appestudo.lexjus_leis 
            ORDER BY ordem_exibicao";
        
        $result = pg_query($conexao, $query_leis);
        while ($row = pg_fetch_assoc($result)) {
            $relatorio['leis_disponiveis'][] = $row;
        }
        
        // Distribuição de dados por lei
        $query_distribuicao = "
            SELECT 
                l.codigo,
                l.nome,
                COALESCE(r.revisoes, 0) as revisoes,
                COALESCE(f.favoritos, 0) as favoritos,
                COALESCE(lt.listas, 0) as listas,
                COALESCE(p.progresso, 0) as progresso,
                COALESCE(a.anotacoes, 0) as anotacoes
            FROM appestudo.lexjus_leis l
            LEFT JOIN (
                SELECT lei_id, COUNT(*) as revisoes 
                FROM appestudo.lexjus_revisoes 
                GROUP BY lei_id
            ) r ON l.id = r.lei_id
            LEFT JOIN (
                SELECT lei_id, COUNT(*) as favoritos 
                FROM appestudo.lexjus_favoritos 
                GROUP BY lei_id
            ) f ON l.id = f.lei_id
            LEFT JOIN (
                SELECT lei_id, COUNT(*) as listas 
                FROM appestudo.lexjus_listas 
                GROUP BY lei_id
            ) lt ON l.id = lt.lei_id
            LEFT JOIN (
                SELECT lei_id, COUNT(*) as progresso 
                FROM appestudo.lexjus_progresso 
                GROUP BY lei_id
            ) p ON l.id = p.lei_id
            LEFT JOIN (
                SELECT lei_id, COUNT(*) as anotacoes 
                FROM appestudo.lexjus_anotacoes 
                GROUP BY lei_id
            ) a ON l.id = a.lei_id
            ORDER BY l.ordem_exibicao";
        
        $result = pg_query($conexao, $query_distribuicao);
        while ($row = pg_fetch_assoc($result)) {
            $relatorio['distribuicao_dados'][] = $row;
        }
        
        // Usuários com preferências
        $query_usuarios = "
            SELECT COUNT(*) as total
            FROM appestudo.lexjus_user_preferencias";
        
        $result = pg_query($conexao, $query_usuarios);
        $row = pg_fetch_assoc($result);
        $relatorio['usuarios_com_preferencias'] = (int)$row['total'];
        
        echo json_encode([
            'sucesso' => true,
            'relatorio' => $relatorio
        ]);
        
    } catch (Exception $e) {
        echo json_encode([
            'sucesso' => false,
            'erro' => $e->getMessage()
        ]);
    }
}

/**
 * Funções auxiliares
 */
function verificarBackupExiste() {
    $backup_dir = '../backups/';
    if (!is_dir($backup_dir)) {
        return false;
    }
    
    $files = glob($backup_dir . 'backup_pre_migracao_*.sql');
    return count($files) > 0;
}

function verificarIntegridadeRapida($conexao) {
    try {
        $query = "
            SELECT 
                (SELECT COUNT(*) FROM appestudo.lexjus_revisoes WHERE lei_id IS NULL) +
                (SELECT COUNT(*) FROM appestudo.lexjus_favoritos WHERE lei_id IS NULL) +
                (SELECT COUNT(*) FROM appestudo.lexjus_listas WHERE lei_id IS NULL) +
                (SELECT COUNT(*) FROM appestudo.lexjus_progresso WHERE lei_id IS NULL) +
                (SELECT COUNT(*) FROM appestudo.lexjus_anotacoes WHERE lei_id IS NULL) as total_sem_lei_id";
        
        $result = pg_query($conexao, $query);
        $row = pg_fetch_assoc($result);
        
        return (int)$row['total_sem_lei_id'] === 0;
    } catch (Exception $e) {
        return false;
    }
}
?>
