<?php
//CacheManager.php
/**
 * Gerenciador de Cache para reduzir consultas ao banco de dados
 */
// CacheManager.php
class CacheManager {
  private $redis;
  private $prefix = 'plano_estudo:';
  private $defaultTTL = 3600; // 1 hora
  
  public function __construct() {
      try {
          $this->redis = new Redis();
          $this->redis->connect('127.0.0.1', 6379);
          $this->redis->setOption(Redis::OPT_SERIALIZER, Redis::SERIALIZER_PHP);
      } catch (Exception $e) {
          error_log("Erro ao conectar Redis: " . $e->getMessage());
          $this->redis = null;
      }
  }
  
  public function get($key) {
      if (!$this->redis) return null;
      try {
          return $this->redis->get($this->prefix . $key);
      } catch (Exception $e) {
          error_log("Erro ao obter cache: " . $e->getMessage());
          return null;
      }
  }
  
  public function set($key, $value, $ttl = null) {
      if (!$this->redis) return false;
      try {
          return $this->redis->set(
              $this->prefix . $key,
              $value,
              ['nx', 'ex' => $ttl ?? $this->defaultTTL]
          );
      } catch (Exception $e) {
          error_log("Erro ao definir cache: " . $e->getMessage());
          return false;
      }
  }
  
  public function delete($key) {
      if (!$this->redis) return false;
      try {
          return $this->redis->del($this->prefix . $key);
      } catch (Exception $e) {
          error_log("Erro ao deletar cache: " . $e->getMessage());
          return false;
      }
  }
  
  public function clear() {
      if (!$this->redis) return false;
      try {
          $keys = $this->redis->keys($this->prefix . '*');
          return $this->redis->del($keys);
      } catch (Exception $e) {
          error_log("Erro ao limpar cache: " . $e->getMessage());
          return false;
      }
  }
}
?>