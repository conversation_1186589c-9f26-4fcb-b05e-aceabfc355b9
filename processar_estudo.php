<?php
var_dump($_POST);
// Verifica se o formulário foi submetido
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Verifica se todos os campos obrigatórios foram preenchidos
    if (isset($_POST["usuario_id"], $_POST["planejamento_id"], $_POST["materia_id"], $_POST["data"], $_POST["hora_inicio"], $_POST["hora_fim"], $_POST["tempo_liquido"], $_POST["tempo_bruto"], $_POST["tempo_perdido"])) {
        // Obtém os dados do formulário
        $usuario_id = $_POST["usuario_id"];
        $planejamento_id = $_POST["planejamento_id"];
        $materia_id = $_POST["materia_id"];
        $data = $_POST["data"];
        $hora_inicio = $_POST["hora_inicio"];
        $hora_fim = $_POST["hora_fim"];
        $tempo_liquido = $_POST["tempo_liquido"];
        $tempo_bruto = $_POST["tempo_bruto"];
        $tempo_perdido = $_POST["tempo_perdido"];

        // Aqui você deve realizar a conexão com o banco de dados PostgreSQL
        include_once("conexao_POST.php");

        // Verificar se a conexão foi estabelecida com sucesso


        // Montar a query para inserir os dados na tabela "estudos"
        $query_inserir_estudo = "INSERT INTO appEstudo.estudos (data, tempo_liquido, tempo_bruto, tempo_perdido, ponto_estudado, hora_inicio, hora_fim, metodo, q_total, q_errada, q_certa, planejamento_idplanejamento, planejamento_usuario_idusuario, materia_idmateria) 
                                VALUES ('$data', '$tempo_liquido','$tempo_bruto','$tempo_perdido',NULL, '$hora_inicio', '$hora_fim', NULL, NULL, NULL, NULL, $planejamento_id, $usuario_id, $materia_id)";

        // Executar a query de inserção
        $resultado_inserir_estudo = pg_query($conexao, $query_inserir_estudo);

        // Verificar se a inserção foi bem-sucedida
        if ($resultado_inserir_estudo) {
            echo "Estudo registrado com sucesso!";
        } else {
            echo "Erro ao registrar estudo: " . pg_last_error($conexao);
        }

        // Fechar a conexão com o banco de dados PostgreSQL
        pg_close($conexao);
    } else {
        echo "Por favor, preencha todos os campos obrigatórios.";
        echo $_POST["usuario_id"];
        echo $_POST["planejamento_id"];
        echo $_POST["materia_id"];
        echo $_POST["data"];
        echo $_POST["hora_inicio"];
        echo $_POST["hora_fim"];

    }
}
?>
