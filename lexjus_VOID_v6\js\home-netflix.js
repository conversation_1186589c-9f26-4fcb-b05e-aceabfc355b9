/**
 * Sistema de Página Inicial Estilo Netflix
 * Gerencia a seleção de leis e navegação para o sistema de estudo
 */

class HomeNetflix {
    constructor() {
        this.leis = [];
        this.leiSelecionada = null;
        this.previewModal = null;
        
        this.init();
    }
    
    init() {
        console.log('🚀 Inicializando HomeNetflix...');
        this.previewModal = document.getElementById('previewModal');
        this.carregarLeis();
        this.bindEventos();
    }
    
    /**
     * Vincula eventos da página
     */
    bindEventos() {
        // Fechar modal com ESC
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.previewModal.classList.contains('ativo')) {
                this.fecharPreview();
            }
        });
        
        // Fechar modal clicando no overlay
        this.previewModal.addEventListener('click', (e) => {
            if (e.target === this.previewModal) {
                this.fecharPreview();
            }
        });
    }
    
    /**
     * <PERSON>eg<PERSON> todas as leis disponíveis
     */
    async carregarLeis() {
        try {
            console.log('📚 Carregando suas leis...');

            // Usar API autenticada
            const response = await fetch('api/leis.php?acao=listar');
            const data = await response.json();

            if (data.sucesso) {
                this.leis = data.leis;
                this.renderizarLeis();
            } else {
                throw new Error(data.erro || 'Erro ao carregar leis');
            }
        } catch (error) {
            console.error('❌ Erro ao carregar leis:', error);
            this.mostrarErro();
        }
    }
    
    /**
     * Renderiza as leis na página
     */
    renderizarLeis() {
        const loading = document.getElementById('leisLoading');
        const grid = document.getElementById('leisGrid');
        
        loading.style.display = 'none';
        grid.style.display = 'grid';
        
        const leisHTML = this.leis.map((lei, index) => {
            return `
                <div class="lei-card" style="animation-delay: ${index * 0.1}s" onclick="homeNetflix.abrirPreview('${lei.codigo}')">
                    <div class="lei-card-header">
                        <div class="lei-card-icon" style="background: ${lei.cor_tema}">
                            <i class="${lei.icone}"></i>
                        </div>
                        <h3 class="lei-card-title">${lei.nome}</h3>
                        <p class="lei-card-subtitle">${lei.nome_completo}</p>
                    </div>
                    
                    <div class="lei-card-body">
                        <p class="lei-card-description">${lei.descricao}</p>
                    </div>
                    
                    <div class="lei-card-footer">
                        <button class="btn-estudar-card" onclick="event.stopPropagation(); homeNetflix.iniciarEstudo('${lei.codigo}')">
                            <i class="fas fa-play"></i>
                            ${lei.artigos_lidos > 0 ? 'Continuar Estudo' : 'Começar a Estudar'}
                        </button>
                    </div>
                </div>
            `;
        }).join('');
        
        grid.innerHTML = leisHTML;
        console.log('✅ Leis renderizadas com sucesso');
    }
    
    /**
     * Atualiza estatísticas (removido - não usado mais)
     */
    atualizarEstatisticas() {
        // Método removido - estatísticas não são mais exibidas na nova versão
    }
    
    /**
     * Abre preview de uma lei
     */
    async abrirPreview(codigo) {
        console.log('👁️ Abrindo preview da lei:', codigo);
        
        const lei = this.leis.find(l => l.codigo === codigo);
        if (!lei) return;
        
        this.leiSelecionada = lei;
        
        // Atualizar informações do modal
        this.previewModal.querySelector('.modal-lei-icon i').className = lei.icone;
        this.previewModal.querySelector('.modal-lei-icon').style.background = lei.cor_tema;
        this.previewModal.querySelector('.modal-lei-nome').textContent = lei.nome;
        this.previewModal.querySelector('.modal-lei-completo').textContent = lei.nome_completo;
        this.previewModal.querySelector('.modal-desc-text').textContent = lei.descricao;
        
        // Estatísticas removidas
        
        // Carregar preview dos artigos
        await this.carregarPreviewArtigos(codigo);
        
        // Mostrar modal
        this.previewModal.classList.add('ativo');
        document.body.style.overflow = 'hidden';
    }
    
    /**
     * Carrega preview dos primeiros artigos
     */
    async carregarPreviewArtigos(codigo) {
        try {
            const response = await fetch(`api/leis.php?acao=carregar&codigo=${codigo}`);
            const data = await response.json();

            if (data.sucesso && data.artigos) {
                const container = this.previewModal.querySelector('.preview-artigos');
                const artigosHTML = data.artigos.slice(0, 3).map(artigo => `
                    <div class="preview-artigo">
                        <div class="preview-artigo-numero">${artigo.artigo}</div>
                        <div class="preview-artigo-texto">${artigo.caput}</div>
                    </div>
                `).join('');

                container.innerHTML = artigosHTML;
            }
        } catch (error) {
            console.error('Erro ao carregar preview dos artigos:', error);
            const container = this.previewModal.querySelector('.preview-artigos');
            container.innerHTML = '<p style="opacity: 0.7;">Não foi possível carregar o preview dos artigos.</p>';
        }
    }
    
    /**
     * Fecha o modal de preview
     */
    fecharPreview() {
        this.previewModal.classList.remove('ativo');
        document.body.style.overflow = '';
        this.leiSelecionada = null;
    }
    
    /**
     * Inicia o estudo de uma lei
     */
    async iniciarEstudo(codigo = null) {
        const leiCodigo = codigo || (this.leiSelecionada ? this.leiSelecionada.codigo : null);

        if (!leiCodigo) {
            console.error('Nenhuma lei selecionada');
            return;
        }

        console.log('🎯 Iniciando estudo da lei:', leiCodigo);

        try {
            // Definir lei como atual do usuário
            const response = await fetch('api/leis.php?acao=trocar', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ codigo: leiCodigo })
            });

            const data = await response.json();

            if (data.sucesso) {
                // Redirecionar para o sistema de estudo
                window.location.href = 'index.php';
            } else {
                throw new Error(data.erro || 'Erro ao definir lei');
            }
        } catch (error) {
            console.error('Erro ao iniciar estudo:', error);
            alert('Erro ao iniciar estudo. Tente novamente.');
        }
    }
    
    /**
     * Mostra erro no carregamento
     */
    mostrarErro() {
        const loading = document.getElementById('leisLoading');
        loading.innerHTML = `
            <div style="text-align: center; color: #e74c3c;">
                <i class="fas fa-exclamation-triangle" style="font-size: 3rem; margin-bottom: 20px;"></i>
                <p>Erro ao carregar leis disponíveis</p>
                <button onclick="homeNetflix.carregarLeis()" style="margin-top: 15px; padding: 10px 20px; background: #667eea; border: none; border-radius: 5px; color: white; cursor: pointer;">
                    Tentar Novamente
                </button>
            </div>
        `;
    }
}

// Funções globais para uso nos templates
function fecharPreview() {
    if (window.homeNetflix) {
        window.homeNetflix.fecharPreview();
    }
}

function iniciarEstudo() {
    if (window.homeNetflix) {
        window.homeNetflix.iniciarEstudo();
    }
}

// Instância global
let homeNetflix;

// Inicializar quando DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Inicializando sistema HomeNetflix...');
    homeNetflix = new HomeNetflix();
    
    // Disponibilizar globalmente
    window.homeNetflix = homeNetflix;
    console.log('✅ HomeNetflix inicializado e disponibilizado globalmente');
});
