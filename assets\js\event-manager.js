// event-manager.js
class EventManager {
    constructor() {
        if (window.eventManager) {
            return window.eventManager;
        }
        window.eventManager = this;
        
        this.modalAtivo = false;
        this.bindEventos();
        this.init();
    }

    init() {
        const calendarEl = document.getElementById('calendario_agenda');
        if (calendarEl) {
            const calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'dayGridMonth',
                height: 'auto',
                headerToolbar: {
                    start: 'today prev,next',
                    center: 'title',
                    end: 'dayGridMonth timeGridWeek timeGridDay'
                },
                locale: 'pt-br',
                editable: false,
                selectable: false,
                businessHours: true,
                dayMaxEvents: true,
                eventDisplay: 'block',
                displayEventTime: false,
                events: 'listareventos_agenda_POST.php',
                dragScroll: true,
                eventDragStart: function(info) {
                    info.el.style.opacity = '0.5';
                },
                eventDragStop: function(info) {
                    info.el.style.opacity = '1';
                },
                dragRevertDuration: 200,
                eventDragMinDistance: 10,
                dragOpacity: 0.8,
                eventStartEditable: false,
                eventResizableFromStart: false,
                eventDurationEditable: false,

                eventDidMount: (info) => {
                    info.el.style.borderWidth = '3px';
                    info.el.style.cursor = 'pointer';
                    info.el.classList.add('evento-calendario-agenda');

                    if (info.event.extendedProps.tipo) {
                        info.el.setAttribute('data-tipo', info.event.extendedProps.tipo);
                    }
                    if (info.event.extendedProps.realizado) {
                        info.el.setAttribute('data-realizado', info.event.extendedProps.realizado);
                    }
                },

                eventMouseEnter: (info) => {
                    $(info.el).css('transform', 'scale(1.02)');
                    $(info.el).css('transition', 'transform 0.2s ease');
                },

                eventMouseLeave: (info) => {
                    $(info.el).css('transform', 'scale(1)');
                },

                eventClick: (info) => {
                    if (info.event && !this.modalAtivo) {
                        this.modalAtivo = true;
                        this.carregarDetalhesEvento(info.event.id);
                    }
                }
            });

            calendarEl.fcCalendar = calendar;
            calendar.render();
        }
    }

    bindEventos() {
        $(document).off('click', '.evento-link-dashboard, .evento-link');
        
        $(document).on('click', '.evento-link-dashboard, .evento-link', (e) => {
            e.preventDefault();
            e.stopPropagation();
            
            if (this.modalAtivo) return;
            
            const eventoId = $(e.currentTarget).data('id');
            if (eventoId) {
                this.modalAtivo = true;
                this.carregarDetalhesEvento(eventoId);
            }
        });
    }

    convertDataParaInput(dataStr) {
        if (!dataStr) return '';
        const [data] = dataStr.split(' '); // Remove a parte do horário
        const [dia, mes, ano] = data.split('/');
        return `${ano}-${mes.padStart(2, '0')}-${dia.padStart(2, '0')}`;
    }

    convertDataParaEnvio(dataStr) {
        if (!dataStr) return '';
        const [ano, mes, dia] = dataStr.split('-');
        return `${dia}/${mes}/${ano}`;
    }

    async carregarDetalhesEvento(eventoId) {
        try {
            const response = await $.ajax({
                url: 'buscar_detalhes_evento.php',
                method: 'POST',
                data: { evento_id: eventoId },
                dataType: 'json'
            });

            if (response.erro) {
                console.error('Erro:', response.erro);
                this.modalAtivo = false;
                return;
            }

            this.mostrarModal(response);
        } catch (error) {
            console.error('Erro ao carregar detalhes:', error);
            this.modalAtivo = false;
        }
    }

    mostrarModal(dados) {
        const dataInputValue = this.convertDataParaInput(dados.data_inicio);

        $('#modalEvento').remove();

        const modalContent = `
            <div id="modalEvento" class="modal-especifico ativo" style="display: flex;">
                <div class="modal-especifico-overlay"></div>
                <div class="modal-especifico-content">
                    <button class="modal-especifico-close"><i class="fas fa-times"></i></button>

                    <div class="modo-edicao-container" style="margin-bottom: 1rem;">
                        <label class="switch">
                            <input type="checkbox" id="habilitarEdicao">
                            <span class="slider round"></span>
                        </label>
                        <span class="modo-edicao-texto">Modo Edição</span>
                    </div>

                    <input type="text" class="modal-especifico-titulo-input" disabled 
                           value="${dados.title || dados.titulo || ''}"
                           style="width: 100%; margin-bottom: 1rem; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">

                    <div class="status-section modal-especifico-status-section">
                        <span class="status-badge realizado modal-especifico-badge" 
                              style="display: ${dados.realizado === 't' ? 'inline-block' : 'none'}">
                            <i class="fas fa-check-circle"></i> Realizado
                        </span>
                        <span class="status-badge pendente modal-especifico-badge" 
                              style="display: ${dados.realizado !== 't' ? 'inline-block' : 'none'}">
                            <i class="fas fa-clock"></i> Pendente
                        </span>
                    </div>

                    <div class="date-editor" style="margin: 1rem 0;">
                        <label>Data:</label>
                        <input type="date" class="modal-especifico-data" disabled 
                               value="${dataInputValue}"
                               style="width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
                        <small style="display: block; margin-top: 4px; color: #666;">
                           
                        </small>
                    </div>

                    <div class="details-container">
                        <textarea class="modal-especifico-detalhes-input" disabled 
                                  style="width: 100%; min-height: 100px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;"
                        >${dados.detalhes || ''}</textarea>
                    </div>

                    <div id="eventoRealizadoCheckboxContainer" style="margin: 1rem 0;">
                        <label>
                            <input type="checkbox" id="eventoRealizadoCheckbox" ${dados.realizado === 't' ? 'checked' : ''}> 
                            Marcar como realizado
                        </label>
                    </div>

                    ${dados.tipo === 'Planejamento' ? `
                        <div style="display: flex; justify-content: center; margin: 1rem 0;">
                            <button class="btn-ciclo-navegacao estudarMateriaModal" 
                                    style="background: var(--primary); color: white; font-size: 1rem; padding: 10px 22px; border-radius: 12px; border: none; cursor: pointer; display: inline-flex; align-items: center; gap: 8px;" 
                                    data-event-id="${dados.id}" 
                                    data-materia-id="${dados.materia_idmateria}"
                                    onclick="return false;">
                                <i class="fas fa-play"></i> Estudar esta matéria
                            </button>
                        </div>
                    ` : ''}

                    <div class="button-group">
                        <button class="btn btn-success salvarEventoModal">
                            <i class="fas fa-save"></i> Salvar
                        </button>
                        <button class="btn btn-danger excluirEventoModal">
                            <i class="fas fa-trash"></i> Excluir
                        </button>
                    </div>
                </div>
            </div>
        `;

        $(modalContent).appendTo('body');

        // Remover event listeners antigos antes de adicionar novos
        $(document).off('click', '.estudarMateriaModal');
        
        // Adicionar novo event listener
        $(document).on('click', '.estudarMateriaModal', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const eventoId = $(this).data('event-id');
            console.log('Botão estudar clicado, evento ID:', eventoId); // Debug
            
            if (!eventoId) {
                console.error('ID do evento não encontrado');
                return;
            }
            
            // Mostrar loading
            Swal.fire({
                title: 'Carregando...',
                text: 'Buscando dados do evento',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Primeiro, buscar os detalhes do evento
            $.ajax({
                url: 'buscar_detalhes_evento.php',
                method: 'POST',
                data: { evento_id: eventoId },
                dataType: 'json',
                success: function(eventoResponse) {
                    console.log('Resposta do evento:', eventoResponse); // Debug
                    
                    if (eventoResponse.erro) {
                        console.error('Erro ao buscar detalhes do evento:', eventoResponse.erro);
                        Swal.fire({
                            icon: 'error',
                            title: 'Erro!',
                            text: 'Erro ao buscar dados do evento. Tente novamente.',
                            showConfirmButton: false,
                            timer: 2000
                        });
                        return;
                    }

                    // Se o evento tem uma matéria associada, buscar o último estudo dessa matéria
                    if (eventoResponse.materia_idmateria) {
                        $.ajax({
                            url: 'ultimo_estudo_materia.php',
                            method: 'GET',
                            data: { id: eventoResponse.materia_idmateria },
                            dataType: 'json',
                            success: function(estudoResponse) {
                                console.log('Resposta do último estudo:', estudoResponse); // Debug
                                Swal.close();
                                
                                // Verificar se temos dados válidos do último estudo
                                if (estudoResponse && !estudoResponse.erro) {
                                    // Usar os detalhes do evento como ponto estudado inicial
                                    const pontoEstudado = eventoResponse.detalhes || '';
                                    console.log('Ponto estudado a ser enviado:', pontoEstudado); // Debug
                                    
                                    // Usar a função abrirCronometroComCamposEstruturado com os dados do último estudo
                                    const params = {
                                        materia: eventoResponse.nome_materia,
                                        curso: estudoResponse.curso,
                                        metodo: estudoResponse.tipo,
                                        link_conteudo: estudoResponse.link_conteudo || '',
                                        ponto_estudado: pontoEstudado
                                    };
                                    console.log('Parâmetros para abrirCronometroComCamposEstruturado:', params); // Debug
                                    
                                    try {
                                        console.log('Chamando abrirCronometroComCamposEstruturado...'); // Debug
                                        window.abrirCronometroComCamposEstruturado(
                                            params.materia,
                                            params.curso,
                                            params.metodo,
                                            params.link_conteudo,
                                            params.ponto_estudado
                                        );
                                        console.log('Função abrirCronometroComCamposEstruturado chamada com sucesso'); // Debug
                                    } catch (error) {
                                        console.error('Erro ao chamar abrirCronometroComCamposEstruturado:', error); // Debug
                                    }
                                } else {
                                    // Se não houver dados do último estudo, usar os detalhes do evento
                                    const pontoEstudado = eventoResponse.detalhes || '';
                                    console.log('Ponto estudado (sem último estudo):', pontoEstudado); // Debug
                                    
                                    const params = {
                                        materia: eventoResponse.nome_materia,
                                        curso: '',
                                        metodo: '',
                                        link_conteudo: '',
                                        ponto_estudado: pontoEstudado
                                    };
                                    console.log('Parâmetros para abrirCronometroComCamposEstruturado (sem último estudo):', params); // Debug
                                    
                                    try {
                                        console.log('Chamando abrirCronometroComCamposEstruturado (sem último estudo)...'); // Debug
                                        window.abrirCronometroComCamposEstruturado(
                                            params.materia,
                                            params.curso,
                                            params.metodo,
                                            params.link_conteudo,
                                            params.ponto_estudado
                                        );
                                        console.log('Função abrirCronometroComCamposEstruturado chamada com sucesso (sem último estudo)'); // Debug
                                    } catch (error) {
                                        console.error('Erro ao chamar abrirCronometroComCamposEstruturado (sem último estudo):', error); // Debug
                                    }
                                }

                                // Fechar o modal atual
                                window.eventManager.fecharModal();
                            },
                            error: function(xhr, status, error) {
                                console.error('Erro na requisição do último estudo:', {
                                    status: status,
                                    error: error,
                                    response: xhr.responseText
                                });
                                
                                // Usar os detalhes do evento mesmo em caso de erro
                                const pontoEstudado = eventoResponse.detalhes || '';
                                console.log('Ponto estudado (erro):', pontoEstudado); // Debug
                                
                                const params = {
                                    materia: eventoResponse.nome_materia,
                                    curso: '',
                                    metodo: '',
                                    link_conteudo: '',
                                    ponto_estudado: pontoEstudado
                                };
                                console.log('Parâmetros para abrirCronometroComCamposEstruturado (erro):', params); // Debug
                                
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Erro!',
                                    text: 'Erro ao buscar dados do último estudo. Abrindo cronômetro com dados padrão.',
                                    showConfirmButton: false,
                                    timer: 2000
                                }).then(() => {
                                    try {
                                        console.log('Chamando abrirCronometroComCamposEstruturado (erro)...'); // Debug
                                        window.abrirCronometroComCamposEstruturado(
                                            params.materia,
                                            params.curso,
                                            params.metodo,
                                            params.link_conteudo,
                                            params.ponto_estudado
                                        );
                                        console.log('Função abrirCronometroComCamposEstruturado chamada com sucesso (erro)'); // Debug
                                    } catch (error) {
                                        console.error('Erro ao chamar abrirCronometroComCamposEstruturado (erro):', error); // Debug
                                    }

                                    // Fechar o modal atual
                                    window.eventManager.fecharModal();
                                });
                            }
                        });
                    } else {
                        // Se o evento não tem matéria associada, abrir com os dados do evento
                        const pontoEstudado = eventoResponse.detalhes || '';
                        console.log('Ponto estudado (sem matéria):', pontoEstudado); // Debug
                        
                        const params = {
                            materia: eventoResponse.title,
                            curso: '',
                            metodo: '',
                            link_conteudo: '',
                            ponto_estudado: pontoEstudado
                        };
                        console.log('Parâmetros para abrirCronometroComCamposEstruturado (sem matéria):', params); // Debug
                        
                        Swal.close();
                        try {
                            console.log('Chamando abrirCronometroComCamposEstruturado (sem matéria)...'); // Debug
                            window.abrirCronometroComCamposEstruturado(
                                params.materia,
                                params.curso,
                                params.metodo,
                                params.link_conteudo,
                                params.ponto_estudado
                            );
                            console.log('Função abrirCronometroComCamposEstruturado chamada com sucesso (sem matéria)'); // Debug
                        } catch (error) {
                            console.error('Erro ao chamar abrirCronometroComCamposEstruturado (sem matéria):', error); // Debug
                        }

                        // Fechar o modal atual
                        window.eventManager.fecharModal();
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erro na requisição dos detalhes do evento:', {
                        status: status,
                        error: error,
                        response: xhr.responseText
                    });
                    
                    Swal.fire({
                        icon: 'error',
                        title: 'Erro!',
                        text: 'Erro ao buscar dados do evento. Tente novamente.',
                        showConfirmButton: false,
                        timer: 2000
                    });
                }
            });
        });

        const self = this;
        const habilitarEdicaoCheckbox = document.getElementById('habilitarEdicao');
        const tituloInput = document.querySelector('.modal-especifico-titulo-input');
        const dataInput = document.querySelector('.modal-especifico-data');
        const detalhesInput = document.querySelector('.modal-especifico-detalhes-input');
        const realizadoCheckbox = document.getElementById('eventoRealizadoCheckbox');

        habilitarEdicaoCheckbox.addEventListener('change', function() {
            const estaHabilitado = this.checked;
            const isPlanejamento = dados.tipo === 'Planejamento';
            
            // Se for Planejamento, o título sempre fica desabilitado
            if (!isPlanejamento) {
                if (estaHabilitado) {
                    tituloInput.removeAttribute('disabled');
                    
                    tituloInput.style.cursor = 'text';
                    tituloInput.style.opacity = '1';
                } else {
                    tituloInput.setAttribute('disabled', 'disabled');
                    
                    tituloInput.style.cursor = 'not-allowed';
                    tituloInput.style.opacity = '0.7';
                }
            } else {
                // Para eventos de Planejamento, manter o título sempre desabilitado
                tituloInput.setAttribute('disabled', 'disabled');
                
                tituloInput.style.cursor = 'not-allowed';
                tituloInput.style.opacity = '0.7';
            }
            
            // Para os outros campos, comportamento normal
            if (estaHabilitado) {
                dataInput.removeAttribute('disabled');
                detalhesInput.removeAttribute('disabled');
                
                dataInput.style.cursor = 'text';
                dataInput.style.opacity = '1';
                
                detalhesInput.style.cursor = 'text';
                detalhesInput.style.opacity = '1';
            } else {
                dataInput.setAttribute('disabled', 'disabled');
                detalhesInput.setAttribute('disabled', 'disabled');
                
                dataInput.style.cursor = 'not-allowed';
                dataInput.style.opacity = '0.7';
                
                detalhesInput.style.cursor = 'not-allowed';
                detalhesInput.style.opacity = '0.7';
            }
            
            document.querySelector('#modalEvento').classList.toggle('modo-edicao', estaHabilitado);
        });

        realizadoCheckbox.addEventListener('change', function() {
            const realizadoBadge = document.querySelector('.modal-especifico-badge.realizado');
            const pendenteBadge = document.querySelector('.modal-especifico-badge.pendente');
            realizadoBadge.style.display = this.checked ? 'inline-block' : 'none';
            pendenteBadge.style.display = this.checked ? 'none' : 'inline-block';
        });

        $('.salvarEventoModal').on('click', async () => {
            const dadosAtualizados = {
                eventoId: dados.id,
                realizado: realizadoCheckbox.checked
            };

            if (habilitarEdicaoCheckbox.checked) {
                const dataFormatada = this.convertDataParaEnvio(dataInput.value);

                Object.assign(dadosAtualizados, {
                    titulo: tituloInput.value.trim(),
                    detalhes: detalhesInput.value.trim(),
                    data_inicio: dataFormatada,
                    data_fim: dataFormatada
                });

                if (!dadosAtualizados.titulo) {
                    alert('O título do evento não pode estar vazio!');
                    return;
                }

                if (!dataInput.value) {
                    alert('A data do evento não pode estar vazia!');
                    return;
                }
            }

            try {
                //console.log('Dados enviados:', dadosAtualizados);
                const response = await $.ajax({
                    url: 'agenda_atualizar_evento.php',
                    method: 'POST',
                    data: dadosAtualizados,
                    dataType: 'json'
                });

                if (response.success) {
                    await this.atualizarInterface();
                    this.fecharModal();
                } else {
                    throw new Error(response.message || 'Erro ao atualizar evento');
                }
            } catch (error) {
                console.error('Erro ao salvar:', error);
                alert('Erro ao salvar as alterações: ' + error.message);
            }
        });

        $('.excluirEventoModal').on('click', async () => {
            const result = await Swal.fire({
                title: 'Confirmar exclusão',
                text: 'Tem certeza que deseja excluir este evento?',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#d33',
                cancelButtonColor: '#3085d6',
                confirmButtonText: 'Sim, excluir',
                cancelButtonText: 'Cancelar'
            });

            if (result.isConfirmed) {
                try {
                    // Mostrar loading
                    Swal.fire({
                        title: 'Excluindo...',
                        text: 'Por favor, aguarde',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    await $.ajax({
                        url: 'processa_novo_evento.php',
                        method: 'POST',
                        data: { eventoIdexcluir: dados.id }
                    });

                    await this.atualizarInterface();
                    this.fecharModal();

                    // Mostrar mensagem de sucesso
                    Swal.fire({
                        icon: 'success',
                        title: 'Sucesso!',
                        text: 'Evento excluído com sucesso!',
                        timer: 1500,
                        showConfirmButton: false
                    });
                } catch (error) {
                    console.error('Erro ao excluir:', error);
                    Swal.fire({
                        icon: 'error',
                        title: 'Erro!',
                        text: 'Erro ao excluir o evento. Por favor, tente novamente.'
                    });
                }
            }
        });

        $('.modal-especifico-close, .modal-especifico-overlay').on('click', () => {
            this.fecharModal();
        });

        $(document).on('keydown.modal', (e) => {
            if (e.key === 'Escape') this.fecharModal();
        });
    }

    async atualizarInterface() {
        try {
            // Atualizar o calendário
            this.atualizarCalendario();

            // Atualizar a lista de eventos futuros
            if (typeof atualizarListaEventos === 'function') {
                //console.log('Atualizando lista de eventos...');
                // Aguardar a atualização da lista de eventos
                await new Promise((resolve) => {
                    $.ajax({
                        url: 'buscar_eventos_futuros.php',
                        method: 'GET',
                        dataType: 'json',
                        success: function(response) {
                            const container = document.querySelector('.eventos-futuros-container');
                            if (!container) {
                                console.error('Container de eventos não encontrado');
                                resolve();
                                return;
                            }
                            
                            if (Array.isArray(response)) {
                                let html = '<div class="lista-eventos">';
                                
                                if (response.length > 0) {
                                    response.forEach(evento => {
                                        let cor;
                                        switch(evento.tipo_evento) {
                                            case 'Faculdade':
                                                cor = '#1976D2';
                                                break;
                                            case 'Trabalho':
                                                cor = 'gray';
                                                break;
                                            case 'Concurso':
                                                cor = 'blue';
                                                break;
                                            case 'Pessoal':
                                                cor = '#00796B';
                                                break;
                                            case 'Planejamento':
                                                cor = '#FFD700';
                                                break;
                                            default:
                                                cor = '#000000';
                                        }
                                        
                                        const data_formatada = new Date(evento.data_inicio).toLocaleDateString('pt-BR');
                                        const dias_restantes = Math.ceil((new Date(evento.data_inicio) - new Date()) / (1000 * 60 * 60 * 24));
                                        const texto_dias = dias_restantes == 1 ? "Falta 1 dia" : `Faltam ${dias_restantes} dias`;
                                        
                                        html += `<div class='evento-item' data-id='${evento.id}'>
                                            <div class='evento-marcador' style='background-color: ${cor}'></div>
                                            <div class='evento-conteudo'>
                                                <div class='evento-titulo'>${evento.titulo}</div>
                                                <div class='evento-data'>
                                                    <i class='fas fa-calendar-alt'></i> ${data_formatada}
                                                </div>
                                                <div class='evento-contagem'>
                                                    <i class='fas fa-hourglass-half'></i> ${texto_dias}
                                                </div>
                                            </div>
                                        </div>`;
                                    });
                                } else {
                                    html = `<div class="sem-eventos">
                                        <i class="fas fa-calendar-times"></i>
                                        <p>Não há eventos futuros agendados</p>
                                    </div>`;
                                }
                                
                                container.innerHTML = html;
                                
                                // Reativar os event listeners para os novos itens
                                document.querySelectorAll('.evento-item').forEach(item => {
                                    item.addEventListener('click', function() {
                                        const eventoId = this.getAttribute('data-id');
                                        if (window.eventManager) {
                                            window.eventManager.carregarDetalhesEvento(eventoId);
                                        }
                                    });
                                });
                            }
                            resolve();
                        },
                        error: function(xhr, status, error) {
                            console.error('Erro ao atualizar lista de eventos:', error);
                            resolve();
                        }
                    });
                });
            } else {
                console.error('Função atualizarListaEventos não encontrada');
            }

            // Atualizar outros componentes se necessário
            const response = await $.ajax({
                url: 'atualizar_agenda_component.php',
                method: 'GET'
            });

            const cardContent = $('.card-planejamento .card-content_planejamento_agenda');
            if (cardContent.length) {
                cardContent.html(response);
            }

            this.bindEventos();
        } catch (error) {
            console.error('Erro ao atualizar interface:', error);
            Swal.fire({
                icon: 'error',
                title: 'Erro!',
                text: 'Erro ao atualizar a interface. Por favor, recarregue a página.'
            });
        }
    }

    atualizarCalendario() {
        const calendarEl = document.getElementById('calendario_agenda');
        if (calendarEl && calendarEl.fcCalendar) {
            calendarEl.fcCalendar.refetchEvents();
        }
    }

    fecharModal() {
        $('#modalEvento').remove();
        $(document).off('keydown.modal');
        this.modalAtivo = false;
    }
}

// Mover as funções para fora da classe EventManager
function abrirModalEstudoEstruturado(eventoId) {
    // Criar o modal se não existir
    let modal = document.getElementById('modal-estudo-estruturado');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'modal-estudo-estruturado';
        modal.style.cssText = 'display:none; position:fixed; z-index:99999; left:0; top:0; width:100%; height:100%; background:rgba(30,41,59,0.18); align-items:center; justify-content:center; backdrop-filter: blur(1px);';
        modal.innerHTML = `
            <div style="background:#fff; border-radius:16px; box-shadow:0 8px 32px rgba(0,0,0,0.18); width:95%; max-width:650px; min-height:600px; position:relative; overflow:hidden; margin: 20px auto; transform: translateY(-20px);">
                <button onclick="fecharModalEstudoEstruturado()" class="modal-close" style="position: absolute; top: 1rem; right: 1rem; background: var(--danger-color); border: none; width: 32px; height: 32px; border-radius: 50%; color: white; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.3s ease;">&times;</button>
                <iframe id="iframe-estudo-estruturado" src="" style="width:100%; height:95vh; border:none; border-radius:16px;"></iframe>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Mostrar o modal
    modal.style.display = 'flex';
    
    // Carregar o conteúdo no iframe
    const iframe = modal.querySelector('#iframe-estudo-estruturado');
    iframe.src = `estudo_estruturado.php?evento_id=${eventoId}`;

    // Fechar o modal atual do evento
    if (window.eventManager) {
        window.eventManager.fecharModal();
    }
}

function fecharModalEstudoEstruturado() {
    const modal = document.getElementById('modal-estudo-estruturado');
    if (modal) {
        modal.style.display = 'none';
        const iframe = modal.querySelector('#iframe-estudo-estruturado');
        if (iframe) {
            iframe.src = '';
        }
    }
}

// Adicionar eventos globais para fechar o modal
document.addEventListener('click', function(e) {
    const modal = document.getElementById('modal-estudo-estruturado');
    if (modal && e.target === modal) {
        fecharModalEstudoEstruturado();
    }
});

document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const modal = document.getElementById('modal-estudo-estruturado');
        if (modal && modal.style.display === 'flex') {
            fecharModalEstudoEstruturado();
        }
    }
});

// Inicialização
$(document).ready(() => {
    window.eventManager = new EventManager();
});

// Mover a função para o escopo global com o novo nome
window.abrirCronometroComCamposEstruturado = function(materia, curso, metodo, link_conteudo, ponto_estudado = '') {
    console.log('Parâmetros recebidos em abrirCronometroComCamposEstruturado:', {
        materia,
        curso,
        metodo,
        link_conteudo,
        ponto_estudado
    });
    
    // Construir a URL base usando URLSearchParams
    const params = new URLSearchParams();
    
    // Adicionar parâmetros obrigatórios
    if (materia) params.append('materia', materia);
    if (curso) params.append('curso', curso);
    if (metodo) params.append('metodo', metodo);
    if (link_conteudo) params.append('link_conteudo', link_conteudo);
    
    // Adicionar ponto estudado se existir
    if (ponto_estudado && typeof ponto_estudado === 'string' && ponto_estudado.trim() !== '') {
        console.log('Adicionando ponto estudado à URL:', ponto_estudado.trim()); // Debug
        params.append('ponto', encodeURIComponent(ponto_estudado.trim()));
    }
    
    // Construir a URL final
    const url = `0cronometro.php?${params.toString()}`;
    console.log('URL final gerada:', url); // Debug
    
    // Verificar se o ponto estudado está na URL
    const urlParams = new URLSearchParams(url.split('?')[1]);
    console.log('Parâmetros na URL final:', {
        materia: urlParams.get('materia'),
        curso: urlParams.get('curso'),
        metodo: urlParams.get('metodo'),
        link_conteudo: urlParams.get('link_conteudo'),
        ponto: urlParams.get('ponto')
    }); // Debug
    
    // Configurar a janela
    const largura = 1000;
    const altura = 800;
    const left = (screen.width - largura) / 2;
    const top = (screen.height - altura) / 2;
    
    // Abrir a janela com a URL completa
    const novaJanela = window.open(
        url,
        '_blank',
        `width=${largura},height=${altura},left=${left},top=${top},` +
        'scrollbars=yes,status=no,toolbar=no,location=no,directories=no,menubar=no,resizable=yes,fullscreen=no'
    );
    
    // Verificar se a janela foi aberta com sucesso
    if (novaJanela) {
        console.log('Janela do cronômetro aberta com sucesso');
    } else {
        console.error('Erro ao abrir janela do cronômetro');
        Swal.fire({
            icon: 'error',
            title: 'Erro!',
            text: 'Não foi possível abrir o cronômetro. Verifique se o bloqueador de pop-ups está desativado.'
        });
    }
};