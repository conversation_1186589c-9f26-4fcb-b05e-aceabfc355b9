   // Funções globais para manipulação do modal
   function fecharModalCategoria() {
    document.getElementById('modalCategoria').style.display = 'none';
    document.getElementById('select-categoria').value = '';
}

function continuarCriarPost() {
    const select = document.getElementById('select-categoria');
    const catId = select.value;
    if (!catId) {
        select.focus();
        select.style.borderColor = '#dc3545';
        setTimeout(() => { select.style.borderColor = 'var(--border)'; }, 1200);
        return;
    }
    window.location.href = `novo_topico.php?categoria=${catId}`;
}

// Função para controlar o modo escuro
function toggleDarkMode() {
    const body = document.body;
    const icon = document.querySelector('#toggleDarkMode i');
    
    // Alterna a classe dark-mode
    body.classList.toggle('dark-mode');
    
    // Atualiza o ícone
    if (body.classList.contains('dark-mode')) {
        icon.classList.remove('fa-moon');
        icon.classList.add('fa-sun');
        localStorage.setItem('darkMode', 'enabled');
    } else {
        icon.classList.remove('fa-sun');
        icon.classList.add('fa-moon');
        localStorage.setItem('darkMode', 'disabled');
    }
}

// Funções para o painel de filtros
function toggleFilterPanel() {
    const panel = document.getElementById('filterPanel');
    const overlay = document.querySelector('.filter-overlay');
    panel.classList.toggle('active');
    
    if (!overlay) {
        const newOverlay = document.createElement('div');
        newOverlay.className = 'filter-overlay';
        document.body.appendChild(newOverlay);
        newOverlay.addEventListener('click', toggleFilterPanel);
    } else {
        overlay.classList.toggle('active');
    }
}

function atualizarBadgeFiltros() {
    const badge = document.getElementById('filterBadge');
    const categoria = document.querySelector('input[name="categoria"]:checked').value;
    const periodo = document.querySelector('input[name="periodo"]:checked').value;
    const ordem = document.querySelector('input[name="ordem"]:checked').value;
    
    let contador = 0;
    if (categoria) contador++;
    if (periodo) contador++;
    if (ordem !== 'recentes') contador++;
    
    if (contador > 0) {
        badge.style.display = 'flex';
        badge.textContent = contador;
    } else {
        badge.style.display = 'none';
    }
}

function aplicarFiltros() {
    const categoria = document.querySelector('input[name="categoria"]:checked').value;
    const periodo = document.querySelector('input[name="periodo"]:checked').value;
    const ordem = document.querySelector('input[name="ordem"]:checked').value;

    // Construir a URL com os parâmetros de filtro
    const url = new URL(window.location);
    url.searchParams.set('categoria', categoria);
    url.searchParams.set('periodo', periodo);
    url.searchParams.set('ordem', ordem);
    
    // Atualizar a URL e carregar os tópicos filtrados
    window.history.pushState({}, '', url);
    carregarTopicosFiltrados(categoria, periodo, ordem);
    toggleFilterPanel();
    }

function limparFiltros() {
    // Resetar todos os radio buttons
    document.querySelectorAll('input[type="radio"]').forEach(radio => {
        if (radio.value === '') {
            radio.checked = true;
        } else if (radio.name === 'ordem' && radio.value === 'recentes') {
            radio.checked = true;
        }
    });

    // Limpar a URL e recarregar os tópicos
    const url = new URL(window.location);
    url.searchParams.delete('categoria');
    url.searchParams.delete('periodo');
    url.searchParams.delete('ordem');
    window.history.pushState({}, '', url);
    
    // Atualizar badge e recarregar tópicos
    atualizarBadgeFiltros();
    carregarTopicos('recentes');
}

function carregarTopicosFiltrados(categoria, periodo, ordem) {
    const topicsContainer = document.getElementById('topics-container');
    topicsContainer.innerHTML = '<div class="loading">Carregando tópicos...</div>';

    // Construir a URL com os parâmetros de filtro
    const params = new URLSearchParams({
        categoria: categoria || '',
        periodo: periodo || '',
        ordem: ordem || 'recentes'
    });

    fetch(`buscar_topicos.php?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.topicos && data.topicos.length > 0) {
                let html = '';
                data.topicos.forEach(topico => {
                    html += `
                        <div class="topic-card">
                            <div class="topic-content">
                                <div class="topic-category">
                                    <a href="ver_categoria.php?id=${topico.categoria_id}">c/${topico.categoria_nome}</a>
                                    <span class="topic-meta">• Postado por u/${topico.autor_nome.toLowerCase().replace(' ', '_')} • ${topico.tempo}</span>
                                </div>
                                <h2 class="topic-title">
                                    <a href="ver_topico.php?id=${topico.id}" onclick="incrementarVisualizacao(${topico.id})">${topico.titulo}</a>
                                </h2>
                                <p class="topic-excerpt">${topico.resumo}</p>
                                <div class="topic-actions">
                                    <a href="ver_topico.php?id=${topico.id}#comentarios" class="topic-action">
                                        <i class="far fa-comment-alt"></i> ${topico.total_respostas} comentários
                                    </a>
                                    <span class="topic-action">
                                        <i class="far fa-eye"></i> ${topico.views} visualizações
                                    </span>
                                    <button class="topic-action">
                                        <i class="fas fa-share"></i> Compartilhar
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;
                });
                topicsContainer.innerHTML = html;
            } else {
                topicsContainer.innerHTML = `
                    <div class="no-topics">
                        <p>Nenhum tópico encontrado com os filtros selecionados.</p>
                        <button onclick="limparFiltros()" class="btn-criar-post">Limpar Filtros</button>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Erro ao carregar tópicos:', error);
            topicsContainer.innerHTML = '<div class="error">Erro ao carregar tópicos. Tente novamente.</div>';
        });
}

    function carregarTopicos(tab) {
        const topicsContainer = document.getElementById('topics-container');
        topicsContainer.innerHTML = '<div class="loading">Carregando tópicos...</div>';

        fetch(`buscar_topicos.php?tab=${tab}`)
            .then(response => response.json())
            .then(data => {
                if (data.topicos && data.topicos.length > 0) {
                    let html = '';
                    data.topicos.forEach(topico => {
                        html += `
                            <div class="topic-card">
                                <div class="topic-content">
                                    <div class="topic-category">
                                        <a href="ver_categoria.php?id=${topico.categoria_id}">c/${topico.categoria_nome}</a>
                                        <span class="topic-meta">• Postado por u/${topico.autor_nome.toLowerCase().replace(' ', '_')} • ${topico.tempo}</span>
                                    </div>
                                    <h2 class="topic-title">
                                        <a href="ver_topico.php?id=${topico.id}" onclick="incrementarVisualizacao(${topico.id})">${topico.titulo}</a>
                                    </h2>
                                    <p class="topic-excerpt">${topico.resumo}</p>
                                    <div class="topic-actions">
                                        <a href="ver_topico.php?id=${topico.id}#comentarios" class="topic-action">
                                            <i class="far fa-comment-alt"></i> ${topico.total_respostas} comentários
                                        </a>
                                        <span class="topic-action">
                                            <i class="far fa-eye"></i> ${topico.views} visualizações
                                        </span>
                                        <button class="topic-action">
                                            <i class="fas fa-share"></i> Compartilhar
                                        </button>
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                    topicsContainer.innerHTML = html;
                } else {
                    topicsContainer.innerHTML = `
                        <div class="no-topics">
                            <p>Nenhum tópico encontrado. Seja o primeiro a criar um!</p>
                            <a href="novo_topico.php" class="btn-criar-post">Criar Tópico</a>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Erro ao carregar tópicos:', error);
                topicsContainer.innerHTML = '<div class="error">Erro ao carregar tópicos. Tente novamente.</div>';
            });
    }

// Verifica se o modo escuro estava ativado anteriormente
document.addEventListener('DOMContentLoaded', function() {
    const darkMode = localStorage.getItem('darkMode');
    const icon = document.querySelector('#toggleDarkMode i');
    
    if (darkMode === 'enabled') {
        document.body.classList.add('dark-mode');
        icon.classList.remove('fa-moon');
        icon.classList.add('fa-sun');
    }

    // Adiciona o evento de clique para o botão de modo escuro
    document.getElementById('toggleDarkMode').addEventListener('click', function(e) {
        e.preventDefault();
        toggleDarkMode();
    });

    // Adicionar eventos aos links das tabs
    const tabs = document.querySelectorAll('.forum-tabs .tab');
    tabs.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            const tabName = this.getAttribute('data-tab');
            
            // Atualizar classes ativas
            tabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            
            // Atualizar URL sem recarregar a página
            const url = new URL(window.location);
            url.searchParams.set('tab', tabName);
            window.history.pushState({}, '', url);
            
            // Carregar tópicos
            carregarTopicos(tabName);
        });
    });

    // Atualizar o evento do botão de filtro
    const btnFiltro = document.getElementById('btnFiltro');
    if (btnFiltro) {
        btnFiltro.addEventListener('click', function(e) {
            e.preventDefault();
            toggleFilterPanel();
        });
    }
    
    // Adicionar eventos para atualizar o badge
    document.querySelectorAll('.filter-option input[type="radio"]').forEach(radio => {
        radio.addEventListener('change', atualizarBadgeFiltros);
    });
    
    // Verificar filtros na URL ao carregar a página
    const urlParams = new URLSearchParams(window.location.search);
    const categoria = urlParams.get('categoria');
    const periodo = urlParams.get('periodo');
    const ordem = urlParams.get('ordem');

    if (categoria) {
        document.querySelector(`input[name="categoria"][value="${categoria}"]`).checked = true;
    }
    if (periodo) {
        document.querySelector(`input[name="periodo"][value="${periodo}"]`).checked = true;
    }
    if (ordem) {
        document.querySelector(`input[name="ordem"][value="${ordem}"]`).checked = true;
    }

    atualizarBadgeFiltros();
    atualizarBadgeNotificacoes();
});

// Notificações - Dropdown
const bell = document.getElementById('notificationBell');
const dropdown = document.getElementById('notificationDropdown');
const badge = document.getElementById('notificationBadge');

function formatarTempo(data) {
    const d = new Date(data);
    const agora = new Date();
    const diff = (agora - d) / 1000;
    if (diff < 60) return 'agora mesmo';
    if (diff < 3600) return Math.floor(diff/60) + ' min atrás';
    if (diff < 86400) return Math.floor(diff/3600) + 'h atrás';
    return d.toLocaleDateString('pt-BR');
}

// Atualiza o badge de notificações não lidas ao carregar a página
function atualizarBadgeNotificacoes() {
    fetch('buscar_notificacoes.php')
        .then(res => res.json())
        .then(data => {
            if (badge) {
                if (data.notificacoes && data.notificacoes.length > 0) {
                    const naoLidas = data.notificacoes.filter(n => !n.lida).length;
                    if (naoLidas > 0) {
                        badge.style.display = 'flex';
                        badge.textContent = naoLidas;
                    } else {
                        badge.style.display = 'none';
                    }
                } else {
                    badge.style.display = 'none';
                }
            }
        });
}

if (bell) {
    bell.addEventListener('click', function(e) {
                e.preventDefault();
        if (dropdown.style.display === 'block') {
            dropdown.style.display = 'none';
            return;
        }
        // Buscar notificações reais
        fetch('buscar_notificacoes.php')
            .then(res => res.json())
            .then(data => {
                const list = dropdown.querySelector('.dropdown-list');
                list.innerHTML = '';
                if (data.notificacoes && data.notificacoes.length > 0) {
                    let naoLidas = 0;
                    data.notificacoes.forEach(notif => {
                        if (!notif.lida) naoLidas++;
                        let link = notif.topico_id ? `ver_topico.php?id=${notif.topico_id}` : '#';
                        list.innerHTML += `
                            <li${notif.lida ? '' : ' style=\"background:#f0f6ff;\"'}>
                                <a href="${link}" style="text-decoration:none;color:inherit;display:block;">
                                    <span class=\"notif-title\">${notif.mensagem}</span><br>
                                    <span class=\"notif-time\">${formatarTempo(notif.data_criada)}</span>
                                </a>
                            </li>
                        `;
                    });
                    badge.style.display = naoLidas > 0 ? 'flex' : 'none';
                    badge.textContent = naoLidas;
                } else {
                    list.innerHTML = '<li><span class="notif-title">Nenhuma notificação</span></li>';
                    badge.style.display = 'none';
                }
                dropdown.style.display = 'block';
                // Marcar notificações como lidas
                fetch('marcar_notificacoes_lidas.php', { method: 'POST' })
                    .then(() => atualizarBadgeNotificacoes());
            });
        // Fecha ao clicar fora
        document.addEventListener('mousedown', function handler(event) {
            if (!dropdown.contains(event.target) && !bell.contains(event.target)) {
                dropdown.style.display = 'none';
                document.removeEventListener('mousedown', handler);
            }
        });
    });
}
document.addEventListener('DOMContentLoaded', function() {
    var btnCriarPost = document.getElementById('btnCriarPost');
    if (btnCriarPost) {
        btnCriarPost.addEventListener('click', function(e) {
            e.preventDefault();
            abrirModalCategoria(); // ou o nome da função que abre o modal
        });
    }
});

function abrirModalCategoria() {
    document.getElementById('modalCategoria').style.display = 'flex';
}

