<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

// Verifica se o usuário está logado e é admin
if (!isAdmin()) {
    header('Location: login.php');
    exit;
}

// Função para deletar assinatura
function deletarAssinatura($conexao, $id) {
    $sql = "DELETE FROM appestudo.assinaturas WHERE id = $1";
    $result = pg_query_params($conexao, $sql, array($id));
    return $result !== false;
}

// Função para buscar todas as assinaturas com dados do usuário e plano
function getAssinaturas($conexao) {
    $sql = "SELECT a.*, u.nome as nome_usuario, u.email, p.nome as nome_plano 
            FROM appestudo.assinaturas a 
            JOIN appestudo.usuario u ON a.usuario_id = u.idusuario 
            JOIN appestudo.planos p ON a.plano_id = p.id 
            ORDER BY a.data_inicio DESC";
            
    $result = pg_query($conexao, $sql);
    if (!$result) {
        error_log("Erro ao buscar assinaturas: " . pg_last_error($conexao));
        return [];
    }
    
    return pg_fetch_all($result);
}

// Função para atualizar status da assinatura
function atualizarStatusAssinatura($conexao, $id, $status) {
    $sql = "UPDATE appestudo.assinaturas 
            SET status = $1, updated_at = CURRENT_TIMESTAMP 
            WHERE id = $2";
            
    $result = pg_query_params($conexao, $sql, array($status, $id));
    return $result !== false;
}

// Processamento do formulário
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $id = (int)$_POST['assinatura_id'];
        
        switch ($_POST['action']) {
            case 'ativar':
                atualizarStatusAssinatura($conexao, $id, 't');
                break;
            case 'desativar':
                atualizarStatusAssinatura($conexao, $id, 'f');
                break;
            case 'deletar':
                deletarAssinatura($conexao, $id);
                break;
        }
    }
}

// Busca assinaturas
$assinaturas = getAssinaturas($conexao);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciamento de Assinaturas - Planeja AQUI</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        :root {
            --primary: #00008B;
            --primary-light: #1a1a9d;
            --secondary: #f8f0e3;
            --accent: #FF6B6B;
            --accent-dark: #FF4949;
            --success: #a645c4;
            --success-dark: #801a9f;
            --border: #e0e0e0;
            --text: #2c3e50;
            --hover: #f5f5f5;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --white: #ffffff;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Quicksand', sans-serif;
            color: var(--text);
            line-height: 1.6;
            background-color: var(--hover);
        }

        .header {
            background: var(--primary);
            padding: 1rem 0;
            box-shadow: 0 2px 10px var(--shadow-color);
            margin-bottom: 2rem;
        }

        .header h1 {
            color: var(--white);
            text-align: center;
            font-size: 2rem;
            margin: 0;
        }

        .container {
            max-width: 1600px;
            margin: 2rem auto;
            padding: 0 1rem;
        }

        .card {
            background: var(--white);
            border-radius: 10px;
            box-shadow: 0 4px 6px var(--shadow-color);
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .filters {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .form-control, .form-select {
            border: 1px solid var(--border);
            border-radius: 8px;
            padding: 0.75rem;
            font-family: 'Quicksand', sans-serif;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 0.2rem rgba(0, 0, 139, 0.25);
        }

        .table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table th {
            background-color: var(--primary);
            color: var(--white);
            padding: 1rem;
            font-weight: 600;
        }

        .table td {
            padding: 1rem;
            border-bottom: 1px solid var(--border);
        }

        .badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
        }

        .badge.bg-success {
            background-color: var(--success) !important;
        }

        .badge.bg-danger {
            background-color: var(--accent) !important;
        }

        .btn {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            font-family: 'Quicksand', sans-serif;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        .btn-success {
            background-color: var(--success);
            border-color: var(--success);
        }

        .btn-success:hover {
            background-color: var(--success-dark);
            border-color: var(--success-dark);
        }

        .btn-danger {
            background-color: var(--accent);
            border-color: var(--accent);
        }

        .btn-danger:hover {
            background-color: var(--accent-dark);
            border-color: var(--accent-dark);
        }

        .btn-info {
            background-color: var(--primary-light);
            border-color: var(--primary-light);
            color: var(--white);
        }

        .btn-info:hover {
            background-color: var(--primary);
            border-color: var(--primary);
            color: var(--white);
        }

        .modal-content {
            border-radius: 10px;
            box-shadow: 0 4px 6px var(--shadow-color);
        }

        .modal-header {
            background-color: var(--primary);
            color: var(--white);
            border-radius: 10px 10px 0 0;
        }

        .modal-body {
            padding: 2rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 0.5rem;
            }

            .card {
                padding: 1rem;
            }

            .table {
                display: block;
                overflow-x: auto;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <h1><i class="fas fa-users-cog"></i> Gerenciamento de Assinaturas</h1>
    </header>

    <div class="container">
        <div class="card">
            <!-- Filtros -->
            <div class="filters">
                <input type="text" id="buscarUsuario" class="form-control" placeholder="Buscar por usuário...">
                <select id="filtroStatus" class="form-select">
                    <option value="">Todos os status</option>
                    <option value="t">Ativas</option>
                    <option value="f">Inativas</option>
                </select>
                <select id="filtroModulo" class="form-select">
                    <option value="">Todos os módulos</option>
                    <option value="cronograma_inteligente">Cronograma Inteligente</option>
                </select>
            </div>

            <!-- Tabela -->
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Usuário</th>
                            <th>Email</th>
                            <th>Plano</th>
                            <th>Módulo</th>
                            <th>Início</th>
                            <th>Fim</th>
                            <th>Status</th>
                            <th>Valor Pago</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($assinaturas as $assinatura): ?>
                        <tr>
                            <td><?= htmlspecialchars($assinatura['id']) ?></td>
                            <td><?= htmlspecialchars($assinatura['nome_usuario']) ?></td>
                            <td><?= htmlspecialchars($assinatura['email']) ?></td>
                            <td><?= htmlspecialchars($assinatura['nome_plano']) ?></td>
                            <td><?= htmlspecialchars($assinatura['modulo']) ?></td>
                            <td><?= date('d/m/Y', strtotime($assinatura['data_inicio'])) ?></td>
                            <td><?= date('d/m/Y', strtotime($assinatura['data_fim'])) ?></td>
                            <td>
                                <span class="badge <?= $assinatura['status'] == 't' ? 'bg-success' : 'bg-danger' ?>">
                                    <?= $assinatura['status'] == 't' ? 'Ativa' : 'Inativa' ?>
                                </span>
                            </td>
                            <td>R$ <?= number_format((float)$assinatura['valor_pago'], 2, ',', '.') ?></td>
                            <td>
                                <div class="btn-group">
                                    <form method="POST" class="d-inline me-1">
                                        <input type="hidden" name="assinatura_id" value="<?= $assinatura['id'] ?>">
                                        <?php if ($assinatura['status'] == 't'): ?>
                                            <button type="submit" name="action" value="desativar" 
                                                    class="btn btn-sm btn-danger">
                                                <i class="fas fa-times"></i> Desativar
                                            </button>
                                        <?php else: ?>
                                            <button type="submit" name="action" value="ativar" 
                                                    class="btn btn-sm btn-success">
                                                <i class="fas fa-check"></i> Ativar
                                            </button>
                                        <?php endif; ?>
                                    </form>
                                    <button class="btn btn-sm btn-info me-1" onclick="verDetalhes(<?= $assinatura['id'] ?>)">
                                        <i class="fas fa-info-circle"></i> Detalhes
                                    </button>
                                    <button class="btn btn-sm btn-danger" 
                                            onclick="confirmarDelete(<?= $assinatura['id'] ?>, '<?= htmlspecialchars($assinatura['nome_usuario']) ?>')">
                                        <i class="fas fa-trash"></i> Excluir
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Modal de Detalhes -->
    <div class="modal fade" id="modalDetalhes" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Detalhes da Assinatura</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- Conteúdo será preenchido via JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Modal de Confirmação de Exclusão -->
    <div class="modal fade" id="modalDelete" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirmar Exclusão</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Tem certeza que deseja excluir a assinatura de <strong id="nomeUsuario"></strong>?</p>
                    <p class="text-danger">Esta ação não pode ser desfeita!</p>
                </div>
                <div class="modal-footer">
                    <form method="POST">
                        <input type="hidden" name="assinatura_id" id="deleteAssinaturaId">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
                        <button type="submit" name="action" value="deletar" class="btn btn-danger">
                            <i class="fas fa-trash"></i> Excluir
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Função para filtrar a tabela
        function filtrarTabela() {
            const busca = document.getElementById('buscarUsuario').value.toLowerCase();
            const status = document.getElementById('filtroStatus').value;
            const modulo = document.getElementById('filtroModulo').value;
            
            const linhas = document.querySelectorAll('tbody tr');
            
            linhas.forEach(linha => {
                const usuario = linha.cells[1].textContent.toLowerCase();
                const statusAtual = linha.cells[7].textContent.trim() === 'Ativa' ? 't' : 'f';
                const moduloAtual = linha.cells[4].textContent.trim();
                
                const matchBusca = usuario.includes(busca);
                const matchStatus = status === '' || status === statusAtual;
                const matchModulo = modulo === '' || modulo === moduloAtual;
                
                linha.style.display = matchBusca && matchStatus && matchModulo ? '' : 'none';
            });
        }

        // Adiciona eventos aos filtros
        document.getElementById('buscarUsuario').addEventListener('input', filtrarTabela);
        document.getElementById('filtroStatus').addEventListener('change', filtrarTabela);
        document.getElementById('filtroModulo').addEventListener('change', filtrarTabela);

        // Função para ver detalhes da assinatura
        async function verDetalhes(id) {
            try {
                const response = await fetch(`api/assinaturas.php?id=${id}`);
                if (!response.ok) throw new Error('Erro ao buscar dados');
                
                const dados = await response.json();
                
                const modalBody = document.querySelector('#modalDetalhes .modal-body');
                modalBody.innerHTML = `
                    <p><strong>ID:</strong> ${dados.id}</p>
                    <p><strong>Usuário:</strong> ${dados.nome_usuario}</p>
                    <p><strong>Plano:</strong> ${dados.nome_plano}</p>
                    <p><strong>Forma de Pagamento:</strong> ${dados.forma_pagamento || 'Não informado'}</p>
                    <p><strong>Código Transação:</strong> ${dados.codigo_transacao || 'Não informado'}</p>
                    <p><strong>Renovação Automática:</strong> ${dados.renovacao_automatica === 't' ? 'Sim' : 'Não'}</p>
                    <p><strong>Criado em:</strong> ${new Date(dados.created_at).toLocaleString()}</p>
                    <p><strong>Última Atualização:</strong> ${new Date(dados.updated_at).toLocaleString()}</p>
                `;
                
                const modal = new bootstrap.Modal(document.getElementById('modalDetalhes'));
                modal.show();
            } catch (erro) {
                console.error('Erro ao buscar detalhes:', erro);
                alert('Erro ao carregar detalhes da assinatura');
            }
        }

        // Função para confirmar exclusão
        function confirmarDelete(id, nome) {
            document.getElementById('nomeUsuario').textContent = nome;
            document.getElementById('deleteAssinaturaId').value = id;
            const modal = new bootstrap.Modal(document.getElementById('modalDelete'));
            modal.show();
        }

        // Animação suave para mensagens
        document.addEventListener('DOMContentLoaded', function() {
            // Adiciona classe para fade out após 5 segundos
            const mensagens = document.querySelectorAll('.alert');
            mensagens.forEach(mensagem => {
                setTimeout(() => {
                    mensagem.classList.add('fade');
                    setTimeout(() => mensagem.remove(), 500);
                }, 5000);
            });
        });
    </script>
</body>
</html>