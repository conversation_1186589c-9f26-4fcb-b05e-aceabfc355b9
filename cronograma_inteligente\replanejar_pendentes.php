<?php
// replanejar_pendentes.php (versão atualizada)
session_start();
require_once 'includes/verificar_modulo.php';
require_once 'includes/mensagens.php';
verificarModulo();

// Desativa a exibição de erros no output
ini_set('display_errors', 0);

// Define o tipo de conteúdo como JSON
header('Content-Type: application/json');

if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Usuário não autenticado']);
    exit();
}

// Inclui a configuração
require_once 'assets/config.php';

$usuario_id = $_SESSION['idusuario'];

try {
    // Recebe e valida o JSON
    $jsonData = file_get_contents('php://input');
    if (!$jsonData) {
        throw new Exception('Dados não recebidos');
    }

    $data = json_decode($jsonData, true);
    if (!$data || !isset($data['nova_data_inicio'])) {
        throw new Exception('Data de início não fornecida');
    }

    // Valida a data
    $dataObj = DateTime::createFromFormat('Y-m-d', $data['nova_data_inicio']);
    $dataObj->setTime(0, 0, 0); // Zera o horário para comparação
    if (!$dataObj) {
        throw new Exception('Formato de data inválido');
    }

    // Inicia a transação
    pg_query($conexao, "BEGIN");

    // 1. Busca a prova ativa com sua data
    $query_prova = "SELECT id, data_prova FROM appestudo.provas 
                   WHERE usuario_id = $1 AND status = true 
                   ORDER BY created_at DESC LIMIT 1";
    
    $result_prova = pg_query_params($conexao, $query_prova, array($usuario_id));
    if (!$result_prova || pg_num_rows($result_prova) == 0) {
        throw new Exception('Nenhuma prova ativa encontrada');
    }

    $prova = pg_fetch_assoc($result_prova);
    $prova_id = $prova['id'];
    $data_prova = new DateTime($prova['data_prova']);
    $data_prova->setTime(0, 0, 0); // Zera o horário para comparação
    // Verifica se a nova data é posterior à data da prova
    if ($dataObj > $data_prova) {
        throw new Exception('A nova data não pode ser posterior à data da prova');
    }

    // IMPORTANTE: Marca os conteúdos estudados com um sinalizador de replanejamento
    // Isso registra quais conteúdos foram estudados ANTES do replanejamento
    $query_marcar = "UPDATE appestudo.usuario_conteudo 
                     SET replanejado_em = NOW() 
                     WHERE usuario_id = $1 
                     AND status_estudo = 'Estudado'";
    
    $result_marcar = pg_query_params($conexao, $query_marcar, array($usuario_id));
    if (!$result_marcar) {
        throw new Exception('Erro ao marcar conteúdos já estudados: ' . pg_last_error($conexao));
    }

    // 2. Atualiza a data de início e marca como replanejado
    $query_update = "UPDATE appestudo.provas 
                    SET data_inicio_estudo = $1, 
                        replanejado = true,
                        data_replanejamento = NOW()
                    WHERE id = $2 AND usuario_id = $3";

    $result_update = pg_query_params($conexao, $query_update, array(
        $data['nova_data_inicio'],
        $prova_id,
        $usuario_id
    ));

    if (!$result_update) {
        throw new Exception('Erro ao atualizar data de início: ' . pg_last_error($conexao));
    }

    // Commit da transação
    pg_query($conexao, "COMMIT");

    // Retorna sucesso
    echo json_encode([
        'success' => true,
        'message' => 'Cronograma replanejado com sucesso! Os conteúdos já estudados foram preservados.',
        'data' => [
            'nova_data_inicio' => $data['nova_data_inicio'],
            'prova_id' => $prova_id,
            'replanejado' => true
        ]
    ]);

} catch (Exception $e) {
    // Rollback em caso de erro
    if (isset($conexao)) {
        pg_query($conexao, "ROLLBACK");
    }

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} finally {
    if (isset($conexao)) {
        pg_close($conexao);
    }
}

// Verifica se a nova data de início está na semana da prova
$data_prova = new DateTime($prova['data_prova']);
$nova_data = new DateTime($data['nova_data_inicio']);

// Calcula a data do início da semana da prova
$inicio_semana_prova = clone $data_prova;
while ($inicio_semana_prova->format('N') != 1) {
    $inicio_semana_prova->modify('-1 day');
}

// Se a nova data cair na semana da prova
if ($nova_data >= $inicio_semana_prova) {
    throw new Exception('A nova data deve ser anterior à semana da prova (antes de ' . $inicio_semana_prova->format('d/m/Y') . '). Escolha uma data pelo menos uma semana antes da prova.');
}
?>