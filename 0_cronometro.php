<?php
//0cronometro.php
session_start();
include_once("conexao_POST.php");
if (isset($_SESSION['idusuario'])) {
    $id_usuario = $_SESSION['idusuario'];

    // Consultar os dados do planejamento relacionado ao usuario logado
    $query_consultar_idplanejamento = "SELECT p.idplanejamento
    FROM appEstudo.planejamento p 
    WHERE p.usuario_idusuario = $1"; // SEGURO
    $resultado_idplanejamento = pg_query_params($conexao, $query_consultar_idplanejamento, array($id_usuario));

    if ($resultado_idplanejamento && pg_num_rows($resultado_idplanejamento) > 0) { // Adicionado pg_num_rows > 0
        $row_planejamento = pg_fetch_assoc($resultado_idplanejamento); // Renomeado $row para evitar conflito
        $id_planejamento = $row_planejamento['idplanejamento'];
    } else {
        // Logar o erro específico para o desenvolvedor
        error_log("Falha ao buscar ID do planejamento para usuário ID: {$id_usuario}. Erro PG: " . pg_last_error($conexao));
        // Mostrar mensagem amigável e talvez impedir o restante do carregamento do formulário
        // ou redirecionar com uma mensagem de erro.
        // Ex:
        echo "Erro ao carregar dados do planejamento. Tente novamente mais tarde.";
        // Você pode querer dar um exit aqui se o $id_planejamento for essencial para o restante da página.
        // Por exemplo, se não há planejamento, talvez o usuário devesse ser guiado a criar um.
        // (similar ao que você já faz no processa_index.php)
        exit;
    }



    // Consultar as matérias relacionadas ao planejamento do usuario logado
    $query_consultar_materias = "SELECT m.* FROM appEstudo.materia m 
    INNER JOIN appEstudo.planejamento_materia pm ON m.idmateria = pm.materia_idmateria
    WHERE pm.planejamento_idplanejamento = $1"; // SEGURO
    $resultado_materias = pg_query_params($conexao, $query_consultar_materias, array($id_planejamento));

    $materias = array();

// Armazenar as matérias no array $materias.
    while ($row = pg_fetch_assoc($resultado_materias)) {
        $materias[] = $row['nome'];
    }


    // Consultar os Cursos Registrados
    $query_consultar_cursos = "SELECT c.nome, c.logo_url
    FROM appEstudo.curso c
    INNER JOIN appEstudo.usuario_has_curso uc
    ON c.idcurso = uc.curso_idcurso
    WHERE uc.usuario_idusuario = $1"; // SEGURO
    $resultado_cursos = pg_query_params($conexao, $query_consultar_cursos, array($id_usuario));

    $cursos = array();
    while ($row = pg_fetch_assoc($resultado_cursos)) {
        $cursos[] = [
            'nome' => $row['nome'],
            'logo_url' => $row['logo_url']
        ];
    }


} else {
    $_SESSION['validacao'] = false;

    // Tenta obter o tema atual do cookie para aplicar à página de erro
    $current_theme = isset($_COOKIE['theme']) ? htmlspecialchars($_COOKIE['theme']) : 'light';

    // Limpar qualquer saída pendente para garantir que o modal seja a única coisa na página
    if (ob_get_level() > 0) {
        ob_end_clean();
    }

    // Heredoc para o HTML da página de erro
    echo <<<HTML
<!DOCTYPE html>
<html lang="pt-br" data-theme="{$current_theme}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Acesso Restrito - Relógio de Estudo</title>
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    
    <!-- Fontes e Ícones (essenciais para a página de erro) -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">

    <!-- Link para seu CSS principal (se quiser tentar usá-lo) ou estilos embutidos -->
    <link rel="stylesheet" href="assets/css/cronometro.css"> 
    
</head>
<body>
    <div class="error-modal-overlay">
        <div class="error-modal-content">
            <!-- <div class="modal-corner modal-corner-tl"></div> -->
            <!-- <div class="modal-corner modal-corner-tr"></div> -->
            <h3 class="error-modal-title">
                <i class="fas fa-shield-alt error-modal-icon"></i> <!-- Ícone alterado -->
                Acesso Restrito
            </h3>
            <p class="error-modal-message">
                Ops! Parece que você não está logado ou sua sessão expirou.
                Para continuar, por favor, faça o login.
            </p>
            <button class="error-modal-button" onclick="window.location.href='login_index.php'">
                <i class="fas fa-sign-in-alt" style="margin-right: 8px;"></i>Ir para Login
            </button>
            <!-- <div class="modal-corner modal-corner-bl"></div> -->
            <!-- <div class="modal-corner modal-corner-br"></div> -->
        </div>
    </div>
    <script>
        // Para o caso de o tema ser trocado em outra aba e o cookie não ser atualizado a tempo
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            document.documentElement.setAttribute('data-theme', savedTheme);
        }
    </script>
</body>
</html>
HTML;
    exit; // Fundamental para parar a execução do script aqui
}

// Consultar os métodos de estudo ativos
$query_consultar_metodos = "SELECT nome, descricao FROM appEstudo.metodo_estudo WHERE ativo = true ORDER BY nome";
$resultado_metodos = pg_query($conexao, $query_consultar_metodos);

$metodos = array();
while ($row = pg_fetch_assoc($resultado_metodos)) {
    $metodos[] = $row['nome'];
}

$modal_sucesso = false;
$dados_estudo = [];
if (isset($_GET['sucesso']) && $_GET['sucesso'] == 1 && isset($_GET['dados'])) {
    $modal_sucesso = true;
    $dados_estudo = json_decode($_GET['dados'], true);
}

?>


<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <title>Relógio de Estudo</title>
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400&family=Special+Elite&family=Cinzel:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Digital+7&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="https://fonts.cdnfonts.com/css/quartz-ms?styles=70377" rel="stylesheet">
    <link href="https://fonts.cdnfonts.com/css/digital-7-mono?styles=24170,24171,24172,24169" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="./assets/css/cronometro.css">

</head>
<body>
<?php if ($modal_sucesso): ?>
<div class="modal-overlay" id="modalSucesso" style="display:flex;z-index:2000;">
    <div class="modal-content">
        <h1 class="modal-title" style="font-family:'Cinzel',serif;color:#00008B;text-align:center;font-size:2rem;margin-bottom:1.2rem;letter-spacing:1px;font-weight:700;">Estudo Registrado com Sucesso!</h1>
        <div class="study-details">
            <p><strong>Matéria:</strong> <?= htmlspecialchars($dados_estudo['materia']) ?></p>
            <p><strong>Ponto Estudado:</strong> <?= htmlspecialchars($dados_estudo['ponto_estudado']) ?></p>
            <p><strong>Tempo Líquido:</strong> <?= htmlspecialchars($dados_estudo['tempo_estudo']) ?></p>
            <p><strong>Tempo Bruto:</strong> <?= htmlspecialchars($dados_estudo['tempo_bruto']) ?></p>
            <p><strong>Tempo Perdido:</strong> <?= htmlspecialchars($dados_estudo['tempo_perdido']) ?></p>
            <p><strong>Data:</strong> <?= htmlspecialchars($dados_estudo['data_estudo']) ?></p>
            <p><strong>Registro Início:</strong> <?= htmlspecialchars($dados_estudo['tempo_inicio_estudo']) ?></p>
            <p><strong>Registro Fim:</strong> <?= htmlspecialchars($dados_estudo['tempo_fim_estudo']) ?></p>
            <p><strong>Método de Estudo:</strong> <?= htmlspecialchars($dados_estudo['metodo']) ?></p>
            <p><strong>Curso:</strong> <?= htmlspecialchars($dados_estudo['curso']) ?></p>
            <?php if (!empty($dados_estudo['descricao'])): ?>
                <p><strong>Observações:</strong> <?= htmlspecialchars($dados_estudo['descricao']) ?></p>
            <?php endif; ?>
            <?php if (!empty($dados_estudo['link_conteudo'])): ?>
                <p><strong>Link do Conteúdo:</strong> <a href="<?= htmlspecialchars($dados_estudo['link_conteudo']) ?>" target="_blank" style="color:#00008B;word-break:break-all;display:inline-block;max-width:100%;text-decoration:underline;"><?= htmlspecialchars($dados_estudo['link_conteudo']) ?></a></p>
            <?php endif; ?>
        </div>
        <button class="btn-close" id="fecharModalSucesso" style="font-family:'Cinzel',serif;background:#00008B;color:#fff;border:none;border-radius:8px;padding:0.9rem 2.2rem;font-size:1.1rem;text-transform:uppercase;letter-spacing:2px;cursor:pointer;margin:0 auto;display:flex;align-items:center;justify-content:center;transition:background 0.2s,color 0.2s,box-shadow 0.2s;box-shadow:0 2px 8px rgba(0,0,0,0.08);text-align:center;width:fit-content;">Fechar Página</button>
    </div>
</div>
<script>
    document.body.style.overflow = 'hidden';
    document.getElementById('fecharModalSucesso').onclick = function() {
        // Dispara evento para recarregar index.php em qualquer aba aberta
        localStorage.setItem('recarregarIndex', Date.now().toString());
        setTimeout(function() {
            window.close();
        }, 300);
    };
</script>
<?php endif; ?>

<!-- HTML do Modal -->
<div class="modal-overlay" id="confirmModal" style="display: none;">
    <div class="modal-content">
        <div class="modal-corner modal-corner-tl"></div>
        <div class="modal-corner modal-corner-tr"></div>
        <div class="modal-corner modal-corner-bl"></div>
        <div class="modal-corner modal-corner-br"></div>

        <h3 class="modal-title">Confirmar Ação</h3>
        <p class="modal-message">Tem certeza que deseja zerar o cronômetro?</p>

        <div class="modal-buttons">
            <button class="modal-btn modal-btn-confirm" onclick="confirmarZerar()">Confirmar</button>
            <button class="modal-btn modal-btn-cancel" onclick="closeModal()">Cancelar</button>
        </div>
    </div>
</div>

<div class="container">
    <div class="elegant-paper">
        <button id="theme-toggle-btn" class="theme-toggle-btn" aria-label="Alternar modo escuro">
            <i id="theme-icon" class="fas fa-moon"></i>
        </button>
        <button id="fullscreen-toggle-btn" class="fullscreen-toggle-btn" aria-label="Modo tela cheia">
            <i id="fullscreen-icon" class="fas fa-expand"></i>
        </button>
        <div class="corner-ornament"></div>
        <div class="corner-ornament"></div>
        <div class="corner-ornament"></div>
        <div class="corner-ornament"></div>
        <!-- <div class="vintage-seal"></div> -->

        <h1>Relógio de Estudo</h1>

        <div class="timer-display">
    <div class="timer-label">Tempo Líquido</div>
    <div class="lost-time-counter" id="lost-time-counter">00:00:00</div>
    <h2 id="timer">00:00:00</h2>
</div>

        <div class="button-group">
            <button id="btn_iniciar" class="btn btn-elegant"><i class="fas fa-play me-2"></i>Iniciar</button>
            <button id="btn_continuar" class="btn btn-elegant btn-continuar"><i class="fas fa-play me-2"></i>Continuar</button>
            <button id="btn_pausar" class="btn btn-elegant btn-pausar"><i class="fas fa-pause me-2"></i>Pausar</button>
            <button id="btn_zerar" class="btn btn-elegant btn-zerar"><i class="fas fa-times-circle me-2"></i>Zerar</button>
            <button id="btn_gravar" class="btn btn-elegant btn-salvar"><i class="fas fa-save me-2"></i>Salvar</button>
        </div>

        <form method="POST" action="processa_POST.php">
            <div class="elegant-checkbox">
                <input type="checkbox" id="checkboxSalvar">
                <label for="checkboxSalvar">Registro Manual</label>
            </div>

            <div class="form-group">
                <label class="form-label"><i class="fas fa-book"></i> Matéria</label>
                <select name="materia" class="form-select" required>
                    <option value="">Selecione uma matéria</option>
                    <?php foreach ($materias as $materia): ?>
                        <option value="<?php echo htmlspecialchars($materia); ?>"><?php echo htmlspecialchars($materia); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="form-group">
                <label class="form-label"><i class="fas fa-graduation-cap"></i> Curso</label>
                <div class="curso-select-wrapper" style="display:flex;align-items:center;gap:10px;">
                    <img id="logo_curso_preview" src="/logo/Estudo Off/favicon_32x32.png" style="width:53.19px;height:50.19px;object-fit:contain;border-radius:5px;background:#fff;border:1px solid #eee;" alt="Logo do curso">
<style>
.logo-curso-highlight {
    border: 1px solid var(--primary) !important;
}
</style>
                    <select name="curso" class="form-select" id="selectCurso" required style="width:auto;flex:1;">
                        <option value="">Selecione um curso</option>
                        <?php foreach ($cursos as $curso): ?>
                            <option value="<?php echo htmlspecialchars($curso['nome']); ?>" data-logo="<?php echo htmlspecialchars($curso['logo_url']); ?>">
                                <?php echo $curso['nome']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <script>
                document.addEventListener('DOMContentLoaded', function() {
                    var select = document.getElementById('selectCurso');
                    var img = document.getElementById('logo_curso_preview');
                    select.addEventListener('change', function() {
                        var logo = select.options[select.selectedIndex].getAttribute('data-logo');
                        img.src = logo ? '/cadastros/img/cursos/' + logo.split('/').pop() : '/logo/Estudo Off/favicon_32x32.png';
                    });
                });
                </script>
                <script>
                document.addEventListener('DOMContentLoaded', function() {
                    var select = document.getElementById('selectCurso');
                    var img = document.getElementById('logo_curso_preview');
                    select.addEventListener('focus', function() {
                        img.classList.add('logo-curso-highlight');
                    });
                    select.addEventListener('blur', function() {
                        img.classList.remove('logo-curso-highlight');
                    });
                });
                </script>
            </div>

            <div class="form-group">
                <label class="form-label"><i class="fas fa-lightbulb"></i> Método de Estudo</label>
                <select name="metodo" class="form-select" onchange="mostrarCampos(this.value)" required>
                    <option value="">Selecione um método</option>
                    <?php foreach ($metodos as $metodo): ?>
                        <option value="<?php echo htmlspecialchars($metodo); ?>"><?php echo htmlspecialchars($metodo); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div id="campos-simulado" style="display: none;">
                <div class="row g-4">
                    <div class="col-md-4">
                        <label class="form-label">Total</label>
                        <input type="number" name="num-questoes" class="form-control" min="0" value="0">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Erradas</label>
                        <input type="number" name="questoes-erradas" class="form-control" min="0" value="0">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Certas</label>
                        <input type="number" name="questoes-certas" class="form-control" min="0" value="0">
                    </div>
                </div>
            </div>

            <div class="time-inputs">
                <div class="form-group">
                    <label class="form-label"><i class="fas fa-clock me-2"></i>Tempo Líquido</label>
                    <input type="time" id="tempo" name="tempo" step="1" class="form-control">
                    <div class="error-message">Este campo é obrigatório</div>
                </div>
                <div class="form-group">
                    <label class="form-label"><i class="fas fa-stopwatch me-2"></i>Tempo Bruto</label>
                    <input type="time" id="tempoTotal" name="tempoTotal" step="1" class="form-control">
                    <div class="error-message">Este campo é obrigatório</div>
                </div>
                <div class="form-group">
                    <label class="form-label"><i class="fas fa-pause-circle me-2"></i>Tempo Perdido</label>
                    <input type="time" id="tempoPausado" name="tempoPausado" step="1" class="form-control">
                    <div class="error-message">Este campo é obrigatório</div>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label"><i class="fas fa-pencil-alt"></i> Ponto Estudado</label>
                <textarea name="ponto" class="form-control placeholder-1" rows="4" placeholder="Ponto que você Estudou"></textarea>
            </div>

            <div class="form-group">
                <label class="form-label"><i class="fas fa-comment"></i> Observações</label>
                <textarea name="descricao" class="form-control" rows="3" placeholder="Observações adicionais do Ponto estudado (opcional)"></textarea>          
            </div>

            <div class="form-group">
                <label class="form-label"><i class="fas fa-link"></i> Link do Conteúdo</label>
                <input type="url" name="link_conteudo" class="form-control" placeholder="Cole aqui o link do conteúdo estudado (opcional)" pattern="https?://.+">
                <small class="form-text text-muted-1">Ex: https://www.youtube.com/watch?v=..., https://docs.google.com/...</small>
            </div>

            <div class="form-group">
                <label class="form-label"><i class="fas fa-calendar-alt"></i> Data</label>
                <div class="date-input-wrapper">
                    <input type="date" name="data" id="dataField" class="form-control date-input dark-date-input">
                    <i class="fas fa-calendar-alt date-custom-icon"></i>
                </div>
            </div>

            <div class="button-group">
                <button type="submit" class="btn btn-elegant"><i class="fas fa-check-circle me-2"></i>Registrar</button>
                <button type="button" class="btn btn-elegant btn-zerar" onclick="window.close();"><i class="fas fa-times me-2"></i>Fechar</button>
            </div>

            <input type="hidden" name="id_planejamento" value="<?php echo htmlspecialchars($id_planejamento); ?>">
            <input type="text" id="tempoInicialEstudo" name="tempoInicialEstudo" class="desabilitado" required readonly hidden>
            <input type="text" id="tempoFinalEstudo" name="tempoFinalEstudo" class="desabilitado" required readonly hidden>
        </form>
    </div>
</div>

<div id="alert-placeholder"></div>

<script>
    // Configuração inicial da data
    const dataAtual = new Date();
    const ano = dataAtual.getFullYear();
    const mes = String(dataAtual.getMonth() + 1).padStart(2, '0');
    const dia = String(dataAtual.getDate()).padStart(2, '0');
    const dataFormatada = `${ano}-${mes}-${dia}`;
    document.getElementById('dataField').value = dataFormatada;

    // Função para mostrar/ocultar campos de simulado
    function mostrarCampos(valor) {
        const camposSimulado = document.getElementById("campos-simulado");
        if (valor === "Simulado" || valor === "Questões") {
            camposSimulado.style.display = "block";
        } else {
            camposSimulado.style.display = "none";
        }
    }

    // Validação de formulário elegante
    document.querySelector('form').addEventListener('submit', function(event) {
        const checkboxSalvar = document.getElementById('checkboxSalvar');
        if (checkboxSalvar.checked) {
            const tempoLiquido = document.getElementById('tempo').valueAsNumber;
            const tempoBruto = document.getElementById('tempoTotal').valueAsNumber;

            if (tempoLiquido > tempoBruto) {
                event.preventDefault();
                const alertPlaceholder = document.getElementById('alert-placeholder');

                const alertHTML = `
                <div class="alert-overlay">
                    <div class="alert-container">
                        <div class="alert" style="
                            background: var(--parchment);
                            border: 2px solid var(--burgundy);
                            padding: 2rem;
                            text-align: center;
                            position: relative;
                            max-width: 500px;
                            margin: 1rem;
                        ">
                            <h4 style="color: var(--burgundy); font-family: 'Cinzel', serif; margin-bottom: 1rem;">
                                Atenção
                            </h4>
                            <p style="color: var(--dark-ink); margin-bottom: 1.5rem;">
                                O tempo <strong>LÍQUIDO</strong> deve ser <strong>MENOR</strong> ou <strong>IGUAL</strong> ao tempo <strong>BRUTO</strong>.
                            </p>
                            <button class="btn btn-elegant" onclick="this.closest('.alert-overlay').remove();">
                                Entendi
                            </button>
                        </div>
                    </div>
                </div>
            `;

                alertPlaceholder.innerHTML = alertHTML;
            }
            if (tempoLiquido <= 0) {
                event.preventDefault();
                const alertPlaceholder = document.getElementById('alert-placeholder');

                const alertHTML = `
                <div class="alert-overlay">
                    <div class="alert-container">
                        <div class="alert" style="
                            background: var(--parchment);
                            border: 2px solid var(--burgundy);
                            padding: 2rem;
                            text-align: center;
                            position: relative;
                            max-width: 500px;
                            margin: 1rem;
                        ">
                            <h4 style="color: var(--burgundy); font-family: 'Cinzel', serif; margin-bottom: 1rem;">
                                Atenção
                            </h4>
                            <p style="color: var(--dark-ink); margin-bottom: 1.5rem;">
                                O tempo <strong>LÍQUIDO</strong> não Pode está <strong>ZERADO</strong>.
                            </p>
                            <button class="btn btn-elegant" onclick="this.closest('.alert-overlay').remove();">
                                Entendi
                            </button>
                        </div>
                    </div>
                </div>
            `;

                alertPlaceholder.innerHTML = alertHTML;
            }
        }
    });

    // Funções para o modal
    function showModal() {
        document.getElementById('confirmModal').style.display = 'flex';
        document.body.style.overflow = 'hidden'; // Previne rolagem
    }

    function closeModal() {
        document.getElementById('confirmModal').style.display = 'none';
        document.body.style.overflow = 'auto'; // Restaura rolagem
    }

    function confirmarZerar() {
        // Aqui vai sua lógica para zerar o cronômetro
        closeModal();
    }

    // Evento para fechar o modal clicando fora
    document.getElementById('confirmModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });

    // Modifique o evento do botão zerar para mostrar o modal
    document.getElementById('btn_zerar').onclick = function() {
        showModal();
    };

    // Função para controlar os campos de tempo
    function controlarCamposTempo() {
        const checkbox = document.getElementById('checkboxSalvar');
        const tempoInputs = document.querySelectorAll('.time-inputs input[type="time"]');
        const timeInputsContainer = document.querySelector('.time-inputs');
        const form = document.querySelector('form');

        // Função para validar os campos
        function validarCampos(e) {
            if (checkbox.checked) {
                let isValid = true;
                let firstError = null;

                tempoInputs.forEach(input => {
                    // Remove qualquer estilo de erro existente
                    input.classList.remove('input-error');
                    const errorDiv = input.parentElement.querySelector('.error-message');
                    if (errorDiv) errorDiv.remove();

                    // Verifica se o campo está vazio
                    if (!input.value) {
                        isValid = false;
                        input.classList.add('input-error');

                        // Cria mensagem de erro
                        const errorMessage = document.createElement('div');
                        errorMessage.className = 'error-message';
                        errorMessage.style.color = '#8B0000';
                        errorMessage.style.fontSize = '0.85rem';
                        errorMessage.style.marginTop = '0.5rem';
                        errorMessage.textContent = 'Este campo é obrigatório';
                        input.parentElement.appendChild(errorMessage);

                        if (!firstError) firstError = input;
                    }
                });

                if (!isValid) {
                    e.preventDefault(); // Impede o envio do formulário

                    // Scroll até o primeiro erro
                    if (firstError) {
                        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }

                    // Mostra mensagem de alerta elegante
                    const alertHTML = `
                <div class="alert-overlay">
                    <div class="alert-container">
                        <div class="alert" style="
                            background: var(--parchment);
                            border: 2px solid var(--burgundy);
                            padding: 2rem;
                            text-align: center;
                            position: relative;
                            max-width: 500px;
                            margin: 1rem;
                        ">
                            <h4 style="color: var(--burgundy); font-family: 'Cinzel', serif; margin-bottom: 1rem;">
                                Atenção
                            </h4>
                            <p style="color: var(--dark-ink); margin-bottom: 1.5rem;">
                                Por favor, preencha todos os campos de tempo quando o Registro Manual estiver ativo.
                            </p>
                            <button class="btn btn-elegant" onclick="this.closest('.alert-overlay').remove();">
                                Entendi
                            </button>
                        </div>
                    </div>
                </div>
            `;

                    document.getElementById('alert-placeholder').innerHTML = alertHTML;
                    return false;
                }
            }
            return true;
        }

        // Função para atualizar o estado dos campos
        function atualizarEstadoCampos() {
            tempoInputs.forEach(input => {
                if (checkbox.checked) {
                    input.removeAttribute('readonly'); // Remove o readonly se checkbox estiver marcado
                    input.setAttribute('required', '');
                    input.classList.add('required-field');
                    input.parentElement.classList.add('required');
                } else {
                    input.setAttribute('readonly', ''); // Define readonly ao invés de disabled
                    input.removeAttribute('required');
                    input.classList.remove('required-field');
                    input.parentElement.classList.remove('required');
                    // Limpa mensagens de erro quando desativa o checkbox
                    const errorDiv = input.parentElement.querySelector('.error-message');
                    if (errorDiv) errorDiv.remove();
                }
            });

            if (checkbox.checked) {
                timeInputsContainer.classList.remove('disabled');
            } else {
                timeInputsContainer.classList.add('disabled');
            }
        }

        // Modifique o event listener do form para incluir a validação dos selects
        form.addEventListener('submit', function(e) {
            e.preventDefault(); // Previne o comportamento padrão primeiro

            const curso = document.querySelector('select[name="curso"]');
            const metodo = document.querySelector('select[name="metodo"]');
            const pontoEstudado = document.querySelector('textarea[name="ponto"]');
            let isValid = true;

            // Remove classes de erro existentes
            curso.classList.remove('input-error');
            metodo.classList.remove('input-error');
            pontoEstudado.classList.remove('input-error');

            // Valida curso
            if (!curso.value) {
                curso.classList.add('input-error');
                curso.focus();
                isValid = false;
            }

            // Valida método
            if (!metodo.value) {
                metodo.classList.add('input-error');
                if (isValid) { // Só foca se for o primeiro erro
                    metodo.focus();
                }
                isValid = false;
            }

            // Valida ponto estudado
            if (!pontoEstudado.value.trim()) {
                mostrarAlertaElegante('Por favor, descreva o conteúdo estudado nesta sessão.');
                pontoEstudado.classList.add('input-error');
                if (isValid) { // Só foca se for o primeiro erro
                    pontoEstudado.focus();
                }
                isValid = false;
            }

            // Valida os tempos
            if (!validarTempos()) {
                isValid = false;
            }

            // Se passou por todas as validações, envia o formulário
            if (isValid) {
                form.submit();
            }
        });

        // Adicione eventos para remover o destaque de erro quando o usuário selecionar algo
        document.querySelector('select[name="curso"]').addEventListener('change', function() {
            this.classList.remove('input-error');
        });

        document.querySelector('select[name="metodo"]').addEventListener('change', function() {
            this.classList.remove('input-error');
        });

        // Adiciona validação ao botão de gravar
        const btnGravar = document.getElementById('btn_gravar');
        if (btnGravar) {
            btnGravar.addEventListener('click', function(e) {
                if (checkbox.checked && !validarCampos(e)) {
                    e.preventDefault();
                }
            });
        }

        // Atualiza estado inicial
        atualizarEstadoCampos();

        // Adiciona listener para mudanças no checkbox
        checkbox.addEventListener('change', atualizarEstadoCampos);
    }

    // Inicializa o controle quando o documento carregar
    document.addEventListener('DOMContentLoaded', controlarCamposTempo);

</script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const materia = urlParams.get('materia');
        const ponto = urlParams.get('ponto');
        const curso = urlParams.get('curso');
        const metodo = urlParams.get('metodo');
        const linkConteudo = urlParams.get('link_conteudo');

        if (materia) {
            const selectMateria = document.querySelector('select[name="materia"]');
            const options = Array.from(selectMateria.options);
            const matchingOption = options.find(option => option.value === materia);

            if (matchingOption) {
                selectMateria.value = materia;
            } else {
                // Modal de alerta quando a matéria não existe
                const alertHTML = `
               <div class="alert-overlay">
                   <div class="alert" style="
                       background: var(--parchment);
                       border: 2px solid var(--burgundy);
                       padding: 2rem;
                       text-align: center;
                       position: relative;
                       max-width: 500px;
                       margin: 1rem;
                       box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                   ">
                       <h4 style="color: var(--burgundy); font-family: 'Cinzel', serif; margin-bottom: 1rem;">
                           Atenção
                       </h4>
                       <p style="color: var(--dark-ink); margin-bottom: 1.5rem;">
                           Esta matéria ainda não está no seu planejamento. Por favor, adicione-a ao seu planejamento primeiro.
                       </p>
                       <button class="btn btn-elegant" onclick="this.closest('.alert-overlay').remove(); window.close();">
                           Entendi
                       </button>
                   </div>
               </div>
           `;
                document.getElementById('alert-placeholder').innerHTML = alertHTML;
            }
        }

        if (ponto) {
            document.querySelector('textarea[name="ponto"]').value = decodeURIComponent(ponto);
        }

        if (curso) {
            const selectCurso = document.querySelector('select[name="curso"]');
            if (selectCurso) {
                for (let opt of selectCurso.options) {
                    if (opt.value === curso) {
                        selectCurso.value = curso;
                        selectCurso.dispatchEvent(new Event('change'));
                        break;
                    }
                }
            }
        }

        if (metodo) {
            const selectMetodo = document.querySelector('select[name="metodo"]');
            if (selectMetodo) {
                for (let opt of selectMetodo.options) {
                    if (opt.value === metodo) {
                        selectMetodo.value = metodo;
                        break;
                    }
                }
            }
        }

        if (linkConteudo) {
            const inputLink = document.querySelector('input[name="link_conteudo"]');
            if (inputLink) {
                inputLink.value = decodeURIComponent(linkConteudo);
            }
        }
    });
</script>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="assets/js/cronometro.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', () => {
        const themeToggleBtn = document.getElementById('theme-toggle-btn');
        const themeIcon = document.getElementById('theme-icon');
        const html = document.documentElement;
        
        // Verifica se há preferência salva
        const currentTheme = localStorage.getItem('theme') || 'light';
        html.setAttribute('data-theme', currentTheme);
        updateIcon(currentTheme);
        
        themeToggleBtn.addEventListener('click', () => {
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateIcon(newTheme);
        });
        
        function updateIcon(theme) {
            if (theme === 'dark') {
                themeIcon.classList.remove('fa-moon');
                themeIcon.classList.add('fa-sun');
            } else {
                themeIcon.classList.remove('fa-sun');
                themeIcon.classList.add('fa-moon');
            }
        }
    });
</script>

</body>
</html>
