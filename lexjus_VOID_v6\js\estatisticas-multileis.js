/**
 * Sistema de Estatísticas Multi-Leis
 * Gerencia estatísticas separadas por lei
 */

class EstatisticasMultiLeis {
    constructor() {
        this.leiAtual = null;
        this.estatisticas = {};
        this.intervalos = {};
        
        this.init();
    }
    
    init() {
        this.bindEventos();
        this.detectarLeiAtual();
        this.inicializarBotaoRevisao();
        this.iniciarAtualizacaoAutomatica();
    }

    /**
     * Inicializa o estado do botão de revisão
     */
    inicializarBotaoRevisao() {
        // Garantir que o botão esteja sempre visível e habilitado
        const btnRevisao = document.getElementById('btnRevisao');
        if (btnRevisao) {
            btnRevisao.style.display = 'flex';
            btnRevisao.style.opacity = '1';
            btnRevisao.style.cursor = 'pointer';
            btnRevisao.classList.remove('disabled');
            btnRevisao.title = 'Sistema de Revisão';
            console.log(`📊 Botão de revisão inicializado como visível e habilitado`);
        }
    }

    /**
     * Detecta a lei atual do sistema
     */
    detectarLeiAtual() {
        // Tentar obter lei do sistema multi-leis
        if (window.sistemaMultiLeis && window.sistemaMultiLeis.leiAtual) {
            this.leiAtual = window.sistemaMultiLeis.leiAtual;
            console.log(`📊 Lei detectada do sistema multi-leis: ${this.leiAtual}`);
            this.atualizarEstatisticas();
            return;
        }

        // Tentar obter lei do data attribute do body
        const leiCodigo = document.body.getAttribute('data-lei-codigo');
        if (leiCodigo) {
            this.leiAtual = leiCodigo;
            console.log(`📊 Lei detectada do data attribute: ${this.leiAtual}`);
            this.atualizarEstatisticas();
            return;
        }

        // Tentar obter lei da URL
        const urlParams = new URLSearchParams(window.location.search);
        const leiURL = urlParams.get('lei');
        if (leiURL) {
            this.leiAtual = leiURL;
            console.log(`📊 Lei detectada da URL: ${this.leiAtual}`);
            this.atualizarEstatisticas();
            return;
        }

        console.warn('📊 Nenhuma lei detectada para estatísticas');
    }
    
    /**
     * Vincula eventos do sistema
     */
    bindEventos() {
        // Escutar mudanças de lei
        document.addEventListener('leiAlterada', (evento) => {
            this.onLeiAlterada(evento.detail.leiAtual);
        });
        
        // Escutar atualizações de progresso
        document.addEventListener('progressoAtualizado', () => {
            this.atualizarEstatisticas();
        });
        
        // Escutar mudanças no sistema de revisão
        document.addEventListener('revisaoAtualizada', (evento) => {
            console.log(`📊 Evento revisaoAtualizada recebido:`, evento.detail);

            // Se é a primeira revisão sendo adicionada, mostrar botão imediatamente
            if (evento.detail.acao === 'adicionada') {
                const btnRevisao = document.getElementById('btnRevisao');
                if (btnRevisao && btnRevisao.style.display === 'none') {
                    console.log(`📊 Primeira revisão detectada - mostrando botão imediatamente`);
                    this.atualizarVisibilidadeBotaoRevisao(1);
                }
            }

            // Forçar atualização imediata das estatísticas
            setTimeout(() => {
                this.atualizarEstatisticas();
            }, 500); // Pequeno delay para garantir que o banco foi atualizado
        });

        // Escutar quando sistema multi-leis estiver pronto
        document.addEventListener('sistemaMultiLeisCarregado', () => {
            this.detectarLeiAtual();
        });
    }
    
    /**
     * Quando lei é alterada
     */
    onLeiAlterada(novaLei) {
        this.leiAtual = novaLei;
        this.atualizarEstatisticas();
    }
    
    /**
     * Atualiza estatísticas da lei atual
     */
    async atualizarEstatisticas() {
        if (!this.leiAtual) {
            console.warn('📊 Não é possível atualizar estatísticas: leiAtual não definida');
            return;
        }

        console.log(`📊 Atualizando estatísticas para lei: ${this.leiAtual}`);

        try {
            const response = await fetch(`api/leis.php?acao=estatisticas&codigo=${this.leiAtual}`);
            const data = await response.json();

            if (data.sucesso) {
                console.log(`📊 Estatísticas recebidas para ${this.leiAtual}:`, data.estatisticas);
                this.estatisticas[this.leiAtual] = data.estatisticas;
                this.atualizarInterface();
            } else {
                console.error(`📊 Erro na API de estatísticas:`, data);
            }
        } catch (error) {
            console.error('📊 Erro ao carregar estatísticas:', error);
        }
    }
    
    /**
     * Atualiza interface com estatísticas
     */
    atualizarInterface() {
        const stats = this.estatisticas[this.leiAtual];
        if (!stats) return;
        
        this.atualizarContadores(stats);
        this.atualizarBarrasProgresso(stats);
        this.atualizarBadges(stats);
        this.atualizarDashboard(stats);
    }
    
    /**
     * Atualiza contadores principais
     */
    atualizarContadores(stats) {
        // Verificar se stats tem a estrutura esperada
        if (!stats || !stats.lei || !stats.progresso || !stats.revisao) {
            console.warn('Estrutura de estatísticas inválida:', stats);
            return;
        }

        // Contadores de artigos
        this.atualizarElemento('totalArtigos', stats.lei.total_artigos || 0);
        this.atualizarElemento('artigosLidos', stats.progresso.lidos || 0);
        this.atualizarElemento('percentualProgresso', `${stats.progresso.percentual || 0}%`);

        // Contadores de revisão
        this.atualizarElemento('totalRevisoes', stats.revisao.total || 0);
        this.atualizarElemento('revisoesPendentes', stats.revisao.pendentes || 0);
        this.atualizarElemento('artigosAprendendo', stats.revisao.aprendendo || 0);
        this.atualizarElemento('artigosRevisando', stats.revisao.revisando || 0);
        this.atualizarElemento('artigosDominados', stats.revisao.dominado || 0);
        this.atualizarElemento('artigosDificeis', stats.revisao.dificil || 0);

        // Métricas avançadas
        this.atualizarElemento('facilidadeMedia', stats.revisao.facilidade_media || 0);
        this.atualizarElemento('totalFavoritos', stats.favoritos || 0);
        this.atualizarElemento('totalListas', stats.listas || 0);
        this.atualizarElemento('totalAnotacoes', stats.anotacoes || 0);
    }
    
    /**
     * Atualiza barras de progresso
     */
    atualizarBarrasProgresso(stats) {
        // Verificar estrutura
        if (!stats || !stats.lei || !stats.progresso || !stats.revisao) {
            return;
        }

        // Barra de progresso principal
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');

        if (progressFill) {
            const percentual = stats.progresso.percentual || 0;
            progressFill.style.width = `${percentual}%`;
            // Remover o background inline para usar o CSS (var(--primary))
            progressFill.style.background = '';
        }

        if (progressText) {
            const lidos = stats.progresso.lidos || 0;
            const total = stats.lei.total_artigos || 0;
            const percentual = stats.progresso.percentual || 0;
            progressText.textContent = `${lidos} de ${total} artigos lidos (${percentual}%)`;
        }

        // Barras de status de revisão
        const totalRevisoes = stats.revisao.total || 0;
        this.atualizarBarraStatus('barraAprendendo', stats.revisao.aprendendo || 0, totalRevisoes, '#3498db');
        this.atualizarBarraStatus('barraRevisando', stats.revisao.revisando || 0, totalRevisoes, '#f39c12');
        this.atualizarBarraStatus('barraDominados', stats.revisao.dominado || 0, totalRevisoes, '#27ae60');
        this.atualizarBarraStatus('barraDificeis', stats.revisao.dificil || 0, totalRevisoes, '#e74c3c');
    }
    
    /**
     * Atualiza barra de status específica
     */
    atualizarBarraStatus(elementId, valor, total, cor) {
        const elemento = document.getElementById(elementId);
        if (!elemento) return;
        
        const percentual = total > 0 ? (valor / total) * 100 : 0;
        elemento.style.width = `${percentual}%`;
        elemento.style.backgroundColor = cor;
        
        // Atualizar tooltip se existir
        elemento.title = `${valor} de ${total} artigos (${percentual.toFixed(1)}%)`;
    }
    
    /**
     * Atualiza badges de navegação
     */
    atualizarBadges(stats) {
        // Verificar estrutura
        if (!stats || !stats.revisao) {
            return;
        }

        // Badge de revisões pendentes
        const revisaoCount = document.getElementById('revisaoCount');
        if (revisaoCount) {
            const pendentes = stats.revisao.pendentes || 0;
            revisaoCount.textContent = pendentes;
            revisaoCount.style.display = pendentes > 0 ? 'inline' : 'none';
        }

        // Badge de favoritos
        const favoritosCount = document.getElementById('favoritosCount');
        if (favoritosCount) {
            const favoritos = stats.favoritos || 0;
            favoritosCount.textContent = favoritos;
            favoritosCount.style.display = favoritos > 0 ? 'inline' : 'none';
        }

        // Badge de listas
        const listasCount = document.getElementById('listasCount');
        if (listasCount) {
            const listas = stats.listas || 0;
            listasCount.textContent = listas;
            listasCount.style.display = listas > 0 ? 'inline' : 'none';
        }

        // Mostrar/ocultar botão de revisão
        this.atualizarVisibilidadeBotaoRevisao(stats.revisao.total || 0);
    }
    
    /**
     * Atualiza dashboard de estatísticas
     */
    atualizarDashboard(stats) {
        // Criar ou atualizar cards de estatísticas
        this.criarCardEstatistica('cardProgresso', {
            titulo: 'Progresso de Leitura',
            valor: `${stats.progresso.percentual}%`,
            descricao: `${stats.progresso.lidos} de ${stats.lei.total_artigos} artigos`,
            cor: stats.lei.cor_tema,
            icone: 'fas fa-chart-line'
        });
        
        this.criarCardEstatistica('cardRevisao', {
            titulo: 'Sistema de Revisão',
            valor: stats.revisao.total,
            descricao: `${stats.revisao.pendentes} pendentes`,
            cor: '#3498db',
            icone: 'fas fa-brain'
        });
        
        this.criarCardEstatistica('cardFavoritos', {
            titulo: 'Favoritos',
            valor: stats.favoritos,
            descricao: 'Artigos marcados',
            cor: '#e74c3c',
            icone: 'fas fa-heart'
        });
        
        this.criarCardEstatistica('cardListas', {
            titulo: 'Listas Criadas',
            valor: stats.listas,
            descricao: 'Organizações personalizadas',
            cor: '#9b59b6',
            icone: 'fas fa-bookmark'
        });
    }
    
    /**
     * Cria ou atualiza card de estatística
     */
    criarCardEstatistica(elementId, config) {
        let card = document.getElementById(elementId);
        
        if (!card) {
            // Criar card se não existir
            const container = document.getElementById('estatisticasContainer');
            if (!container) return;
            
            card = document.createElement('div');
            card.id = elementId;
            card.className = 'estatistica-card';
            container.appendChild(card);
        }
        
        card.innerHTML = `
            <div class="estatistica-icone" style="background: ${config.cor}">
                <i class="${config.icone}"></i>
            </div>
            <div class="estatistica-info">
                <h3>${config.titulo}</h3>
                <div class="estatistica-valor">${config.valor}</div>
                <div class="estatistica-descricao">${config.descricao}</div>
            </div>
        `;
        
        // Animar atualização
        card.style.transform = 'scale(1.05)';
        setTimeout(() => {
            card.style.transform = 'scale(1)';
        }, 200);
    }
    
    /**
     * Atualiza elemento se existir
     */
    atualizarElemento(id, valor) {
        const elemento = document.getElementById(id);
        if (elemento) {
            elemento.textContent = valor;
        }
    }
    
    /**
     * Inicia atualização automática
     */
    iniciarAtualizacaoAutomatica() {
        // Atualizar a cada 30 segundos
        this.intervalos.atualizacao = setInterval(() => {
            this.atualizarEstatisticas();
        }, 30000);
        
        // Atualizar quando página fica visível
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.atualizarEstatisticas();
            }
        });
    }
    
    /**
     * Para atualização automática
     */
    pararAtualizacaoAutomatica() {
        if (this.intervalos.atualizacao) {
            clearInterval(this.intervalos.atualizacao);
            delete this.intervalos.atualizacao;
        }
    }
    
    /**
     * Obtém estatísticas de uma lei específica
     */
    async obterEstatisticasLei(codigoLei) {
        try {
            const response = await fetch(`api/leis.php?acao=estatisticas&codigo=${codigoLei}`);
            const data = await response.json();
            
            if (data.sucesso) {
                this.estatisticas[codigoLei] = data.estatisticas;
                return data.estatisticas;
            }
        } catch (error) {
            console.error(`Erro ao carregar estatísticas da lei ${codigoLei}:`, error);
        }
        
        return null;
    }
    
    /**
     * Obtém estatísticas da lei atual
     */
    getEstatisticasAtual() {
        return this.estatisticas[this.leiAtual] || null;
    }
    
    /**
     * Obtém estatísticas de todas as leis carregadas
     */
    getTodasEstatisticas() {
        return this.estatisticas;
    }
    
    /**
     * Limpa cache de estatísticas
     */
    limparCache() {
        this.estatisticas = {};
    }
    
    /**
     * Força atualização imediata
     */
    forcarAtualizacao() {
        this.atualizarEstatisticas();
    }

    /**
     * Atualiza visibilidade do botão de revisão
     */
    atualizarVisibilidadeBotaoRevisao(totalRevisoes) {
        const btnRevisao = document.getElementById('btnRevisao');
        if (btnRevisao) {
            // Sempre manter o botão visível e habilitado
            btnRevisao.style.display = 'flex';
            btnRevisao.style.opacity = '1';
            btnRevisao.style.cursor = 'pointer';
            btnRevisao.classList.remove('disabled');
            btnRevisao.title = 'Sistema de Revisão';

            console.log(`📊 Botão de revisão sempre visível (total: ${totalRevisoes})`);
        }
    }
}

// Instância global
let estatisticasMultiLeis;

// Inicializar quando DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    estatisticasMultiLeis = new EstatisticasMultiLeis();

    // Disponibilizar globalmente
    window.estatisticasMultiLeis = estatisticasMultiLeis;

    // Tentar detectar lei após um pequeno delay para dar tempo do sistema carregar
    setTimeout(() => {
        if (!estatisticasMultiLeis.leiAtual) {
            console.log('📊 Tentando detectar lei novamente após delay...');
            estatisticasMultiLeis.detectarLeiAtual();
        }
    }, 1000);
});

// Exportar para uso em outros módulos
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EstatisticasMultiLeis;
}
