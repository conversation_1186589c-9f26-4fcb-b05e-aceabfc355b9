<?php
//adicionar_materia.php
session_start();

// Ativar relatório de erros para diagnóstico durante o desenvolvimento
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Definir cabeçalho JSON
header('Content-Type: application/json');

// Log para rastrear execução
error_log("Iniciando adicionar_materia.php");

try {
    // Verificar autenticação
    if (!isset($_SESSION['idusuario'])) {
        throw new Exception('Usuário não autenticado');
    }

    error_log("Usuário autenticado: " . $_SESSION['idusuario']);

    // Verificar parâmetro materia
    if (!isset($_GET['materia']) || empty($_GET['materia'])) {
        throw new Exception('Matéria não especificada');
    }

    $materia_nome = urldecode($_GET['materia']);
    error_log("Matéria a ser adicionada: " . $materia_nome);

    // Incluir arquivos necessários
    $includeFiles = [
        '../conexao_POST.php',
        'includes/calculos.php',
        'includes/plano_estudo_logic.php'
    ];

    foreach ($includeFiles as $file) {
        if (!file_exists($file)) {
            throw new Exception("Arquivo não encontrado: " . $file);
        }
        require_once $file;
    }

    // Verificar se a conexão foi estabelecida
    if (!isset($conexao) || !$conexao) {
        throw new Exception('Erro na conexão com o banco de dados');
    }
    
    error_log("Conexão com o banco estabelecida");

    // Criar objeto PlanoEstudoLogic e adicionar matéria
    $planoData = new PlanoEstudoLogic($conexao);
    $sucesso = $planoData->adicionarMateriaPlanejamento($materia_nome);

    error_log("Resultado da adição: " . ($sucesso ? "sucesso" : "falha"));
    
    // Retornar resultado
    echo json_encode(['sucesso' => $sucesso]);

} catch (Exception $e) {
    error_log("Erro ao adicionar matéria: " . $e->getMessage());
    error_log("Pilha de exceção: " . $e->getTraceAsString());
    
    http_response_code(500);
    echo json_encode([
        'sucesso' => false, 
        'erro' => $e->getMessage(),
        'detalhes' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 3)
    ]);
}