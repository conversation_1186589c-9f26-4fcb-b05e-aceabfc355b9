<?php
include 'conexao_POST.php';
include 'processa_index.php';
include_once 'funcoes.php'; // Inclua as funções aqui

// Verifica se $id_planejamento e $id_usuario estão definidos
if (!isset($id_planejamento) || !isset($id_usuario)) {
    die("ID do planejamento ou ID do usuário não definido.");
}

// Consultar os IDs e nomes das matérias associadas ao planejamento
$query_consultar_materias_planejamento = "
    SELECT pm.materia_idmateria, m.nome AS nome_materia, m.cor AS cor_materia
    FROM appEstudo.planejamento_materia pm
    INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    WHERE pm.planejamento_idplanejamento = $id_planejamento";

$resultado_materias_planejamento = pg_query($conexao, $query_consultar_materias_planejamento);

// Inicializa arrays para armazenar os dados das matérias
$materias_no_planejamento = array();
$cores_materias = array();

// Preenche os arrays com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_materias_planejamento)) {
    $materia_id = $row['materia_idmateria'];
    $nome_materia = $row['nome_materia'];
    $cor_materia = $row['cor_materia'];

    $materias_no_planejamento[$materia_id] = $nome_materia;
    $cores_materias[$nome_materia] = $cor_materia;
}

// Consultar dados para o gráfico de cursos
$query_consulta_curso = "
    SELECT m.nome AS nome_materia, 
           e.ponto_estudado AS ponto_estudado,
           e.tempo_liquido AS tempo_estudo,
           to_char(e.data, 'DD-MM-YYYY') AS data_estudo,
           c.nome AS nome_curso
    FROM appEstudo.estudos e
    INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
    INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
    LEFT JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria
    LEFT JOIN appEstudo.curso c ON e.idcurso = c.idcurso
    WHERE u.idusuario = $id_usuario AND e.materia_idmateria IN (" . implode(',', array_keys($materias_no_planejamento)) . ")
    ORDER BY m.nome, e.data";

$resultado_consulta_metodos = pg_query($conexao, $query_consulta_curso);

// Inicializa um array para armazenar os dados de tempo de estudo por método para cada matéria
$metodo_estudo_por_materia = array();
$tempo_total_por_materia = array();

// Preenche o array com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_consulta_metodos)) {
    $materia = $row['nome_materia'];
    $nome_curso = $row['nome_curso'];
    $tempo_estudo = $row['tempo_estudo']; // Tempo no formato "HH:MM:SS"

    // Converte o tempo de estudo para segundos
    $tempo_estudo_segundos = converterParaSegundos($tempo_estudo);

    // Adiciona os tempos de estudo por curso para a matéria correspondente
    if (!isset($metodo_estudo_por_materia[$materia][$nome_curso])) {
        $metodo_estudo_por_materia[$materia][$nome_curso] = 0;
    }
    // Adiciona o tempo de estudo ao tempo total já existente para o curso
    $metodo_estudo_por_materia[$materia][$nome_curso] += $tempo_estudo_segundos;

    // Calcula o tempo total por matéria
    if (!isset($tempo_total_por_materia[$materia])) {
        $tempo_total_por_materia[$materia] = 0;
    }
    $tempo_total_por_materia[$materia] += $tempo_estudo_segundos;
}

// Convertendo os arrays para JSON para serem utilizados no JavaScript
$dados_curso_json = json_encode($metodo_estudo_por_materia);
$cores_json = json_encode($cores_materias);
$tempo_total_json = json_encode($tempo_total_por_materia);
?>

<div id="cursoChart" class="chart-container"></div>

<script>
    // Recuperando os dados do PHP
    var dadosCurso = <?php echo $dados_curso_json; ?>;
    var cores = <?php echo $cores_json; ?>;
    var tempoTotalPorMateria = <?php echo $tempo_total_json; ?>;

    //console.log("Dados do curso recebidos:", dadosCurso); // Debug: Imprime os dados do curso no console
    //console.log("Cores recebidas:", cores); // Debug: Imprime as cores no console
    //console.log("Tempo total por matéria:", tempoTotalPorMateria); // Debug: Imprime o tempo total no console

    // Transformando os dados no formato esperado pelo Highcharts para o gráfico de cursos
    var seriesDataCurso = [];
    var drilldownSeriesCurso = [];

    for (var materia in tempoTotalPorMateria) {
        var totalTempoEstudo = tempoTotalPorMateria[materia];
        var horas = Math.floor(totalTempoEstudo / 3600);
        var minutos = Math.floor((totalTempoEstudo % 3600) / 60);
        var formattedTime = horas + 'h ' + minutos + 'm';
        seriesDataCurso.push({
            name: materia,
            y: totalTempoEstudo,
            formattedTime: formattedTime,
            drilldown: materia,
            color: cores[materia] // Atribui a cor específica da matéria
        });

        var dataCurso = [];
        if (dadosCurso[materia]) {
            for (var nomeCurso in dadosCurso[materia]) {
                var tempoEstudoCurso = parseInt(dadosCurso[materia][nomeCurso]);
                var horasCurso = Math.floor(tempoEstudoCurso / 3600);
                var minutosCurso = Math.floor((tempoEstudoCurso % 3600) / 60);
                var formattedTimeCurso = horasCurso + 'h ' + minutosCurso + 'm';
                dataCurso.push({ name: nomeCurso, y: tempoEstudoCurso, formattedTime: formattedTimeCurso });
            }
        }

        drilldownSeriesCurso.push({
            name: materia,
            id: materia,
            data: dataCurso.map(function(item) {
                return { name: item.name, y: item.y, formattedTime: item.formattedTime };
            })
        });
    }

    // Configuração do gráfico de cursos
    Highcharts.chart('cursoChart', {
        chart: {
            type: 'pie',
            backgroundColor: 'transparent' // Fundo transparente
        },
        title: {
            text: null
        },
        credits: {
            enabled: false // Desativar créditos
        },
        plotOptions: {
            series: {
                dataLabels: {
                    enabled: true,
                    format: '{point.name}: {point.percentage:.2f}% ({point.formattedTime})',
                    style: {
                        textOutline: 'none' // Remover sombra branca ao redor do texto
                    }
                }
            }
        },
        tooltip: {
            pointFormat: '<span style="color:{point.color}">\u25CF</span> {series.name}: <b>{point.percentage:.2f}%</b> ({point.formattedTime})<br/>',
            style: {
                textOutline: 'none' // Remover sombra branca ao redor do texto da tooltip
            }
        },
        series: [{
            name: 'Matéria',
            colorByPoint: true,
            data: seriesDataCurso
        }],
        drilldown: {
            series: drilldownSeriesCurso
        }
    });

</script>
