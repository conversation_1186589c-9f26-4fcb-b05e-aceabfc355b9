<?php
session_start();
require_once 'conexao_POST.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit;
}

$id_usuario = $_SESSION['idusuario'];

// Consultar eventos futuros - query simplificada
$query_eventos_futuros = "SELECT * FROM appEstudo.agenda 
                         WHERE usuario_idusuario = $1 
                         AND data_inicio > CURRENT_TIMESTAMP 
                         ORDER BY data_inicio ASC";

try {
    $resultado_eventos_futuros = pg_prepare($conexao, "buscar_eventos_futuros", $query_eventos_futuros);
    $resultado_eventos_futuros = pg_execute($conexao, "buscar_eventos_futuros", array($id_usuario));

    $eventos = array();

    if ($resultado_eventos_futuros) {
        while ($evento = pg_fetch_assoc($resultado_eventos_futuros)) {
            // Converter a data para o formato ISO
            $data_inicio = new DateTime($evento['data_inicio']);
            
            $eventos[] = array(
                'id' => (int)$evento['id'],
                'titulo' => $evento['titulo'],
                'data_inicio' => $data_inicio->format('Y-m-d\TH:i:s'),
                'tipo_evento' => $evento['tipo_evento'],
                'realizado' => $evento['realizado'] === 't'
            );
        }
    }

    // Limpar qualquer saída anterior
    if (ob_get_length()) ob_clean();

    // Definir os headers corretos
    header('Content-Type: application/json; charset=utf-8');
    header('Cache-Control: no-cache, must-revalidate');

    // Enviar a resposta JSON
    echo json_encode($eventos, JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    error_log("Erro ao buscar eventos futuros: " . $e->getMessage());
    http_response_code(500);
    echo json_encode(['erro' => 'Erro ao buscar eventos: ' . $e->getMessage()]);
}
exit;
?> 