<?php
session_start();
include_once("assets/config.php");
include_once("helpers.php");

// Verifica se o usuário está logado
if (!isset($_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit();
}

// Busca o nome do usuário
$query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = " . $_SESSION['idusuario'];
$resultado_nome = pg_query($conexao, $query_buscar_nome);
$nome_usuario = ($resultado_nome && pg_num_rows($resultado_nome) > 0) 
    ? pg_fetch_assoc($resultado_nome)['nome'] 
    : "Usuário";
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Em Breve - Flashcards</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700&family=Quicksand:wght@400;500;600;700&family=Varela+Round&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <style>
        :root {
            --primary-color: #000080;
            --secondary-color: #4169E1;
            --background-color: #E8ECF3;
            --text-color: #2C3345;
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Nunito', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background-color: var(--primary-color);
            padding: 1rem;
            color: white;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo img {
            height: 40px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .coming-soon-container {
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
            margin: 2rem;
        }

        .icon-container {
            font-size: 4rem;
            color: var(--primary-color);
            margin-bottom: 1.5rem;
        }

        h1 {
            color: var(--primary-color);
            font-size: 2.5rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }

        .description {
            font-size: 1.2rem;
            line-height: 1.6;
            color: #666;
            margin-bottom: 2rem;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .feature {
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            text-align: center;
        }

        .feature i {
            font-size: 1.5rem;
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.5rem;
            background-color: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 128, 0.2);
        }

        .footer {
            text-align: center;
            padding: 1rem;
            background: white;
            color: #666;
            margin-top: auto;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">
            <img src="../logo/logo_vertical.png" alt="Logo">
        </div>
        <div class="user-info">
            <i class="fas fa-user-circle"></i>
            <span><?php echo htmlspecialchars($nome_usuario); ?></span>
        </div>
    </div>

    <div class="main-content">
        <div class="coming-soon-container">
            <div class="icon-container">
                <i class="fas fa-layer-group"></i>
            </div>
            <h1>Em Breve: Flashcards</h1>
            <p class="description">
                Estamos preparando uma experiência incrível de estudo para você! 
                Em breve, você terá acesso a uma poderosa ferramenta de flashcards 
                para otimizar seu aprendizado.
            </p>

            <div class="features">
                <div class="feature">
                    <i class="fas fa-brain"></i>
                    <h3>Memorização Ativa</h3>
                </div>
                <div class="feature">
                    <i class="fas fa-chart-line"></i>
                    <h3>Progresso Detalhado</h3>
                </div>
                <div class="feature">
                    <i class="fas fa-clock"></i>
                    <h3>Revisão Espaçada</h3>
                </div>
            </div>

            <a href="../index.php" class="btn">
                <i class="fas fa-home"></i>
                Voltar para Início
            </a>
        </div>
    </div>

    <div class="footer">
        <p>© <?php echo date('Y'); ?> Sistema de Flashcards | www.planejaaqui.com.br</p>
    </div>
</body>
</html>