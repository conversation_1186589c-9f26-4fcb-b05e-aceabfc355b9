<?php
// atualizar_evento.php

// Incluir o arquivo de conexão com o banco de dados
include 'conexao_POST.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $eventoId = $_POST['eventoId'];
    $novaDataInicio = $_POST['novaDataInicio'];
    $novaDataFim = $_POST['novaDataFim'];

    // Converter a data para o formato do banco de dados (se necessário)
    $novaDataInicio = date('Y-m-d H:i:s', strtotime($novaDataInicio));
    $novaDataFim = $novaDataFim ? date('Y-m-d H:i:s', strtotime($novaDataFim)) : null;

    // Atualizar o evento no banco de dados
    $query = "UPDATE appEstudo.agenda 
              SET data_inicio = '$novaDataInicio', 
                  data_fim = '$novaDataFim' 
              WHERE id = $eventoId";

    $result = pg_query($conexao, $query);

    if ($result) {
        echo json_encode(['success' => true, 'message' => 'Evento atualizado com sucesso!']);
    } else {
        echo json_encode(['success' => false, 'message' => 'Erro ao atualizar evento.']);
    }
}
?>