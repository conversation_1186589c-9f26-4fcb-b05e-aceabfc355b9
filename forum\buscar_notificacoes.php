<?php
include_once("../session_config.php");
include_once("../assets/config.php");

if (!verificarSessaoValida()) {
    http_response_code(401);
    echo json_encode(['error' => 'Não autenticado']);
    exit();
}

$idusuario = $_SESSION['idusuario'];

$query = "
    SELECT id, mensagem, lida, data_criada, topico_id
    FROM appestudo.forum_notificacoes
    WHERE usuario_id = $1
    ORDER BY data_criada DESC
    LIMIT 10
";
$result = pg_query_params($conexao, $query, [$idusuario]);

$notificacoes = [];
while ($row = pg_fetch_assoc($result)) {
    $notificacoes[] = [
        'id' => $row['id'],
        'mensagem' => $row['mensagem'],
        'lida' => $row['lida'] === 't',
        'data_criada' => $row['data_criada'],
        'topico_id' => $row['topico_id']
    ];
}

header('Content-Type: application/json');
echo json_encode(['notificacoes' => $notificacoes]); 