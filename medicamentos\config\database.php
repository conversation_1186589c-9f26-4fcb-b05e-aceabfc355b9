<?php
// config/database.php

class Database {
    private $host = "app_estudo.postgresql.dbaas.com.br";
    private $db_name = "app_estudo";
    private $username = "app_estudo";
    private $password = "Lucasb90#";
    private $port = "5432";
    public $conn;


    // Método para obter a conexão com o banco de dados
    public function getConnection() {
        $this->conn = null;
        
        try {
            $dsn = "pgsql:host=" . $this->host . ";port=" . $this->port . ";dbname=" . $this->db_name;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            echo "Erro na conexão: " . $e->getMessage();
        }
        
        return $this->conn;
    }
}
?>