<?php
// ver_cards.php
session_start();
include_once("assets/config.php");
include_once("helpers.php");

// Configurar encoding
header('Content-Type: text/html; charset=utf-8');

// Configurar encoding para a conexão PostgreSQL
pg_set_client_encoding($conexao, "UTF8");

if (!isset($_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit();
}

if (!isset($_GET['topico'])) {
    header("Location: flashcards.php");
    exit();
}

$topico_id = (int)$_GET['topico'];
$mensagem = '';
$is_admin = checkAdmin($conexao, $_SESSION['idusuario']);

// Função para limpar e converter encoding
function fixEncoding($string) {
    if ($string === null) {
        return '';
    }
    
    // Remove caracteres especiais HTML
    $string = html_entity_decode($string, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    // Remove &nbsp; e outros espaços especiais
    $string = str_replace('&nbsp;', ' ', $string);
    
    // Converte para UTF-8 se necessário
    if (!mb_check_encoding($string, 'UTF-8')) {
        $string = mb_convert_encoding($string, 'UTF-8', 'ISO-8859-1');
    }
    
    // Remove caracteres inválidos UTF-8
    $string = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x9F]/u', '', $string);
    
    // Substitui caracteres quebrados por espaço
    $string = preg_replace('/[^\p{L}\p{N}\p{Z}\p{P}\p{S}]/u', ' ', $string);
    
    return $string;
}

// Consulta para buscar o nome do usuário com base no ID
$query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = " . $_SESSION['idusuario'];
$resultado_nome = pg_query($conexao, $query_buscar_nome);

if ($resultado_nome && pg_num_rows($resultado_nome) > 0) {
    $row = pg_fetch_assoc($resultado_nome);
    $nome_usuario = $row['nome'];
} else {
    $nome_usuario = "Usuário";
}

// Buscar informações do tópico e sua hierarquia
$query_info = "
    SELECT 
        t.nome as topic_name,
        t.descricao as topic_description,
        d.nome as deck_name,
        d.id as deck_id,
        c.nome as categoria_name,
        c.id as categoria_id,
        m.nome as materia_nome
    FROM appestudo.flashcard_topics t
    JOIN appestudo.flashcard_decks d ON d.id = t.deck_id
    JOIN appestudo.flashcard_categories c ON c.id = d.category_id
    JOIN appestudo.materia m ON m.idmateria = d.materia_id
    WHERE t.id = $1";
$result_info = pg_query_params($conexao, $query_info, array($topico_id));
$info = pg_fetch_assoc($result_info);

if (!$info) {
    header("Location: flashcards.php");
    exit();
}

// Buscar cards associados ao tópico
$query_cards = "
    SELECT 
        f.id,
        f.pergunta,
        f.resposta,
        f.resumo,
        f.previsao_legal,
        f.jurisprudencia,
        EXISTS(SELECT 1 FROM appestudo.flashcard_mindmaps fm WHERE fm.flashcard_id = f.id) as has_mindmap,
        (
            SELECT COUNT(DISTINCT fp.id)
            FROM appestudo.flashcard_progress fp
            WHERE fp.flashcard_id = f.id
        ) as total_estudos,
        (
            SELECT AVG(fp.nivel_conhecimento)::numeric(10,1)
            FROM appestudo.flashcard_progress fp
            WHERE fp.flashcard_id = f.id
        ) as media_conhecimento
    FROM appestudo.flashcards f
    JOIN appestudo.flashcard_topic_association ft ON ft.flashcard_id = f.id
    WHERE ft.topic_id = $1
    ORDER BY f.id DESC";
$result_cards = pg_query_params($conexao, $query_cards, array($topico_id));

// Preparar dados para o Vue em formato JSON
$cards = [];
$contador = 1;
while ($card = pg_fetch_assoc($result_cards)) {
    $pergunta = fixEncoding($card['pergunta']);
    $card['pergunta_limpa'] = mb_substr(strip_tags($pergunta), 0, 150, 'UTF-8') . '...';
    $card['contador'] = $contador++;
    // Certifique-se de que todos os campos estão com encoding correto
    $card['resposta'] = fixEncoding($card['resposta']);
    $card['resumo'] = fixEncoding($card['resumo']);
    $card['previsao_legal'] = fixEncoding($card['previsao_legal']);
    $card['jurisprudencia'] = fixEncoding($card['jurisprudencia']);
    // Formatar valores numéricos corretamente
    $card['media_conhecimento'] = $card['media_conhecimento'] ? 
        number_format((float)$card['media_conhecimento'], 1, '.', '') : null;
    $cards[] = $card;
}

// Para garantir que os dados JSON sejam válidos, sanitize todos os campos
function sanitizeForJson($data) {
    if (is_array($data)) {
        foreach ($data as $key => $value) {
            $data[$key] = sanitizeForJson($value);
        }
        return $data;
    } elseif (is_string($data)) {
        return fixEncoding($data);
    } else {
        return $data;
    }
}

// Sanitize os dados antes de codificar para JSON
$info = sanitizeForJson($info);
$cards = sanitizeForJson($cards);

// Usar constantes JSON para garantir encoding correto
$infoJson = json_encode($info, JSON_HEX_QUOT | JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_UNESCAPED_UNICODE);
$cardsJson = json_encode($cards, JSON_HEX_QUOT | JSON_HEX_TAG | JSON_HEX_AMP | JSON_HEX_APOS | JSON_UNESCAPED_UNICODE);
$isAdminJson = json_encode($is_admin);
$topicoIdJson = json_encode($topico_id);

// Verificar se há erro de JSON
if (json_last_error() !== JSON_ERROR_NONE) {
    error_log('JSON error: ' . json_last_error_msg());
    // Fornecer valores padrão em caso de erro
    $infoJson = json_encode([
        'topic_name' => 'Tópico',
        'topic_description' => '',
        'deck_name' => 'Baralho',
        'deck_id' => $info['deck_id'] ?? 0,
        'categoria_name' => 'Categoria',
        'categoria_id' => $info['categoria_id'] ?? 0,
        'materia_nome' => 'Matéria'
    ]);
    $cardsJson = '[]';
}

// Verificações finais de validade JSON
if ($infoJson === false) $infoJson = '{}';
if ($cardsJson === false) $cardsJson = '[]';
if ($isAdminJson === false) $isAdminJson = 'false';
if ($topicoIdJson === false) $topicoIdJson = '0';
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cards - <?php echo htmlspecialchars($info['topic_name'] ?? 'Tópico'); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700&family=Quicksand:wght@400;500;600;700&family=Varela+Round&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <!-- Vue.js CDN -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <link rel="stylesheet" href="assets/css/ver_cards.css">
</head>
<body>
    <div id="app">
        <div class="header">
            <div class="header-left">
                <div class="logo">
                    <img src="../logo/logo_vertical_azul.png" alt="Logo" class="logo-light">
                    <img src="../logo/logo_vertical.png" alt="Logo" class="logo-dark">
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span class="user-name"><?php echo htmlspecialchars($nome_usuario); ?></span>
                </div>

                <div class="theme-toggle">
                    <button id="theme-toggle-btn" class="theme-btn">
                        <i class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>

        <a :href="'ver_topicos.php?baralho=' + info.deck_id" class="btn-back">
            <i class="fas fa-arrow-left"></i>
        </a>

        <div class="container">
            <div class="clean-container">
                <div class="clean-header">
                    <h1>Cards de {{ info.topic_name }}</h1>
                </div>

                <div class="breadcrumb">
                    <i class="fas fa-home"></i>
                    <span>{{ info.categoria_name }}</span>
                    <i class="fas fa-chevron-right"></i>
                    <span>{{ info.deck_name }}</span>
                    <i class="fas fa-chevron-right"></i>
                    <span>{{ info.topic_name }}</span>
                </div>

                <div class="header-actions">
                    <div class="header-info">
                        <i class="fas fa-info-circle"></i>
                        Flashcards disponíveis para estudo
                    </div>
                    <a v-if="isAdmin" :href="'criar_card.php?topico=' + topicoId" class="btn">
                        <i class="fas fa-plus"></i> Novo Card
                    </a>
                </div>

                <div v-if="loading" class="loader"></div>
                <table v-else-if="cards.length > 0" class="clean-table">
                    <thead>
                        <tr>
                            <th class="number-column">#</th>
                            <th>Pergunta</th>
                            <th>Estatísticas</th>
                            <th>Ações</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="card in cards" :key="card.id">
                            <td class="number-column">{{ card.contador }}</td>
                            <td>{{ card.pergunta_limpa }}</td>
                            <td>
                                <span class="stats-badge">
                                    <i class="fas fa-users"></i>
                                    {{ card.total_estudos }} estudos
                                </span>
                                <span v-if="card.media_conhecimento" class="stats-badge">
                                    <i class="fas fa-chart-line"></i>
                                    {{ card.media_conhecimento }}/5
                                </span>
                                <span v-if="card.has_mindmap === 't'" class="stats-badge">
                                    <i class="fas fa-project-diagram"></i>
                                    Mapa Mental
                                </span>
                            </td>
                            <td>
                                <div class="table-actions">
                                    <a :href="'visualizar_card.php?id=' + card.id" class="btn btn-secondary">
                                        <i class="fas fa-eye"></i> Ver
                                    </a>
                                    <a v-if="isAdmin" :href="'editar_card.php?id=' + card.id" class="btn btn-success">
                                        <i class="fas fa-edit"></i> Editar
                                    </a>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <div v-else class="empty-state">
                    <i class="fas fa-book-open"></i>
                    <p>Nenhum card cadastrado ainda neste tópico.</p>
                    <a v-if="isAdmin" :href="'criar_card.php?topico=' + topicoId" class="btn">
                        <i class="fas fa-plus"></i> Criar Primeiro Card
                    </a>
                </div>
            </div>
        </div>
    </div>
    <script>
document.addEventListener('DOMContentLoaded', function() {
    try {
        // Inicialize os dados do Vue
        const infoData = <?php echo $infoJson; ?>;
        const cardsData = <?php echo $cardsJson; ?>;
        const isAdminData = <?php echo $isAdminJson; ?>;
        const topicoIdData = <?php echo $topicoIdJson; ?>;
        
        // 1. Aplica o tema salvo, se existir
        const savedTheme = localStorage.getItem('theme');
        if (savedTheme) {
            document.documentElement.setAttribute('data-theme', savedTheme);
            updateThemeIcon(savedTheme);
        }
        
        // 2. Função de atualização do ícone
        function updateThemeIcon(theme) {
            const icon = document.querySelector('#theme-toggle-btn i');
            if (icon) {
                if (theme === 'dark') {
                    icon.className = 'fas fa-sun';
                } else {
                    icon.className = 'fas fa-moon';
                }
            }
        }
        
        // 3. Função simples de alternância de tema
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            
            console.log(`Alternando tema: ${currentTheme} → ${newTheme}`);
            
            // Aplica o tema
            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateThemeIcon(newTheme);
        }
        
        // 4. Configura o botão de tema - com tempo suficiente para o DOM carregar
        setTimeout(function() {
            const themeBtn = document.getElementById('theme-toggle-btn');
            if (themeBtn) {
                console.log('Botão de tema encontrado e configurado');
                themeBtn.onclick = toggleTheme;
            } else {
                console.error('Botão de tema não encontrado!');
            }
        }, 100);
        
        // 5. Inicializa o Vue
        new Vue({
            el: '#app',
            data: {
                info: infoData,
                cards: cardsData,
                isAdmin: isAdminData,
                topicoId: topicoIdData,
                loading: false
            },
            mounted() {
                // Simulação de carregamento para demonstrar a animação
                this.loading = true;
                setTimeout(() => {
                    this.loading = false;
                }, 500);
            },
            methods: {
                formatarTexto(texto) {
                    return texto ? texto.substr(0, 150) + '...' : '';
                }
            }
        });
    } catch (e) {
        console.error('Erro ao inicializar aplicação:', e);
        document.getElementById('app').innerHTML = '<div class="empty-state"><i class="fas fa-exclamation-triangle"></i><p>Erro ao carregar a aplicação. Por favor, tente novamente.</p></div>';
    }
});
</script>
</body>
</html>