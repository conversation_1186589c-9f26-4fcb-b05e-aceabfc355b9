<?php
// backup_estrutura_php.php - Backup da estrutura usando PHP puro
session_start();
require_once('../includes/auth.php');
require_once('../conexao_POST.php');
require_once('../cadastros/includes/verify_admin.php');

// Verifica se é o super admin
verificarSuperAdmin($conexao, false);

function extrairEstruturaTabelas($conexao) {
    try {
        $estrutura = "-- Backup da Estrutura do Banco de Dados\n";
        $estrutura .= "-- Gerado em: " . date('Y-m-d H:i:s') . "\n\n";
        
        // Buscar todas as tabelas do schema appestudo
        $query_tabelas = "
            SELECT 
                table_name,
                table_type
            FROM information_schema.tables 
            WHERE table_schema = 'appestudo'
            ORDER BY table_name
        ";
        
        $result_tabelas = pg_query($conexao, $query_tabelas);
        if (!$result_tabelas) {
            throw new Exception("Erro ao buscar tabelas: " . pg_last_error($conexao));
        }
        
        $estrutura .= "-- =============================================\n";
        $estrutura .= "-- TABELAS ENCONTRADAS\n";
        $estrutura .= "-- =============================================\n\n";
        
        while ($tabela = pg_fetch_assoc($result_tabelas)) {
            $nome_tabela = $tabela['table_name'];
            $estrutura .= "-- Tabela: {$nome_tabela}\n";
            
            // Buscar estrutura da tabela
            $query_colunas = "
                SELECT 
                    column_name,
                    data_type,
                    character_maximum_length,
                    is_nullable,
                    column_default,
                    ordinal_position
                FROM information_schema.columns 
                WHERE table_schema = 'appestudo' 
                AND table_name = '$nome_tabela'
                ORDER BY ordinal_position
            ";
            
            $result_colunas = pg_query($conexao, $query_colunas);
            if ($result_colunas) {
                $estrutura .= "CREATE TABLE appestudo.{$nome_tabela} (\n";
                $colunas = [];
                
                while ($coluna = pg_fetch_assoc($result_colunas)) {
                    $def_coluna = "    " . $coluna['column_name'] . " " . $coluna['data_type'];
                    
                    if ($coluna['character_maximum_length']) {
                        $def_coluna .= "(" . $coluna['character_maximum_length'] . ")";
                    }
                    
                    if ($coluna['is_nullable'] === 'NO') {
                        $def_coluna .= " NOT NULL";
                    }
                    
                    if ($coluna['column_default']) {
                        $def_coluna .= " DEFAULT " . $coluna['column_default'];
                    }
                    
                    $colunas[] = $def_coluna;
                }
                
                $estrutura .= implode(",\n", $colunas) . "\n";
                $estrutura .= ");\n\n";
            }
            
            // Buscar chaves primárias
            $query_pk = "
                SELECT 
                    kcu.column_name
                FROM information_schema.table_constraints tc
                JOIN information_schema.key_column_usage kcu 
                    ON tc.constraint_name = kcu.constraint_name
                WHERE tc.table_schema = 'appestudo'
                AND tc.table_name = '$nome_tabela'
                AND tc.constraint_type = 'PRIMARY KEY'
                ORDER BY kcu.ordinal_position
            ";
            
            $result_pk = pg_query($conexao, $query_pk);
            if ($result_pk && pg_num_rows($result_pk) > 0) {
                $pk_colunas = [];
                while ($pk = pg_fetch_assoc($result_pk)) {
                    $pk_colunas[] = $pk['column_name'];
                }
                $estrutura .= "ALTER TABLE appestudo.{$nome_tabela} ADD PRIMARY KEY (" . implode(', ', $pk_colunas) . ");\n\n";
            }
            
            // Buscar índices
            $query_indices = "
                SELECT 
                    indexname,
                    indexdef
                FROM pg_indexes 
                WHERE schemaname = 'appestudo' 
                AND tablename = '$nome_tabela'
                AND indexname NOT LIKE '%_pkey'
            ";
            
            $result_indices = pg_query($conexao, $query_indices);
            if ($result_indices) {
                while ($indice = pg_fetch_assoc($result_indices)) {
                    $estrutura .= $indice['indexdef'] . ";\n";
                }
                $estrutura .= "\n";
            }
        }
        
        // Buscar foreign keys
        $estrutura .= "-- =============================================\n";
        $estrutura .= "-- FOREIGN KEYS\n";
        $estrutura .= "-- =============================================\n\n";
        
        $query_fks = "
            SELECT 
                tc.table_name,
                tc.constraint_name,
                kcu.column_name,
                ccu.table_name AS foreign_table_name,
                ccu.column_name AS foreign_column_name
            FROM information_schema.table_constraints tc
            JOIN information_schema.key_column_usage kcu 
                ON tc.constraint_name = kcu.constraint_name
            JOIN information_schema.constraint_column_usage ccu 
                ON ccu.constraint_name = tc.constraint_name
            WHERE tc.table_schema = 'appestudo'
            AND tc.constraint_type = 'FOREIGN KEY'
            ORDER BY tc.table_name, tc.constraint_name
        ";
        
        $result_fks = pg_query($conexao, $query_fks);
        if ($result_fks) {
            while ($fk = pg_fetch_assoc($result_fks)) {
                $estrutura .= "ALTER TABLE appestudo.{$fk['table_name']} ";
                $estrutura .= "ADD CONSTRAINT {$fk['constraint_name']} ";
                $estrutura .= "FOREIGN KEY ({$fk['column_name']}) ";
                $estrutura .= "REFERENCES appestudo.{$fk['foreign_table_name']}({$fk['foreign_column_name']});\n";
            }
        }
        
        return $estrutura;
        
    } catch (Exception $e) {
        throw new Exception("Erro ao extrair estrutura: " . $e->getMessage());
    }
}

function salvarEstruturaArquivo($estrutura) {
    try {
        $data = date('Y-m-d_H-i-s');
        $diretorio_backup = '../backups/';
        
        if (!file_exists($diretorio_backup)) {
            mkdir($diretorio_backup, 0755, true);
        }
        
        $arquivo_backup = $diretorio_backup . "estrutura_php_{$data}.sql";
        
        if (file_put_contents($arquivo_backup, $estrutura) === false) {
            throw new Exception("Erro ao salvar arquivo de backup");
        }
        
        // Compactar o backup
        $zip = new ZipArchive();
        $arquivo_zip = $diretorio_backup . "estrutura_php_{$data}.zip";
        
        if ($zip->open($arquivo_zip, ZipArchive::CREATE) === TRUE) {
            $zip->addFile($arquivo_backup, basename($arquivo_backup));
            $zip->close();
            
            // Remove o arquivo SQL original após compactação
            unlink($arquivo_backup);
            
            return [
                'sucesso' => true,
                'arquivo' => $arquivo_zip,
                'data' => $data,
                'tamanho' => filesize($arquivo_zip)
            ];
        }
        
        throw new Exception("Erro ao compactar arquivo de backup");
        
    } catch (Exception $e) {
        throw new Exception("Erro ao salvar estrutura: " . $e->getMessage());
    }
}

// Processar requisição
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['extrair_estrutura'])) {
    try {
        $estrutura = extrairEstruturaTabelas($conexao);
        $resultado = salvarEstruturaArquivo($estrutura);
        
        if ($resultado['sucesso']) {
            $_SESSION['mensagem'] = "Estrutura extraída com sucesso! Arquivo: " . basename($resultado['arquivo']);
            $_SESSION['tipo_mensagem'] = "success";
        }
        
    } catch (Exception $e) {
        $_SESSION['mensagem'] = "Erro ao extrair estrutura: " . $e->getMessage();
        $_SESSION['tipo_mensagem'] = "error";
    }
    
    header("Location: backup.php");
    exit;
}

// Se chegou aqui, redirecionar para a página principal
header("Location: backup.php");
exit;
?>
