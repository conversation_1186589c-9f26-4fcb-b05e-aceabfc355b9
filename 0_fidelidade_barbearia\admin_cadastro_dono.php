<?php
// =====================================================
// PÁGINA ADMINISTRATIVA - CADASTRO DO PRIMEIRO DONO
// Sistema de Fidelidade Barbearia
// =====================================================

require_once 'config.php';

// Verificar se a chave de admin foi fornecida
if (!isset($_GET['key']) || !verificarChaveAdmin($_GET['key'])) {
    die('❌ Acesso negado. Chave de administrador inválida.');
}

$mensagem = '';
$erro = '';

// Conectar ao banco
try {
    $pdo = conectarBanco();
} catch (Exception $e) {
    die("❌ " . $e->getMessage());
}

// Verificar se já existe um dono cadastrado
try {
    $donoExiste = jaExisteDono($pdo);
} catch (Exception $e) {
    $erro = $e->getMessage();
}

// Processar formulário
if ($_POST && !$donoExiste) {
    $nome = limparEntrada($_POST['nome'] ?? '');
    $cpf = preg_replace('/\D/', '', $_POST['cpf'] ?? ''); // Remove não-dígitos
    $senha = $_POST['senha'] ?? '';
    $confirmar_senha = $_POST['confirmar_senha'] ?? '';
    $senha_mestra = $_POST['senha_mestra'] ?? '';

    // Validação adicional de confirmação de senha
    if ($senha !== $confirmar_senha) {
        $erro = "Senha e confirmação não coincidem.";
    } else {
        try {
            // Para MySQL, usar procedure em vez de função
            $stmt = $pdo->prepare("CALL criar_primeiro_dono(?, ?, ?, ?)");
            $stmt->execute([$cpf, $nome, password_hash($senha, PASSWORD_DEFAULT), $senha_mestra]);
            $result = $stmt->fetch();
            $dono_id = $result['resultado'];

            $mensagem = "✅ Primeiro dono cadastrado com sucesso!<br><strong>ID:</strong> $dono_id";
            $donoExiste = true;
        } catch (Exception $e) {
            $erro = $e->getMessage();
        }
    }
}

// Buscar informações do dono se já existe
$infoDono = null;
if ($donoExiste) {
    try {
        $infoDono = buscarInfoDono($pdo);
    } catch (Exception $e) {
        $erro = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin - Cadastro Dono | Sistema Barbearia</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: #fff;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #FFD700;
            font-size: 2.2em;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #ccc;
            font-size: 1.1em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            color: #FFD700;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #444;
            border-radius: 8px;
            background: #2a2a2a;
            color: #fff;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #FFD700;
        }
        
        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #000;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        
        .alert-success {
            background: rgba(40, 167, 69, 0.2);
            border: 2px solid #28a745;
            color: #28a745;
        }
        
        .alert-error {
            background: rgba(220, 53, 69, 0.2);
            border: 2px solid #dc3545;
            color: #dc3545;
        }
        
        .info-card {
            background: rgba(255, 215, 0, 0.1);
            border: 2px solid #FFD700;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .info-card h3 {
            color: #FFD700;
            margin-bottom: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #444;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: bold;
            color: #ccc;
        }
        
        .info-value {
            color: #FFD700;
        }
        
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border: 2px solid #ffc107;
            color: #ffc107;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏪 Sistema Barbearia</h1>
            <p>Painel Administrativo - Cadastro do Primeiro Dono</p>
        </div>

        <?php if ($mensagem): ?>
            <div class="alert alert-success"><?= $mensagem ?></div>
        <?php endif; ?>

        <?php if ($erro): ?>
            <div class="alert alert-error"><?= $erro ?></div>
        <?php endif; ?>

        <?php if ($donoExiste && $infoDono): ?>
            <div class="info-card">
                <h3>✅ Dono da Barbearia Cadastrado</h3>
                <div class="info-item">
                    <span class="info-label">ID:</span>
                    <span class="info-value"><?= limparEntrada($infoDono['id']) ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Nome:</span>
                    <span class="info-value"><?= limparEntrada($infoDono['nome']) ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">CPF:</span>
                    <span class="info-value"><?= limparEntrada($infoDono['cpf']) ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Senha Mestra:</span>
                    <span class="info-value"><?= limparEntrada($infoDono['senha_mestra']) ?></span>
                </div>
                <div class="info-item">
                    <span class="info-label">Cadastrado em:</span>
                    <span class="info-value"><?= formatarDataBR($infoDono['data_criacao']) ?></span>
                </div>
            </div>
            
            <div class="warning">
                <strong>⚠️ Importante:</strong> O dono já está cadastrado. Para alterar a senha mestra, 
                ele deve usar o app Flutter com sua conta de dono.
            </div>
        <?php else: ?>
            <form method="POST">
                <div class="form-group">
                    <label for="nome">Nome Completo do Dono:</label>
                    <input type="text" id="nome" name="nome" required
                           value="<?= limparEntrada($_POST['nome'] ?? '') ?>">
                </div>

                <div class="form-group">
                    <label for="cpf">CPF (apenas números):</label>
                    <input type="text" id="cpf" name="cpf" maxlength="11" required
                           value="<?= limparEntrada($_POST['cpf'] ?? '') ?>"
                           placeholder="12345678901">
                </div>

                <div class="form-group">
                    <label for="senha">Senha de Acesso do Dono:</label>
                    <input type="password" id="senha" name="senha" required 
                           placeholder="Mínimo 6 caracteres">
                </div>

                <div class="form-group">
                    <label for="confirmar_senha">Confirmar Senha:</label>
                    <input type="password" id="confirmar_senha" name="confirmar_senha" required 
                           placeholder="Digite novamente a senha">
                </div>

                <div class="form-group">
                    <label for="senha_mestra">Senha Mestra (para cadastrar barbeiros):</label>
                    <input type="password" id="senha_mestra" name="senha_mestra" required 
                           placeholder="Mínimo 6 caracteres" value="admin123">
                </div>

                <button type="submit" class="btn">🏪 Cadastrar Primeiro Dono</button>
            </form>

            <div class="warning">
                <strong>⚠️ Atenção:</strong> Esta operação só pode ser feita uma vez. 
                Após cadastrar o primeiro dono, ele poderá gerenciar o sistema através do app Flutter.
            </div>
        <?php endif; ?>
    </div>

    <script>
        // Máscara para CPF
        document.getElementById('cpf').addEventListener('input', function(e) {
            this.value = this.value.replace(/\D/g, '');
        });
    </script>
</body>
</html>
