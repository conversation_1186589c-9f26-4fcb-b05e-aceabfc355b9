<?php
// Verificação dos IDs
if (!isset($id_planejamento) || !isset($id_usuario)) {
    die("ID do planejamento ou ID do usuário não definido.");
}

// Consulta SQL permanece a mesma
$query_consultar_materias_planejamento = "
    SELECT pm.materia_idmateria, m.nome AS nome_materia, m.cor AS cor_materia
    FROM appEstudo.planejamento_materia pm
    INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    WHERE pm.planejamento_idplanejamento = $id_planejamento";

$resultado_materias_planejamento = pg_query($conexao, $query_consultar_materias_planejamento);

$materias_no_planejamento = array();
$cores_materias = array();

while ($row = pg_fetch_assoc($resultado_materias_planejamento)) {
    $materia_id = $row['materia_idmateria'];
    $nome_materia = $row['nome_materia'];
    $cor_materia = $row['cor_materia'];

    $materias_no_planejamento[$materia_id] = $nome_materia;
    $cores_materias[$nome_materia] = $cor_materia;
}

$query_consulta_pontos_estudo = "
    SELECT m.nome AS nome_materia, 
           e.ponto_estudado AS ponto_estudado,
           SUM(e.tempo_liquido) AS tempo_estudo
    FROM appEstudo.materia m
    LEFT JOIN appEstudo.estudos e ON m.idmateria = e.materia_idmateria 
        AND e.planejamento_usuario_idusuario = $id_usuario
    WHERE m.idmateria IN (" . implode(',', array_keys($materias_no_planejamento)) . ")
    GROUP BY m.nome, e.ponto_estudado";

$resultado_consulta_pontos_estudo = pg_query($conexao, $query_consulta_pontos_estudo);

$pontos_estudo_por_materia = array();
$tempo_total_por_materia = array();

while ($row = pg_fetch_assoc($resultado_consulta_pontos_estudo)) {
    $materia = $row['nome_materia'];
    $ponto_estudado = $row['ponto_estudado'];
    $tempo_estudo = $row['tempo_estudo'];

    $tempo_estudo_segundos = $tempo_estudo ? converterParaSegundos($tempo_estudo) : 0;

    if (!isset($pontos_estudo_por_materia[$materia])) {
        $pontos_estudo_por_materia[$materia] = array();
    }
    $pontos_estudo_por_materia[$materia][$ponto_estudado] = $tempo_estudo_segundos;

    if (!isset($tempo_total_por_materia[$materia])) {
        $tempo_total_por_materia[$materia] = 0;
    }
    $tempo_total_por_materia[$materia] += $tempo_estudo_segundos;
}

$dados_json = json_encode($pontos_estudo_por_materia);
$cores_json = json_encode($cores_materias);
$tempo_total_json = json_encode($tempo_total_por_materia);
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">

    <style>
        .container {
            max-width: 1000px;
            margin: 0 auto;
            border-radius: 15px;
        }

        .estatistica-header {
            background: #000080;
            padding: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }

        .estatistica-titulo {
            font-size: 1.2rem;
            font-weight: 600;
            letter-spacing: 0.5px;
            font-family: 'Cinzel', serif;
            margin-bottom: 0.5rem;
        }

        .chart-container {
            padding: 2rem;
        }
    </style>
</head>
<body>

    
        
            
            
       

        <div class="chart-container">
            <div id="radar" style="min-height: 700px;"></div>
            <!-- Removendo o container da legenda -->
        </div>
    


<script>
    document.addEventListener('DOMContentLoaded', function() {
        var materias = <?php echo json_encode(array_keys($tempo_total_por_materia)); ?>;
        var tempoTotalPorMateria = <?php echo json_encode(array_values($tempo_total_por_materia)); ?>;
        var cores = <?php echo $cores_json; ?>;

        // Função para gerar ordinal em português
        function getOrdinal(n) {
            return n + "º";
        }

        // Organiza os dados por tempo (do maior para o menor)
        let dadosOrdenados = materias.map((materia, index) => ({
            materia: materia,
            tempo: tempoTotalPorMateria[index],
            cor: cores[materia]
        })).sort((a, b) => b.tempo - a.tempo);

        function converterParaHHMM(segundos) {
            var horas = Math.floor(segundos / 3600);
            var minutos = Math.floor((segundos % 3600) / 60);
            return horas + "h:" + (minutos < 10 ? "0" + minutos : minutos) + "min";
        }

        // Criar gráfico
        Highcharts.chart('radar', {
            credits: {
                enabled: false  // Remove a marca Highcharts.com
            },
            chart: {
                type: 'bar',
                backgroundColor: 'transparent'
            },
            title: {
                text: null
            },
            legend: {
                enabled: false  // Remove a legenda
            },
            xAxis: {
                categories: dadosOrdenados.map(d => d.materia),
                labels: {
                    style: {
                        fontFamily: 'Quicksand, sans-serif',
                        fontWeight: '600'
                    }
                }
            },
            yAxis: {
                title: {
                    text: 'Tempo de Estudo',
                    style: {
                        fontFamily: 'Quicksand, sans-serif'
                    }
                },
                labels: {
                    formatter: function() {
                        return converterParaHHMM(this.value);
                    }
                }
            },
            tooltip: {
                formatter: function() {
                    return `<div style="color: ${this.point.color}">
                        <b>${this.point.category}</b><br/>
                        <b>${this.point.rank}º Lugar</b><br/>
                        <b>Tempo Total:</b> ${converterParaHHMM(this.y)}<br/>
                        <b>Proporção:</b> ${Math.round(this.point.percentage)}% do total
                    </div>`;
                }
            },
            series: [{
                name: 'Tempo de Estudo',
                data: dadosOrdenados.map((d, index) => ({
                    y: d.tempo,
                    color: d.cor,
                    rank: index + 1,
                    percentage: (d.tempo / tempoTotalPorMateria.reduce((a, b) => a + b, 0)) * 100
                }))
            }],
            plotOptions: {
                bar: {
                    borderRadius: 8,
                    borderWidth: 0,
                    shadow: false,
                    colorByPoint: true,
                    dataLabels: {
                        enabled: true,
                        formatter: function() {
                            return converterParaHHMM(this.y);
                        },
                        style: {
                            fontWeight: '600',
                            textOutline: 'none'
                        }
                    }
                },
                series: {
                    pointWidth: 25,
                    animation: {
                        duration: 1000
                    }
                }
            }
        });
    });
</script>

</body>
</html>
