<?php
//calendario_mensal.php
session_start();
require_once 'includes/verificar_modulo.php';
require_once 'includes/mensagens.php';
verificarModulo();
if (!isset($_SESSION['idusuario'])) {
    header("Location: /login_index.php");
    exit();
}

include_once("assets/config.php");

setlocale(LC_TIME, 'pt_BR', 'pt_BR.utf-8');

if (!function_exists('strftime')) {
    function strftime($format, $timestamp = null) {
        $date = $timestamp === null ? new DateTime() : new DateTime("@$timestamp");
        $format = strtr($format, [
            '%B' => $date->format('F'),
            '%Y' => $date->format('Y')
        ]);
        return $format;
    }
}
// Configurar data de início
$data_inicio = isset($prova['data_inicio_estudo']) ? new DateTime($prova['data_inicio_estudo']) : new DateTime();

$mes_selecionado = isset($_GET['mes']) ? $_GET['mes'] : date('m');
$ano_selecionado = isset($_GET['ano']) ? $_GET['ano'] : date('Y');
$data_atual = new DateTime("$ano_selecionado-$mes_selecionado-01");

function isLightColor($hex) {
    $hex = ltrim($hex, '#');
    if (strlen($hex) === 3) {
        $hex = $hex[0] . $hex[0] . $hex[1] . $hex[1] . $hex[2] . $hex[2];
    }
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));
    $yiq = (($r * 299) + ($g * 587) + ($b * 114)) / 1000;
    return $yiq >= 128;
}

// Na função gerarCalendario, adicione verificações
function gerarCalendario($mes, $ano, $conteudosPorDia, $mode = 'mes') {
    if (!$mes) $mes = date('m');
    if (!$ano) $ano = date('Y');
    if (!$conteudosPorDia) $conteudosPorDia = [];

    $diasSemana = $mode == 'semana' ?
        ['Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'] :
        ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];

    $primeiroDia = new DateTime("$ano-$mes-01");
    $ultimoDia = new DateTime($primeiroDia->format('Y-m-t'));

    echo '<div class="calendario-grid">';

    foreach ($diasSemana as $dia) {
        echo "<div class='dia-header'>$dia</div>";
    }

    $primeiroDiaSemana = (int)$primeiroDia->format('w');
    for ($i = 0; $i < $primeiroDiaSemana; $i++) {
        if ($mode == 'semana' && $i == 0) continue; // Pula domingo na view semanal
        echo "<div class='dia-celula dia-inativo'></div>";
    }

    $diaAtual = clone $primeiroDia;
    while ($diaAtual <= $ultimoDia) {
        $diaSemana = (int)$diaAtual->format('w');
        if ($mode == 'semana' && $diaSemana == 0) {
            $diaAtual->modify('+1 day');
            continue;
        }

        $diaClass = $diaSemana == 0 ? 'dia-celula dia-inativo' : 'dia-celula';
        $dataStr = $diaAtual->format('Y-m-d');

        echo "<div class='$diaClass'>";
        echo "<div class='dia-numero'>" . $diaAtual->format('d') . "</div>";

        if (isset($conteudosPorDia[$dataStr]) && $diaSemana != 0) {
            echo "<div class='dia-conteudo'>";
            foreach ($conteudosPorDia[$dataStr] as $conteudo) {
                $bgColor = $conteudo['cor'];
                $textColor = isLightColor($conteudo['cor']) ? '#000' : '#fff';
                echo "<div class='card-estudo' style='background-color: $bgColor; color: $textColor'>";
                echo "<small>{$conteudo['materia']}</small><br>";
                if (!empty($conteudo['descricao_capitulo_principal'])) {
                    echo "<small><i>{$conteudo['descricao_capitulo_principal']}</i></small><br>";
                }
                echo "<strong>{$conteudo['capitulo']}</strong> {$conteudo['descricao']}";
                echo "</div>";
            }
            echo "</div>";
        }

        echo "</div>";
        $diaAtual->modify('+1 day');
    }

    echo '</div>';
}



$usuario_id = $_SESSION['idusuario'];

// Buscar informações da prova ativa
$query_prova = "
    SELECT p.*, ap.horas_estudo_dia 
    FROM appestudo.provas p
    LEFT JOIN appestudo.ajustes_plano ap ON p.id = ap.prova_id
    WHERE p.usuario_id = $usuario_id 
    AND p.status = true 
    ORDER BY p.created_at DESC 
    LIMIT 1";
$result_prova = pg_query($conexao, $query_prova);
$prova = pg_fetch_assoc($result_prova);

if (!$prova) {
    header("Location: configurar_prova.php");
    exit();
}

// Calcular total de conteúdos
$query_total = "
    SELECT COUNT(*) as total
    FROM appestudo.usuario_conteudo uc
    WHERE uc.usuario_id = $usuario_id 
    AND uc.status = true";
$result_total = pg_query($conexao, $query_total);
$total_row = pg_fetch_assoc($result_total);
$total_conteudos = $total_row['total'];

// Calcular dias úteis até a prova
$data_prova = new DateTime($prova['data_prova']);
$hoje = new DateTime($prova['data_inicio_estudo']);
$dias_para_prova = $hoje->diff($data_prova)->days;
$multiplicador_peso = ($dias_para_prova <= 7) ? 2 : 1;

// Calcular dias úteis (considerando 6 dias por semana)
if ($dias_para_prova > 0) {
    $semanas_totais = ceil($dias_para_prova / 7); // Usa ceil para arredondar para cima
    $dias_extras = $dias_para_prova % 7;
    $dias_uteis_totais = ($semanas_totais - 1) * 6 + min($dias_extras, 6); // Ajusta dias úteis
} else {
    $semanas_totais = 0;
    $dias_uteis_totais = 0;
}
if ($dias_para_prova <= 0) {
    echo "<div class='alert-message warning'>";
    echo "<i class='fas fa-exclamation-circle'></i>";
    echo "<span>A data da prova já passou ou é hoje! Reavalie seu cronograma.</span>";
    echo "</div>";
}


// Calcular cards necessários por dia
if ($dias_uteis_totais > 0) {
    $cards_necessarios = ceil($total_conteudos / $dias_uteis_totais);
} else {
    $cards_necessarios = $total_conteudos;
}

// Ajustar para um número razoável de cards (entre 2 e 10)
$cardsPerDay = max(2, min(10, $cards_necessarios));

// Recalcular total de semanas com base nos cards por dia
$cardsPerWeek = $cardsPerDay * 6; // 6 dias por semana
$totalSemanas = ceil($total_conteudos / $cardsPerWeek);

// Verificar se o tempo está apertado
$tempo_apertado = ($cardsPerDay >= 8);

// AQUI VAI SUA QUERY GRANDE DE CONTEÚDOS (a que começa com WITH niveis_do_edital)
// Query atualizada para incluir o status_estudo
$query = "
WITH niveis_do_edital AS (
    SELECT 
        edital_id,
        materia_id,
        MIN(capitulo) as primeiro_capitulo
    FROM 
        appestudo.conteudo_edital
    GROUP BY 
        edital_id, materia_id
),
pesos_calculados AS (
    -- Calcula os pesos considerando as configurações da prova
    SELECT 
        m.idmateria,
        COALESCE(pm.peso, 1) * COALESCE(pm.nivel_dificuldade, 1) * 
        CASE 
            WHEN (SELECT data_prova::date - CURRENT_DATE <= 7 
                  FROM appestudo.provas 
                  WHERE usuario_id = $usuario_id AND status = true) 
            THEN 2 
            ELSE 1 
        END as prioridade_calculada
    FROM appestudo.materia m
    LEFT JOIN appestudo.pesos_materias pm ON m.idmateria = pm.materia_id
    LEFT JOIN appestudo.provas p ON pm.prova_id = p.id AND p.status = true
    WHERE p.usuario_id = $usuario_id
),
conteudo_base AS (
    SELECT 
        uc.id, 
        m.idmateria,
        m.nome AS materia_nome, 
        m.cor, 
        ce.descricao, 
        ce.capitulo,
        pc.prioridade_calculada,
        CASE 
            WHEN ce.capitulo ~ '^\d+\.\d+\.\d+$' AND EXISTS (
                SELECT 1 FROM niveis_do_edital ne 
                WHERE ne.edital_id = ce.edital_id 
                AND ne.materia_id = ce.materia_id 
                AND ne.primeiro_capitulo ~ '^\d+\.\d+$'
            ) THEN 
                (SELECT cp.capitulo || ' ' || cp.descricao 
                 FROM appestudo.conteudo_edital cp
                 WHERE cp.edital_id = ce.edital_id
                 AND cp.materia_id = ce.materia_id
                 AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || split_part(ce.capitulo, '.', 2)
                 ORDER BY cp.ordem
                 LIMIT 1)
            WHEN ce.capitulo ~ '^\d+\.\d+\.\d+\.\d+$' AND EXISTS (
                SELECT 1 FROM niveis_do_edital ne 
                WHERE ne.edital_id = ce.edital_id 
                AND ne.materia_id = ce.materia_id 
                AND ne.primeiro_capitulo ~ '^\d+\.\d+\.\d+$'
            ) THEN 
                (SELECT cp.capitulo || ' ' || cp.descricao 
                 FROM appestudo.conteudo_edital cp
                 WHERE cp.edital_id = ce.edital_id
                 AND cp.materia_id = ce.materia_id
                 AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || split_part(ce.capitulo, '.', 2) || '.' || split_part(ce.capitulo, '.', 3)
                 ORDER BY cp.ordem
                 LIMIT 1)
            ELSE 
                (SELECT cp.capitulo || ' ' || cp.descricao 
                 FROM appestudo.conteudo_edital cp
                 WHERE cp.edital_id = ce.edital_id
                 AND cp.materia_id = ce.materia_id
                 AND cp.capitulo = split_part(ce.capitulo, '.', 1) || '.' || split_part(ce.capitulo, '.', 2) || '.' || split_part(ce.capitulo, '.', 3)
                 ORDER BY cp.ordem
                 LIMIT 1)
        END as descricao_capitulo_principal,
        uc.status_estudo,
        CAST(split_part(ce.capitulo, '.', 1) AS INTEGER) AS capitulo_principal,
        COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 2), '') AS INTEGER), 0) AS subnivel1,
        COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 3), '') AS INTEGER), 0) AS subnivel2,
        COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 4), '') AS INTEGER), 0) AS subnivel3,
        ce.ordem,
        -- Número sequencial dentro de cada matéria
        ROW_NUMBER() OVER (
            PARTITION BY ce.materia_id 
            ORDER BY 
                CAST(split_part(ce.capitulo, '.', 1) AS INTEGER),
                COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 2), '') AS INTEGER), 0),
                COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 3), '') AS INTEGER), 0),
                COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 4), '') AS INTEGER), 0)
        ) as ordem_na_materia
    FROM 
        appestudo.usuario_conteudo AS uc
    JOIN 
        appestudo.conteudo_edital AS ce ON uc.conteudo_id = ce.id_conteudo
    JOIN 
        appestudo.materia AS m ON ce.materia_id = m.idmateria
    JOIN
        pesos_calculados pc ON m.idmateria = pc.idmateria
    WHERE 
        uc.usuario_id = $usuario_id
        AND uc.status = true
        AND ce.capitulo ~ '^[0-9.]+$'
)
SELECT 
    id, 
    idmateria,
    materia_nome, 
    cor, 
    descricao, 
    capitulo,
    descricao_capitulo_principal,
    status_estudo,
    capitulo_principal,
    subnivel1,
    subnivel2,
    subnivel3,
    ordem,
    ordem_na_materia
FROM 
    conteudo_base
ORDER BY 
    prioridade_calculada DESC, -- Primeiro ordenar por prioridade
    ordem_na_materia,         -- Depois pela ordem dentro da matéria
    idmateria;                -- Por fim pelo ID da matéria
";

$result_conteudos = pg_query($conexao, $query);

if (!$result_conteudos || pg_num_rows($result_conteudos) == 0) {
    echo "<p>Erro: Nenhum conteúdo selecionado para estudo. Por favor, selecione conteúdos para o plano de estudo.</p>";
    exit();
}

// Organizar conteúdos
$conteudos = [];
while ($row = pg_fetch_assoc($result_conteudos)) {
    $conteudos[] = [
        'id' => $row['id'],
        'descricao' => $row['descricao'],
        'materia' => $row['materia_nome'],
        'cor' => $row['cor'],
        'capitulo' => $row['capitulo'],
        'descricao_capitulo_principal' => $row['descricao_capitulo_principal'],
        'status_estudo' => $row['status_estudo'],
        'capitulo_principal' => $row['capitulo_principal'],
        'subnivel1' => $row['subnivel1'],
        'subnivel2' => $row['subnivel2']
    ];
}

// Organizar em semanas e dias
$conteudosPorSemana = [];
$totalDias = 6;
$conteudoIndex = 0;
$totalConteudos = count($conteudos);

while ($conteudoIndex < $totalConteudos) {
    $semana = [];

    for ($dia = 0; $dia < $totalDias; $dia++) {
        $conteudosDoDia = [];

        for ($i = 0; $i < $cardsPerDay && $conteudoIndex < $totalConteudos; $i++) {
            $conteudosDoDia[] = $conteudos[$conteudoIndex];
            $conteudoIndex++;
        }

        $semana[] = $conteudosDoDia;
    }

    if (!empty($semana)) {
        $conteudosPorSemana[] = $semana;
    }
}

// Agora defina o total de semanas baseado na organização real
$totalSemanas = count($conteudosPorSemana);

// Preparar informações para exibição
$info_estudo = [
    'total_conteudos' =>count($conteudos),
    'dias_ate_prova' => $dias_para_prova,
    'dias_uteis' => $dias_uteis_totais,
    'cards_por_dia' => $cardsPerDay,
    'semanas_necessarias' => $totalSemanas
];

// Verificar se o número de semanas necessárias ultrapassa as semanas disponíveis
if ($totalSemanas > $semanas_totais) {
    echo "<div class='alert-message warning'>";
    echo "<i class='fas fa-exclamation-circle'></i>";
    echo "<span>Atenção: O plano de estudo exige $totalSemanas semanas, mas há apenas $semanas_totais semanas disponíveis. Considere ajustar sua carga diária ou reavaliar os conteúdos.</span>";
    echo "</div>";
}

// Organizar conteúdos por data
$conteudosPorDia = [];
$dataAtual = clone $data_inicio;

foreach ($conteudosPorSemana as $semana) {
    foreach ($semana as $dia) {
        if ($dataAtual->format('w') != 0) { // Pula domingo
            $dataStr = $dataAtual->format('Y-m-d');
            $conteudosPorDia[$dataStr] = $dia;
        }
        $dataAtual->modify('+1 day');
    }
}

$totalSemanas = count($conteudosPorSemana);


?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plano de Estudo Vintage</title>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Old+Standard+TT:wght@400;700&family=Crimson+Text:wght@400;600&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <style>
        .btn-voltar {
            position: fixed;
            top: 20px;
            left: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: var(--paper-color);
            border: 2px solid var(--border-color);
            border-radius: 50%;
            box-shadow: 4px 4px 0 var(--border-color);
            color: var(--primary-color);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .btn-voltar:hover {
            transform: translateY(-2px);
            box-shadow: 4px 4px 0 var(--border-color);
            background-color: var(--primary-color);
            color: white;
        }

        .btn-voltar:active {
            transform: translateY(0);
            box-shadow: 2px 2px 0 var(--border-color);
        }

        .btn-voltar i {
            font-size: 1.5rem;
        }

        /* Para telas menores */
        @media (max-width: 768px) {
            .btn-voltar {
                top: 10px;
                left: 10px;
                width: 40px;
                height: 40px;
            }

            .btn-voltar i {
                font-size: 1.2rem;
            }
        }

        .config-form {
            background: var(--paper-color);
            padding: 20px;
            margin-bottom: 20px;
            border: 2px solid var(--border-color);
            box-shadow: 3px 3px 0 var(--border-color);
        }

        .config-section {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .config-section label {
            font-family: 'Old Standard TT', serif;
            color: var(--primary-color);
            font-weight: bold;
        }

        .config-section select {
            padding: 8px;
            border: 1px solid var(--border-color);
            background: white;
            font-family: 'Crimson Text', serif;
            color: var(--text-color);
            cursor: pointer;
        }
        .dia {
            min-height: 200px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .dia h3 {
            position: sticky;
            top: 0;
            background: var(--paper-color); /* Cor sólida em vez de transparente */
            padding: 15px;
            z-index: 10;
            margin: -20px -20px 10px -20px;
            border-bottom: 2px solid var(--border-color);
            font-family: 'Old Standard TT', serif;
            font-size: 1.4rem;
            color: var(--primary-color);
            text-align: center;
            /* Adicione uma sombra sutil para dar mais destaque */
            box-shadow: 0 2px 4px rgba(139, 69, 19, 0.1);
        }

        /* Adicione este estilo para garantir que os conteúdos fiquem abaixo do cabeçalho */
        .conteudo-item {
            position: relative;
            z-index: 1;
        }

        .conteudo-item {
            flex: 1;
        }

        /* Estilos adicionais */
        .updating {
            opacity: 0.7;
            position: relative;
        }

        .updating::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 20px;
            height: 20px;
            margin: -10px 0 0 -10px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-top-color: #fff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .semana-counter {
            margin-left: auto;
            padding: 4px 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 12px;
            font-size: 0.9em;
        }

        .semana-completa .semana-title {
            background: rgba(0, 128, 0, 0.1);
        }

        .semana-completa .semana-counter {
            background: rgba(0, 128, 0, 0.2);
            color: darkgreen;
        }

        /* Otimização para mobile */
        @media (max-width: 768px) {
            .semana-title {
                font-size: 1.2rem;
                padding: 15px;
            }

            .semana-counter {
                font-size: 0.8em;
                padding: 3px 6px;
            }
        }
        :root {
            --primary-color: #B85C5C; /* Vermelho claro queimado */
            --paper-color: #EDE3D0; /* Papel velho um pouco mais claro */
            --secondary-color: #D2691E;
            --background-color: #F5E6D3;
            --text-color: #2C1810;
            --border-color: #8B4513;
        }



        body {
            font-family: 'Crimson Text', serif;
            background-color: var(--background-color);
            background-image:
                    linear-gradient(rgba(245, 230, 211, 0.9), rgba(245, 230, 211, 0.9)),
                    url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%238b4513' fill-opacity='0.1' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
            color: var(--text-color);
            padding: 40px;
            line-height: 1.6;
        }

        h1 {
            font-family: 'Playfair Display', serif;
            text-align: center;
            color: var(--primary-color);
            font-size: 2.5rem;
            margin-bottom: 40px;
            text-transform: uppercase;
            letter-spacing: 3px;
            position: relative;
        }

        h1::after {
            content: "";
            display: block;
            width: 150px;
            height: 3px;
            background-color: var(--primary-color);
            margin: 20px auto;
        }

        .progress-container {
            width: 100%;
            background: var(--paper-color);
            border-radius: 0;
            overflow: hidden;
            margin: 20px 0;
            border: 2px solid var(--border-color);
            padding: 3px;
            box-shadow: 3px 3px 0 var(--border-color);
        }

        .progress-bar {
            width: 0%;
            height: 25px;
            background: var(--primary-color);
            transition: width 0.5s ease;
            position: relative;
        }

        .progress-text {
            font-family: 'Old Standard TT', serif;
            margin-top: 10px;
            text-align: center;
            font-weight: bold;
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .semana {
            margin-bottom: 20px;
            background: var(--paper-color);
            border: 2px solid var(--border-color);
            box-shadow: 5px 5px 0 var(--border-color);
        }

        .semana-title {
            font-family: 'Playfair Display', serif;
            font-size: 1.8rem;
            color: var(--primary-color);
            padding: 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(139, 69, 19, 0.1);
            transition: background-color 0.3s ease;
        }

        .semana-title:hover {
            background: rgba(139, 69, 19, 0.15);
        }

        .semana-title .icon {
            transition: transform 0.3s ease;
        }

        .semana-title.collapsed .icon {
            transform: rotate(-90deg);
        }

        .semana-content {
            padding: 30px;
            display: none;
        }

        .semana-content.active {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .cronograma {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            padding: 20px;
        }

        .dia {
            background: rgba(255, 248, 220, 0.7);
            padding: 20px;
            border: 1px solid var(--border-color);
        }

        .dia h3 {
            font-family: 'Old Standard TT', serif;
            font-size: 1.4rem;
            color: var(--primary-color);
            margin-bottom: 20px;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
            padding-bottom: 10px;
        }

        .conteudo-item {
            margin-bottom: 15px;
            padding: 20px;
            transition: all 0.3s ease;
            position: relative;
            border: none;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
            color: #fff;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
        }


        .conteudo-item.completed {
            opacity: 0.6;
            transform: scale(0.98);
            filter: grayscale(30%);
            border-style: dashed;
        }

        .conteudo-item.completed::after {
            content: '✓ Concluído';
            position: absolute;
            top: 10px;
            right: 10px;
            font-family: 'Old Standard TT', serif;
            font-size: 0.8rem;
            padding: 2px 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            border-radius: 3px;
            text-shadow: none;
        }

        .conteudo-item:not(.completed):hover {
            transform: translateY(-3px);
            box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.2);
        }

        .conteudo-item.completed:hover {
            transform: scale(0.98);
            cursor: default;
        }

        .conteudo-item label {
            display: flex;
            align-items: flex-start;
            cursor: pointer;
            position: relative;
            z-index: 1;
        }

        .conteudo-item input[type="checkbox"] {
            appearance: none;
            -webkit-appearance: none;
            width: 20px;
            height: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(0, 0, 0, 0.2);
            margin-right: 10px;
            position: relative;
            cursor: pointer;
            flex-shrink: 0;
            border-radius: 3px;
        }

        .conteudo-item input[type="checkbox"]:checked::after {
            content: "✓";
            position: absolute;
            color: #000;
            font-size: 16px;
            left: 2px;
            top: -2px;
        }

        .conteudo-item h4 {
            font-family: 'Old Standard TT', serif;
            margin: 0;
            font-size: 1.1rem;
            font-weight: bold;
        }

        .conteudo-item p {
            margin: 5px 0 0;
            font-size: 0.95rem;
            opacity: 0.9;
        }

        .conteudo-item.light-color {
            color: #333;
            text-shadow: none;
        }

        .conteudo-item.light-color input[type="checkbox"] {
            border-color: rgba(0, 0, 0, 0.3);
        }

        @media (max-width: 768px) {
            body {
                padding: 20px;
            }

            .cronograma {
                grid-template-columns: 1fr;
            }

            .conteudo-item {
                margin-bottom: 10px;
            }
        }

        .capitulo-principal {
            margin-bottom: 8px;
            font-style: italic;
            opacity: 0.85;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding-bottom: 5px;
        }

        .subcapitulo {
            margin-top: 5px;
            font-weight: bold;
        }
        .config-form {
            background: var(--paper-color);
            padding: 20px;
            margin-bottom: 20px;
            border: 2px solid var(--border-color);
            box-shadow: 3px 3px 0 var(--border-color);
        }

        .config-section {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .config-section label {
            font-family: 'Old Standard TT', serif;
            color: var(--primary-color);
            font-weight: bold;
        }

        .config-section select {
            padding: 8px;
            border: 1px solid var(--border-color);
            background: white;
            font-family: 'Crimson Text', serif;
            color: var(--text-color);
            cursor: pointer;
        }

        .total-semanas {
            font-family: 'Old Standard TT', serif;
            color: var(--primary-color);
            font-weight: bold;
            padding: 5px 10px;
            background: rgba(139, 69, 19, 0.1);
            border-radius: 4px;
        }
        .dashboard {
            background: var(--paper-color);
            padding: 20px;
            margin: 20px 0;
            border: 2px solid var(--border-color);
            box-shadow: 5px 5px 0 var(--border-color);
        }

        .dashboard h2 {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            text-align: center;
            margin-bottom: 20px;
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .stat-card {
            padding: 15px;
            border-radius: 8px;
            box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.1);
        }

        .stat-card h3 {
            margin: 0 0 10px 0;
            font-size: 1.2em;
        }

        .stat-progress {
            background: rgba(255, 255, 255, 0.3);
            height: 10px;
            border-radius: 5px;
            margin: 10px 0;
            overflow: hidden;
        }

        .stat-progress .progress-bar {
            height: 100%;
            background: rgba(255, 255, 255, 0.8);
            transition: width 0.3s ease;
        }

        .light-text {
            color: white;
        }

        .dark-text {
            color: #333;
        }

        .recompensa-notificacao {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(100px);
            opacity: 0;
            transition: all 0.3s ease;
            z-index: 1000;
            font-family: 'Old Standard TT', serif;
        }

        .recompensa-notificacao.mostrar {
            transform: translateY(0);
            opacity: 1;
        }

        /* Ajustes para os cards de revisão */
        .revisoes-container {
            background: var(--paper-color);
            padding: 30px;
            margin: 20px 0;
            border: 2px solid var(--border-color);
            box-shadow: 5px 5px 0 var(--border-color);
            position: relative; /* Adiciona posicionamento relativo */
            overflow: visible; /* Permite que as sombras dos cards apareçam */
        }

        .revisoes-container h2 {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            text-align: center;
            margin-bottom: 30px;
            font-size: 2rem;
            padding-bottom: 15px;
            border-bottom: 2px solid rgba(139, 69, 19, 0.2);
        }

        .revisoes-lista {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            padding: 10px;
            margin: 0; /* Reseta margens */
            width: calc(100% - 20px); /* Ajusta a largura considerando o padding */
        }

        .card-revisao {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            height: 100%;
            border-left: 5px solid;
            margin: 0; /* Reseta margens */
            max-width: 100%; /* Limita a largura máxima */
            box-sizing: border-box; /* Inclui padding na largura total */
        }

        .card-revisao h4 {
            color: var(--primary-color);
            margin: 0 0 20px 0;
            font-family: 'Old Standard TT', serif;
            font-size: 1.3rem;
            padding-bottom: 12px;
            border-bottom: 1px solid rgba(139, 69, 19, 0.1);
        }

        .revisao-capitulo {
            font-weight: bold;
            color: var(--secondary-color);
            margin-bottom: 12px;
            font-size: 1rem;
        }

        .revisao-descricao {
            font-size: 1.1rem;
            line-height: 1.6;
            color: var(--text-color);
            margin-bottom: 25px;
        }

        .avaliacao-confianca {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid rgba(139, 69, 19, 0.1);
        }

        .avaliacao-confianca p {
            text-align: center;
            margin-bottom: 15px;
            font-family: 'Old Standard TT', serif;
            color: var(--primary-color);
            font-size: 1.1rem;
        }

        .botoes-confianca {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            padding: 0 10px;
        }

        .btn-confianca {
            padding: 12px;
            border: 1px solid var(--border-color);
            background: var(--paper-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'Crimson Text', serif;
            font-size: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .btn-confianca:hover {
            background: var(--primary-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(139, 69, 19, 0.2);
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .revisoes-container {
                padding: 20px;
            }

            .botoes-confianca {
                grid-template-columns: 1fr 1fr;
            }

            .revisoes-lista {
                grid-template-columns: 1fr; /* Uma coluna em telas menores */
                padding: 0 15px 15px;
            }

            .card-revisao {
                margin: 0;
            }
        }

        @media (max-width: 480px) {
            .botoes-confianca {
                grid-template-columns: 1fr;
            }

            .card-revisao {
                padding: 20px;
            }
        }

        .card-revisao .capitulo-principal {
            margin-bottom: 8px;
            font-style: italic;
            opacity: 0.85;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            padding-bottom: 5px;
            color: var(--secondary-color);
        }

        .card-revisao .subcapitulo {
            margin-top: 5px;
            font-weight: bold;
            color: var(--text-color);
        }

        .card-revisao h4 {
            font-family: 'Old Standard TT', serif;
            margin: 0;
            font-size: 1.1rem;
            font-weight: bold;
            color: var(--primary-color);
        }

        /* Mensagens de status */
        .sem-revisoes, .erro-revisoes {
            text-align: center;
            padding: 20px;
            background: var(--paper-color);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            font-family: 'Old Standard TT', serif;
            color: var(--primary-color);
        }
        .dia{
            background: var(--primary-color);
        }

        .prova-config {
            background: var(--paper-color);
            padding: 30px;
            margin: 20px 0;
            border: 2px solid var(--border-color);
            box-shadow: 5px 5px 0 var(--border-color);
        }

        .prova-form {
            max-width: 800px;
            margin: 0 auto;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-family: 'Old Standard TT', serif;
            color: var(--primary-color);
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }

        .materias-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .materia-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .peso-controls {
            margin-top: 10px;
        }

        .btn-submit {
            background: var(--primary-color);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-family: 'Old Standard TT', serif;
            transition: all 0.3s ease;
        }

        .btn-submit:hover {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        /* Ajuste responsivo */
        @media (max-width: 768px) {
            .materias-grid {
                grid-template-columns: 1fr;
            }
        }

        /* Estilos para o container principal de informações */
        .plano-info {
            margin: 30px 0;
        }

        .info-cards {
            background: var(--paper-color);
            padding: 30px;
            border: 2px solid var(--border-color);
            box-shadow: 5px 5px 0 var(--border-color);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .info-cards.alert-warning {
            background: rgba(184, 92, 92, 0.1);
            border-color: var(--primary-color);
        }

        /* Cabeçalho elegante */
        .info-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid rgba(139, 69, 19, 0.1);
            position: relative;
        }

        .info-header::after {
            content: "";
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 2px;
            background: var(--primary-color);
        }

        .info-header h3 {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            font-size: 1.8rem;
            margin: 0 0 10px 0;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .info-header p {
            font-family: 'Old Standard TT', serif;
            color: var(--secondary-color);
            font-style: italic;
            margin: 0;
            font-size: 1.1rem;
        }

        /* Grid de informações */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .info-item {
            background: rgba(255, 255, 255, 0.5);
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid rgba(139, 69, 19, 0.2);
            transition: all 0.3s ease;
        }

        .info-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(139, 69, 19, 0.1);
        }

        .info-item strong {
            display: block;
            color: var(--primary-color);
            font-family: 'Old Standard TT', serif;
            font-size: 1rem;
            margin-bottom: 8px;
        }

        .info-item span {
            display: block;
            color: var(--secondary-color);
            font-size: 1.4rem;
            font-weight: bold;
            font-family: 'Playfair Display', serif;
        }

        /* Mensagens de alerta */
        .alert-message {
            background: rgba(184, 92, 92, 0.1);
            border-left: 4px solid var(--primary-color);
            padding: 15px 20px;
            margin-top: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            border-radius: 4px;
        }

        .alert-message i {
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .alert-message span {
            color: var(--text-color);
            font-family: 'Old Standard TT', serif;
            font-size: 1.1rem;
        }

        .alert-message.warning {
            background: rgba(255, 193, 7, 0.1);
            border-left-color: #ffc107;
        }

        .alert-message.warning i {
            color: #ffc107;
        }

        /* Botões de ação */
        .actions {
            text-align: center;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid rgba(139, 69, 19, 0.1);
        }

        .btn-config {
            display: inline-block;
            background: var(--primary-color);
            color: white;
            padding: 12px 25px;
            border-radius: 6px;
            text-decoration: none;
            font-family: 'Old Standard TT', serif;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .btn-config:hover {
            background: var(--paper-color);
            color: var(--primary-color);
            border-color: var(--primary-color);
            transform: translateY(-2px);
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .info-cards {
                padding: 20px;
            }

            .info-header h3 {
                font-size: 1.5rem;
            }

            .info-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .info-item {
                padding: 15px;
            }

            .info-item span {
                font-size: 1.2rem;
            }
        }


    </style>
    <style>

        .calendario-nav-tipo {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
        }

        .btn-view {
            background: var(--paper-color);
            border: 2px solid var(--border-color);
            padding: 8px 15px;
            cursor: pointer;
            font-family: 'Old Standard TT', serif;
            transition: all 0.3s ease;
        }

        .btn-view.active {
            background: var(--primary-color);
            color: white;
        }

        .semana-view .dia-celula {
            min-height: 150px;
        }
        .calendario-wrapper {
            background: var(--paper-color);
            padding: 30px;
            border: 2px solid var(--border-color);
            box-shadow: 5px 5px 0 var(--border-color);
            margin: 20px 0;
        }

        .calendario-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 2px solid var(--border-color);
            padding-bottom: 10px;
        }

        .calendario-nav {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .calendario-nav button {
            background: var(--paper-color);
            border: 2px solid var(--border-color);
            padding: 8px 15px;
            cursor: pointer;
            font-family: 'Old Standard TT', serif;
            transition: all 0.3s ease;
        }

        .calendario-nav button:hover {
            background: var(--primary-color);
            color: white;
        }

        .calendario-mes {
            font-family: 'Playfair Display', serif;
            font-size: 1.8rem;
            color: var(--primary-color);
        }

        .calendario-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 10px;
        }

        .dia-header {
            text-align: center;
            padding: 10px;
            font-family: 'Old Standard TT', serif;
            font-weight: bold;
            color: var(--primary-color);
            border-bottom: 1px solid var(--border-color);
        }

        .dia-celula {
            min-height: 120px;
            border: 1px solid var(--border-color);
            padding: 10px;
            background: rgba(255, 255, 255, 0.5);
        }

        .dia-numero {
            font-family: 'Playfair Display', serif;
            font-size: 1.2rem;
            margin-bottom: 5px;
            color: var(--primary-color);
        }

        .dia-conteudo {
            max-height: 200px;
            overflow-y: auto;
        }

        .card-estudo {
            margin: 5px 0;
            padding: 8px;
            border-radius: 4px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }

        .dia-inativo {
            background: #f5f5f5;
            opacity: 0.5;
        }

        @media (max-width: 768px) {
            .calendario-grid {
                font-size: 0.9rem;
            }

            .dia-celula {
                min-height: 100px;
            }
        }
    </style>
</head>
<body>
<a href="https://concurseirooff.com.br/edital_verticalizado/index.php" class="btn-voltar">
    <i class="fas fa-arrow-left"></i>
</a>

<h1>Plano de Estudos: <?= htmlspecialchars($prova['nome']) ?></h1>

<!-- Informações do Plano -->
<div class="plano-info">
    <div class="info-cards <?php echo $tempo_apertado ? 'alert-warning' : 'alert-info'; ?>">
        <div class="info-header">
            <h3>Plano Otimizado de Estudos</h3>
            <p>Calculado para concluir todo o conteúdo antes da prova</p>
        </div>
        <div class="info-grid">
            <div class="info-item">
                <strong>Total de Conteúdos:</strong>
                <span><?php echo $info_estudo['total_conteudos']; ?></span>
            </div>
            <div class="info-item">
                <strong>Dias até a Prova:</strong>
                <span><?php echo $info_estudo['dias_ate_prova']; ?> dias</span>
            </div>
            <div class="info-item">
                <strong>Dias Úteis de Estudo:</strong>
                <span><?php echo $info_estudo['dias_uteis']; ?> dias</span>
            </div>
            <div class="info-item">
                <strong>Cards por Dia:</strong>
                <span><?php echo $info_estudo['cards_por_dia']; ?> cards</span>
            </div>
            <div class="info-item">
                <strong>Semanas Necessárias:</strong>
                <span><?php echo $info_estudo['semanas_necessarias']; ?> semanas</span>
            </div>
        </div>
        <?php if ($tempo_apertado): ?>
            <div class="alert-message">
                <i class="fas fa-exclamation-triangle"></i>
                <span>Atenção: O prazo está apertado! Considere aumentar as horas de estudo diárias.</span>
            </div>
        <?php endif; ?>

        <?php if ($dias_para_prova <= 7): ?>
            <div class="alert-message warning">
                <i class="fas fa-exclamation-circle"></i>
                <span>MODO INTENSIVO ATIVADO! Prioridade das matérias dobrada.</span>
            </div>
        <?php endif; ?>

        <div class="actions">
            <a href="configurar_prova.php" class="btn-config">Ajustar Configurações</a>
        </div>
    </div>
</div>

<!-- Barra de Progresso -->
<div class="progress-container">
    <div class="progress-bar" id="progress-bar"></div>
</div>
<div class="progress-text" id="progress-text">Progresso: 0%</div>

<!-- Container de Revisões -->
<div class="revisoes-container">
    <h2>Revisões para Hoje</h2>
    <div id="revisoes-lista" class="revisoes-lista">
        <!-- As revisões serão inseridas aqui via JavaScript -->
    </div>
</div>

<!-- Dashboard de Estatísticas -->
<div class="dashboard">
    <h2>Estatísticas por Matéria</h2>
    <div class="stats-container" id="stats-container">
        <!-- As estatísticas serão inseridas aqui via JavaScript -->
    </div>
</div>


<div class="calendario-nav-tipo">
    <button class="btn-view" data-view="mes" id="viewMes">Mês</button>
    <button class="btn-view" data-view="semana" id="viewSemana">Semana</button>
</div>

<div class="calendario-wrapper">
    <div class="calendario-header">
        <div class="calendario-nav">
            <button onclick="mudarMes(-1)">&lt; Anterior</button>
            <span class="calendario-mes">
                <?php echo strftime('%B %Y', $data_atual->getTimestamp()); ?>
            </span>
            <button onclick="mudarMes(1)">Próximo &gt;</button>
        </div>
    </div>

    <?php
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && $_SERVER['HTTP_X_REQUESTED_WITH'] === 'XMLHttpRequest') {
        header('Content-Type: text/html');
        $viewMode = $_GET['view'] ?? 'mes';
        gerarCalendario($mes_selecionado, $ano_selecionado, $conteudosPorDia, $viewMode);
        exit;
    } else {
        gerarCalendario($mes_selecionado, $ano_selecionado, $conteudosPorDia);
    }
    ?>
</div>

<script>

    let viewMode = 'mes';
    let semanaAtual = 0;

    function toggleView(mode) {
        viewMode = mode;
        document.querySelectorAll('.btn-view').forEach(btn => {
            btn.classList.toggle('active', btn.dataset.view === mode);
        });
        atualizarCalendario();
    }

    function atualizarCalendario() {
        const container = document.querySelector('.calendario-grid');
        if (viewMode === 'semana') {
            container.style.gridTemplateColumns = 'repeat(6, 1fr)'; // Remove domingo
            // Mostrar apenas a semana atual
            Array.from(container.children).forEach((child, index) => {
                const isSemanaAtual = Math.floor(index / 7) === semanaAtual;
                child.style.display = isSemanaAtual ? '' : 'none';
            });
        } else {
            container.style.gridTemplateColumns = 'repeat(7, 1fr)';
            Array.from(container.children).forEach(child => {
                child.style.display = '';
            });
        }
    }

    // Adicione os event listeners
    document.getElementById('viewMes').addEventListener('click', () => toggleView('mes'));
    document.getElementById('viewSemana').addEventListener('click', () => toggleView('semana'));

    // Adicione botões de navegação para semana
    document.querySelector('.calendario-nav').innerHTML += `
    <button onclick="mudarSemana(-1)" id="prevSemana" style="display:none">&lt; Semana</button>
    <button onclick="mudarSemana(1)" id="nextSemana" style="display:none">Semana &gt;</button>
`;

    function mudarSemana(direcao) {
        const totalSemanas = Math.ceil(document.querySelectorAll('.dia-celula').length / 7);
        semanaAtual = Math.max(0, Math.min(semanaAtual + direcao, totalSemanas - 1));
        atualizarCalendario();
    }

    let mesAtual = new Date();

    async function mudarMes(direcao) {
        mesAtual.setMonth(mesAtual.getMonth() + direcao);
        const mes = mesAtual.getMonth() + 1;
        const ano = mesAtual.getFullYear();

        try {
            // Atualiza URL sem recarregar
            history.pushState({}, '', `?mes=${mes}&ano=${ano}`);

            // Atualiza título do mês
            document.querySelector('.calendario-mes').textContent =
                mesAtual.toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' });

            // Busca novo calendário via AJAX
            const response = await fetch(`?mes=${mes}&ano=${ano}`);
            const html = await response.text();

            // Extrai o calendário do HTML retornado
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const novoCalendario = doc.querySelector('.calendario-grid');

            // Atualiza o calendário
            document.querySelector('.calendario-grid').outerHTML = novoCalendario.outerHTML;

        } catch (error) {
            console.error('Erro ao atualizar calendário:', error);
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        const urlParams = new URLSearchParams(window.location.search);
        const mes = urlParams.get('mes');
        const ano = urlParams.get('ano');

        if (mes && ano) {
            mesAtual = new Date(ano, mes - 1);
        }
    });
    // Função assíncrona para carregar revisões
    async function carregarRevisoes() {
        try {
            const response = await fetch('get_revisoes.php');
            const dados = await response.json();
            //console.log('Dados recebidos:', dados); // Para debug

            const container = document.getElementById('revisoes-lista');

            if (!container) {
                console.error('Container de revisões não encontrado');
                return;
            }

            if (dados && Array.isArray(dados) && dados.length > 0) {
                container.innerHTML = dados.map(revisao => `
                <div class="card-revisao" style="border-left: 4px solid ${revisao.cor}">
                    <h4><strong>${revisao.materia}</strong></h4>
                    ${revisao.descricao_capitulo_principal ?
                    `<p class="capitulo-principal">*${revisao.descricao_capitulo_principal}*</p>` : ''}
                    <p class="subcapitulo"><strong>${revisao.capitulo}</strong> ${revisao.conteudo}</p>
                    <div class="avaliacao-confianca">
                        <p>Como está seu conhecimento neste conteúdo?</p>
                        <div class="botoes-confianca">
                            <button onclick="avaliarRevisao(${revisao.id}, 1)" class="btn-confianca">
                                😟 Não lembro
                            </button>
                            <button onclick="avaliarRevisao(${revisao.id}, 2)" class="btn-confianca">
                                🙁 Lembro pouco
                            </button>
                            <button onclick="avaliarRevisao(${revisao.id}, 3)" class="btn-confianca">
                                😐 Razoável
                            </button>
                            <button onclick="avaliarRevisao(${revisao.id}, 4)" class="btn-confianca">
                                🙂 Lembro bem
                            </button>
                            <button onclick="avaliarRevisao(${revisao.id}, 5)" class="btn-confianca">
                                😄 Domino
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
            } else {
                container.innerHTML = `
                <div class="sem-revisoes">
                    <p>Não há revisões programadas para hoje! 🎉</p>
                </div>
            `;
            }
        } catch (error) {
            console.error('Erro ao carregar revisões:', error);
            container.innerHTML = `
            <div class="erro-revisoes">
                <p>Erro ao carregar revisões: ${error.message}</p>
            </div>
        `;
        }
    }

    // Função assíncrona para avaliar revisão
    async function avaliarRevisao(revisaoId, confianca) {
        try {
            const response = await fetch('avaliar_revisao.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    revisao_id: revisaoId,
                    confianca: confianca
                })
            });

            const data = await response.json();

            if (data.success) {
                await carregarRevisoes(); // Recarrega as revisões após avaliação
                mostrarRecompensa('✨ Revisão avaliada com sucesso!');
            }
        } catch (error) {
            console.error('Erro ao avaliar revisão:', error);
            mostrarRecompensa('❌ Erro ao avaliar revisão. Tente novamente.');
        }
    }

    // Inicialização
    const totalConteudos = <?= $totalConteudos ?>;
    let conteudosEstudados = document.querySelectorAll('.conteudo-checkbox:checked').length;

    // Função para verificar cor clara/escura
    function isLightColor(hex) {
        const hex_clean = hex.replace('#', '');
        const r = parseInt(hex_clean.substr(0, 2), 16);
        const g = parseInt(hex_clean.substr(2, 2), 16);
        const b = parseInt(hex_clean.substr(4, 2), 16);
        const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
        return yiq >= 128;
    }

    // Função para criar card de estatística
    function criarCardEstatistica(stat) {
        const isLight = isLightColor(stat.cor);
        return `
        <div class="stat-card" data-materia="${stat.materia_nome}" style="background-color: ${stat.cor}">
            <h3 class="${isLight ? 'dark-text' : 'light-text'}">${stat.materia_nome}</h3>
            <div class="stat-progress">
                <div class="progress-bar" style="width: ${stat.percentual}%"></div>
            </div>
            <p class="${isLight ? 'dark-text' : 'light-text'}">
                Progresso: ${stat.percentual}%
                <br>
                (${stat.itens_estudados}/${stat.total_itens} itens)
            </p>
        </div>
    `;
    }

    // Função para atualizar estatísticas
    async function atualizarEstatisticas() {
        try {
            const response = await fetch('get_estatisticas.php');
            const estatisticas = await response.json();

            const container = document.getElementById('stats-container');
            container.innerHTML = estatisticas.map(stat => criarCardEstatistica(stat)).join('');
        } catch (error) {
            console.error('Erro ao atualizar estatísticas:', error);
        }
    }

    // Função para atualizar progresso
    function atualizarProgresso() {
        const progresso = (conteudosEstudados / totalConteudos) * 100;
        document.getElementById('progress-bar').style.width = progresso + '%';
        document.getElementById('progress-text').textContent = `Progresso: ${progresso.toFixed(2)}%`;

        document.querySelectorAll('.semana').forEach((semana, index) => {
            const checkboxesSemana = semana.querySelectorAll('.conteudo-checkbox');
            const estudadosSemana = Array.from(checkboxesSemana).filter(cb => cb.checked).length;
            const totalSemana = checkboxesSemana.length;

            let contadorSemana = semana.querySelector('.semana-counter');
            if (!contadorSemana) {
                contadorSemana = document.createElement('span');
                contadorSemana.className = 'semana-counter';
                semana.querySelector('.semana-title').appendChild(contadorSemana);
            }
            contadorSemana.textContent = `${estudadosSemana}/${totalSemana}`;

            if (estudadosSemana === totalSemana && totalSemana > 0) {
                semana.classList.add('semana-completa');
            } else {
                semana.classList.remove('semana-completa');
            }
        });
    }

    // Função para atualizar status de estudo
    async function atualizarStatusEstudo(conteudoId, estudado) {
        try {
            const formData = new FormData();
            formData.append('conteudo_id', conteudoId);
            formData.append('status', estudado);

            const response = await Promise.race([
                fetch('atualizar_status.php', {
                    method: 'POST',
                    body: formData
                }),
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('Tempo limite excedido')), 5000)
                )
            ]);

            const data = await response.json();

            if (!data.success) {
                throw new Error(data.error || 'Erro ao atualizar status');
            }

            return data.status === 'Estudado';
        } catch (error) {
            console.error('Erro:', error);
            let mensagem = 'Erro ao salvar o progresso. ';
            if (error.message === 'Tempo limite excedido') {
                mensagem += 'Conexão muito lenta. ';
            } else if (!navigator.onLine) {
                mensagem += 'Você está offline. ';
            }
            mensagem += 'Por favor, tente novamente.';

            alert(mensagem);
            return null;
        }
    }

    // Função para mostrar recompensas
    function mostrarRecompensa(mensagem) {
        const recompensa = document.createElement('div');
        recompensa.className = 'recompensa-notificacao';
        recompensa.innerHTML = mensagem;
        document.body.appendChild(recompensa);

        setTimeout(() => {
            recompensa.classList.add('mostrar');
            setTimeout(() => {
                recompensa.classList.remove('mostrar');
                setTimeout(() => recompensa.remove(), 300);
            }, 3000);
        }, 100);
    }

    // Sistema de metas
    const metas = {
        diaria: {
            meta: <?= $cardsPerDay ?>,
            recompensa: '🌟 Meta diária alcançada!'
        },
        semanal: {
            meta: <?= $cardsPerDay * 6 ?>,
            recompensa: '🏆 Meta semanal alcançada! Continue assim!'
        },
        materia: {
            meta: 100,
            recompensa: '📚 Matéria dominada! Excelente trabalho!'
        }
    };

    // Função para verificar metas
    // Variável para controlar se a meta do dia já foi mostrada
    let metaDiariaJaMostrada = {};

    function verificarMetas() {
        // Verifica metas diárias - para o dia que foi marcado
        const diasAtivos = document.querySelectorAll('.semana-content.active .cronograma .dia');
        diasAtivos.forEach((dia, index) => {
            const diaId = `${document.querySelector('.semana-content.active').dataset.semana}-${index}`;

            const checkboxesDia = dia.querySelectorAll('.conteudo-checkbox');
            const estudadosHoje = Array.from(checkboxesDia).filter(cb => cb.checked).length;

            // Inicializa o controle para este dia se não existir
            if (metaDiariaJaMostrada[diaId] === undefined) {
                metaDiariaJaMostrada[diaId] = false;
            }

            // Mostra mensagem apenas quando atingir exatamente o número de cards E não tiver mostrado ainda
            if (estudadosHoje === <?= $cardsPerDay ?> && !metaDiariaJaMostrada[diaId]) {
                const diaDaSemana = dia.querySelector('h3').textContent;
                mostrarRecompensa(`🌟 Meta diária alcançada para ${diaDaSemana}!`);
                metaDiariaJaMostrada[diaId] = true; // Marca que já mostrou a mensagem
            } else if (estudadosHoje < <?= $cardsPerDay ?>) {
                metaDiariaJaMostrada[diaId] = false; // Reseta o controle se desmarcar cards
            }
        });

        // Verifica metas semanais (mantém a mesma lógica)
        const semanaAtual = document.querySelector('.semana-content.active');
        if (semanaAtual) {
            const checkboxesSemana = semanaAtual.querySelectorAll('.conteudo-checkbox');
            const estudadosSemana = Array.from(checkboxesSemana).filter(cb => cb.checked).length;
            const totalSemana = checkboxesSemana.length;

            if (estudadosSemana === totalSemana) {
                mostrarRecompensa('🏆 Meta semanal alcançada! Continue assim!');
            }
        }
    }

    // Adicione um identificador para cada semana no HTML
    document.querySelectorAll('.semana').forEach((semana, semanaIndex) => {
        semana.querySelector('.semana-content').dataset.semana = semanaIndex;
    });

    // Quando mudar de semana, mantém o histórico das metas diárias
    document.querySelectorAll('.semana-title').forEach((title, semanaIndex) => {
        title.addEventListener('click', function() {
            // O histórico é mantido, não precisa resetar metaDiariaJaMostrada
            // Apenas atualiza a semana ativa
        });
    });

    // Event Listeners
    document.addEventListener('DOMContentLoaded', function() {
        carregarRevisoes();
        atualizarEstatisticas();
        atualizarProgresso();

        // Recuperar última semana aberta
        const ultimaSemanaAberta = localStorage.getItem('ultimaSemanaAberta');
        if (ultimaSemanaAberta) {
            const semanas = document.querySelectorAll('.semana');
            const semanaParaAbrir = semanas[parseInt(ultimaSemanaAberta)];
            if (semanaParaAbrir) {
                semanaParaAbrir.querySelector('.semana-content').classList.add('active');
                semanaParaAbrir.querySelector('.semana-title').classList.remove('collapsed');
            }
        }
    });

    // Gerenciamento dos checkboxes
    document.querySelectorAll('.conteudo-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', async function() {
            const card = this.closest('.conteudo-item');
            const estudado = this.checked;
            const conteudoId = this.dataset.id;

            card.classList.add('updating');
            this.disabled = true;

            const statusAtualizado = await atualizarStatusEstudo(conteudoId, estudado);

            card.classList.remove('updating');
            this.disabled = false;

            if (statusAtualizado !== null) {
                this.checked = statusAtualizado;
                if (statusAtualizado) {
                    conteudosEstudados++;
                    card.classList.add('completed');
                } else {
                    conteudosEstudados--;
                    card.classList.remove('completed');
                }
                atualizarProgresso();
                await atualizarEstatisticas();
                verificarMetas();
            } else {
                this.checked = !estudado;
            }
        });
    });

    // Gerenciamento do acordeão
    document.querySelectorAll('.semana-title').forEach((title, index) => {
        title.addEventListener('click', function() {
            const content = this.nextElementSibling;
            const isActive = content.classList.contains('active');

            document.querySelectorAll('.semana-content.active').forEach(panel => {
                panel.classList.remove('active');
                panel.previousElementSibling.classList.add('collapsed');
            });

            if (!isActive) {
                content.classList.add('active');
                this.classList.remove('collapsed');
                localStorage.setItem('ultimaSemanaAberta', index.toString());
            }
        });
    });
</script>


</body>
</html>
