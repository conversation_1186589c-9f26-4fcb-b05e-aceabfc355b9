<?php
session_start();
header('Content-Type: application/json');
// Headers de segurança HTTP
header('X-Frame-Options: DENY');
header('X-Content-Type-Options: nosniff');
header('Referrer-Policy: no-referrer');
header('Permissions-Policy: interest-cohort=()');
header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');

// Inclui o arquivo de configuração do banco de dados
require_once __DIR__ . '/config/database.php';

// Recebe e decodifica os dados JSON
$dados = json_decode(file_get_contents('php://input'), true);

if (!$dados || !isset($dados['pagina_url'])) {
    http_response_code(400);
    echo json_encode(['erro' => 'URL da página não fornecida']);
    exit;
}
// Sanitização da entrada
$pagina_url = filter_var($dados['pagina_url'], FILTER_SANITIZE_URL);
if (empty($pagina_url)) {
    http_response_code(400);
    echo json_encode(['erro' => 'URL da página inválida']);
    exit;
}

try {
    $pdo = getDbConnection();

    // Verifica se o usuário está logado
    if (!isset($_SESSION['idusuario'])) {
        http_response_code(401);
        echo json_encode(['erro' => 'Usuário não autenticado']);
        exit;
    }

    // Usa o ID do usuário da sessão
    $usuario_id = $_SESSION['idusuario'];

    $stmt = $pdo->prepare("
        SELECT
            elemento_id,
            tipo_marcacao,
            texto_marcado,
            posicao_inicio,
            posicao_fim
        FROM marcacoes
        WHERE pagina_url = :pagina_url
        AND usuario_id = :usuario_id
        ORDER BY posicao_inicio ASC
    ");

    $stmt->execute([
        ':pagina_url' => $dados['pagina_url'],
        ':usuario_id' => $usuario_id
    ]);

    $marcacoes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode($marcacoes);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['erro' => 'Erro ao carregar marcações: ' . $e->getMessage()]);
}



