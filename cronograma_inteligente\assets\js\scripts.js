    //scripts.js
    // Cache para hierarquia dos cards
const hierarquiaCache = {};

// Função para armazenar a hierarquia do conteúdo no cache
function cachearHierarquia(id, data) {
    hierarquiaCache[id] = data;
    //console.log('Hierarquia cacheada para ID', id, data);
}

// Função para obter hierarquia do cache
function obterHierarquiaCache(id) {
    return hierarquiaCache[id];
}
    // Função para mostrar o calendário de replanejamento
// Modifica a função mostrarCalendarioReplanejamento
function mostrarCalendarioReplanejamento() {
    const container = document.querySelector('.replanejar-container');
    if (!container) return;

    // Criar input de data
    const dateInput = document.createElement('input');
    dateInput.type = 'date';
    dateInput.className = 'datepicker';
    
    // Define data mínima como hoje com horário zerado
    const hoje = new Date();
    hoje.setHours(0, 0, 0, 0);
    dateInput.min = hoje.toISOString().split('T')[0];
    
    // Define valor padrão como hoje
    dateInput.valueAsDate = hoje;
    
    // Define data máxima como a data da prova
    // Assumindo que você tem acesso à data da prova via PHP/JavaScript
    if (window.PLANO_CONFIG && window.PLANO_CONFIG.data_prova) {
        dateInput.max = window.PLANO_CONFIG.data_prova;
    }
    
    // Criar botão de confirmação
    const confirmBtn = document.createElement('button');
    confirmBtn.className = 'btn-confirmar-replanejamento';
    confirmBtn.textContent = 'Confirmar Replanejamento';
    
    // Limpar container e adicionar elementos
    container.innerHTML = '';
    container.appendChild(dateInput);
    container.appendChild(confirmBtn);
    container.classList.add('visible');

    // Adicionar evento de confirmação
    confirmBtn.addEventListener('click', async () => {
        const selectedDate = dateInput.value;
        if (!selectedDate) {
            mostrarRecompensa('❌ Por favor, selecione uma data válida.');
            return;
        }

        // Validação adicional no frontend
        const dataEscolhida = new Date(selectedDate);
        const dataProva = new Date(window.PLANO_CONFIG.data_prova);
        
        if (dataEscolhida > dataProva) {
            mostrarRecompensa('❌ A nova data não pode ser posterior à data da prova.');
            return;
        }

        try {
            const response = await fetch('replanejar_pendentes.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    nova_data_inicio: selectedDate
                })
            });

            const data = await response.json();
            
            if (data.success) {
                mostrarRecompensa('📅 Replanejamento realizado com sucesso!');
                await carregarRevisoes();
                renderizarCardsAtrasados(cards_atrasados);
                container.classList.remove('visible');
            } else {
                throw new Error(data.error || 'Erro ao replanejar');
            }
        } catch (error) {
            console.error('Erro ao replanejar:', error);
            mostrarRecompensa('❌ ' + (error.message || 'Erro ao replanejar. Tente novamente.'));
        }
    });
}

    // Adicionar evento ao botão de replanejamento
    document.addEventListener('DOMContentLoaded', function() {
        atualizarTodosDias();
        const replanejarBtn = document.querySelector('.btn-replanejar');
        if (replanejarBtn) {
            replanejarBtn.addEventListener('click', mostrarCalendarioReplanejamento);
        }
    });

    function scrollToSection(section) {
        const sections = {
            'info': '.plano-info',
            'dashboard': '.dashboard-motivacional', 
            'pendentes': '.cards-atrasados',
            'stats': '.dashboard',
            'revisoes': '.revisoes-container',
            'plano': '.semana'
        };

        const element = document.querySelector(sections[section]);
        if (element) {
            const offset = 120;
            const elementPosition = element.getBoundingClientRect().top;
            const offsetPosition = elementPosition + window.pageYOffset - offset;

            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
            });

            updateActiveButton(section);
        }
    }

    // Função separada para atualizar botão ativo
    function updateActiveButton(section) {
        document.querySelectorAll('.nav-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[onclick="scrollToSection('${section}')"]`)?.classList.add('active');
    }

    // Observer modificado para ser mais preciso
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting && entry.intersectionRatio > 0.5) {
                const section = entry.target.getAttribute('data-section');
                if (section) {
                    updateActiveButton(section);
                }
            }
        });
    }, {
        threshold: [0.5], // Aumentar threshold para 0.5 (50% visível)
        rootMargin: '-120px 0px -40% 0px' // Ajustar margens para melhor detecção
    });


    // Função assíncrona para carregar revisões
// Função assíncrona para carregar revisões com hierarquia completa
async function carregarRevisoes() {
    try {
        const response = await fetch('get_revisoes.php');
        const dados = await response.json();

        const container = document.getElementById('revisoes-lista');
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
             
        if (!container) {
            console.error('Container de revisões não encontrado');
            return;
        }

        if (dados && Array.isArray(dados) && dados.length > 0) {
            const htmlRevisoes = dados.map(revisao => {
                // Inicializa o conteúdo da hierarquia
                let hierarquiaHTML = '';
                
                // Nível 1 (Principal)
                if (revisao.descricao_capitulo_principal) {
                    hierarquiaHTML += `<p class="hierarchy-level-1">${revisao.descricao_capitulo_principal}</p>`;
                }
                
                // Nível 2 (Secundário)
                if (revisao.descricao_capitulo_secundario) {
                    hierarquiaHTML += `<p class="hierarchy-level-2">${revisao.descricao_capitulo_secundario}</p>`;
                }
                
                // Nível 3 (Terciário)
                if (revisao.descricao_capitulo_terciario) {
                    hierarquiaHTML += `<p class="hierarchy-level-3">${revisao.descricao_capitulo_terciario}</p>`;
                }
                
                // Nível 4 (Quaternário)
                if (revisao.descricao_capitulo_quaternario) {
                    hierarquiaHTML += `<p class="hierarchy-level-4">${revisao.descricao_capitulo_quaternario}</p>`;
                }
                
                // Nível 5 (Quinário)
                if (revisao.descricao_capitulo_quinario) {
                    hierarquiaHTML += `<p class="hierarchy-level-5">${revisao.descricao_capitulo_quinario}</p>`;
                }
                
                // Nível 6 (Sexto)
                if (revisao.descricao_capitulo_sexto) {
                    hierarquiaHTML += `<p class="hierarchy-level-6">${revisao.descricao_capitulo_sexto}</p>`;
                }
                
                // Nível 7 (Sétimo)
                if (revisao.descricao_capitulo_setimo) {
                    hierarquiaHTML += `<p class="hierarchy-level-7">${revisao.descricao_capitulo_setimo}</p>`;
                }

                return `
                <div class="card-revisao" style="border-left: 4px solid ${revisao.cor}">
                    <div class="card-header">
                        <h4>${revisao.materia}</h4>
                    </div>

                    <div class="conteudo-hierarchy">
                        ${hierarquiaHTML}
                        <p class="current-topic"><strong>${revisao.capitulo}</strong> ${revisao.conteudo}</p>
                    </div>

                    <div class="avaliacao-opcoes">
                        <div class="avaliacao-section">
                            <h5>
                                <i class="fas fa-star"></i>
                                Avaliar Conhecimento
                            </h5>
                            <div class="botoes-confianca">
                                <button onclick="avaliarRevisao(${revisao.id}, 1)" class="btn-confianca">
                                    😟 Não lembro
                                </button>
                                <button onclick="avaliarRevisao(${revisao.id}, 2)" class="btn-confianca">
                                    🙁 Lembro pouco
                                </button>
                                <button onclick="avaliarRevisao(${revisao.id}, 3)" class="btn-confianca">
                                    😐 Razoável
                                </button>
                                <button onclick="avaliarRevisao(${revisao.id}, 4)" class="btn-confianca">
                                    🙂 Lembro bem
                                </button>
                                <button onclick="avaliarRevisao(${revisao.id}, 5)" class="btn-confianca">
                                    😄 Domino
                                </button>
                            </div>
                        </div>

                        <div class="ignorar-section">
                            <h5>
                                <i class="fas fa-clock"></i>
                                Opções de Adiamento
                            </h5>
                            <div class="botoes-ignorar">
                                <button onclick="executarIgnorar(${revisao.id}, 'temporario')" class="btn-ignorar-opcao">
                                    <i class="fas fa-clock"></i>
                                    <span>Adiar Revisão</span>
                                    <small>Volta em alguns dias</small>
                                </button>
                                <button onclick="executarIgnorar(${revisao.id}, 'permanente')" class="btn-ignorar-opcao">
                                    <i class="fas fa-ban"></i>
                                    <span>Ignorar Permanentemente</span>
                                    <small>Não revisa mais este conteúdo</small>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                `;
            }).join('');

            container.innerHTML = htmlRevisoes;
        } else {
            container.innerHTML = `
                <div class="sem-revisoes">
                    <i class="fas fa-check-circle"></i>
                    <p>Não há revisões programadas para hoje! 🎉</p>
                </div>
            `;
        }
        // Adicionar esta linha no final da função
        atualizarContadoresRevisoes();

    } catch (error) {
        console.error('Erro detalhado ao carregar revisões:', error);
        const container = document.getElementById('revisoes-lista');
        if (container) {
            container.innerHTML = `
                <div class="erro-revisoes">
                    <i class="fas fa-exclamation-circle"></i>
                    <p>Erro ao carregar revisões: ${error.message}</p>
                    <button onclick="carregarRevisoes()" class="btn-retry">
                        <i class="fas fa-sync"></i> Tentar novamente
                    </button>
                </div>
            `;
        }
    }
}

// Aplicar os mesmos estilos para a hierarquia nos cards de revisão
function adicionarEstiloHierarquiaRevisoes() {
    // Verifica se o estilo já existe
    if (document.querySelector('style[data-id="hierarquia-cards-revisao"]')) {
        return;
    }
    
    const style = document.createElement('style');
    style.setAttribute('data-id', 'hierarquia-cards-revisao');
    style.textContent = `
        .card-revisao .conteudo-hierarchy {
            display: flex;
            flex-direction: column;
            gap: 5px;
            margin-bottom: 15px;
        }
        
        .card-revisao .hierarchy-level-1 {
            font-weight: 600;
            font-size: 0.95rem;
            margin: 0;
        }
        
        .card-revisao .hierarchy-level-2 {
            font-weight: 500;
            font-size: 0.9rem;
            margin: 0;
            padding-left: 10px;
        }
        
        .card-revisao .hierarchy-level-3 {
            font-size: 0.85rem;
            margin: 0;
            padding-left: 20px;
        }
        
        .card-revisao .hierarchy-level-4,
        .card-revisao .hierarchy-level-5,
        .card-revisao .hierarchy-level-6,
        .card-revisao .hierarchy-level-7 {
            font-size: 0.8rem;
            margin: 0;
            padding-left: 30px;
        }
        
        .card-revisao .current-topic {
            font-size: 0.9rem;
            margin: 0;
            color: var(--text-color);
            background-color: rgba(0, 0, 0, 0.05);
            padding: 5px;
            border-radius: 4px;
        }
        
        /* Esconder os elementos antigos se existirem */
        .card-revisao .capitulo-principal {
            display: none;
        }
        
        .card-revisao .subcapitulo {
            display: none;
        }
    `;
    
    document.head.appendChild(style);
}

    // Função assíncrona para avaliar revisão
    async function avaliarRevisao(revisaoId, confianca) {
        try {
            const response = await fetch('avaliar_revisao.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    revisao_id: revisaoId,
                    confianca: confianca
                })
            });

            const data = await response.json();

            if (data.success) {
                await carregarRevisoes(); // Recarrega as revisões após avaliação
                mostrarRecompensa('✨ Revisão avaliada com sucesso!');
            }
        } catch (error) {
            console.error('Erro ao avaliar revisão:', error);
            mostrarRecompensa('❌ Erro ao avaliar revisão. Tente novamente.');
        }
    }

// Inicialização
// Inicialização
let totalConteudos = 0;
let conteudosEstudados = 0;
let cardsPerDay = 0;
let cards_atrasados = [];
let metas = {};

// Aguarda o carregamento da página
document.addEventListener('DOMContentLoaded', function() {
    if (window.PLANO_CONFIG) {
        totalConteudos = window.PLANO_CONFIG.totalConteudos;
        conteudosEstudados = document.querySelectorAll('.conteudo-checkbox:checked').length;
        cardsPerDay = window.PLANO_CONFIG.cardsPerDay;
        cards_atrasados = window.PLANO_CONFIG.cards_atrasados;
        metas = window.PLANO_CONFIG.metas;
    } else {
        console.error('PLANO_CONFIG não foi definido');
    }
});

    // Função para verificar cor clara/escura
    function isLightColor(hex) {
        const hex_clean = hex.replace('#', '');
        const r = parseInt(hex_clean.substr(0, 2), 16);
        const g = parseInt(hex_clean.substr(2, 2), 16);
        const b = parseInt(hex_clean.substr(4, 2), 16);
        const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000;
        return yiq >= 128;
    }

    // Função para criar card de estatística
    function criarCardEstatistica(stat) {
        const isLight = isLightColor(stat.cor);
        return `
        <div class="stat-card" data-materia="${stat.materia_nome}" style="background-color: ${stat.cor}">
            <h3 class="${isLight ? 'dark-text' : 'light-text'}">${stat.materia_nome}</h3>
            <div class="stat-progress">
                <div class="progress-bar" style="width: ${stat.percentual}%"></div>
            </div>
            <p class="${isLight ? 'dark-text' : 'light-text'}">
                Progresso: ${stat.percentual}%
                <br>
                (${stat.itens_estudados}/${stat.total_itens} itens)
            </p>
        </div>
    `;
    }

    // Função para atualizar estatísticas
    async function atualizarEstatisticas() {
        try {
            const response = await fetch('get_estatisticas.php');
            const estatisticas = await response.json();

            const container = document.getElementById('stats-container');
            container.innerHTML = estatisticas.map(stat => criarCardEstatistica(stat)).join('');
        } catch (error) {
            console.error('Erro ao atualizar estatísticas:', error);
        }
    }

    // Função para atualizar progresso
    function atualizarProgresso() {
        const progresso = (conteudosEstudados / totalConteudos) * 100;
        document.getElementById('progress-bar').style.width = progresso + '%';
        document.getElementById('progress-text').textContent = `Progresso: ${progresso.toFixed(2)}%`;


            // Agrupa conteúdos por matéria
    const materias = {};
    document.querySelectorAll('.conteudo-item').forEach(item => {
        const materia = item.dataset.materia;
        if (!materias[materia]) {
            materias[materia] = {
                total: 0,
                estudados: 0,
                element: item.closest('.conteudos-grid')
            };
        }
        materias[materia].total++;
        if (item.querySelector('.conteudo-checkbox').checked) {
            materias[materia].estudados++;
        }
    });

// Verifica progresso de cada matéria
Object.entries(materias).forEach(([materia, dados]) => {
    const progressoMateria = (dados.estudados / dados.total) * 100;
    verificarProgressoMateria(dados.element, progressoMateria, materia);
});

        document.querySelectorAll('.semana').forEach((semana, index) => {
            const checkboxesSemana = semana.querySelectorAll('.conteudo-checkbox');
            const estudadosSemana = Array.from(checkboxesSemana).filter(cb => cb.checked).length;
            const totalSemana = checkboxesSemana.length;
            
    
            // Atualiza contador da semana
            let contadorSemana = semana.querySelector('.semana-counter');
            if (!contadorSemana) {
                contadorSemana = document.createElement('span');
                contadorSemana.className = 'semana-counter';
                semana.querySelector('.semana-title').appendChild(contadorSemana);
            }
            contadorSemana.textContent = `${estudadosSemana}/${totalSemana}`;
    
            // Atualiza contadores por dia
            semana.querySelectorAll('.dia-horizontal').forEach(dia => {
                const checkboxesDia = dia.querySelectorAll('.conteudo-checkbox');
                const estudadosDia = Array.from(checkboxesDia).filter(cb => cb.checked).length;
                const totalDia = checkboxesDia.length;
    
                let contadorDia = dia.querySelector('.conteudos-counter');
                if (contadorDia) {
                    contadorDia.textContent = `${estudadosDia}/${totalDia} conteúdos`;
                }
            });
    
            // Adiciona classe quando a semana está completa
            if (estudadosSemana === totalSemana && totalSemana > 0) {
                semana.classList.add('semana-completa');
            } else {
                semana.classList.remove('semana-completa');
            }
        });
    }

    // Função para atualizar status de estudo
    // Função para atualizar status de estudo modificada
// Atualiza a função de atualizar status de estudo para incluir verificação de atrasos
// Função melhorada para transferir todos os dados hierárquicos
async function atualizarStatusEstudo(conteudoId, estudado) {
    try {
        const formData = new FormData();
        formData.append('conteudo_id', conteudoId);
        formData.append('status', estudado);
        
        // Adicionamos um novo parâmetro para indicar se a página está em um estado replanejado
        // Isso é importante para que o backend saiba como tratar estes cards
        formData.append('apos_replanejamento', true);

        const response = await fetch('atualizar_status.php', {
            method: 'POST',
            body: formData
        });

        const data = await response.json();

        if (!data.success) {
            throw new Error(data.error || 'Erro ao atualizar status');
        }

        if (data.success) {
            // Remove o card atrasado se existir
            if (estudado) {
                cards_atrasados = cards_atrasados.filter(card => card.id !== conteudoId);
            } else {
                // Primeiro verifica se o card já não existe na lista
                const cardJaExiste = cards_atrasados.some(card => card.id === conteudoId);
                
                if (!cardJaExiste) {
                    const cardElement = document.querySelector(`.conteudo-item [data-id="${conteudoId}"]`)?.closest('.conteudo-item');
                    if (cardElement) {
                        const dataString = cardElement.getAttribute('data-data-prevista');
                        const hoje = new Date();
                        hoje.setHours(0, 0, 0, 0);
                    
                        const [anoCard, mesCard, diaCard] = dataString.split('-');
                        const dataCard = new Date(anoCard, mesCard - 1, diaCard);
                        dataCard.setHours(0, 0, 0, 0);
                    
                        // Só adiciona se realmente estiver atrasado
                        if (dataCard < hoje) {
                            // Captura todos os dados hierárquicos disponíveis
                            const cardData = {
                                id: conteudoId,
                                materia: cardElement.getAttribute('data-materia'),
                                capitulo: cardElement.getAttribute('data-capitulo'),
                                descricao: cardElement.getAttribute('data-descricao'),
                                descricao_capitulo_principal: cardElement.getAttribute('data-descricao-capitulo-principal'),
                                descricao_capitulo_secundario: cardElement.getAttribute('data-descricao-capitulo-secundario'),
                                descricao_capitulo_terciario: cardElement.getAttribute('data-descricao-capitulo-terciario'),
                                descricao_capitulo_quaternario: cardElement.getAttribute('data-descricao-capitulo-quaternario'),
                                descricao_capitulo_quinario: cardElement.getAttribute('data-descricao-capitulo-quinario'),
                                descricao_capitulo_sexto: cardElement.getAttribute('data-descricao-capitulo-sexto'),
                                descricao_capitulo_setimo: cardElement.getAttribute('data-descricao-capitulo-setimo'),
                                cor: cardElement.getAttribute('data-cor'),
                                data_prevista: `${diaCard}/${mesCard}/${anoCard}`,
                                status_estudo: 'Pendente'
                            };
                            
                            // Adiciona apenas se não existir
                            if (!cards_atrasados.some(card => card.id === cardData.id)) {
                                cards_atrasados.push(cardData);
                            }
                        }
                    }
                }
            }
            
            // Reordena os cards por data antes de renderizar
            cards_atrasados.sort((a, b) => {
                const [diaA, mesA, anoA] = a.data_prevista.split('/').map(Number);
                const [diaB, mesB, anoB] = b.data_prevista.split('/').map(Number);
                const dataA = new Date(anoA, mesA - 1, diaA);
                const dataB = new Date(anoB, mesB - 1, diaB);
                return dataA - dataB;
            });
            
            // Remove qualquer duplicata antes de renderizar
            cards_atrasados = [...new Map(cards_atrasados.map(card => [card.id, card])).values()];
            
            renderizarCardsAtrasados(cards_atrasados);
        }

        return data.status === 'Estudado';
    } catch (error) {
        console.error('Erro:', error);
        console.error('Stack trace:', error.stack);
        alert('Erro ao salvar o progresso. Por favor, tente novamente.');
        return null;
    }
}

    // Função para mostrar recompensas
    function mostrarRecompensa(mensagem) {
        const recompensa = document.createElement('div');
        recompensa.className = 'recompensa-notificacao';
        recompensa.innerHTML = mensagem;
        document.body.appendChild(recompensa);

        setTimeout(() => {
            recompensa.classList.add('mostrar');
            setTimeout(() => {
                recompensa.classList.remove('mostrar');
                setTimeout(() => recompensa.remove(), 300);
            }, 3000);
        }, 100);
    }

    // Sistema de metas


    // Função para verificar metas
    // Variável para controlar se a meta do dia já foi mostrada
    let metaDiariaJaMostrada = {};

    function verificarMetas() {
        // Verifica metas diárias - para o dia que foi marcado
        const diasAtivos = document.querySelectorAll('.semana-content.active .cronograma .dia');
        diasAtivos.forEach((dia, index) => {
            const diaId = `${document.querySelector('.semana-content.active').dataset.semana}-${index}`;

            const checkboxesDia = dia.querySelectorAll('.conteudo-checkbox');
            const estudadosHoje = Array.from(checkboxesDia).filter(cb => cb.checked).length;

            // Inicializa o controle para este dia se não existir
            if (metaDiariaJaMostrada[diaId] === undefined) {
                metaDiariaJaMostrada[diaId] = false;
            }

           // Mostra mensagem apenas quando atingir exatamente o número de cards E não tiver mostrado ainda
if (estudadosHoje === cardsPerDay && !metaDiariaJaMostrada[diaId]) {
    const diaDaSemana = dia.querySelector('h3').textContent;
    mostrarRecompensa(`🌟 Meta diária alcançada para ${diaDaSemana}!`);
    metaDiariaJaMostrada[diaId] = true; // Marca que já mostrou a mensagem
} else if (estudadosHoje < cardsPerDay) {
    metaDiariaJaMostrada[diaId] = false; // Reseta o controle se desmarcar cards
}
        });

        // Verifica metas semanais (mantém a mesma lógica)
        const semanaAtual = document.querySelector('.semana-content.active');
        if (semanaAtual) {
            const checkboxesSemana = semanaAtual.querySelectorAll('.conteudo-checkbox');
            const estudadosSemana = Array.from(checkboxesSemana).filter(cb => cb.checked).length;
            const totalSemana = checkboxesSemana.length;

            if (estudadosSemana === totalSemana) {
                mostrarRecompensa('🏆 Meta semanal alcançada! Continue assim!');
            }
        }
    }

    // Adicione um identificador para cada semana no HTML
    document.querySelectorAll('.semana').forEach((semana, semanaIndex) => {
        semana.querySelector('.semana-content').dataset.semana = semanaIndex;
    });

    // Quando mudar de semana, mantém o histórico das metas diárias
    document.querySelectorAll('.semana-title').forEach((title, semanaIndex) => {
        title.addEventListener('click', function() {
            // O histórico é mantido, não precisa resetar metaDiariaJaMostrada
            // Apenas atualiza a semana ativa
        });
    });



    // Gerenciamento do acordeão
    document.querySelectorAll('.semana-title').forEach((title, index) => {
        title.addEventListener('click', function() {
            const content = this.nextElementSibling;
            const isActive = content.classList.contains('active');

            document.querySelectorAll('.semana-content.active').forEach(panel => {
                panel.classList.remove('active');
                panel.previousElementSibling.classList.add('collapsed');
            });

            if (!isActive) {
                content.classList.add('active');
                this.classList.remove('collapsed');
                localStorage.setItem('ultimaSemanaAberta', index.toString());
            }
        });
    });


    // Inicializar o array de cards atrasados
    

    // Função para converter data e evitar problemas de fuso horário
function parseDataPrevista(dataString) {
    const [dia, mes, ano] = dataString.split('/').map(Number);
    const data = new Date(ano, mes - 1, dia);
    data.setHours(12, 0, 0, 0); // Define meio-dia para evitar problemas com fuso horário
    return data;
}

// Função para verificar se um conteúdo está atrasado
function isConteudoAtrasado(dataPrevista) {
    const hoje = new Date();
    hoje.setHours(0, 0, 0, 0);

    // Converter a data prevista para o formato correto
    const [dia, mes, ano] = dataPrevista.split('/').map(Number);
    const data = new Date(ano, mes - 1, dia);
    data.setHours(0, 0, 0, 0);

    return data < hoje;
}

    // Função para atualizar o contador
    function atualizarContador() {
        const totalCards = document.querySelectorAll('.card-atrasado').length;
        const counter = document.getElementById('cards-counter');
        counter.textContent = `${totalCards} pendente${totalCards !== 1 ? 's' : ''}`;

        const container = document.getElementById('cards-atrasados-container');
        if (totalCards === 0) {
            container.innerHTML = ''; // Remove qualquer conteúdo
        }
    }



    // Função para remover card com animação
async function removerCard(cardElement) {
        cardElement.classList.add('removing');
        await new Promise(resolve => setTimeout(resolve, 300));
        cardElement.remove();
        atualizarContador();
}

// Funções para garantir a exibição correta do tópico pai

// Função melhorada para criar card atrasado com hierarquia completa
// Função corrigida para criar card atrasado com hierarquia completa
// 3. Ajuste a função criarCardAtrasado para lidar com ambos os formatos de dados
function criarCardAtrasado(card) {
    //console.log("Criando card atrasado com dados:", card);
    
    const [dia, mes, ano] = card.data_prevista.split('/').map(Number);
    const dataCard = new Date(ano, mes - 1, dia);
    dataCard.setHours(0, 0, 0, 0);

    const hoje = new Date();
    hoje.setHours(0, 0, 0, 0);

    const diffTime = Math.abs(hoje - dataCard);
    const diasAtraso = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    let statusAtraso;
    if (diasAtraso > 7) {
        statusAtraso = 'muito-atrasado';
    } else if (diasAtraso > 3) {
        statusAtraso = 'atrasado';
    } else {
        statusAtraso = 'pouco-atrasado';
    }

    // Inicializa o conteúdo da hierarquia
    let hierarquiaHTML = '';
    
    // Log para depuração de cada nível
   // console.log("Dados de hierarquia para card " + card.id + ":");
   // console.log("Principal:", card.descricao_capitulo_principal);
   // console.log("Secundário:", card.descricao_capitulo_secundario);
  //  console.log("Terciário:", card.descricao_capitulo_terciario);
    
    // Verifica e constrói cada nível da hierarquia apenas se existir e não for undefined/null/empty
    // Nível 1 (Principal)
    if (card.descricao_capitulo_principal) {
        hierarquiaHTML += `<p class="hierarchy-level-1">${card.descricao_capitulo_principal}</p>`;
    }
    
    // Nível 2 (Secundário)
    if (card.descricao_capitulo_secundario) {
        hierarquiaHTML += `<p class="hierarchy-level-2">${card.descricao_capitulo_secundario}</p>`;
    }
    
    // Nível 3 (Terciário)
    if (card.descricao_capitulo_terciario) {
        hierarquiaHTML += `<p class="hierarchy-level-3">${card.descricao_capitulo_terciario}</p>`;
    }
    
    // Nível 4 (Quaternário)
    if (card.descricao_capitulo_quaternario) {
        hierarquiaHTML += `<p class="hierarchy-level-4">${card.descricao_capitulo_quaternario}</p>`;
    }
    
    // Nível 5 (Quinário)
    if (card.descricao_capitulo_quinario) {
        hierarquiaHTML += `<p class="hierarchy-level-5">${card.descricao_capitulo_quinario}</p>`;
    }
    
    // Nível 6 (Sexto)
    if (card.descricao_capitulo_sexto) {
        hierarquiaHTML += `<p class="hierarchy-level-6">${card.descricao_capitulo_sexto}</p>`;
    }
    
    // Nível 7 (Sétimo)
    if (card.descricao_capitulo_setimo) {
        hierarquiaHTML += `<p class="hierarchy-level-7">${card.descricao_capitulo_setimo}</p>`;
    }

    // Usa materia ou materia_nome, qual estiver disponível
    const materiaNome = card.materia_nome || card.materia || 'Sem matéria';

    return `
        <div class="card-atrasado adding ${statusAtraso}"
             style="border-left: 4px solid ${card.cor}"
             data-id="${card.id}">
            <div class="card-atrasado-header">
                <div class="card-atrasado-materia">
                    <h4 style="color: ${card.cor}">${materiaNome}</h4>
                </div>
                <div class="card-atrasado-status">
                    ${diasAtraso === 0 ?
                    '<span class="atraso-hoje">Vence hoje</span>' :
                    `<span class="atraso-dias">${diasAtraso} dia${diasAtraso === 1 ? '' : 's'} de atraso</span>`}
                </div>
            </div>
            <div class="card-atrasado-content">
                <div class="conteudo-hierarchy">
                    ${hierarquiaHTML}
                    <p class="current-topic"><strong>${card.capitulo}</strong> ${card.descricao || ''}</p>
                </div>
                <div class="card-atrasado-data">
                    Data prevista: ${card.data_prevista}
                </div>
            </div>
            <div class="card-atrasado-actions">
                <label class="conteudo-label">
                    <input type="checkbox" class="conteudo-checkbox" data-id="${card.id}">
                    <span>Marcar como estudado</span>
                </label>
            </div>
        </div>
    `;
}

// Função melhorada para renderizar cards atrasados
function renderizarCardsAtrasados(cardsAtrasados) {
    const container = document.getElementById('cards-atrasados-container');
    const counter = document.getElementById('cards-counter');
    const headerSection = document.querySelector('.cards-atrasados-header');
    
    // Remove qualquer botão replanejar existente
    const botaoExistente = document.querySelector('.btn-replanejar');
    if (botaoExistente) {
        botaoExistente.remove();
    }
    
    // Remove duplicatas usando Set e map
    const cardsUnicos = [...new Map(cardsAtrasados.map(card => [card.id, card])).values()];
    
    // Filtrar cards realmente atrasados
    const cardsRealmente_atrasados = cardsUnicos.filter(card => {
        const [dia, mes, ano] = card.data_prevista.split('/').map(Number);
        const dataCard = new Date(ano, mes - 1, dia);
        const hoje = new Date();
        
        dataCard.setHours(0, 0, 0, 0);
        hoje.setHours(0, 0, 0, 0);
        
        return dataCard < hoje && card.status_estudo !== 'Estudado';
    });

    // Atualizar contador 
    if (counter) {
        counter.textContent = `${cardsRealmente_atrasados.length} pendente${cardsRealmente_atrasados.length !== 1 ? 's' : ''}`;
    }

    // Só adiciona o botão se tiver cards atrasados
    if (cardsRealmente_atrasados.length > 0 && headerSection) {
        const replanejarBtn = document.createElement('button');
        replanejarBtn.className = 'btn-replanejar';
        replanejarBtn.innerHTML = '<i class="fas fa-calendar-alt"></i> Replanejar Cronograma';
        replanejarBtn.onclick = replanejarPendentes;
        headerSection.appendChild(replanejarBtn);
    }

    // Verificar se há cards atrasados
    if (!cardsRealmente_atrasados || cardsRealmente_atrasados.length === 0) {
        container.innerHTML = `
            <div class="sem-cards-atrasados">
                <i class="fas fa-check-circle"></i>
                <p>Parabéns! Você está em dia com seus estudos!</p>
            </div>
        `;
        return;
    }

    // Ordenar cards por data
    const cardsOrdenados = [...cardsRealmente_atrasados].sort((a, b) => {
        const [diaA, mesA, anoA] = a.data_prevista.split('/').map(Number);
        const [diaB, mesB, anoB] = b.data_prevista.split('/').map(Number);
        const dataA = new Date(anoA, mesA - 1, diaA);
        const dataB = new Date(anoB, mesB - 1, diaB);
        return dataA - dataB;
    });

    // Renderizar cards com a função atualizada
    container.innerHTML = cardsOrdenados.map(card => criarCardAtrasado(card)).join('');
    
    // Adiciona evento aos checkboxes nos cards atrasados
// Adiciona evento aos checkboxes nos cards atrasados
document.querySelectorAll('.card-atrasado .conteudo-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', async function() {
        const cardElement = this.closest('.card-atrasado');
        const conteudoId = this.dataset.id;
        
        if (this.checked) {
            const estudado = await atualizarStatusEstudo(conteudoId, true);
            if (estudado) {
                // Mostra mensagem e remove o card
                mostrarRecompensa('✓ Conteúdo marcado como estudado!');
                await removerCard(cardElement);
                
                // Atualiza o contador de pendentes
                atualizarContador();
                
                // SINCRONIZAÇÃO: Encontra e marca o checkbox correspondente na aba cronograma
                const checkboxCronograma = document.querySelector(`.conteudo-item .conteudo-checkbox[data-id="${conteudoId}"]`);
                if (checkboxCronograma && !checkboxCronograma.checked) {
                    // Marca o checkbox sem disparar o evento change
                    checkboxCronograma.checked = true;
                    
                    // Adiciona a classe completed ao item pai
                    const itemCronograma = checkboxCronograma.closest('.conteudo-item');
                    if (itemCronograma) {
                        itemCronograma.classList.add('completed');
                        
                        // Atualiza os contadores visuais
                        const dia = itemCronograma.closest('.dia-horizontal');
                        if (dia) {
                            atualizarStatusDia(dia);
                        }
                        
                        // Atualiza o progresso geral
                        conteudosEstudados++;
                        atualizarProgresso();
                        atualizarEstatisticas();
                    }
                }
            }
        }
    });
});
}

// Adicionar CSS para a hierarquia nos cards atrasados
function adicionarEstiloHierarquia() {
    // Verifica se o estilo já existe
    if (document.querySelector('style[data-id="hierarchia-cards-atrasados"]')) {
        return;
    }
    
    const style = document.createElement('style');
    style.setAttribute('data-id', 'hierarchia-cards-atrasados');
    style.textContent = `
        .card-atrasado .conteudo-hierarchy {
            display: flex;
            flex-direction: column;
            gap: 5px;
            margin-bottom: 10px;
        }
        
        .card-atrasado .hierarchy-level-1 {
            font-weight: 600;
            font-size: 0.95rem;
            margin: 0;
        }
        
        .card-atrasado .hierarchy-level-2 {
            font-weight: 500;
            font-size: 0.9rem;
            margin: 0;
            padding-left: 10px;
        }
        
        .card-atrasado .hierarchy-level-3 {
            font-size: 0.85rem;
            margin: 0;
            padding-left: 20px;
        }
        
        .card-atrasado .hierarchy-level-4,
        .card-atrasado .hierarchy-level-5,
        .card-atrasado .hierarchy-level-6,
        .card-atrasado .hierarchy-level-7 {
            font-size: 0.8rem;
            margin: 0;
            padding-left: 30px;
        }
        
        .card-atrasado .current-topic {
            font-size: 0.9rem;
            margin: 0;
            color: var(--text-color);
            background-color: rgba(0, 0, 0, 0.05);
            padding: 5px;
            border-radius: 4px;
        }
        
        .card-atrasado-actions {
            display: flex;
            margin-top: 10px;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-atrasado-actions .conteudo-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 0.9rem;
        }
        
        .card-atrasado-actions .conteudo-checkbox {
            margin-right: 5px;
            cursor: pointer;
        }
    `;
    
    document.head.appendChild(style);
}

// Certificar que o estilo seja adicionado quando o DOM estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    adicionarEstiloHierarquia();
    
    // Se os cards já estiverem carregados, atualiza-os
    if (cards_atrasados && cards_atrasados.length > 0) {
        renderizarCardsAtrasados(cards_atrasados);
    }
});

// Adicione esta definição de função antes da função renderizarCardsAtrasados

// Função para mostrar o modal do calendário para replanejamento
// Função melhorada para mostrar o modal do calendário para replanejamento
// Função aprimorada para mostrar o modal de replanejamento
// Função para mostrar o modal do calendário para replanejamento
function mostrarModalCalendario() {
    // Remove modal existente se houver
    const modalExistente = document.querySelector('.modal-calendario');
    if (modalExistente) {
        modalExistente.remove();
    }

    // Calcular a data de hoje com horário zerado
    const hoje = new Date();
    hoje.setHours(0, 0, 0, 0);
    const dataHojeFormatada = hoje.toISOString().split('T')[0]; // Formato YYYY-MM-DD

    // Calcula quantos conteúdos estão pendentes vs. estudados
    const totalConteudos = document.querySelectorAll('.conteudo-item').length;
    const conteudosEstudados = document.querySelectorAll('.conteudo-item.completed').length;
    const conteudosPendentes = totalConteudos - conteudosEstudados;

    // Cria o modal
    const modal = document.createElement('div');
    modal.className = 'modal-calendario';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>Replanejar Cronograma</h3>
                <button class="btn-fechar"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <div class="info-box">
                    <i class="fas fa-info-circle"></i>
                    <div class="info-content">
                        <p><strong>O que acontecerá ao replanejar:</strong></p>
                        <ul>
                            <li>Os ${conteudosEstudados} conteúdos já estudados serão mantidos em uma seção separada</li>
                            <li>Os ${conteudosPendentes} conteúdos pendentes serão redistribuídos a partir da nova data</li>
                            <li>O sistema recalculará o cronograma automaticamente</li>
                        </ul>
                    </div>
                </div>
				
				
				 <!-- Adicione o alerta aqui -->
                <div class="alert alert-info" style="margin-bottom: 15px; padding: 10px; background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 4px; color: #0c5460;">
                    <i class="fas fa-info-circle"></i>
                    <strong>Nota:</strong> A data de início deve ser, no mínimo, uma semana antes da data da prova para permitir a distribuição adequada do conteúdo.
                </div>
				
				
				
                <div class="calendario-container">
                    <label for="nova-data-inicio">Nova data de início:</label>
                    <input type="date" id="nova-data-inicio" 
                           min="${dataHojeFormatada}" 
                           value="${dataHojeFormatada}"
                           class="input-calendario" required>
                </div>
                <div class="modal-footer">
                    <button class="btn-cancelar">Cancelar</button>
                    <button class="btn-confirmar">Confirmar Replanejamento</button>
                </div>
            </div>
        </div>
    `;

    // Adiciona estilos CSS (se já não existirem)
    if (!document.querySelector('style[data-modal-calendario]')) {
        const style = document.createElement('style');
        style.setAttribute('data-modal-calendario', 'true');
        style.textContent = `
            .modal-calendario {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease-out;
            }

            .modal-content {
                background: var(--paper-color, #fff);
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                width: 90%;
                max-width: 450px;
                padding: 20px;
                position: relative;
                animation: slideIn 0.3s ease-out;
            }

            .modal-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            }

            .modal-header h3 {
                margin: 0;
                color: var(--primary-color, #333);
                font-size: 1.2rem;
            }

            .info-box {
                display: flex;
                gap: 12px;
                background-color: rgba(0, 123, 255, 0.1);
                padding: 15px;
                border-radius: 6px;
                margin-bottom: 20px;
                border-left: 4px solid #0d6efd;
            }

            .info-box i {
                color: #0d6efd;
                font-size: 1.5rem;
                flex-shrink: 0;
                margin-top: 4px;
            }

            .info-content p {
                margin: 0 0 8px 0;
            }

            .info-content ul {
                margin: 0;
                padding-left: 20px;
            }

            .info-content li {
                margin-bottom: 4px;
                font-size: 0.9rem;
            }

            .btn-fechar {
                background: none;
                border: none;
                font-size: 1.2rem;
                cursor: pointer;
                color: #666;
                padding: 5px;
            }

            .calendario-container {
                margin-bottom: 20px;
            }

            .calendario-container label {
                display: block;
                margin-bottom: 8px;
                font-weight: 500;
            }

            .input-calendario {
                width: 100%;
                padding: 12px;
                border: 2px solid #ddd;
                border-radius: 8px;
                font-size: 1rem;
                font-family: inherit;
                box-sizing: border-box;
                background-color: white;
                color: var(--text-color, #333);
                outline: none;
                transition: all 0.3s ease;
            }

            .modal-footer {
                display: flex;
                justify-content: flex-end;
                gap: 10px;
                padding-top: 15px;
                margin-top: 5px;
            }

            .btn-cancelar, .btn-confirmar {
                padding: 10px 16px;
                border-radius: 6px;
                cursor: pointer;
                border: none;
                font-size: 0.95rem;
                transition: all 0.3s ease;
            }

            .btn-cancelar {
                background: #f1f1f1;
                color: #666;
                border: 1px solid #ddd;
            }

            .btn-confirmar {
                background: var(--primary-color, #4a90e2);
                color: white;
                font-weight: 500;
            }

            .btn-confirmar:disabled {
                background: #a0a0a0;
                cursor: not-allowed;
            }

            .btn-cancelar:hover {
                background: #e4e4e4;
            }

            .btn-confirmar:hover:not(:disabled) {
                filter: brightness(110%);
            }

            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }

            @keyframes slideIn {
                from { transform: translateY(-20px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(modal);

    // Event Listeners
    const btnFechar = modal.querySelector('.btn-fechar');
    const btnCancelar = modal.querySelector('.btn-cancelar');
    const btnConfirmar = modal.querySelector('.btn-confirmar');
    const inputData = modal.querySelector('#nova-data-inicio');

    btnFechar.addEventListener('click', () => modal.remove());
    btnCancelar.addEventListener('click', () => modal.remove());

    // Define a data padrão como hoje e habilita o botão de confirmar
    inputData.valueAsDate = hoje;
    btnConfirmar.disabled = false;

    // Habilita ou desabilita o botão de confirmar baseado na validação da data
    inputData.addEventListener('change', function() {
        const dataVal = this.value;
        const dataObj = dataVal ? new Date(dataVal + 'T00:00:00') : null; // Força o horário para meia-noite
        
        // Zerar as horas para comparação apenas de datas
        const hojeComparar = new Date();
        hojeComparar.setHours(0, 0, 0, 0);
        
        // Compara apenas as datas, sem considerar o horário
        btnConfirmar.disabled = !dataVal || dataObj < hojeComparar;
        
        if (!dataVal) {
            mostrarRecompensa('⚠️ Selecione uma data válida');
        } else if (dataObj < hojeComparar) {
            mostrarRecompensa('⚠️ A data deve ser hoje ou uma data futura');
        }
    });

    // Função que confirma o replanejamento
    btnConfirmar.addEventListener('click', async () => {
        const novaData = inputData.value;
        if (!novaData) {
            mostrarRecompensa('⚠️ Por favor, selecione uma data válida');
            return;
        }
    
        // Mostra loader ou indicador de progresso
        btnConfirmar.disabled = true;
        btnConfirmar.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Replanejando...';
    
        try {
            const response = await fetch('replanejar_pendentes.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    nova_data_inicio: novaData
                })
            });
    
            // Se a resposta não for ok, analisa o erro
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Erro ao replanejar');
            }
    
            const data = await response.json();
            
            if (data.success) {
                mostrarRecompensa('📅 ' + (data.message || 'Cronograma replanejado com sucesso!'));
                modal.remove();
                
                // Dá um tempo antes de recarregar para o usuário ver a mensagem
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                throw new Error(data.error || 'Erro ao replanejar conteúdos');
            }
        } catch (error) {
            console.error('Erro ao replanejar:', error);
            btnConfirmar.disabled = false;
            btnConfirmar.innerHTML = 'Confirmar Replanejamento';
            mostrarRecompensa('❌ ' + (error.message || 'Erro ao replanejar. Tente novamente.'));
        }
    });

    // Fecha o modal se clicar fora
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
    
    // Fecha o modal com a tecla ESC
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && document.contains(modal)) {
            modal.remove();
        }
    });
}

// Função para replanejar pendentes (isso era o que estava faltando)
function replanejarPendentes() {
    mostrarModalCalendario();
}






// Funções auxiliares para renderização de cards atrasados
function getStatusAtraso(dataPrevista) {
    const diasAtraso = calcularDiasAtraso(dataPrevista);
    if (diasAtraso > 7) return 'muito-atrasado';
    if (diasAtraso > 3) return 'atrasado';
    return 'pouco-atrasado';
}

function calcularDiasAtraso(dataPrevista) {
    const [dia, mes, ano] = dataPrevista.split('/').map(Number);
    const dataCard = new Date(ano, mes - 1, dia);
    const hoje = new Date();
    
    dataCard.setHours(0, 0, 0, 0);
    hoje.setHours(0, 0, 0, 0);
    
    const diffTime = Math.abs(hoje - dataCard);
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

// Adicionar este código como parte do evento change dos checkboxes na aba cronograma
    // Adiciona eventos a todos os checkboxes de conteúdo
// Adiciona eventos a todos os checkboxes de conteúdo
document.querySelectorAll('.conteudo-checkbox').forEach(checkbox => {
    checkbox.addEventListener('change', async function(e) {
        const conteudoId = this.dataset.id;
        const estudado = this.checked;
        const isReplanejado = this.classList.contains('conteudo-replanejado');
        
        // Se for um conteúdo replanejado e estiver sendo desmarcado
        if (isReplanejado && !estudado) {
            // Impede a ação padrão
            e.preventDefault();
            this.checked = true; // Mantém marcado até confirmar
            
            // Mostra o modal de confirmação
            mostrarModalRecalculo(conteudoId);
            return;
        }
        
        // Para conteúdos normais, continua com o fluxo padrão
        const conteudoItem = this.closest('.conteudo-item');
        
        if (conteudoItem) {
            conteudoItem.classList.add('updating');
            this.disabled = true;
            
            try {
                // Prepara os dados para envio
                const formData = new FormData();
                formData.append('conteudo_id', conteudoId);
                formData.append('status', estudado);
                formData.append('apos_replanejamento', true);
                
                // Envia a requisição
                const response = await fetch('atualizar_status.php', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                
                // Remove o estado de atualização
                conteudoItem.classList.remove('updating');
                this.disabled = false;
                
                if (data.success) {
                    // Define o checkbox com base na resposta do servidor
                    const novoStatus = data.status === 'Estudado';
                    this.checked = novoStatus;
                    
                    // Atualiza a classe completed no item pai
                    if (novoStatus) {
                        conteudoItem.classList.add('completed');
                        
                        // Mostrar mensagem de recompensa
                        mostrarRecompensa('✓ Conteúdo marcado como estudado!');
                        
                        // Atualizar os contadores visuais
                        const dia = conteudoItem.closest('.dia-horizontal');
                        if (dia) {
                            atualizarStatusDia(dia);
                        }
                        
                        // Atualiza o progresso geral
                        conteudosEstudados++;
                        
                        // Remover dos cards atrasados (se estiver lá)
                        const cardAtrasado = document.querySelector(`.card-atrasado[data-id="${conteudoId}"]`);
                        if (cardAtrasado) {
                            cards_atrasados = cards_atrasados.filter(card => card.id !== conteudoId);
                            removerCard(cardAtrasado);
                        }
                    } else {
                        conteudoItem.classList.remove('completed');
                        
                        // Mostrar mensagem 
                        mostrarRecompensa('Conteúdo desmarcado');
                        
                        // Atualizar os contadores visuais
                        const dia = conteudoItem.closest('.dia-horizontal');
                        if (dia) {
                            atualizarStatusDia(dia);
                        }
                        
                        // Atualiza o progresso geral
                        conteudosEstudados--;
                        
                        // Verificar se precisa adicionar aos cards atrasados
                        const dataString = conteudoItem.getAttribute('data-data-prevista');
                        if (dataString) {
                            const [anoCard, mesCard, diaCard] = dataString.split('-');
                            const dataCard = new Date(anoCard, mesCard - 1, diaCard);
                            const hoje = new Date();
                            
                            dataCard.setHours(0, 0, 0, 0);
                            hoje.setHours(0, 0, 0, 0);
                            
                            if (dataCard < hoje) {
                                const dadosCard = extrairHierarquiaDom(conteudoId);
                                if (dadosCard) {
                                    dadosCard.data_prevista = `${diaCard}/${mesCard}/${anoCard}`;
                                    dadosCard.status_estudo = 'Pendente';
                                    
                                    if (!cards_atrasados.some(card => card.id === dadosCard.id)) {
                                        cards_atrasados.push(dadosCard);
                                        renderizarCardsAtrasados(cards_atrasados);
                                    }
                                }
                            }
                        }
                    }
                    
                    // Atualizar interface, contadores, etc.
                    atualizarProgresso();
                    atualizarEstatisticas();
                    verificarMetas();
                    verificarCardsEstudados();
                } else {
                    // Em caso de erro, reverte o estado do checkbox
                    this.checked = !estudado;
                    mostrarRecompensa('❌ Erro ao salvar o progresso: ' + (data.error || 'Erro desconhecido'));
                }
            } catch (error) {
                console.error('Erro:', error);
                conteudoItem.classList.remove('updating');
                this.disabled = false;
                this.checked = !estudado; // Reverte o estado
                mostrarRecompensa('❌ Erro ao salvar o progresso. Por favor, tente novamente.');
            }
        }
    });
});

    // Event Listeners
    document.addEventListener('DOMContentLoaded', async function() {
        
        try {
            // Inicializar verificação de cards atrasados
        verificarTodosCardsAtrasados();
            // Inicializar dados
            renderizarCardsAtrasados(cards_atrasados);
            await atualizarEstatisticas();
            await carregarRevisoes();
            atualizarProgresso();

            // Configurar observador para navegação
            const sections = {
                'info': '.plano-info',
                'dashboard': '.dashboard-motivacional',
                'pendentes': '.cards-atrasados',
               
                'revisoes': '.revisoes-container',
                'plano': '.semana'
            };

            // Adicionar data-section e observer para cada seção
            Object.entries(sections).forEach(([key, selector]) => {
                const element = document.querySelector(selector);
                if (element) {
                    element.setAttribute('data-section', key);
                    observer.observe(element);
                }
            });

            // Carregar última semana aberta
            try {
                const ultimaSemanaAberta = localStorage.getItem('ultimaSemanaAberta');
                if (ultimaSemanaAberta) {
                    const semanas = document.querySelectorAll('.semana');
                    const semanaParaAbrir = semanas[parseInt(ultimaSemanaAberta)];
                    if (semanaParaAbrir) {
                        document.querySelectorAll('.semana-content').forEach(content => {
                            content.classList.remove('active');
                        });
                        document.querySelectorAll('.semana-title').forEach(title => {
                            title.classList.add('collapsed');
                        });

                        const content = semanaParaAbrir.querySelector('.semana-content');
                        const title = semanaParaAbrir.querySelector('.semana-title');
                        if (content && title) {
                            content.classList.add('active');
                            title.classList.remove('collapsed');
                        }
                    }
                }
            } catch (error) {
                console.error('Erro ao recuperar última semana:', error);
                localStorage.removeItem('ultimaSemanaAberta');
            }

        } catch (error) {
            console.error('Erro ao inicializar dados:', error);
        }

        // Verificar visibilidade da navegação
       
        const semanas = document.querySelector('.semana');

    });

    // Função auxiliar para formatar data no padrão brasileiro
function formatarData(data) {
    return data.toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    });
}

    // Controle de visibilidade dos botões
    window.addEventListener('scroll', () => {
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
    const backButton = document.querySelector('.btn-voltar');
    const menuIcon = document.querySelector('.menu-icon');
    const btnModoNoturno = document.getElementById('modo-noturno');
    
    if (scrollPosition > 100) {
        if (backButton) backButton.classList.add('nav-hidden');
        if (menuIcon) {
            menuIcon.style.opacity = '0';
            menuIcon.style.visibility = 'hidden';
        }
        if (btnModoNoturno) btnModoNoturno.classList.add('hidden');
        showScrollTopButton();
    } else {
        if (backButton) backButton.classList.remove('nav-hidden');
        if (menuIcon) {
            menuIcon.style.opacity = '1';
            menuIcon.style.visibility = 'visible';
        }
        if (btnModoNoturno) btnModoNoturno.classList.remove('hidden');
        hideScrollTopButton();
    }
});

    // Criar botão de voltar ao topo
    function initializeScrollToTop() {
        // Criar o botão
        const scrollButton = document.createElement('button');
        scrollButton.className = 'btn-scroll-top';
        scrollButton.innerHTML = '<i class="fas fa-arrow-up"></i>';
        document.body.appendChild(scrollButton);

        // Adicionar estilos
        const style = document.createElement('style');
        style.textContent = `
        .btn-scroll-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: var(--paper-color);
            border: 2px solid var(--border-color);
            border-radius: 50%;
            box-shadow: 4px 4px 0 var(--border-color);
            color: var(--primary-color);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            opacity: 0;
            pointer-events: none;
            z-index: 1000;
            visibility: hidden;
        }

        .btn-scroll-top.show {
            opacity: 1;
            pointer-events: auto;
            visibility: visible;
            transform: translateY(0);
        }

        .btn-scroll-top:hover {
            transform: translateY(-2px);
            box-shadow: 4px 4px 0 var(--border-color);
            background-color: var(--primary-color);
            color: white;
        }

        .btn-scroll-top:active {
            transform: translateY(0);
            box-shadow: 2px 2px 0 var(--border-color);
        }
    `;
        document.head.appendChild(style);

        // Adicionar eventos
        scrollButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });


    }

    // Inicializar quando o DOM estiver pronto
    document.addEventListener('DOMContentLoaded', initializeScrollToTop);




 



// 2. Função otimizada para mudar tabs
function mudarTab(tabId) {
    const buttons = document.querySelectorAll('.revisoes-tabs .tab-button');
    const contents = document.querySelectorAll('#review .tab-content');
    
    buttons.forEach(btn => {
        const isActive = btn.dataset.tab === tabId;
        btn.classList.toggle('active', isActive);
    });

    contents.forEach(content => {
        const isActive = content.id === `tab-${tabId}`;
        content.classList.toggle('active', isActive);
    });
}

    // Também precisamos ajustar a função carregarRevisoesIgnoradas
async function carregarRevisoesIgnoradas() {
    try {
        const response = await fetch('get_revisoes_ignoradas.php');
        const dados = await response.json();

        const container = document.getElementById('revisoes-ignoradas');

        if (!container) {
            console.error('Container de revisões ignoradas não encontrado');
            return;
        }

        if (dados && Array.isArray(dados) && dados.length > 0) {
            const htmlRevisoes = dados.map(revisao => {
                // Inicializa o conteúdo da hierarquia
                let hierarquiaHTML = '';
                
                // Nível 1 (Principal)
                if (revisao.descricao_capitulo_principal) {
                    hierarquiaHTML += `<p class="hierarchy-level-1">${revisao.descricao_capitulo_principal}</p>`;
                }
                
                // Nível 2 (Secundário)
                if (revisao.descricao_capitulo_secundario) {
                    hierarquiaHTML += `<p class="hierarchy-level-2">${revisao.descricao_capitulo_secundario}</p>`;
                }
                
                // Nível 3 (Terciário)
                if (revisao.descricao_capitulo_terciario) {
                    hierarquiaHTML += `<p class="hierarchy-level-3">${revisao.descricao_capitulo_terciario}</p>`;
                }
                
                // Nível 4 (Quaternário)
                if (revisao.descricao_capitulo_quaternario) {
                    hierarquiaHTML += `<p class="hierarchy-level-4">${revisao.descricao_capitulo_quaternario}</p>`;
                }
                
                // Nível 5 (Quinário)
                if (revisao.descricao_capitulo_quinario) {
                    hierarquiaHTML += `<p class="hierarchy-level-5">${revisao.descricao_capitulo_quinario}</p>`;
                }
                
                // Nível 6 (Sexto)
                if (revisao.descricao_capitulo_sexto) {
                    hierarquiaHTML += `<p class="hierarchy-level-6">${revisao.descricao_capitulo_sexto}</p>`;
                }
                
                // Nível 7 (Sétimo)
                if (revisao.descricao_capitulo_setimo) {
                    hierarquiaHTML += `<p class="hierarchy-level-7">${revisao.descricao_capitulo_setimo}</p>`;
                }

                return `
                <div class="card-revisao card-ignorado" style="border-left: 4px solid ${revisao.cor}">
                    <div class="card-header">
                        <h4><strong>${revisao.materia}</strong></h4>
                        <span class="status-ignorado">
                            ${revisao.status_revisao === 'ignorado_permanente' ?
                        '<i class="fas fa-ban"></i> Ignorado Permanentemente' :
                        '<i class="fas fa-clock"></i> Ignorado Temporariamente'}
                        </span>
                    </div>
                    
                    <div class="conteudo-hierarchy">
                        ${hierarquiaHTML}
                        <p class="current-topic"><strong>${revisao.capitulo}</strong> ${revisao.conteudo}</p>
                    </div>
                    
                    <div class="card-actions">
                        <button onclick="reativarRevisao(${revisao.id})" class="btn-reativar">
                            <i class="fas fa-redo"></i> Reativar Revisão
                        </button>
                    </div>
                </div>
            `;
            }).join('');

            container.innerHTML = htmlRevisoes;
        } else {
            container.innerHTML = `
                <div class="sem-revisoes">
                    <p>Não há revisões ignoradas! 🎉</p>
                </div>
            `;
        }
        // Adicionar esta linha no final da função
        atualizarContadoresRevisoes();

    } catch (error) {
        console.error('Erro ao carregar revisões ignoradas:', error);
        container.innerHTML = `
            <div class="erro-revisoes">
                <p>Erro ao carregar revisões ignoradas: ${error.message}</p>
            </div>
        `;
    }
}

    function ignorarRevisao(revisaoId) {
        const confirmDialog = document.createElement('div');
        confirmDialog.className = 'confirm-dialog';
        confirmDialog.innerHTML = `
        <div class="confirm-content">
            <h3>Como deseja ignorar esta revisão?</h3>
            <div class="confirm-options">
                <button onclick="executarIgnorar(${revisaoId}, 'temporario')" class="btn-option">
                    <i class="fas fa-clock"></i>
                    <span>Adiar Revisão</span>
                    <small>Volta em alguns dias</small>
                </button>
                <button onclick="executarIgnorar(${revisaoId}, 'permanente')" class="btn-option">
                    <i class="fas fa-ban"></i>
                    <span>Ignorar Permanentemente</span>
                    <small>Não revisa mais este conteúdo</small>
                </button>
            </div>
            <button onclick="this.closest('.confirm-dialog').remove()" class="btn-cancel">
                Cancelar
            </button>
        </div>
    `;
        document.body.appendChild(confirmDialog);
    }

    async function executarIgnorar(revisaoId, tipo) {
        try {
            const response = await fetch('ignorar_revisao.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    revisao_id: revisaoId,
                    tipo_ignorar: tipo
                })
            });

            const data = await response.json();

            if (data.success) {
                document.querySelector('.confirm-dialog')?.remove();
                await Promise.all([
                    carregarRevisoes(),
                    carregarRevisoesIgnoradas()
                ]);
                mostrarRecompensa(tipo === 'temporario' ?
                    '⏰ Revisão adiada com sucesso' :
                    '🚫 Conteúdo ignorado permanentemente');
            } else {
                throw new Error(data.error || 'Erro ao ignorar revisão');
            }
        } catch (error) {
            console.error('Erro ao ignorar revisão:', error);
            mostrarRecompensa('❌ Erro ao ignorar revisão. Tente novamente.');
        }
    }

    async function reativarRevisao(revisaoId) {
        try {
            const response = await fetch('reativar_revisao.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    revisao_id: revisaoId
                })
            });

            const data = await response.json();

            if (data.success) {
                await Promise.all([
                    carregarRevisoes(),
                    carregarRevisoesIgnoradas()
                ]);
                mostrarRecompensa('✨ Revisão reativada com sucesso!');
            } else {
                throw new Error(data.error || 'Erro ao reativar revisão');
            }
        } catch (error) {
            console.error('Erro ao reativar revisão:', error);
            mostrarRecompensa('❌ Erro ao reativar revisão. Tente novamente.');
        }
    }

    function toggleDia(header) {
    const diaElement = header.closest('.dia-horizontal');
    const content = diaElement.querySelector('.dia-content');
    const isExpanded = diaElement.classList.contains('expanded');
    
    // Fecha todos os outros dias
    document.querySelectorAll('.dia-horizontal').forEach(dia => {
        if (dia !== diaElement) {
            dia.classList.remove('expanded');
            dia.querySelector('.dia-content').classList.remove('active');
        }
    });
    
    // Toggle do dia atual
    diaElement.classList.toggle('expanded', !isExpanded);
    content.classList.toggle('active', !isExpanded);
}


// Funções auxiliares para o botão de scroll
function showScrollTopButton() {
    const scrollTopButton = document.querySelector('.btn-scroll-top');
    if (scrollTopButton) {
        scrollTopButton.classList.add('show');
    }
}

function hideScrollTopButton() {
    const scrollTopButton = document.querySelector('.btn-scroll-top');
    if (scrollTopButton) {
        scrollTopButton.classList.remove('show');
    }
}

// Único DOMContentLoaded para todas as funcionalidades
document.addEventListener('DOMContentLoaded', function() {
    adicionarEstiloHierarquiaRevisoes();
    // Inicializa o cache para todos os conteúdos existentes
    document.querySelectorAll('.conteudo-item').forEach(item => {
        const conteudoId = item.querySelector('.conteudo-checkbox')?.dataset.id;
        if (conteudoId) {
            const hierarquia = {
                id: conteudoId,
                materia: item.getAttribute('data-materia'),
                capitulo: item.getAttribute('data-capitulo'),
                descricao: item.getAttribute('data-descricao'),
                descricao_capitulo_principal: item.getAttribute('data-descricao-capitulo-principal'),
                descricao_capitulo_secundario: item.getAttribute('data-descricao-capitulo-secundario'),
                descricao_capitulo_terciario: item.getAttribute('data-descricao-capitulo-terciario'),
                descricao_capitulo_quaternario: item.getAttribute('data-descricao-capitulo-quaternario'),
                descricao_capitulo_quinario: item.getAttribute('data-descricao-capitulo-quinario'),
                descricao_capitulo_sexto: item.getAttribute('data-descricao-capitulo-sexto'),
                descricao_capitulo_setimo: item.getAttribute('data-descricao-capitulo-setimo'),
                cor: item.getAttribute('data-cor')
            };
            cachearHierarquia(conteudoId, hierarquia);
        }
    });
    // 1. Inicialização dos elementos principais
    const backButton = document.querySelector('.btn-voltar');
    const menuIcon = document.querySelector('.menu-icon');
    const semanasElement = document.querySelector('.semana');
    const btnModoNoturno = document.getElementById('modo-noturno');
    const modoNoturnoIcone = document.getElementById('modo-noturno-icone');
    const body = document.body;

     // Verifica se o modo noturno estava ativo
     const modoNoturnoAtivo = localStorage.getItem('modoNoturno') === 'ativado';
    
     // Aplica o modo noturno se estiver ativo
     if (modoNoturnoAtivo) {
         body.classList.add('modo-noturno');
         if (modoNoturnoIcone) {
             modoNoturnoIcone.classList.remove('fa-moon');
             modoNoturnoIcone.classList.add('fa-sun');
         }
     }
 
     // Adiciona o evento de clique
     if (btnModoNoturno) {
         btnModoNoturno.addEventListener('click', function() {
             // Toggle da classe no body
             body.classList.toggle('modo-noturno');
             
             // Atualiza o ícone
             if (body.classList.contains('modo-noturno')) {
                 modoNoturnoIcone.classList.remove('fa-moon');
                 modoNoturnoIcone.classList.add('fa-sun');
                 localStorage.setItem('modoNoturno', 'ativado');
             } else {
                 modoNoturnoIcone.classList.remove('fa-sun');
                 modoNoturnoIcone.classList.add('fa-moon');
                 localStorage.setItem('modoNoturno', 'desativado');
             }
         });
     }
    
    // 2. Configuração do scroll listener
    window.addEventListener('scroll', function() {
        const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
        const backButton = document.querySelector('.btn-voltar');
        const menuIcon = document.querySelector('.menu-icon');
        const btnModoNoturno = document.getElementById('modo-noturno');
        
        if (scrollPosition > 100) {
            if (backButton) backButton.classList.add('nav-hidden');
            if (menuIcon) {
                menuIcon.style.opacity = '0';
                menuIcon.style.visibility = 'hidden';
            }
            if (btnModoNoturno) btnModoNoturno.classList.add('hidden');
            showScrollTopButton();
        } else {
            if (backButton) backButton.classList.remove('nav-hidden');
            if (menuIcon) {
                menuIcon.style.opacity = '1';
                menuIcon.style.visibility = 'visible';
            }
            if (btnModoNoturno) btnModoNoturno.classList.remove('hidden');
            hideScrollTopButton();
        }
    });

    // 3. Inicialização do sistema principal de abas
    const tabs = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');
    
    if (tabs.length > 0 && tabContents.length > 0) {
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const targetId = tab.dataset.tab;
                
                // Remove active de todas as tabs
                tabs.forEach(t => t.classList.remove('active'));
                tabContents.forEach(content => content.classList.remove('active'));
                
                // Ativa a tab selecionada
                tab.classList.add('active');
                const targetContent = document.getElementById(targetId);
                if (targetContent) {
                    targetContent.classList.add('active');
                    
                    // Se a tab de revisões foi selecionada, recarrega os dados
                    if (targetId === 'review') {
                        carregarRevisoes();
                        carregarRevisoesIgnoradas();
                        
                        // Ativa a aba "pendentes" por padrão
                        const pendentesTab = document.querySelector('.revisoes-tabs .tab-button[data-tab="pendentes"]');
                        if (pendentesTab) {
                            pendentesTab.click();
                        }
                    }
                }
                
                // Salva a última tab ativa
                localStorage.setItem('lastActiveTab', targetId);
            });
        });
        
        // Carrega a última tab ativa ou usa a primeira como padrão
        const lastActiveTab = localStorage.getItem('lastActiveTab') || tabs[0].dataset.tab;
        const defaultTab = document.querySelector(`[data-tab="${lastActiveTab}"]`);
        if (defaultTab) {
            defaultTab.click();
        }
    }

    // 4. Inicialização das abas de revisões
    const revisoesButtons = document.querySelectorAll('.revisoes-tabs .tab-button');
    if (revisoesButtons) {
        // Define "pendentes" como ativa por padrão
        const pendentesTab = document.querySelector('.revisoes-tabs .tab-button[data-tab="pendentes"]');
        if (pendentesTab) {
            pendentesTab.classList.add('active');
            const pendentesContent = document.getElementById('tab-pendentes');
            if (pendentesContent) {
                pendentesContent.classList.add('active');
            }
        }

        revisoesButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabId = button.dataset.tab;
                
                // Remove active de todas as abas
                revisoesButtons.forEach(btn => btn.classList.remove('active'));
                document.querySelectorAll('#review .tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                
                // Ativa a aba selecionada
                button.classList.add('active');
                const targetContent = document.getElementById(`tab-${tabId}`);
                if (targetContent) {
                    targetContent.classList.add('active');
                }
            });
        });
    }

    // Carregar conteúdo inicial apenas da tab ativa
    const activeTab = document.querySelector('.tab-btn.active');
    if (activeTab && activeTab.dataset.tab === 'review') {
        carregarRevisoes();
        carregarRevisoesIgnoradas();
    }


    // 5. Inicialização do modo noturno


    // 6. Inicialização do botão voltar ao topo
    let scrollTopButton = document.querySelector('.btn-scroll-top');
    if (!scrollTopButton) {
        scrollTopButton = document.createElement('button');
        scrollTopButton.className = 'btn-scroll-top';
        scrollTopButton.innerHTML = '<i class="fas fa-arrow-up"></i>';
        document.body.appendChild(scrollTopButton);
        
        scrollTopButton.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // 7. Carrega as funcionalidades adicionais
    carregarRevisoes();
    carregarRevisoesIgnoradas();
    atualizarEstatisticas();
});

function searchTopic() {
    const searchText = document.getElementById('search-topic').value.toLowerCase();
    const cards = document.querySelectorAll('.conteudo-item'); // Esta é a classe correta dos seus cards

    // Se o campo de pesquisa estiver vazio, restaura o estado original
    if (!searchText) {
        // Restaura todos os cards e containers
        cards.forEach(card => card.style.display = '');
        
        // Remove qualquer display inline que foi adicionado
        document.querySelectorAll('.dia-content, .dia-horizontal, .semana-content, .semana, .conteudos-grid').forEach(el => {
            el.style.removeProperty('display'); // Usa removeProperty ao invés de definir ''
        });
        
        // Restaura as classes collapsed nas semanas que não estão expandidas
        document.querySelectorAll('.semana-title:not(.active)').forEach(el => {
            if (!el.classList.contains('collapsed')) {
                el.classList.add('collapsed');
            }
        });
        
        return;
    }
    cards.forEach(card => {
        // Procura o texto dentro dos elementos específicos do card
        const materia = card.querySelector('h4')?.textContent || '';
        const capituloPrincipal = card.querySelector('.capitulo-principal')?.textContent || '';
        const subcapitulo = card.querySelector('.subcapitulo')?.textContent || '';
        
        const cardText = (materia + ' ' + capituloPrincipal + ' ' + subcapitulo).toLowerCase();
        
        // Encontra os elementos pai que precisam ser mostrados/escondidos
        const diaContent = card.closest('.dia-content');
        const diaHorizontal = diaContent?.closest('.dia-horizontal');
        const semanaContent = diaHorizontal?.closest('.semana-content');
        const semana = semanaContent?.closest('.semana');
        
        if (cardText.includes(searchText)) {
            // Mostra o card e seus containers pai
            card.style.display = 'block';
            if (diaContent) diaContent.style.display = 'block';
            if (diaHorizontal) diaHorizontal.style.display = 'block';
            if (semanaContent) semanaContent.style.display = 'block';
            if (semana) semana.style.display = 'block';
        } else {
            // Esconde apenas o card
            card.style.display = 'none';
        }
    });
    
    // Verifica se há cards visíveis em cada dia
    document.querySelectorAll('.dia-content').forEach(diaContent => {
        const hasVisibleCards = Array.from(diaContent.querySelectorAll('.conteudo-item'))
            .some(card => card.style.display !== 'none');
        
        if (!hasVisibleCards) {
            const diaHorizontal = diaContent.closest('.dia-horizontal');
            if (diaHorizontal) diaHorizontal.style.display = 'none';
        }
    });
    
    // Verifica se há dias visíveis em cada semana
    document.querySelectorAll('.semana-content').forEach(semanaContent => {
        const hasVisibleDays = Array.from(semanaContent.querySelectorAll('.dia-horizontal'))
            .some(dia => dia.style.display !== 'none');
        
        if (!hasVisibleDays) {
            const semana = semanaContent.closest('.semana');
            if (semana) semana.style.display = 'none';
        }
    });
}

// Adicionar o event listener quando o documento estiver carregado
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('search-topic');
    if (searchInput) {
        searchInput.addEventListener('input', searchTopic);
    }
});

// Função para verificar se todos os cards de um dia estão completos
function verificarDiaCompleto(diaElement) {
    const cards = diaElement.querySelectorAll('.conteudo-item');
    const cardsCompletos = diaElement.querySelectorAll('.conteudo-item.completed');
    return cards.length > 0 && cards.length === cardsCompletos.length;
}

// Função para atualizar o status visual do dia
function atualizarStatusDia(diaElement) {
    const diaCompleto = verificarDiaCompleto(diaElement);
    if (diaCompleto) {
        diaElement.classList.add('completed');
    } else {
        diaElement.classList.remove('completed');
    }
}

// Função para atualizar todos os dias
function atualizarTodosDias() {
    document.querySelectorAll('.dia-horizontal').forEach(dia => {
        atualizarStatusDia(dia);
    });
}

// Função para atualizar os contadores de revisões
function atualizarContadoresRevisoes() {
    // Contador de pendentes
    const revisoesPendentes = document.querySelectorAll('#revisoes-lista .card-revisao').length;
    const contadorPendentes = document.getElementById('contador-pendentes');
    if (contadorPendentes) {
        contadorPendentes.textContent = revisoesPendentes;
    }

    // Contador de ignorados
    const revisoesIgnoradas = document.querySelectorAll('#revisoes-ignoradas .card-revisao').length;
    const contadorIgnorados = document.getElementById('contador-ignorados');
    if (contadorIgnorados) {
        contadorIgnorados.textContent = revisoesIgnoradas;
    }
}

function verificarProgressoMateria(materiaElement, progresso, materiaName) {
   
    
    const statCard = document.querySelector(`.stat-card[data-materia="${materiaName}"]`);
   
    
    if (progresso >= 100 && statCard && !statCard.classList.contains('materia-completa')) {
        // Adiciona classe para estilo
        statCard.classList.add('materia-completa');
        
        // Adiciona o ícone de troféu se ainda não existir
        if (!statCard.querySelector('.trophy-icon')) {
            const trofeu = document.createElement('div');
            trofeu.className = 'trophy-icon';
            trofeu.innerHTML = '<i class="fas fa-trophy"></i>';
            statCard.appendChild(trofeu);
            
            // Adiciona o efeito de brilho
            const brilho = document.createElement('div');
            brilho.className = 'completion-glow';
            statCard.appendChild(brilho);
            
            // Toca um som de conquista (opcional)
            const audio = new Audio('assets/sounds/achievement.mp3');
            audio.volume = 0.5;
            audio.play().catch(() => {}); // Ignora erro se o navegador bloquear
        }
    }
}

function mostrarModal(mensagem, tipo, materia, ponto) {
    const modal = document.createElement("div");
    modal.className = "modal-overlay";
    
    let botoes = "";
    if (tipo === "confirmar") {
        botoes = `
            <div class="modal-buttons">
                <button onclick="adicionarMateria(\'${materia}\', \'${ponto}\')" class="modal-button btn-sim">Sim</button>
                <button onclick="recusarAdicao()" class="modal-button btn-nao">Não</button>
            </div>
        `;
    } else {
        botoes = `
            <div class="modal-buttons">
                <button onclick="fecharModal()" class="modal-button btn-ok">OK</button>
            </div>
        `;
    }
    
    modal.innerHTML = `
        <div class="modal-content">
            <p>${mensagem}</p>
            ${botoes}
        </div>
    `;
    
    document.body.appendChild(modal);
}

function fecharModal() {
    const modal = document.querySelector(".modal-overlay");
    if (modal) {
        modal.remove();
    }
}

function verificarEAbrirCronometro(materia, ponto) {
    // Remover os "+" e substituir por espaços, depois decodificar o resto
    const materiaLimpa = materia.replace(/\+/g, ' ');
    const pontoLimpo = ponto.replace(/\+/g, ' ');
    
    // Decodificar caracteres especiais que ainda podem estar codificados
    const materiaDecodificada = decodeURIComponent(materiaLimpa);
    const pontoDecodificado = decodeURIComponent(pontoLimpo);
    
    console.log("Matéria tratada:", materiaDecodificada);
    console.log("Ponto tratado:", pontoDecodificado);
    
    // Agora codificar corretamente para a URL da API
    const materiaEnc = encodeURIComponent(materiaDecodificada);
    
    // Ajustando o caminho para incluir o diretório correto
    fetch("verificar_materia.php?materia=" + materiaEnc)
    .then(response => {
        if (!response.ok) {
            throw new Error('Erro na requisição: ' + response.status);
        }
        
        // Vamos obter a resposta como texto primeiro para diagnóstico
        return response.text().then(text => {
            // Tenta encontrar o JSON válido na resposta
            let jsonText = text;
            const scriptEndIndex = text.indexOf('</script>');
            
            if (scriptEndIndex !== -1) {
                // Se há um script, pega apenas a parte após o </script>
                jsonText = text.substring(scriptEndIndex + 9).trim();
            }
            
            // Tenta converter para JSON
            try {
                return JSON.parse(jsonText);
            } catch (e) {
                console.error("Erro ao parsear JSON:", e);
                throw new Error("Resposta não é JSON válido");
            }
        });
    })
    .then(data => {
        if (data.erro) {
            console.error('Erro retornado pela API:', data.erro);
            mostrarModal(data.erro, "erro");
            return;
        }
        
        if (data.existe) {
            // Usar os valores corretamente tratados
            const urlCronometro = `../0cronometro.php?materia=${encodeURIComponent(materiaDecodificada)}&ponto=${encodeURIComponent(pontoDecodificado)}`;
            
            console.log("Abrindo URL do cronômetro:", urlCronometro);
            
            window.open(urlCronometro, "_blank", "width=800,height=900");
        } else {
            mostrarModal(
                "Para registrar tempo dessa matéria, temos que adicioná-la a seu Planejamento?",
                "confirmar",
                materiaDecodificada,
                pontoDecodificado
            );
        }
    })
    .catch(error => {
        console.error("Erro detalhado:", error);
        mostrarModal("Erro ao verificar a matéria. Verifique o console para mais detalhes.", "erro");
    });
}

function adicionarMateria(materia, ponto) {
    console.log("Iniciando adição de matéria:", materia);
    
    // Usar o arquivo simplificado
    fetch("add_materia_api.php?materia=" + encodeURIComponent(materia))
    .then(async response => {
        console.log("Resposta recebida, status:", response.status);
        
        let responseText = await response.text();
        
        try {
            // Tentar extrair apenas o JSON da resposta
            // Esta regex procura o primeiro objeto JSON válido na resposta
            const jsonMatch = responseText.match(/\{[\s\S]*\}/);
            
            if (jsonMatch) {
                // Se encontrou um objeto JSON, tenta analisá-lo
                const jsonData = JSON.parse(jsonMatch[0]);
                console.log("Dados JSON extraídos:", jsonData);
                
                if (jsonData.sucesso) {
                    fecharModal();
                    
                    // Codificar corretamente os parâmetros para a URL
                    const urlCronometro = `../0cronometro.php?materia=${encodeURIComponent(materia)}&ponto=${encodeURIComponent(ponto)}`;
                    
                    console.log("Abrindo URL do cronômetro após adicionar matéria:", urlCronometro);
                    
                    window.open(urlCronometro, "_blank", "width=800,height=900");
                } else {
                    mostrarModal("Erro ao adicionar a matéria: " + (jsonData.erro || "Erro desconhecido"), "erro");
                }
            } else {
                // Se não encontrou JSON válido na resposta
                console.error("Resposta sem JSON válido:", responseText);
                
                // Como a funcionalidade parece estar funcionando mesmo com o erro,
                // vamos continuar com a operação
                fecharModal();
                
                // Abre o cronômetro mesmo assim
                const urlCronometro = `../0cronometro.php?materia=${encodeURIComponent(materia)}&ponto=${encodeURIComponent(ponto)}`;
                window.open(urlCronometro, "_blank", "width=800,height=900");
            }
        } catch (e) {
            console.error("Erro ao processar resposta:", e);
            console.error("Resposta recebida:", responseText);
            
            // Como a funcionalidade parece estar funcionando mesmo com o erro,
            // vamos continuar com a operação
            fecharModal();
            
            // Abre o cronômetro mesmo assim
            const urlCronometro = `../0cronometro.php?materia=${encodeURIComponent(materia)}&ponto=${encodeURIComponent(ponto)}`;
            window.open(urlCronometro, "_blank", "width=800,height=900");
        }
    })
    .catch(error => {
        console.error("Erro completo:", error);
        
        // Mesmo com erro, tenta abrir o cronômetro
        fecharModal();
        const urlCronometro = `../0cronometro.php?materia=${encodeURIComponent(materia)}&ponto=${encodeURIComponent(ponto)}`;
        window.open(urlCronometro, "_blank", "width=800,height=900");
        
        mostrarModal("Aviso: A matéria pode ter sido adicionada, mas houve um erro na comunicação.", "aviso");
    });
}

function recusarAdicao() {
    fecharModal();
    mostrarModal("Que pena, você não pode registrar o tempo dessa matéria!", "info");
}

// Atualizar a função verificarTodosCardsAtrasados para capturar todos os dados hierárquicos
// Função corrigida para verificar todos os cards atrasados
// 3. Modifique a função verificarTodosCardsAtrasados para usar o cache
function verificarTodosCardsAtrasados() {
  //  console.log('Iniciando verificação de cards atrasados...');
    const todosCards = document.querySelectorAll('.conteudo-item');
    let cardsAtrasadosTemp = [];

    todosCards.forEach(card => {
        // Verifica se o card já não está sendo mostrado como atrasado
        const conteudoId = card.querySelector('.conteudo-checkbox')?.dataset.id;
        const estudado = card.querySelector('.conteudo-checkbox')?.checked;
        const dataString = card.getAttribute('data-data-prevista');
        
        if (!conteudoId || !dataString) {
            return;
        }

        // Converte a data para comparação
        const [ano, mes, dia] = dataString.split('-');
        const dataCard = new Date(ano, mes - 1, dia);
        const hoje = new Date();
        
        // Normaliza as horas para comparação precisa
        dataCard.setHours(0, 0, 0, 0);
        hoje.setHours(0, 0, 0, 0);

        // Se está atrasado e não foi estudado, adiciona à lista
        if (dataCard < hoje && !estudado) {
            // Dados hierárquicos completos
            const hierarquia = {
                id: conteudoId,
                materia: card.getAttribute('data-materia'),
                capitulo: card.getAttribute('data-capitulo'),
                descricao: card.getAttribute('data-descricao'),
                descricao_capitulo_principal: card.getAttribute('data-descricao-capitulo-principal'),
                descricao_capitulo_secundario: card.getAttribute('data-descricao-capitulo-secundario'),
                descricao_capitulo_terciario: card.getAttribute('data-descricao-capitulo-terciario'),
                descricao_capitulo_quaternario: card.getAttribute('data-descricao-capitulo-quaternario'),
                descricao_capitulo_quinario: card.getAttribute('data-descricao-capitulo-quinario'),
                descricao_capitulo_sexto: card.getAttribute('data-descricao-capitulo-sexto'),
                descricao_capitulo_setimo: card.getAttribute('data-descricao-capitulo-setimo'),
                cor: card.getAttribute('data-cor')
            };
            
            // Salva no cache
            cachearHierarquia(conteudoId, hierarquia);
            
            // Cria o card com todos os dados
            const cardData = {
                ...hierarquia,
                data_prevista: `${dia}/${mes}/${ano}`,
                status_estudo: 'Pendente'
            };
            
            cardsAtrasadosTemp.push(cardData);
        }
    });

    // Atualiza a variável global
    cards_atrasados = cardsAtrasadosTemp;
    
    // Renderiza os cards atrasados
    renderizarCardsAtrasados(cards_atrasados);
}


// Aguarda o DOM estar completamente carregado
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('modal-reset');
    const btnResetEstudos = document.getElementById('btn-reset-estudos');
    const btnCancelarReset = document.getElementById('btn-cancelar-reset');
    const btnConfirmarReset = document.getElementById('btn-confirmar-reset');

    // Função para mostrar o modal
    function mostrarModalReset() {
        if (modal) {
            modal.style.display = 'block';
            document.body.style.overflow = 'hidden'; // Impede o scroll da página
        }
    }

    // Função para fechar o modal
    function fecharModalReset() {
        if (modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto'; // Restaura o scroll da página
        }
    }

    // Função para executar o reset
    function executarReset() {
        fetch('reset_estudos.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Desmarca todos os checkboxes
                document.querySelectorAll('.conteudo-checkbox').forEach(checkbox => {
                    checkbox.checked = false;
                });
                
                // Remove a classe 'completed' de todos os conteúdos
                document.querySelectorAll('.conteudo-item').forEach(item => {
                    item.classList.remove('completed');
                });
                
                // Atualiza a barra de progresso
                atualizarProgresso();
                
                // Fecha o modal
                fecharModalReset();
                
                // Recarrega a página para atualizar todos os dados
                window.location.reload();
            } else {
                alert('Erro ao resetar progresso: ' + (data.error || 'Erro desconhecido'));
            }
        })
        .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao resetar progresso. Por favor, tente novamente.');
        });
    }

    // Event Listeners
    if (btnResetEstudos) {
        btnResetEstudos.addEventListener('click', mostrarModalReset);
    }

    if (btnCancelarReset) {
        btnCancelarReset.addEventListener('click', fecharModalReset);
    }

    if (btnConfirmarReset) {
        btnConfirmarReset.addEventListener('click', executarReset);
    }

    // Fecha o modal se clicar fora dele
    if (modal) {
        window.addEventListener('click', (event) => {
            if (event.target === modal) {
                fecharModalReset();
            }
        });
    }

    // Fecha o modal com a tecla ESC
    document.addEventListener('keydown', (event) => {
        if (event.key === 'Escape' && modal && modal.style.display === 'block') {
            fecharModalReset();
        }
    });
});

// Função para verificar se existem cards estudados
function verificarCardsEstudados() {
    const cardsEstudados = document.querySelectorAll('.conteudo-item.completed');
    const resetContainer = document.querySelector('.reset-container');
    
    if (cardsEstudados.length > 0) {
        resetContainer.style.display = 'block';
    } else {
        resetContainer.style.display = 'none';
    }
}

// Adicionar evento aos checkboxes
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('conteudo-checkbox')) {
        const conteudoItem = e.target.closest('.conteudo-item');
        
        // Verifica se o conteudoItem existe antes de manipular suas classes
        if (conteudoItem) {
            if (e.target.checked) {
                conteudoItem.classList.add('completed');
            } else {
                conteudoItem.classList.remove('completed');
            }
            // Verifica imediatamente após a mudança do checkbox
            verificarCardsEstudados();
        } else {
            //console.log('Checkbox clicado, mas não está dentro de um elemento .conteudo-item');
        }
    }
});

// Checar estado inicial quando a página carrega
document.addEventListener('DOMContentLoaded', function() {
    verificarCardsEstudados();
    
    // Adiciona a verificação após qualquer atualização de status
    const checkboxes = document.querySelectorAll('.conteudo-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', () => {
            verificarCardsEstudados();
        });
    });
});

// Se você estiver usando AJAX para atualizar o status
function updateEstudoStatus(checkbox, id) {
    // Seu código existente de atualização...
    
    // Adicione isso após a atualização do status
    verificarCardsEstudados();
}

// Função para sincronizar o status de estudo entre abas
// Função corrigida para sincronizar o status de estudo entre abas
function sincronizarStatusEstudo(conteudoId, estudado) {
    // Ação baseada no status
    if (estudado) {
        // 1. Atualiza na aba cronograma
        const checkboxCronograma = document.querySelector(`.conteudo-item .conteudo-checkbox[data-id="${conteudoId}"]`);
        if (checkboxCronograma && !checkboxCronograma.checked) {
            // Marca o checkbox sem disparar o evento change nativo
            checkboxCronograma.checked = true;
            
            // Adiciona classe de completado ao item
            const itemCronograma = checkboxCronograma.closest('.conteudo-item');
            if (itemCronograma) {
                itemCronograma.classList.add('completed');
                
                // Atualiza o status do dia
                const diaElement = itemCronograma.closest('.dia-horizontal');
                if (diaElement) {
                    atualizarStatusDia(diaElement);
                }
            }
        }
        
        // 2. Remove da aba pendentes
        const cardAtrasado = document.querySelector(`.card-atrasado[data-id="${conteudoId}"]`);
        if (cardAtrasado) {
            cards_atrasados = cards_atrasados.filter(card => card.id !== conteudoId);
            removerCard(cardAtrasado);
        }
    } else {
        // Desmarcado como estudado
        // 1. Atualiza na aba cronograma
        const checkboxCronograma = document.querySelector(`.conteudo-item .conteudo-checkbox[data-id="${conteudoId}"]`);
        if (checkboxCronograma && checkboxCronograma.checked) {
            checkboxCronograma.checked = false;
            
            const itemCronograma = checkboxCronograma.closest('.conteudo-item');
            if (itemCronograma) {
                itemCronograma.classList.remove('completed');
                
                // Atualiza o status do dia
                const diaElement = itemCronograma.closest('.dia-horizontal');
                if (diaElement) {
                    atualizarStatusDia(diaElement);
                }
                
                // 2. Adiciona à aba pendentes se estiver atrasado
                const dataString = itemCronograma.getAttribute('data-data-prevista');
                if (dataString) {
                    const [anoCard, mesCard, diaCard] = dataString.split('-');
                    const dataCard = new Date(anoCard, mesCard - 1, diaCard);
                    const hoje = new Date();
                    
                    dataCard.setHours(0, 0, 0, 0);
                    hoje.setHours(0, 0, 0, 0);
                    
                    if (dataCard < hoje) {
                        // CORREÇÃO AQUI: Usamos a nova função para extrair a hierarquia completa 
                        const dadosCard = extrairHierarquiaDom(conteudoId);
                        if (dadosCard) {
                            // Adiciona a data prevista
                            dadosCard.data_prevista = `${diaCard}/${mesCard}/${anoCard}`;
                            dadosCard.status_estudo = 'Pendente';
                            
                            //console.log("Dados completos do card a ser adicionado:", dadosCard);
                            
                            // Adiciona apenas se ainda não existir na lista
                            if (!cards_atrasados.some(card => card.id === dadosCard.id)) {
                                cards_atrasados.push(dadosCard);
                                renderizarCardsAtrasados(cards_atrasados);
                            }
                        }
                    }
                }
            }
        }
    }
    
    // Atualiza o progresso geral e estatísticas
    atualizarProgresso();
    atualizarEstatisticas();
}

// 1. Primeiramente, vamos adicionar essa função para recuperar os dados da hierarquia diretamente do HTML
function extrairHierarquiaDom(conteudoId) {
    //console.log(`Extraindo hierarquia do DOM para o item ${conteudoId}...`);
    
    // Encontra o elemento de conteúdo pelo ID
    const itemCronograma = document.querySelector(`.conteudo-item .conteudo-checkbox[data-id="${conteudoId}"]`)?.closest('.conteudo-item');
    if (!itemCronograma) {
       // console.log(`Item com ID ${conteudoId} não encontrado no DOM`);
        return null;
    }
    
    // Verifica se o elemento tem os atributos data necessários
    const materia = itemCronograma.getAttribute('data-materia');
    const capitulo = itemCronograma.getAttribute('data-capitulo');
    const descricao = itemCronograma.getAttribute('data-descricao');
    
    // Agora busca os elementos de hierarquia renderizados
    const hierarquiaContainer = itemCronograma.querySelector('.conteudo-hierarchy');
    
    if (!hierarquiaContainer) {
        //console.log(`Item ${conteudoId} não tem container de hierarquia`);
        return {
            id: conteudoId,
            materia: materia,
            materia_nome: materia, // Garante que ambos os campos existam
            capitulo: capitulo,
            descricao: descricao,
            cor: itemCronograma.getAttribute('data-cor'),
            // Usa os atributos data
            descricao_capitulo_principal: itemCronograma.getAttribute('data-descricao-capitulo-principal'),
            descricao_capitulo_secundario: itemCronograma.getAttribute('data-descricao-capitulo-secundario'),
            descricao_capitulo_terciario: itemCronograma.getAttribute('data-descricao-capitulo-terciario'),
            descricao_capitulo_quaternario: itemCronograma.getAttribute('data-descricao-capitulo-quaternario'),
            descricao_capitulo_quinario: itemCronograma.getAttribute('data-descricao-capitulo-quinario'),
            descricao_capitulo_sexto: itemCronograma.getAttribute('data-descricao-capitulo-sexto'),
            descricao_capitulo_setimo: itemCronograma.getAttribute('data-descricao-capitulo-setimo')
        };
    }
    
    // Extrai os textos dos níveis de hierarquia renderizados
    const nivel1 = hierarquiaContainer.querySelector('.hierarchy-level-1')?.textContent;
    const nivel2 = hierarquiaContainer.querySelector('.hierarchy-level-2')?.textContent;
    const nivel3 = hierarquiaContainer.querySelector('.hierarchy-level-3')?.textContent;
    const nivel4 = hierarquiaContainer.querySelector('.hierarchy-level-4')?.textContent;
    const nivel5 = hierarquiaContainer.querySelector('.hierarchy-level-5')?.textContent;
    const nivel6 = hierarquiaContainer.querySelector('.hierarchy-level-6')?.textContent;
    const nivel7 = hierarquiaContainer.querySelector('.hierarchy-level-7')?.textContent;
    
   // console.log("Níveis extraídos do DOM:");
 //   console.log("Nivel 1:", nivel1);
  //  console.log("Nivel 2:", nivel2);
    
    return {
        id: conteudoId,
        materia: materia,
        materia_nome: materia, // Garante que ambos os campos existam
        capitulo: capitulo,
        descricao: descricao,
        cor: itemCronograma.getAttribute('data-cor'),
        // Prioriza o que está renderizado, com fallback para os atributos data
        descricao_capitulo_principal: nivel1 || itemCronograma.getAttribute('data-descricao-capitulo-principal'),
        descricao_capitulo_secundario: nivel2 || itemCronograma.getAttribute('data-descricao-capitulo-secundario'),
        descricao_capitulo_terciario: nivel3 || itemCronograma.getAttribute('data-descricao-capitulo-terciario'),
        descricao_capitulo_quaternario: nivel4 || itemCronograma.getAttribute('data-descricao-capitulo-quaternario'),
        descricao_capitulo_quinario: nivel5 || itemCronograma.getAttribute('data-descricao-capitulo-quinario'),
        descricao_capitulo_sexto: nivel6 || itemCronograma.getAttribute('data-descricao-capitulo-sexto'),
        descricao_capitulo_setimo: nivel7 || itemCronograma.getAttribute('data-descricao-capitulo-setimo')
    };
}

// Adicione esta função ao seu arquivo scripts.js
document.addEventListener('DOMContentLoaded', function() {
    // Encontra todos os checkboxes de conteúdos já replanejados
    document.querySelectorAll('.conteudo-replanejado').forEach(checkbox => {
        checkbox.addEventListener('change', function(e) {
            // Se estiver tentando desmarcar
            if (!this.checked) {
                // Impede a mudança imediata
                e.preventDefault();
                this.checked = true;
                
                // Mostra o modal de confirmação
                mostrarModalRecalculo(this.dataset.id);
            }
        });
    });
});

// Função para mostrar o modal de confirmação de recálculo
function mostrarModalRecalculo(conteudoId) {
    // Remove modal existente se houver
    const modalExistente = document.querySelector('.modal-recalculo');
    if (modalExistente) {
        modalExistente.remove();
    }

    // Cria o modal
    const modal = document.createElement('div');
    modal.className = 'modal-recalculo';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>Atenção - Impacto no Cronograma</h3>
                <button class="btn-fechar"><i class="fas fa-times"></i></button>
            </div>
            <div class="modal-body">
                <div class="info-box warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div class="info-content">
                        <p><strong>Este conteúdo foi estudado antes do replanejamento!</strong></p>
                        <p>Ao desmarcá-lo como não estudado, o sistema precisará:</p>
                        <ul>
                            <li>Recalcular a distribuição de todos os cards</li>
                            <li>Recarregar o cronograma completo</li>
                            <li>Incluir este conteúdo novamente no planejamento atual</li>
                        </ul>
                        <p>Tem certeza que deseja continuar?</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-cancelar">Cancelar</button>
                <button class="btn-confirmar">Confirmar Alteração</button>
            </div>
        </div>
    `;

    // Adiciona estilos CSS específicos para este modal
    if (!document.querySelector('style[data-modal-recalculo]')) {
        const style = document.createElement('style');
        style.setAttribute('data-modal-recalculo', 'true');
        style.textContent = `
            .modal-recalculo {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 1000;
                animation: fadeIn 0.3s ease-out;
            }

            .info-box.warning {
                background-color: rgba(255, 193, 7, 0.1);
                border-left: 4px solid #ffc107;
            }

            .info-box.warning i {
                color: #ffc107;
            }
        `;
        document.head.appendChild(style);
    }

    document.body.appendChild(modal);

    // Event Listeners
    const btnFechar = modal.querySelector('.btn-fechar');
    const btnCancelar = modal.querySelector('.btn-cancelar');
    const btnConfirmar = modal.querySelector('.btn-confirmar');

    btnFechar.addEventListener('click', () => modal.remove());
    btnCancelar.addEventListener('click', () => modal.remove());

    // Função que confirma a desmarcação
    btnConfirmar.addEventListener('click', async () => {
        // Mostra loader ou indicador de progresso
        btnConfirmar.disabled = true;
        btnConfirmar.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processando...';
    
        try {
            // Chamar o backend para desmarcar o conteúdo e reprocessar o cronograma
            const response = await fetch('desmarcar_replanejado.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    conteudo_id: conteudoId
                })
            });
    
            const data = await response.json();
            
            if (data.success) {
                mostrarRecompensa('✓ ' + (data.message || 'Conteúdo desmarcado com sucesso!'));
                modal.remove();
                
                // Recarrega a página para mostrar o cronograma atualizado
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                throw new Error(data.error || 'Erro ao desmarcar conteúdo');
            }
        } catch (error) {
            console.error('Erro ao desmarcar:', error);
            btnConfirmar.disabled = false;
            btnConfirmar.innerHTML = 'Confirmar Alteração';
            mostrarRecompensa('❌ ' + (error.message || 'Erro ao processar. Tente novamente.'));
        }
    });

    // Fecha o modal se clicar fora
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

