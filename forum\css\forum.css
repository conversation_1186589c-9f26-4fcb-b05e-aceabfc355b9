/* Variáveis de cores */
:root {
    --primary: #000080;
    --primary-light: #3949ab;
    --secondary: #f8f9fa;
    --accent: #0056b3;
    --border: #e0e0e0;
    --text: #333333;
    --text-secondary: #6c757d;
    --background: #f8f9fa;
    --card-background: white;
    --upvote: #ff4500;
    --downvote: #7193ff;
    --shadow: rgba(0, 0, 0, 0.1);
    --hover: #f5f5f5;
}

/* Variáveis para modo escuro */
.dark-mode {
    --primary: #4a90e2;
    --primary-light: #6ba7e7;
    --secondary: #2d2d2d;
    --accent: #64b5f6;
    --border: #404040;
    --text: #e0e0e0;
    --text-secondary: #a0a0a0;
    --background: #1a1a1a;
    --card-background: #2d2d2d;
    --upvote: #ff6b6b;
    --downvote: #7ba7ff;
    --shadow: rgba(0, 0, 0, 0.3);
    --hover: #404040;
}

/* Reset e estilos gerais */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: var(--background);
    color: var(--text);
    line-height: 1.5;
    font-size: 14px;
}

a {
    text-decoration: none;
    color: var(--accent);
}

button {
    cursor: pointer;
    border: none;
    background: none;
}

/* Header */
.main-header {
    background-color: white;
    box-shadow: 0 1px 3px var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    height: 56px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-left {
    display: flex;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    font-size: 18px;
    margin-right: 20px;
    color: var(--primary);
}

.logo strong {
    font-weight: 700;
}

.logo span {
    font-weight: 400;
    color: var(--text-secondary);
    margin-left: 4px;
}

.search-container {
    position: relative;
    width: 300px;
}

.search-input {
    width: 100%;
    padding: 8px 12px;
    padding-right: 40px;
    border: 1px solid var(--border);
    border-radius: 4px;
    font-size: 14px;
    background-color: var(--secondary);
}

.search-button {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.btn-criar-post {
    background-color: var(--primary);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 14px;
    transition: background-color 0.2s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.btn-criar-post:hover {
    background-color: var(--primary-light);
    text-decoration: none;
    color: white;
}

/* Estilo específico para o botão na sidebar */
.sidebar-card .btn-criar-post {
    width: 100%;
    margin-top: 12px;
    padding: 10px 16px;
    font-size: 14px;
    background-color: white;
    color: var(--primary);
    border: 1px solid var(--primary);
}

.sidebar-card .btn-criar-post:hover {
    background-color: var(--secondary);
    color: var(--primary);
    text-decoration: none;
}

.icon-button {
    color: var(--text-secondary);
    font-size: 18px;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.icon-button:hover {
    background-color: var(--hover);
}

/* Layout principal */
.main-container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 0 20px;
    display: grid;
    grid-template-columns: 1fr 320px;
    gap: 24px;
}

/* Cabeçalho do fórum */
.forum-header {
    margin-bottom: 20px;
}

.forum-header h1 {
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 16px;
    color: var(--text);
}

.forum-tabs {
    display: flex;
    border-bottom: 1px solid var(--border);
    margin-bottom: 16px;
}

.tab {
    padding: 8px 16px;
    color: var(--text-secondary);
    font-weight: 500;
    border-bottom: 2px solid transparent;
    transition: all 0.2s;
}

.tab.active {
    color: var(--primary);
    border-bottom-color: var(--primary);
}

.tab i {
    margin-right: 6px;
}

.forum-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.btn-filtrar {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background-color: white;
    border: 1px solid var(--border);
    border-radius: 4px;
    color: var(--text);
    font-weight: 500;
}

/* Lista de tópicos */
.topics-list {
    display: flex;
    flex-direction: column;
    gap: 1px;
}

.topics-list.topics-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.topic-card {
    background-color: white;
    border: 1px solid var(--border);
    border-radius: 4px;
    display: flex;
    overflow: hidden;
}

.vote-buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    background-color: var(--secondary);
    min-width: 40px;
}

.vote-up, .vote-down {
    color: var(--text-secondary);
    padding: 4px;
    transition: color 0.2s;
}

.vote-up:hover {
    color: var(--upvote);
}

.vote-down:hover {
    color: var(--downvote);
}

.vote-count {
    font-weight: 600;
    margin: 4px 0;
}

.topic-content {
    padding: 16px;
    flex-grow: 1;
}

.topic-category {
    margin-bottom: 8px;
    font-size: 12px;
}

.topic-category a {
    font-weight: 600;
}

.topic-meta {
    color: var(--text-secondary);
}

.topic-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 8px;
}

.topic-title a {
    color: var(--text);
}

.topic-title a:hover {
    color: var(--accent);
}

.topic-excerpt {
    color: var(--text-secondary);
    margin-bottom: 16px;
    font-size: 14px;
    line-height: 1.5;
}

.topic-actions {
    display: flex;
    gap: 16px;
}

.topic-action {
    display: flex;
    align-items: center;
    gap: 6px;
    color: var(--text-secondary);
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.topic-action:hover {
    background-color: var(--hover);
}

/* Sidebar */
.sidebar-card {
    background-color: white;
    border: 1px solid var(--border);
    border-radius: 4px;
    padding: 16px;
    margin-bottom: 16px;
}

.sidebar-card h2 {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--text);
}

.welcome-card {
    background-color: var(--primary);
    color: white;
}

.welcome-card h2 {
    color: white;
}

.welcome-card p {
    margin-bottom: 16px;
    font-size: 14px;
}

.btn-full {
    display: block;
    width: 100%;
    padding: 8px 0;
    text-align: center;
    background-color: white;
    color: var(--primary);
    border-radius: 4px;
    font-weight: 500;
    transition: background-color 0.2s;
}

.btn-full:hover {
    background-color: var(--secondary);
}

.categories-list {
    list-style: none;
}

.categories-list li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border);
}

.categories-list li:last-child {
    border-bottom: none;
}

.category-count {
    color: var(--text-secondary);
    font-size: 12px;
}

.link-ver-todas {
    display: block;
    text-align: center;
    margin-top: 12px;
    font-size: 14px;
    color: var(--accent);
}

.rules-list {
    padding-left: 20px;
    font-size: 14px;
}

.rules-list li {
    margin-bottom: 8px;
}

/* Responsividade */
@media (max-width: 768px) {
    .main-container {
        grid-template-columns: 1fr;
    }
    
    .search-container {
        width: 200px;
    }
    
    .forum-actions {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
}

@media (max-width: 576px) {
    .header-container {
        padding: 0 12px;
    }
    
    .search-container {
        display: none;
    }
    
    .main-container {
        padding: 0 12px;
    }
    
    .topic-card {
        flex-direction: column;
    }
    
    .vote-buttons {
        flex-direction: row;
        justify-content: center;
        width: 100%;
        padding: 4px;
    }
    
    .vote-count {
        margin: 0 8px;
    }
}

@media (max-width: 800px) {
    .topics-list.topics-grid {
        grid-template-columns: 1fr;
    }
}

.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: var(--card-background, #fff);
    margin: 5% auto;
    padding: 24px;
    width: 90%;
    max-width: 600px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: relative;
}

.close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    position: absolute;
    top: 12px;
    right: 12px;
    color: var(--text-secondary);
}

.close-button:hover {
    color: var(--primary);
}

.modal-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

.btn-excluir, .btn-cancel, .btn-confirm {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 14px;
    border: none;
    cursor: pointer;
}

.btn-excluir, .btn-confirm {
    background-color: #dc3545;
    color: white;
}

.btn-excluir:hover, .btn-confirm:hover {
    background-color: #b52a37;
}

.btn-cancel {
    background-color: #6c757d;
    color: white;
}

.btn-cancel:hover {
    background-color: #495057;
}

.warning-text {
    color: #dc3545;
    font-size: 13px;
    margin-top: 8px;
}

.comment-card {
    background: none;
    border: none;
    border-radius: 0;
    box-shadow: none;
    padding: 8px 0 8px 8px;
    margin-bottom: 0;
    position: relative;
    transition: none;
}

.comment-nivel-1 {
    border-left: 2.5px solid #1976d2;
    background: none;
    margin-left: 24px;
    /*padding-left: 12px;*/
}
.comment-nivel-2 {
    border-left: 2.5px solid #90caf9;
    background: none;
    margin-left: 48px;
    /*padding-left: 12px;*/
}
.comment-nivel-3 {
    border-left: 2.5px solid #bdbdbd;
    background: none;
    margin-left: 72px;
    /*padding-left: 12px;*/
}
.comment-nivel-4 {
    border-left: 2.5px solid #e0e0e0;
    background: none;
    margin-left: 96px;
    /*padding-left: 12px;*/
}
.comment-nivel-5, .comment-nivel-6, .comment-nivel-7 {
    border-left: 2.5px solid #e0e0e0;
    background: none;
    margin-left: 120px;
    /*padding-left: 12px;*/
}

.comment-card .comment-actions {
    display: flex;
    align-items: center;
    gap: 18px;
    margin-top: 4px;
    font-size: 14px;
}

.comment-card .comment-actions button,
.comment-card .comment-actions .topic-action {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: 14px;
    padding: 0 4px;
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    border-radius: 2px;
    transition: background 0.15s;
}

.comment-card .comment-actions button:hover,
.comment-card .comment-actions .topic-action:hover {
    background: none;
    color: var(--primary);
    padding: 2px;
}

.comment-card .comment-actions .vote-btn {
    font-size: 14px;
    color: #888;
    padding: 0 2px;
}

.comment-card .comment-actions .vote-btn.voted {
    color: #1976d2;
}

.comment-card .comment-actions .vote-count {
    font-weight: 500;
    margin: 0 2px;
    font-size: 13px;
}

.comment-card .comment-actions .btn-action {
    color: #888;
    font-size: 14px;
    padding: 0 4px;
}

.comment-card .comment-actions .btn-action.btn-delete {
    color: #c62828;
}

.comment-card .comment-actions .btn-action.btn-delete:hover {
    color: #fff;
    background: #c62828;
    padding: 2px;
}

.btn-submit {
    background: var(--primary);
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 8px 18px;
    font-size: 15px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s;
    display: flex;
    align-items: center;
    gap: 6px;
}

.btn-submit:hover {
    background: var(--primary-light);
}


.loading {
    text-align: center;
    padding: 20px;
    font-size: 1.1em;
    color: #666;
}
.error {
    text-align: center;
    padding: 20px;
    color: #dc3545;
    font-size: 1.1em;
}
.filter-panel {
    position: fixed;
    top: 0;
    right: -350px;
    width: 350px;
    max-width: 100vw;
    height: 100vh;
    background: var(--bg-color, #fff);
    box-shadow: -2px 0 16px rgba(0,0,0,0.12);
    z-index: 1000;
    transition: right 0.35s cubic-bezier(.4,0,.2,1);
    display: flex;
    flex-direction: column;
    border-left: 1px solid var(--border, #e0e0e0);
}

.filter-panel.active {
    right: 0;
}

.filter-panel-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border, #e0e0e0);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--bg-color, #fff);
}

.filter-panel-header h3 {
    margin: 0;
    font-size: 1.2em;
    color: var(--text-color, #222);
    display: flex;
    align-items: center;
    gap: 8px;
}

.close-filter {
    background: none;
    border: none;
    color: var(--text-color, #222);
    cursor: pointer;
    font-size: 1.3em;
    padding: 4px 8px;
}

.filter-panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 24px;
    background: var(--bg-color, #fff);
}

.filter-section {
    margin-bottom: 28px;
    background: var(--bg-color, #fff);
    border-radius: 8px;
    padding: 12px 0;
}

.filter-section h4 {
    margin: 0 0 12px 0;
    color: var(--text-color, #222);
    font-size: 1em;
    font-weight: 600;
}

.filter-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.filter-option {
    display: flex;
    align-items: center;
    padding: 7px 0 7px 2px;
    border-radius: 6px;
    cursor: pointer;
    transition: background 0.2s;
    color: var(--text-color, #222);
    font-size: 0.98em;
}

.filter-option:hover {
    background: var(--hover-color, #f5f5f5);
}

.filter-option input[type="radio"] {
    margin-right: 10px;
    accent-color: var(--accent-color, #2a7ae4);
}

.filter-option span {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-panel-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--border, #e0e0e0);
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    background: var(--bg-color, #fff);
}

.btn-limpar, .btn-aplicar {
    padding: 8px 18px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.97em;
    transition: all 0.2s;
}

.btn-limpar {
    background: var(--bg-color, #fff);
    color: var(--text-color, #222);
    border: 1px solid var(--border, #e0e0e0);
}

.btn-aplicar {
    background: var(--accent-color, #2a7ae4);
    color: #fff;
}

.btn-limpar:hover {
    background: var(--hover-color, #f5f5f5);
}

.btn-aplicar:hover {
    opacity: 0.92;
}

.btn-filtrar {
    display: flex;
    align-items: center;
    gap: 8px;
    position: relative;
}

.filter-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: var(--accent-color, #2a7ae4);
    color: #fff;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 0.7em;
    display: flex;
    align-items: center;
    justify-content: center;
}

@media (max-width: 600px) {
    .filter-panel {
        width: 100vw;
        right: -100vw;
        border-radius: 0;
    }
    .filter-panel.active {
        right: 0;
    }
    .filter-panel-content, .filter-panel-header, .filter-panel-footer {
        padding-left: 12px;
        padding-right: 12px;
    }
}

  /* Notificações */
  .notification-wrapper {
    position: relative;
    display: inline-block;
}
.icon-button {
    position: relative;
}
.notification-badge {
    position: absolute;
    top: 2px;
    right: 2px;
    background: #e53935;
    color: #fff;
    border-radius: 50%;
    font-size: 0.7em;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
}
.notification-dropdown {
    display: none;
    position: absolute;
    right: 0;
    top: 36px;
    width: 320px;
    background: var(--bg-color, #fff);
    box-shadow: 0 4px 24px rgba(0,0,0,0.13);
    border-radius: 8px;
    z-index: 1002;
    border: 1px solid var(--border, #e0e0e0);
    animation: fadeInNotif 0.18s;
}
@keyframes fadeInNotif {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}
.notification-dropdown .dropdown-header {
    padding: 14px 18px 10px 18px;
    font-weight: 600;
    border-bottom: 1px solid var(--border, #e0e0e0);
    color: var(--text-color, #222);
}
.notification-dropdown .dropdown-list {
    list-style: none;
    margin: 0;
    padding: 0;
    max-height: 220px;
    overflow-y: auto;
}
.notification-dropdown .dropdown-list li {
    padding: 12px 18px 8px 18px;
    border-bottom: 1px solid var(--border, #f0f0f0);
    font-size: 0.97em;
    cursor: pointer;
    transition: background 0.18s;
}
.notification-dropdown .dropdown-list li:hover {
    background: var(--hover-color, #f5f5f5);
}
.notif-title {
    font-weight: 500;
    color: var(--text-color, #222);
}
.notif-time {
    font-size: 0.85em;
    color: #888;
}
.notification-dropdown .dropdown-footer {
    padding: 10px 18px;
    text-align: right;
    background: var(--bg-color, #fff);
    border-top: 1px solid var(--border, #e0e0e0);
}
.notification-dropdown .dropdown-footer a {
    color: var(--accent-color, #2a7ae4);
    font-size: 0.97em;
    text-decoration: none;
}
@media (max-width: 600px) {
    .notification-dropdown {
        width: 98vw;
        right: -10vw;
    }
}

/* Estilos para os botões de comentar e responder */
.btn-comentar, .btn-responder {
    background-color: var(--primary);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 14px;
    transition: background-color 0.2s;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    border: none;
    cursor: pointer;
}

.btn-comentar:hover, .btn-responder:hover {
    background-color: var(--primary-light);
    text-decoration: none;
    color: white;
}
