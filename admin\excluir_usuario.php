<?php
session_start();
require_once('../conexao_POST.php');
require_once('../cadastros/includes/verify_admin.php');

// Verifica se é o super admin
verificarSuperAdmin($conexao, false);

// Verifica se o ID do usuário foi fornecido
if (!isset($_POST['user_id']) || empty($_POST['user_id'])) {
    $_SESSION['erro'] = "ID do usuário não fornecido.";
    header('Location: gerenciar_usuarios.php');
    exit();
}

$user_id = intval($_POST['user_id']);

// Não permite excluir o próprio usuário
if ($user_id == $_SESSION['idusuario']) {
    $_SESSION['erro'] = "Você não pode excluir seu próprio usuário.";
    header('Location: gerenciar_usuarios.php');
    exit();
}

// Inicia uma transação para garantir que todas as operações sejam concluídas ou nenhuma
pg_query($conexao, "BEGIN");

// Array para armazenar logs de operações
$log_operacoes = [];
$log_operacoes[] = "Iniciando exclusão do usuário ID: $user_id";

try {
    // Verificar se o usuário existe
    $query_check = "SELECT nome FROM appestudo.usuario WHERE idusuario = $1";
    $result_check = pg_query_params($conexao, $query_check, array($user_id));
    
    if (pg_num_rows($result_check) == 0) {
        throw new Exception("Usuário não encontrado");
    }
    
    $nome_usuario = pg_fetch_result($result_check, 0, 0);
    $log_operacoes[] = "Usuário encontrado: $nome_usuario (ID: $user_id)";
    
    // 1. Excluir registros relacionados em tabelas com chaves estrangeiras
    $tabelas_diretas = [
        "appestudo.assinaturas" => "usuario_id",
        "appestudo.anotacoes" => "usuario_id",
        "appestudo.flashcard_categories" => "usuario_id",
        "appestudo.flashcard_decks" => "usuario_id",
        "appestudo.flashcard_progress" => "usuario_id",
        "appestudo.forum_respostas" => "usuario_id",
        "appestudo.forum_topicos" => "usuario_id",
        "appestudo.kanban_etiquetas" => "usuario_id",
        "appestudo.kanban_tarefas" => "usuario_id",
        "appestudo.log_seguranca" => "usuario_id",
        "appestudo.marcacoes" => "usuario_id",
        "appestudo.notificacoes_usuarios" => "usuario_id",
        "appestudo.provas" => "usuario_id",
        "appestudo.revisoes" => "usuario_id",
        "appestudo.usuario_conteudo" => "usuario_id",
        "appestudo.usuario_dias_estudo" => "usuario_id",
        "appestudo.usuario_edital" => "usuario_id",
        "appestudo.usuario_has_curso" => "usuario_idusuario",
        "appestudo.agenda" => "usuario_idusuario",
        "appestudo.medicamento" => "usuario_idusuario",
        "appestudo.planejamento" => "usuario_idusuario"
    ];
    
    $total_registros_excluidos = 0;
    
    // Excluir registros relacionados
    foreach ($tabelas_diretas as $tabela => $campo) {
        // Verificar se a tabela existe
        $query_check_table = "SELECT EXISTS (
            SELECT FROM information_schema.tables 
            WHERE table_schema = 'appestudo' 
            AND table_name = '" . str_replace('appestudo.', '', $tabela) . "'
        )";
        $result_check_table = pg_query($conexao, $query_check_table);
        $table_exists = pg_fetch_result($result_check_table, 0, 0);
        
        if ($table_exists === 't') {
            $query = "DELETE FROM $tabela WHERE $campo = $1";
            $result = @pg_query_params($conexao, $query, array($user_id));
            
            if (!$result) {
                $error = pg_last_error($conexao);
                $log_operacoes[] = "Erro ao excluir registros de $tabela: $error";
                error_log("Erro ao excluir registros de $tabela para usuário $user_id: $error");
            } else {
                $rows_affected = pg_affected_rows($result);
                $total_registros_excluidos += $rows_affected;
                if ($rows_affected > 0) {
                    $log_operacoes[] = "$rows_affected registros excluídos de $tabela";
                }
            }
        }
    }
    
    // Tratamento especial para registros_uso (relacionados a medicamentos)
    $query_check_registros = "SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'appestudo' 
        AND table_name = 'registros_uso'
    )";
    $result_check_registros = pg_query($conexao, $query_check_registros);
    $registros_exists = pg_fetch_result($result_check_registros, 0, 0);
    
    if ($registros_exists === 't') {
        // Primeiro precisamos encontrar os medicamentos do usuário
        $query_medicamentos = "
            SELECT m.id 
            FROM appestudo.medicamentos m
            JOIN appestudo.registros_uso r ON m.id = r.medicamento_id
            WHERE r.id IN (
                SELECT r2.id
                FROM appestudo.registros_uso r2
                WHERE r2.observacao LIKE '%usuário $user_id%'
            )
        ";
        $result_medicamentos = @pg_query($conexao, $query_medicamentos);
        
        if ($result_medicamentos) {
            $medicamento_ids = [];
            while ($row = pg_fetch_assoc($result_medicamentos)) {
                $medicamento_ids[] = $row['id'];
            }
            
            if (!empty($medicamento_ids)) {
                $ids_string = implode(',', $medicamento_ids);
                $log_operacoes[] = "Encontrados medicamentos relacionados: $ids_string";
                
                $query_delete_registros = "DELETE FROM appestudo.registros_uso WHERE medicamento_id IN ($ids_string)";
                $result_delete_registros = @pg_query($conexao, $query_delete_registros);
                
                if ($result_delete_registros) {
                    $rows_affected = pg_affected_rows($result_delete_registros);
                    $total_registros_excluidos += $rows_affected;
                    $log_operacoes[] = "$rows_affected registros excluídos de appestudo.registros_uso";
                }
            }
        }
    }
    
    // 2. Excluir estudos relacionados ao usuário
    $query_estudos = "DELETE FROM appestudo.estudos WHERE planejamento_usuario_idusuario = $1";
    $result_estudos = @pg_query_params($conexao, $query_estudos, array($user_id));
    
    if ($result_estudos) {
        $rows_affected = pg_affected_rows($result_estudos);
        $total_registros_excluidos += $rows_affected;
        if ($rows_affected > 0) {
            $log_operacoes[] = "$rows_affected registros excluídos de appestudo.estudos";
        }
    }
    
    $log_operacoes[] = "Total de $total_registros_excluidos registros relacionados excluídos";
    
    // 3. Excluir o usuário
    $query_usuario = "DELETE FROM appestudo.usuario WHERE idusuario = $1";
    $result_usuario = pg_query_params($conexao, $query_usuario, array($user_id));
    
    if (!$result_usuario) {
        $error = pg_last_error($conexao);
        $log_operacoes[] = "Erro ao excluir o usuário: $error";
        throw new Exception("Erro ao excluir o usuário: $error");
    }
    
    $rows_affected = pg_affected_rows($result_usuario);
    
    if ($rows_affected == 0) {
        $log_operacoes[] = "Nenhum usuário foi excluído";
        throw new Exception("Nenhum usuário foi excluído");
    }
    
    $log_operacoes[] = "Usuário $nome_usuario (ID: $user_id) excluído com sucesso";
    
    // Confirma a transação
    pg_query($conexao, "COMMIT");
    $log_operacoes[] = "Transação confirmada com sucesso";
    
    // Registrar a operação no log do sistema
    $admin_id = $_SESSION['idusuario'];
    $admin_ip = $_SERVER['REMOTE_ADDR'];
    $detalhes = json_encode([
        'usuario_excluido' => $user_id,
        'nome_usuario' => $nome_usuario,
        'operacoes' => $log_operacoes
    ]);
    
    $query_log = "INSERT INTO appestudo.log_seguranca (usuario_id, tipo_evento, ip, detalhes) 
                 VALUES ($1, $2, $3, $4)";
    pg_query_params($conexao, $query_log, array($admin_id, 'exclusao_usuario', $admin_ip, $detalhes));
    
    $_SESSION['mensagem'] = "Usuário $nome_usuario excluído com sucesso!";
    header('Location: gerenciar_usuarios.php');
    exit();
    
} catch (Exception $e) {
    // Reverte a transação em caso de erro
    pg_query($conexao, "ROLLBACK");
    $log_operacoes[] = "Erro: " . $e->getMessage() . " - Transação revertida";
    
    // Registrar o erro no log do sistema
    $admin_id = $_SESSION['idusuario'];
    $admin_ip = $_SERVER['REMOTE_ADDR'];
    $detalhes = json_encode([
        'usuario_id' => $user_id,
        'erro' => $e->getMessage(),
        'operacoes' => $log_operacoes
    ]);
    
    $query_log = "INSERT INTO appestudo.log_seguranca (usuario_id, tipo_evento, ip, detalhes) 
                 VALUES ($1, $2, $3, $4)";
    pg_query_params($conexao, $query_log, array($admin_id, 'exclusao_usuario_falha', $admin_ip, $detalhes));
    
    $_SESSION['erro'] = "Erro ao excluir usuário: " . $e->getMessage();
    header('Location: gerenciar_usuarios.php');
    exit();
}
?>

<br>
<a href="gerenciar_usuarios.php" style="display: inline-block; padding: 10px 15px; background-color: #007bff; color: white; text-decoration: none; border-radius: 4px;">Voltar para Gerenciamento de Usuários</a>


