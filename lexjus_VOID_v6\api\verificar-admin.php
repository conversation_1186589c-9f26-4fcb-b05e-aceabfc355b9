<?php
/**
 * API para Verificar Permissões de Administrador
 * Verifica se o usuário atual tem permissões administrativas
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Tratar requisições OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Verificar se o usuário está logado
session_start();
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit();
}

// Incluir conexão existente
require_once '../../conexao_POST.php';

$usuario_id = $_SESSION['idusuario'];

try {
    $is_admin = verificarSeEAdmin($conexao, $usuario_id);
    
    echo json_encode([
        'sucesso' => true,
        'is_admin' => $is_admin,
        'usuario_id' => $usuario_id
    ]);
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['erro' => 'Erro interno do servidor: ' . $e->getMessage()]);
}

/**
 * Verifica se o usuário é administrador
 */
function verificarSeEAdmin($conexao, $usuario_id) {
    // Método 1: Verificar se é o usuário ID 1 (admin padrão)
    if ($usuario_id == 1) {
        return true;
    }
    
    // Método 2: Verificar se tem campo admin na tabela usuario
    $query_admin_campo = "
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_schema = 'appestudo' 
        AND table_name = 'usuario' 
        AND column_name IN ('admin', 'is_admin', 'tipo_usuario', 'nivel_acesso')";
    
    $result = pg_query($conexao, $query_admin_campo);
    
    if ($result && pg_num_rows($result) > 0) {
        $row = pg_fetch_assoc($result);
        $campo_admin = $row['column_name'];
        
        // Verificar valor do campo admin
        $query_verificar = "
            SELECT $campo_admin 
            FROM appestudo.usuario 
            WHERE idusuario = $1";
        
        $result_verificar = pg_query_params($conexao, $query_verificar, [$usuario_id]);
        
        if ($result_verificar && pg_num_rows($result_verificar) > 0) {
            $row_verificar = pg_fetch_assoc($result_verificar);
            $valor_admin = $row_verificar[$campo_admin];
            
            // Interpretar diferentes tipos de valores
            if ($campo_admin === 'admin' || $campo_admin === 'is_admin') {
                return $valor_admin === 't' || $valor_admin === true || $valor_admin === '1' || $valor_admin === 1;
            } elseif ($campo_admin === 'tipo_usuario') {
                return strtolower($valor_admin) === 'admin' || strtolower($valor_admin) === 'administrador';
            } elseif ($campo_admin === 'nivel_acesso') {
                return (int)$valor_admin >= 9; // Nível 9 ou 10 = admin
            }
        }
    }
    
    // Método 3: Verificar se existe tabela de permissões
    $query_tabela_permissoes = "
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'appestudo' 
        AND table_name IN ('usuario_permissoes', 'permissoes', 'roles', 'user_roles')";
    
    $result_permissoes = pg_query($conexao, $query_tabela_permissoes);
    
    if ($result_permissoes && pg_num_rows($result_permissoes) > 0) {
        $row_permissoes = pg_fetch_assoc($result_permissoes);
        $tabela_permissoes = $row_permissoes['table_name'];
        
        // Verificar permissões específicas
        if ($tabela_permissoes === 'usuario_permissoes') {
            $query_perm = "
                SELECT COUNT(*) as count
                FROM appestudo.usuario_permissoes 
                WHERE usuario_id = $1 
                AND (permissao LIKE '%admin%' OR permissao LIKE '%migra%' OR permissao = 'sistema_completo')";
            
            $result_perm = pg_query_params($conexao, $query_perm, [$usuario_id]);
            
            if ($result_perm) {
                $row_perm = pg_fetch_assoc($result_perm);
                return (int)$row_perm['count'] > 0;
            }
        }
    }
    
    // Método 4: Verificar por email/nome específicos (para casos especiais)
    $query_usuario_especial = "
        SELECT email, nome 
        FROM appestudo.usuario 
        WHERE idusuario = $1";
    
    $result_especial = pg_query_params($conexao, $query_usuario_especial, [$usuario_id]);
    
    if ($result_especial && pg_num_rows($result_especial) > 0) {
        $row_especial = pg_fetch_assoc($result_especial);
        $email = strtolower($row_especial['email'] ?? '');
        $nome = strtolower($row_especial['nome'] ?? '');
        
        // Emails/nomes que são considerados admin
        $admins_especiais = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ];
        
        $nomes_admin = [
            'admin',
            'administrador',
            'root',
            'developer',
            'dev'
        ];
        
        if (in_array($email, $admins_especiais) || in_array($nome, $nomes_admin)) {
            return true;
        }
    }
    
    // Método 5: Verificar se é o primeiro usuário cadastrado (além do ID 1)
    $query_primeiro_usuario = "
        SELECT MIN(idusuario) as primeiro_id 
        FROM appestudo.usuario 
        WHERE idusuario > 1";
    
    $result_primeiro = pg_query($conexao, $query_primeiro_usuario);
    
    if ($result_primeiro && pg_num_rows($result_primeiro) > 0) {
        $row_primeiro = pg_fetch_assoc($result_primeiro);
        $primeiro_id = (int)$row_primeiro['primeiro_id'];
        
        // Se for o primeiro usuário cadastrado (além do admin padrão), considerar admin
        if ($usuario_id === $primeiro_id) {
            return true;
        }
    }
    
    // Por padrão, não é admin
    return false;
}

/**
 * Função para definir um usuário como admin (para uso em scripts de setup)
 */
function definirComoAdmin($conexao, $usuario_id) {
    // Tentar adicionar campo admin se não existir
    $query_add_campo = "
        ALTER TABLE appestudo.usuario 
        ADD COLUMN IF NOT EXISTS admin BOOLEAN DEFAULT false";
    
    pg_query($conexao, $query_add_campo);
    
    // Definir como admin
    $query_set_admin = "
        UPDATE appestudo.usuario 
        SET admin = true 
        WHERE idusuario = $1";
    
    return pg_query_params($conexao, $query_set_admin, [$usuario_id]);
}

/**
 * Função para criar tabela de permissões se não existir
 */
function criarTabelaPermissoes($conexao) {
    $query_criar_tabela = "
        CREATE TABLE IF NOT EXISTS appestudo.usuario_permissoes (
            id SERIAL PRIMARY KEY,
            usuario_id INTEGER NOT NULL REFERENCES appestudo.usuario(idusuario) ON DELETE CASCADE,
            permissao VARCHAR(100) NOT NULL,
            data_concessao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(usuario_id, permissao)
        )";
    
    return pg_query($conexao, $query_criar_tabela);
}

/**
 * Função para conceder permissão específica
 */
function concederPermissao($conexao, $usuario_id, $permissao) {
    // Criar tabela se não existir
    criarTabelaPermissoes($conexao);
    
    $query_conceder = "
        INSERT INTO appestudo.usuario_permissoes (usuario_id, permissao)
        VALUES ($1, $2)
        ON CONFLICT (usuario_id, permissao) DO NOTHING";
    
    return pg_query_params($conexao, $query_conceder, [$usuario_id, $permissao]);
}
?>
