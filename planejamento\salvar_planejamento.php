<?php
//planejamento/salvar_planejamento.php
session_start();
include_once("../assets/config.php");

if (!$conexao) {
    die(json_encode(['erro' => 'Erro na conexão com o banco de dados']));
}

// Recebe os dados do formulário
$id_planejamento = $_POST['id_planejamento'];
$nome = $_POST['nome'];
$data_inicio = $_POST['data_inicio'];
$data_fim = $_POST['data_fim'];
$tempo_planejamento = $_POST['tempo_planejamento'];
$materias = json_decode($_POST['materias']);

// Inicia uma transação
pg_query($conexao, "BEGIN");

try {
    // Atualiza os dados do planejamento
    $query_atualizar = "UPDATE appEstudo.planejamento SET 
        nome = $1,
        data_inicio = $2,
        data_fim = $3,
        tempo_planejamento = $4
        WHERE idplanejamento = $5";
    
    $resultado = pg_query_params($conexao, $query_atualizar, [
        $nome,
        $data_inicio,
        $data_fim,
        $tempo_planejamento,
        $id_planejamento
    ]);

    if (!$resultado) {
        throw new Exception("Erro ao atualizar planejamento");
    }

    // Remove todas as matérias antigas
    $query_limpar = "DELETE FROM appEstudo.planejamento_materia WHERE planejamento_idplanejamento = $1";
    $resultado = pg_query_params($conexao, $query_limpar, [$id_planejamento]);

    if (!$resultado) {
        throw new Exception("Erro ao limpar matérias antigas");
    }

    // Insere as novas matérias selecionadas
    foreach ($materias as $id_materia) {
        $query_inserir = "INSERT INTO appEstudo.planejamento_materia 
            (planejamento_idplanejamento, materia_idmateria) VALUES ($1, $2)";
        $resultado = pg_query_params($conexao, $query_inserir, [$id_planejamento, $id_materia]);

        if (!$resultado) {
            throw new Exception("Erro ao inserir matéria");
        }
    }

    // Confirma a transação
    pg_query($conexao, "COMMIT");
    echo json_encode(['sucesso' => true]);

} catch (Exception $e) {
    // Em caso de erro, desfaz todas as alterações
    pg_query($conexao, "ROLLBACK");
    echo json_encode(['erro' => $e->getMessage()]);
}

pg_close($conexao);
?>