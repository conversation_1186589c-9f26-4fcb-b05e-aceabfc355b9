<?php
session_start();
require_once __DIR__ . '/../../conexao_POST.php';

// Verificar se o usuário está autenticado
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit;
}

$usuario_id = $_SESSION['idusuario'];

// Incluir as funções necessárias
require_once __DIR__ . '/../api/revisao.php';

header('Content-Type: application/json');

try {
    // Testar a função obterLeiAtualUsuario
    $lei_codigo = obterLeiAtualUsuario($conexao, $usuario_id);
    $lei_id = obterLeiId($conexao, $lei_codigo);
    
    // Verificar também na tabela de configurações do usuário
    $query_config = "
        SELECT lei_atual 
        FROM appestudo.lexjus_usuarios_config 
        WHERE usuario_id = $1
    ";
    
    $result_config = pg_query_params($conexao, $query_config, [$usuario_id]);
    $lei_config = null;
    
    if ($result_config && pg_num_rows($result_config) > 0) {
        $row_config = pg_fetch_assoc($result_config);
        $lei_config = $row_config['lei_atual'];
    }
    
    // Verificar todas as leis disponíveis
    $query_leis = "SELECT id, codigo, nome FROM appestudo.lexjus_leis ORDER BY codigo";
    $result_leis = pg_query($conexao, $query_leis);
    $leis_disponiveis = [];
    
    if ($result_leis) {
        while ($row = pg_fetch_assoc($result_leis)) {
            $leis_disponiveis[] = $row;
        }
    }
    
    // Verificar qual lei tem ID 3 (que está sendo usado incorretamente)
    $query_lei_id_3 = "SELECT codigo, nome FROM appestudo.lexjus_leis WHERE id = 3";
    $result_lei_id_3 = pg_query($conexao, $query_lei_id_3);
    $lei_id_3 = null;
    
    if ($result_lei_id_3 && pg_num_rows($result_lei_id_3) > 0) {
        $lei_id_3 = pg_fetch_assoc($result_lei_id_3);
    }
    
    echo json_encode([
        'sucesso' => true,
        'usuario_id' => $usuario_id,
        'lei_codigo' => $lei_codigo,
        'lei_id' => $lei_id,
        'lei_config_tabela' => $lei_config,
        'lei_id_3' => $lei_id_3,
        'leis_disponiveis' => $leis_disponiveis,
        'debug' => [
            'funcao_obterLeiAtualUsuario' => $lei_codigo,
            'funcao_obterLeiId' => $lei_id,
            'explicacao' => 'Se lei_codigo for sempre CPC, o problema está na função obterLeiAtualUsuario'
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'sucesso' => false,
        'erro' => $e->getMessage()
    ]);
}

pg_close($conexao);
?>
