<?php
// Inclua o arquivo de conexão com o banco de dados
include 'conexao_POST.php';

// Inclua o arquivo que fornece a próxima matéria estudada
include 'consulta_banco_ultima_proxima.php';

// Query para selecionar todas as matérias estudadas
$query = "SELECT * FROM appestudo.materias_estudadas ORDER BY nome ASC";
$result = pg_query($conexao, $query);

if (!$result) {
    echo "Erro na consulta.\n";
    exit;
}

$materias = array();
while ($materia = pg_fetch_assoc($result)) {
    $materias[] = $materia;
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Courier+Prime:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <style>
        body {
            font-family: 'Courier Prime', monospace;
        }
        .materia-box h4 {
            font-size: 2em; /* Ajuste o tamanho conforme necessário */
        }
        .destacada1 {
            background-color: #CCCCCC;
        }
    </style>
</head>
<body>
<div class="container">
    <?php foreach ($materias as $materia): ?>
        <?php
        $destacadaClass = ($proximaMateriaEstudada === $materia['nome']) ? 'destacada1' : '';
        $backgroundColor = ($destacadaClass !== '') ? '#CCCCCC' : '#f0f0f0';
        ?>
        <div class="materia-box <?= $destacadaClass ?>" style="background-color: <?= $backgroundColor ?>; padding: 20px; margin-bottom: 20px;">
            <h4 style="color: <?= $materia['cor'] ?>;"><?= $materia['nome'] ?></h4>
            <p><?= $materia['detalhe'] ?></p>
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="mostrarFormulario('formEditar<?= $materia['id'] ?>')">Editar</button>

            <div id="formEditar<?= $materia['id'] ?>" class="form-editar" style="display: none; margin-top: 20px;">
                <form action="modal_planejamento_editar.php" method="POST">
                    <input type="hidden" name="id" value="<?= $materia['id'] ?>">
                    <input type="hidden" name="materia" value="<?= $materia['nome'] ?>">
                    <div class="form-group">
                        <label for="nome<?= $materia['id'] ?>">Nome:</label>
                        <input type="text" class="form-control" id="nome<?= $materia['id'] ?>" name="nome" value="<?= $materia['nome'] ?>">
                    </div>
                    <div class="form-group">
                        <label for="detalhe<?= $materia['id'] ?>">Detalhe:</label>
                        <textarea class="form-control" id="detalhe<?= $materia['id'] ?>" name="detalhe" style="width: 100%; min-height: 400px;"><?= $materia['detalhe'] ?></textarea>
                    </div>
                    <button type="button" class="btn btn-secondary" onclick="esconderFormulario('formEditar<?= $materia['id'] ?>')">Cancelar</button>
                    <button type="submit" class="btn btn-primary">Salvar Mudanças</button>
                </form>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<script>
    function mostrarFormulario(id) {
        document.getElementById(id).style.display = 'block';
    }

    function esconderFormulario(id) {
        document.getElementById(id).style.display = 'none';
    }
</script>
</body>
</html>
