<?php
//atualizar_estudo.php

session_start();
if (!isset($_SESSION['idusuario']) || $_SESSION['idusuario'] != 1) {
    header("Location: login.php");
    exit();
}

include_once("assets/config.php");

$usuario_id = $_SESSION['idusuario'];

// Verifique se os dados do formulário foram enviados
if (isset($_POST['status_estudo'])) {
    foreach ($_POST['status_estudo'] as $conteudo_id => $status_estudo) {
        // Atualize o status de estudo para cada conteúdo
        $query_update = "
            UPDATE appestudo.usuario_conteudo 
            SET status_estudo = $1
            WHERE id = $2 AND usuario_id = $3
        ";

        $params = [$status_estudo, $conteudo_id, $usuario_id];
        $result = pg_query_params($conexao, $query_update, $params);

        if (!$result) {
            echo "Erro ao atualizar o status de estudo para o conteúdo ID: $conteudo_id";
            exit;
        }
    }
}

// Redirecione de volta para a página de progresso de estudo
header("Location: estudo_progresso.php");
exit();
