<?php
include 'processa_index.php';

// Consultar os dados do planejamento relacionado ao usuario logado
$query_consultar_idplanejamento = "SELECT p.idplanejamento
        FROM appEstudo.planejamento p 
        WHERE p.usuario_idusuario = $id_usuario";
$resultado_idplanejamento = pg_query($conexao, $query_consultar_idplanejamento);

if ($resultado_idplanejamento) {
    $row = pg_fetch_assoc($resultado_idplanejamento);
    $id_planejamento = $row['idplanejamento'];
} else {
    echo "Erro ao executar a consulta.";
}

// Consultar as matérias relacionadas ao planejamento do usuario logado
$query_consultar_materias = "SELECT m.* FROM appEstudo.materia m 
                                     INNER JOIN appEstudo.planejamento_materia pm ON m.idmateria = pm.materia_idmateria
                                     WHERE pm.planejamento_idplanejamento = $id_planejamento";
$resultado_materias = pg_query($conexao, $query_consultar_materias);

$materias = array();
while ($row = pg_fetch_assoc($resultado_materias)) {
    $materias[] = $row['nome'];
}

// Consultar os Cursos Registrados
$query_consultar_cursos = "SELECT c.nome 
                           FROM appEstudo.curso c
                           INNER JOIN appEstudo.usuario_has_curso uc
                           ON c.idcurso = uc.curso_idcurso
                           WHERE uc.usuario_idusuario = $id_usuario";
$resultado_cursos = pg_query($conexao, $query_consultar_cursos);

$cursos = array();
while ($row = pg_fetch_assoc($resultado_cursos)) {
    $cursos[] = $row['nome'];
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src='fullcalendar-6.1.8/dist/index.global.js'></script>
    <script src='fullcalendar-6.1.8/packages/core/locales/pt-br.global.js'></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Anton&family=Orbitron:wght@400;500;600;700;800;900&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Ysabeau+SC:wght@1;100;200;300;400;500;600;700;800;900;1000&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700&display=swap"
          rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courier+Prime:ital,wght@0,400;0,700;1,400;1,700&display=swap"
          rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.6/jquery.inputmask.min.js"></script>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css"
          integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js"
            integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy"
            crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js"></script>
    <title>Calendário - Agenda Pessoal</title>
    <link rel="stylesheet" href="css/enhanced-calendar.css">
    <script src="js/caminho_para_fullcalendar.js"></script>
</head>
<link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">



<body>

<div class="container">

    <div style="border: 2px solid #ccc; padding: 20px; border-radius: 10px; box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1); background-color: #f0f0f0;" id="header">
        <script>
            document.addEventListener("DOMContentLoaded", function () {
                var selectBox = document.getElementById("tipo_evento");
                selectBox.addEventListener("change", function () {
                    var selectedValue = selectBox.value;

                    if (selectedValue === "Planejamento") {
                        document.getElementById("campo_materia").style.display = "block";
                        document.getElementById("materia").setAttribute("required", "required");
                        document.getElementById("campo_titulo").style.display = "none";
                        document.getElementById("titulo").removeAttribute("required");
                    } else {
                        document.getElementById("campo_materia").style.display = "none";
                        document.getElementById("materia").removeAttribute("required");
                        document.getElementById("campo_titulo").style.display = "block";
                        document.getElementById("titulo").setAttribute("required", "required");
                    }
                });
            });
        </script>


        <?php
        echo "<h2 class='titulo'> Criar Evento na Agenda de $nome_usuario </h2>";
        ?>


        <form id="eventoForm" action="inserir_evento_agenda.php" method="POST">
            <label for="tipo_evento" class="titulo">Tipo de Evento:</label>
            <select id="tipo_evento" name="tipo_evento" class="campoTipo" required>
                <option value="">Selecione...</option>
                <option value="Faculdade">Faculdade</option>
                <option value="Trabalho">Trabalho</option>
                <option value="Concurso">Concurso</option>
                <option value="Pessoal">Pessoal</option>
                <option value="Planejamento">Planejamento</option>
            </select>

            <!-- Campo de entrada para "Matéria" -->
            <div id="campo_materia" style="display: none; margin-top: 10px;">
                <label for="materia" class="titulo">Matéria:</label>
                <input class="titulo" type="text" id="materia" name="materia" placeholder="Matéria do concurso"
                       list="materias-list" required>
                <datalist id="materias-list">
                    <?php foreach ($materias as $materia): ?>
                        <option value="<?php echo htmlspecialchars($materia); ?>"></option>
                    <?php endforeach; ?>
                </datalist>
            </div>

            <!-- Campo de entrada para "Título" -->
            <div id="campo_titulo" style="display: block;margin-top: 10px;">
                <label for="titulo" class="titulo">Título:</label>
                <input class="titulo" type="text" id="titulo" name="titulo" placeholder="Título do evento" required>
            </div>

            <div id="data_inicio" style="display: block;margin-top: 10px;">
                <label for="data_inicio" class="titulo">Dia:</label>
                <input class="titulo" type="date" id="data_inicio" name="data_inicio" required>
            </div>

            <div style="display: flex; align-items: center;">
                <label for="detalhes" class="titulo" style="margin-right: 10px;">Detalhes:</label>
                <textarea id="detalhes" name="detalhes" placeholder="Detalhes do evento" class="campoEstudado" style="flex: 1;"></textarea>
            </div>

            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 10px;">
                <button type="submit" class="btn btn-warning btn-sm" style="font-family: 'Courier Prime', monospace;">Criar Evento</button>

                <div>
                    <!-- Botão para voltar à página index.php com a font definida -->
                    <a href="#" class="btn btn-dark btn-sm btn-gold"
                       onclick="if (window.opener) { window.opener.location.reload(); } window.close(); return false;">Fechar Página</a>
                </div>
            </div>

        </form>




        <!-- Legenda de tipos de evento -->
        <div class="legenda-eventos" style="display: flex; justify-content: center; align-items: justify; margin-top: 10px;">
            <div class="legenda-item" style="margin-right: 20px;">
                <div class="legenda-cor" style="background-color: #1976D2;"></div>
                <div class="legenda-texto">Faculdade</div>

                <div class="legenda-cor" style="background-color: gray;"></div>
                <div class="legenda-texto">Trabalho</div>

                <div class="legenda-cor" style="background-color: blue;"></div>
                <div class="legenda-texto">Concurso</div>

                <div class="legenda-cor" style="background-color: #00796B;"></div>
                <div class="legenda-texto">Pessoal</div>

                <div class="legenda-cor" style="background-color: #FFD700;"></div>
                <div class="legenda-texto">Planejamento</div>

                <div class="legenda-cor legenda-pendente"></div>
                <div class="legenda-texto">Evento Pendente</div>

                <div class="legenda-cor legenda-realizado"></div>
                <div class="legenda-texto">Evento Realizado</div>
            </div>
        </div>

    </div>
</div>

<div style="display: flex; justify-content: center; align-items: center;" id="calendar-container">
    <div id="calendar"></div>
</div>
<!-- Local onde o calendário será renderizado -->


</body>
</html>
