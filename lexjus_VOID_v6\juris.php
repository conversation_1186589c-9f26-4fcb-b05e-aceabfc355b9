<?php
// Headers para evitar cache da página HTML
header('Cache-Control: no-cache, no-store, must-revalidate, max-age=0');
header('Pragma: no-cache');
header('Expires: Thu, 01 Jan 1970 00:00:00 GMT');

// Incluir sistema de cache busting
require_once 'includes/cache_buster.php';
include_once("../session_config.php");
require_once '../conexao_POST.php';

// Verificar se o usuário está logado
if (!verificarSessaoValida()) {
    header("Location: ../login_index.php");
    exit();
}

// Buscar nome do usuário
$query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = " . $_SESSION['idusuario'];
$resultado_nome = pg_query($conexao, $query_buscar_nome);
$nome_usuario = ($resultado_nome && pg_num_rows($resultado_nome) > 0)
    ? pg_fetch_assoc($resultado_nome)['nome']
    : "Usuário";
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LexJus - Jurisprudência</title>
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <meta name="app-version" content="<?php echo cache_version(); ?>">
    
    <!-- CSS Principal -->
    <link rel="stylesheet" href="<?php echo cache_css('style.css'); ?>">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: var(--light-bg);
            background-image:
                radial-gradient(circle at 20% 80%, rgba(0, 0, 139, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(57, 73, 171, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(74, 74, 74, 0.03) 0%, transparent 50%);
            min-height: 100vh;
            color: var(--text-primary);
            line-height: 1.6;
            transition: var(--transition);
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Header-barra - Mesmo estilo da index.php */
        .header-barra {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 7px 20px;
            border-bottom: 1px solid var(--border);
            box-shadow: var(--shadow-light);
            background: var(--card-bg);
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            transition: var(--transition);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo img {
            height: 50px;
            width: auto;
            transition: opacity 0.3s ease;
        }

        /* Controle de visibilidade dos logos */
        .logo .logo-light {
            opacity: 1;
        }

        .logo .logo-dark {
            opacity: 0;
            position: absolute;
        }

        [data-theme="dark"] .logo .logo-light {
            opacity: 0;
        }

        [data-theme="dark"] .logo .logo-dark {
            opacity: 1;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            background: var(--hover);
            color: var(--text-primary);
        }

        .user-info i {
            color: var(--primary);
            font-size: 1.2rem;
        }

        .user-name {
            color: var(--text);
            font-weight: 600;
        }

        .theme-toggle {
            display: flex;
            align-items: center;
        }

        .theme-btn {
            background: var(--card-bg);
            border: 2px solid var(--border);
            border-radius: 50%;
            width: 45px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
            color: var(--text-primary);
            font-size: 1.1rem;
            box-shadow: var(--shadow-light);
        }

        .theme-btn:hover {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        .theme-btn:active {
            transform: scale(0.95);
        }

        .back-btn {
            background: var(--card-bg);
            border: 2px solid var(--border);
            border-radius: 12px;
            padding: 8px 15px;
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            transition: var(--transition);
            color: var(--text-primary);
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            box-shadow: var(--shadow-light);
        }

        .back-btn:hover {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
            transform: translateY(-2px);
            box-shadow: var(--shadow-medium);
        }

        /* Seção Principal */
        .main-section {
            padding: 120px 0 60px;
            text-align: center;
        }

        .coming-soon-container {
            max-width: 600px;
            margin: 0 auto;
            padding: 60px 40px;
            background: var(--card-bg);
            border-radius: 20px;
            box-shadow: var(--shadow-light);
            border: 2px solid var(--border);
        }

        .coming-soon-icon {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: var(--primary-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            color: white;
            margin: 0 auto 30px;
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
        }

        .coming-soon-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 20px;
            color: var(--text-primary);
        }

        .coming-soon-subtitle {
            font-size: 1.2rem;
            opacity: 0.8;
            margin-bottom: 30px;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .coming-soon-description {
            font-size: 1rem;
            opacity: 0.7;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .coming-soon-container {
                margin: 0 20px;
                padding: 40px 30px;
            }

            .coming-soon-title {
                font-size: 2rem;
            }

            .coming-soon-subtitle {
                font-size: 1.1rem;
            }

            .coming-soon-icon {
                width: 100px;
                height: 100px;
                font-size: 2.5rem;
            }
        }

        @media (max-width: 480px) {
            .coming-soon-container {
                margin: 0 10px;
                padding: 30px 20px;
            }

            .coming-soon-title {
                font-size: 1.8rem;
            }

            .coming-soon-subtitle {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="header-barra">
        <div class="header-left">
            <a href="home_mae.php" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Voltar
            </a>
            <div class="logo">
                <img src="../logo/logo_vertical_azul.png" alt="Logo" class="logo-light">
                <img src="../logo/logo_vertical.png" alt="Logo" class="logo-dark">
            </div>
        </div>
        <div class="header-right">
            <div class="user-info">
                <i class="fas fa-user-circle"></i>
                <span class="user-name"><?php echo htmlspecialchars($nome_usuario ?? 'Usuário'); ?></span>
            </div>
            <div class="theme-toggle">
                <button id="themeToggle" class="theme-btn" title="Alternar tema">
                    <i class="fas fa-moon" id="themeIcon"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Seção Principal -->
    <section class="main-section">
        <div class="container">
            <div class="coming-soon-container">
                <div class="coming-soon-icon">
                    <i class="fas fa-gavel"></i>
                </div>
                <h1 class="coming-soon-title">Jurisprudência</h1>
                <p class="coming-soon-subtitle">
                    Em breve você terá acesso ao nosso sistema completo de jurisprudência
                </p>
                <p class="coming-soon-description">
                    Estamos trabalhando para trazer a você as principais decisões dos tribunais superiores, 
                    organizadas de forma intuitiva e com ferramentas avançadas de busca e estudo.
                </p>
            </div>
        </div>
    </section>

    <!-- Script do tema escuro -->
    <script>
        // Sistema de tema escuro/claro
        const themeToggle = document.getElementById('themeToggle');
        const themeIcon = document.getElementById('themeIcon');
        const body = document.body;

        // Verificar tema salvo
        const savedTheme = localStorage.getItem('theme') || 'light';
        body.setAttribute('data-theme', savedTheme);
        updateThemeIcon(savedTheme === 'dark');

        // Toggle do tema
        themeToggle.addEventListener('click', () => {
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateThemeIcon(newTheme === 'dark');
        });

        function updateThemeIcon(isDark) {
            themeIcon.className = isDark ? 'fas fa-sun' : 'fas fa-moon';
        }
    </script>
</body>
</html>
