<?php
//agenda_atualizar_estado_evento.php
ob_clean(); // Limpa qualquer output anterior
include_once("conexao_POST.php");

header('Content-Type: application/json'); // Define o tipo de resposta

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $eventoIdExcluir = $_POST['eventoIdexcluir'] ?? null;

    if ($eventoIdExcluir !== null) {
        try {
            $query_excluir = "DELETE FROM appEstudo.agenda WHERE id = $1 RETURNING id";
            $resultado_excluir = pg_query_params($conexao, $query_excluir, array($eventoIdExcluir));

            if ($resultado_excluir && pg_num_rows($resultado_excluir) > 0) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Evento excluído com sucesso'
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => 'Evento não encontrado'
                ]);
            }
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Erro ao excluir: ' . $e->getMessage()
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'ID do evento não fornecido'
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Método não permitido'
    ]);
}

exit;
?>