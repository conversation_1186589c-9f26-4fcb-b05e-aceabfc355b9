<?php
// desmarcar_replanejado.php
session_start();
require_once 'includes/verificar_modulo.php';
verificarModulo();

// Desativa a exibição de erros no output
ini_set('display_errors', 0);

// Define o tipo de conteúdo como JSON
header('Content-Type: application/json');

if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['success' => false, 'error' => 'Usuário não autenticado']);
    exit();
}

// Inclui a configuração
require_once 'assets/config.php';

$usuario_id = $_SESSION['idusuario'];

try {
    // Recebe e valida o JSON
    $jsonData = file_get_contents('php://input');
    if (!$jsonData) {
        throw new Exception('Dados não recebidos');
    }

    $data = json_decode($jsonData, true);
    if (!$data || !isset($data['conteudo_id'])) {
        throw new Exception('ID do conteúdo não fornecido');
    }

    $conteudo_id = intval($data['conteudo_id']);

    // Inicia a transação
    pg_query($conexao, "BEGIN");

    // 1. Atualiza o status do conteúdo para não estudado e limpa o campo replanejado_em
    $query_update = "UPDATE appestudo.usuario_conteudo 
                    SET status_estudo = 'Pendente', 
                        replanejado_em = NULL
                    WHERE id = $1 AND usuario_id = $2";

    $result_update = pg_query_params($conexao, $query_update, array(
        $conteudo_id,
        $usuario_id
    ));

    if (!$result_update) {
        throw new Exception('Erro ao atualizar status do conteúdo: ' . pg_last_error($conexao));
    }

    // Commit da transação
    pg_query($conexao, "COMMIT");

    // Retorna sucesso
    echo json_encode([
        'success' => true,
        'message' => 'Conteúdo desmarcado e cronograma atualizado!'
    ]);

} catch (Exception $e) {
    // Rollback em caso de erro
    if (isset($conexao)) {
        pg_query($conexao, "ROLLBACK");
    }

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} finally {
    if (isset($conexao)) {
        pg_close($conexao);
    }
}
?>