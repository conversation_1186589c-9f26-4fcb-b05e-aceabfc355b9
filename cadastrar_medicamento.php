<?php
session_start();
include_once("conexao_POST.php");

// Verificar sessão e autenticação
if (!isset($_SESSION['idusuario'])) {
    $_SESSION['validacao'] = false;
    echo '<script>
        alert("VOCÊ NÃO ESTÁ LOGADO!");
        window.location.href = "login_index.php";
    </script>';
    exit;
}

$id_usuario = $_SESSION['idusuario'];

// Buscar nome do usuário
try {
    $query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = $1";
    $resultado_nome = pg_query_params($conexao, $query_buscar_nome, array($id_usuario));

    if (!$resultado_nome || pg_num_rows($resultado_nome) === 0) {
        throw new Exception("Usuário não encontrado.");
    }

    $row = pg_fetch_assoc($resultado_nome);
    $nome_usuario = htmlspecialchars($row['nome']);

} catch (Exception $e) {
    die("Erro: " . $e->getMessage());
}

// Processar formulário
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    try {
        $nome_medicamento = filter_input(INPUT_POST, "nome", FILTER_SANITIZE_STRING);
        $detalhes_medicamento = filter_input(INPUT_POST, "detalhes", FILTER_SANITIZE_STRING);
        $data_inicio = filter_input(INPUT_POST, "data_inicio", FILTER_SANITIZE_STRING);
        $dosagem = filter_input(INPUT_POST, "dosagem", FILTER_SANITIZE_STRING);

        // Processar imagem se foi enviada
        $imagem = null;
        if (isset($_FILES["foto"]) && $_FILES["foto"]["size"] > 0) {
            $imagem = file_get_contents($_FILES["foto"]["tmp_name"]);
        }

        $query = "INSERT INTO appEstudo.medicamento (nome, detalhes, data_inicio, dosagem, usuario_idusuario, foto) 
                 VALUES ($1, $2, $3, $4, $5, $6)";

        $result = pg_query_params($conexao, $query, [
            $nome_medicamento,
            $detalhes_medicamento,
            $data_inicio,
            $dosagem,
            $id_usuario,
            pg_escape_bytea($imagem)
        ]);

        if (!$result) {
            throw new Exception("Erro ao inserir dados: " . pg_last_error());
        }

        header("Location: listar_medicamentos.php");
        exit();
    } catch (Exception $e) {
        $error_message = $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cadastro de Medicamento da Maya Rafaela</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: #f5f6fa;
            color: #2d3436;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: #6c5ce7;
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2em;
            margin: 0;
        }

        .container {
            max-width: 800px;
            margin: 40px auto;
            padding: 0 20px;
            flex-grow: 1;
        }

        .back-button {
            display: inline-block;
            padding: 10px 20px;
            background: #a29bfe;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-bottom: 20px;
            transition: background 0.3s;
        }

        .back-button:hover {
            background: #6c5ce7;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .welcome-message {
            background: #6c5ce7;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 25px;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #2d3436;
            font-weight: 500;
        }

        input[type="text"],
        input[type="date"],
        textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input[type="text"]:focus,
        input[type="date"]:focus,
        textarea:focus {
            border-color: #6c5ce7;
            outline: none;
        }

        .file-input-container {
            position: relative;
            margin-top: 10px;
        }

        .file-input-button {
            display: inline-block;
            padding: 12px 20px;
            background: #a29bfe;
            color: white;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .file-input-button:hover {
            background: #6c5ce7;
        }

        input[type="file"] {
            position: absolute;
            left: -9999px;
        }

        .submit-button {
            background: #6c5ce7;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: background 0.3s;
            width: 100%;
            margin-top: 20px;
        }

        .submit-button:hover {
            background: #5046b8;
        }

        .error-message {
            background: #ff7675;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        footer {
            background: #6c5ce7;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: auto;
        }

        @media (max-width: 768px) {
            .container {
                margin: 20px auto;
            }

            .card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
<header class="header">
    <h1>Cadastrar Medicamento - Maya Rafaela</h1>
</header>

<main class="container">
    <a href="medicamento.php" class="back-button">
        <i class="fas fa-arrow-left"></i> Voltar
    </a>

    <div class="card">
        <div class="welcome-message">
            Olá, <strong><?= $nome_usuario ?></strong>! Por favor, preencha o formulário abaixo para cadastrar um medicamento.
        </div>

        <?php if (isset($error_message)): ?>
            <div class="error-message">
                <i class="fas fa-exclamation-circle"></i> <?= htmlspecialchars($error_message) ?>
            </div>
        <?php endif; ?>

        <form method="post" enctype="multipart/form-data" onsubmit="return validateForm()">
            <div class="form-group">
                <label for="nome">
                    <i class="fas fa-pills"></i> Nome do Medicamento:
                </label>
                <input type="text" name="nome" id="nome" required>
            </div>

            <div class="form-group">
                <label for="detalhes">
                    <i class="fas fa-info-circle"></i> Detalhes:
                </label>
                <textarea name="detalhes" id="detalhes" rows="4"></textarea>
            </div>

            <div class="form-group">
                <label for="data_inicio">
                    <i class="fas fa-calendar-alt"></i> Data de Início:
                </label>
                <input type="date" name="data_inicio" id="data_inicio">
            </div>

            <div class="form-group">
                <label for="dosagem">
                    <i class="fas fa-prescription"></i> Dosagem:
                </label>
                <input type="text" name="dosagem" id="dosagem">
            </div>

            <div class="form-group">
                <label>
                    <i class="fas fa-image"></i> Imagem do Medicamento:
                </label>
                <div class="file-input-container">
                    <label for="foto" class="file-input-button">
                        <i class="fas fa-upload"></i> Escolher Imagem
                    </label>
                    <input type="file" name="foto" id="foto" accept="image/*">
                </div>
                <div id="file-name" style="margin-top: 10px; color: #666;"></div>
            </div>

            <button type="submit" class="submit-button">
                <i class="fas fa-save"></i> Cadastrar Medicamento
            </button>
        </form>
    </div>
</main>

<footer>
    <p>&copy; 2024 Sistema de Medicamentos Maya Rafaela</p>
</footer>

<script>
    // Mostrar nome do arquivo selecionado
    document.getElementById('foto').addEventListener('change', function(e) {
        const fileName = e.target.files[0]?.name || 'Nenhum arquivo selecionado';
        document.getElementById('file-name').textContent = fileName;
    });

    // Validação do formulário
    function validateForm() {
        const nome = document.getElementById('nome').value.trim();
        const dosagem = document.getElementById('dosagem').value.trim();

        if (nome === '') {
            alert('Por favor, preencha o nome do medicamento.');
            return false;
        }

        return true;
    }
</script>
</body>
</html>