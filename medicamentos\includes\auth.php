<?php
// includes/auth.php
session_start();

class Auth {
    private $conn;
    
    public function __construct() {
        require_once __DIR__ . '/../config/database.php';
        $database = new Database();
        $this->conn = $database->getConnection();
    }
    
    /**
     * Verifica as credenciais do usuário
     * 
     * @param string $usuario Nome de usuário
     * @param string $senha Senha do usuário
     * @return bool|array Retorna os dados do usuário se autenticado, false caso contrário
     */
    public function login($usuario, $senha) {
        $query = "SELECT idusuario, nome, usuario, email, is_admin FROM appestudo.usuario 
                  WHERE usuario = :usuario AND senha = :senha";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":usuario", $usuario);
        $stmt->bindParam(":senha", $senha);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }
        
        return false;
    }
    
    /**
     * Verifica se o usuário está logado
     * 
     * @return bool Retorna true se o usuário estiver logado, false caso contrário
     */
    public function estaLogado() {
        return isset($_SESSION['usuario_id']);
    }
    
    /**
     * Verifica se o usuário logado é administrador
     * 
     * @return bool Retorna true se o usuário for administrador, false caso contrário
     */
    public function isAdmin() {
        return isset($_SESSION['is_admin']) && $_SESSION['is_admin'] === true;
    }
    
    /**
     * Encerra a sessão do usuário
     */
    public function logout() {
        session_unset();
        session_destroy();
    }
}

/**
 * Função auxiliar para verificar se o usuário está logado
 * Redireciona para a página de login se não estiver
 */
function verificarLogin() {
    $auth = new Auth();
    if (!$auth->estaLogado()) {
        header("Location: login.php");
        exit;
    }
}
?>