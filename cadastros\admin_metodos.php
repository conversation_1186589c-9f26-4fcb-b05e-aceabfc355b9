<?php
session_start();
require_once("assets/config.php");
require_once('includes/verify_admin.php');

// Verifica se é admin
verificarAcessoAdmin($conexao, false);

// Headers de segurança
header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com https://code.jquery.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data:;");
header("X-Frame-Options: DENY");
header("X-Content-Type-Options: nosniff");
header("X-XSS-Protection: 1; mode=block");
header("Referrer-Policy: strict-origin-when-cross-origin");

if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Processar formulário de adição/edição
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
        die("Erro de validação CSRF");
    }

    if (isset($_POST['acao'])) {
        if ($_POST['acao'] === 'adicionar') {
            $nome = pg_escape_string($conexao, $_POST['nome']);
            $descricao = pg_escape_string($conexao, $_POST['descricao']);
            
            $query = "INSERT INTO appEstudo.metodo_estudo (nome, descricao) VALUES ('$nome', '$descricao')";
            if (pg_query($conexao, $query)) {
                $_SESSION['mensagem'] = "Método adicionado com sucesso!";
                $_SESSION['tipo_mensagem'] = "success";
            } else {
                $_SESSION['mensagem'] = "Erro ao adicionar método.";
                $_SESSION['tipo_mensagem'] = "danger";
            }
        }
        elseif ($_POST['acao'] === 'editar') {
            $id = (int)$_POST['id'];
            $nome = pg_escape_string($conexao, $_POST['nome']);
            $descricao = pg_escape_string($conexao, $_POST['descricao']);
            $ativo = isset($_POST['ativo']) ? 'true' : 'false';
            
            $query = "UPDATE appEstudo.metodo_estudo SET 
                     nome = '$nome', 
                     descricao = '$descricao',
                     ativo = $ativo,
                     data_atualizacao = CURRENT_TIMESTAMP
                     WHERE idmetodo = $id";
            if (pg_query($conexao, $query)) {
                $_SESSION['mensagem'] = "Método atualizado com sucesso!";
                $_SESSION['tipo_mensagem'] = "success";
            } else {
                $_SESSION['mensagem'] = "Erro ao atualizar método.";
                $_SESSION['tipo_mensagem'] = "danger";
            }
        }
        header("Location: admin_metodos.php");
        exit;
    }
}

// Buscar todos os métodos
$query = "SELECT * FROM appEstudo.metodo_estudo ORDER BY nome";
$resultado = pg_query($conexao, $query);
if (!$resultado) {
    die("Erro ao buscar métodos: " . pg_last_error($conexao));
}
$metodos = pg_fetch_all($resultado);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Métodos de Estudo</title>
    <link href="https://fonts.googleapis.com/css2?family=Varela+Round&family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert2/11.7.32/sweetalert2.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert2/11.7.32/sweetalert2.all.min.js"></script>
    <style>
        :root {
            --primary: #00008B;
            --primary-light: #3949ab;
            --secondary: #f8f0e3;
            --accent: #4a4a4a;
            --border: #e0e0e0;
            --text: #2c3e50;
            --active: #00008B;
            --hover: #f5f5f5;
            --primary-blue: #00008B;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --background: #f5f5f5;
            --card-background: white;
            --gradient-start: #00008B;
            --gradient-end: #0000CD;
        }

        [data-theme="dark"] {
            --primary: #4169E1;
            --secondary: #1a1a2e;
            --accent: #6c7293;
            --border: #2d2d42;
            --text: #e4e6f0;
            --active: #4169E1;
            --hover: #232338;
            --primary-blue: #4169E1;
            --shadow-color: rgba(0, 0, 0, 0.3);
            --background: #0a0a1f;
            --card-background: #13132b;
            --gradient-start: #4169E1;
            --gradient-end: #6495ED;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background: var(--background);
            color: var(--text);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            padding: 20px;
            box-shadow: 0 4px 12px var(--shadow-color);
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
            z-index: 1;
        }

        .header-left, .header-right {
            position: relative;
            z-index: 2;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .theme-toggle {
            cursor: pointer;
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .theme-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            cursor: pointer;
            padding: 12px;
            border-radius: 50%;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .theme-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .header_centro {
            text-align: center;
            margin: 30px 0;
            position: relative;
        }

        .header_centro h1 {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 20px;
            font-weight: 700;
            text-shadow: 2px 2px 4px var(--shadow-color);
        }

        .menu-icon {
            color: var(--primary);
            margin-bottom: 15px;
        }

        .menu-icon i {
            font-size: 4rem;
            color: var(--primary);
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(2px 2px 4px var(--shadow-color));
        }

        .metodo-card {
            background: var(--card-background);
            border-radius: 20px;
            box-shadow: 0 8px 16px var(--shadow-color);
            transition: all 0.3s ease;
            border: 1px solid var(--border);
            padding: 25px;
            margin-bottom: 0;
            position: relative;
            overflow: hidden;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .metodo-card form {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .metodo-card .btn-group {
            margin-top: auto;
            padding-top: 20px;
        }

        .metodo-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 5px;
            background: linear-gradient(90deg, var(--gradient-start), var(--gradient-end));
        }

        .metodo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 24px var(--shadow-color);
        }

        .metodo-card.inativo {
            opacity: 0.7;
        }

        .metodo-card h3 {
            color: var(--primary);
            font-size: 1.5rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .metodo-card h3 i {
            color: var(--primary);
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-family: 'Quicksand', sans-serif;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            margin: 5px;
            box-shadow: 0 4px 12px var(--shadow-color);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px var(--shadow-color);
            opacity: 0.9;
        }

        .btn i {
            font-size: 1.1rem;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--border);
            border-radius: 12px;
            background: var(--card-background);
            color: var(--text);
            font-size: 1rem;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        textarea.form-control {
            min-height: 100px;
            resize: vertical;
            font-family: inherit;
            line-height: 1.5;
        }

        textarea.form-control:focus {
            min-height: 120px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: var(--text);
            font-weight: 600;
            font-size: 0.95rem;
        }

        .form-check {
            display: flex;
            align-items: center;
            gap: 12px;
            margin: 20px 0;
            padding: 10px;
            background: var(--hover);
            border-radius: 12px;
        }

        /* Estilo do Toggle Switch */
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .slider:after {
            content: 'OFF';
            color: white;
            display: block;
            position: absolute;
            transform: translateX(0);
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 10px;
            font-weight: bold;
        }

        input:checked + .slider:after {
            content: 'ON';
        }

        .status-label {
            font-weight: 600;
            color: var(--text);
            margin-left: 10px;
        }

        .status-active {
            color: #28a745;
        }

        .status-inactive {
            color: #dc3545;
        }

        .alert {
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 30px;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            gap: 15px;
            animation: slideIn 0.3s ease;
        }

        @keyframes slideIn {
            from {
                transform: translateY(-20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .alert-success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .alert-danger {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            margin-top: 20px;
        }

        .row {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin: 0 -15px;
            padding: 0 15px;
        }

        .col-md-6 {
            padding: 0;
        }

        @media (max-width: 1200px) {
            .row {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .row {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .metodo-card {
                padding: 20px;
            }
        }

        /* Ajuste para o formulário de adicionar novo método */
        .form-add-metodo {
            grid-column: 1 / -1;
            margin-bottom: 30px;
        }

        .form-add-metodo .metodo-card {
            background: linear-gradient(135deg, var(--card-background), var(--hover));
        }

        .logo img {
            height: 50px;
            filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.2));
        }

        .btn-voltar {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
            min-width: 120px;
            background-color: var(--primary);
            color: white;
            text-decoration: none;
        }

        .btn-voltar:hover {
            background-color: var(--primary);
            transform: translateY(-2px);
            text-decoration: none;
        }

    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <div class="logo">
                <img src="../logo/logo_vertical.png" alt="Logo">
            </div>
        </div>
    </div>

    <div class="theme-toggle">
        <button id="theme-toggle-btn" class="theme-btn">
            <i id="theme-icon" class="fas fa-moon"></i>
        </button>
    </div>

    <div class="container">
        <!-- Botão Voltar -->
        <div style="margin: 20px 0;">
            <a href="index.php" class="btn-voltar">
                <i class="fas fa-arrow-left"></i> Voltar
            </a>
        </div>

        <div class="header_centro">
            <div class="menu-icon">
                <i class="fas fa-tasks fa-3x"></i>
            </div>
            <h1>Gerenciar Métodos de Estudo</h1>
        </div>

        <?php if (isset($_SESSION['mensagem'])): ?>
            <div class="alert alert-<?php echo $_SESSION['tipo_mensagem']; ?> alert-dismissible fade show" role="alert">
                <i class="fas <?php echo $_SESSION['tipo_mensagem'] === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?>"></i>
                <?php 
                echo $_SESSION['mensagem'];
                unset($_SESSION['mensagem']);
                unset($_SESSION['tipo_mensagem']);
                ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <!-- Formulário para adicionar novo método -->
        <div class="metodo-card form-add-metodo">
            <h3><i class="fas fa-plus-circle"></i> Adicionar Novo Método</h3>
            <form method="POST" class="row g-3">
                <input type="hidden" name="acao" value="adicionar">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <div class="col-md-6">
                    <label class="form-label">Nome do Método</label>
                    <input type="text" name="nome" class="form-control" required placeholder="Digite o nome do método">
                </div>
                <div class="col-md-6">
                    <label class="form-label">Descrição</label>
                    <textarea name="descricao" class="form-control" required placeholder="Digite a descrição do método"></textarea>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn">
                        <i class="fas fa-save"></i> Adicionar Método
                    </button>
                </div>
            </form>
        </div>

        <!-- Lista de métodos existentes -->
        <div class="row">
            <?php foreach ($metodos as $metodo): ?>
            <div class="col-md-6">
                <div class="metodo-card <?php echo $metodo['ativo'] === 't' ? '' : 'inativo'; ?>">
                    <form method="POST">
                        <input type="hidden" name="acao" value="editar">
                        <input type="hidden" name="id" value="<?php echo $metodo['idmetodo']; ?>">
                        <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                        
                        <div class="mb-3">
                            <label class="form-label">Nome</label>
                            <input type="text" name="nome" class="form-control" value="<?php echo htmlspecialchars($metodo['nome']); ?>" required>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Descrição</label>
                            <textarea name="descricao" class="form-control" required placeholder="Digite a descrição do método"><?php echo htmlspecialchars($metodo['descricao']); ?></textarea>
                        </div>
                        
                        <div class="form-check">
                            <label class="switch">
                                <input type="checkbox" name="ativo" id="ativo<?php echo $metodo['idmetodo']; ?>" 
                                    <?php echo $metodo['ativo'] === 't' ? 'checked' : ''; ?>>
                                <span class="slider"></span>
                            </label>
                            <span class="status-label <?php echo $metodo['ativo'] === 't' ? 'status-active' : 'status-inactive'; ?>">
                                <?php echo $metodo['ativo'] === 't' ? 'Método Ativo' : 'Método Inativo'; ?>
                            </span>
                        </div>

                        <script>
                            document.getElementById('ativo<?php echo $metodo['idmetodo']; ?>').addEventListener('change', function() {
                                const statusLabel = this.parentElement.nextElementSibling;
                                if (this.checked) {
                                    statusLabel.textContent = 'Método Ativo';
                                    statusLabel.classList.remove('status-inactive');
                                    statusLabel.classList.add('status-active');
                                } else {
                                    statusLabel.textContent = 'Método Inativo';
                                    statusLabel.classList.remove('status-active');
                                    statusLabel.classList.add('status-inactive');
                                }
                            });
                        </script>
                        
                        <div class="btn-group">
                            <button type="submit" class="btn">
                                <i class="fas fa-save"></i> Salvar Alterações
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Configuração do tema
            const themeToggleBtn = document.getElementById('theme-toggle-btn');
            if (themeToggleBtn) {
                themeToggleBtn.addEventListener('click', function() {
                    const currentTheme = document.documentElement.getAttribute('data-theme');
                    const newTheme = currentTheme === 'dark' ? '' : 'dark';
                    document.documentElement.setAttribute('data-theme', newTheme);
                    localStorage.setItem('theme', newTheme);
                    updateThemeIcon(newTheme);
                });
            }
        });

        // Função para atualizar o ícone do tema
        function updateThemeIcon(theme) {
            const icon = document.getElementById('theme-icon');
            if (icon) {
                icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }

        // Aplicar tema salvo ao carregar
        const savedTheme = localStorage.getItem('theme') || '';
        document.documentElement.setAttribute('data-theme', savedTheme);
        updateThemeIcon(savedTheme);
    </script>
</body>
</html> 