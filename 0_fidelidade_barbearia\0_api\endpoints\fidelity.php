<?php
/**
 * Endpoint de Fidelidade
 * Sistema de Fidelidade da Barbearia
 */

/**
 * Validar formato do ID do cliente
 * Formato esperado: CPF-CLIENTE (ex: 12345678901-CLIENTE)
 */
function validateClientId($clienteId) {
    return preg_match('/^[0-9]{11}-CLIENTE$/', $clienteId);
}

function handleFidelity($method, $action, $id, $input, $db) {
    switch ($method) {
        case 'GET':
            if ($action === 'score' && !empty($id)) {
                getClientScore($id, $db);
            } elseif ($action === 'history') {
                getServiceHistory($db);
            } elseif ($action === 'config') {
                getSystemConfig($db);
            } elseif ($action === 'stats') {
                getGeneralStats($db);
            } else {
                ApiResponse::error('Ação não especificada', 400);
            }
            break;
            
        case 'POST':
            if ($action === 'service') {
                addServicePoint($input, $db);
            } elseif ($action === 'score') {
                getClientScorePost($input, $db);
            } else {
                ApiResponse::error('Ação não especificada', 400);
            }
            break;
            
        case 'PUT':
            if ($action === 'remove-point') {
                removePoint($input, $db);
            } elseif ($action === 'reset-points') {
                resetCategoryPoints($input, $db);
            } elseif ($action === 'config') {
                updateSystemConfig($input, $db);
            } else {
                ApiResponse::error('Ação não especificada', 400);
            }
            break;
            
        default:
            ApiResponse::methodNotAllowed();
    }
}

/**
 * Buscar pontuação do cliente (GET)
 * Cliente ID agora no formato: CPF-CLIENTE
 */
function getClientScore($clienteId, $db) {
    // Validar formato do ID do cliente
    if (!validateClientId($clienteId)) {
        ApiResponse::validation(['cliente_id'], 'ID do cliente deve estar no formato CPF-CLIENTE');
    }

    try {
        $sql = "SELECT * FROM pontuacao_fidelidade WHERE cliente_id = ?";

        $stmt = $db->prepare($sql);
        $stmt->execute([$clienteId]);
        $score = $stmt->fetch();

        if (!$score) {
            // Criar registro de pontuação se não existir
            $sql = "INSERT INTO pontuacao_fidelidade (cliente_id, pontos_cabelo, pontos_barba, pontuacao_geral)
                    VALUES (?, 0, 0, 0)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$clienteId]);

            $score = [
                'cliente_id' => $clienteId,
                'pontos_cabelo' => 0,
                'pontos_barba' => 0,
                'pontuacao_geral' => 0,
                'data_atualizacao' => date('Y-m-d H:i:s')
            ];
        }

        ApiResponse::success($score);

    } catch (PDOException $e) {
        error_log("Get client score error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar pontuação');
    }
}

/**
 * Buscar pontuação do cliente (POST) - para IDs no novo formato
 * Cliente ID agora no formato: CPF-CLIENTE (sem espaços ou caracteres especiais)
 */
function getClientScorePost($input, $db) {
    if (empty($input['cliente_id'])) {
        ApiResponse::validation(['cliente_id'], 'Cliente ID é obrigatório');
    }

    // Validar formato do ID do cliente
    if (!validateClientId($input['cliente_id'])) {
        ApiResponse::validation(['cliente_id'], 'ID do cliente deve estar no formato CPF-CLIENTE');
    }

    try {
        $clienteId = $input['cliente_id'];
        $sql = "SELECT * FROM pontuacao_fidelidade WHERE cliente_id = ?";

        $stmt = $db->prepare($sql);
        $stmt->execute([$clienteId]);
        $score = $stmt->fetch();

        if (!$score) {
            // Criar registro de pontuação se não existir
            $sql = "INSERT INTO pontuacao_fidelidade (cliente_id, pontos_cabelo, pontos_barba, pontuacao_geral)
                    VALUES (?, 0, 0, 0)";
            $stmt = $db->prepare($sql);
            $stmt->execute([$clienteId]);

            $score = [
                'cliente_id' => $clienteId,
                'pontos_cabelo' => 0,
                'pontos_barba' => 0,
                'pontuacao_geral' => 0,
                'data_atualizacao' => date('Y-m-d H:i:s')
            ];
        }

        ApiResponse::success($score);

    } catch (PDOException $e) {
        error_log("Get client score POST error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar pontuação');
    }
}

/**
 * Adicionar ponto de atendimento
 * Cliente ID no formato: CPF-CLIENTE
 */
function addServicePoint($input, $db) {
    if (empty($input['cliente_id']) || empty($input['tipo_servico'])) {
        ApiResponse::validation(['cliente_id', 'tipo_servico'], 'Cliente ID e tipo de serviço são obrigatórios');
    }

    // Validar formato do ID do cliente
    if (!validateClientId($input['cliente_id'])) {
        ApiResponse::validation(['cliente_id'], 'ID do cliente deve estar no formato CPF-CLIENTE');
    }

    if (!in_array($input['tipo_servico'], ['cabelo', 'barba'])) {
        ApiResponse::validation(['tipo_servico'], 'Tipo de serviço deve ser "cabelo" ou "barba"');
    }

    try {
        $db->beginTransaction();
        
        // Registrar atendimento
        $dataAtendimento = $input['data_atendimento'] ?? date('Y-m-d H:i:s');

        $sql = "INSERT INTO historico_atendimentos (cliente_id, tipo_servico, data_atendimento)
                VALUES (?, ?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $input['cliente_id'],
            $input['tipo_servico'],
            $dataAtendimento
        ]);
        
        // Atualizar pontuação
        $sql = "INSERT INTO pontuacao_fidelidade (cliente_id, pontos_cabelo, pontos_barba, pontuacao_geral) 
                VALUES (?, 0, 0, 0) 
                ON DUPLICATE KEY UPDATE 
                pontos_cabelo = pontos_cabelo + IF(? = 'cabelo', 1, 0),
                pontos_barba = pontos_barba + IF(? = 'barba', 1, 0),
                pontuacao_geral = pontuacao_geral + 1,
                data_atualizacao = NOW()";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $input['cliente_id'],
            $input['tipo_servico'],
            $input['tipo_servico']
        ]);
        
        // Verificar se ganhou brinde
        $sql = "SELECT pontos_cabelo, pontos_barba FROM pontuacao_fidelidade WHERE cliente_id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$input['cliente_id']]);
        $pontos = $stmt->fetch();
        
        $metaCabelo = 10; // Buscar da configuração
        $metaBarba = 5;   // Buscar da configuração
        
        $brindeGanho = false;
        
        if ($input['tipo_servico'] === 'cabelo' && $pontos['pontos_cabelo'] >= $metaCabelo) {
            // Resetar pontos de cabelo
            $sql = "UPDATE pontuacao_fidelidade SET pontos_cabelo = 0 WHERE cliente_id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$input['cliente_id']]);
            
            // Adicionar brinde pendente
            $sql = "INSERT INTO brindes_pendentes (cliente_id, tipo_brinde, data_ganho) 
                    VALUES (?, 'Corte de Cabelo Grátis', NOW())";
            $stmt = $db->prepare($sql);
            $stmt->execute([$input['cliente_id']]);
            
            $brindeGanho = true;
        }
        
        if ($input['tipo_servico'] === 'barba' && $pontos['pontos_barba'] >= $metaBarba) {
            // Resetar pontos de barba
            $sql = "UPDATE pontuacao_fidelidade SET pontos_barba = 0 WHERE cliente_id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$input['cliente_id']]);
            
            // Adicionar brinde pendente
            $sql = "INSERT INTO brindes_pendentes (cliente_id, tipo_brinde, data_ganho) 
                    VALUES (?, 'Corte de Barba Grátis', NOW())";
            $stmt = $db->prepare($sql);
            $stmt->execute([$input['cliente_id']]);
            
            $brindeGanho = true;
        }
        
        $db->commit();
        
        ApiResponse::success([
            'brinde_ganho' => $brindeGanho
        ], 'Ponto adicionado com sucesso');
        
    } catch (PDOException $e) {
        $db->rollBack();
        error_log("Add service point error: " . $e->getMessage());
        ApiResponse::error('Erro ao adicionar ponto');
    }
}

/**
 * Remover ponto
 */
function removePoint($input, $db) {
    if (empty($input['cliente_id']) || empty($input['tipo_servico'])) {
        ApiResponse::validation(['cliente_id', 'tipo_servico'], 'Cliente ID e tipo de serviço são obrigatórios');
    }
    
    try {
        $campo = $input['tipo_servico'] === 'cabelo' ? 'pontos_cabelo' : 'pontos_barba';
        
        $sql = "UPDATE pontuacao_fidelidade 
                SET $campo = GREATEST($campo - 1, 0),
                    data_atualizacao = NOW()
                WHERE cliente_id = ?";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$input['cliente_id']]);
        
        ApiResponse::success(null, 'Ponto removido com sucesso');
        
    } catch (PDOException $e) {
        error_log("Remove point error: " . $e->getMessage());
        ApiResponse::error('Erro ao remover ponto');
    }
}

/**
 * Zerar pontos de uma categoria
 */
function resetCategoryPoints($input, $db) {
    if (empty($input['cliente_id']) || empty($input['tipo_servico'])) {
        ApiResponse::validation(['cliente_id', 'tipo_servico'], 'Cliente ID e tipo de serviço são obrigatórios');
    }
    
    try {
        $campo = $input['tipo_servico'] === 'cabelo' ? 'pontos_cabelo' : 'pontos_barba';
        
        $sql = "UPDATE pontuacao_fidelidade 
                SET $campo = 0,
                    data_atualizacao = NOW()
                WHERE cliente_id = ?";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$input['cliente_id']]);
        
        ApiResponse::success(null, 'Pontos zerados com sucesso');
        
    } catch (PDOException $e) {
        error_log("Reset points error: " . $e->getMessage());
        ApiResponse::error('Erro ao zerar pontos');
    }
}

/**
 * Buscar histórico de atendimentos
 */
function getServiceHistory($db) {
    $clienteId = $_GET['cliente_id'] ?? '';
    $limit = $_GET['limit'] ?? 50;
    
    if (empty($clienteId)) {
        ApiResponse::validation(['cliente_id'], 'Cliente ID é obrigatório');
    }
    
    try {
        $sql = "SELECT ha.*, u.nome as cliente_nome
                FROM historico_atendimentos ha
                JOIN usuarios u ON ha.cliente_id = u.id
                WHERE ha.cliente_id = ?
                ORDER BY ha.data_atendimento DESC
                LIMIT ?";

        $stmt = $db->prepare($sql);
        $stmt->execute([$clienteId, (int)$limit]);
        $history = $stmt->fetchAll();

        ApiResponse::success($history);
        
    } catch (PDOException $e) {
        error_log("Get service history error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar histórico');
    }
}

/**
 * Buscar configurações do sistema
 */
function getSystemConfig($db) {
    try {
        $config = [
            'meta_cabelo' => 10,
            'meta_barba' => 5
        ];
        
        ApiResponse::success($config);
        
    } catch (PDOException $e) {
        error_log("Get system config error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar configurações');
    }
}

/**
 * Atualizar configurações do sistema
 */
function updateSystemConfig($input, $db) {
    // Por enquanto, apenas retorna sucesso
    // Em uma implementação completa, salvaria em uma tabela de configurações
    ApiResponse::success(null, 'Configurações atualizadas com sucesso');
}

/**
 * Buscar estatísticas gerais
 */
function getGeneralStats($db) {
    try {
        $stats = [];
        
        // Total de clientes
        $sql = "SELECT COUNT(*) as total FROM usuarios WHERE tipo = 'cliente'";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $stats['total_clientes'] = $stmt->fetch()['total'];
        
        // Total de atendimentos
        $sql = "SELECT COUNT(*) as total FROM historico_atendimentos";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $stats['total_atendimentos'] = $stmt->fetch()['total'];
        
        // Brindes pendentes
        $sql = "SELECT COUNT(*) as total FROM brindes_pendentes";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $stats['brindes_pendentes'] = $stmt->fetch()['total'];
        
        ApiResponse::success($stats);
        
    } catch (PDOException $e) {
        error_log("Get general stats error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar estatísticas');
    }
}
?>
