<?php
//salvar_preferencias.php
session_start();
require_once("includes/init.php");

// Verificação de sessão
if (!isset($_SESSION['idusuario'])) {
    header("Location: login.php");
    exit();
}

// Verificação de CSRF
if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
    header("Location: configurar_preferencias.php?erro=Token inválido");
    exit();
}

$usuario_id = $_SESSION['idusuario'];
$dias = $_POST['dias'] ?? [];
$horas = $_POST['horas'] ?? [];
$minutos = $_POST['minutos'] ?? [];
$from_main = isset($_POST['from_main']) ? true : false;

try {
    // Iniciar transação
    pg_query($conexao, "BEGIN");

    // Primeiro, desativa todos os dias do usuário
    $query_desativar = "UPDATE appestudo.usuario_dias_estudo 
                       SET ativo = false 
                       WHERE usuario_id = $1";
    $result = pg_query_params($conexao, $query_desativar, array($usuario_id));

    if (!$result) {
        throw new Exception("Erro ao desativar dias existentes: " . pg_last_error($conexao));
    }

    // Verificar se pelo menos um dia foi selecionado
    if (empty($dias)) {
        throw new Exception("Selecione pelo menos um dia de estudo!");
    }

    // Para cada dia selecionado
    foreach ($dias as $dia_num) {
        $dia_num = (int)$dia_num;
        $horas_valor = isset($horas[$dia_num]) ? (int)$horas[$dia_num] : 0;
        $minutos_valor = isset($minutos[$dia_num]) ? (int)$minutos[$dia_num] : 0;
        
        // Validação dos valores
        if ($horas_valor < 0 || $horas_valor > 23 || $minutos_valor < 0 || $minutos_valor > 59) {
            throw new Exception("Valores de tempo inválidos para o dia $dia_num");
        }

        // Verificar se pelo menos um valor é maior que zero
        if ($horas_valor == 0 && $minutos_valor == 0) {
            throw new Exception("Defina pelo menos algumas horas ou minutos para os dias selecionados");
        }

        // Converter para formato decimal
        $horas_estudo = $horas_valor + ($minutos_valor / 60);
        
        // Verifica se já existe registro para este dia
        $query_check = "SELECT id FROM appestudo.usuario_dias_estudo 
                       WHERE usuario_id = $1 AND dia_semana = $2";
        $result = pg_query_params($conexao, $query_check, array($usuario_id, $dia_num));

        if (!$result) {
            throw new Exception("Erro ao verificar dia existente: " . pg_last_error($conexao));
        }
        
        if (pg_num_rows($result) > 0) {
            // Update
            $query = "UPDATE appestudo.usuario_dias_estudo 
                     SET horas_estudo = $1, ativo = true 
                     WHERE usuario_id = $2 AND dia_semana = $3";
            $result = pg_query_params($conexao, $query, array($horas_estudo, $usuario_id, $dia_num));
        } else {
            // Insert
            $query = "INSERT INTO appestudo.usuario_dias_estudo 
                     (usuario_id, dia_semana, horas_estudo, ativo) 
                     VALUES ($1, $2, $3, true)";
            $result = pg_query_params($conexao, $query, array($usuario_id, $dia_num, $horas_estudo));
        }

        if (!$result) {
            throw new Exception("Erro ao salvar configuração do dia $dia_num: " . pg_last_error($conexao));
        }
    }

    // Commit da transação
    pg_query($conexao, "COMMIT");

    // Registrar log da operação
    $log_query = "INSERT INTO appestudo.log_operacoes 
                  (usuario_id, operacao, detalhes) 
                  VALUES ($1, 'atualização_horarios', $2)";
    $log_detalhes = json_encode([
        'dias_configurados' => $dias,
        'data_modificacao' => date('Y-m-d H:i:s')
    ]);
    pg_query_params($conexao, $log_query, array($usuario_id, $log_detalhes));

    // Limpar token CSRF após uso bem-sucedido
    unset($_SESSION['csrf_token']);

    // Decidir para onde redirecionar baseado na origem
    if ($from_main) {
        $_SESSION['sucesso'] = "Horários de estudo atualizados com sucesso!";
        header("Location: plano_estudo_inteligente.php");
    } else {
        // Se veio do fluxo normal de configuração inicial
        if (isset($_SESSION['prova_temporaria_id'])) {
            $_SESSION['sucesso'] = "Configurações concluídas com sucesso! Seu plano está pronto.";
            header("Location: plano_estudo_inteligente.php");
        } else {
            $_SESSION['sucesso'] = "Preferências salvas com sucesso!";
            header("Location: configurar_preferencias.php");
        }
    }
    exit();

} catch (Exception $e) {
    // Rollback em caso de erro
    pg_query($conexao, "ROLLBACK");
    
    // Log do erro
    error_log("Erro ao salvar preferências: " . $e->getMessage());
    
    // Redirecionar com mensagem de erro
    $_SESSION['erro'] = "Erro ao salvar as preferências: " . $e->getMessage();
    header("Location: configurar_preferencias.php");
    exit();
}
?>