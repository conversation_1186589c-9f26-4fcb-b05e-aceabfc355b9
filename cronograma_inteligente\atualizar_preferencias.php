<?php
//configurar_preferencias.php
require_once 'includes/init.php';

// Definição de constantes
define('MAX_HORAS', 23);
define('MAX_MINUTOS', 59);

// Verificação de sessão
if (!isset($_SESSION['idusuario'])) {
    header('Location: login.php');
    exit();
}

// Verificação de CSRF
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$usuario_id = $_SESSION['idusuario'];

try {
    // Buscar nome do usuário com prepared statement
    $query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = $1";
    $resultado_nome = pg_query_params($conexao, $query_buscar_nome, array($usuario_id));

    if (!$resultado_nome) {
        throw new Exception("Erro ao buscar nome do usuário: " . pg_last_error($conexao));
    }

    if (pg_num_rows($resultado_nome) > 0) {
        $row = pg_fetch_assoc($resultado_nome);
        $nome_usuario = htmlspecialchars($row['nome']);
    } else {
        $nome_usuario = "Usuário";
    }

    // Buscar configurações atuais do usuário
    $query = "SELECT * FROM appestudo.usuario_dias_estudo WHERE usuario_id = $1 AND ativo = true";
    $result = pg_query_params($conexao, $query, array($usuario_id));

    if (!$result) {
        throw new Exception("Erro ao buscar configurações: " . pg_last_error($conexao));
    }

    $dias_configurados = [];
    while ($row = pg_fetch_assoc($result)) {
        $horas = floor($row['horas_estudo']);
        $minutos = round(($row['horas_estudo'] - $horas) * 60);
        $dias_configurados[$row['dia_semana']] = [
            'horas' => $horas,
            'minutos' => $minutos
        ];
    }

} catch (Exception $e) {
    error_log($e->getMessage());
    $erro = "Ocorreu um erro ao carregar as configurações. Por favor, tente novamente.";
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configurar Dias e Horas de Estudo</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="assets/css/toastr.min.css">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <style>
        :root {
            --primary: #00008B;
            --secondary: #f8f0e3;
            --accent: #4a4a4a;
            --border: #e0e0e0;
            --text: #2c3e50;
            --active: #00008B;
            --hover: #f5f5f5;
            --primary-blue: #00008B;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --background: #f5f5f5;
            --card-background: #ffffff;
            --header-background: #ffffff;
        }

        [data-theme="dark"] {
            --primary: #4169E1;
            --secondary: #1a1a2e;
            --accent: #e0e0e0;
            --border: #2a2a3a;
            --text: #e0e0e0;
            --active: #4169E1;
            --hover: #1a1a2e;
            --primary-blue: #4169E1;
            --shadow-color: rgba(0, 0, 0, 0.3);
            --background: #0a0a1a;
            --card-background: #1a1a2e;
            --header-background: #151525;
        }

        body {
            font-family: 'Quicksand', sans-serif;
            background-color: var(--background);
            color: var(--text);
            margin: 0;
            min-height: 100vh;
            box-sizing: border-box;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: var(--header-background);
            border-bottom: 1px solid var(--border);
            margin-bottom: 30px;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo img {
            height: 50px;
            width: auto;
        }

        .logo-dark {
            display: none;
        }

        [data-theme="dark"] .logo-light {
            display: none;
        }

        [data-theme="dark"] .logo-dark {
            display: block;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 15px;
            background: var(--hover);
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .user-info i {
            color: var(--primary);
            font-size: 1.2rem;
        }

        .user-name {
            color: var(--text);
            font-weight: 600;
        }

        h1 {
            font-family: 'Quicksand', sans-serif;
            text-align: center;
            color: var(--primary);
            font-size: 2rem;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .btn-voltar {
            position: fixed;
            top: 140px;
            left: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: white;
            border: 1px solid var(--border);
            border-radius: 50%;
            box-shadow: 0 2px 4px var(--shadow-color);
            color: var(--primary);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .btn-voltar:hover {
            background: var(--primary);
            color: white;
            transform: scale(1.1);
        }

        .flow-info {
            background: var(--card-background);
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid var(--border);
            box-shadow: 0 2px 4px var(--shadow-color);
            border-radius: 8px;
        }
        .steps-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
            max-width: 800px;
            margin: 0 auto;
            position: relative;
        }

        .steps-container::before {
            content: "";
            position: absolute;
            top: 25px;
            left: 50px;
            right: 50px;
            height: 2px;
            background: var(--border);
            z-index: 0;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            z-index: 1;
        }

        .step-number {
            width: 50px;
            height: 50px;
            background: white;
            border: 2px solid var(--border);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            color: var(--text);
        }

        .step.active .step-number {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        .step.completed .step-number {
            background: var(--accent);
            color: white;
            border-color: var(--accent);
        }

        .step-text {
            color: var(--text);
            font-size: 0.9rem;
            text-align: center;
        }

        .dias-estudo-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: var(--card-background);
            border-radius: 10px;
            box-shadow: 0 2px 4px var(--shadow-color);
            border: 1px solid var(--border);
        }

        .dia-config {
            display: flex;
            align-items: center;
            padding: 20px;
            border-bottom: 1px solid var(--border);
            transition: all 0.3s ease;
            margin-bottom: 10px;
            border-radius: 8px;
        }

        .dia-config:hover {
            background: var(--hover);
        }

        .dia-config label {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 15px;
            font-weight: 600;
            color: var(--text);
            cursor: pointer;
        }

        .tempo-container {
            display: flex;
            align-items: center;
            gap: 15px;
            background: var(--hover);
            padding: 10px 20px;
            border-radius: 8px;
            border: 1px solid var(--border);
            transition: all 0.3s ease;
        }

        .tempo-container.disabled {
            opacity: 0.5;
            pointer-events: none;
        }

        .tempo-input {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tempo-input input {
            width: 60px;
            padding: 8px;
            border: 1px solid var(--border);
            border-radius: 6px;
            text-align: center;
            font-size: 1rem;
            background: var(--card-background);
            color: var(--text);
        }

        .btn-submit {
            display: block;
            padding: 12px 24px;
            background: linear-gradient(135deg, var(--primary) 0%, #0000a3 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            text-align: center;
            border: none;
            cursor: pointer;
            margin: 20px auto;
            min-width: 200px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .theme-toggle {
            margin-left: 15px;
        }

        .theme-btn {
            background: transparent;
            border: none;
            color: var(--primary);
            cursor: pointer;
            font-size: 1.2rem;
            padding: 8px;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .dias-estudo-container {
                padding: 10px;
            }

            .dia-config {
                flex-direction: column;
                align-items: stretch;
            }

            .tempo-container {
                margin-top: 10px;
            }

            .steps-container {
                flex-direction: column;
                gap: 10px;
            }

            .steps-container::before {
                width: 2px;
                height: auto;
                top: 50px;
                bottom: 50px;
                left: 24px;
                right: auto;
            }

            .btn-voltar {
                top: 10px;
                left: 10px;
                width: 40px;
                height: 40px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <div class="logo">
                <img src="../logo/logo_vertical_azul.png" alt="Logo" class="logo-light">
                <img src="../logo/logo_vertical.png" alt="Logo" class="logo-dark">
            </div>
        </div>
        <div class="header-right">
            <div class="user-info">
                <i class="fas fa-user-circle"></i>
                <span class="user-name"><?php echo $nome_usuario; ?></span>
            </div>
            <div class="theme-toggle">
                <button id="theme-toggle-btn" class="theme-btn">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </div>
    </div>

    <a href="plano_estudo_inteligente.php" class="btn-voltar">
        <i class="fas fa-arrow-left"></i>
    </a>

    <div class="container">
        <h1>Configurar Dias e Horas de Estudo</h1>

        <div class="flow-info">
            <div class="steps-container">
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-text">Selecionar Edital</div>
                </div>
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-text">Selecionar Conteúdos</div>
                </div>
                <div class="step completed">
                    <div class="step-number"><i class="fas fa-check"></i></div>
                    <div class="step-text">Configurar Plano</div>
                </div>
                <div class="step active">
                    <div class="step-number">4</div>
                    <div class="step-text">Definir Horários</div>
                </div>
            </div>
        </div>

        <form method="POST" action="salvar_preferencias.php" id="form-preferencias">
            <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
            
            <div class="dias-estudo-container">
                <?php
                $dias = [
                    1 => 'Segunda-feira',
                    2 => 'Terça-feira',
                    3 => 'Quarta-feira',
                    4 => 'Quinta-feira',
                    5 => 'Sexta-feira',
                    6 => 'Sábado',
                    7 => 'Domingo'
                ];
                
                foreach ($dias as $num => $nome) {
                    $checked = isset($dias_configurados[$num]) ? 'checked' : '';
                    $horas = $dias_configurados[$num]['horas'] ?? 0;
                    $minutos = $dias_configurados[$num]['minutos'] ?? 0;
                    
                    echo "<div class='dia-config' id='dia-$num'>";
                    echo "<label>";
                    echo "<input type='checkbox' name='dias[]' value='$num' $checked class='checkbox-custom' onchange='toggleDia($num)'>";
                    echo "<i class='fas fa-calendar-day'></i> " . htmlspecialchars($nome);
                    echo "</label>";
                    
                    $disabled = $checked ? '' : 'disabled';
                    echo "<div class='tempo-container " . (!$checked ? 'disabled' : '') . "'>";
                    echo "<div class='tempo-input'>";
                    echo "<input type='number' name='horas[$num]' min='0' max='" . MAX_HORAS . "' value='$horas' 
                          onchange='validarTempo(this)' onblur='formatarNumero(this, " . MAX_HORAS . ")' $disabled>";
                    echo "<span>horas</span>";
                    echo "</div>";
                    echo "<div class='tempo-input'>";
                    echo "<input type='number' name='minutos[$num]' min='0' max='" . MAX_MINUTOS . "' value='$minutos' 
                          onchange='validarTempo(this)' onblur='formatarNumero(this, " . MAX_MINUTOS . ")' $disabled>";
                    echo "<span>minutos</span>";
                    echo "</div>";
                    echo "</div>";
                    echo "</div>";
                }
                ?>
            </div>
            <button type="submit" class="btn-submit">
            <i class="fas fa-sync-alt"></i> Atualizar Configuração
            </button>
        </form>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <script src="assets/js/toastr.min.js"></script>
    <script>
        let formAlterado = false;
        let submitting = false;

        function toggleDia(num) {
            const diaConfig = document.getElementById(`dia-${num}`);
            const checkbox = diaConfig.querySelector('input[type="checkbox"]');
            const inputs = diaConfig.querySelectorAll('input[type="number"]');
            const tempoContainer = diaConfig.querySelector('.tempo-container');
            
            if (!checkbox.checked) {
                tempoContainer.classList.add('disabled');
                inputs.forEach(input => {
                    input.disabled = true;
                    input.value = '00';
                });
            } else {
                tempoContainer.classList.remove('disabled');
                inputs.forEach(input => {
                    input.disabled = false;
                });
            }
            
            formAlterado = true;
        }

        function validarTempo(input) {
            let value = parseInt(input.value);
            const min = parseInt(input.min);
            const max = parseInt(input.max);

            if (isNaN(value)) value = 0;
            if (value < min) value = min;
            if (value > max) value = max;

            input.value = value;
            formAlterado = true;
        }

        function formatarNumero(input, max) {
            let value = parseInt(input.value);
            if (isNaN(value)) value = 0;
            if (value < 0) value = 0;
            if (value > max) value = max;
            input.value = value.toString().padStart(2, '0');
        }

        document.addEventListener('DOMContentLoaded', () => {
            const themeToggleBtn = document.getElementById('theme-toggle-btn');
            const themeIcon = themeToggleBtn.querySelector('i');
            
            const currentTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', currentTheme);
            updateIcon(currentTheme);
            
            themeToggleBtn.addEventListener('click', () => {
                const currentTheme = document.documentElement.getAttribute('data-theme');
                const newTheme = currentTheme === 'light' ? 'dark' : 'light';
                
                document.documentElement.setAttribute('data-theme', newTheme);
                localStorage.setItem('theme', newTheme);
                updateIcon(newTheme);
            });
            
            function updateIcon(theme) {
                if (theme === 'dark') {
                    themeIcon.classList.remove('fa-moon');
                    themeIcon.classList.add('fa-sun');
                } else {
                    themeIcon.classList.remove('fa-sun');
                    themeIcon.classList.add('fa-moon');
                }
            }

            // Validação do formulário
            const form = document.querySelector('#form-preferencias');
            form.addEventListener('submit', function(event) {
                submitting = true;
                let valid = true;
                let diasComErro = [];
                const dias = document.querySelectorAll('.dia-config');

                dias.forEach(dia => {
                    const checkbox = dia.querySelector('input[type="checkbox"]');
                    if (checkbox.checked) {
                        const horasInput = dia.querySelector('input[name^="horas"]');
                        const minutosInput = dia.querySelector('input[name^="minutos"]');
                        const nomeDia = dia.querySelector('label').textContent.trim();
                        
                        const horas = parseInt(horasInput.value) || 0;
                        const minutos = parseInt(minutosInput.value) || 0;

                        if (horas === 0 && minutos === 0) {
                            valid = false;
                            horasInput.classList.add('input-error');
                            minutosInput.classList.add('input-error');
                            diasComErro.push(nomeDia);
                        }
                    }
                });

                if (!valid) {
                    event.preventDefault();
                    submitting = false;
                    toastr.error(`Por favor, insira pelo menos horas ou minutos para os seguintes dias: ${diasComErro.join(', ')}`);
                }
            });

            // Aviso de alterações não salvas
            window.addEventListener('beforeunload', function(e) {
                if (formAlterado && !submitting) {
                    e.preventDefault();
                    e.returnValue = '';
                }
            });

            // Configurações do toastr
            toastr.options = {
                "closeButton": true,
                "progressBar": true,
                "positionClass": "toast-top-right",
                "timeOut": "5000"
            };
        });
    </script>
</body>
</html>