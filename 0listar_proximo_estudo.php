<?php
//0listar_proximo_estudo.php

?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <title>Próxima <PERSON></title>
    <style>
        .proxima-materia {
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0, 86, 179, 0.42);
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .proxima-materia:hover {
            transform: translateY(-3px);
        }

        .card-header-proxima {
            background: linear-gradient(135deg, #2978B4, rgba(0, 86, 179, 0.42));
            padding: 1.5rem;
            color: white;
        }

        .header-content {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .titulo-proxima {
            font-size: 1.2rem;
            font-weight: 600;
            letter-spacing: 0.5px;
            opacity: 0.9;
        }

        .materia-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255, 255, 255, 0.15);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 1.1rem;
            font-weight: 500;
            backdrop-filter: blur(5px);
        }

        .content-row {
            display: flex;
            flex-wrap: wrap;
            gap: 0.75rem;
            margin: 1rem 0;
        }

        .info-chip {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: #f0f7ff;
            padding: 0.5rem 1rem;
            border-radius: 15px;
            font-size: 0.9rem;
            color: #2193b0;
            border: 1px solid rgba(33, 147, 176, 0.2);
        }

        .ponto-estudado {
            background: var(--houver);
            border: 1px solid #e1e8ed;
            border-radius: 12px;
            padding: 1rem;
            margin: 1rem 0;
        }

        .ponto-label {
            font-size: 0.85rem;
            color: #687684;
            margin-bottom: 0.5rem;
        }

        .ponto-texto {
            font-size: 1rem;
            color: #2193b0;
            line-height: 1.4;
        }

        .tempo-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
            margin: 1rem 0;
        }

        .tempo-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            background: #f8fafb;
            padding: 1rem;
            border-radius: 12px;
            transition: background-color 0.2s ease;
        }

        .tempo-item:hover {
            background: #f0f7ff;
        }

        .tempo-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border-radius: 10px;
            color: #2193b0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }

        .tempo-info {
            flex: 1;
        }

        .tempo-label-proximo {
            font-size: 0.8rem;
            color: #687684;
            margin-bottom: 0.25rem;
        }

        .tempo-valor {
            font-size: 1rem;
            color: #2193b0;
            font-weight: 500;
        }

        .tempo-valor.highlight {
            color: #2193b0;
            font-weight: 600;
        }

        .periodo-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: #f8fafb;
            padding: 1rem;
            border-radius: 12px;
            margin-top: 1rem;
        }

        .periodo-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            color: #687684;
        }

        .periodo-separator {
            color: #2193b0;
        }

        /* Estado de Atenção */
        .atencao-card {
            background: #fff4e5;
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
        }

        .atencao-header {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            color: #ff6b00;
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 1rem;
        }

        .atencao-materia {
            font-size: 1.2rem;
            color: #1a2634;
            font-weight: 600;
            margin-bottom: 1rem;
            padding: 0.75rem;
            background: white;
            border-radius: 8px;
        }

        .atencao-mensagem {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            color: #ff6b00;
            margin-bottom: 1rem;
        }

        .atencao-action {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: #ff6b00;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 20px;
            font-weight: 500;
        }

        /* Estado Vazio */
        .empty-state {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
        }

        .empty-icon {
            width: 60px;
            height: 60px;
            background: #f0f7ff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: #2193b0;
        }

        .status-message.error {
            color: #ff6b00;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            margin-top: 1rem;
            font-weight: 500;
        }

        @media (max-width: 600px) {
  .tempo-grid {
    grid-template-columns: 1fr;
  }
  .content-row {
    flex-direction: column;
  }
  .proxima-materia {
    border-radius: 8px;
    padding: 1rem;
  }
}

        @media (max-width: 768px) {
            .tempo-grid {
                grid-template-columns: 1fr;
            }

            .content-row {
                flex-direction: column;
            }

            .periodo-info {
                flex-direction: column;
                gap: 1rem;
            }

            .periodo-separator {
                transform: rotate(90deg);
            }
        }
    </style>
</head>
<body>

<?php if ($semRegistro === true): ?>
    <div class="proxima-materia empty-state">
        <div class="empty-icon">
            <i class="fas fa-book-reader"></i>
        </div>
        <div class="titulo-proxima">Próxima Matéria a Estudar</div>
        <div class="status-message error">
            <i class="fas fa-exclamation-circle"></i>
            Não Iniciou o Estudo!
        </div>
    </div>
<?php else: ?>
    <div class="proxima-materia"
         data-estudo='<?php echo htmlspecialchars(json_encode(["nome_materia" => $proximaMateriaEstudada]), ENT_QUOTES, 'UTF-8'); ?>'
         style="cursor: pointer;">
        <div class="card-header-proxima">
            <div class="header-content">
                <div class="titulo-proxima">Próxima Matéria</div>
                <?php if ($resultado_proxima_materia && pg_num_rows($resultado_proxima_materia) > 0): ?>
                    <?php if ($resultado_estudo_proxima_materia_0 == true && $linha_estudo_proxima_materia['hora_inicio'] != null): ?>
                        <div class="materia-badge">
                            <i class="fas fa-bookmark"></i>
                            <?php echo $proximaMateriaEstudada; ?>
                        </div>

                        <div class="card-content-proxima">
                            <!-- Curso e Método na mesma linha -->
                            <div class="content-row">
                                <div class="info-chip">
                                    <i class="fas fa-graduation-cap"></i>
                                    <?php echo $linha_estudo_proxima_materia['nome_curso']; ?>
                                </div>
                                <div class="info-chip">
                                    <i class="fas fa-tasks"></i>
                                    <?php echo $linha_estudo_proxima_materia['metodo']; ?>
                                </div>
                            </div>

                            <!-- Último ponto estudado -->
                            <div class="ponto-estudado">
                                <div class="ponto-label">Último Ponto Estudado</div>
                                <div class="ponto-texto"><?php echo $linha_estudo_proxima_materia['ponto_estudado']; ?></div>
                            </div>

                            <!-- Grade de tempo -->
                            <div class="tempo-grid">
                                <div class="tempo-item">
                                    <div class="tempo-icon"><i class="fas fa-clock"></i></div>
                                    <div class="tempo-info">
                                        <div class="tempo-label-proximo">Tempo Bruto</div>
                                        <div class="tempo-valor"><?php echo $linha_estudo_proxima_materia['tempo_bruto']; ?></div>
                                    </div>
                                </div>
                                <div class="tempo-item">
                                    <div class="tempo-icon"><i class="fas fa-stopwatch"></i></div>
                                    <div class="tempo-info">
                                        <div class="tempo-label-proximo">Tempo Líquido</div>
                                        <div class="tempo-valor highlight"><?php echo $linha_estudo_proxima_materia['tempo_liquido']; ?></div>
                                    </div>
                                </div>
                            </div>

                            <div class="periodo-info">
                                <div class="periodo-item">
                                    <i class="fas fa-hourglass-start"></i>
                                    <span><?php echo $linha_estudo_proxima_materia['hora_inicio']; ?></span>
                                </div>
                                <div class="periodo-separator">
                                    <i class="fas fa-arrow-right"></i>
                                </div>
                                <div class="periodo-item">
                                    <i class="fas fa-hourglass-end"></i>
                                    <span><?php echo $linha_estudo_proxima_materia['hora_fim']; ?></span>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="atencao-card">
                            <div class="atencao-header">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span>ATENÇÃO</span>
                            </div>
                            <div class="atencao-materia">
                                <?php echo $proximaMateriaEstudada ?? $proximoMateriaNome; ?>
                            </div>
                            <div class="atencao-mensagem">
                                <?php if ($proximaMateriaEstudada === null): ?>
                                    <i class="fas fa-history"></i>
                                    Muito Tempo sem Estudar
                                <?php else: ?>
                                    <i class="fas fa-play-circle"></i>
                                    Estudo Não Iniciado
                                <?php endif; ?>
                            </div>
                            <div class="atencao-action">
                                <i class="fas fa-flag"></i>
                                Comece ou Revise!
                            </div>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
<?php endif; ?>

</body>
</html>