<?php
//plano_estudo_inteligente.php

require_once 'includes/init.php';
require_once 'includes/verificar_modulo.php';
verificarModulo();
require_once 'includes/mensagens.php';

require_once 'includes/calculos.php';
require_once 'includes/plano_estudo_logic.php';


// Primeiro inicialize o planoData
$planoData = new PlanoEstudoLogic($conexao);
$prova = $planoData->getProvaAtiva();

// Adicione no início do arquivo, após as require_once
error_reporting(E_ALL);
ini_set('display_errors', 1);

// ADICIONE O NOVO CÓDIGO AQUI
$ajuste = 0;
$data_inicio = new DateTime($prova['data_inicio_estudo']);
$primeiro_dia_semana = intval($data_inicio->format('N'));



// Adicione no início do arquivo, após as require_once
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Após inicializar $planoData
echo "<!-- Debug Prova: ";
var_export($prova);
echo " -->";

// Após getConteudos()
echo "<!-- Debug Conteudos: ";
var_export($conteudos);
echo " -->";

// Após $planoData->organizarConteudosPorSemana()
echo "<!-- Debug ConteudosPorSemana: ";
var_export($conteudosPorSemana);
echo " -->";

// Após a consulta SQL em getConteudos()
function debugQuery($conexao) {
    $error = pg_last_error($conexao);
    echo "<!-- SQL Error: " . htmlspecialchars($error) . " -->";
}



$usuario_id = $_SESSION['idusuario'];

// Inicializa dados do plano de estudo usando a classe PlanoEstudoLogic
$planoData = new PlanoEstudoLogic($conexao);
$prova = $planoData->getProvaAtiva();
$id_planejamento = $planoData->getPlanejamentoAtivo(); // Adicione esta linha
$info_estudo = $planoData->getInfoEstudo();
$conteudos = $planoData->getConteudos();
$conteudos = filtrarTodosConteudos($conteudos);
$conteudosPorSemana = $planoData->organizarConteudosPorSemanaFiltrados();

$conteudosFiltrados = filtrarTodosConteudos($conteudos);
$total_conteudos = count($conteudosFiltrados);
$semanas_datas = $planoData->gerarSemanasDatas(); // Adicionar esta linha

// Calcula informações baseadas nos dados obtidos
$calculo = new Calculos($prova, count($conteudosFiltrados), $conexao);
$dias_para_prova = $calculo->getDiasParaProva();
$dias_uteis_totais = $calculo->getDiasUteis();
$dias_estudo = $calculo->getDiasEstudo();
$cards_por_dia = [];

foreach ($dias_estudo as $dia) {
    $cards_por_dia[$dia] = $calculo->getCardsPorDia($dia);
}
$cardsPerDay = max($cards_por_dia); // para manter a lógica de tempo_apertado
$totalSemanas = $calculo->getTotalSemanas();


$total_dias = $calculo->getTotalDias();


//$qtd_dias = $planoData->getQuantidadeDiasEstudo();
//$totalSemanas = $info_estudo['semanas_necessarias'];
//$dias_uteis_totais = $qtd_dias * $totalSemanas;

// Verificar se o tempo está apertado
$tempo_apertado = ($cardsPerDay >= 8);

// Função para formatar data em português
function formatarDataPtBr($data) {
    return $data->format('d/m/Y');
}

// Captura o dia da semana inicial
// Primeiro, defina as datas das semanas

// Definir os dias da semana baseado nas preferências do usuário
$dias = [];
$nomes_dias = [
    1 => 'Segunda-feira',
    2 => 'Terça-feira',
    3 => 'Quarta-feira',
    4 => 'Quinta-feira',
    5 => 'Sexta-feira',
    6 => 'Sábado',
    7 => 'Domingo'
];

foreach ($dias_estudo as $dia) {
    $dias[] = $nomes_dias[$dia];
}

// Se não houver dias configurados, usar padrão (seg-sex)
if (empty($dias)) {
    $dias = array_slice($nomes_dias, 0, 5);
}


// Agora podemos calcular os cards atrasados
$hoje = new DateTime();
$hoje->setTime(0, 0, 0);
$cards_atrasados = [];

// No loop onde os cards atrasados são calculados:
foreach ($conteudosPorSemana as $semana_index => $semana) {
    foreach ($semana as $dia_index => $dia) {
        foreach ($dia as $conteudo) {
            // Verifica se a data existe antes de clonar
            if (isset($semanas_datas[$semana_index]['datas'][$dia_index])) {
                $data_dia = clone $semanas_datas[$semana_index]['datas'][$dia_index];
                $data_dia->setTime(0, 0, 0);

                if ($data_dia < $hoje && $conteudo['status_estudo'] !== 'Estudado') {
                    $conteudo['data_prevista'] = $data_dia->format('d/m/Y');
                    $cards_atrasados[] = $conteudo;
                }
            }
        }
    }
}

// Calcular dias restantes até a prova
$hoje = new DateTime();
$data_prova = new DateTime($prova['data_prova']);

// Zera as horas para comparação apenas das datas
$data_prova->setTime(0, 0, 0);
$hoje->setTime(0, 0, 0);

// Calcula a diferença em dias
//$interval = $hoje->diff($data_prova);
//$dias_ate_prova_autal = $interval->invert ? 0 : ($interval->days + 1); // Adiciona 1 para incluir o dia atual

// Atualizar no info_estudo
//$info_estudo['dias_ate_prova_autal'] = $dias_ate_prova_autal;



// Resto do arquivo HTML permanece o mesmo
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cronograma Inteligente</title>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Old+Standard+TT:wght@400;700&family=Crimson+Text:wght@400;600&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courier+Prime:ital,wght@0,400;0,700;1,400;1,700&display=swap"  rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <script src="assets/js/sistema-conquistas.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300..700&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Tektur:wght@400..900&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Quicksand:wght@300..700&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Tektur:wght@400..900&display=swap" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link rel="stylesheet" href="assets/css/plano-estudo.css">

<!-- Fontes do sistema central -->
<link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    

</head>
<body>
<!-- Modal de Carregamento -->
<div id="loader-overlay">
  <div id="loader-modal">
    <div class="loader-spinner"></div>
    <span>Carregando...</span>
  </div>
</div>
<button id="modo-noturno" aria-label="Alternar modo noturno">
    <i class="fas fa-moon" id="modo-noturno-icone"></i>
</button>

<a href="index.php" class="btn-voltar">
    <i class="fas fa-arrow-left"></i>
</a>

<?php echo exibirMensagens(); ?>

<!-- Primeiro, vamos modificar o header -->
<div class="header">
    <div class="header-left">
        <div class="logo">
        <img src="../logo/logo_vertical_azul.png" alt="Logo" class="logo-light">
        <img src="../logo/logo_vertical.png" alt="Logo" class="logo-dark">
        </div>
    </div>
    <div class="header-center">
        <div class="progress-container">
            <div class="progress-wrapper">
                <div class="progress-bar" id="progress-bar"></div>
            </div>
            <div class="progress-text" id="progress-text">Progresso: 0%</div>
        </div>
    </div>
    <div class="header-right">
        <div class="user-info">
            <i class="fas fa-book-reader"></i>
            <span class="user-name"><?= htmlspecialchars($prova['nome'], ENT_QUOTES, 'UTF-8') ?></span>
        </div>
    </div>
</div>

<!-- Adicione esta div container -->
<div class="plano-container">

<!-- Substitua a quick-nav atual por este sistema de abas -->
<div class="tabs-container">
    <div class="tabs-navigation">
        <button class="tab-btn active" data-tab="info">
            <i class="fas fa-info-circle"></i>
            Informações
        </button>
        <button class="tab-btn" data-tab="progress">
            <i class="fas fa-chart-line"></i>
            Seu Progresso
        </button>
        <button class="tab-btn" data-tab="pending">
            <i class="fas fa-clock"></i>
            Pendentes
        </button>
        <button class="tab-btn" data-tab="review">
            <i class="fas fa-sync"></i>
            Revisões
        </button>
        <button class="tab-btn" data-tab="schedule">
            <i class="fas fa-calendar"></i>
            Cronograma
        </button>
    </div>


    <!-- Conteúdo das abas -->
    <div class="tab-content active" id="info">
    <div class="plano-info">
        <div class="info-cards <?php echo $tempo_apertado ? 'alert-warning' : 'alert-info'; ?>">
            <div class="info-header">
                <h3>Cronograma Inteligente</h3>
                <p>Calculado para concluir todo o conteúdo antes da prova</p>
                
                <div class="countdown-box">
                    <div class="countdown-value"><?php echo $dias_para_prova; ?></div>
                    <div class="countdown-label">Dias Restantes</div>
                </div>
            </div>

            <div class="info-grid">
                <div class="info-item">
                    <i class="fas fa-clock"></i>
                    <strong>Total de Dias:</strong>
                    <?php
                    //$data_inicio = new DateTime($prova['data_inicio_estudo']);
                    //$data_prova = new DateTime($prova['data_prova']);
                    //$dias_totais = $data_inicio->diff($data_prova)->days;
                    ?>
                    <span><?php echo $total_dias; ?></span>
                </div>
                <div class="info-item">
                    <i class="fas fa-calendar-check"></i>
                    <strong>Dias Úteis de Estudo:</strong>
                    <span><?php echo $info_estudo['dias_uteis']; ?></span>
                </div>
                <div class="info-item">
    <i class="fas fa-tasks"></i>
    <strong>Tópicos por Dia:</strong>
    <span>
    <?php 
    $i = 0;
    foreach ($dias_estudo as $dia) {
        $nome_dia = $nomes_dias[$dia];
        $cards = $info_estudo['cards_por_dia'][$i];
        echo "{$nome_dia}: {$cards}<br>";
        $i++;
    }
    ?>
    </span>
</div>
               <!-- Modifique o span adicionando um id -->
<div class="info-item">
    <i class="fas fa-calendar-week"></i>
    <strong>Semanas Necessárias:</strong>
    <span id="semanas-necessarias"><?php echo $info_estudo['semanas_necessarias']; ?></span>
</div>
<div class="info-item">
    <i class="fas fa-book"></i>
    <strong>Total de Tópicos:</strong>
    <span><?php echo $info_estudo['total_conteudos']; ?></span>
</div>
            </div>
<!-- Adicione o botão de download aqui 
            <?php if ($tempo_apertado): ?>
                <div class="alert-message">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Atenção: O prazo está apertado! Considere aumentar as horas de estudo diárias.</span>
                </div>
            <?php endif; ?>
            -->
<!-- Adicione o botão de download aqui 
            <?php if ($dias_para_prova <= 7): ?>
                <div class="alert-message warning">
                    <i class="fas fa-exclamation-circle"></i>
                    <span>MODO INTENSIVO ATIVADO! Prioridade das matérias dobrada.</span>
                </div>
            <?php endif; ?>
             -->

            <div class="plano-periodo">
                <h2>Período do Plano de Estudos</h2>
                <div class="periodo-grid">
                    <div class="periodo-item">
                        <i class="fas fa-play"></i>
                        <span>Início: <?php echo formatarDataPtBr(new DateTime($prova['data_inicio_estudo'])); ?></span>
                    </div>
                    <div class="periodo-item">
                        <i class="fas fa-flag-checkered"></i>
                        <span>Data da Prova: <?php echo formatarDataPtBr($data_prova); ?></span>
                    </div>
                </div>
            </div>

            <div class="actions">


    <a href="atualizar_prova.php" class="btn-config">
    <i class="fas fa-cog"></i>
        Ajustar Configurações
    </a>
    
    <a href="atualizar_data_hora.php" class="btn-config">
        <i class="fas fa-clock"></i>
        Ajustar Dias e Horas
    </a>
</div>
        </div>
    </div>
</div>


<div class="tab-content" id="progress">
    <div id="dashboard-container"></div>
    <!-- Dashboard de Estatísticas -->
<div class="dashboard">
    
    <div class="stats-container" id="stats-container">
        <!-- As estatísticas serão inseridas aqui via JavaScript -->
    </div>
</div>
</div>

    <div class="tab-content" id="pending">
     <div class="cards-atrasados" id="cards-atrasados-section" data-section="pendentes">
    <div class="cards-atrasados-header">
        <div class="cards-atrasados-icon">
            <i class="fas fa-clock"></i>
        </div>
        <div class="cards-atrasados-title">
            <h2>Conteúdos Pendentes</h2>
            <p>Estes conteúdos precisam de sua atenção</p>
        </div>
        <div class="replanejar-container">
            <div class="form-group">
                <label for="nova_data_inicio">Nova Data de Início:</label>
                <input type="date" id="nova_data_inicio" name="nova_data_inicio" required>
            </div>
            <button class="btn-replanejar" id="btn-replanejar">
                <i class="fas fa-calendar-check"></i>
                Replanejar
            </button>
        </div>
        <div class="cards-atrasados-counter" id="cards-counter">
            0 pendentes
        </div>
    </div>

    <div class="cards-atrasados-grid" id="cards-atrasados-container">
        <!-- Cards serão inseridos aqui dinamicamente -->
    </div>
</div>
    </div>


 <?php






// Atualizar $data_termino com a data do último card
if (!empty($semanas_datas)) {
    $ultima_semana = end($semanas_datas);  // Obtém a última semana do cronograma
    if (!empty($ultima_semana['ultimo_card'])) {
        $data_termino = clone $ultima_semana['ultimo_card'];  // Configura com o último card
    }
}

// Captura o conteúdo do buffer


function isLightColor($hex)
{
    $hex = ltrim($hex, '#');
    $r = hexdec(substr($hex, 0, 2));
    $g = hexdec(substr($hex, 2, 2));
    $b = hexdec(substr($hex, 4, 2));
    $yiq = (($r * 299) + ($g * 587) + ($b * 114)) / 1000;
    return $yiq >= 128;
}
?>

    <div class="tab-content" id="stats">


    </div>

    <div class="tab-content" id="review">
    <div class="revisoes-container">
    <h2>Revisões</h2>

<div class="revisoes-tabs">
    <button class="tab-button active" data-tab="pendentes">
        <i class="fas fa-clock"></i> Pendentes <span class="contador-revisoes" id="contador-pendentes">0</span>
    </button>
    <button class="tab-button" data-tab="ignorados">
        <i class="fas fa-eye-slash"></i> Ignorados <span class="contador-revisoes" id="contador-ignorados">0</span>
    </button>
</div>

    <div class="tab-content active" id="tab-pendentes">
        <div id="revisoes-lista" class="revisoes-lista">
            <!-- As revisões pendentes serão inseridas aqui -->
        </div>
    </div>

    <div class="tab-content" id="tab-ignorados">
        <div id="revisoes-ignoradas" class="revisoes-lista">
            <!-- As revisões ignoradas serão inseridas aqui -->
        </div>
    </div>
</div>

    </div>

    <div class="tab-content" id="schedule">
         <!-- Adicione aqui a barra de pesquisa -->
<div class="search-container">
    <i class="fas fa-search"></i>
    <input type="text" id="search-topic" placeholder="Pesquisar tópico de estudo...">
</div>

<div class="reset-container">
    <button id="btn-reset-estudos" class="btn-reset">
        <i class="fas fa-undo"></i>
        Resetar Progresso
    </button>
</div>

        <!-- Conteúdo do cronograma (semanas e dias) -->
         <?php
         $semanaIndex = 0;  // <--- ADICIONE ESTA LINHA
// Exibição do conteúdo
// Verifica se a prova foi replanejada
// Verifica se a prova foi replanejada (verificação mais rigorosa)
$replanejado = isset($prova['replanejado']) && (
    $prova['replanejado'] === 't' || 
    $prova['replanejado'] === true || 
    $prova['replanejado'] === 1 || 
    $prova['replanejado'] === '1' ||
    $prova['replanejado'] === 'true'
);

// Se foi replanejada, primeiro mostra os conteúdos já estudados em uma seção separada
// Se foi replanejada, primeiro mostra os conteúdos já estudados em uma seção separada
if ($replanejado) {
    // Filtra apenas os conteúdos já estudados QUE FORAM ESTUDADOS ANTES DO REPLANEJAMENTO
    $conteudosEstudados = array_filter($conteudos, function($item) {
        return $item['status_estudo'] === 'Estudado' && 
               isset($item['replanejado_em']) && 
               !empty($item['replanejado_em']);
    });
    
    // Só exibe esta seção se houver conteúdos estudados
    if (!empty($conteudosEstudados)) {
        echo "<div class='semana semana-estudados'>";
        echo "<div class='semana-title'>";
        echo "<div class='semana-header'>";
        echo "<span class='semana-numero'>Conteúdos Já Estudados Antes do Replanejamento</span>";
        echo "<span class='estudados-counter'>" . count($conteudosEstudados) . " conteúdos</span>";
        echo "</div>";
        echo "<i class='fas fa-chevron-down icon'></i>";
        echo "</div>";

        echo "<div class='semana-content'>";
        
        // Renderiza os conteúdos estudados em uma grade, sem datas ou dias da semana
        echo "<div class='conteudos-estudados-grid'>";
        foreach ($conteudosEstudados as $conteudo) {
            $isLightColor = isLightColor($conteudo['cor']);
            $lightClass = $isLightColor ? 'light-color' : '';
            
            echo "<div class='conteudo-item {$lightClass} completed' 
                 style='background-color: {$conteudo['cor']}'
                 data-id='{$conteudo['id']}'
                 data-materia='{$conteudo['materia_nome']}'
                 data-capitulo='{$conteudo['capitulo']}'
                 data-descricao='{$conteudo['descricao']}'
                 data-descricao-capitulo-principal='" . htmlspecialchars($conteudo['descricao_capitulo_principal'] ?? '', ENT_QUOTES) . "'
                 data-descricao-capitulo-secundario='" . htmlspecialchars($conteudo['descricao_capitulo_secundario'] ?? '', ENT_QUOTES) . "'
                 data-descricao-capitulo-terciario='" . htmlspecialchars($conteudo['descricao_capitulo_terciario'] ?? '', ENT_QUOTES) . "'
                 data-descricao-capitulo-quaternario='" . htmlspecialchars($conteudo['descricao_capitulo_quaternario'] ?? '', ENT_QUOTES) . "'
                 data-descricao-capitulo-quinario='" . htmlspecialchars($conteudo['descricao_capitulo_quinario'] ?? '', ENT_QUOTES) . "'
                 data-descricao-capitulo-sexto='" . htmlspecialchars($conteudo['descricao_capitulo_sexto'] ?? '', ENT_QUOTES) . "'
                 data-descricao-capitulo-setimo='" . htmlspecialchars($conteudo['descricao_capitulo_setimo'] ?? '', ENT_QUOTES) . "'
                 data-cor='{$conteudo['cor']}'>";
            
                 echo '<label class="conteudo-label">';
                 echo "<input type='checkbox' class='conteudo-checkbox conteudo-replanejado' data-id='{$conteudo['id']}' checked>";
                 // Removemos o 'disabled' e adicionamos a classe 'conteudo-replanejado'
            
            // Exibição da hierarquia
            echo '<div class="conteudo-hierarchy">';
            echo "<h4>{$conteudo['materia_nome']}</h4>";
    
            // Inicializa o array de partes do capítulo
            $partes_capitulo = explode('.', $conteudo['capitulo']);
            $capitulo_atual = $conteudo['capitulo'];
            $mostrar_atual = true;
    
            // Nível 1 (Principal)
            if (!empty($conteudo['descricao_capitulo_principal'])) {
                $descricao_sem_numero = preg_replace('/^' . $partes_capitulo[0] . '\.\s*/', '', $conteudo['descricao_capitulo_principal']);
                echo "<p class='hierarchy-level-1'>{$partes_capitulo[0]}. {$descricao_sem_numero}</p>";
                
                if ($capitulo_atual === $partes_capitulo[0]) {
                    $mostrar_atual = false;
                }
            }
    
            // Nível 2
            if (isset($partes_capitulo[1]) && !empty($conteudo['descricao_capitulo_secundario'])) {
                $nivel2 = $partes_capitulo[0] . '.' . $partes_capitulo[1];
                $descricao_sem_numero = preg_replace('/^' . preg_quote($nivel2, '/') . '\.\s*/', '', $conteudo['descricao_capitulo_secundario']);
                echo "<p class='hierarchy-level-2'>{$nivel2}. {$descricao_sem_numero}</p>";
                
                if ($capitulo_atual === $nivel2) {
                    $mostrar_atual = false;
                }
            }
    
            // Nível 3
            if (isset($partes_capitulo[2]) && !empty($conteudo['descricao_capitulo_terciario'])) {
                $nivel3 = $partes_capitulo[0] . '.' . $partes_capitulo[1] . '.' . $partes_capitulo[2];
                $descricao_sem_numero = preg_replace('/^' . preg_quote($nivel3, '/') . '\.\s*/', '', $conteudo['descricao_capitulo_terciario']);
                echo "<p class='hierarchy-level-3'>{$nivel3}. {$descricao_sem_numero}</p>";
                
                if ($capitulo_atual === $nivel3) {
                    $mostrar_atual = false;
                }
            }

            // Nível 4 (Quaternário)
            if (isset($partes_capitulo[3]) && !empty($conteudo['descricao_capitulo_quaternario'])) {
                $nivel4 = $partes_capitulo[0] . '.' . $partes_capitulo[1] . '.' . $partes_capitulo[2] . '.' . $partes_capitulo[3];
                $descricao_sem_numero = preg_replace('/^' . preg_quote($nivel4, '/') . '\.\s*/', '', $conteudo['descricao_capitulo_quaternario']);
                echo "<p class='hierarchy-level-4'>{$nivel4}. {$descricao_sem_numero}</p>";
                
                if ($capitulo_atual === $nivel4) {
                    $mostrar_atual = false;
                }
            }

            // Nível 5 (Quinário)
            if (isset($partes_capitulo[4]) && !empty($conteudo['descricao_capitulo_quinario'])) {
                $nivel5 = $partes_capitulo[0] . '.' . $partes_capitulo[1] . '.' . $partes_capitulo[2] . '.' . $partes_capitulo[3] . '.' . $partes_capitulo[4];
                $descricao_sem_numero = preg_replace('/^' . preg_quote($nivel5, '/') . '\.\s*/', '', $conteudo['descricao_capitulo_quinario']);
                echo "<p class='hierarchy-level-5'>{$nivel5}. {$descricao_sem_numero}</p>";
                
                if ($capitulo_atual === $nivel5) {
                    $mostrar_atual = false;
                }
            }

            // Nível 6 (Sexto)
            if (isset($partes_capitulo[5]) && !empty($conteudo['descricao_capitulo_sexto'])) {
                $nivel6 = $partes_capitulo[0] . '.' . $partes_capitulo[1] . '.' . $partes_capitulo[2] . '.' . $partes_capitulo[3] . '.' . $partes_capitulo[4] . '.' . $partes_capitulo[5];
                $descricao_sem_numero = preg_replace('/^' . preg_quote($nivel6, '/') . '\.\s*/', '', $conteudo['descricao_capitulo_sexto']);
                echo "<p class='hierarchy-level-6'>{$nivel6}. {$descricao_sem_numero}</p>";
                
                if ($capitulo_atual === $nivel6) {
                    $mostrar_atual = false;
                }
            }

            // Nível 7 (Sétimo)
            if (isset($partes_capitulo[6]) && !empty($conteudo['descricao_capitulo_setimo'])) {
                $nivel7 = $partes_capitulo[0] . '.' . $partes_capitulo[1] . '.' . $partes_capitulo[2] . '.' . $partes_capitulo[3] . '.' . $partes_capitulo[4] . '.' . $partes_capitulo[5] . '.' . $partes_capitulo[6];
                $descricao_sem_numero = preg_replace('/^' . preg_quote($nivel7, '/') . '\.\s*/', '', $conteudo['descricao_capitulo_setimo']);
                echo "<p class='hierarchy-level-7'>{$nivel7}. {$descricao_sem_numero}</p>";
                
                if ($capitulo_atual === $nivel7) {
                    $mostrar_atual = false;
                }
            }
    
            // Exibe o tópico atual somente se ele for diferente de todos os níveis anteriores
            if ($mostrar_atual || !strpos($capitulo_atual, '.')) {
                $descricao_atual = preg_replace('/^\d+(\.\d+)*\.?\s*/', '', $conteudo['descricao']);
                echo "<p class='current-topic'><strong>{$capitulo_atual}.</strong> {$descricao_atual}</p>";
            }
    
            echo '</div>';
            echo '</label>';
        
            // Botões de ação
            echo '<div class="conteudo-actions">';
            echo '<button onclick="verificarEAbrirCronometro(\'' . 
                urlencode($conteudo['materia_nome']) . '\', \'' . 
                urlencode($conteudo['capitulo'] . ' ' . $conteudo['descricao']) . 
                '\')" class="btn-cronometro">
                <i class="fas fa-clock"></i> Cronômetro
            </button>';
            
            echo '<form method="POST" action="../painel_editar_materias.php" target="_blank" style="width: 100%; margin: 0;">
                <input type="hidden" name="id_materia" value="' . $conteudo['idmateria'] . '">
                <input type="hidden" name="id_planejamento" value="' . $id_planejamento . '">
                <button type="submit" class="btn-anotacoes">
                    <i class="fas fa-notes-medical"></i> Anotações
                </button>
            </form>';
            echo '</div>';
            echo '</div>';
        }
        echo '</div>'; // Fim da conteudos-estudados-grid
        echo '</div>'; // Fim da semana-content
        echo '</div>'; // Fim da semana-estudados
    }
}

// Adicione no final do arquivo, no bloco <style> existente ou crie um novo:
$estilosCSS = "
<style>
    /* Estilos para a seção de conteúdos estudados */
    .semana-estudados {
        margin-bottom: 20px;
        border: 1px solid var(--border-color);
        border-radius: 8px;
        background-color: rgba(0, 0, 0, 0.02);
    }
    
    .semana-estudados .semana-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 100%;
    }
    
    .semana-estudados .estudados-counter {
        font-size: 0.9rem;
        color: var(--secondary-color);
        background-color: rgba(0, 0, 0, 0.05);
        padding: 3px 8px;
        border-radius: 12px;
    }
    
    .conteudos-estudados-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 15px;
        padding: 15px;
    }
    
    .conteudos-estudados-grid .conteudo-item {
        opacity: 0.85;
        transition: all 0.3s ease;
    }
    
    .conteudos-estudados-grid .conteudo-item:hover {
        opacity: 1;
        transform: translateY(-2px);
    }
    
    /* Estilos adicionais para todos os níveis da hierarquia */
    .hierarchy-level-4,
    .hierarchy-level-5,
    .hierarchy-level-6,
    .hierarchy-level-7 {
        font-size: 0.8rem;
        margin: 0;
        padding-left: 30px;
    }
    
    /* Ajuste progressivo dos níveis de identação */
    .hierarchy-level-4 { padding-left: 30px; }
    .hierarchy-level-5 { padding-left: 35px; }
    .hierarchy-level-6 { padding-left: 40px; }
    .hierarchy-level-7 { padding-left: 45px; }
</style>
<!-- Loader CSS -->
<style>
#loader-overlay {
    position: fixed;
    top: 0; left: 0; right: 0; bottom: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(255,255,255,0.7);
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(6px);
    transition: opacity 0.3s;
}
#loader-modal {
    background: rgba(255,255,255,0.95);
    border-radius: 16px;
    box-shadow: 0 4px 32px rgba(0,0,0,0.12);
    padding: 32px 48px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
}
.loader-spinner {
    border: 6px solid #f3f3f3;
    border-top: 6px solid #00008B;
    border-radius: 50%;
    width: 48px;
    height: 48px;
    animation: spin 1s linear infinite;
}
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
body.loading {
    overflow: hidden !important;
    pointer-events: none;
    /* filter: blur(3px); */
}
</style>
";

// Adicione essa variável $estilosCSS no final do arquivo antes de </head>
echo $estilosCSS;

// Adicione antes do foreach que renderiza as semanas
if (empty($conteudosPorSemana)) {
    echo '<div class="alert alert-warning">Nenhum conteúdo encontrado para exibir no cronograma.</div>';
}
$semanaVisivel = 1; // Adicione esta linha antes do foreach

foreach ($conteudosPorSemana as $semanaIndex => $semana) {
    // Verifica se é a última semana (semana da prova)
    if ($semanaIndex === count($conteudosPorSemana) - 1) {
        $data_inicio_ultima_semana = new DateTime($prova['data_prova']);
        while ($data_inicio_ultima_semana->format('N') != 1) {
            $data_inicio_ultima_semana->modify('-1 day');
        }
        $data_fim_ultima_semana = new DateTime($prova['data_prova']);

        echo "<div class='semana-prova-aviso'>";
        echo "<div class='semana-prova-header'>";
        echo "<i class='fas fa-graduation-cap'></i> Semana " . $semanaVisivel . " - Semana da Prova <i class='fas fa-graduation-cap'></i>";
        echo "</div>";
        echo "<div class='semana-prova-datas'>";
        echo "<i class='far fa-calendar-alt'></i> " . $data_inicio_ultima_semana->format('d/m/Y') . " até " . $data_fim_ultima_semana->format('d/m/Y');
        echo "</div>";
        echo "<div class='semana-prova-info'>";
        echo "<div class='info-item_semana_prova'><i class='fas fa-check-circle'></i> Revisão dos conteúdos principais</div>";
        echo "<div class='info-item_semana_prova'><i class='fas fa-book-reader'></i> Resolução de questões</div>";
        echo "<div class='info-item_semana_prova'><i class='fas fa-brain'></i> Preparação mental</div>";
        echo "</div>";
        echo "</div>";
        continue;
    }

    // Conta o número total de cards na semana
    $totalCardsNaSemana = 0;
    foreach ($semana as $diaConteudos) {
        $totalCardsNaSemana += count($diaConteudos);
    }

    // Só exibe a semana se tiver cards
    if ($totalCardsNaSemana > 0) {
        echo "<div class='semana'>";
        $collapsedClass = 'collapsed';
        $activeClass = '';
        
        echo "<div class='semana-title $collapsedClass'>";
        echo "<div class='semana-header'>";
        echo "<span class='semana-numero'>Semana " . $semanaVisivel . '</span>';

        // Período da semana
        if (isset($semanas_datas[$semanaIndex]) &&
            isset($semanas_datas[$semanaIndex]['primeiro_card']) &&
            isset($semanas_datas[$semanaIndex]['ultimo_card'])) {
            echo "<span class='semana-periodo'>"
                . $semanas_datas[$semanaIndex]['primeiro_card']->format('d/m/Y')
                . ' até '
                . $semanas_datas[$semanaIndex]['ultimo_card']->format('d/m/Y')
                . '</span>';
        }

        echo '</div>';
        echo "<i class='fas fa-chevron-down icon'></i>";
        echo '</div>';

        echo "<div class='semana-content $activeClass' data-semana='$semanaIndex'>";

        // Calcula a data inicial da semana
        $data_inicio_semana = null;
        if (isset($semanas_datas[$semanaIndex]['primeiro_card'])) {
            $data_inicio_semana = clone $semanas_datas[$semanaIndex]['primeiro_card'];
            // Volta para segunda-feira
            while ($data_inicio_semana->format('N') != 1) {
                $data_inicio_semana->modify('-1 day');
            }
        }

        foreach ($dias as $diaIndex => $dia) {
            $isDiaInativo = $semanaIndex === 0 && $diaIndex < $ajuste;
            
            // Calcula a data correta para este dia
            $data_dia = null;
            if ($data_inicio_semana) {
                $dia_numero = array_search($dia, $nomes_dias);
                if ($dia_numero !== false) {
                    $data_dia = clone $data_inicio_semana;
                    $data_dia->modify('+' . ($dia_numero - 1) . ' days');
                }
            }

            echo "<div class='dia-horizontal" . ($isDiaInativo ? ' inativo' : '') . "'>";
            echo "<div class='dia-header' onclick='toggleDia(this)'>";
            echo "<div class='dia-info'>";
            echo "<h3>$dia";
            if (!$isDiaInativo && $data_dia) {
                echo "<span class='data-dia'>" . $data_dia->format('d/m/Y') . '</span>';
            }
            echo '</h3>';

            if (isset($semana[$diaIndex])) {
                $estudados = count(array_filter($semana[$diaIndex], function ($c) {
                    return $c['status_estudo'] === 'Estudado';
                }));
                echo "<span class='conteudos-counter'>$estudados/" . count($semana[$diaIndex]) . ' conteúdos</span>';
            }

            echo '</div>';
            echo "<i class='fas fa-chevron-down'></i>";
            echo '</div>';

            echo "<div class='dia-content'>";

            if ($isDiaInativo) {
                echo "<p class='dia-inativo'>Anterior à data de início</p>";
            } else // Substitua por:
                if (isset($semana[$diaIndex]) && !empty($semana[$diaIndex])) {
                    echo "<div class='conteudos-grid'>";
                    // CORRIGIDO: Removido filtro adicional que estava causando problemas
                    foreach ($semana[$diaIndex] as $conteudo) {
                        $isLightColor = isLightColor($conteudo['cor']);
                        $lightClass = $isLightColor ? 'light-color' : '';
                        $temSubdivisao = strpos($conteudo['capitulo'], '.') !== false;
                        $estudado = $conteudo['status_estudo'] === 'Estudado';
                        $completedClass = $estudado ? 'completed' : '';
                    
                        echo "<div class='conteudo-item {$lightClass} {$completedClass}' 
                        style='background-color: {$conteudo['cor']}'
                        data-id='{$conteudo['id']}'
                        data-data-prevista='" . ($data_dia ? $data_dia->format('Y-m-d') : '') . "'
                        data-materia='{$conteudo['materia_nome']}'
                        data-capitulo='{$conteudo['capitulo']}'
                        data-descricao='{$conteudo['descricao']}'
                        data-descricao-capitulo-principal='" . htmlspecialchars($conteudo['descricao_capitulo_principal'] ?? '', ENT_QUOTES) . "'
                        data-descricao-capitulo-secundario='" . htmlspecialchars($conteudo['descricao_capitulo_secundario'] ?? '', ENT_QUOTES) . "'
                        data-descricao-capitulo-terciario='" . htmlspecialchars($conteudo['descricao_capitulo_terciario'] ?? '', ENT_QUOTES) . "'
                        data-descricao-capitulo-quaternario='" . htmlspecialchars($conteudo['descricao_capitulo_quaternario'] ?? '', ENT_QUOTES) . "'
                        data-descricao-capitulo-quinario='" . htmlspecialchars($conteudo['descricao_capitulo_quinario'] ?? '', ENT_QUOTES) . "'
                        data-descricao-capitulo-sexto='" . htmlspecialchars($conteudo['descricao_capitulo_sexto'] ?? '', ENT_QUOTES) . "'
                        data-descricao-capitulo-setimo='" . htmlspecialchars($conteudo['descricao_capitulo_setimo'] ?? '', ENT_QUOTES) . "'
                        data-cor='{$conteudo['cor']}'>";
                        
                        echo '<label class="conteudo-label">';
                        echo "<input type='checkbox' class='conteudo-checkbox' data-id='{$conteudo['id']}'" . ($estudado ? ' checked' : '') . '>';
                        
                        // Exibição da hierarquia
                        echo '<div class="conteudo-hierarchy">';
                        echo "<h4>{$conteudo['materia_nome']}</h4>";
                
                        // Inicializa o array de partes do capítulo
                        $partes_capitulo = explode('.', $conteudo['capitulo']);
                        $capitulo_atual = $conteudo['capitulo'];
                        $mostrar_atual = true;
                
                        // Nível 1 (Principal)
                        if (!empty($conteudo['descricao_capitulo_principal'])) {
                            $descricao_sem_numero = preg_replace('/^' . $partes_capitulo[0] . '\.\s*/', '', $conteudo['descricao_capitulo_principal']);
                            echo "<p class='hierarchy-level-1'>{$partes_capitulo[0]}. {$descricao_sem_numero}</p>";
                            
                            // Se o tópico atual for exatamente este nível, não exibiremos novamente
                            if ($capitulo_atual === $partes_capitulo[0]) {
                                $mostrar_atual = false;
                            }
                        }
                
                        // Nível 2
                        if (isset($partes_capitulo[1]) && !empty($conteudo['descricao_capitulo_secundario'])) {
                            $nivel2 = $partes_capitulo[0] . '.' . $partes_capitulo[1];
                            $descricao_sem_numero = preg_replace('/^' . preg_quote($nivel2, '/') . '\.\s*/', '', $conteudo['descricao_capitulo_secundario']);
                            echo "<p class='hierarchy-level-2'>{$nivel2}. {$descricao_sem_numero}</p>";
                            
                            // Se o tópico atual for exatamente este nível, não exibiremos novamente
                            if ($capitulo_atual === $nivel2) {
                                $mostrar_atual = false;
                            }
                        }
                
                        // Nível 3
                        if (isset($partes_capitulo[2]) && !empty($conteudo['descricao_capitulo_terciario'])) {
                            $nivel3 = $partes_capitulo[0] . '.' . $partes_capitulo[1] . '.' . $partes_capitulo[2];
                            $descricao_sem_numero = preg_replace('/^' . preg_quote($nivel3, '/') . '\.\s*/', '', $conteudo['descricao_capitulo_terciario']);
                            echo "<p class='hierarchy-level-3'>{$nivel3}. {$descricao_sem_numero}</p>";
                            
                            // Se o tópico atual for exatamente este nível, não exibiremos novamente
                            if ($capitulo_atual === $nivel3) {
                                $mostrar_atual = false;
                            }
                        }
                
                        // Nível 4
                        if (isset($partes_capitulo[3]) && !empty($conteudo['descricao_capitulo_quaternario'])) {
                            $nivel4 = $partes_capitulo[0] . '.' . $partes_capitulo[1] . '.' . $partes_capitulo[2] . '.' . $partes_capitulo[3];
                            $descricao_sem_numero = preg_replace('/^' . preg_quote($nivel4, '/') . '\.\s*/', '', $conteudo['descricao_capitulo_quaternario']);
                            echo "<p class='hierarchy-level-4'>{$nivel4}. {$descricao_sem_numero}</p>";
                            
                            // Se o tópico atual for exatamente este nível, não exibiremos novamente
                            if ($capitulo_atual === $nivel4) {
                                $mostrar_atual = false;
                            }
                        }
                
                        // Nível 5
                        if (isset($partes_capitulo[4]) && !empty($conteudo['descricao_capitulo_quinario'])) {
                            $nivel5 = $partes_capitulo[0] . '.' . $partes_capitulo[1] . '.' . $partes_capitulo[2] . '.' . $partes_capitulo[3] . '.' . $partes_capitulo[4];
                            $descricao_sem_numero = preg_replace('/^' . preg_quote($nivel5, '/') . '\.\s*/', '', $conteudo['descricao_capitulo_quinario']);
                            echo "<p class='hierarchy-level-5'>{$nivel5}. {$descricao_sem_numero}</p>";
                            
                            // Se o tópico atual for exatamente este nível, não exibiremos novamente
                            if ($capitulo_atual === $nivel5) {
                                $mostrar_atual = false;
                            }
                        }
                
                        // Nível 6
                        if (isset($partes_capitulo[5]) && !empty($conteudo['descricao_capitulo_sexto'])) {
                            $nivel6 = $partes_capitulo[0] . '.' . $partes_capitulo[1] . '.' . $partes_capitulo[2] . '.' . $partes_capitulo[3] . '.' . $partes_capitulo[4] . '.' . $partes_capitulo[5];
                            $descricao_sem_numero = preg_replace('/^' . preg_quote($nivel6, '/') . '\.\s*/', '', $conteudo['descricao_capitulo_sexto']);
                            echo "<p class='hierarchy-level-6'>{$nivel6}. {$descricao_sem_numero}</p>";
                            
                            // Se o tópico atual for exatamente este nível, não exibiremos novamente
                            if ($capitulo_atual === $nivel6) {
                                $mostrar_atual = false;
                            }
                        }
                
                        // Nível 7
                        if (isset($partes_capitulo[6]) && !empty($conteudo['descricao_capitulo_setimo'])) {
                            $nivel7 = $partes_capitulo[0] . '.' . $partes_capitulo[1] . '.' . $partes_capitulo[2] . '.' . $partes_capitulo[3] . '.' . $partes_capitulo[4] . '.' . $partes_capitulo[5] . '.' . $partes_capitulo[6];
                            $descricao_sem_numero = preg_replace('/^' . preg_quote($nivel7, '/') . '\.\s*/', '', $conteudo['descricao_capitulo_setimo']);
                            echo "<p class='hierarchy-level-7'>{$nivel7}. {$descricao_sem_numero}</p>";
                            
                            // Se o tópico atual for exatamente este nível, não exibiremos novamente
                            if ($capitulo_atual === $nivel7) {
                                $mostrar_atual = false;
                            }
                        }
                
                        // Exibe o tópico atual somente se ele for diferente de todos os níveis anteriores
                        // ou se for um tópico principal sem níveis de hierarquia
                        if ($mostrar_atual || !strpos($capitulo_atual, '.')) {
                            // Remove números do início da descrição se existirem
                            $descricao_atual = preg_replace('/^\d+(\.\d+)*\.?\s*/', '', $conteudo['descricao']);
                            echo "<p class='current-topic'>{$capitulo_atual}. {$descricao_atual}</p>";
                        }
                
                        echo '</div>';
                        echo '</label>';
                    
                        // Botões de ação
                        echo '<div class="conteudo-actions">';
                        echo '<button onclick="verificarEAbrirCronometro(\'' . 
                            urlencode($conteudo['materia_nome']) . '\', \'' . 
                            urlencode($conteudo['capitulo'] . ' ' . $conteudo['descricao']) . 
                            '\')" class="btn-cronometro">
                            <i class="fas fa-clock"></i> Cronômetro
                        </button>';
                        
                        echo '<form method="POST" action="../painel_editar_materias.php" target="_blank" style="width: 100%; margin: 0;">
                            <input type="hidden" name="id_materia" value="' . $conteudo['idmateria'] . '">
                            <input type="hidden" name="id_planejamento" value="' . $id_planejamento . '">
                            <button type="submit" class="btn-anotacoes">
                                <i class="fas fa-notes-medical"></i> Anotações
                            </button>
                        </form>';
                        echo '</div>';
                        echo '</div>';
                    }
                    echo '</div>';
                } else {
                    echo '<p>Nenhum conteúdo programado.</p>';
                }

            echo '</div>';
            echo '</div>';
        }

        echo '</div>';
        echo '</div>';
        
        $semanaVisivel++; // Incrementa o contador apenas quando uma semana é exibida
    }
}

// Adiciona a mensagem sobre a semana de revisão após todas as semanas
$data_inicio_ultima_semana = new DateTime($prova['data_prova']);

// Primeiro ajusta para segunda-feira da semana da prova
while ($data_inicio_ultima_semana->format('N') != 1) {
    $data_inicio_ultima_semana->modify('-1 day');
}

$data_fim_ultima_semana = new DateTime($prova['data_prova']);
$numero_ultima_semana = $semanaIndex + 2; // Adiciona 1 porque $semanaIndex começa em 0, e mais 1 para a semana de revisão



$info_estudo['semanas_necessarias'] = $semanaIndex;
?>
    </div>
</div>

<?php


// Cabeçalho com placeholders


// Antes do fechamento do </head>
$totalConteudos = isset($total_conteudos) ? intval($total_conteudos) : 0;
$cardsPerDay = isset($cardsPerDay) ? intval($cardsPerDay) : 0;
$cards_atrasados_json = isset($cards_atrasados) ? json_encode($cards_atrasados) : '[]';

$debug = [
    'conteudos' => $conteudos,
    'conteudosPorSemana' => $conteudosPorSemana,
    'semanas_datas' => $semanas_datas,
    'total_conteudos' => $total_conteudos,
    'conteudos_estudados' => array_filter($conteudos, function($c) {
        return $c['status_estudo'] === 'Estudado';
    })
];

echo "<!-- Debug Info: ";
print_r($debug);
echo " -->";

$conteudosEstudados = count(array_filter($conteudos, function($c) {
    return $c['status_estudo'] === 'Estudado';
}));
?>



<script>
window.PLANO_CONFIG = {
    totalConteudos: <?php echo $totalConteudos; ?>,
    cardsPerDay: <?php echo $cardsPerDay; ?>,
    cards_atrasados: <?php echo $cards_atrasados_json; ?>,
    metas: {
        diaria: {
            meta: <?php echo intval($cardsPerDay); ?>,
            recompensa: '🌟 Meta diária alcançada!'
        },
        semanal: {
            meta: <?php echo intval($cardsPerDay * 6); ?>,
            recompensa: '🏆 Meta semanal alcançada! Continue assim!'
        },
        materia: {
            meta: 100,
            recompensa: '📚 Matéria dominada! Excelente trabalho!'
        }
    }
};
</script>
<script src="assets/js/scripts.js"></script>


</div>

<!-- Modal de Confirmação de Reset -->
<div id="modal-reset" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <i class="fas fa-exclamation-triangle"></i>
            <h2>Confirmação de Reset</h2>
        </div>
        <div class="modal-body">
            <p>Tem certeza que deseja resetar todo o seu progresso?</p>
            <p class="warning-text"><i class="fas fa-info-circle"></i> Esta ação não pode ser desfeita!</p>
        </div>
        <div class="modal-footer">
            <button class="btn-cancelar" id="btn-cancelar-reset">
                <i class="fas fa-times"></i>
                Cancelar
            </button>
            <button class="btn-confirmar" id="btn-confirmar-reset">
                <i class="fas fa-check"></i>
                Confirmar Reset
            </button>
        </div>
    </div>
</div>

<script>
// Exibe loader até a página carregar
window.addEventListener('DOMContentLoaded', function() {
  document.body.classList.add('loading');
});
window.addEventListener('load', function() {
  setTimeout(function() {
    document.body.classList.remove('loading');
    const overlay = document.getElementById('loader-overlay');
    if (overlay) overlay.style.display = 'none';
  }, 8000); // 1.2 segundos de atraso
});
</script>
</body>
</html>
