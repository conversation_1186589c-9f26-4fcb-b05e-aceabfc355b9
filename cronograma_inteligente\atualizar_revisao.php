<?php
//atualizar_revisao.php

session_start();
if (!isset($_SESSION['idusuario']) || $_SESSION['idusuario'] != 1) {
    header("Location: login.php");
    exit();
}

include_once("assets/config.php");

$usuario_id = $_SESSION['idusuario'];

// Loop através dos status e data de revisão enviados no formulário
foreach ($_POST['status'] as $conteudo_id => $status) {
    $data_revisao = $_POST['data_revisao'][$conteudo_id];

    // Define uma variável SQL para `data_revisao`, tratando valores vazios como `NULL`
    if (empty($data_revisao)) {
        $query = "
            UPDATE appestudo.usuario_conteudo 
            SET status_revisao = $1, data_revisao = NULL
            WHERE usuario_id = $2 AND id = $3
        ";
        $params = [$status, $usuario_id, $conteudo_id];
    } else {
        $query = "
            UPDATE appestudo.usuario_conteudo 
            SET status_revisao = $1, data_revisao = $2
            WHERE usuario_id = $3 AND id = $4
        ";
        $params = [$status, $data_revisao, $usuario_id, $conteudo_id];
    }

    $result = pg_query_params($conexao, $query, $params);

    if (!$result) {
        die("Erro ao atualizar o conteúdo: " . pg_last_error($conexao));
    }
}

echo "Atualizações salvas com sucesso!";
header("Location: revisao_estudo.php");
exit();
?>
