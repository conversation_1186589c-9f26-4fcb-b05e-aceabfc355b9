<?php
// Incluir configuração centralizada de sessão
include_once 'session_config.php';
include_once 'conexao_POST.php';
include_once 'includes/auth.php';

// Verificar autenticação
try {
    $id_usuario = verificarAutenticacao($conexao);
} catch (Exception $e) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Usuário não autenticado']);
    exit;
}

// Verificar timeout de sessão
if (!verificarTimeoutSessao()) {
    logEventoSessao('sessao_expirada', $id_usuario);
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Sessão expirada']);
    exit;
}

// Renovar sessão se válida
renovarSessao();

header('Content-Type: application/json');

try {
    // Verificar se todos os campos necessários estão presentes
    $required_fields = ['idestudo', 'data', 'ponto_estudado', 'tempo_liquido'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field])) {
            throw new Exception("Campo obrigatório ausente: $field");
        }
    }

    // Debug
    error_log("Dados recebidos: " . print_r($_POST, true));

    // Preparar os dados
    $id = $_POST['idestudo'];
    $data = $_POST['data'];
    $ponto = $_POST['ponto_estudado'];
    $tempo = $_POST['tempo_liquido'];
    $q_total = $_POST['q_total'] ? $_POST['q_total'] : 0;
    $q_certa = $_POST['q_certa'] ? $_POST['q_certa'] : 0;
    $metodo = $_POST['metodo'];
    // Adicionar o campo descrição, permitindo que seja nulo ou uma string vazia
    $descricao = isset($_POST['descricao']) ? $_POST['descricao'] : null;
    // Adicionar o campo link_conteudo, permitindo que seja nulo ou uma string vazia
    $link_conteudo = isset($_POST['link_conteudo']) ? $_POST['link_conteudo'] : null;

    // Verificar se o registro pertence ao usuário autenticado
    $verificar_propriedade = "SELECT idestudos FROM appEstudo.estudos 
                              WHERE idestudos = $1 AND planejamento_usuario_idusuario = $2";
    $result_verificacao = pg_query_params($conexao, $verificar_propriedade, [$id, $id_usuario]);
    
    if (!pg_fetch_assoc($result_verificacao)) {
        throw new Exception("Registro não encontrado ou não pertence ao usuário");
    }

    // Query de atualização
    $query = "UPDATE appEstudo.estudos
              SET data = $1::date,
                  ponto_estudado = $2,
                  tempo_liquido = $3::interval,
                  q_total = $4,
                  q_certa = $5,
                  metodo = $6,
                  descricao = $7, -- Adicionado campo descrição
                  link_conteudo = $8 -- Adicionado campo link_conteudo
              WHERE idestudos = $9 -- O índice do idestudos agora é 9
              RETURNING materia_idmateria";

    $result = pg_query_params($conexao, $query, [
        $data,
        $ponto,
        $tempo,
        $q_total,
        $q_certa,
        $metodo,
        $descricao, // Adicionado parâmetro descrição
        $link_conteudo, // Adicionado parâmetro link_conteudo
        $id
    ]);

    if (!$result) {
        throw new Exception(pg_last_error($conexao));
    }

    $row = pg_fetch_assoc($result);
    
    // Buscar o nome da matéria
    $materia_query = "SELECT nome FROM appEstudo.materia WHERE idmateria = $1";
    $materia_result = pg_query_params($conexao, $materia_query, [$row['materia_idmateria']]);
    $materia_row = pg_fetch_assoc($materia_result);

    // Gerar timestamp único para atualização
    $timestamp_atualizacao = time();

    echo json_encode([
        'success' => true,
        'message' => 'Registro atualizado com sucesso',
        'materia' => $materia_row['nome'],
        'timestamp_atualizacao' => $timestamp_atualizacao,
        'update_instructions' => [
            'recarregarIndex' => $timestamp_atualizacao,
            'atualizarPaginaInicial' => $timestamp_atualizacao
        ]
    ]);

} catch (Exception $e) {
    error_log("Erro na atualização: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>

