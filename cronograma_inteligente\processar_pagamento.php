<?php
// processar_pagamento.php
session_start();
require_once 'assets/config.php';

// Verifica se usuário está logado e se recebeu os dados do plano
if (!isset($_SESSION['idusuario']) || !isset($_POST['plano_id']) || !isset($_POST['tipo'])) {
    $_SESSION['erro_pagamento'] = "Dados inválidos para processamento.";
    header("Location: upgrade_plano.php");
    exit();
}

$plano_id = (int)$_POST['plano_id'];
$tipo = $_POST['tipo'];
$usuario_id = $_SESSION['idusuario'];

// Busca informações do plano selecionado
$query_plano = "SELECT * FROM appestudo.planos WHERE id = $1 AND ativo = true";
$resultado_plano = pg_query_params($conexao, $query_plano, array($plano_id));

if (!$resultado_plano || pg_num_rows($resultado_plano) == 0) {
    $_SESSION['erro_pagamento'] = "Plano não encontrado ou inativo.";
    header("Location: upgrade_plano.php");
    exit();
}

$plano = pg_fetch_assoc($resultado_plano);

// Define o valor baseado no tipo de assinatura
$valor = ($tipo === 'mensal') ? 10.60 : 97.90;

try {
    // Aqui você incluiria a integração com seu gateway de pagamento
    // Por enquanto, vamos simular uma transação bem-sucedida
    $pagamento_aprovado = true;
    $codigo_transacao = 'SIMULACAO_' . time();
    
    if ($pagamento_aprovado) {
        // Calcula data de fim baseada no tipo de assinatura
        $data_inicio = new DateTime();
        $data_fim = clone $data_inicio;
        
        if ($tipo === 'mensal') {
            $data_fim->modify('+1 month');
        } else {
            $data_fim->modify('+1 year');
        }
        
        // Primeiro, desativa qualquer assinatura ativa existente
        $query_desativar = "
            UPDATE appestudo.assinaturas 
            SET status = false 
            WHERE usuario_id = $1 
            AND modulo = 'cronograma_inteligente' 
            AND status = true";
        pg_query_params($conexao, $query_desativar, array($usuario_id));
        
        // Insere nova assinatura
        $query_inserir = "
            INSERT INTO appestudo.assinaturas 
            (usuario_id, plano_id, modulo, data_inicio, data_fim, valor_pago, 
             forma_pagamento, codigo_transacao, tipo_assinatura)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)";
            
        $params = array(
            $usuario_id,
            $plano_id,
            'cronograma_inteligente',
            $data_inicio->format('Y-m-d H:i:s'),
            $data_fim->format('Y-m-d H:i:s'),
            $valor,
            'SIMULACAO', // Aqui seria o método real de pagamento
            $codigo_transacao,
            $tipo
        );
        
        $resultado_insert = pg_query_params($conexao, $query_inserir, $params);
        
        if ($resultado_insert) {
            // Atualiza o status do usuário
            $query_update_usuario = "
                UPDATE appestudo.usuario 
                SET cronograma_inteligente = true 
                WHERE idusuario = $1";
            pg_query_params($conexao, $query_update_usuario, array($usuario_id));
            
            $_SESSION['sucesso_pagamento'] = "Assinatura realizada com sucesso! Você já pode acessar o Cronograma Inteligente.";
            header("Location: index.php");
            exit();
        } else {
            throw new Exception("Erro ao registrar assinatura.");
        }
    }
    
} catch (Exception $e) {
    $_SESSION['erro_pagamento'] = "Erro ao processar pagamento: " . $e->getMessage();
    header("Location: upgrade_plano.php");
    exit();
}
?>