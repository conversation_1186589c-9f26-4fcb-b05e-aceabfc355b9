<?php
/**
 * Endpoint para Procedures
 * Sistema de Fidelidade da Barbearia
 */

function handleProcedure($method, $action, $input, $db, $database) {
    if ($method !== 'POST') {
        ApiResponse::methodNotAllowed();
    }
    
    try {
        $result = $database->executeProcedure($action, $input);
        ApiResponse::success($result, 'Procedure executada com sucesso');
        
    } catch (Exception $e) {
        error_log("Procedure error: " . $e->getMessage());
        ApiResponse::error('Erro ao executar procedure: ' . $e->getMessage());
    }
}
?>
