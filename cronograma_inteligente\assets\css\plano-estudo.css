:root {
    /* Cores principais */
    --primary-color: #00008B;          
    --paper-color: #FFFFFF;            
    --secondary-color: #4a4a4a;        
    --background-color: #F5F7FA;       
    --text-color: #2c3e50;             
    --text-light-color: #FFFFFF;       /* NOVO: Para texto em fundos escuros */
    --text-muted: #64748b;             /* NOVO: Para texto secundário */
    --border-color: #E2E8F0;           
    
    /* Cores de estado e feedback */
    --hover-color: #0000A5;            /* MODIFICADO: Mais claro que o anterior */
    --active-color: #00008B;           
    --success-color: #28a745;          
    --warning-color: #ffc107;          
    --danger-color: #dc3545;           
    
    /* Cores de destaque e acentos */
    --accent-light: #F8F9FB;           
    --accent-medium: #CBD5E0;          
    --accent-dark: #718096;            
    
    /* Cores para elementos especiais */
    --color-info-item: rgba(0, 0, 139, 0.05);
    --light-blue: #EDF2F7;             
    --gray-light: #F7FAFC;             
    --gray-medium: #A0AEC0;            
    
    /* Sombras e gradientes */
    --shadow-sm: rgba(0, 0, 0, 0.05);  /* NOVO: Sombra sutil */
    --shadow-md: rgba(0, 0, 0, 0.1);   /* NOVO: Sombra média */
    --shadow-lg: rgba(0, 0, 0, 0.15);  /* NOVO: Sombra mais pronunciada */
    --elegant-border: linear-gradient(45deg, #E2E8F0, #CBD5E0, #E2E8F0);

    /* NOVO: Variações de opacidade da cor primária */
    --primary-opacity-10: rgba(0, 0, 139, 0.1);
    --primary-opacity-25: rgba(0, 0, 139, 0.25);
    --primary-opacity-50: rgba(0, 0, 139, 0.5);
}


#modo-noturno {
    position: fixed; /* Fixa o botão na tela */
    top: 140px; /* Mesma altura do botão voltar */
    right: 20px; /* Distância da direita */
    background-color: var(--paper-color); /* Cor de fundo do botão */
    border: 2px solid var(--border-color); /* Borda do botão */
    border-radius: 50%; /* Formato circular */
    width: 40px; /* Largura do botão */
    height: 40px; /* Altura do botão */
    display: flex; /* Centraliza o ícone */
    align-items: center;
    justify-content: center;
    cursor: pointer; /* Muda o cursor para indicar que é clicável */
    transition: all 0.3s ease; /* Adiciona uma transição suave */
    z-index: 1000; /* Garante que o botão fique acima de outros elementos */
}

#modo-noturno:hover {
    transform: translateY(-2px); /* Move o botão ligeiramente para cima */
    box-shadow: 2px 2px 0 var(--border-color); /* Adiciona uma sombra */
}

#modo-noturno.hidden {
    opacity: 0; /* Torna o botão invisível */
    visibility: hidden; /* Remove o botão do fluxo da página */
    transition: opacity 0.3s ease, visibility 0.3s ease; /* Adiciona uma transição suave */
}

#modo-noturno i {
    color: var(--text-color); /* Cor do ícone */
    font-size: 1.2rem; /* Tamanho do ícone */
}

.modo-noturno {
    --primary-color: #4A90E2; /* Azul vívido para destaques */
    --paper-color: #1E2A38; /* Azul escuro suave para simular papel no modo escuro */
    --secondary-color: #5DADE2; /* Azul claro para elementos secundários */
    --background-color: #121C2C; /* Azul muito escuro para o fundo */
    --text-color: #EAF2F8; /* Branco-azulado para texto */
    --border-color: #2E4057; /* Azul acinzentado para bordas */
    --color-info-item: #2E4057;





    background-color: var(--background-color);
    background-image:
            linear-gradient(rgba(0, 86, 179, 0.42), rgba(0, 86, 179, 0.42)),
            url("data:image/svg+xml,%3Csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%238b4513' fill-opacity='0.1' fill-rule='evenodd'%3E%3Ccircle cx='3' cy='3' r='3'/%3E%3Ccircle cx='13' cy='13' r='3'/%3E%3C/g%3E%3C/svg%3E");
}
.btn-voltar {
    position: fixed;
    top: 140px; /* Aumentado de 20px para 80px para ficar abaixo da barra de progresso */
    left: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    background: var(--paper-color);
    border: 2px solid var(--border-color);
    border-radius: 50%;
    box-shadow: 4px 4px 0 var(--border-color);
    color: var(--primary-color);
    text-decoration: none;
    font-size: 1.2rem;
    z-index: 998;
    transition: all 0.3s ease;
}

.btn-voltar.nav-hidden {
    opacity: 0;
    visibility: hidden;
    transform: translateY(-20px);
    pointer-events: none;
}


.btn-voltar:hover {
    transform: translateY(-2px);
    box-shadow: 4px 4px 0 var(--border-color);
    background-color: var(--primary-color);
    color: white;
}

.btn-voltar:active {
    transform: translateY(0);
    box-shadow: 2px 2px 0 var(--border-color);
}

.btn-voltar i {
    font-size: 1.5rem;
}

/* Para telas menores */
@media (max-width: 768px) {
    .btn-voltar {
        top: 70px; /* Ajustado para mobile */
        left: 10px;
        width: 40px;
        height: 40px;
    }

    .btn-voltar i {
        font-size: 1.2rem;
    }
}

.config-form {
    background: var(--paper-color);
    padding: 20px;
    margin-bottom: 20px;
    border: 2px solid var(--border-color);
    box-shadow: 3px 3px 0 var(--border-color);
}

.config-section {
    display: flex;
    align-items: center;
    gap: 10px;
}

.config-section label {
    font-family: 'Quicksand', sans-serif;
    color: var(--primary-color);
    font-weight: bold;
}

.config-section select {
    padding: 8px;
    border: 1px solid var(--border-color);
    background: white;
    font-family: 'Crimson Text', serif;
    color: var(--text-color);
    cursor: pointer;
}
.dia {
    min-height: 200px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.dia h3 {
    position: sticky;
    top: 0;
    background: var(--paper-color); /* Cor sólida em vez de transparente */
    padding: 15px;
    z-index: 10;
    margin: -20px -20px 10px -20px;
    border-bottom: 2px solid var(--border-color);
    font-family: 'Quicksand', sans-serif;
    font-size: 1.4rem;
    color: var(--primary-color);
    text-align: center;
    /* Adicione uma sombra sutil para dar mais destaque */
    box-shadow: 0 2px 4px rgba(139, 69, 19, 0.1);
}

/* Adicione este estilo para garantir que os conteúdos fiquem abaixo do cabeçalho */
.conteudo-item {
    position: relative;
    z-index: 1;
}

.conteudo-item {
    flex: 1;
}

/* Estilos adicionais */
.updating {
    opacity: 0.7;
    position: relative;
}

.updating::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.semana-counter {
    margin-left: auto;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    font-size: 0.9em;
}

.semana-completa .semana-title {
    background: rgba(0, 128, 0, 0.1);
}

.semana-completa .semana-counter {
    background: rgba(0, 128, 0, 0.2);
    color: darkgreen;
}

/* Otimização para mobile */
@media (max-width: 768px) {
    .semana-title {
        font-size: 1.2rem;
        padding: 15px;
    }

    .semana-counter {
        font-size: 0.8em;
        padding: 3px 6px;
    }
}


/* Modo noturno */
.modo-noturno {
    --primary-color: #00008B;
    --paper-color: #1E2A38;
    --secondary-color: #A0AEC0;
    --background-color: #121C2C;
    --text-color: #F7FAFC;
    --border-color: #2D3748;
    --color-info-item: rgba(247, 250, 252, 0.05);
    --hover-color: #000066;
    --accent-light: #2D3748;
    --accent-medium: #4A5568;
    --accent-dark: #718096;
}

/* CSS para o body com o novo background */
body {
    font-family: 'Quicksand', sans-serif;
    background-color: var(--background-color);
    background-image: linear-gradient(
        140deg,
        var(--background-color) 0%,
        rgba(0, 0, 139, 0.02) 50%,
        var(--background-color) 100%
    );
    color: var(--text-color);
    padding: 40px;
    line-height: 1.6;
    padding-top: 60px;
}

h1 {
    font-family: 'Quicksand', sans-serif;
    text-align: center;
    color: var(--primary-color);
    font-size: 2.5rem;
    margin-bottom: 20px;
    text-transform: uppercase;
    letter-spacing: 3px;
    position: relative;
}

h1::after {
    content: "";
    display: block;
    width: 150px;
    height: 3px;
    background-color: var(--primary-color);
    margin: 20px auto;
}

.progress-container {
    display: flex;
    align-items: center;
    gap: 15px;
    width: 100%;
    max-width: 800px;
    margin: 0 auto;
}

.progress-wrapper {
    flex: 1;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 5px;
    border: 1px solid var(--border-color);
}

.progress-bar {
    width: 0%;
    height: 20px;
    background: var(--primary-color);
    transition: width 0.5s ease;
    position: relative;
    border-radius: 3px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 30px 30px;
    animation: move 2s linear infinite;
    border-radius: 3px;
}

.progress-text {
    font-family: 'Quicksand', sans-serif;
    color: var(--primary-color);
    font-size: 1rem;
    font-weight: bold;
    white-space: nowrap;
    min-width: 120px;
}

/* Estilo quando o modo escuro está ativo */
.modo-noturno .progress-text {
    color: var(--text-light-color); /* Usa a variável de cor clara para o texto */
}

@keyframes move {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 30px 30px;
    }
}


.semana {
    margin-bottom: 20px;
    background: var(--paper-color);
    border: 2px solid var(--border-color);
    box-shadow: 5px 5px 0 var(--border-color);
}

.semana-title {
    font-family: 'Quicksand', sans-serif;
    font-size: 1.8rem;
    color: var(--primary-color);
    padding: 20px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--accent-medium);
    transition: background-color 0.3s ease;
}

.semana-title:hover {
    background: var(--accent-dark);
}

.semana-title .icon {
    transition: transform 0.3s ease;
}

.semana-title.collapsed .icon {
    transform: rotate(-90deg);
}

.semana-content {
    padding: 30px;
    display: none;
}

.semana-content.active {
    display: block;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.cronograma {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    padding: 20px;
}

.dia {
    background: rgba(255, 248, 220, 0.7);
    padding: 20px;
    border: 1px solid var(--border-color);
}

.dia h3 {
    font-family: 'Quicksand', sans-serif;
    font-size: 1.4rem;
    color: var(--primary-color);
    margin-bottom: 20px;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
}

.conteudo-item {
    margin-bottom: 0;
    padding: 20px;
    position: relative;
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
    color: #fff;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
    transition: opacity 0.3s ease-in-out, filter 0.3s ease-in-out;
}

.conteudo-item.completed {
    opacity: 0.6;
    filter: grayscale(30%);
    border-style: dashed;
}



.conteudo-item:not(.completed):hover {
    box-shadow: 4px 4px 0 rgba(0, 0, 0, 0.2);
}

.conteudo-item.completed:hover {
    transform: scale(0.98);
    cursor: default;
}

.conteudo-item label {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
    position: relative;
    z-index: 1;
}

.conteudo-item input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    width: 20px;
    height: 20px;
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(0, 0, 0, 0.2);
    margin-right: 10px;
    position: relative;
    cursor: pointer;
    flex-shrink: 0;
    border-radius: 3px;
}

.conteudo-item input[type="checkbox"]:checked::after {
    content: "✓";
    position: absolute;
    color: #000;
    font-size: 16px;
    left: 2px;
    top: -2px;
}

.conteudo-item h4 {
    font-family: 'Roboto Condensed', sans-serif;
    margin: 0;
    font-size: 1.1rem;
    font-weight: bold;
}

.conteudo-item p {
    margin: 5px 0 0;
    font-size: 0.95rem;
    opacity: 0.9;
}

.conteudo-item.light-color {
    color: #333 !important;
    text-shadow: none !important;
}

.conteudo-item.light-color h4 {
    color: #333 !important;
}

.conteudo-item.light-color p {
    color: #333 !important;
    opacity: 0.8;
}

.conteudo-item.light-color .hierarchy-parent {
    color: #555 !important;
}

.conteudo-item.light-color .hierarchy-child {
    color: #333 !important;
}

.conteudo-item.light-color .hierarchy-current {
    color: #333 !important;
}

.conteudo-item.light-color .hierarchy-grandparent {
    color: #666 !important;
}

.conteudo-item.light-color input[type="checkbox"] {
    border-color: rgba(0, 0, 0, 0.3);
}

/* Estilos para modo escuro */
[data-theme="dark"] .conteudo-item {
    color: #fff;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .conteudo-item.light-color {
    color: #333 !important;
    text-shadow: none !important;
}

[data-theme="dark"] .conteudo-item.light-color h4,
[data-theme="dark"] .conteudo-item.light-color p,
[data-theme="dark"] .conteudo-item.light-color .hierarchy-parent,
[data-theme="dark"] .conteudo-item.light-color .hierarchy-child,
[data-theme="dark"] .conteudo-item.light-color .hierarchy-current,
[data-theme="dark"] .conteudo-item.light-color .hierarchy-grandparent {
    color: #333 !important;
}

/* Melhor contraste para cores muito escuras */
.conteudo-item.dark-color {
    color: #fff !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5) !important;
}

.conteudo-item.dark-color h4,
.conteudo-item.dark-color p,
.conteudo-item.dark-color .hierarchy-parent,
.conteudo-item.dark-color .hierarchy-child,
.conteudo-item.dark-color .hierarchy-current,
.conteudo-item.dark-color .hierarchy-grandparent {
    color: #fff !important;
}

@media (max-width: 768px) {
    body {
        padding: 20px;
        padding-top: 60px;
    }

    .cronograma {
        grid-template-columns: 1fr;
    }

    .conteudo-item {
        margin-bottom: 10px;
    }
    .progress-container {
        padding: 5px 20px;
    }
    .progress-text {
        font-size: 0.9rem;
        min-width: 120px;
    }
    .progress-bar {
        height: 20px;
    }
}

.capitulo-principal {
    margin-bottom: 8px;
    font-style: italic;
    opacity: 0.85;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 5px;
}

.subcapitulo {
    margin-top: 5px;
    font-weight: bold;
}
.config-form {
    background: var(--paper-color);
    padding: 20px;
    margin-bottom: 20px;
    border: 2px solid var(--border-color);
    box-shadow: 3px 3px 0 var(--border-color);
}

.config-section {
    display: flex;
    align-items: center;
    gap: 20px;
}

.config-section label {
    font-family: 'Quicksand', sans-serif;
    color: var(--primary-color);
    font-weight: bold;
}

.config-section select {
    padding: 8px;
    border: 1px solid var(--border-color);
    background: white;
    font-family: 'Crimson Text', serif;
    color: var(--text-color);
    cursor: pointer;
}

.total-semanas {
    font-family: 'Quicksand', sans-serif;
    color: var(--primary-color);
    font-weight: bold;
    padding: 5px 10px;
    background: var(--accent-medium);
    border-radius: 4px;
}
.dashboard {
    background: var(--paper-color);
    padding: 20px;
    margin: 20px 0;
    border: 2px solid var(--border-color);
    box-shadow: 5px 5px 0 var(--border-color);
}

.dashboard h2 {
    font-family: 'Quicksand', sans-serif;
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 20px;
}

.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.stat-card {
    padding: 15px;
    border-radius: 8px;
    box-shadow: 3px 3px 0 rgba(0, 0, 0, 0.1);
}

.stat-card h3 {
    margin: 0 0 10px 0;
    font-size: 1.2em;
    font-family: 'Quicksand', sans-serif;
}

/* Container da barra de progresso */
.stat-progress {
    background: rgba(255, 255, 255, 0.2);
    height: 12px;
    border-radius: 6px;
    margin: 15px 0;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Barra de progresso */
.stat-progress .progress-bar {
    height: 100%;
    
    border-radius: 6px;
    transition: width 0.5s ease;
    position: relative;
    overflow: hidden;
}

/* Efeito de brilho na barra de progresso */
.stat-progress .progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    animation: shimmer 2s infinite;
}

/* Textos dentro do card */
.stat-card p {
    margin: 15px 0 0 0;
    font-size: 1.1rem;
    line-height: 1.6;
    font-family: 'Courier Prime', sans-serif;
}

.light-text {
    color: white;
}

.dark-text {
    color: #333;
}

.recompensa-notificacao {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: var(--primary-color);
    color: white;
    padding: 15px 25px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transform: translateY(100px);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 1000;
    font-family: 'Quicksand', sans-serif;
}

.recompensa-notificacao.mostrar {
    transform: translateY(0);
    opacity: 1;
}

/* Ajustes para os cards de revisão */
.revisoes-container {
    background: var(--paper-color);
    padding: 30px;
    margin: 20px 0;
    border: 2px solid var(--border-color);
    box-shadow: 5px 5px 0 var(--border-color);
    position: relative; /* Adiciona posicionamento relativo */
    overflow: visible; /* Permite que as sombras dos cards apareçam */
}

.revisoes-container h2 {
    color: var(--primary-color);
    text-align: center;
    margin-bottom: 30px;
    font-size: 2rem;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(139, 69, 19, 0.2);
}

.revisoes-lista {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
    padding: 10px;
    margin: 0; /* Reseta margens */
    width: calc(100% - 20px); /* Ajusta a largura considerando o padding */
}

.card-revisao {
    background: var( --color-info-item);
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
    border-left: 5px solid;
    margin: 0; /* Reseta margens */
    max-width: 100%; /* Limita a largura máxima */
    box-sizing: border-box; /* Inclui padding na largura total */
}

.card-revisao h4 {
    color: var(--primary-color);
    margin: 0 0 20px 0;
    font-family: 'Quicksand', sans-serif;
    font-size: 1.3rem;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(139, 69, 19, 0.1);
}

.revisao-capitulo {
    font-weight: bold;
    color: var(--secondary-color);
    margin-bottom: 12px;
    font-size: 1rem;
}

.revisao-descricao {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--text-color);
    margin-bottom: 25px;
}

.avaliacao-confianca {
    margin-top: auto;
    padding-top: 20px;
    border-top: 1px solid rgba(139, 69, 19, 0.1);
}

.avaliacao-confianca p {
    text-align: center;
    margin-bottom: 15px;
    font-family: 'Quicksand', sans-serif;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.botoes-confianca {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
    padding: 0 10px;
}

.btn-confianca {
    padding: 12px;
    border: 1px solid var(--border-color);
    background: var(--paper-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Crimson Text', serif;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    color: var( --secondary-color);
}

.btn-confianca:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(139, 69, 19, 0.2);
}

/* Responsividade */
@media (max-width: 768px) {
    .revisoes-container {
        padding: 20px;
    }

    .botoes-confianca {
        grid-template-columns: 1fr 1fr;
    }

    .revisoes-lista {
        grid-template-columns: 1fr; /* Uma coluna em telas menores */
        padding: 0 15px 15px;
    }

    .card-revisao {
        margin: 0;
    }
}

@media (max-width: 480px) {
    .botoes-confianca {
        grid-template-columns: 1fr;
    }

    .card-revisao {
        padding: 20px;
    }
}

.card-revisao .capitulo-principal {
    margin-bottom: 8px;
    font-style: italic;
    opacity: 0.85;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 5px;
    color: var(--secondary-color);
}

.card-revisao .subcapitulo {
    margin-top: 5px;
    font-weight: bold;
    color: var(--text-color);
}

.card-revisao h4 {
    font-family: 'Quicksand', sans-serif;
    margin: 0;
    font-size: 1.1rem;
    font-weight: bold;
    color: var(--primary-color);
}

/* Mensagens de status */
.sem-revisoes, .erro-revisoes {
    text-align: center;
    padding: 20px;
    background: var(--paper-color);
    border-radius: 8px;
    border: 1px solid var(--border-color);
    font-family: 'Quicksand', sans-serif;
    color: var(--primary-color);
}
.dia{
    background: var(--primary-color);
}

.prova-config {
    background: var(--paper-color);
    padding: 30px;
    margin: 20px 0;
    border: 2px solid var(--border-color);
    box-shadow: 5px 5px 0 var(--border-color);
}

.prova-form {
    max-width: 800px;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-family: 'Quicksand', sans-serif;
    color: var(--primary-color);
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.materias-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.materia-card {
    background: white;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.peso-controls {
    margin-top: 10px;
}

.btn-submit {
    background: var(--primary-color);
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-family: 'Quicksand', sans-serif;
    transition: all 0.3s ease;
}

.btn-submit:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
}

/* Ajuste responsivo */
@media (max-width: 768px) {
    .materias-grid {
        grid-template-columns: 1fr;
    }
}

/* Estilos para o container principal de informações */
.plano-info {
    margin: 30px 0;
}

.info-cards {
    background: var(--paper-color);
    padding: 30px;
    border: 2px solid var(--border-color);
    box-shadow: 5px 5px 0 var(--border-color);
    border-radius: 8px;
    position: relative;
    overflow: hidden;
}

.info-cards.alert-warning {
    background: var(--paper-color);
    border-color: var(--primary-color);
}

/* Cabeçalho elegante */
.info-header {
    text-align: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid rgba(139, 69, 19, 0.1);
    position: relative;
}

.info-header::after {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 2px;
    background: var(--primary-color);
}

.info-header p {
    font-family: 'Quicksand', sans-serif;
    color: var(--secondary-color);
    font-style: italic;
    margin: 0;
    font-size: 1.1rem;
}

/* Grid de informações */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.info-item {
    background: var(--color-info-item);
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    border: 1px solid rgba(139, 69, 19, 0.2);
    transition: all 0.3s ease;
}

.info-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(139, 69, 19, 0.1);
}

.info-item strong {
    display: block;
    color: var(--primary-color);
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 1rem;
    margin-bottom: 8px;
}

.info-item span {
    display: block;
    color: var(--secondary-color);
    font-size: 1.4rem;
    font-weight: bold;
    font-family: 'Roboto Condensed', sans-serif;
}

/* Mensagens de alerta */
.alert-message {
    background: rgba(184, 92, 92, 0.1);
    border-left: 4px solid var(--primary-color);
    padding: 15px 20px;
    margin-top: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-radius: 4px;
}

.alert-message i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.alert-message span {
    color: var(--text-color);
    font-family: 'Open Sans', sans-serif;
    font-size: 1.1rem;
}

.alert-message.warning {
    background: rgba(255, 193, 7, 0.1);
    border-left-color: #ffc107;
}

.alert-message.warning i {
    color: #ffc107;
}

/* Botões de ação */
.actions {
    text-align: center;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid rgba(139, 69, 19, 0.1);
}

.btn-config {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 12px 25px;
    border-radius: 6px;
    text-decoration: none;
    font-family: 'Quicksand', sans-serif;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.btn-config:hover {
    background: var(--paper-color);
    color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Responsividade */
@media (max-width: 768px) {
    .info-cards {
        padding: 20px;
    }

    .info-header h3 {
        font-size: 1.5rem;
    }

    .info-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .info-item {
        padding: 15px;
    }

    .info-item span {
        font-size: 1.2rem;
    }
}

.semana-header {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.semana-numero {
    font-size: 1.4rem;
    color: var(--primary-color);
}

.semana-periodo {
    font-size: 1rem;
    color: var(--secondary-color);
    font-family: 'Quicksand', sans-serif;
    font-style: italic;
}

.data-dia {
    display: block;
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin-top: 5px;
    font-family: 'Crimson Text', serif;
    font-style: italic;
}

.plano-periodo {
    text-align: center;
    margin: 20px 0;
    padding: 15px;
    background: var(--paper-color);
    border: 2px solid var(--border-color);
    box-shadow: 3px 3px 0 var(--border-color);
    font-family: 'Quicksand', sans-serif;
}

.plano-periodo h2 {
    color: var(--primary-color);
    font-size: 1.3rem;
    margin-bottom: 10px;
}

.plano-periodo p {
    color: var(--secondary-color);
    font-size: 1.1rem;
    margin: 5px 0;
}

@media (max-width: 768px) {
    .semana-periodo {
        font-size: 0.9rem;
    }

    .data-dia {
        font-size: 0.8rem;
    }
}

/* Adicionar antes do último } dos estilos */

.flow-info {
    background: var(--paper-color);
    padding: 20px;
    margin-bottom: 30px;
    border: 2px solid var(--border-color);
    box-shadow: 5px 5px 0 var(--border-color);
    border-radius: 8px;
}

.steps-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
    max-width: 800px;
    margin: 0 auto;
    position: relative;
}

.steps-container::before {
    content: "";
    position: absolute;
    top: 25px;
    left: 50px;
    right: 50px;
    height: 2px;
    background: rgba(139, 69, 19, 0.2);
    z-index: 0;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    z-index: 1;
}

.step-number {
    width: 50px;
    height: 50px;
    background: var(--secondary-color);
    color: white;
    border: 2px solid var(--secondary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Quicksand', sans-serif;
    font-size: 1.2rem;
}

.step-text {
    font-family: 'Quicksand', sans-serif;
    color: var(--secondary-color);
    font-size: 0.9rem;
    text-align: center;
}

.quick-nav {
    position: sticky;
    top: 45px; /* Aumentado para ficar abaixo da barra de progresso */
    z-index: 997; /* Diminuído para ficar abaixo da barra de progresso que é 999 */
    display: flex;
    justify-content: center;
    gap: 10px;
    padding: 10px;
    margin: 20px 0;
    background: var(--paper-color);
    border: 2px solid var(--border-color);
    box-shadow: 3px 3px 0 var(--border-color);
    border-radius: 8px;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.nav-btn {
    padding: 10px 20px;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: 'Quicksand', sans-serif;
    color: var(--text-color);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.nav-btn:hover,
.nav-btn.active {
    background: var(--primary-color);
    color: white;
}

.nav-btn i {
    font-size: 1.1rem;
}

@media (max-width: 768px) {
    .quick-nav {
        flex-wrap: wrap;
        top: 40px; /* Um pouco menor no mobile */
        padding: 8px;
    }

    .nav-btn {
        flex: 1;
        min-width: calc(50% - 5px);
        justify-content: center;
    }
}

html {
    scroll-padding-top: 100px; /* Ajuste este valor conforme a altura do seu menu fixo */
}

.semana {
    scroll-margin-top: 100px; /* Mesmo valor que o scroll-padding-top */
}


.quick-nav.nav-hidden {
    transform: translateY(-150%);
    opacity: 0;
    pointer-events: none;
}

/* Ajuste para o cabeçalho dos dias */
.dia h3 {
    z-index: 900; /* Menor que o z-index da quick-nav */
}

.menu-icon {
    width: 70px;
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--primary-color);
    border-radius: 50%;
    margin: 0 auto 20px;
    color: var(--paper-color);
    font-size: 1.8rem;
    transition: all 0.3s ease;
    border: 2px solid var(--border-color);
}

.dia-inativo {
    color: #999;
    font-style: italic;
    padding: 10px;
    background: rgba(0,0,0,0.05);
    border-radius: 4px;
    text-align: center;
}

.dia {
    opacity: 1;
    transition: opacity 0.3s ease;
}

.dia.inativo {
    opacity: 0.7;
}

.data-dia {
    display: block;
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin-top: 5px;
    font-style: italic;
}

.cards-atrasados {
    background: var(--paper-color);
    padding: 30px;
    margin: 30px 0;
    border: 2px solid var(--border-color);
    box-shadow: 5px 5px 0 var(--border-color);
    border-radius: 8px;
}

.cards-atrasados-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid rgba(139, 69, 19, 0.1);
}

.replanejar-container {
    display: none; /* Escondido inicialmente */
    align-items: flex-end;
    gap: 15px;
    margin-left: auto;
    padding-left: 20px;
}

.replanejar-container.visible {
    display: flex; /* Mostra quando tiver a classe visible */
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.replanejar-container .form-group {
    margin: 0;
}

.replanejar-container label {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin-bottom: 5px;
}

.replanejar-container input[type="date"] {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: 'Crimson Text', serif;
    font-size: 0.9rem;
}

.btn-replanejar {
    background: var(--primary-color);
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-family: 'Quicksand', sans-serif;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-replanejar:hover {
    background: var(--hover-color);
    transform: translateY(-2px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.btn-replanejar i {
    font-size: 1rem;
}

.cards-atrasados-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #FFF3F3;
    border-radius: 50%;
    color: var(--primary-color);
    font-size: 1.5rem;
}

.cards-atrasados-title {
    flex-grow: 1;
}

.cards-atrasados-title h2 {
    color: var(--primary-color);
    font-size: 1.8rem;
    margin: 0;
}

.cards-atrasados-title p {
    color: var(--secondary-color);
    font-family: 'Quicksand', sans-serif;
    margin: 5px 0 0 0;
    font-style: italic;
}

.cards-atrasados-counter {
    background: var(--accent-medium);
    padding: 8px 15px;
    border-radius: 20px;
    color: var(--primary-color);
    font-family: 'Quicksand', sans-serif;
    font-weight: bold;
}

.cards-atrasados-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding: 10px;
}

.card-atrasado {
    background: var(--color-info-item);
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}


.card-atrasado.pouco-atrasado {
    border: 1px solid rgba(255, 193, 7, 0.3);
}

.card-atrasado.atrasado {
    border: 1px solid rgba(255, 152, 0, 0.3);
}

.card-atrasado.muito-atrasado {
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.card-atrasado-status {
    font-size: 0.9rem;
    padding: 4px 8px;
    border-radius: 12px;
    font-family: 'Quicksand', sans-serif;
}

.atraso-hoje {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.atraso-dias {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.pouco-atrasado .atraso-dias {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.atrasado .atraso-dias {
    background: rgba(255, 152, 0, 0.1);
    color: #ff9800;
}

.muito-atrasado .atraso-dias {
    background: rgba(244, 67, 54, 0.1);
    color: #f44336;
}

.card-atrasado.adding {
    animation: slideIn 0.3s ease forwards;
}

.card-atrasado.removing {
    animation: slideOut 0.3s ease forwards;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(20px);
    }
}

.card-atrasado.removing {
    transform: translateX(100%);
    opacity: 0;
}

.card-atrasado-header {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 15px;
}

.card-atrasado-materia {
    flex-grow: 1;
}

.card-atrasado-materia h4 {
    color: var(--primary-color);
    font-family: 'Quicksand', sans-serif;
    margin: 0 0 5px 0;
    font-size: 1.2rem;
}

.card-atrasado-content {
    padding-left: 15px;
}

.card-atrasado-capitulo {
    font-family: 'Quicksand', sans-serif;
    color: var(--secondary-color);
    margin-bottom: 10px;
    font-style: italic;
}

.card-atrasado-descricao {
    color: var(--text-color);
    line-height: 1.5;
}

.card-atrasado-data {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid rgba(139, 69, 19, 0.1);
    color: var(--secondary-color);
    font-size: 0.9rem;
    font-style: italic;
}

.sem-cards-atrasados {
    text-align: center;
    padding: 30px;
    background: var(--color-info-item);
    border-radius: 8px;
}

.sem-cards-atrasados i {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 15px;
}

.sem-cards-atrasados p {
    color: var(--secondary-color);
    font-family: 'Quicksand', sans-serif;
    font-size: 1.2rem;
    margin: 0;
}

@media (max-width: 768px) {
    .cards-atrasados {
        padding: 20px;
    }

    .cards-atrasados-grid {
        grid-template-columns: 1fr;
    }

    .cards-atrasados-header {
        flex-direction: column;
        text-align: center;
    }

    .cards-atrasados-counter {
        align-self: center;
    }
}

.card-atrasado.adding {
    animation: slideIn 0.3s ease forwards;
}

@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.dashboard-motivacional {
    margin-top: 20px;
    margin-bottom: 20px;
}

/* Ajuste para mobile */
@media (max-width: 768px) {
    .progresso-cards {
        grid-template-columns: 1fr;
    }
}

.revisoes-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 10px;
}

.tab-button {
    padding: 10px 20px;
    border: none;
    background: none;
    cursor: pointer;
    font-family: 'Quicksand', sans-serif;
    color: var(--secondary-color);
    opacity: 0.7;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tab-button:hover {
    opacity: 1;
}

.tab-button.active {
    color: var(--primary-color);
    opacity: 1;
    border-bottom: 2px solid var(--primary-color);
    margin-bottom: -12px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

.card-ignorado {
    opacity: 0.8;
    transition: all 0.3s ease;
}

.card-ignorado:hover {
    opacity: 1;
}

.status-ignorado {
    font-size: 0.9rem;
    color: var(--secondary-color);
    display: flex;
    align-items: center;
    gap: 5px;
}

.card-actions {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid rgba(139, 69, 19, 0.1);
    display: flex;
    justify-content: center;
}

.btn-reativar {
    padding: 10px 20px;
    background: var(--paper-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    cursor: pointer;
    font-family: 'Quicksand', sans-serif;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-reativar:hover {
    background: var(--primary-color);
    color: white;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.avaliacao-opcoes {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(139, 69, 19, 0.1);
}

.avaliacao-section, .ignorar-section {
    background: rgba(255, 255, 255, 0.5);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--border-color);
}

.avaliacao-section h5, .ignorar-section h5 {
    font-family: 'Quicksand', sans-serif;
    color: var(--primary-color);
    margin: 0 0 15px 0;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.1rem;
}

.botoes-confianca {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
}

.botoes-ignorar {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.btn-ignorar-opcao {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    background: white;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-ignorar-opcao:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

.btn-ignorar-opcao i {
    font-size: 1.5rem;
    margin-bottom: 8px;
}

.btn-ignorar-opcao span {
    font-weight: bold;
    margin-bottom: 4px;
}

.btn-ignorar-opcao small {
    opacity: 0.8;
}

.btn-acao {
    padding: 8px 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background: var(--paper-color);
    color: var(--secondary-color);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-acao:hover {
    background: var(--primary-color);
    color: white;
}

.card-actions {
    display: flex;
    gap: 10px;
}

@media (max-width: 768px) {
    .botoes-confianca, .botoes-ignorar {
        grid-template-columns: 1fr;
    }
}


.dia-horizontal {
    border: 2px solid var(--border-color);
    border-radius: 8px;
    margin-bottom: 15px;
    background: var(--paper-color);
}

.dia-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    cursor: pointer;
    background: var(--accent-medium);
    border-radius: 6px 6px 0 0;
    transition: background-color 0.3s ease;
}

.dia-header:hover {
    background: var(--accent-dark);
}

.dia-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.dia-info h3 {
    font-family: 'Quicksand', sans-serif;
    margin: 0;
    font-size: 1.2rem;
    color: var(--primary-color);
    display: flex;
    flex-direction: column;
}

.conteudos-counter {
    font-size: 0.9rem;
    color: var(--secondary-color);
    background: rgba(255, 255, 255, 0.5);
    padding: 4px 10px;
    border-radius: 12px;
}

.dia-content {
    display: none;
    padding: 20px;
    background: var(--paper-color);
    border-top: 1px solid var(--border-color);
}

.dia-content.active {
    display: block;
}

/* Adicione esse novo estilo para criar o grid de conteúdos */
.dia-content .conteudos-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    padding: 10px;
}

.dia-horizontal.inativo {
    opacity: 0.7;
}

.dia-horizontal .fa-chevron-down {
    transition: transform 0.3s ease;
}

.dia-horizontal.expanded .fa-chevron-down {
    transform: rotate(180deg);
}

/* Adicione um container principal */
.plano-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Para telas muito grandes, podemos reduzir ainda mais os espaços laterais */
@media (min-width: 1600px) {
    .plano-container {
        padding: 0 40px;
    }
}

/* Para telas menores, garantimos que o conteúdo fique adequado */
@media (max-width: 768px) {
    .plano-container {
        padding: 0 15px;
    }
}


/* Estilos para o container das abas */
.tabs-container {
    max-width: 1400px;
    margin: 20px auto;
    background: var(--paper-color);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    box-shadow: 5px 5px 0 var(--border-color);
}



/* Botões das abas */
.tab-btn {
    padding: 12px 20px;
    background: var(--paper-color);
    border: 2px solid transparent;
    border-radius: 6px;
    color: var(--secondary-color);
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    white-space: nowrap;
}

.tab-btn i {
    font-size: 1.1rem;
}

.tab-btn:hover {
    background: var(--accent-dark);
    color: white;
}

.tab-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--border-color);
    transform: translateY(-2px);
    box-shadow: 2px 2px 0 var(--border-color);
}

/* Conteúdo das abas */
.tab-content {
    display: none;
    padding: 20px;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsividade */
@media (max-width: 768px) {
    .tabs-navigation {
        padding: 10px;
    }

    .tab-btn {
        padding: 10px 15px;
        font-size: 0.9rem;
    }
}

/* Estilos base para a navegação das abas */
.tabs-container {
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
}

.tabs-navigation {
    display: flex;
    gap: 5px;
    padding: 15px;
    background: var(--accent-medium);
    border-bottom: 2px solid var(--border-color);
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    justify-content: center;
    flex-wrap: nowrap;
    scroll-behavior: smooth;
}

/* Estilo base para os botões */
.tab-btn {
    white-space: nowrap;
    padding: 10px 20px;
    font-size: 16px;
    transition: all 0.3s ease;
}

/* Desktop grande (1200px e acima) */
@media (min-width: 1200px) {
    .tabs-navigation {
        gap: 15px;
        padding: 20px;
    }

    .tab-btn {
        padding: 12px 25px;
        font-size: 16px;
    }
}

/* Desktop médio (992px a 1199px) */
@media (max-width: 1199px) {
    .tabs-navigation {
        gap: 10px;
        padding: 15px;
    }
}

/* Tablet (768px a 991px) */
@media (max-width: 991px) {
    .tabs-navigation {
        justify-content: flex-start;
        padding: 12px;
        gap: 8px;
    }

    .tab-btn {
        padding: 10px 18px;
        font-size: 15px;
    }
}

/* Mobile grande (576px a 767px) */
@media (max-width: 767px) {
    .tabs-navigation {
        padding: 10px;
        gap: 6px;
    }

    .tab-btn {
        padding: 8px 16px;
        font-size: 14px;
    }

    /* Esconde textos longos e mostra apenas ícones em telas menores */
    .tab-btn span:not(.icon) {
        display: none;
    }

    .tab-btn i {
        margin-right: 0;
        font-size: 18px;
    }
}

/* Mobile pequeno (menos de 576px) */
@media (max-width: 575px) {
    .tabs-navigation {
        padding: 8px;
        gap: 4px;
    }

    .tab-btn {
        padding: 8px 12px;
        font-size: 13px;
        min-width: auto;
    }
}

/* Estilização da barra de rolagem */
.tabs-navigation::-webkit-scrollbar {
    height: 4px;
}

.tabs-navigation::-webkit-scrollbar-track {
    background: rgba(139, 69, 19, 0.05);
    border-radius: 4px;
}

.tabs-navigation::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

/* Indicadores de scroll nas laterais */
.tabs-container {
    position: relative;
}

.tabs-container::before,
.tabs-container::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    width: 30px;
    pointer-events: none;
    z-index: 1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.tabs-container::before {
    left: 0;
    background: linear-gradient(to right, rgba(255,255,255,0.9), transparent);
}

.tabs-container::after {
    right: 0;
    background: linear-gradient(to left, rgba(255,255,255,0.9), transparent);
}

.tabs-navigation.scroll-left::before {
    opacity: 1;
}

.tabs-navigation.scroll-right::after {
    opacity: 1;
}

.header {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 20px;
    align-items: center;
    padding: 15px 20px;
    background: var(--paper-color);
    border-bottom: 2px solid var(--border-color);
    margin-bottom: 30px;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.header-left {
    display: flex;
    align-items: center;
}

.header-center {
    flex-grow: 1;
    padding: 0 20px;
}



.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 15px;
    background: var(--color-info-item);
    border-radius: 20px;
    font-size: 0.9rem;
}

.user-info i {
    color: var(--primary);
    font-size: 1.2rem;
}

.user-name {
    color: var(--text);
    font-weight: 600;
}


/* Ajustes para responsividade */
@media (max-width: 1024px) {
    .header {
        grid-template-columns: auto 1fr;
        padding: 10px;
    }

    .header-right {
        grid-column: 1 / -1;
        justify-content: center;
    }

    .progress-text {
        font-size: 0.9rem;
        min-width: 100px;
    }
}

@media (max-width: 768px) {
    .header {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .header-left {
        justify-content: center;
    }

    .logo img {
        height: 40px;
    }

    .progress-container {
        flex-direction: column;
        gap: 5px;
    }

    .progress-text {
        text-align: center;
    }

    .user-info {
        font-size: 0.8rem;
        padding: 6px 12px;
    }
}

/* Ajuste no padding do body para compensar o header fixo */
body {
    padding-top: 120px;
}

/* Estilos do botão Replanejar */
.btn-replanejar {
    background: linear-gradient(145deg, var(--primary-color), var(--hover-color));
    color: var(--text-light-color);
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    margin-left: 15px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px var(--shadow-md);
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-replanejar:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px var(--shadow-lg);
    background: linear-gradient(145deg, var(--hover-color), var(--active-color));
}

.btn-replanejar:active {
    transform: translateY(0);
    box-shadow: 0 4px 6px var(--shadow-md);
}

.btn-replanejar i {
    font-size: 16px;
    transition: transform 0.3s ease;
}

.btn-replanejar:hover i {
    transform: rotate(-15deg);
}


.countdown-box {
    background: var(--primary-color);
    color: var(--text-light-color);
    padding: 20px;
    border-radius: 10px;
    margin: 20px auto;
    max-width: 200px;
}

.countdown-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.countdown-label {
    font-size: 1rem;
    opacity: 0.9;
}

.info-header {
    text-align: center;
    margin-bottom: 30px;
    position: relative;
    padding-bottom: 20px;
    border-bottom: 2px solid rgba(139, 69, 19, 0.1);
}

.info-header h3 {
    font-family: 'Quicksand', sans-serif;
    color: var(--primary-color);
    font-size: 1.8rem;
    margin: 0 0 10px 0;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.btn-cronometro {
    background: var(--primary-opacity-10);  
    border: 2px solid var(--text-light-color);
    color: var(--text-light-color);
    font-family: 'Quicksand', sans-serif;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    margin-top: 0.8rem;
    width: 100%;
    box-shadow: 2px 2px 0 var(--shadow-sm);
    cursor: pointer;
 }
 
 .btn-cronometro:hover {
    background: var(--hover-color);
    color: var(--text-light-color);
    transform: translateY(-2px);
    box-shadow: 3px 3px 0 var(--shadow-md);
    border: 2px solid var(--hover-color);
 }
 
 .btn-cronometro:active {
    background: var(--active-color);
    transform: translateY(0);
    box-shadow: 1px 1px 0 var(--shadow-sm);
 }
 
 .btn-cronometro i {
    margin-right: 0.5rem;
 }

 /* Estilo para o container da pesquisa */
 .search-container {
     display: flex;
     align-items: center;
     margin: 25px auto;
     max-width: 500px;
     width: 95%;
     position: relative;
     background: var(--paper-color);
     border-radius: 30px;
     box-shadow: 0 4px 15px var(--shadow-sm);
     transition: all 0.3s ease;
 }
 
 /* Estilo para o campo de input */
 #search-topic {
     flex: 1;
     padding: 16px 25px;
     padding-left: 50px;
     border: 2px solid var(--border-color);
     border-radius: 30px;
     font-size: 1rem;
     color: var(--text-color);
     background-color: transparent;
     transition: all 0.3s ease;
     width: 100%;
 }
 
 #search-topic:focus {
     outline: none;
     border-color: var(--primary-color);
     box-shadow: 0 6px 20px var(--shadow-md);
 }
 
 /* Estilo para o ícone de pesquisa */
 .search-container .fa-search {
     position: absolute;
     left: 20px;
     top: 50%;
     transform: translateY(-50%);
     color: var(--primary-color);
     font-size: 1.2rem;
     opacity: 0.7;
     transition: all 0.3s ease;
 }
 
 /* Efeito hover no container */
 .search-container:hover {
     box-shadow: 0 6px 20px var(--shadow-md);
 }
 
 .search-container:hover .fa-search {
     opacity: 1;
 }
 
 /* Placeholder personalizado */
 #search-topic::placeholder {
     color: var(--text-muted);
     opacity: 0.8;
     font-size: 0.95rem;
 }
 
 /* Responsividade */
 @media (max-width: 768px) {
     .search-container {
         margin: 20px 15px;
         width: auto;
     }
     
     #search-topic {
         padding: 14px 20px;
         padding-left: 45px;
         font-size: 0.95rem;
     }
     
     .search-container .fa-search {
         left: 15px;
         font-size: 1.1rem;
     }
 }

/* Estilo para o dia completo */
.dia-horizontal.completed {
    background: linear-gradient(145deg, rgba(40, 167, 69, 0.1), rgba(40, 167, 69, 0.2));
    border-left: 4px solid #28a745;
    transition: all 0.3s ease;
}

.dia-horizontal.completed .dia-header {
    background: rgba(40, 167, 69, 0.1);
    color: #2a28a7;
}

.dia-horizontal.completed .dia-header,
.dia-horizontal.completed .dia-header .conteudos-counter {
    color: #28a745;
}

.dia-horizontal.completed .dia-header i {
    color: #28a745;
}



/* Efeito hover no dia completo */
.dia-horizontal.completed .dia-header:hover {
    background: var(--accent-dark);
}

.contador-revisoes {
    background-color: var(--primary-color);
    color: white;
    border-radius: 12px;
    padding: 2px 8px;
    font-size: 0.8em;
    margin-left: 8px;
}

.tab-button:not(.active) .contador-revisoes {
    background-color: var(--text-muted);
}



/* Ajuste para o troféu nos cards de estatística */
.stat-card.materia-completa {
    position: relative;
    border: 2px solid var(--primary-color) !important;
    /* Removi o background gradient para manter a cor original */
    animation: completionPulse 1s ease-in-out;
}

.stat-card .trophy-icon {
    position: absolute;
    top: -10px;
    right: -10px;
    background: var(--primary-color);
    color: white;
    padding: 8px;
    border-radius: 50%;
    font-size: 1em;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    animation: trophyBounce 1s ease-in-out;
    z-index: 1;
}

.btn-anotacoes {
    background: var(--primary-opacity-10);  
    border: 2px solid var(--text-light-color);
    color: var(--text-light-color);
    font-family: 'Quicksand', sans-serif;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    margin-top: 0.8rem;
    width: 100%;
    box-shadow: 2px 2px 0 var(--shadow-sm);
    cursor: pointer;
}

.btn-anotacoes:hover {
    background: var(--hover-color);
    color: var(--text-light-color);
    transform: translateY(-2px);
    box-shadow: 3px 3px 0 var(--shadow-md);
    border: 2px solid var(--hover-color);
}

.btn-anotacoes:active {
    background: var(--active-color);
    transform: translateY(0);
    box-shadow: 1px 1px 0 var(--shadow-sm);
}

.btn-anotacoes i {
    margin-right: 0.5rem;
}


.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    max-width: 400px;
    width: 90%;
    text-align: center;
}

.modal-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-top: 20px;
}

.modal-button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

.btn-sim {
    background-color: #4CAF50;
    color: white;
}

.btn-nao {
    background-color: #f44336;
    color: white;
}

.btn-ok {
    background-color: #2196F3;
    color: white;
}

.semana-prova-aviso {
    background: linear-gradient(135deg, #4A90E2 0%, #4A90E2 100%);
    color: var(--primary-color);
    padding: 20px;
    margin: 20px 0;
    border-radius: 10px;
    border: 2px solid var(--primary-color);;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.semana-prova-header {
    font-size: 1.4em;
    font-weight: bold;
    text-align: center;
    margin-bottom: 15px;
    color: var(--primary-color);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.semana-prova-datas {
    text-align: center;
    font-size: 1.1em;
    margin-bottom: 15px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 5px;
}

.semana-prova-info {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px dashed var(--primary-color);
}

.info-item_semana_prova {
    background: white;
    padding: 10px 20px;
    border-radius: 25px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.95em;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s;
}

.info-item_semana_prova:hover {
    transform: translateY(-2px);
}

.info-item_semana_prova i {
    color: var(--primary-color);;
}

.semana-prova-aviso i {
    margin: 0 10px;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.semana-prova-header i {
    color: var(--primary-color);
    animation: pulse 2s infinite;
}




.reset-container.visible {
    display: block; /* Mostra quando tiver a classe visible */
}

.reset-container {
    display: none;
    margin: 20px 0;
    text-align: right;
    transition: all 0.3s ease;
}

.btn-reset {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: auto;
}

.btn-reset:hover {
    background-color: #c82333;
}

.btn-reset i {
    font-size: 16px;
}

/* Modal de Reset */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    animation: fadeIn 0.3s;
}

.modal-content {
    position: relative;
    background-color: #fff;
    margin: 15% auto;
    padding: 0;
    width: 90%;
    max-width: 500px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    animation: slideIn 0.3s;
}

.modal-header {
    background-color: #f8f9fa;
    padding: 20px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.modal-header i {
    color: #dc3545;
    font-size: 24px;
}

.modal-header h2 {
    margin: 0;
    color: #343a40;
    font-size: 1.5rem;
}

.modal-body {
    padding: 25px 20px;
}

.modal-body p {
    margin: 0 0 15px;
    font-size: 1.1rem;
    color: #495057;
}

.modal-body .warning-text {
    color: #dc3545;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.modal-body .warning-text i {
    font-size: 1.2rem;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    gap: 15px;
}

.modal-footer button {
    padding: 10px 20px;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
    transition: all 0.3s;
}

.btn-cancelar {
    background-color: #6c757d;
    color: white;
}

.btn-cancelar:hover {
    background-color: #5a6268;
}

.btn-confirmar {
    background-color: #dc3545;
    color: white;
}

.btn-confirmar:hover {
    background-color: #c82333;
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Responsividade */
@media (max-width: 576px) {
    .modal-content {
        width: 95%;
        margin: 30% auto;
    }

    .modal-footer {
        flex-direction: column;
    }

    .modal-footer button {
        width: 100%;
        justify-content: center;
    }
}

/* Estilo para o container do logo */
.logo {
    position: relative;
    height: 50px;  /* Altura fixa igual à das imagens */
    width: auto;
    display: flex;
    align-items: center;
}

/* Estilo base para ambas as imagens do logo */
.logo img {
    position: absolute;
    top: 0;
    left: 0;
    height: 50px;
    width: auto;
    transition: opacity 0.3s ease;
}

/* Estilo específico para o logo escuro (inicialmente oculto) */
.logo-dark {
    opacity: 0;
}

/* Quando o modo escuro está ativo */
.modo-noturno .logo-light {
    opacity: 0;
}

.modo-noturno .logo-dark {
    opacity: 1;
}

/* Ajustes responsivos */
@media (max-width: 768px) {
    .logo {
        height: 40px;
    }
    
    .logo img {
        height: 40px;
    }
}

.actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

/* Adicione estes estilos ao seu arquivo CSS */
.card-atrasado-capitulo-principal {
    margin-bottom: 8px;
    padding-bottom: 5px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    font-weight: 500;
}

.card-atrasado-descricao {
    margin-top: 5px;
    margin-bottom: 5px;
}

.semana-info-message {
    background-color: rgba(0, 123, 255, 0.1);
    color: #0d6efd;
    padding: 8px 12px;
    margin-bottom: 15px;
    border-radius: 4px;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.semana-info-message i {
    font-size: 1.1rem;
}

