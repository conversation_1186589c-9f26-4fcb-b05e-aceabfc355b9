<?php
include 'conexao_POST.php';
include 'processa_index.php';
include_once 'funcoes.php'; // Inclua as funções aqui

// Verifica se $id_planejamento e $id_usuario estão definidos
if (!isset($id_planejamento) || !isset($id_usuario)) {
    die("ID do planejamento ou ID do usuário não definido.");
}

// Consultar os IDs e nomes das matérias associadas ao planejamento
$query_consultar_materias_planejamento = "
    SELECT pm.materia_idmateria, m.nome AS nome_materia, m.cor AS cor_materia
    FROM appEstudo.planejamento_materia pm
    INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    WHERE pm.planejamento_idplanejamento = $id_planejamento";

$resultado_materias_planejamento = pg_query($conexao, $query_consultar_materias_planejamento);

// Inicializa arrays para armazenar os dados das matérias
$materias_no_planejamento = array();
$cores_materias = array();

// Preenche os arrays com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_materias_planejamento)) {
    $materia_id = $row['materia_idmateria'];
    $nome_materia = $row['nome_materia'];
    $cor_materia = $row['cor_materia'];

    $materias_no_planejamento[$materia_id] = $nome_materia;
    $cores_materias[$nome_materia] = $cor_materia;
}

// Realiza a consulta ao banco de dados para obter os dados de estudo por ponto estudado de cada matéria no planejamento
$query_consulta_pontos_estudo = "
    SELECT m.nome AS nome_materia, 
           e.ponto_estudado AS ponto_estudado,
           SUM(e.tempo_liquido) AS tempo_estudo
    FROM appEstudo.materia m
    LEFT JOIN appEstudo.estudos e ON m.idmateria = e.materia_idmateria 
        AND e.planejamento_usuario_idusuario = $id_usuario
    WHERE m.idmateria IN (" . implode(',', array_keys($materias_no_planejamento)) . ")
    GROUP BY m.nome, e.ponto_estudado";

$resultado_consulta_pontos_estudo = pg_query($conexao, $query_consulta_pontos_estudo);

// Inicializa um array para armazenar os dados de tempo de estudo por ponto estudado para cada matéria
$pontos_estudo_por_materia = array();
$tempo_total_por_materia = array();

// Preenche o array com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_consulta_pontos_estudo)) {
    $materia = $row['nome_materia'];
    $ponto_estudado = $row['ponto_estudado'];
    $tempo_estudo = $row['tempo_estudo']; // Tempo no formato "HH:MM:SS"

    // Converte o tempo de estudo para segundos
    $tempo_estudo_segundos = $tempo_estudo ? converterParaSegundos($tempo_estudo) : 0;

    // Adiciona os tempos de estudo por ponto estudado para a matéria correspondente
    if (!isset($pontos_estudo_por_materia[$materia])) {
        $pontos_estudo_por_materia[$materia] = array();
    }
    $pontos_estudo_por_materia[$materia][$ponto_estudado] = $tempo_estudo_segundos;

    // Calcula o tempo total por matéria
    if (!isset($tempo_total_por_materia[$materia])) {
        $tempo_total_por_materia[$materia] = 0;
    }
    $tempo_total_por_materia[$materia] += $tempo_estudo_segundos;
}

// Convertendo os arrays para JSON para serem utilizados no JavaScript
$dados_json = json_encode($pontos_estudo_por_materia);
$cores_json = json_encode($cores_materias);
$tempo_total_json = json_encode($tempo_total_por_materia);
?>

<div id="radar"></div>
<style>
    .highcharts-title {
        font-family: 'Indie Flower', cursive;
    }
    .highcharts-xaxis-labels text {
        font-family: "Courier Prime", monospace;
    }
    .highcharts-yaxis-labels text, .highcharts-tooltip text {
        font-family: "Courier Prime", monospace;
    }
</style>

<script>
    // Recuperando os dados do PHP
    var materias = <?php echo json_encode(array_keys($tempo_total_por_materia)); ?>;
    var tempoTotalPorMateria = <?php echo json_encode(array_values($tempo_total_por_materia)); ?>;

    // Função para converter segundos em HHh:MMmin
    function converterParaHHMM(segundos) {
        var horas = Math.floor(segundos / 3600);
        var minutos = Math.floor((segundos % 3600) / 60);
        return horas + "h:" + (minutos < 10 ? "0" + minutos : minutos) + "min";
    }

    Highcharts.chart('radar', {

        chart: {
            polar: true,
            type: 'line',
            backgroundColor: 'rgba(0, 0, 0, 0)', // Define o fundo como transparente
            marginTop: 80 // Ajusta a margem superior para dar espaço ao título
        },

        title: {
            text: 'Pontos <span style="color: green;">Fortes</span> e <span style="color: red;">Fracos</span>: Relação ao Tempo de Estudo',
            align: 'center', // Centraliza o título
            x: 0 // Define o deslocamento horizontal para centralizar totalmente
        },

        pane: {
            size: '90%'
        },

        xAxis: {
            categories: materias,
            tickmarkPlacement: 'on',
            gridLineColor:'red',
            lineWidth: 0
        },

        yAxis: {
            gridLineInterpolation: 'polygon',
            gridLineColor:'red',
            lineWidth: 0,
            min: 0,
            labels: {
                formatter: function() {
                    return converterParaHHMM(this.value);
                }
            }
        },

        tooltip: {
            shared: true,
            pointFormatter: function() {
                return '<span style="color:' + this.color + '">' + this.series.name + ': <b>' +
                    converterParaHHMM(this.y) + '</b><br/>';
            }
        },

        legend: {
            align: 'right',
            verticalAlign: 'middle',
            layout: 'vertical'
        },

        series: [{
            name: 'Tempo de Estudo',
            data: tempoTotalPorMateria,
            pointPlacement: 'on',
            showInLegend: false // Oculta o nome da série na legenda e no rodapé
        }],

        credits: {
            enabled: false
        },

        responsive: {
            rules: [{
                condition: {
                    maxWidth: 500
                },
                chartOptions: {
                    legend: {
                        align: 'center',
                        verticalAlign: 'bottom',
                        layout: 'horizontal'
                    },
                    pane: {
                        size: '70%'
                    }
                }
            }]
        }

    });
</script>
