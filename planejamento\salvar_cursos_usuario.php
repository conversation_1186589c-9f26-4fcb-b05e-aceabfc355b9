<?php
session_start();
include_once("../assets/config.php");

header('Content-Type: application/json');

if (!isset($_SESSION['idusuario'])) {
    echo json_encode(['success' => false, 'message' => 'Usuário não autenticado']);
    exit;
}

$input = json_decode(file_get_contents('php://input'), true);
$cursos = $input['cursos'] ?? [];
$id_usuario = $_SESSION['idusuario'];

pg_query($conexao, "BEGIN");

try {
    // Remove todos os cursos existentes do usuário
    $query_delete = "DELETE FROM appEstudo.usuario_has_curso WHERE usuario_idusuario = $1";
    pg_query_params($conexao, $query_delete, array($id_usuario));

    // Insere os novos cursos
    if (!empty($cursos)) {
        $values = array();
        $params = array();
        $i = 1;
        
        foreach ($cursos as $curso_id) {
            $values[] = "($" . $i . ", $" . ($i + 1) . ")";
            $params[] = $id_usuario;
            $params[] = $curso_id;
            $i += 2;
        }
        
        $query_insert = "INSERT INTO appEstudo.usuario_has_curso (usuario_idusuario, curso_idcurso) VALUES " . implode(", ", $values);
        $result = pg_query_params($conexao, $query_insert, $params);
        
        if (!$result) {
            throw new Exception(pg_last_error($conexao));
        }
    }

    pg_query($conexao, "COMMIT");
    echo json_encode(['success' => true, 'message' => 'Cursos salvos com sucesso']);
} catch (Exception $e) {
    pg_query($conexao, "ROLLBACK");
    error_log("Erro ao salvar cursos: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Erro ao salvar cursos: ' . $e->getMessage()]);
}

pg_close($conexao);
?>
