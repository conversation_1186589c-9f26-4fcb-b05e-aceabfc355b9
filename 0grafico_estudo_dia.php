<?php
//0grafico_estudo_dia.php


include_once 'funcoes.php';

if (!isset($id_planejamento) || !isset($id_usuario)) {
    die("ID do planejamento ou ID do usuário não definido.");
}

$query_consultar_materias_planejamento = "
    SELECT pm.materia_idmateria, m.nome AS nome_materia, m.cor AS cor_materia
    FROM appEstudo.planejamento_materia pm
    INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    WHERE pm.planejamento_idplanejamento = $id_planejamento";

$resultado_materias_planejamento = pg_query($conexao, $query_consultar_materias_planejamento);

$materias_no_planejamento_Grafico = array();
$cores_materias = array();

while ($row = pg_fetch_assoc($resultado_materias_planejamento)) {
    $materia_id = $row['materia_idmateria'];
    $nome_materia = $row['nome_materia'];
    $cor_materia = $row['cor_materia'];

    $materias_no_planejamento_Grafico[$materia_id] = $nome_materia;
    $cores_materias[$nome_materia] = $cor_materia;
}

$data_atual = date('Y-m-d');

$query_consulta_pontos_estudo = "
    SELECT m.nome AS nome_materia, 
           e.ponto_estudado AS ponto_estudado,
           SUM(e.tempo_liquido) AS tempo_estudo
    FROM appEstudo.estudos e
    INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
    INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
    LEFT JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria
    WHERE u.idusuario = $id_usuario 
      AND e.materia_idmateria IN (" . implode(',', array_keys($materias_no_planejamento_Grafico)) . ")
      AND DATE(e.data) = '$data_atual'
    GROUP BY m.nome, e.ponto_estudado";

$resultado_consulta_pontos_estudo = pg_query($conexao, $query_consulta_pontos_estudo);

$houve_estudo = pg_num_rows($resultado_consulta_pontos_estudo) > 0;

$pontos_estudo_por_materia = array();
$tempo_total_por_materia = array();

while ($row = pg_fetch_assoc($resultado_consulta_pontos_estudo)) {
    $materia = $row['nome_materia'];
    $ponto_estudado = $row['ponto_estudado'];
    $tempo_estudo = $row['tempo_estudo'];

    $tempo_estudo_segundos = converterParaSegundos($tempo_estudo);

    if (!isset($pontos_estudo_por_materia[$materia])) {
        $pontos_estudo_por_materia[$materia] = array();
    }
    $pontos_estudo_por_materia[$materia][$ponto_estudado] = $tempo_estudo_segundos;

    if (!isset($tempo_total_por_materia[$materia])) {
        $tempo_total_por_materia[$materia] = 0;
    }
    $tempo_total_por_materia[$materia] += $tempo_estudo_segundos;
}

$dados_json = json_encode($pontos_estudo_por_materia);
$cores_json = json_encode($cores_materias);
$tempo_total_json = json_encode($tempo_total_por_materia);
$houve_estudo_json = json_encode($houve_estudo);

$query = "SELECT tempo_planejamento FROM appEstudo.planejamento WHERE usuario_idusuario = $id_usuario";
$result = pg_query($conexao, $query);

if ($result && pg_num_rows($result) > 0) {
    $row = pg_fetch_assoc($result);
    $tempo_planejamento = $row['tempo_planejamento'];

    function converterTempoParaSegundos_E($tempo) {
        $partes = explode(':', $tempo);
        return ($partes[0] * 3600) + ($partes[1] * 60);
    }

    $tempoPlanejamento = converterTempoParaSegundos_E($tempo_planejamento);
}

function segundosParaHoraMinuto($segundos) {
    $horas = floor($segundos / 3600);
    $minutos = floor(($segundos % 3600) / 60);
    return sprintf("%02dh:%02dmin", $horas, $minutos);
}

$tempo_meta = $tempoPlanejamento;

function calcularTempoRestanteOuExcedente($tempoPlanejamento, $tempoTotalDoDia) {
    if ($tempoTotalDoDia >= $tempoPlanejamento) {
        return "👏 Parabéns! Você bateu a meta diária! 🎉";
    } else {
        $diferenca = $tempoPlanejamento - $tempoTotalDoDia;
        return "<b>Falta</b> <span style=\"color: orange;\"><b>" . segundosParaHoraMinuto($diferenca) . "</b></span> para bater a meta diária de <span style=\"color: green;  font-weight: bold;\">" . segundosParaHoraMinuto($tempoPlanejamento) . "</span>";
    }
}

$tempoRestanteOuExcedente = calcularTempoRestanteOuExcedente($tempoPlanejamento, array_sum($tempo_total_por_materia));
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title>Análise de Tempo de Estudo</title>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>


        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 1rem;
            font-family: 'Quicksand', sans-serif;
        }

        .warning-card {
            /*background: white;*/
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            margin: 1rem auto;
            max-width: 600px;
            transition: all 0.3s ease;
        }

        .warning-header {
            background: var(--primary-blue);

            text-align: center;
        }

        .warning-header span {
            background: #ff0000;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            font-weight: bold;
            font-family: 'Cinzel', serif;
        }

        .warning-body {
            padding: 2rem;
            text-align: center;
        }

        .warning-message {
            font-size: 1.2rem;
            color: #333;
            margin: 1.5rem 0;
            font-weight: 500;
        }

        .constancy-message {
            font-size: 1.4rem;
            color: #800020;
            margin: 2rem 0;
            font-weight: bold;
            border-top: 2px solid #b8860b;
            border-bottom: 2px solid #b8860b;
            padding: 1rem 0;
        }

        .daily-goal {
            font-family: 'Courier Prime', monospace;
            font-size: 1.2rem;
            text-align: right;
            margin-top: 2rem;
        }

        .goal-time {
            background: var(--primary-blue);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            font-weight: bold;
            display: inline-block;
            margin-left: 0.5rem;
        }

        .estatistica-header {


            color: white;

        }

        .estatistica-titulo {
            font-size: 1.2rem;
            font-weight: 600;
            letter-spacing: 0.5px;
            font-family: 'Cinzel', serif;
            margin-bottom: 0.5rem;
        }

        .estatistica-subtitulo {
            font-size: 0.9rem;
            opacity: 0.8;
            font-family: 'Courier Prime', monospace;
        }


    </style>
</head>
<body>
<?php if (!$houve_estudo): ?>
    <div class="container">
        <div class="warning-card">
            <div class="warning-header">
                <span>ATENÇÃO!</span>
            </div>
            <div class="warning-body">
                <div class="warning-message">
                    Você ainda não registrou nenhum estudo hoje!
                </div>
                <div class="constancy-message">
                    A CONSTÂNCIA é mais importante que a INTENSIDADE!
                </div>
                <div class="daily-goal">
                    Sua meta DIÁRIA é
                    <span class="goal-time"><?php echo segundosParaHoraMinuto($tempo_meta); ?></span>
                </div>
            </div>
        </div>
    </div>
<?php else: ?>


    <div class="card-header">
        <h3>Distribuição do Tempo Estudado Hoje</h3>
    </div>
            <div id="pontoChart2"></div>


<?php endif; ?>

<script>
    var dados = <?php echo $dados_json; ?>;
    var cores = <?php echo $cores_json; ?>;
    var tempoTotalPorMateria = <?php echo $tempo_total_json; ?>;
    var houveEstudo = <?php echo $houve_estudo_json; ?>;
    var tempoPlanejamento = <?php echo $tempoPlanejamento ?>;
    var metadeTempoPlanejamentoSegundos = tempoPlanejamento / 2;
    var tempoRestanteOuExcedente = '<?php echo $tempoRestanteOuExcedente; ?>';

    if (houveEstudo) {
        var tempoTotalDoDia = 0;
        for (var materia in tempoTotalPorMateria) {
            tempoTotalDoDia += tempoTotalPorMateria[materia];
        }

        var horasTotal = Math.floor(tempoTotalDoDia / 3600);
        var minutosTotal = Math.floor((tempoTotalDoDia % 3600) / 60);
        var tempoTotalFormatado = horasTotal + '<span style="font-size: 20px;">h</span>' + minutosTotal + '<span style="font-size: 20px;">min</span>';

        var tempoTotal = tempoTotalDoDia;
        var corTexto = '';

        if (tempoTotal < metadeTempoPlanejamentoSegundos) {
            corTexto = 'rgba(255,0,0,0.74)';
        } else if (tempoTotal < tempoPlanejamento) {
            corTexto = '#cabb53';
        } else {
            corTexto = '#269c0f';
        }

        var subtitleText = '<span style="font-weight: bold; font-size: 20px; font-family: \'Courier Prime\', monospace; ">Tempo Total: </span>' +
            '<span style="font-weight: bold; font-size: 30px; font-family: \'Courier Prime\', monospace; color: ' + corTexto + ';">' + tempoTotalFormatado + '</span><br>' +
            '<p style="text-align: center; font-family: \'Courier Prime\', monospace; font-size: 12px;">' + tempoRestanteOuExcedente + '</p>';

        var seriesData = [];
        var drilldownSeries = [];

        for (var materia in tempoTotalPorMateria) {
            var totalTempoEstudo = tempoTotalPorMateria[materia];
            var horas = Math.floor(totalTempoEstudo / 3600);
            var minutos = Math.floor((totalTempoEstudo % 3600) / 60);
            var formattedTime = horas + 'h ' + minutos + 'm';

            seriesData.push({
                name: materia,
                y: totalTempoEstudo,
                formattedTime: formattedTime,
                drilldown: materia,
                color: cores[materia]
            });

            var data = [];
            for (var pontoEstudado in dados[materia]) {
                var tempoEstudo = parseInt(dados[materia][pontoEstudado]);
                var horasPontoEstudo = Math.floor(tempoEstudo / 3600);
                var minutosPontoEstudo = Math.floor((tempoEstudo % 3600) / 60);
                var formattedTimePontoEstudo = horasPontoEstudo + 'h ' + minutosPontoEstudo + 'm';
                data.push({
                    name: pontoEstudado,
                    y: tempoEstudo,
                    formattedTime: formattedTimePontoEstudo
                });
            }

            drilldownSeries.push({
                name: materia,
                id: materia,
                data: data
            });
        }

        Highcharts.chart('pontoChart2', {
            chart: {
                 height: 450,  // Aumentado para 600px
                type: 'pie',
                backgroundColor: 'transparent',
                style: {
                    fontFamily: 'Quicksand, sans-serif'
                }
            },
            title: {
                text: null,
                style: {
                    fontFamily: 'Cinzel, serif',
                    fontSize: '1.5rem',
                    color: '#800020'
                }
            },
            credits: {
                enabled: false
            },
            plotOptions: {
                pie: {
                    innerSize: '50%',
                    depth: 45,
                    dataLabels: {
                        enabled: true,
                        format: '{point.name}<br>{point.percentage:.1f}%<br>{point.formattedTime}',
                        style: {
                            fontFamily: 'Quicksand, sans-serif',
                            fontSize: '0.9rem',
                            color: 'var(--text)',
                            textOutline: 'none'
                        },
                        distance: 20
                    },
                    showInLegend: true
                }
            },
            legend: {
                align: 'right',
                verticalAlign: 'middle',
                layout: 'vertical',
                itemStyle: {
                    fontFamily: 'Quicksand, sans-serif',
                    fontWeight: 'normal',
                    color: 'var(--text)'
                }
            },
            tooltip: {
                headerFormat: '',
                pointFormat: '<span style="color:{point.color}">\u25CF</span> <b>{point.name}</b><br>' +
                    'Tempo: <b>{point.formattedTime}</b><br>' +
                    'Porcentagem: <b>{point.percentage:.1f}%</b>',
                style: {
                    fontFamily: 'Quicksand, sans-serif'
                }
            },
            series: [{
                name: 'Matérias',
                colorByPoint: true,
                data: seriesData
            }],
            drilldown: {
                series: drilldownSeries,
                activeDataLabelStyle: {
                    color: 'var(--text)'
                }
            },
            subtitle: {
                useHTML: true,
                text: subtitleText,
                align: 'center',
                verticalAlign: 'bottom'
            }
        });
    }
</script>
</body>
</html>