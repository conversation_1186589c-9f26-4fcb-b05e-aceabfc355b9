<?php
// editar_categoria.php
session_start();
include_once("assets/config.php");
include_once("helpers.php");

if (!isset($_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit();
}

redirectIfNotAdmin($conexao, $_SESSION['idusuario']);

if (!isset($_GET['id'])) {
    header("Location: flashcards.php");
    exit();
}

$categoria_id = (int)$_GET['id'];
$mensagem = '';

// Buscar dados da categoria
$query_categoria = "SELECT * FROM appestudo.flashcard_categories WHERE id = $1";
$result_categoria = pg_query_params($conexao, $query_categoria, array($categoria_id));
$categoria = pg_fetch_assoc($result_categoria);

if (!$categoria) {
    header("Location: flashcards.php");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nome = trim($_POST['nome']);
    $descricao = trim($_POST['descricao']);
    $status = isset($_POST['status']) ? true : false;
    
    if (!empty($nome)) {
        $query = "UPDATE appestudo.flashcard_categories 
                 SET nome = $1, descricao = $2, status = $3 
                 WHERE id = $4";
        
        $result = pg_query_params($conexao, $query, array($nome, $descricao, $status, $categoria_id));
        
        if ($result) {
            header("Location: flashcards.php");
            exit();
        } else {
            $mensagem = "Erro ao atualizar categoria. Tente novamente.";
        }
    } else {
        $mensagem = "O nome da categoria é obrigatório.";
    }
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Categoria - <?php echo htmlspecialchars($categoria['nome']); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Old+Standard+TT:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Quicksand:wght@300..700&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Tektur:wght@400..900&family=Varela+Round&display=swap" rel="stylesheet">
    <style>
        /* Mesmo CSS do criar_categoria.php */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');
        :root {
            --primary-color: #000080;
            --paper-color: #FFFFFF;
            --secondary-color: #4169E1;
            --background-color: #E8ECF3;
            --text-color: #2C3345;
            --border-color: #B8C2CC;
        }

        body {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 40px;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .form-container {
            background: var(--paper-color);
            padding: 30px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 4px 4px 0 var(--border-color);
        }

        h1 {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            color: var(--primary-color);
            margin-bottom: 30px;
            font-size: 2rem;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            color: var(--primary-color);
            margin-bottom: 5px;
            font-weight: bold;
        }

        input[type="text"],
        textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            font-size: 1rem;
            background: white;
            color: var(--text-color);
            box-sizing: border-box;
        }

        textarea {
            min-height: 120px;
            resize: vertical;
        }

        .actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        .btn {
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            font-size: 1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            border: none;
        }

        .btn-secondary {
            background: var(--paper-color);
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn-primary:hover {
            background: var(--secondary-color);
        }

        .error-message {
            background: #FEE;
            border: 1px solid #FAA;
            color: #A00;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .btn-back {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 50px;
            height: 50px;
            background: var(--paper-color);
            border: 2px solid var(--border-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .btn-back:hover {
            transform: translateY(-2px);
            background: var(--primary-color);
            color: white;
        }

        @media (max-width: 768px) {
            body {
                padding: 20px;
            }

            .actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
        /* Adicionar estilos para o switch de status */
        .switch-container {
            margin-top: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--primary-color);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .switch-label {
            color: var(--primary-color);
            font-weight: bold;
        }
    </style>
</head>
<body>
    <a href="flashcards.php" class="btn-back">
        <i class="fas fa-arrow-left"></i>
    </a>

    <div class="container">
        <div class="form-container">
            <h1>Editar Categoria</h1>

            <?php if (!empty($mensagem)): ?>
                <div class="error-message">
                    <?php echo htmlspecialchars($mensagem); ?>
                </div>
            <?php endif; ?>

            <form method="POST">
                <div class="form-group">
                    <label for="nome">Nome da Categoria*</label>
                    <input type="text" id="nome" name="nome" required 
                           value="<?php echo htmlspecialchars($categoria['nome']); ?>"
                           placeholder="Ex: Delegado PF, Juiz Federal, etc">
                </div>

                <div class="form-group">
                    <label for="descricao">Descrição</label>
                    <textarea id="descricao" name="descricao" 
                              placeholder="Descreva o objetivo desta categoria de estudos..."><?php echo htmlspecialchars($categoria['descricao']); ?></textarea>
                </div>

                <div class="switch-container">
                    <label class="switch">
                        <input type="checkbox" name="status" <?php echo $categoria['status'] ? 'checked' : ''; ?>>
                        <span class="slider"></span>
                    </label>
                    <span class="switch-label">Categoria Ativa</span>
                </div>

                <div class="actions">
                    <a href="flashcards.php" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-check"></i>
                        Salvar Alterações
                    </button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>