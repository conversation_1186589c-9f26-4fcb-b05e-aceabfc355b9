<?php
//salvar_prova.php

session_start();
if (!isset($_SESSION['idusuario'])) {
    header("Location: /login_index.php");
    exit();
}

include_once 'assets/config.php';

$usuario_id = $_SESSION['idusuario'];

// Validar dados recebidos
$nome_prova = trim($_POST['nome_prova'] ?? '');
$data_prova = $_POST['data_prova'] ?? '';
$data_inicio_estudo = $_POST['data_inicio_estudo'] ?? '';

// Validações básicas
if (empty($nome_prova) || empty($data_prova) || empty($data_inicio_estudo)) {
    $_SESSION['erro'] = "Todos os campos são obrigatórios!";
    header("Location: configurar_prova.php");
    exit();
}

try {
    // Validar datas
    $data_atual = new DateTime();
    $data_prova_obj = new DateTime($data_prova);
    $data_inicio_obj = new DateTime($data_inicio_estudo);

    // Definir horário como meia-noite para todas as datas
    $data_atual->setTime(0, 0, 0);
    $data_prova_obj->setTime(0, 0, 0);
    $data_inicio_obj->setTime(0, 0, 0);

    // Validação da data da prova no passado
    if ($data_prova_obj < $data_atual) {
        $_SESSION['erro'] = "A data da prova não pode ser no passado!";
        header("Location: configurar_prova.php");
        exit();
    }

    // Validação da data de início posterior à prova
    if ($data_inicio_obj >= $data_prova_obj) {
        $_SESSION['erro'] = "A data de início não pode ser IGUAL ou POSTERIOR à data da prova!";
        header("Location: configurar_prova.php");
        exit();
    }

    // Calcular diferença em dias
    $diferenca = $data_prova_obj->diff($data_inicio_obj);
    $dias_diferenca = $diferenca->days;

    if ($dias_diferenca < 15) {
        $_SESSION['erro'] = "O período de estudos não pode ser MENOR que 15 dias! O período selecionado tem " . $dias_diferenca . " dias.";
        header("Location: configurar_prova.php");
        exit();
    }

    // Iniciar transação
    pg_query($conexao, "BEGIN");

    // Inativar provas anteriores
    $query_inativar = "
        UPDATE appestudo.provas 
        SET status = false 
        WHERE usuario_id = $1 
        AND status = true";
    pg_query_params($conexao, $query_inativar, array($usuario_id));

    // Inserir nova prova
    $query_prova = "
        INSERT INTO appestudo.provas 
        (usuario_id, nome, data_prova, data_inicio_estudo, status) 
        VALUES ($1, $2, $3, $4, true)
        RETURNING id";
    $result_prova = pg_query_params($conexao, $query_prova, array(
        $usuario_id,
        $nome_prova,
        $data_prova,
        $data_inicio_estudo
    ));

    if (!$result_prova) {
        throw new Exception("Erro ao salvar a prova!");
    }

    $prova = pg_fetch_assoc($result_prova);
    $prova_id = $prova['id'];

    // Sincronizar com planejamento se solicitado
// Sincronizar com planejamento se solicitado
if (isset($_POST['sincronizar_planejamento']) || isset($_POST['sincronizar_datas'])) {
    $query_planejamento = "
        SELECT idplanejamento 
        FROM appestudo.planejamento 
        WHERE usuario_idusuario = $1 
        ORDER BY data_inicio DESC 
        LIMIT 1";
    
    $result_planejamento = pg_query_params($conexao, $query_planejamento, array($usuario_id));
    
    if ($planejamento = pg_fetch_assoc($result_planejamento)) {
        // Preparar a query de atualização
        $campos_update = array();
        $params = array();
        $param_count = 1;
        
        // Atualizar nome se solicitado
        if (isset($_POST['sincronizar_planejamento'])) {
            $campos_update[] = "nome = $" . $param_count;
            $params[] = $nome_prova;
            $param_count++;
        }
        
        // Atualizar datas se solicitado
        if (isset($_POST['sincronizar_datas'])) {
            $campos_update[] = "data_inicio = $" . $param_count;
            $params[] = $data_inicio_estudo;
            $param_count++;
            
            $campos_update[] = "data_fim = $" . $param_count;
            $params[] = $data_prova;
            $param_count++;
        }
        
        // Se houver campos para atualizar
        if (!empty($campos_update)) {
            $query_update_planejamento = "
                UPDATE appestudo.planejamento 
                SET " . implode(", ", $campos_update) . "
                WHERE idplanejamento = $" . $param_count;
            
            $params[] = $planejamento['idplanejamento'];
            
            $result_update = pg_query_params($conexao, $query_update_planejamento, $params);

            if (!$result_update) {
                throw new Exception("Erro ao atualizar planejamento!");
            }
        }
    }
}

    // Processar pesos e dificuldades das matérias
    $query_materias = "
        SELECT DISTINCT m.idmateria 
        FROM appestudo.conteudo_edital ce
        JOIN appestudo.materia m ON ce.materia_id = m.idmateria
        JOIN appestudo.usuario_edital ue ON ce.edital_id = ue.edital_id
        WHERE ue.usuario_id = $1";

    $result_materias = pg_query_params($conexao, $query_materias, array($usuario_id));

    while ($materia = pg_fetch_assoc($result_materias)) {
        $materia_id = $materia['idmateria'];
        $peso = intval($_POST["peso_$materia_id"] ?? 1);
        $dificuldade = intval($_POST["dificuldade_$materia_id"] ?? 3);

        $query_pesos = "
            INSERT INTO appestudo.pesos_materias 
            (prova_id, materia_id, peso, nivel_dificuldade) 
            VALUES ($1, $2, $3, $4)";
        pg_query_params($conexao, $query_pesos, array(
            $prova_id,
            $materia_id,
            $peso,
            $dificuldade
        ));
    }

    // Commit da transação
    pg_query($conexao, "COMMIT");

    // Redirecionar para configuração de preferências
    $_SESSION['prova_temporaria_id'] = $prova_id;
    $_SESSION['sucesso'] = "Configurações da prova salvas com sucesso! Agora defina seus horários de estudo.";
    header("Location: configurar_preferencias.php");
    exit();

} catch (Exception $e) {
    // Rollback em caso de erro
    pg_query($conexao, "ROLLBACK");

    $_SESSION['erro'] = "Erro ao salvar configurações: " . $e->getMessage();
    header("Location: configurar_prova.php");
    exit();
}
?>