<?php
//processa_index.php

    if (!defined('MEU_SISTEMA_PHP_EXECUCAO_VALIDA')) {
        // Se a constante não estiver definida, o arquivo foi acessado diretamente.
        // Mostra a mensagem SweetAlert e redireciona.

        // É importante garantir que session_start() tenha sido chamado ANTES de tentar usar SweetAlert
        // se o SweetAlert ou o redirecionamento dependesse de alguma lógica de sessão aqui.
        // No entanto, para este caso específico de acesso direto e mensagem genérica,
        // não precisamos de dados de sessão, apenas de sair.
        // Se você já tem session_start() no seu script que *chama* este (o header.php),
        // e este script é chamado *direto*, session_start() pode não ter sido chamado ainda neste contexto.
        // Para o SweetAlert funcionar como está, ele não depende de sessão.

        echo '<!DOCTYPE html>
        <html>
        <head>
            <title>Acesso Negado - Redirecionando...</title>
            <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
            <style>
                body { margin: 0; padding: 0; font-family: "Quicksand", sans-serif; display: flex; align-items: center; justify-content: center; min-height: 100vh; background-color: #f0f2f5; }
                .swal2-popup { font-family: "Quicksand", sans-serif !important; }
            </style>
        </head>
        <body>
            <script>
                document.addEventListener("DOMContentLoaded", function() {
                    Swal.fire({
                        title: "Acesso Direto Negado", // Mensagem um pouco mais específica para este caso
                        text: "Esta página não pode ser acessada diretamente. Você será redirecionado.",
                        icon: "error", // "error" ou "warning" podem ser mais apropriados que "info"
                        confirmButtonText: "OK",
                        confirmButtonColor: "#00008B",
                        allowOutsideClick: false,
                        customClass: {
                            popup: "swal-custom",
                            title: "swal-title",
                            confirmButton: "swal-button"
                        }
                    }).then((result) => {
                        // Redireciona para a página inicial ou de login, independentemente da confirmação do SweetAlert,
                        // pois o acesso é inválido de qualquer maneira.
                        window.location.href = "index.php"; // Ou "login_index.php" se preferir
                    });
                });
            </script>
        </body>
        </html>';
        exit; // Crucial para parar a execução do resto do script.
    }

        date_default_timezone_set('America/Sao_Paulo');
        $id_usuario = $_SESSION['idusuario'];

        // Consulta para buscar o nome do usuário com base no ID
        $query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = $1";
        $resultado_nome = pg_query_params($conexao, $query_buscar_nome, array($id_usuario));

        if ($resultado_nome && pg_num_rows($resultado_nome) > 0) {
            $row_nome = pg_fetch_assoc($resultado_nome);
            $nome_usuario = $row_nome['nome'];
        } else {
            echo "Usuário não encontrado.";
        }

        if($id_usuario==1){
            $mostrarBotao_planejamento = true; // Substitua pelo valor da sua variável
        }else{
            $mostrarBotao_planejamento = false;
        }

        // Consultar os dados do planejamento pelo usuario, incluindo o nome do usuário
        $query_consultar_planejamento = "SELECT p.*, u.nome as nome_usuario 
                             FROM appEstudo.planejamento p 
                             INNER JOIN appEstudo.usuario u ON p.usuario_idusuario = u.idusuario
                             WHERE p.usuario_idusuario = $1"; // SEGURO
        $resultado_planejamento = pg_query_params($conexao, $query_consultar_planejamento, array($id_usuario));

        if (pg_num_rows($resultado_planejamento) > 0) {
            $planejamento = pg_fetch_assoc($resultado_planejamento);
            $id_planejamento = $planejamento['idplanejamento'];
            $nome_planejamento = $planejamento['nome'];
            $planejamento['nome_usuario'];
            $data_inicio_planejamento = $planejamento['data_inicio'];
            $data_fim_planejamento = $planejamento['data_fim'];

            // No início do arquivo, após os includes
            $query = "SELECT CURRENT_DATE as hoje";
            $result = pg_query($conexao, $query);
            $row_hoje_calc = pg_fetch_assoc($result);
            $hoje = new DateTime($row_hoje_calc['hoje']);

            // Convertendo as strings de data para objetos DateTime
            $data_inicio = new DateTime($data_inicio_planejamento);
            $data_fim = new DateTime($data_fim_planejamento);

            // Formatando as datas para exibição
            $data_inicio_planejamento_formatada = date('d-m-Y', strtotime($data_inicio_planejamento));
            $data_fim_planejamento_formatada = date('d-m-Y', strtotime($data_fim_planejamento));
        }else{
            // A tabela está vazia, mostra o alerta estilizado e redireciona para a página desejada
            echo '<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
            <script type="text/javascript">
                document.addEventListener("DOMContentLoaded", function() {
                    Swal.fire({
                        title: "Atenção!",
                        text: "CRIE SEU PLANEJAMENTO!",
                        icon: "info",
                        confirmButtonText: "Vamos lá",
                        confirmButtonColor: "#00008B",
                        background: document.documentElement.getAttribute("data-theme") === "dark" ? "#13132b" : "#fff",
                        color: document.documentElement.getAttribute("data-theme") === "dark" ? "#e4e6f0" : "#2c3e50",
                        customClass: {
                            popup: "rounded-lg shadow-md",
                            title: "text-xl font-bold mb-4",
                            confirmButton: "px-6 py-2 rounded-md transition-all hover:shadow-lg"
                        }
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = "./planejamento/cadastrar_planejamento.php";
                        } else {
                            window.location.href = "./planejamento/cadastrar_planejamento.php";
                        }
                    });
                });
            </script>';
            exit; // Certifique-se de usar "exit" após o redirecionamento para evitar execução adicional de código
        }

        // Consultar os IDs e nomes das matérias associadas ao planejamento
        $query_consultar_materias_planejamento = "SELECT 
        pm.materia_idmateria, 
        m.nome AS nome_materia, 
        m.cor AS cor_materia
        FROM appEstudo.planejamento_materia pm
        INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
        WHERE pm.planejamento_idplanejamento = $1
        ORDER BY pm.ordem ASC";

        $resultado_materias_planejamento = pg_query_params($conexao, $query_consultar_materias_planejamento, array($id_planejamento));
         


        // Construir um array com os IDs e nomes das matérias associadas ao planejamento
        $materias_no_planejamento = array();
        while ($materia_planejamento = pg_fetch_assoc($resultado_materias_planejamento)) {
            $materias_no_planejamento[] = array(
                'id' => $materia_planejamento['materia_idmateria'],
                'nome' => $materia_planejamento['nome_materia'],
                'cor' => $materia_planejamento['cor_materia']
            );
        }


        // Consultar os dados dos estudos pelo ID do usuario
        $query_consultar_estudos = "SELECT e.*, 
        m_estudo.nome AS nome_materia_estudo, 
        m_estudo.cor AS cor_materia_estudo
         FROM appEstudo.estudos e
         INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
         INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
         LEFT JOIN appEstudo.materia m_estudo ON e.materia_idmateria = m_estudo.idmateria
         WHERE u.idusuario = $1
         ORDER BY e.data ASC"; // SEGURO (adicionei ORDER BY que estava no original)
 



        $tempoTotalEstudado = 0;
        $tempoTotalBruto = 0;
        $tempoTotalPerdido = 0;
        $tempoTotalLiquido = 0;
        $horasEstudadasPorData = array();
        $diasEstudados = 0;
        $diasEstudadosHoje = 0;
        $diasNaoEstudados = 0;
        $ponto_estudado = null;
        $materia = null;
        $metodo = null;
        $mediaTempoEstudo = null;
        $todalEventos = null;
        $data_estudo = null;
        $Simulado_data_estudo = null;
        $Simulado_ponto_estudado = null;
        $q_total = null;
        $q_errada = null;
        $q_certa = null;
        $ultimoEstudo=null;

        $tempoEstudadoPorMateria = array();


        $materias = array();

  

        // Use pg_query_params para todas as execuções desta query:
        $consulta_materias = pg_query_params($conexao, $query_consultar_estudos, array($id_usuario));
        $resultado_estudos = pg_query_params($conexao, $query_consultar_estudos, array($id_usuario));
        $consultaprimeiro = pg_query_params($conexao, $query_consultar_estudos, array($id_usuario));

        $registros = pg_num_rows($resultado_estudos);



if ($registros === 0) {
    $primeiraData = "Não Iniciado";
    $Simulado_data_proximo = null;
    $corTextoInicio = "red";
    $horasFormatadasLiquido = null;
    $minutosFormatadosLiquido = null;
    $mediaFormatadahoras = null;
    $mediaFormatadominutos = null;
    $numDias = null;

    //Primeiro dia pegando a data do planejamento
    $primeiraDataObj = DateTime::createFromFormat('Y-m-d', $data_inicio_planejamento);
    $dataAtualObj = new DateTime(); // Cria um objeto DateTime para a data atual

    $diferenca = $primeiraDataObj->diff($dataAtualObj); // Calcula a diferença entre as datas

    $diasNaoEstudados = $diferenca->days; // Obtém o número de dias da diferença


}else{
    $corTextoInicio = "blue";
    $primeiroRegistro = pg_fetch_assoc($consultaprimeiro);

    // Agora você pode acessar os valores do primeiro registro
    $primeiraData = $primeiroRegistro['data'];
    $primeiraData = date('d-m-Y', strtotime($primeiraData));

    // Cria objetos DateTime para a primeira data e a data atual
    $dataAtual = new DateTime();
    $primeiraData_clone = new DateTime($primeiraData);

    // Calcula a diferença entre as datas em dias
    $diferenca = $dataAtual->diff($primeiraData_clone);
    $numDias = $diferenca->days;


    // Percorrer os resultados da consulta e preencher o array $materias
    while ($registro = pg_fetch_assoc($consulta_materias)) {
        $materia = $registro['nome_materia_estudo'];
        $cor = $registro['cor_materia_estudo'];

        // Adicionar a matéria e sua cor ao array $materias
        $materias[$materia] = $cor;
    }

    // Percorrer os resultados da consulta e preencher o array estudos
    while ($exibirRegistros = pg_fetch_array($resultado_estudos)) {
        $idestudo = $exibirRegistros[0];
        $data_estudo = $exibirRegistros[1];
        $tempo_estudo = $exibirRegistros[2];
        $tempo_bruto = $exibirRegistros[3];
        $tempo_perdido = $exibirRegistros[4];
        $ponto_estudado = $exibirRegistros[5];
        $metodo = $exibirRegistros[8];
        $materia = $exibirRegistros[16];
        $idMateriaProxima = $exibirRegistros[14];


        //$tempo_perdido_parts = explode(':', $tempo_perdido);
        //$tempoTotalPerdido += ($tempo_perdido_parts[0] * 3600) + ($tempo_perdido_parts[1] * 60) + $tempo_perdido_parts[2];

        //Calcular Tempo Liquido de forma Fracionada
        $tempo_perdido_parts = explode(':', $tempo_perdido);
        $horas_perdido = $tempo_perdido_parts[0];
        $minutos_perdido = $tempo_perdido_parts[1];
        $segundos_perdido = $tempo_perdido_parts[2];
        $tempoTotalPerdido += ($horas_perdido * 3600) + ($minutos_perdido * 60) + $segundos_perdido;
        $horasFormatadasPerdido = floor($tempoTotalPerdido / 3600);
        $minutosFormatadosperdido = floor(($tempoTotalPerdido % 3600) / 60);
        $segundosFormatadosperdido = $tempoTotalPerdido % 60;

        //Calcular Tempo Bruto de forma Fracionada
        $tempo_bruto_parts = explode(':', $tempo_bruto);
        $horas_bruto = $tempo_bruto_parts[0];
        $minutos_bruto = $tempo_bruto_parts[1];
        $segundos_bruto = $tempo_bruto_parts[2];
        $tempoTotalBruto += ($horas_bruto * 3600) + ($minutos_bruto * 60) + $segundos_bruto;
        $horasFormatadasBruto = floor($tempoTotalBruto / 3600);
        $minutosFormatadosBruto = floor(($tempoTotalBruto % 3600) / 60);
        $segundosFormatadosBruto = $tempoTotalBruto % 60;

        //Média do tempo de estudo fora o Simulado
        if ($materia != "Simulado") {

            $todalEventos++;

            //Calcular Tempo Liquido de forma Fracionada
            $tempo_liquido_parts = explode(':', $tempo_estudo);
            $horas_liquido = $tempo_liquido_parts[0];
            $minutos_liquido = $tempo_liquido_parts[1];
            $segundos_liquido = $tempo_liquido_parts[2];
            $tempoTotalLiquido += ($horas_liquido * 3600) + ($minutos_liquido * 60) + $segundos_liquido;
            $horasFormatadasLiquido = floor($tempoTotalLiquido / 3600);
            $minutosFormatadosLiquido = floor(($tempoTotalLiquido % 3600) / 60);
            $segundosFormatadosLiquido = $tempoTotalLiquido % 60;
        }


        if (array_key_exists($data_estudo, $horasEstudadasPorData)) {
            $horasEstudadasPorData[$data_estudo] += ($tempo_liquido_parts[0] * 3600) + ($tempo_liquido_parts[1] * 60) + $tempo_liquido_parts[2];
        } else {
            $horasEstudadasPorData[$data_estudo] = 0;
            $horasEstudadasPorData[$data_estudo] += ($tempo_liquido_parts[0] * 3600) + ($tempo_liquido_parts[1] * 60) + $tempo_liquido_parts[2];
        }

        //verifica se é simulado e se for acrescenta 30 dias para fazer um novo
        if ($metodo == "Simulado") {
            $Simulado_data_estudo = $data_estudo;
            $Simulado_data_proximo = DateTime::createFromFormat('Y-m-d', $data_estudo);
            $Simulado_data_proximo->modify('+30 days');
            $Simulado_data_proximo = $Simulado_data_proximo->format('d-m-Y');
            $Simulado_ponto_estudado = $ponto_estudado;
            $q_total = $exibirRegistros[9];
            $q_errada = $exibirRegistros[10];
            $q_certa = $exibirRegistros[11];
        }else{
            $dataAtual = new DateTime();
            $Simulado_data_proximo = $dataAtual->format('d-m-Y'); // Formata a data atual no formato desejado


        }

        if (array_key_exists($materia, $tempoEstudadoPorMateria)) {
            // Se a matéria já existe, adiciona o tempo estudado
            $tempoEstudadoPorMateria[$materia] += ($tempo_liquido_parts[0] * 3600) + ($tempo_liquido_parts[1] * 60) + $tempo_liquido_parts[2];
        } else {
            // Se a matéria não existe, cria uma entrada no array
            $tempoEstudadoPorMateria[$materia] = ($tempo_liquido_parts[0] * 3600) + ($tempo_liquido_parts[1] * 60) + $tempo_liquido_parts[2];
        }

        $ultimoEstudo = $data_estudo;

    }

    //Calucar quantos dias se passaram desde o ultimo dia de estudo
    $ultimoEstudo = DateTime::createFromFormat('Y-m-d', $ultimoEstudo);
    $ultimoEstudo = $ultimoEstudo->diff($dataAtual);
    $ultimoEstudo = $ultimoEstudo->days;

    $tempoTotalEstudadoFormatado = gmdate('H:i:s', $tempoTotalEstudado);
    //$tempoTotalBrutoFormatado = gmdate('H:i:s', $tempoTotalBruto);
    $tempoTotalPerdidoFormatado = gmdate('H:i:s', $tempoTotalPerdido);

    $datasRegistradas = array_keys($horasEstudadasPorData);
    $primeiraDataRegistro = $primeiroRegistro['data'];

    $anoAtual = date('Y');
    $mesAtual = date('m');
    $diaAtual = date('d') - 1;
    $numeroDiasMesAtual = cal_days_in_month(CAL_GREGORIAN, $mesAtual, $anoAtual);
    $primeiroDiaSemana = date('N', strtotime($anoAtual . '-' . $mesAtual . '-01'));
    $nomeMesAtual = date('F', strtotime($anoAtual . '-' . $mesAtual . '-01'));

    // Criando objeto DateTime para a primeira data de estudo (não do planejamento)
    $primeiraDataObj = DateTime::createFromFormat('Y-m-d', $primeiraDataRegistro);
    $dataAtualFormatada = date('Y-m-d'); // Data atual formatada

    // Contagem de dias estudados hoje e total
    foreach ($datasRegistradas as $dataRegistrada) {
        if ($dataRegistrada === $dataAtualFormatada) {
            $diasEstudadosHoje++;
        }
        $diasEstudados++;
    }

    // Criar array com todas as datas desde o primeiro estudo até hoje
    $todasDatas = array();
    $dataTemp = clone $primeiraDataObj;
    $dataAtual = new DateTime();

    while ($dataTemp <= $dataAtual) {
        $todasDatas[] = $dataTemp->format('Y-m-d');
        $dataTemp->modify('+1 day');
    }

    // Calcular dias não estudados (diferença entre todas as datas e datas estudadas)
    $diasNaoEstudados = count(array_diff($todasDatas, $datasRegistradas));

    $mediaTempoEstudo = $tempoTotalLiquido / $diasEstudados;
    $totalSegundos = round($mediaTempoEstudo);

    $mediaFormatadahoras = floor($totalSegundos / 3600); // Calcula as horas completas
    $mediaFormatadominutos = floor(($totalSegundos % 3600) / 60); // Calcula os minutos restantes
    $mediaFormatadosegundos = $totalSegundos % 60; // Calcula os segundos restantes

    $mediaFormatada = gmdate("H:i:s", $totalSegundos);

}

    // Consulta SQL para recuperar os eventos agendados do banco de dados
    $query_consultar_agenda = "SELECT * FROM appEstudo.agenda WHERE usuario_idusuario = $1"; // SEGURO
    $resultado_agenda = pg_query_params($conexao, $query_consultar_agenda, array($id_usuario));

    // Inicialização das variáveis
    $evento_proximo = null;
    $eventosProximos = null;
    $eventos_do_Dia = null;
    $eventosPendentes = null;
    $diasAteProximoEvento = null;

    while ($registro = pg_fetch_assoc($resultado_agenda)) {
        $evento_atual_titulo = $registro['titulo'];
        $evento_atual_data = strtotime($registro['data_inicio']);
        $evento_atual_data_sem_horas = strtotime(date('Y-m-d', $evento_atual_data));

        if ($evento_atual_data_sem_horas > time() && ($evento_proximo === null || $evento_proximo > $evento_atual_data_sem_horas)) {
            // Verifica se o evento não foi realizado antes de definir como próximo
            if ($registro['realizado'] == "f") {
                $evento_proximo = $evento_atual_data_sem_horas;
            }
        }

        if ($evento_atual_data_sem_horas == strtotime(date('Y-m-d')) && ($registro['realizado'] == "f")) {
            $eventos_do_Dia[] = array(
                'id' => $registro['id'],
                'titulo' => $evento_atual_titulo
            );
        }

        if ($evento_atual_data_sem_horas < strtotime(date('Y-m-d')) && ($registro['realizado'] == "f")) {
            $eventosPendentes[] = array(
                'id' => $registro['id'],
                'titulo' => $evento_atual_titulo
            );
        }
    }

    if ($evento_proximo !== null) {
        // Buscar os eventos na data do evento mais próximo encontrado     
        $data_proximo_formatada = date('Y-m-d', $evento_proximo);
        $query_eventos_proximos = "SELECT * FROM appEstudo.agenda WHERE usuario_idusuario = $1 AND data_inicio::date = $2"; // SEGURO
        $resultado_eventos_proximos = pg_query_params($conexao, $query_eventos_proximos, array($id_usuario, $data_proximo_formatada));

        while ($registro = pg_fetch_assoc($resultado_eventos_proximos)) {
            // Adiciona apenas eventos não realizados à lista de próximos eventos
            if ($registro['realizado'] == "f") {
                $eventosProximos[] = array(
                    'id' => $registro['id'],
                    'titulo' => $registro['titulo']
                );
            }
        }

        $diasAteProximoEvento = floor(($evento_proximo - strtotime(date('Y-m-d'))) / (60 * 60 * 24));

    }
?>
