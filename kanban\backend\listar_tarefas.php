<?php
// Primeiro limpa qualquer saída anterior
ob_clean();

// Define o cabeçalho como JSON
header('Content-Type: application/json');

// Inclui o arquivo de conexão
include_once("../../conexao_POST.php");

try {
    // Busca as tarefas
    $query = "SELECT id, titulo, descricao, status, prioridade, 
                     data_limite::date::text as data_limite,
                     data_criacao, data_conclusao, usuario_id
              FROM appestudo.kanban_tarefas 
              ORDER BY 
                data_limite ASC NULLS LAST,
                CASE prioridade
                    WHEN 'alta' THEN 1
                    WHEN 'media' THEN 2
                    WHEN 'baixa' THEN 3
                    ELSE 4
                END ASC,
                data_criacao DESC";
    $result = pg_query($conexao, $query);
    
    if (!$result) {
        throw new Exception("Erro na consulta: " . pg_last_error($conexao));
    }
    
    $tarefas = array();
    while ($row = pg_fetch_assoc($result)) {
        $tarefas[] = array(
            'id' => $row['id'],
            'titulo' => $row['titulo'],
            'descricao' => $row['descricao'],
            'prioridade' => $row['prioridade'],
            'status' => $row['status'],
            'data_limite' => $row['data_limite']
        );
    }
    
    echo json_encode([
        'success' => true,
        'dados' => $tarefas
    ]);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>


