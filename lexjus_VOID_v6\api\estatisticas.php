<?php
/**
 * API de Estatísticas Avançadas por Lei
 * Fornece dados detalhados e comparativos entre leis
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Tratar requisições OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Verificar se o usuário está logado
session_start();
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit();
}

// Incluir conexão existente
require_once '../../conexao_POST.php';

// Tentar incluir configuração PDO, mas usar conexão existente como fallback
try {
    require_once '../config/database.php';
} catch (Exception $e) {
    // Usar conexão pg_connect existente
    $pdo = null;
}

$usuario_id = $_SESSION['idusuario'];
$metodo = $_SERVER['REQUEST_METHOD'];
$acao = $_GET['acao'] ?? '';

try {
    switch ($metodo) {
        case 'GET':
            switch ($acao) {
                case 'dashboard':
                    obterDashboardCompleto($pdo, $usuario_id);
                    break;
                case 'comparativo':
                    obterComparativoLeis($pdo, $usuario_id);
                    break;
                case 'detalhada':
                    obterEstatisticaDetalhada($pdo, $usuario_id);
                    break;
                case 'historico':
                    obterHistoricoProgresso($pdo, $usuario_id);
                    break;
                case 'ranking':
                    obterRankingLeis($pdo, $usuario_id);
                    break;
                case 'tempo_estudo':
                    obterTempoEstudo($pdo, $usuario_id);
                    break;
                case 'metas':
                    obterMetas($pdo, $usuario_id);
                    break;
                default:
                    obterDashboardCompleto($pdo, $usuario_id);
                    break;
            }
            break;
            
        case 'POST':
            switch ($acao) {
                case 'definir_meta':
                    definirMeta($pdo, $usuario_id);
                    break;
                default:
                    http_response_code(400);
                    echo json_encode(['erro' => 'Ação não reconhecida']);
                    break;
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['erro' => 'Método não permitido']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['erro' => 'Erro interno do servidor: ' . $e->getMessage()]);
}

/**
 * Obtém dashboard completo com todas as estatísticas
 */
function obterDashboardCompleto($pdo, $usuario_id) {
    $sql = "
        WITH lei_stats AS (
            SELECT 
                l.id,
                l.codigo,
                l.nome,
                l.nome_completo,
                l.cor_tema,
                l.icone,
                l.total_artigos,
                
                -- Estatísticas de progresso
                COALESCE(p.artigos_lidos, 0) as artigos_lidos,
                CASE 
                    WHEN l.total_artigos > 0 THEN 
                        ROUND((COALESCE(p.artigos_lidos, 0) * 100.0 / l.total_artigos), 1)
                    ELSE 0 
                END as percentual_progresso,
                
                -- Estatísticas de revisão
                COALESCE(r.total_revisoes, 0) as total_revisoes,
                COALESCE(r.pendentes, 0) as pendentes,
                COALESCE(r.aprendendo, 0) as aprendendo,
                COALESCE(r.revisando, 0) as revisando,
                COALESCE(r.dominados, 0) as dominados,
                COALESCE(r.dificeis, 0) as dificeis,
                COALESCE(r.facilidade_media, 0) as facilidade_media,
                
                -- Outras estatísticas
                COALESCE(f.favoritos, 0) as favoritos,
                COALESCE(lt.listas, 0) as listas,
                COALESCE(a.anotacoes, 0) as anotacoes,
                
                -- Tempo de estudo (estimado)
                COALESCE(h.tempo_total_estudo, 0) as tempo_total_estudo,
                COALESCE(h.sessoes_estudo, 0) as sessoes_estudo
                
            FROM appestudo.lexjus_leis l
            LEFT JOIN (
                SELECT 
                    lei_id,
                    COUNT(*) as artigos_lidos
                FROM appestudo.lexjus_progresso 
                WHERE usuario_id = :usuario_id AND lido = true
                GROUP BY lei_id
            ) p ON l.id = p.lei_id
            LEFT JOIN (
                SELECT 
                    lei_id,
                    COUNT(*) as total_revisoes,
                    COUNT(CASE WHEN data_proxima_revisao <= CURRENT_TIMESTAMP THEN 1 END) as pendentes,
                    COUNT(CASE WHEN status = 'aprendendo' THEN 1 END) as aprendendo,
                    COUNT(CASE WHEN status = 'revisando' THEN 1 END) as revisando,
                    COUNT(CASE WHEN status = 'dominado' THEN 1 END) as dominados,
                    COUNT(CASE WHEN status = 'dificil' THEN 1 END) as dificeis,
                    ROUND(AVG(facilidade), 2) as facilidade_media
                FROM appestudo.lexjus_revisoes 
                WHERE usuario_id = :usuario_id
                GROUP BY lei_id
            ) r ON l.id = r.lei_id
            LEFT JOIN (
                SELECT 
                    lei_id,
                    COUNT(*) as favoritos
                FROM appestudo.lexjus_favoritos 
                WHERE usuario_id = :usuario_id
                GROUP BY lei_id
            ) f ON l.id = f.lei_id
            LEFT JOIN (
                SELECT 
                    lei_id,
                    COUNT(*) as listas
                FROM appestudo.lexjus_listas 
                WHERE usuario_id = :usuario_id
                GROUP BY lei_id
            ) lt ON l.id = lt.lei_id
            LEFT JOIN (
                SELECT 
                    lei_id,
                    COUNT(*) as anotacoes
                FROM appestudo.lexjus_anotacoes 
                WHERE usuario_id = :usuario_id
                GROUP BY lei_id
            ) a ON l.id = a.lei_id
            LEFT JOIN (
                SELECT 
                    r.lei_id,
                    SUM(h.tempo_resposta) as tempo_total_estudo,
                    COUNT(DISTINCT DATE(h.data_revisao)) as sessoes_estudo
                FROM appestudo.lexjus_historico_revisoes h
                JOIN appestudo.lexjus_revisoes r ON h.revisao_id = r.id
                WHERE h.usuario_id = :usuario_id
                GROUP BY r.lei_id
            ) h ON l.id = h.lei_id
            WHERE l.ativa = true
            ORDER BY l.ordem_exibicao
        ),
        totais AS (
            SELECT 
                SUM(artigos_lidos) as total_artigos_lidos,
                SUM(total_artigos) as total_artigos_disponiveis,
                SUM(total_revisoes) as total_revisoes_sistema,
                SUM(pendentes) as total_pendentes,
                SUM(favoritos) as total_favoritos,
                SUM(listas) as total_listas,
                SUM(anotacoes) as total_anotacoes,
                SUM(tempo_total_estudo) as tempo_total_global,
                AVG(facilidade_media) as facilidade_media_global
            FROM lei_stats
        )
        SELECT 
            json_build_object(
                'leis', json_agg(
                    json_build_object(
                        'codigo', ls.codigo,
                        'nome', ls.nome,
                        'nome_completo', ls.nome_completo,
                        'cor_tema', ls.cor_tema,
                        'icone', ls.icone,
                        'total_artigos', ls.total_artigos,
                        'artigos_lidos', ls.artigos_lidos,
                        'percentual_progresso', ls.percentual_progresso,
                        'revisao', json_build_object(
                            'total', ls.total_revisoes,
                            'pendentes', ls.pendentes,
                            'aprendendo', ls.aprendendo,
                            'revisando', ls.revisando,
                            'dominados', ls.dominados,
                            'dificeis', ls.dificeis,
                            'facilidade_media', ls.facilidade_media
                        ),
                        'favoritos', ls.favoritos,
                        'listas', ls.listas,
                        'anotacoes', ls.anotacoes,
                        'tempo_estudo', json_build_object(
                            'total_segundos', ls.tempo_total_estudo,
                            'sessoes', ls.sessoes_estudo,
                            'media_por_sessao', CASE 
                                WHEN ls.sessoes_estudo > 0 THEN 
                                    ROUND(ls.tempo_total_estudo / ls.sessoes_estudo, 0)
                                ELSE 0 
                            END
                        )
                    ) ORDER BY ls.ordem_exibicao
                ),
                'resumo_geral', json_build_object(
                    'total_artigos_lidos', t.total_artigos_lidos,
                    'total_artigos_disponiveis', t.total_artigos_disponiveis,
                    'percentual_geral', CASE 
                        WHEN t.total_artigos_disponiveis > 0 THEN 
                            ROUND((t.total_artigos_lidos * 100.0 / t.total_artigos_disponiveis), 1)
                        ELSE 0 
                    END,
                    'total_revisoes', t.total_revisoes_sistema,
                    'total_pendentes', t.total_pendentes,
                    'total_favoritos', t.total_favoritos,
                    'total_listas', t.total_listas,
                    'total_anotacoes', t.total_anotacoes,
                    'tempo_total_estudo', t.tempo_total_global,
                    'facilidade_media_global', ROUND(t.facilidade_media_global, 2)
                )
            ) as dashboard
        FROM lei_stats ls, totais t
        GROUP BY t.total_artigos_lidos, t.total_artigos_disponiveis, t.total_revisoes_sistema, 
                 t.total_pendentes, t.total_favoritos, t.total_listas, t.total_anotacoes, 
                 t.tempo_total_global, t.facilidade_media_global
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':usuario_id', $usuario_id, PDO::PARAM_INT);
    $stmt->execute();
    
    $resultado = $stmt->fetch(PDO::FETCH_ASSOC);
    $dashboard = json_decode($resultado['dashboard'], true);
    
    echo json_encode([
        'sucesso' => true,
        'dashboard' => $dashboard
    ]);
}

/**
 * Obtém comparativo entre leis
 */
function obterComparativoLeis($pdo, $usuario_id) {
    $sql = "
        SELECT 
            l.codigo,
            l.nome,
            l.cor_tema,
            l.total_artigos,
            COALESCE(p.artigos_lidos, 0) as artigos_lidos,
            CASE 
                WHEN l.total_artigos > 0 THEN 
                    ROUND((COALESCE(p.artigos_lidos, 0) * 100.0 / l.total_artigos), 1)
                ELSE 0 
            END as percentual_progresso,
            COALESCE(r.total_revisoes, 0) as total_revisoes,
            COALESCE(r.dominados, 0) as dominados,
            COALESCE(r.facilidade_media, 0) as facilidade_media,
            COALESCE(f.favoritos, 0) as favoritos
        FROM appestudo.lexjus_leis l
        LEFT JOIN (
            SELECT lei_id, COUNT(*) as artigos_lidos
            FROM appestudo.lexjus_progresso 
            WHERE usuario_id = :usuario_id AND lido = true
            GROUP BY lei_id
        ) p ON l.id = p.lei_id
        LEFT JOIN (
            SELECT 
                lei_id,
                COUNT(*) as total_revisoes,
                COUNT(CASE WHEN status = 'dominado' THEN 1 END) as dominados,
                ROUND(AVG(facilidade), 2) as facilidade_media
            FROM appestudo.lexjus_revisoes 
            WHERE usuario_id = :usuario_id
            GROUP BY lei_id
        ) r ON l.id = r.lei_id
        LEFT JOIN (
            SELECT lei_id, COUNT(*) as favoritos
            FROM appestudo.lexjus_favoritos 
            WHERE usuario_id = :usuario_id
            GROUP BY lei_id
        ) f ON l.id = f.lei_id
        WHERE l.ativa = true
        ORDER BY percentual_progresso DESC, l.ordem_exibicao
    ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':usuario_id', $usuario_id, PDO::PARAM_INT);
    $stmt->execute();
    
    $leis = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'sucesso' => true,
        'comparativo' => $leis
    ]);
}

/**
 * Obtém estatística detalhada de uma lei específica
 */
function obterEstatisticaDetalhada($pdo, $usuario_id) {
    $lei_codigo = $_GET['codigo'] ?? '';

    if (empty($lei_codigo)) {
        http_response_code(400);
        echo json_encode(['erro' => 'Código da lei é obrigatório']);
        return;
    }

    $sql = "
        WITH lei_info AS (
            SELECT * FROM appestudo.lexjus_leis WHERE codigo = :lei_codigo AND ativa = true
        ),
        estatisticas_detalhadas AS (
            SELECT
                -- Progresso por período
                COUNT(CASE WHEN p.data_leitura >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as lidos_ultima_semana,
                COUNT(CASE WHEN p.data_leitura >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as lidos_ultimo_mes,
                COUNT(CASE WHEN p.lido = true THEN 1 END) as total_lidos,

                -- Revisões por status
                COUNT(CASE WHEN r.status = 'novo' THEN 1 END) as novos,
                COUNT(CASE WHEN r.status = 'aprendendo' THEN 1 END) as aprendendo,
                COUNT(CASE WHEN r.status = 'revisando' THEN 1 END) as revisando,
                COUNT(CASE WHEN r.status = 'dominado' THEN 1 END) as dominados,
                COUNT(CASE WHEN r.status = 'dificil' THEN 1 END) as dificeis,

                -- Métricas de performance
                ROUND(AVG(r.facilidade), 2) as facilidade_media,
                ROUND(AVG(r.tempo_medio_resposta), 0) as tempo_medio_resposta,
                MAX(r.repeticoes) as max_repeticoes,

                -- Atividade recente
                COUNT(CASE WHEN h.data_revisao >= CURRENT_DATE - INTERVAL '7 days' THEN 1 END) as revisoes_ultima_semana,
                COUNT(CASE WHEN h.data_revisao >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) as revisoes_ultimo_mes,

                -- Qualidade das respostas
                ROUND(AVG(CASE WHEN h.qualidade_resposta >= 4 THEN 1.0 ELSE 0.0 END) * 100, 1) as percentual_respostas_boas,
                ROUND(AVG(h.qualidade_resposta), 1) as qualidade_media

            FROM lei_info l
            LEFT JOIN appestudo.lexjus_progresso p ON p.lei_id = l.id AND p.usuario_id = :usuario_id
            LEFT JOIN appestudo.lexjus_revisoes r ON r.lei_id = l.id AND r.usuario_id = :usuario_id
            LEFT JOIN appestudo.lexjus_historico_revisoes h ON h.lei_id = l.id AND h.usuario_id = :usuario_id
        ),
        tendencias AS (
            SELECT
                DATE_TRUNC('week', h.data_revisao) as semana,
                COUNT(*) as revisoes_semana,
                ROUND(AVG(h.qualidade_resposta), 1) as qualidade_semana
            FROM lei_info l
            JOIN appestudo.lexjus_historico_revisoes h ON h.lei_id = l.id
            WHERE h.usuario_id = :usuario_id
            AND h.data_revisao >= CURRENT_DATE - INTERVAL '12 weeks'
            GROUP BY DATE_TRUNC('week', h.data_revisao)
            ORDER BY semana DESC
            LIMIT 12
        )
        SELECT
            json_build_object(
                'lei', row_to_json(l.*),
                'estatisticas', row_to_json(e.*),
                'tendencias', COALESCE(
                    (SELECT json_agg(row_to_json(t.*)) FROM tendencias t),
                    '[]'::json
                ),
                'distribuicao_status', json_build_object(
                    'novo', e.novos,
                    'aprendendo', e.aprendendo,
                    'revisando', e.revisando,
                    'dominado', e.dominados,
                    'dificil', e.dificeis
                )
            ) as detalhes
        FROM lei_info l, estatisticas_detalhadas e
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':lei_codigo', $lei_codigo);
    $stmt->bindParam(':usuario_id', $usuario_id, PDO::PARAM_INT);
    $stmt->execute();

    $resultado = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$resultado) {
        http_response_code(404);
        echo json_encode(['erro' => 'Lei não encontrada']);
        return;
    }

    $detalhes = json_decode($resultado['detalhes'], true);

    echo json_encode([
        'sucesso' => true,
        'detalhes' => $detalhes
    ]);
}

/**
 * Obtém histórico de progresso ao longo do tempo
 */
function obterHistoricoProgresso($pdo, $usuario_id) {
    $periodo = $_GET['periodo'] ?? '30'; // dias
    $lei_codigo = $_GET['codigo'] ?? null;

    $where_lei = '';
    $params = [':usuario_id' => $usuario_id, ':periodo' => (int)$periodo];

    if ($lei_codigo) {
        $where_lei = 'AND l.codigo = :lei_codigo';
        $params[':lei_codigo'] = $lei_codigo;
    }

    $sql = "
        WITH historico_diario AS (
            SELECT
                DATE(COALESCE(p.data_leitura, h.data_revisao)) as data,
                l.codigo as lei_codigo,
                l.nome as lei_nome,
                l.cor_tema,
                COUNT(DISTINCT p.artigo_numero) as artigos_lidos_dia,
                COUNT(DISTINCT h.artigo_numero) as artigos_revisados_dia,
                ROUND(AVG(h.qualidade_resposta), 1) as qualidade_media_dia
            FROM appestudo.lexjus_leis l
            LEFT JOIN appestudo.lexjus_progresso p ON p.lei_id = l.id
                AND p.usuario_id = :usuario_id
                AND p.data_leitura >= CURRENT_DATE - INTERVAL ':periodo days'
            LEFT JOIN appestudo.lexjus_historico_revisoes h ON h.lei_id = l.id
                AND h.usuario_id = :usuario_id
                AND h.data_revisao >= CURRENT_DATE - INTERVAL ':periodo days'
            WHERE l.ativa = true $where_lei
            AND (p.data_leitura IS NOT NULL OR h.data_revisao IS NOT NULL)
            GROUP BY DATE(COALESCE(p.data_leitura, h.data_revisao)), l.codigo, l.nome, l.cor_tema
            ORDER BY data DESC
        )
        SELECT json_agg(row_to_json(h.*)) as historico
        FROM historico_diario h
    ";

    $stmt = $pdo->prepare($sql);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();

    $resultado = $stmt->fetch(PDO::FETCH_ASSOC);
    $historico = json_decode($resultado['historico'] ?? '[]', true);

    echo json_encode([
        'sucesso' => true,
        'historico' => $historico,
        'periodo' => $periodo
    ]);
}

/**
 * Obtém ranking de leis por diferentes métricas
 */
function obterRankingLeis($pdo, $usuario_id) {
    $sql = "
        SELECT
            l.codigo,
            l.nome,
            l.cor_tema,
            l.icone,

            -- Ranking por progresso
            CASE
                WHEN l.total_artigos > 0 THEN
                    ROUND((COALESCE(p.artigos_lidos, 0) * 100.0 / l.total_artigos), 1)
                ELSE 0
            END as percentual_progresso,

            -- Ranking por facilidade
            COALESCE(r.facilidade_media, 0) as facilidade_media,

            -- Ranking por dominância
            CASE
                WHEN COALESCE(r.total_revisoes, 0) > 0 THEN
                    ROUND((COALESCE(r.dominados, 0) * 100.0 / r.total_revisoes), 1)
                ELSE 0
            END as percentual_dominados,

            -- Ranking por atividade
            COALESCE(h.atividade_recente, 0) as atividade_recente,

            -- Dados brutos para cálculos
            COALESCE(p.artigos_lidos, 0) as artigos_lidos,
            l.total_artigos,
            COALESCE(r.total_revisoes, 0) as total_revisoes,
            COALESCE(r.dominados, 0) as dominados

        FROM appestudo.lexjus_leis l
        LEFT JOIN (
            SELECT lei_id, COUNT(*) as artigos_lidos
            FROM appestudo.lexjus_progresso
            WHERE usuario_id = :usuario_id AND lido = true
            GROUP BY lei_id
        ) p ON l.id = p.lei_id
        LEFT JOIN (
            SELECT
                lei_id,
                COUNT(*) as total_revisoes,
                COUNT(CASE WHEN status = 'dominado' THEN 1 END) as dominados,
                ROUND(AVG(facilidade), 2) as facilidade_media
            FROM appestudo.lexjus_revisoes
            WHERE usuario_id = :usuario_id
            GROUP BY lei_id
        ) r ON l.id = r.lei_id
        LEFT JOIN (
            SELECT
                lei_id,
                COUNT(*) as atividade_recente
            FROM appestudo.lexjus_historico_revisoes
            WHERE usuario_id = :usuario_id
            AND data_revisao >= CURRENT_DATE - INTERVAL '7 days'
            GROUP BY lei_id
        ) h ON l.id = h.lei_id
        WHERE l.ativa = true
        ORDER BY l.ordem_exibicao
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':usuario_id', $usuario_id, PDO::PARAM_INT);
    $stmt->execute();

    $leis = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Criar rankings
    $rankings = [
        'por_progresso' => $leis,
        'por_facilidade' => $leis,
        'por_dominancia' => $leis,
        'por_atividade' => $leis
    ];

    // Ordenar cada ranking
    usort($rankings['por_progresso'], function($a, $b) {
        return $b['percentual_progresso'] <=> $a['percentual_progresso'];
    });

    usort($rankings['por_facilidade'], function($a, $b) {
        return $b['facilidade_media'] <=> $a['facilidade_media'];
    });

    usort($rankings['por_dominancia'], function($a, $b) {
        return $b['percentual_dominados'] <=> $a['percentual_dominados'];
    });

    usort($rankings['por_atividade'], function($a, $b) {
        return $b['atividade_recente'] <=> $a['atividade_recente'];
    });

    echo json_encode([
        'sucesso' => true,
        'rankings' => $rankings
    ]);
}

/**
 * Obtém estatísticas de tempo de estudo
 */
function obterTempoEstudo($pdo, $usuario_id) {
    $sql = "
        WITH tempo_por_lei AS (
            SELECT
                l.codigo,
                l.nome,
                l.cor_tema,
                SUM(h.tempo_resposta) as tempo_total_segundos,
                COUNT(h.id) as total_sessoes,
                COUNT(DISTINCT DATE(h.data_revisao)) as dias_estudados,
                ROUND(AVG(h.tempo_resposta), 0) as tempo_medio_por_revisao
            FROM appestudo.lexjus_leis l
            LEFT JOIN appestudo.lexjus_historico_revisoes h ON h.lei_id = l.id
            WHERE l.ativa = true AND h.usuario_id = :usuario_id
            GROUP BY l.codigo, l.nome, l.cor_tema
        ),
        tempo_por_periodo AS (
            SELECT
                DATE_TRUNC('day', h.data_revisao) as dia,
                SUM(h.tempo_resposta) as tempo_dia
            FROM appestudo.lexjus_historico_revisoes h
            WHERE h.usuario_id = :usuario_id
            AND h.data_revisao >= CURRENT_DATE - INTERVAL '30 days'
            GROUP BY DATE_TRUNC('day', h.data_revisao)
            ORDER BY dia
        )
        SELECT
            json_build_object(
                'por_lei', COALESCE(
                    (SELECT json_agg(
                        json_build_object(
                            'codigo', t.codigo,
                            'nome', t.nome,
                            'cor_tema', t.cor_tema,
                            'tempo_total_segundos', COALESCE(t.tempo_total_segundos, 0),
                            'tempo_total_formatado',
                                CASE
                                    WHEN t.tempo_total_segundos >= 3600 THEN
                                        FLOOR(t.tempo_total_segundos / 3600) || 'h ' ||
                                        FLOOR((t.tempo_total_segundos % 3600) / 60) || 'm'
                                    WHEN t.tempo_total_segundos >= 60 THEN
                                        FLOOR(t.tempo_total_segundos / 60) || 'm ' ||
                                        (t.tempo_total_segundos % 60) || 's'
                                    ELSE
                                        COALESCE(t.tempo_total_segundos, 0) || 's'
                                END,
                            'total_sessoes', COALESCE(t.total_sessoes, 0),
                            'dias_estudados', COALESCE(t.dias_estudados, 0),
                            'tempo_medio_por_revisao', COALESCE(t.tempo_medio_por_revisao, 0)
                        )
                    ) FROM tempo_por_lei t),
                    '[]'::json
                ),
                'historico_diario', COALESCE(
                    (SELECT json_agg(
                        json_build_object(
                            'dia', tp.dia,
                            'tempo_segundos', tp.tempo_dia,
                            'tempo_formatado',
                                CASE
                                    WHEN tp.tempo_dia >= 3600 THEN
                                        FLOOR(tp.tempo_dia / 3600) || 'h ' ||
                                        FLOOR((tp.tempo_dia % 3600) / 60) || 'm'
                                    WHEN tp.tempo_dia >= 60 THEN
                                        FLOOR(tp.tempo_dia / 60) || 'm'
                                    ELSE
                                        tp.tempo_dia || 's'
                                END
                        )
                    ) FROM tempo_por_periodo tp),
                    '[]'::json
                )
            ) as tempo_estudo
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':usuario_id', $usuario_id, PDO::PARAM_INT);
    $stmt->execute();

    $resultado = $stmt->fetch(PDO::FETCH_ASSOC);
    $tempo_estudo = json_decode($resultado['tempo_estudo'], true);

    echo json_encode([
        'sucesso' => true,
        'tempo_estudo' => $tempo_estudo
    ]);
}

/**
 * Obtém metas do usuário
 */
function obterMetas($pdo, $usuario_id) {
    // Por enquanto, retornar metas padrão
    // Futuramente pode ser expandido para metas personalizadas
    $metas_padrao = [
        'artigos_por_dia' => 5,
        'revisoes_por_dia' => 10,
        'tempo_estudo_diario' => 1800, // 30 minutos
        'percentual_acerto' => 80,
        'leis_para_dominar' => 2
    ];

    echo json_encode([
        'sucesso' => true,
        'metas' => $metas_padrao
    ]);
}

/**
 * Define meta personalizada do usuário
 */
function definirMeta($pdo, $usuario_id) {
    $input = json_decode(file_get_contents('php://input'), true);

    // Por enquanto, apenas confirmar recebimento
    // Futuramente implementar persistência de metas

    echo json_encode([
        'sucesso' => true,
        'mensagem' => 'Meta definida com sucesso',
        'meta' => $input
    ]);
}
?>
