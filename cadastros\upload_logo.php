<?php
declare(strict_types=1);
session_start();
require_once("assets/config.php");
require_once("includes/verify_admin.php");

// Headers de segurança
header('Content-Type: application/json');
header('X-Frame-Options: DENY');
header('X-Content-Type-Options: nosniff');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

try {
    // Substituir verificação manual pela função adequada
    verificarAcessoAdmin($conexao, true);

    // Validação dos dados recebidos
    if (!isset($_FILES['logo']) || !isset($_POST['edital_id'])) {
        throw new Exception('Dados incompletos');
    }

    $edital_id = filter_var($_POST['edital_id'], FILTER_VALIDATE_INT);
    if ($edital_id === false || $edital_id === null) {
        throw new Exception('ID do edital inválido');
    }

    $file = $_FILES['logo'];

    // Validação do arquivo
    if ($file['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('Erro no upload do arquivo: ' . getUploadErrorMessage($file['error']));
    }

    // Validação do tipo MIME
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    if (!$finfo) {
        throw new Exception('Não foi possível verificar o tipo do arquivo');
    }

    $mimeType = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);

    $allowedMimes = [
        'image/jpeg' => 'jpg',
        'image/png'  => 'png',
        'image/gif'  => 'gif'
    ];

    if (!array_key_exists($mimeType, $allowedMimes)) {
        throw new Exception('Tipo de arquivo não permitido. Apenas JPG, PNG e GIF são aceitos.');
    }

    // Converte a imagem para base64
    $imageData = file_get_contents($file['tmp_name']);
    if ($imageData === false) {
        throw new Exception('Erro ao ler o arquivo');
    }

    $base64Image = base64_encode($imageData);
    $fileExtension = $allowedMimes[$mimeType];
    
    // Atualiza o banco com a imagem em base64
    $query = "UPDATE appestudo.edital SET logo_base64 = $1, logo_mime_type = $2 WHERE id_edital = $3 AND EXISTS (SELECT 1 FROM appestudo.edital WHERE id_edital = $3)";
    $result = pg_query_params($conexao, $query, [$base64Image, $mimeType, $edital_id]);

    if (!$result) {
        throw new Exception('Erro ao atualizar o banco de dados: ' . pg_last_error($conexao));
    }

    if (pg_affected_rows($result) === 0) {
        throw new Exception('Edital não encontrado');
    }

    echo json_encode([
        'success' => true,
        'message' => 'Logo atualizada com sucesso'
    ]);

} catch (Exception $e) {
    error_log("Erro no upload de logo: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
} finally {
    if (isset($conexao)) {
        pg_close($conexao);
    }
}

/**
 * Retorna mensagem de erro para códigos de upload
 */
function getUploadErrorMessage(int $error): string {
    $uploadErrors = [
        UPLOAD_ERR_INI_SIZE   => 'Arquivo excede o limite definido no php.ini',
        UPLOAD_ERR_FORM_SIZE  => 'Arquivo excede o limite definido no formulário',
        UPLOAD_ERR_PARTIAL    => 'Upload realizado parcialmente',
        UPLOAD_ERR_NO_FILE    => 'Nenhum arquivo enviado',
        UPLOAD_ERR_NO_TMP_DIR => 'Diretório temporário não encontrado',
        UPLOAD_ERR_CANT_WRITE => 'Falha ao gravar arquivo',
        UPLOAD_ERR_EXTENSION  => 'Upload interrompido por extensão'
    ];

    return $uploadErrors[$error] ?? 'Erro desconhecido no upload';
}
?>
