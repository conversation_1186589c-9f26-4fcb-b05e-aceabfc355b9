<?php
/**
 * Endpoint de Usuários
 * Sistema de Fidelidade da Barbearia
 */

function handleUsers($method, $action, $id, $input, $db) {
    switch ($method) {
        case 'GET':
            if ($action === 'cpf' && !empty($id)) {
                // Verificar se há parâmetro tipo na query string
                $tipo = $_GET['tipo'] ?? null;
                if ($tipo) {
                    getUserByCpfAndType($id, $tipo, $db);
                } else {
                    getUserByCpf($id, $db);
                }
            } elseif ($action === 'clients') {
                getAllClients($db);
            } elseif ($action === 'search') {
                searchUsers($db);
            } elseif ($action === 'birthdays') {
                getUpcomingBirthdays($db);
            } elseif ($action === 'profile' && !empty($id)) {
                getUserProfile($id, $db);
            } elseif ($action === 'complete' && !empty($id)) {
                getUserCompleteData($id, $db);
            } elseif ($action === 'debug' && !empty($id)) {
                debugUserData($id, $db);
            } elseif (!empty($action)) {
                getUserById($action, $db);
            } else {
                ApiResponse::error('Ação não especificada', 400);
            }
            break;
            
        case 'POST':
            if ($action === 'profile') {
                createUserProfile($input, $db);
            } else {
                createUser($input, $db);
            }
            break;
            
        case 'PUT':
            if ($action === 'profile' && !empty($id)) {
                updateUserProfile($id, $input, $db);
            } elseif ($action === 'password' && !empty($id)) {
                updateUserPassword($id, $input, $db);
            } elseif (!empty($action)) {
                updateUser($action, $input, $db);
            } else {
                ApiResponse::error('ID do usuário não especificado', 400);
            }
            break;
            
        case 'DELETE':
            if ($action === 'profile' && !empty($id)) {
                deleteUserProfile($id, $db);
            } else {
                ApiResponse::methodNotAllowed('Exclusão de usuários não permitida');
            }
            break;
            
        default:
            ApiResponse::methodNotAllowed();
    }
}

/**
 * Criar novo usuário
 */
function createUser($input, $db) {
    // Validar dados de entrada
    $requiredFields = ['cpf', 'nome', 'senha', 'tipo'];
    foreach ($requiredFields as $field) {
        if (empty($input[$field])) {
            ApiResponse::validation([$field], "Campo $field é obrigatório");
        }
    }
    
    // Validar CPF
    if (!Validator::validateCPF($input['cpf'])) {
        ApiResponse::validation(['cpf'], 'CPF inválido');
    }
    
    // Validar tipo
    if (!in_array($input['tipo'], ['cliente', 'barbeiro'])) {
        ApiResponse::validation(['tipo'], 'Tipo deve ser "cliente" ou "barbeiro"');
    }
    
    // Validar senha
    if (!Validator::validatePassword($input['senha'])) {
        ApiResponse::validation(['senha'], 'Senha deve ter pelo menos 6 caracteres');
    }
    
    // Validar nome
    if (!Validator::validateName($input['nome'])) {
        ApiResponse::validation(['nome'], 'Nome deve ter pelo menos 2 caracteres');
    }
    
    try {
        // Verificar se CPF já existe para o mesmo tipo
        $sql = "SELECT id FROM usuarios WHERE cpf = ? AND tipo = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$input['cpf'], $input['tipo']]);

        if ($stmt->fetch()) {
            ApiResponse::error('CPF já cadastrado para este tipo de usuário', 409);
        }
        
        // Gerar ID do usuário baseado em CPF + tipo
        $userId = generateUserId($input['cpf'], $input['tipo']);
        
        // Inserir usuário
        $sql = "INSERT INTO usuarios (id, cpf, nome, senha, tipo, is_owner) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([
            $userId,
            $input['cpf'],
            Validator::sanitizeString($input['nome']),
            $input['senha'], // Em produção, usar hash
            $input['tipo'],
            $input['is_owner'] ?? false
        ]);
        
        // Buscar usuário criado
        $sql = "SELECT id, cpf, nome, tipo, is_owner, data_criacao, data_atualizacao 
                FROM usuarios WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$userId]);
        $user = $stmt->fetch();
        
        $user['is_owner'] = (bool) $user['is_owner'];
        
        ApiResponse::success($user, 'Usuário criado com sucesso', 201);
        
    } catch (PDOException $e) {
        error_log("Create user error: " . $e->getMessage());
        ApiResponse::error('Erro ao criar usuário');
    }
}

/**
 * Buscar usuário por CPF
 */
function getUserByCpf($cpf, $db) {
    if (!Validator::validateCPF($cpf)) {
        ApiResponse::validation(['cpf'], 'CPF inválido');
    }

    try {
        $sql = "SELECT id, cpf, nome, tipo, is_owner, data_criacao, data_atualizacao
                FROM usuarios WHERE cpf = ?";

        $stmt = $db->prepare($sql);
        $stmt->execute([$cpf]);
        $users = $stmt->fetchAll();

        if (empty($users)) {
            ApiResponse::notFound('Usuário não encontrado');
        }

        // Se há apenas um usuário, retornar diretamente
        if (count($users) === 1) {
            $users[0]['is_owner'] = (bool) $users[0]['is_owner'];
            ApiResponse::success($users[0]);
        } else {
            // Se há múltiplos usuários (cliente e barbeiro), retornar array
            foreach ($users as &$user) {
                $user['is_owner'] = (bool) $user['is_owner'];
            }
            ApiResponse::success($users);
        }

    } catch (PDOException $e) {
        error_log("Get user by CPF error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar usuário');
    }
}

/**
 * Buscar usuário por CPF e tipo
 */
function getUserByCpfAndType($cpf, $tipo, $db) {
    if (!Validator::validateCPF($cpf)) {
        ApiResponse::validation(['cpf'], 'CPF inválido');
    }

    if (!in_array($tipo, ['cliente', 'barbeiro'])) {
        ApiResponse::validation(['tipo'], 'Tipo deve ser "cliente" ou "barbeiro"');
    }

    try {
        $sql = "SELECT id, cpf, nome, tipo, is_owner, data_criacao, data_atualizacao
                FROM usuarios WHERE cpf = ? AND tipo = ?";

        $stmt = $db->prepare($sql);
        $stmt->execute([$cpf, $tipo]);
        $user = $stmt->fetch();

        if (!$user) {
            ApiResponse::notFound('Usuário não encontrado');
        }

        $user['is_owner'] = (bool) $user['is_owner'];

        ApiResponse::success($user);

    } catch (PDOException $e) {
        error_log("Get user by CPF and type error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar usuário');
    }
}

/**
 * Gerar ID do usuário baseado em CPF e tipo
 */
function generateUserId($cpf, $tipo) {
    return $cpf . '-' . strtoupper($tipo);
}

/**
 * Buscar usuário por ID
 */
function getUserById($id, $db) {
    try {
        $sql = "SELECT id, cpf, nome, tipo, is_owner, data_criacao, data_atualizacao 
                FROM usuarios WHERE id = ?";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$id]);
        $user = $stmt->fetch();
        
        if (!$user) {
            ApiResponse::notFound('Usuário não encontrado');
        }
        
        $user['is_owner'] = (bool) $user['is_owner'];
        
        ApiResponse::success($user);
        
    } catch (PDOException $e) {
        error_log("Get user by ID error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar usuário');
    }
}

/**
 * Buscar todos os clientes
 */
function getAllClients($db) {
    try {
        $sql = "SELECT id, cpf, nome, data_criacao FROM usuarios WHERE tipo = 'cliente' ORDER BY nome";
        
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $clients = $stmt->fetchAll();
        
        ApiResponse::success($clients);
        
    } catch (PDOException $e) {
        error_log("Get all clients error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar clientes');
    }
}

/**
 * Buscar usuários por nome
 */
function searchUsers($db) {
    $nome = $_GET['nome'] ?? '';
    $tipo = $_GET['tipo'] ?? 'cliente';
    
    if (empty($nome)) {
        ApiResponse::validation(['nome'], 'Nome é obrigatório para busca');
    }
    
    try {
        $sql = "SELECT id, cpf, nome, data_criacao 
                FROM usuarios 
                WHERE tipo = ? AND nome LIKE ? 
                ORDER BY nome 
                LIMIT 20";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$tipo, '%' . $nome . '%']);
        $users = $stmt->fetchAll();
        
        ApiResponse::success($users);
        
    } catch (PDOException $e) {
        error_log("Search users error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar usuários');
    }
}

/**
 * Criar perfil do cliente
 */
function createUserProfile($input, $db) {
    if (empty($input['usuario_id'])) {
        ApiResponse::validation(['usuario_id'], 'ID do usuário é obrigatório');
    }

    try {
        $sql = "INSERT INTO perfis_clientes (usuario_id, nome_personalizado, estilo_preferido, observacoes, aniversario, foto_perfil)
                VALUES (?, ?, ?, ?, ?, ?)";

        $stmt = $db->prepare($sql);
        $stmt->execute([
            $input['usuario_id'],
            $input['nome_personalizado'] ?? null,
            $input['estilo_preferido'] ?? null,
            $input['observacoes'] ?? null,
            $input['aniversario'] ?? null,
            $input['foto_perfil'] ?? null
        ]);

        ApiResponse::success(['id' => $db->lastInsertId()], 'Perfil criado com sucesso', 201);

    } catch (PDOException $e) {
        error_log("Create profile error: " . $e->getMessage());
        ApiResponse::error('Erro ao criar perfil');
    }
}

/**
 * Buscar perfil do usuário
 */
function getUserProfile($userId, $db) {
    try {
        $sql = "SELECT * FROM perfis_clientes WHERE usuario_id = ?";

        $stmt = $db->prepare($sql);
        $stmt->execute([$userId]);
        $profile = $stmt->fetch();

        if (!$profile) {
            ApiResponse::notFound('Perfil não encontrado');
        }

        ApiResponse::success($profile);

    } catch (PDOException $e) {
        error_log("Get profile error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar perfil');
    }
}

/**
 * Atualizar perfil do usuário
 */
function updateUserProfile($userId, $input, $db) {
    try {
        $sql = "UPDATE perfis_clientes
                SET nome_personalizado = ?, estilo_preferido = ?, observacoes = ?, aniversario = ?, foto_perfil = ?
                WHERE usuario_id = ?";

        $stmt = $db->prepare($sql);
        $stmt->execute([
            $input['nome_personalizado'] ?? null,
            $input['estilo_preferido'] ?? null,
            $input['observacoes'] ?? null,
            $input['aniversario'] ?? null,
            $input['foto_perfil'] ?? null,
            $userId
        ]);

        if ($stmt->rowCount() === 0) {
            ApiResponse::notFound('Perfil não encontrado');
        }

        ApiResponse::success(null, 'Perfil atualizado com sucesso');

    } catch (PDOException $e) {
        error_log("Update profile error: " . $e->getMessage());
        ApiResponse::error('Erro ao atualizar perfil');
    }
}

/**
 * Deletar perfil do usuário
 */
function deleteUserProfile($userId, $db) {
    try {
        $sql = "DELETE FROM perfis_clientes WHERE usuario_id = ?";

        $stmt = $db->prepare($sql);
        $stmt->execute([$userId]);

        if ($stmt->rowCount() === 0) {
            ApiResponse::notFound('Perfil não encontrado');
        }

        ApiResponse::success(null, 'Perfil deletado com sucesso');

    } catch (PDOException $e) {
        error_log("Delete profile error: " . $e->getMessage());
        ApiResponse::error('Erro ao deletar perfil');
    }
}

/**
 * Buscar aniversariantes próximos
 */
function getUpcomingBirthdays($db) {
    $days = $_GET['days'] ?? 7;

    try {
        $sql = "SELECT u.id, u.nome, p.aniversario
                FROM usuarios u
                JOIN perfis_clientes p ON u.id = p.usuario_id
                WHERE p.aniversario IS NOT NULL
                AND (
                    (DAYOFYEAR(p.aniversario) BETWEEN DAYOFYEAR(NOW()) AND DAYOFYEAR(NOW()) + ?)
                    OR
                    (DAYOFYEAR(p.aniversario) BETWEEN DAYOFYEAR(NOW()) AND DAYOFYEAR(NOW()) + ? - 365)
                )
                ORDER BY DAYOFYEAR(p.aniversario)";

        $stmt = $db->prepare($sql);
        $stmt->execute([$days, $days]);
        $birthdays = $stmt->fetchAll();

        ApiResponse::success($birthdays);

    } catch (PDOException $e) {
        error_log("Get birthdays error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar aniversários');
    }
}

/**
 * Atualizar usuário
 */
function updateUser($id, $input, $db) {
    try {
        $sql = "UPDATE usuarios SET nome = ? WHERE id = ?";

        $stmt = $db->prepare($sql);
        $stmt->execute([
            Validator::sanitizeString($input['nome']),
            $id
        ]);

        if ($stmt->rowCount() === 0) {
            ApiResponse::notFound('Usuário não encontrado');
        }

        ApiResponse::success(null, 'Usuário atualizado com sucesso');

    } catch (PDOException $e) {
        error_log("Update user error: " . $e->getMessage());
        ApiResponse::error('Erro ao atualizar usuário');
    }
}

/**
 * Buscar dados completos do cliente (perfil + pontuação + histórico)
 */
function getUserCompleteData($userId, $db) {
    try {
        error_log("🔍 Buscando dados completos para cliente: " . $userId);

        // Buscar dados da view de relatório de clientes
        $sql = "SELECT * FROM vw_relatorio_clientes WHERE id = ?";

        $stmt = $db->prepare($sql);
        $stmt->execute([$userId]);
        $clientData = $stmt->fetch();

        error_log("📊 Dados encontrados na view: " . json_encode($clientData));

        if ($clientData) {
            error_log("🎨 Estilo preferido da view: " . ($clientData['estilo_preferido'] ?? 'null'));
        }

        if (!$clientData) {
            error_log("❌ Cliente não encontrado na view vw_relatorio_clientes");

            // Verificar se o cliente existe na tabela usuarios
            $sql = "SELECT id, nome, tipo FROM usuarios WHERE id = ?";
            $stmt = $db->prepare($sql);
            $stmt->execute([$userId]);
            $userData = $stmt->fetch();

            if ($userData) {
                error_log("⚠️ Cliente existe na tabela usuarios: " . json_encode($userData));
            } else {
                error_log("❌ Cliente não existe nem na tabela usuarios");
            }

            ApiResponse::notFound('Cliente não encontrado');
        }

        // Buscar dados adicionais do perfil (observações e foto)
        $sql = "SELECT observacoes, foto_perfil FROM perfis_clientes WHERE usuario_id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$userId]);
        $profileExtra = $stmt->fetch();

        error_log("🔍 Buscando perfil extra para usuario_id: " . $userId);
        error_log("📋 Dados do perfil extra: " . json_encode($profileExtra));

        if ($profileExtra) {
            $clientData['observacoes'] = $profileExtra['observacoes'];
            $clientData['foto_perfil'] = $profileExtra['foto_perfil'];
            error_log("✅ Perfil extra encontrado - Obs: " . ($profileExtra['observacoes'] ?? 'null') . ", Foto: " . ($profileExtra['foto_perfil'] ?? 'null'));
        } else {
            $clientData['observacoes'] = null;
            $clientData['foto_perfil'] = null;
            error_log("❌ Nenhum perfil extra encontrado na tabela perfis_clientes");
        }

        ApiResponse::success($clientData);

    } catch (PDOException $e) {
        error_log("Get complete user data error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar dados completos do cliente');
    }
}

/**
 * Debug - Verificar dados do usuário em todas as tabelas
 */
function debugUserData($userId, $db) {
    try {
        $debug = [];

        // 1. Verificar na tabela usuarios
        $sql = "SELECT * FROM usuarios WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$userId]);
        $debug['usuarios'] = $stmt->fetch() ?: 'Não encontrado';

        // 2. Verificar na tabela perfis_clientes
        $sql = "SELECT * FROM perfis_clientes WHERE usuario_id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$userId]);
        $debug['perfis_clientes'] = $stmt->fetch() ?: 'Não encontrado';

        // 3. Verificar na tabela pontuacao_fidelidade
        $sql = "SELECT * FROM pontuacao_fidelidade WHERE cliente_id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$userId]);
        $debug['pontuacao_fidelidade'] = $stmt->fetch() ?: 'Não encontrado';

        // 4. Verificar na view vw_relatorio_clientes
        $sql = "SELECT * FROM vw_relatorio_clientes WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$userId]);
        $debug['vw_relatorio_clientes'] = $stmt->fetch() ?: 'Não encontrado';

        ApiResponse::success($debug, 'Debug dos dados do usuário');

    } catch (PDOException $e) {
        error_log("Debug user data error: " . $e->getMessage());
        ApiResponse::error('Erro ao fazer debug dos dados do usuário');
    }
}

/**
 * Alterar senha do usuário
 */
function updateUserPassword($userId, $input, $db) {
    try {
        // Validar dados de entrada
        $requiredFields = ['senha_atual', 'nova_senha'];
        foreach ($requiredFields as $field) {
            if (empty($input[$field])) {
                ApiResponse::validation([$field], "Campo $field é obrigatório");
            }
        }

        // Validar nova senha
        if (!Validator::validatePassword($input['nova_senha'])) {
            ApiResponse::validation(['nova_senha'], 'Nova senha deve ter pelo menos 6 caracteres');
        }

        // Verificar se o usuário existe e se a senha atual está correta
        $sql = "SELECT senha FROM usuarios WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$userId]);
        $user = $stmt->fetch();

        if (!$user) {
            ApiResponse::notFound('Usuário não encontrado');
        }

        // Verificar senha atual
        if ($user['senha'] !== $input['senha_atual']) {
            ApiResponse::validation(['senha_atual'], 'Senha atual incorreta');
        }

        // Atualizar senha
        $sql = "UPDATE usuarios SET senha = ? WHERE id = ?";
        $stmt = $db->prepare($sql);
        $stmt->execute([$input['nova_senha'], $userId]);

        if ($stmt->rowCount() === 0) {
            ApiResponse::error('Erro ao atualizar senha');
        }

        ApiResponse::success(null, 'Senha alterada com sucesso');

    } catch (PDOException $e) {
        error_log("Update user password error: " . $e->getMessage());
        ApiResponse::error('Erro ao alterar senha');
    }
}
?>
