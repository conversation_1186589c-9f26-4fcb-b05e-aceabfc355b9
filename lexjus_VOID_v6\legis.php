<?php
// Headers para evitar cache da página HTML
header('Cache-Control: no-cache, no-store, must-revalidate, max-age=0');
header('Pragma: no-cache');
header('Expires: Thu, 01 Jan 1970 00:00:00 GMT');

// Incluir sistema de cache busting
require_once 'includes/cache_buster.php';
include_once("../session_config.php");
require_once '../conexao_POST.php';

// Verificar se o usuário está logado
if (!verificarSessaoValida()) {
    header("Location: ../login_index.php");
    exit();
}

// Buscar nome do usuário
$query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = " . $_SESSION['idusuario'];
$resultado_nome = pg_query($conexao, $query_buscar_nome);
$nome_usuario = ($resultado_nome && pg_num_rows($resultado_nome) > 0)
    ? pg_fetch_assoc($resultado_nome)['nome']
    : "Usuário";
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LexJus - Legislação</title>
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <meta name="app-version" content="<?php echo cache_version(); ?>">
    
    <!-- CSS Principal -->
    <link rel="stylesheet" href="<?php echo cache_css('style.css'); ?>">
    <link rel="stylesheet" href="<?php echo cache_css('css/home-netflix.css'); ?>">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <div class="header-barra">
        <div class="header-left">
            <a href="home_mae.php" class="back-btn">
                <i class="fas fa-arrow-left"></i>
                Voltar
            </a>
            <div class="logo">
                <img src="../logo/logo_vertical_azul.png" alt="Logo" class="logo-light">
                <img src="../logo/logo_vertical.png" alt="Logo" class="logo-dark">
            </div>
        </div>
        <div class="header-right">
            <div class="user-info">
                <i class="fas fa-user-circle"></i>
                <span class="user-name"><?php echo htmlspecialchars($nome_usuario ?? 'Usuário'); ?></span>
            </div>
            <div class="theme-toggle">
                <button id="themeToggle" class="theme-btn" title="Alternar tema">
                    <i class="fas fa-moon" id="themeIcon"></i>
                </button>
            </div>
        </div>
    </div>


    <!-- Seção Principal -->
    <section class="main-section">
        <div class="container">
            <div class="welcome-header">
                <h1 class="welcome-title">
                <i class="fas fa-balance-scale"></i> Legislação
                </h1>
                <p class="welcome-subtitle">
                    Escolha qual leigislação você deseja estudar hoje
                </p>
            </div>
        </div>
    </section>

    <!-- Leis Section -->
    <section class="leis-section">
        <div class="container">
            <!-- Grid de Leis -->
            <div class="leis-grid">
                <!-- Card Constituição Federal -->
                <div class="lei-card" data-codigo="CF" onclick="selecionarLei('CF')">
                    <div class="lei-card-header">
                        <div class="lei-card-icon" style="background: #e74c3c;">
                            <i class="fas fa-flag"></i>
                        </div>
                        <div class="lei-card-title">CF</div>
                        <div class="lei-card-subtitle">Constituição Federal</div>
                    </div>
                    <div class="lei-card-body">
                        <div class="lei-card-description">
                            A Lei Fundamental do Brasil, que estabelece os direitos e deveres dos cidadãos e a organização do Estado.
                        </div>
                    </div>
                    <div class="lei-card-footer">
                        <button class="btn-estudar-card">
                            <i class="fas fa-play"></i>
                            Estudar Esta Lei
                        </button>
                    </div>
                </div>

                <!-- Card Código Civil -->
                <div class="lei-card" data-codigo="CC" onclick="selecionarLei('CC')">
                    <div class="lei-card-header">
                        <div class="lei-card-icon" style="background: #2ecc71;">
                            <i class="fas fa-balance-scale"></i>
                        </div>
                        <div class="lei-card-title">CC</div>
                        <div class="lei-card-subtitle">Código Civil</div>
                    </div>
                    <div class="lei-card-body">
                        <div class="lei-card-description">
                            Regula as relações jurídicas entre pessoas físicas e jurídicas, direitos e obrigações de ordem privada.
                        </div>
                    </div>
                    <div class="lei-card-footer">
                        <button class="btn-estudar-card">
                            <i class="fas fa-play"></i>
                            Estudar Esta Lei
                        </button>
                    </div>
                </div>

                <!-- Card Código de Processo Civil -->
                <div class="lei-card" data-codigo="CPC" onclick="selecionarLei('CPC')">
                    <div class="lei-card-header">
                        <div class="lei-card-icon" style="background: #3498db;">
                            <i class="fas fa-gavel"></i>
                        </div>
                        <div class="lei-card-title">CPC</div>
                        <div class="lei-card-subtitle">Código de Processo Civil</div>
                    </div>
                    <div class="lei-card-body">
                        <div class="lei-card-description">
                            Estabelece as normas que regem o processo civil, definindo procedimentos para resolução de conflitos.
                        </div>
                    </div>
                    <div class="lei-card-footer">
                        <button class="btn-estudar-card">
                            <i class="fas fa-play"></i>
                            Estudar Esta Lei
                        </button>
                    </div>
                </div>

                <!-- Card Código Penal -->
                <div class="lei-card" data-codigo="CP" onclick="selecionarLei('CP')">
                    <div class="lei-card-header">
                        <div class="lei-card-icon" style="background: #9b59b6;">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="lei-card-title">CP</div>
                        <div class="lei-card-subtitle">Código Penal</div>
                    </div>
                    <div class="lei-card-body">
                        <div class="lei-card-description">
                            Define os crimes e suas respectivas penas, estabelecendo as bases do direito penal brasileiro.
                        </div>
                    </div>
                    <div class="lei-card-footer">
                        <button class="btn-estudar-card">
                            <i class="fas fa-play"></i>
                            Estudar Esta Lei
                        </button>
                    </div>
                </div>

                <!-- Card Código de Processo Penal -->
                <div class="lei-card" data-codigo="CPP" onclick="selecionarLei('CPP')">
                    <div class="lei-card-header">
                        <div class="lei-card-icon" style="background: #f39c12;">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="lei-card-title">CPP</div>
                        <div class="lei-card-subtitle">Código de Processo Penal</div>
                    </div>
                    <div class="lei-card-body">
                        <div class="lei-card-description">
                            Regula o processo penal brasileiro, estabelecendo procedimentos para investigação e julgamento de crimes.
                        </div>
                    </div>
                    <div class="lei-card-footer">
                        <button class="btn-estudar-card">
                            <i class="fas fa-play"></i>
                            Estudar Esta Lei
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Script para seleção de leis -->
    <script>
        function selecionarLei(codigo) {
            // Redirecionar para o sistema de estudo da lei selecionada
            window.location.href = `index.php?lei=${codigo}`;
        }
    </script>

    <!-- Script do tema escuro -->
    <script>
        // Sistema de tema escuro/claro
        const themeToggle = document.getElementById('themeToggle');
        const themeIcon = document.getElementById('themeIcon');
        const body = document.body;

        // Verificar tema salvo
        const savedTheme = localStorage.getItem('theme') || 'light';
        body.setAttribute('data-theme', savedTheme);
        updateThemeIcon(savedTheme === 'dark');

        // Toggle do tema
        themeToggle.addEventListener('click', () => {
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateThemeIcon(newTheme === 'dark');
        });

        function updateThemeIcon(isDark) {
            themeIcon.className = isDark ? 'fas fa-sun' : 'fas fa-moon';
        }
    </script>
</body>
</html>
