<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Planeja AI - Dashboard de Estudos</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.5.0/fonts/remixicon.css" rel="stylesheet"/>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.5.0/echarts.min.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#1e3a8a',
                        secondary: '#ef4444'
                    },
                    borderRadius: {
                        'none': '0px',
                        'sm': '4px',
                        DEFAULT: '8px',
                        'md': '12px', 
                        'lg': '16px',
                        'xl': '20px',
                        '2xl': '24px',
                        '3xl': '32px',
                        'full': '9999px',
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    <style>
        :where([class^="ri-"])::before { content: "\f3c2"; }
        .progress-bar {
            height: 8px;
            background: #e5e7eb;
            border-radius: 4px;
            overflow: hidden;
        }
        .progress-bar-fill {
            height: 100%;
            background: #1e3a8a;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <nav class="bg-primary text-white">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <span class="font-['Pacifico'] text-2xl">Planeja AI</span>
                </div>
                <div class="flex items-center space-x-8">
                    <a href="#" class="flex items-center space-x-2">
                        <i class="ri-timer-line"></i>
                        <span>Cronômetro</span>
                    </a>
                    <a href="#" class="flex items-center space-x-2">
                        <i class="ri-history-line"></i>
                        <span>Histórico</span>
                    </a>
                    <a href="#" class="flex items-center space-x-2">
                        <i class="ri-file-list-line"></i>
                        <span>Anotações</span>
                    </a>
                    <a href="#" class="flex items-center space-x-2">
                        <i class="ri-flashcard-line"></i>
                        <span>Flashcards</span>
                    </a>
                    <a href="#" class="flex items-center space-x-2">
                        <i class="ri-calendar-line"></i>
                        <span>Cronograma</span>
                    </a>
                    <div class="flex items-center space-x-2">
                        <i class="ri-user-line"></i>
                        <span>Bruno Rafael</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="border-b">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex space-x-6">
                <a href="#" class="flex items-center py-4 px-2 border-b-2 border-primary">
                    <i class="ri-home-line mr-2"></i>
                    <span>Início</span>
                </a>
                <a href="#" class="flex items-center py-4 px-2">
                    <i class="ri-book-line mr-2"></i>
                    <span>Estudo</span>
                </a>
                <a href="#" class="flex items-center py-4 px-2">
                    <i class="ri-calendar-line mr-2"></i>
                    <span>Agenda</span>
                </a>
                <a href="#" class="flex items-center py-4 px-2">
                    <i class="ri-bar-chart-line mr-2"></i>
                    <span>Estatística</span>
                </a>
            </div>
        </div>
    </div>

    <main class="max-w-7xl mx-auto px-4 py-8">
        <div class="flex justify-between items-start mb-8">
            <div class="flex-1">
                <div class="flex items-center space-x-2 mb-4">
                    <div class="flex space-x-1">
                        <div class="w-8 h-8 bg-secondary rounded flex items-center justify-center text-white text-sm">X</div>
                        <div class="w-8 h-8 bg-secondary rounded flex items-center justify-center text-white text-sm">X</div>
                        <div class="w-8 h-8 bg-secondary rounded flex items-center justify-center text-white text-sm">X</div>
                        <div class="w-8 h-8 bg-gray-200 rounded flex items-center justify-center text-gray-400 text-sm">-</div>
                    </div>
                </div>
            </div>
            <div class="text-center">
                <div class="text-5xl font-bold text-primary">0</div>
                <div class="text-sm text-gray-600">dias seguidos</div>
                <div class="mt-2 text-xs text-gray-500">
                    Seu recorde: 22 dias
                    <i class="ri-medal-line text-yellow-500"></i>
                </div>
            </div>
        </div>

        <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-primary mb-2">Modo Preguiça Ativado! 😴</h2>
            <p class="text-gray-600">Tá mais parado que internet discada! Bora estudar?</p>
        </div>

        <div class="grid grid-cols-3 gap-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">Planejamento</h3>
                    <span class="text-sm text-gray-500">DELTA-MG</span>
                </div>
                <div class="space-y-4">
                    <div class="flex justify-between text-sm">
                        <div>
                            <div class="text-gray-500">Início</div>
                            <div>26-08-2024</div>
                        </div>
                        <div class="text-right">
                            <div class="text-gray-500">Término</div>
                            <div>26-01-2025</div>
                        </div>
                    </div>
                    <div class="flex justify-between text-sm">
                        <div>
                            <div class="text-gray-500">Total</div>
                            <div>153 Dias</div>
                        </div>
                        <div class="text-right">
                            <div class="text-gray-500">Faltam</div>
                            <div>31 Dias</div>
                        </div>
                    </div>
                    <div>
                        <div class="flex justify-between text-sm mb-2">
                            <span>Progresso do Planejamento</span>
                            <span>79.7%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-bar-fill" style="width: 79.7%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center space-x-2 mb-4">
                    <i class="ri-sun-line text-yellow-500"></i>
                    <h3 class="text-lg font-semibold">Gladiador Matinal</h3>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold mb-2">Manhã</div>
                    <div class="text-4xl font-bold text-primary mb-4">42h:50min</div>
                    <p class="text-gray-600 text-sm">Transformando o ralho da manhã em sabedoria pura</p>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold mb-4">Agenda Pessoal</h3>
                <div class="space-y-4">
                    <div class="p-3 bg-red-50 rounded-lg">
                        <div class="flex items-center text-red-600 mb-1">
                            <i class="ri-calendar-event-line mr-2"></i>
                            <span class="font-medium">Pendente: Concurseiro Fora da Caixa</span>
                        </div>
                    </div>
                    <div class="p-3 bg-orange-50 rounded-lg">
                        <div class="text-orange-600 mb-1">
                            <div class="flex items-center">
                                <i class="ri-calendar-line mr-2"></i>
                                <span>Data Mais Próxima: 28-12-2024</span>
                            </div>
                        </div>
                        <div class="flex items-center text-orange-600">
                            <i class="ri-notification-line mr-2"></i>
                            <span>Eventos: Assinatura QConcursos</span>
                        </div>
                        <div class="flex items-center text-orange-600 mt-1">
                            <i class="ri-time-line mr-2"></i>
                            <span>Falta: 2 Dias</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="mt-8 bg-white rounded-lg shadow p-6">
            <div class="text-center">
                <h3 class="text-xl font-bold text-primary mb-2">A CONSTÂNCIA é mais importante que a INTENSIDADE!</h3>
                <div class="text-lg">
                    Sua meta DIÁRIA é <span class="bg-primary text-white px-3 py-1 rounded">03h:00min</span>
                </div>
            </div>
        </div>
    </main>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const progressBar = document.querySelector('.progress-bar-fill');
            const progress = 79.7;
            progressBar.style.width = `${progress}%`;
        });
    </script>
</body>
</html>