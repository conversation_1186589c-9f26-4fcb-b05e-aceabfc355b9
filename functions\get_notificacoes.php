<?php
function get_notificacoes($conexao, $usuario_id) {
    $query = "
        SELECT n.*
        FROM appestudo.notificacoes n 
        WHERE 
            n.status = true 
            AND n.data_expiracao > CURRENT_TIMESTAMP 
            AND (
                n.is_global = true 
                OR EXISTS (
                    SELECT 1 
                    FROM appestudo.notificacoes_usuarios nu 
                    WHERE nu.notificacao_id = n.id 
                    AND nu.usuario_id = $1
                )
            )
        ORDER BY n.data_criacao DESC";

    return pg_query_params($conexao, $query, array($usuario_id));
}


