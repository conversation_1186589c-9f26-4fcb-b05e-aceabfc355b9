<?php
include 'processa_dias.php';

$diasDaSemana = array(
    'Sun' => 'Domingo',
    'Mon' => 'Segunda-feira',
    'Tue' => 'Terça-feira',
    'Wed' => 'Quarta-feira',
    'Thu' => 'Quinta-feira',
    'Fri' => 'Sexta-feira',
    'Sat' => 'Sábado'
);

$datas = [];
$tempos = [];

foreach ($tempoEstudadoPorDia as $data => $tempo) {
    $dataFormatada = date('d/m/Y', strtotime($data));
    $diaSemana = $diasDaSemana[date('D', strtotime($data))];
    $horas = floor($tempo / 3600);
    $minutos = floor(($tempo % 3600) / 60);

    $datas[] = "$dataFormatada ($diaSemana)";
    $tempos[] = $tempo;
}

$numeroDias = count($tempos);
$totalSegundos = array_sum($tempos);
$mediaHorasEstudadas = $numeroDias > 0 ? $totalSegundos / ($numeroDias * 3600) : 0;
$mediaHoras = floor($mediaHorasEstudadas);
$mediaMinutos = floor(($mediaHorasEstudadas - $mediaHoras) * 60);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title>Hora Líquida por Dia</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Carregue o Chart.js e o plugin de anotações na ordem correta -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@1.4.0/dist/chartjs-plugin-annotation.min.js"></script>
    <!-- Tailwind CSS para produção via CDN (nota: para produção, considere instalar localmente) -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f5ff',
                            100: '#e0eafc',
                            500: '#4169E1',
                            600: '#3050c8',
                            700: '#000080',
                            800: '#0a1f6c',
                        },
                        success: '#93f77f',
                        warning: '#f7e982',
                        danger: '#f2aaaa'
                    },
                    fontFamily: {
                        'sans': ['Quicksand', 'sans-serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50 font-sans">
    <div class="max-w-7xl mx-auto p-4 md:p-6">
        

            
            <div class="p-6">
                <div class="h-[600px] relative">
                    <canvas id="myChart"></canvas>
                </div>
                
                <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-primary-50 p-4 rounded-lg text-center">
                        <div class="text-sm text-primary-600 font-bold mb-1">Duração Total</div>
                        <div class="text-xl font-bold text-primary-700 flex justify-center">
                            <span id="duracaoTotal"></span>
                        </div>
                    </div>
                    
                    <div class="bg-primary-50 p-4 rounded-lg text-center">
                        <div class="text-sm text-primary-600 font-bold mb-1">Média Diária de Estudo</div>
                        <div class="text-xl font-bold text-primary-700 flex justify-center">
                            <span id="tempoMedio"></span>
                        </div>
                    </div>
                    
                    <div class="bg-primary-50 p-4 rounded-lg text-center">
                        <div class="text-sm text-primary-600 font-bold mb-1">Meta Diária</div>
                        <div class="text-xl font-bold text-primary-700 flex justify-center">
                            <span id="metaDiaria"></span>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 flex flex-wrap gap-3 justify-center">
                    <div class="flex items-center">
                        <span class="inline-block w-3 h-3 rounded-full bg-danger mr-2"></span>
                        <span class="text-sm">Abaixo do Esperado</span>
                    </div>
                    <div class="flex items-center">
                        <span class="inline-block w-3 h-3 rounded-full bg-warning mr-2"></span>
                        <span class="text-sm">Próximo da Meta</span>
                    </div>
                    <div class="flex items-center">
                        <span class="inline-block w-3 h-3 rounded-full bg-success mr-2"></span>
                        <span class="text-sm">Meta Atingida</span>
                    </div>
                    <!-- Adicionar legendas para as linhas tracejadas -->
                    <div class="flex items-center">
                        <span class="inline-block w-6 h-0 border-t-2 border-dashed border-green-600 mr-2"></span>
                        <span class="text-sm">Meta Diária</span>
                    </div>
                    <div class="flex items-center">
                        <span class="inline-block w-6 h-0 border-t-2 border-dashed border-orange-400 mr-2"></span>
                        <span class="text-sm">50% da Meta</span>
                    </div>
                </div>
            </div>
       
    </div>

<script>
    var ctx = document.getElementById('myChart').getContext('2d');
    var datas = <?php echo json_encode($datas); ?>;
    var tempos = <?php echo json_encode($tempos); ?>;
    var mediaHoras = <?php echo $mediaHoras; ?>;
    var mediaMinutos = <?php echo $mediaMinutos; ?>;
    var tempoPlanejamento = <?php echo $tempoPlanejamento ?>;
    var metadeTempoPlanejamentoSegundos = tempoPlanejamento / 2;

    // Cálculo da duração total
    var duracaoTotal = tempos.reduce((total, tempo) => total + tempo, 0);
    var duracaoTotalEmHoras = Math.floor(duracaoTotal / 3600);
    var duracaoTotalEmMinutos = Math.floor((duracaoTotal % 3600) / 60);

    // Atualizar os elementos de resumo
    document.getElementById('duracaoTotal').textContent = 
        duracaoTotalEmHoras + 'h ' + (duracaoTotalEmMinutos < 10 ? '0' : '') + duracaoTotalEmMinutos + 'min';
    document.getElementById('tempoMedio').textContent = 
        mediaHoras + 'h ' + (mediaMinutos < 10 ? '0' : '') + mediaMinutos + 'min';
    
    // Converter tempoPlanejamento para formato legível
    var metaHoras = Math.floor(tempoPlanejamento / 3600);
    var metaMinutos = Math.floor((tempoPlanejamento % 3600) / 60);
    document.getElementById('metaDiaria').textContent = 
        metaHoras + 'h ' + (metaMinutos < 10 ? '0' : '') + metaMinutos + 'min';

    // Configuração global da fonte para o Chart.js
    Chart.defaults.font.family = 'Quicksand, sans-serif';
    Chart.defaults.font.size = 14;
    Chart.defaults.color = '#333';

    // Formatação para exibir horas e minutos
    function formatarTempoHorasMinutos(segundos) {
        var horas = Math.floor(segundos / 3600);
        var minutos = Math.floor((segundos % 3600) / 60);
        return horas + 'h' + (minutos < 10 ? '0' : '') + minutos;
    }

    // Criar o gráfico com as linhas de referência
    var myChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: datas,
            datasets: [
                {
                    label: 'Abaixo do Esperado',
                    data: tempos.map(tempo => tempo < metadeTempoPlanejamentoSegundos ? tempo : null),
                    backgroundColor: 'rgba(242, 170, 170, 0.2)',
                    borderColor: '#f2aaaa',
                    pointBackgroundColor: '#f2aaaa',
                    pointRadius: 7,
                    pointHoverRadius: 9,
                    pointStyle: 'circle',
                    showLine: true,
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                },
                {
                    label: 'Próximo da Meta',
                    data: tempos.map(tempo => tempo >= metadeTempoPlanejamentoSegundos && tempo < tempoPlanejamento ? tempo : null),
                    backgroundColor: 'rgba(247, 233, 130, 0.2)',
                    borderColor: '#f7e982',
                    pointBackgroundColor: '#f7e982',
                    pointRadius: 7,
                    pointHoverRadius: 9,
                    pointStyle: 'circle',
                    showLine: true,
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                },
                {
                    label: 'Meta Atingida',
                    data: tempos.map(tempo => tempo >= tempoPlanejamento ? tempo : null),
                    backgroundColor: 'rgba(147, 247, 127, 0.2)',
                    borderColor: '#93f77f',
                    pointBackgroundColor: '#93f77f',
                    pointRadius: 7,
                    pointHoverRadius: 9,
                    pointStyle: 'circle',
                    showLine: true,
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }
            ]
        },
        options: {
            plugins: {
                legend: {
                    display: false,
                    labels: {
                        font: {
                            family: 'Quicksand, sans-serif',
                            size: 14
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    titleColor: '#333',
                    bodyColor: '#333',
                    borderColor: '#ddd',
                    borderWidth: 1,
                    cornerRadius: 8,
                    padding: 12,
                    boxPadding: 6,
                    usePointStyle: true,
                    titleFont: {
                        family: 'Quicksand, sans-serif',
                        size: 14,
                        weight: 'bold'
                    },
                    bodyFont: {
                        family: 'Quicksand, sans-serif',
                        size: 14
                    },
                    callbacks: {
                        label: function(context) {
                            var horas = Math.floor(context.raw / 3600);
                            var minutos = Math.floor((context.raw % 3600) / 60);
                            return horas + 'h ' + (minutos < 10 ? '0' : '') + minutos + 'm';
                        }
                    }
                },
                annotation: {
                    annotations: {
                        metaDiaria: {
                            type: 'line',
                            yMin: tempoPlanejamento,
                            yMax: tempoPlanejamento,
                            borderColor: 'rgba(0, 128, 0, 0.7)',
                            borderWidth: 2,
                            borderDash: [6, 4],
                            label: {
                                display: true,
                                content: 'Meta: ' + metaHoras + 'h' + (metaMinutos < 10 ? '0' : '') + metaMinutos,
                                position: 'end',
                                backgroundColor: 'rgba(0, 128, 0, 0.7)',
                                font: {
                                    family: 'Quicksand, sans-serif',
                                    size: 12,
                                    weight: 'bold'
                                }
                            }
                        },
                        metadeMetaDiaria: {
                            type: 'line',
                            yMin: metadeTempoPlanejamentoSegundos,
                            yMax: metadeTempoPlanejamentoSegundos,
                            borderColor: 'rgba(255, 165, 0, 0.7)',
                            borderWidth: 2,
                            borderDash: [6, 4],
                            label: {
                                display: true,
                                content: '50%: ' + Math.floor(metaHoras/2) + 'h' + (Math.floor((metaMinutos/2)) < 10 ? '0' : '') + Math.floor(metaMinutos/2),
                                position: 'end',
                                backgroundColor: 'rgba(255, 165, 0, 0.7)',
                                font: {
                                    family: 'Quicksand, sans-serif',
                                    size: 12,
                                    weight: 'bold'
                                }
                            }
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)',
                        drawBorder: false
                    },
                    ticks: {
                        callback: function(value) {
                            var horas = Math.floor(value / 3600);
                            var minutos = Math.floor((value % 3600) / 60);
                            return horas + ':' + (minutos < 10 ? '0' : '') + minutos;
                        },
                        font: {
                            family: 'Quicksand, sans-serif',
                            size: 14
                        },
                        // Forçar a inclusão dos valores das linhas de referência
                        includeBounds: true
                    },
                    // Definir valores específicos para aparecerem no eixo Y
                    ticks: {
                        // Incluir os valores das linhas de referência
                        callback: function(value) {
                            var horas = Math.floor(value / 3600);
                            var minutos = Math.floor((value % 3600) / 60);
                            return horas + ':' + (minutos < 10 ? '0' : '') + minutos;
                        },
                        font: {
                            family: 'Quicksand, sans-serif',
                            size: 14
                        },
                        // Forçar a exibição dos valores específicos
                        stepSize: Math.ceil(tempoPlanejamento / 5 / 3600) * 3600, // Arredondar para hora completa
                        includeBounds: true
                    },
                    // Adicionar os valores específicos das linhas de referência
                    afterTickToLabelConversion: function(scaleInstance) {
                        // Garantir que os valores das linhas de referência estejam incluídos
                        const ticks = scaleInstance.ticks;
                        
                        // Verificar se os valores das linhas já estão incluídos
                        let temMetaDiaria = false;
                        let temMetade = false;
                        
                        for (let i = 0; i < ticks.length; i++) {
                            const valorTick = parseFloat(ticks[i].value);
                            if (Math.abs(valorTick - tempoPlanejamento) < 1) {
                                temMetaDiaria = true;
                                // Destacar o valor da meta
                                ticks[i].major = true;
                                ticks[i].fontStyle = 'bold';
                            }
                            if (Math.abs(valorTick - metadeTempoPlanejamentoSegundos) < 1) {
                                temMetade = true;
                                // Destacar o valor da metade da meta
                                ticks[i].major = true;
                                ticks[i].fontStyle = 'bold';
                            }
                        }
                        
                        // Adicionar o valor da meta se não estiver presente
                        if (!temMetaDiaria) {
                            const metaHoras = Math.floor(tempoPlanejamento / 3600);
                            const metaMinutos = Math.floor((tempoPlanejamento % 3600) / 60);
                            const label = metaHoras + ':' + (metaMinutos < 10 ? '0' : '') + metaMinutos;
                            
                            ticks.push({
                                value: tempoPlanejamento,
                                label: label,
                                major: true
                            });
                        }
                        
                        // Adicionar o valor da metade da meta se não estiver presente
                        if (!temMetade) {
                            const metadeHoras = Math.floor(metadeTempoPlanejamentoSegundos / 3600);
                            const metadeMinutos = Math.floor((metadeTempoPlanejamentoSegundos % 3600) / 60);
                            const label = metadeHoras + ':' + (metadeMinutos < 10 ? '0' : '') + metadeMinutos;
                            
                            ticks.push({
                                value: metadeTempoPlanejamentoSegundos,
                                label: label,
                                major: true
                            });
                        }
                        
                        // Ordenar os ticks por valor
                        ticks.sort((a, b) => a.value - b.value);
                    },
                    title: {
                        display: true,
                        text: 'Tempo de Estudo',
                        font: {
                            family: 'Quicksand, sans-serif',
                            size: 14,
                            weight: 'bold'
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            family: 'Quicksand, sans-serif',
                            size: 14
                        }
                    },
                    title: {
                        display: true,
                        text: 'Dias Estudados nos Últimos 15 dias',
                        font: {
                            family: 'Quicksand, sans-serif',
                            size: 14,
                            weight: 'bold'
                        }
                    }
                }
            },
            maintainAspectRatio: false,
            responsive: true,
            layout: {
                padding: {
                    left: 10,
                    right: 30,
                    top: 20,
                    bottom: 10
                }
            },
            interaction: {
                mode: 'index',
                intersect: false
            },
            elements: {
                line: {
                    tension: 0.4
                }
            }
        }
    });
</script>
</body>
</html>
