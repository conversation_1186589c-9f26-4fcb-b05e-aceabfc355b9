<?php
session_start();

// Verifica se já está logado
if (isset($_SESSION['idusuario'])) {
    header('Location: index.php');
    exit();
}

// Processa o formulário de login
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = $_POST['email'] ?? '';
    $senha = $_POST['senha'] ?? '';

    // Validação básica
    if (empty($email) || empty($senha)) {
        $erro = 'Por favor, preencha todos os campos';
    } else {
        // Conexão com o banco de dados
        require_once 'assets/config.php';
        
        // Verifica se a tabela usuarios existe
        $checkTable = pg_query($conexao, 
            "SELECT EXISTS (
                SELECT FROM pg_tables
                WHERE schemaname = 'public'
                AND tablename = 'usuarios'
            )");
        
        if (!$checkTable || !pg_fetch_result($checkTable, 0, 0)) {
            // Cria a tabela se não existir
            $createTable = pg_query($conexao, "
                CREATE TABLE usuarios (
                    id SERIAL PRIMARY KEY,
                    nome VARCHAR(100) NOT NULL,
                    email VARCHAR(100) UNIQUE NOT NULL,
                    senha VARCHAR(32) NOT NULL
                )");
            
            if (!$createTable) {
                $erro = 'Erro ao criar tabela de usuários';
                return;
            }
            
            // Insere um usuário admin padrão
            pg_query_params($conexao, 
                "INSERT INTO usuarios (nome, email, senha) VALUES ($1, $2, md5($3))",
                array('Admin', '<EMAIL>', 'admin123'));
        }
        
        // Verifica as credenciais
        $query = "SELECT id, nome, email FROM usuarios WHERE email = $1 AND senha = md5($2)";
        $result = pg_query_params($conexao, $query, array($email, $senha));
        
        if ($result && pg_num_rows($result) > 0) {
            $usuario = pg_fetch_assoc($result);
            
            // Inicia a sessão
            $_SESSION['idusuario'] = $usuario['id'];
            $_SESSION['nome'] = $usuario['nome'];
            $_SESSION['email'] = $usuario['email'];
            
            header('Location: index.php');
            exit();
        } else {
            $erro = 'Credenciais inválidas';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Sistema de Estudos</title>
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="login-container">
        <div class="login-box">
            <h1>Login</h1>
            
            <?php if (isset($erro)): ?>
                <div class="alert alert-danger"><?= htmlspecialchars($erro) ?></div>
            <?php endif; ?>
            
            <form method="POST">
                <div class="form-group">
                    <label for="email">E-mail:</label>
                    <input type="email" id="email" name="email" required>
                </div>
                
                <div class="form-group">
                    <label for="senha">Senha:</label>
                    <input type="password" id="senha" name="senha" required>
                </div>
                
                <button type="submit" class="btn-login">Entrar</button>
            </form>
        </div>
    </div>
</body>
</html>
