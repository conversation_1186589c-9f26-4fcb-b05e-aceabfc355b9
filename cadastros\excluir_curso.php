<?php
session_start();
require_once("assets/config.php");
require_once("includes/verify_admin.php");

header('Content-Type: application/json');

try {
    verificarAcessoAdmin($conexao, true);

    $cursoId = filter_input(INPUT_POST, 'cursoId', FILTER_VALIDATE_INT);

    if ($cursoId === false || $cursoId === null) {
        throw new Exception('ID do curso inválido ou não fornecido');
    }

    // Primeiro, exclui as relações na tabela usuario_has_curso
    $query_delete_relations = "DELETE FROM appEstudo.usuario_has_curso WHERE curso_idcurso = $1";
    $result_relations = pg_query_params($conexao, $query_delete_relations, [$cursoId]);

    if (!$result_relations) {
        throw new Exception('Erro ao remover relações do curso: ' . pg_last_error($conexao));
    }

    // Depois, exclui o curso
    $query_delete_curso = "DELETE FROM appEstudo.curso WHERE idcurso = $1";
    $result_curso = pg_query_params($conexao, $query_delete_curso, [$cursoId]);

    if (!$result_curso) {
        throw new Exception('Erro ao excluir o curso: ' . pg_last_error($conexao));
    }

    echo json_encode([
        'success' => true,
        'message' => [
            'titulo' => 'Sucesso!',
            'conteudo' => '
                <div class="modal-success-content">
                    <div class="success-icon">
                        <i class="fas fa-check-circle fa-3x"></i>
                    </div>
                    <div class="success-message">
                        <h4>Curso excluído com sucesso!</h4>
                        <p>O curso foi removido corretamente.</p>
                    </div>
                </div>
            '
        ]
    ]);

} catch (Exception $e) {
    error_log("Erro na exclusão do curso: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => [
            'titulo' => 'Erro',
            'conteudo' => '
                <div class="modal-error-content">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-circle fa-3x"></i>
                    </div>
                    <div class="error-message">
                        <h4>Não foi possível excluir o curso</h4>
                        <p>Por favor, tente novamente.</p>
                    </div>
                </div>
            '
        ]
    ]);
}
?>
