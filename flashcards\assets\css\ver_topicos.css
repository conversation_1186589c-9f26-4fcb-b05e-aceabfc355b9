/* Dark mode & theme variables */
:root {
  --primary-color: #000080;
  --paper-color: #FFFFFF;
  --secondary-color: #4169E1;
  --background-color: #E8ECF3;
  --text-color: #2C3345;
  --border-color: #B8C2CC;
  --hover-color: #f0f5ff;
  --shadow-color: rgba(0, 0, 128, 0.1);
  --success-color: #2e7d32;
  --error-color: #b71c1c;
  --warning-color: #f0ad4e;
}

/* Dark theme variables */
[data-theme="dark"] {
  --primary-color: #4169E1;
  --paper-color: #1e2130;
  --secondary-color: #6c92ff;
  --background-color: #121420;
  --text-color: #e4e8f0;
  --border-color: #3a4056;
  --hover-color: #2c3251;
  --shadow-color: rgba(0, 0, 0, 0.3);
  --success-color: #4caf50;
  --error-color: #f44336;
  --warning-color: #ffb74d;
}

body {
  background-color: var(--background-color);
  font-family: 'Nunito', 'Quicksand', 'Varela Round', sans-serif;
  color: var(--text-color);
  margin: 0;
  padding: 0;
  min-height: 100vh;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Header styles for dark mode */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.8rem 2rem;
  background-color: var(--paper-color);
  box-shadow: 0 3px 8px var(--shadow-color);
  margin-bottom: 2rem;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
  border-bottom: 1px solid var(--border-color);
  position: relative;
  z-index: 100;
}

.header-left, .header-right {
  display: flex;
  align-items: center;
  height: 60px;
}

.logo {
  display: flex;
  align-items: center;
  height: 60px;
  padding: 5px 0;
}

.logo img {
  max-height: 100%;
  max-width: 100%;
  object-fit: contain;
  transition: opacity 0.3s ease;
}

/* Show/hide logo based on theme */
.logo-light {
  display: block;
  opacity: 1;
}

.logo-dark {
  display: none;
  opacity: 0;
}

[data-theme="dark"] .logo-light {
  display: none;
  opacity: 0;
}

[data-theme="dark"] .logo-dark {
  display: block;
  opacity: 1;
}

.user-info {
  display: flex;
  align-items: center;
  margin-right: 1.5rem;
  background-color: rgba(0, 0, 128, 0.08);
  padding: 0.5rem 1rem;
  border-radius: 50px;
  transition: all 0.3s ease;
}

[data-theme="dark"] .user-info {
  background-color: rgba(65, 105, 225, 0.15);
}

.user-info:hover {
  background-color: rgba(0, 0, 128, 0.12);
}

[data-theme="dark"] .user-info:hover {
  background-color: rgba(65, 105, 225, 0.25);
}

.user-info i {
  font-size: 1.5rem;
  margin-right: 0.8rem;
  color: var(--primary-color);
  transition: color 0.3s ease;
}

.user-name {
  font-weight: 600;
  color: var(--text-color);
  transition: color 0.3s ease;
}

.theme-toggle {
  margin-left: 1rem;
  position: relative;
}

.theme-btn {
  background-color: var(--paper-color);
  border: 2px solid var(--primary-color);
  font-size: 1.2rem;
  color: var(--primary-color);
  cursor: pointer;
  padding: 0.6rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px var(--shadow-color);
}

[data-theme="dark"] .theme-btn {
  color: var(--warning-color);
  border-color: var(--secondary-color);
  background-color: var(--hover-color);
}

.theme-btn:hover {
  background-color: var(--hover-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

[data-theme="dark"] .theme-btn:hover {
  background-color: rgba(255, 183, 77, 0.15);
  color: var(--warning-color);
}

.container {
  max-width: 1500px;
  margin: 0 auto;
  padding: 40px 20px;
}

.clean-container {
  background-color: var(--paper-color);
  border-radius: 8px;
  box-shadow: 0 4px 12px var(--shadow-color);
  padding: 30px;
  margin-bottom: 30px;
  position: relative;
  overflow: hidden;
  transition: background-color 0.3s ease, box-shadow 0.3s ease;
}

.clean-header {
  background: var(--primary-color);
  color: var(--paper-color);
  margin: -30px -30px 30px -30px;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 3px solid var(--secondary-color);
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.clean-header h1 {
  font-family: 'Varela Round', 'Quicksand', sans-serif;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
}

.breadcrumb {
  font-family: 'Nunito', sans-serif;
  color: var(--primary-color);
  margin-bottom: 25px;
  font-size: 1rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: color 0.3s ease;
}

.breadcrumb i {
  font-size: 0.9rem;
  opacity: 0.7;
}

.btn-back {
  position: fixed;
  top: 102px;
  left: 20px;
  width: 40px;
  height: 40px;
  background: var(--paper-color);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  text-decoration: none;
  font-size: 1rem;
  transition: all 0.3s ease;
  z-index: 1000;
  box-shadow: 0 2px 6px var(--shadow-color);
}

.btn-back:hover {
  transform: translateY(-2px);
  background: var(--primary-color);
  color: white;
  box-shadow: 0 4px 8px var(--shadow-color);
}

.header-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.header-info {
  color: var(--text-color);
  opacity: 0.8;
  font-size: 0.95rem;
  font-style: italic;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: color 0.3s ease;
}

.header-info i {
  color: var(--secondary-color);
  transition: color 0.3s ease;
}

.btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-family: 'Nunito', 'Quicksand', sans-serif;
  font-size: 0.95rem;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 2px 4px var(--shadow-color);
}

.btn:hover {
  background: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color);
}

.btn-secondary {
  background: var(--paper-color);
  color: var(--primary-color);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background: var(--hover-color);
  color: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-success {
  background: var(--success-color);
}

.btn-success:hover {
  background: #43a047;
}

[data-theme="dark"] .btn-success:hover {
  background: #66bb6a;
}

.btn-warning {
  background: var(--warning-color);
  color: #fff;
}

.btn-warning:hover {
  background: #ec971f;
}

[data-theme="dark"] .btn-warning:hover {
  background: #ffc107;
}

.btn:disabled {
  background: #b0bec5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

[data-theme="dark"] .btn:disabled {
  background: #546e7a;
}

.topics-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.topic-item {
  display: flex;
  align-items: center;
  padding: 20px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  margin-bottom: 15px;
  background: var(--paper-color);
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px var(--shadow-color);
}

.topic-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px var(--shadow-color);
  border-color: var(--secondary-color);
}

.topic-info {
  flex-grow: 1;
}

.topic-title {
  font-family: 'Varela Round', 'Quicksand', sans-serif;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-color);
  margin: 0 0 8px 0;
  transition: color 0.3s ease;
}

.topic-description {
  color: var(--text-color);
  opacity: 0.8;
  font-size: 0.95rem;
  margin: 0 0 10px 0;
  line-height: 1.5;
  transition: color 0.3s ease;
}

.topic-stats {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--secondary-color);
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.topic-stats i {
  opacity: 0.8;
}

.topic-actions {
  display: flex;
  gap: 10px;
  align-items: center;
  flex-wrap: wrap;
}

.order-buttons {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-right: 15px;
}

.order-btn {
  background: none;
  border: none;
  color: var(--secondary-color);
  cursor: pointer;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  outline: none;
}

.order-btn:hover:not(:disabled) {
  background-color: var(--hover-color);
  color: var(--primary-color);
}

.order-btn:disabled {
  color: var(--border-color);
  cursor: not-allowed;
}

.empty-state {
  text-align: center;
  padding: 60px 40px;
  background: var(--paper-color);
  border-radius: 8px;
  border: 1px dashed var(--border-color);
  margin-top: 20px;
  transition: background-color 0.3s ease, border-color 0.3s ease;
}

.empty-state i {
  font-size: 3rem;
  color: var(--secondary-color);
  opacity: 0.7;
  margin-bottom: 25px;
  transition: color 0.3s ease;
}

.empty-state p {
  font-size: 1.1rem;
  color: var(--text-color);
  margin-bottom: 25px;
  transition: color 0.3s ease;
}

.alert {
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: background-color 0.3s ease, color 0.3s ease, border-left-color 0.3s ease;
}

.alert-danger {
  background-color: rgba(183, 28, 28, 0.1);
  color: var(--error-color);
  border-left: 4px solid var(--error-color);
}

[data-theme="dark"] .alert-danger {
  background-color: rgba(244, 67, 54, 0.15);
}

.alert-success {
  background-color: rgba(46, 125, 50, 0.1);
  color: var(--success-color);
  border-left: 4px solid var(--success-color);
}

[data-theme="dark"] .alert-success {
  background-color: rgba(76, 175, 80, 0.15);
}

/* Loading spinner */
.loader {
  border: 4px solid rgba(65, 105, 225, 0.2);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
  margin: 30px auto;
  transition: border-color 0.3s ease;
}

[data-theme="dark"] .loader {
  border-color: rgba(108, 146, 255, 0.2);
  border-top-color: var(--primary-color);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Transições Vue */
.fade-enter-active, .fade-leave-active {
  transition: opacity .5s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}

.slide-enter-active, .slide-leave-active {
  transition: all 0.3s;
}
.slide-enter, .slide-leave-to {
  transform: translateY(20px);
  opacity: 0;
}

.list-move {
  transition: transform 0.5s;
}

@media (max-width: 768px) {
  .container {
    padding: 20px 15px;
    padding-top: 60px;
  }
  
  .header {
    padding: 0.8rem 1rem;
  }
  
  .logo img {
    max-height: 40px;
  }
  
  .user-name {
    font-size: 0.9rem;
  }
  
  .clean-header {
    padding: 15px 20px;
  }
  
  .header-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .topic-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
    padding: 15px;
  }
  
  .topic-actions {
    width: 100%;
    justify-content: center;
    gap: 8px;
  }
  
  .order-buttons {
    flex-direction: row;
    margin-right: 0;
    margin-bottom: 10px;
    width: 100%;
    justify-content: center;
  }
  
  .btn-back {
    top: 10px;
    left: 10px;
    width: 36px;
    height: 36px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .topic-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
  
  .clean-container {
    padding: 20px;
  }
  
  .clean-header {
    margin: -20px -20px 20px -20px;
  }
}