<?php
include 'conexao_POST.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = trim($_POST['email'] ?? '');
    
    // Sanitizar entrada
    $email = filter_var($email, FILTER_SANITIZE_EMAIL);
    
    // Verificar se email existe
    $sql = "SELECT email FROM usuario WHERE email = $1";
    $result = pg_query_params($conexao, $sql, array($email));
    
    $disponivel = pg_num_rows($result) === 0;
    
    echo json_encode(['disponivel' => $disponivel]);
} else {
    http_response_code(405);
    echo json_encode(['erro' => 'Método não permitido']);
}