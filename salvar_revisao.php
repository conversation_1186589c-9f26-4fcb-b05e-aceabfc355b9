<?php
// Iniciar a sessão
session_start();

// Definir o fuso horário para Brasil (São Paulo)
date_default_timezone_set('America/Sao_Paulo');

include_once("conexao_POST.php");

if (!$conexao) {
    echo "Erro na conexão: " . pg_last_error();
    exit;
}

$id_usuario = $_SESSION['idusuario'];

// Verifica se o formulário foi submetido
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['idestudos'])) {
    // Obtém o ID do estudo a ser revisado
    $id_estudo = intval($_POST['idestudos']);

    // Data e hora atual para o timestamp com fuso horário explícito
    $revisao_concluida = date('Y-m-d H:i:s');

    // Log para debug
    error_log("Salvando revisão - ID Estudo: $id_estudo, ID Usuário: $id_usuario, Data/Hora: $revisao_concluida");

    // Prepara a query SQL com placeholders para segurança e clareza
    $query_atualiza_revisao = "
        UPDATE appEstudo.estudos
        SET revisoes = CURRENT_DATE, 
            revisao_concluida = $1
        WHERE idestudos = $2
        AND planejamento_usuario_idusuario = $3
        AND (revisoes IS NULL OR revisoes != CURRENT_DATE);
    ";

    // Prepara a query para execução
    $stmt = pg_prepare($conexao, "update_revisao", $query_atualiza_revisao);

    if ($stmt) {
        // Executa a query com os parâmetros seguros
        $resultado_atualiza_revisao = pg_execute($conexao, "update_revisao", array($revisao_concluida, $id_estudo, $id_usuario));

        if ($resultado_atualiza_revisao) {
            $rows_affected = pg_affected_rows($resultado_atualiza_revisao);
            if ($rows_affected > 0) {
                echo "Revisão Concluída!";
            } else {
                echo "Nenhuma alteração necessária. Revisão já registrada para hoje.";
            }
        } else {
            echo "Erro ao salvar revisão do Estudo: " . pg_last_error($conexao);
        }
    } else {
        echo "Erro ao preparar a query: " . pg_last_error($conexao);
    }
} else {
    echo "Nenhum Estudo selecionado para revisão.";
}

// Botão de voltar (apenas se não for uma requisição AJAX)
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || $_SERVER['HTTP_X_REQUESTED_WITH'] != 'XMLHttpRequest') {
    echo '<br><button onclick="window.location.href=\'revisao.php\'" class="btn btn-dark btn-sm btn-gold">Voltar</button>';
}

