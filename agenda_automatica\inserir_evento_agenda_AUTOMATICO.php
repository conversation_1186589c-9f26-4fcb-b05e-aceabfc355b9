<?php
// Início do arquivo
session_start();
include '../conexao_POST.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['id_usuario'])) {
    header('Location: login.php');
    exit();
}

// Obter dados do formulário
$id_usuario = $_SESSION['id_usuario'];
$tipo_evento = $_POST['tipo_evento'];
$start_date = $_POST['start_date'];
$end_date = $_POST['end_date'];
$materias_per_day = isset($_POST['materias_per_day']) ? intval($_POST['materias_per_day']) : 1;
$materias_selecionadas = isset($_POST['materias_selecionadas']) ? $_POST['materias_selecionadas'] : [];
$usar_config_avancada = isset($_POST['usar_config_avancada']);

// Converter datas para objetos DateTime para manipulação
$start_date_obj = new DateTime($start_date);
$end_date_obj = new DateTime($end_date);
$end_date_obj->setTime(23, 59, 59); // Definir para o final do dia

// Array para armazenar os eventos gerados
$eventos_gerados = [];

// Determinar os dias da semana para estudo
if ($usar_config_avancada) {
    // Configuração avançada - usar dias ativos com matérias específicas
    $dias_ativos = isset($_POST['dias_ativos']) ? $_POST['dias_ativos'] : [];
    $materias_por_dia = isset($_POST['materias_dia']) ? $_POST['materias_dia'] : [];
    
    // Percorrer o intervalo de datas
    $current_date = clone $start_date_obj;
    
    while ($current_date <= $end_date_obj) {
        $dia_semana = $current_date->format('w'); // 0 (domingo) a 6 (sábado)
        
        // Verificar se este dia da semana está ativo
        if (in_array($dia_semana, $dias_ativos)) {
            // Obter matérias para este dia
            $materias_dia = isset($materias_por_dia[$dia_semana]) ? $materias_por_dia[$dia_semana] : [];
            
            // Criar eventos para cada matéria deste dia
            foreach ($materias_dia as $materia) {
                $eventos_gerados[] = [
                    'data' => $current_date->format('Y-m-d'),
                    'materia' => $materia
                ];
            }
        }
        
        // Avançar para o próximo dia
        $current_date->modify('+1 day');
    }
} else {
    // Configuração básica - usar dias da semana selecionados
    $dias_semana = isset($_POST['dias_semana']) ? $_POST['dias_semana'] : [];
    
    // Percorrer o intervalo de datas
    $current_date = clone $start_date_obj;
    
    while ($current_date <= $end_date_obj) {
        $dia_semana = $current_date->format('w'); // 0 (domingo) a 6 (sábado)
        
        // Verificar se este dia da semana está selecionado
        if (in_array($dia_semana, $dias_semana)) {
            // Selecionar aleatoriamente as matérias para este dia
            $materias_dia = [];
            $materias_disponiveis = $materias_selecionadas;
            
            // Limitar ao número de matérias por dia ou ao total disponível
            $num_materias = min($materias_per_day, count($materias_disponiveis));
            
            for ($i = 0; $i < $num_materias; $i++) {
                if (empty($materias_disponiveis)) break;
                
                // Selecionar uma matéria aleatoriamente
                $index = array_rand($materias_disponiveis);
                $materia = $materias_disponiveis[$index];
                $materias_dia[] = $materia;
                
                // Remover a matéria selecionada para não repetir no mesmo dia
                unset($materias_disponiveis[$index]);
                $materias_disponiveis = array_values($materias_disponiveis);
            }
            
            // Criar eventos para cada matéria deste dia
            foreach ($materias_dia as $materia) {
                $eventos_gerados[] = [
                    'data' => $current_date->format('Y-m-d'),
                    'materia' => $materia
                ];
            }
        }
        
        // Avançar para o próximo dia
        $current_date->modify('+1 day');
    }
}

// Inserir eventos no banco de dados
$eventos_inseridos = 0;

foreach ($eventos_gerados as $evento) {
    // Obter informações da matéria
    $query_materia = "SELECT m.idmateria, m.cor 
                     FROM appEstudo.materia m 
                     WHERE m.nome = $1 
                     AND m.usuario_id = $2";
    $result_materia = pg_query_params($conexao, $query_materia, array($evento['materia'], $id_usuario));
    
    if ($row_materia = pg_fetch_assoc($result_materia)) {
        $id_materia = $row_materia['idmateria'];
        $cor_materia = $row_materia['cor'];
        
        // Inserir evento
        $query_inserir = "INSERT INTO appEstudo.eventos_agenda 
                         (usuario_id, tipo, titulo, data_inicio, data_fim, cor, materia_id, materia_nome) 
                         VALUES ($1, $2, $3, $4, $5, $6, $7, $8)";
        
        $data_evento = $evento['data'];
        $titulo_evento = $evento['materia'];
        
        $result_inserir = pg_query_params($conexao, $query_inserir, array(
            $id_usuario,
            $tipo_evento,
            $titulo_evento,
            $data_evento . ' 08:00:00', // Horário padrão de início
            $data_evento . ' 12:00:00', // Horário padrão de fim
            $cor_materia,
            $id_materia,
            $evento['materia']
        ));
        
        if ($result_inserir) {
            $eventos_inseridos++;
        }
    }
}

// Redirecionar de volta para o calendário com mensagem de sucesso
$_SESSION['mensagem'] = "Foram criados $eventos_inseridos eventos de estudo no seu calendário.";
header('Location: calendario_agenda.php');
exit();
?>
