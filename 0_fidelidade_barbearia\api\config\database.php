<?php
/**
 * Configuração de conexão com o banco de dados MySQL
 * Sistema de Fidelidade da Barbearia
 */

class Database {
    // Configurações do banco de dados
    private $host = 'barbeiro_br.mysql.dbaas.com.br';
    private $db_name = 'barbeiro_br';
    private $username = 'barbeiro_br';
    private $password = 'Lucasb90#';
    private $charset = 'utf8mb4';
    
    public $conn;

    /**
     * Conectar ao banco de dados
     */
    public function getConnection() {
        $this->conn = null;

        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ];

            $this->conn = new PDO($dsn, $this->username, $this->password, $options);
            
        } catch(PDOException $exception) {
            error_log("Connection error: " . $exception->getMessage());
            throw new Exception("Erro de conexão com o banco de dados");
        }

        return $this->conn;
    }

    /**
     * Executar uma função do MySQL
     */
    public function executeFunction($functionName, $params = []) {
        try {
            // Se não há parâmetros, chamar função sem placeholders
            if (empty($params)) {
                $sql = "SELECT $functionName() as result";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute();
            } else {
                // Se há parâmetros, criar placeholders
                $placeholders = str_repeat('?,', count($params) - 1) . '?';
                $sql = "SELECT $functionName($placeholders) as result";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute(array_values($params));
            }

            $result = $stmt->fetch();
            return $result['result'];

        } catch(PDOException $e) {
            error_log("Function execution error: " . $e->getMessage());
            throw new Exception("Erro ao executar função: " . $e->getMessage());
        }
    }

    /**
     * Executar uma procedure do MySQL
     */
    public function executeProcedure($procedureName, $params = []) {
        try {
            $placeholders = str_repeat('?,', count($params) - 1) . '?';
            $sql = "CALL $procedureName($placeholders)";
            
            $stmt = $this->conn->prepare($sql);
            $stmt->execute(array_values($params));
            
            return $stmt->fetchAll();
            
        } catch(PDOException $e) {
            error_log("Procedure execution error: " . $e->getMessage());
            throw new Exception("Erro ao executar procedure: " . $e->getMessage());
        }
    }

    /**
     * Fechar conexão
     */
    public function closeConnection() {
        $this->conn = null;
    }
}
?>
