<?php
include_once("../session_config.php");
include_once("../assets/config.php");

if (!verificarSessaoValida()) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Usuário não autenticado']);
    exit();
}

// Obter parâmetros de filtro
$categoria = isset($_GET['categoria']) ? intval($_GET['categoria']) : null;
$periodo = isset($_GET['periodo']) ? intval($_GET['periodo']) : null;
$ordem = isset($_GET['ordem']) ? $_GET['ordem'] : 'recentes';

// Construir a query base
$query_base = "
    SELECT 
        t.id,
        t.titulo,
        t.conteudo,
        t.created_at,
        t.views,
        u.nome as autor_nome,
        c.nome as categoria_nome,
        c.id as categoria_id,
        (SELECT COUNT(*) FROM appestudo.forum_respostas r WHERE r.topico_id = t.id AND r.status = true) as total_respostas,
        (SELECT MAX(created_at) FROM appestudo.forum_respostas r WHERE r.topico_id = t.id AND r.status = true) as ultima_resposta
    FROM 
        appestudo.forum_topicos t
    JOIN 
        appestudo.usuario u ON t.usuario_id = u.idusuario
    JOIN 
        appestudo.forum_categorias c ON t.categoria_id = c.id
    WHERE 
        t.status = true";

// Adicionar filtros
$params = [];
$where_conditions = [];

if ($categoria) {
    $where_conditions[] = "t.categoria_id = $" . (count($params) + 1);
    $params[] = $categoria;
}

if ($periodo) {
    $where_conditions[] = "t.created_at >= NOW() - INTERVAL '" . $periodo . " days'";
}

if (!empty($where_conditions)) {
    $query_base .= " AND " . implode(" AND ", $where_conditions);
}

// Adicionar ordenação
switch ($ordem) {
    case 'populares':
        $query_base .= " ORDER BY total_respostas DESC, t.views DESC";
        break;
    case 'emalta':
        $query_base .= " ORDER BY t.views DESC, t.created_at DESC";
        break;
    case 'recentes':
    default:
        $query_base .= " ORDER BY t.created_at DESC";
        break;
}

// Limitar resultados
$query_base .= " LIMIT 10";

// Executar a query
$result = pg_query_params($conexao, $query_base, $params);

// Preparar o array de resposta
$topicos = [];

if ($result && pg_num_rows($result) > 0) {
    while ($topico = pg_fetch_assoc($result)) {
        // Extrair um resumo do conteúdo
        $resumo = strip_tags($topico['conteudo']);
        $resumo = (strlen($resumo) > 150) ? substr($resumo, 0, 150) . '...' : $resumo;
        
        // Calcular tempo desde a postagem
        $data_postagem = strtotime($topico['created_at']);
        $agora = time();
        $diferenca = $agora - $data_postagem;
        
        if ($diferenca < 60) {
            $tempo = "agora mesmo";
        } elseif ($diferenca < 3600) {
            $minutos = floor($diferenca / 60);
            $tempo = $minutos . " minuto" . ($minutos > 1 ? "s" : "") . " atrás";
        } elseif ($diferenca < 86400) {
            $horas = floor($diferenca / 3600);
            $tempo = $horas . " hora" . ($horas > 1 ? "s" : "") . " atrás";
        } else {
            $dias = floor($diferenca / 86400);
            if ($dias < 30) {
                $tempo = $dias . " dia" . ($dias > 1 ? "s" : "") . " atrás";
            } else {
                $tempo = date('d/m/Y', $data_postagem);
            }
        }

        $topicos[] = [
            'id' => $topico['id'],
            'titulo' => htmlspecialchars($topico['titulo']),
            'resumo' => htmlspecialchars($resumo),
            'categoria_id' => $topico['categoria_id'],
            'categoria_nome' => $topico['categoria_nome'],
            'autor_nome' => $topico['autor_nome'],
            'tempo' => $tempo,
            'total_respostas' => $topico['total_respostas'],
            'views' => $topico['views']
        ];
    }
}

// Retornar resposta em JSON
header('Content-Type: application/json');
echo json_encode(['topicos' => $topicos]);
?> 