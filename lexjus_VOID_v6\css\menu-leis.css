/* Estilos para o Menu de Seleção de Leis */

/* Overlay do menu */
.menu-leis-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.menu-leis-overlay.ativo {
    opacity: 1;
    visibility: visible;
}

/* Container principal do menu */
.menu-leis-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 40px;
    max-width: 900px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
    transform: scale(0.8) translateY(50px);
    transition: all 0.3s ease;
    position: relative;
}

.menu-leis-overlay.ativo .menu-leis-container {
    transform: scale(1) translateY(0);
}

/* Cabeçalho do menu */
.menu-leis-header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

.menu-leis-header h2 {
    font-size: 2.5rem;
    margin: 0 0 10px 0;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.menu-leis-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

/* Botão de fechar */
.menu-leis-fechar {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-leis-fechar:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

/* Grid de leis */
.leis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* Card de cada lei */
.lei-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 3px solid transparent;
}

.lei-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.lei-card.selecionada {
    border-color: #fff;
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

/* Indicador de lei atual */
.lei-card.atual::before {
    content: "ATUAL";
    position: absolute;
    top: 10px;
    right: 10px;
    background: #27ae60;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: bold;
}

/* Cabeçalho do card */
.lei-card-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.lei-icone {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-right: 15px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.lei-info h3 {
    margin: 0 0 5px 0;
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
}

.lei-info p {
    margin: 0;
    font-size: 0.9rem;
    color: #7f8c8d;
    font-weight: 500;
}

/* Descrição da lei */
.lei-descricao {
    color: #34495e;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 15px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Estatísticas da lei */
.lei-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-bottom: 15px;
}

.lei-stat {
    text-align: center;
    padding: 8px;
    background: #f8f9fa;
    border-radius: 8px;
}

.lei-stat-numero {
    display: block;
    font-size: 1.2rem;
    font-weight: bold;
    color: #2c3e50;
}

.lei-stat-label {
    font-size: 0.7rem;
    color: #7f8c8d;
    text-transform: uppercase;
    font-weight: 600;
}

/* Barra de progresso */
.lei-progresso {
    margin-bottom: 10px;
}

.lei-progresso-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 0.8rem;
    color: #7f8c8d;
}

.lei-progresso-barra {
    height: 6px;
    background: #ecf0f1;
    border-radius: 3px;
    overflow: hidden;
}

.lei-progresso-preenchimento {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* Botão de ação */
.lei-botao {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    font-size: 0.9rem;
}

.lei-botao.estudar {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
}

.lei-botao.continuar {
    background: linear-gradient(135deg, #27ae60, #229954);
    color: white;
}

.lei-botao:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Loading state */
.menu-leis-loading {
    text-align: center;
    color: white;
    padding: 40px;
}

.menu-leis-loading i {
    font-size: 3rem;
    margin-bottom: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Responsividade */
@media (max-width: 768px) {
    .menu-leis-container {
        padding: 20px;
        margin: 20px;
        width: calc(100% - 40px);
    }
    
    .menu-leis-header h2 {
        font-size: 2rem;
    }
    
    .leis-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .lei-card {
        padding: 20px;
    }
    
    .lei-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .menu-leis-header h2 {
        font-size: 1.5rem;
    }
    
    .lei-card-header {
        flex-direction: column;
        text-align: center;
    }
    
    .lei-icone {
        margin-right: 0;
        margin-bottom: 10px;
    }
    
    .lei-stats {
        grid-template-columns: 1fr;
    }
}

/* Animações de entrada */
.lei-card {
    animation: slideInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(30px);
}

.lei-card:nth-child(1) { animation-delay: 0.1s; }
.lei-card:nth-child(2) { animation-delay: 0.2s; }
.lei-card:nth-child(3) { animation-delay: 0.3s; }
.lei-card:nth-child(4) { animation-delay: 0.4s; }
.lei-card:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Botão Voltar ao Menu */
.btn-voltar-menu {
    background: var(--primary);
    color: white !important;
    border: 2px solid transparent !important;
    transition: all 0.3s ease !important;
}

.btn-voltar-menu:hover {
    background: var(--primary);
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 15px var(--primary);
}

.btn-voltar-menu .btn-text {
    font-weight: 600 !important;
}

/* Tema escuro */
.tema-escuro .lei-card {
    background: rgba(44, 62, 80, 0.95);
    color: #ecf0f1;
}

.tema-escuro .lei-info h3 {
    color: #ecf0f1;
}

.tema-escuro .lei-descricao {
    color: #bdc3c7;
}

.tema-escuro .lei-stat {
    background: rgba(52, 73, 94, 0.8);
}

.tema-escuro .lei-stat-numero {
    color: #ecf0f1;
}

.tema-escuro .lei-progresso-barra {
    background: rgba(52, 73, 94, 0.8);
}
