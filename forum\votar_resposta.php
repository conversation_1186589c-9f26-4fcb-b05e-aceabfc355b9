<?php
include_once("../session_config.php");
include_once("../assets/config.php");

if (!verificarSessaoValida()) {
    echo json_encode(['success' => false, 'message' => 'Usuário não autenticado']);
    exit;
}

// Recebe e valida os dados
$data = json_decode(file_get_contents('php://input'), true);
$resposta_id = (int)$data['resposta_id'];
$tipo_voto = (int)$data['tipo_voto'];

if (!in_array($tipo_voto, [1, -1])) {
    echo json_encode(['success' => false, 'message' => 'Tipo de voto inválido']);
    exit;
}

try {
    // Inicia uma transação
    pg_query($conexao, "BEGIN");

    // Verifica se já existe um voto deste usuário para esta resposta
    $query_check = "SELECT tipo_voto FROM appestudo.forum_votos 
                   WHERE usuario_id = $1 AND resposta_id = $2";
    $result_check = pg_query_params($conexao, $query_check, 
        array($_SESSION['idusuario'], $resposta_id));
    $voto_existente = pg_fetch_assoc($result_check);

    if ($voto_existente) {
        if ($voto_existente['tipo_voto'] == $tipo_voto) {
            // Remove o voto se clicar novamente no mesmo botão
            $query = "DELETE FROM appestudo.forum_votos 
                     WHERE usuario_id = $1 AND resposta_id = $2";
            pg_query_params($conexao, $query, 
                array($_SESSION['idusuario'], $resposta_id));
        } else {
            // Atualiza o voto existente
            $query = "UPDATE appestudo.forum_votos 
                     SET tipo_voto = $1 
                     WHERE usuario_id = $2 AND resposta_id = $3";
            pg_query_params($conexao, $query, 
                array($tipo_voto, $_SESSION['idusuario'], $resposta_id));
        }
    } else {
        // Insere novo voto
        $query = "INSERT INTO appestudo.forum_votos 
                 (usuario_id, resposta_id, tipo_voto) 
                 VALUES ($1, $2, $3)";
        pg_query_params($conexao, $query, 
            array($_SESSION['idusuario'], $resposta_id, $tipo_voto));
    }

    // Busca os novos totais de votos
    $query_totais = "SELECT 
        (SELECT COUNT(*) FROM appestudo.forum_votos 
         WHERE resposta_id = $1 AND tipo_voto = 1) as upvotes,
        (SELECT COUNT(*) FROM appestudo.forum_votos 
         WHERE resposta_id = $1 AND tipo_voto = -1) as downvotes";
    
    $result_totais = pg_query_params($conexao, $query_totais, array($resposta_id));
    $totais = pg_fetch_assoc($result_totais);

    pg_query($conexao, "COMMIT");

    echo json_encode([
        'success' => true,
        'upvotes' => $totais['upvotes'],
        'downvotes' => $totais['downvotes']
    ]);

} catch (Exception $e) {
    pg_query($conexao, "ROLLBACK");
    echo json_encode(['success' => false, 'message' => 'Erro ao processar voto']);
}
?>
