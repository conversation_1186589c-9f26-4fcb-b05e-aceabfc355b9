<?php
session_start();
include_once("assets/config.php");

// Verificar se o usuário está logado
if (!isset($_SESSION['idusuario'])) {
    header("Location: login.php");
    exit();
}

$usuario_id = $_SESSION['idusuario'];
$conteudos_marcados = isset($_POST['conteudos']) ? $_POST['conteudos'] : [];

// Obter todos os conteúdos previamente associados ao usuário
$query_all_conteudos = "
    SELECT conteudo_id, status 
    FROM appestudo.usuario_conteudo 
    WHERE usuario_id = $usuario_id
";
$result_all_conteudos = pg_query($conexao, $query_all_conteudos);
$conteudos_atuais = [];

while ($row = pg_fetch_assoc($result_all_conteudos)) {
    $conteudos_atuais[$row['conteudo_id']] = $row['status'];
}

// Atualizar status para true nos conteúdos marcados
foreach ($conteudos_marcados as $conteudo_id) {
    if (array_key_exists($conteudo_id, $conteudos_atuais)) {
        $query_update_true = "
            UPDATE appestudo.usuario_conteudo 
            SET status = true 
            WHERE usuario_id = $1 AND conteudo_id = $2
        ";
        $result_update_true = pg_query_params($conexao, $query_update_true, [$usuario_id, $conteudo_id]);
    } else {
        $query_insert = "
            INSERT INTO appestudo.usuario_conteudo (usuario_id, conteudo_id, status) 
            VALUES ($1, $2, true)
        ";
        $result_insert = pg_query_params($conexao, $query_insert, [$usuario_id, $conteudo_id]);
    }
}

// Atualizar status para false nos conteúdos desmarcados
foreach ($conteudos_atuais as $conteudo_id => $status) {
    if (!in_array($conteudo_id, $conteudos_marcados) && $status == true) {
        $query_update_false = "
            UPDATE appestudo.usuario_conteudo 
            SET status = false 
            WHERE usuario_id = $1 AND conteudo_id = $2
        ";
        $result_update_false = pg_query_params($conexao, $query_update_false, [$usuario_id, $conteudo_id]);
    }
}
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sucesso - Atualização de Conteúdos</title>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Old+Standard+TT:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #D2691E;
            --background-color: #F5E6D3;
            --paper-color: #FFF8DC;
            --text-color: #2C1810;
            --border-color: #8B4513;
            --success-color: #2D5A27;
        }

        body {
            background-color: var(--background-color);
            background-image:
                    linear-gradient(rgba(245, 230, 211, 0.9), rgba(245, 230, 211, 0.9)),
                    url("data:image/svg+xml,%3Csvg width='52' height='26' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%238b4513' fill-opacity='0.1'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            font-family: 'Old Standard TT', serif;
            position: relative;
        }

        .btn-voltar {
            position: fixed;
            top: 20px;
            left: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: var(--paper-color);
            border: 2px solid var(--border-color);
            border-radius: 50%;
            box-shadow: 3px 3px 0 var(--border-color);
            color: var(--primary-color);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .btn-voltar::before {
            content: '';
            position: absolute;
            width: calc(100% + 4px);
            height: calc(100% + 4px);
            border: 1px solid var(--border-color);
            border-radius: 50%;
            opacity: 0.5;
            transform: scale(0.9);
            transition: all 0.3s ease;
        }

        .btn-voltar:hover {
            transform: translateY(-2px);
            box-shadow: 4px 4px 0 var(--border-color);
            background: var(--background-color);
        }

        .btn-voltar:hover::before {
            transform: scale(1.1);
            opacity: 0;
        }

        .mensagem-sucesso {
            background: var(--paper-color);
            padding: 3rem;
            border: 2px solid var(--border-color);
            box-shadow: 8px 8px 0 var(--border-color);
            text-align: center;
            position: relative;
            max-width: 500px;
            width: 90%;
            animation: aparecer 0.8s ease forwards;
        }

        .mensagem-sucesso::before,
        .mensagem-sucesso::after {
            content: '';
            position: absolute;
            width: calc(100% + 20px);
            height: calc(100% + 20px);
            border: 1px solid var(--border-color);
            opacity: 0.3;
            z-index: -1;
        }

        .mensagem-sucesso::before {
            top: -10px;
            left: -10px;
        }

        .mensagem-sucesso::after {
            bottom: -10px;
            right: -10px;
        }

        .ornamento {
            position: absolute;
            width: 50px;
            height: 50px;
            border: 2px solid var(--border-color);
            opacity: 0.5;
        }

        .ornamento-tl { top: -25px; left: -25px; border-right: none; border-bottom: none; }
        .ornamento-tr { top: -25px; right: -25px; border-left: none; border-bottom: none; }
        .ornamento-bl { bottom: -25px; left: -25px; border-right: none; border-top: none; }
        .ornamento-br { bottom: -25px; right: -25px; border-left: none; border-top: none; }

        .icone-sucesso {
            font-size: 4rem;
            color: var(--success-color);
            margin-bottom: 1.5rem;
            animation: pulsar 2s infinite;
        }

        h2 {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            font-size: 2.5rem;
            margin: 0 0 1rem 0;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        p {
            color: var(--text-color);
            font-size: 1.2rem;
            margin: 0.5rem 0;
            line-height: 1.6;
        }

        .separador {
            display: block;
            width: 150px;
            height: 2px;
            background: var(--border-color);
            margin: 1.5rem auto;
            position: relative;
        }

        .separador::before,
        .separador::after {
            content: '❖';
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            color: var(--border-color);
            font-size: 1rem;
        }

        .separador::before { left: -20px; }
        .separador::after { right: -20px; }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(44, 24, 16, 0.8);
            backdrop-filter: blur(4px);
            z-index: -1;
        }

        @keyframes aparecer {
            0% {
                opacity: 0;
                transform: translateY(-20px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulsar {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        @media (max-width: 768px) {
            .mensagem-sucesso {
                padding: 2rem;
                margin: 1rem;
            }

            h2 {
                font-size: 2rem;
            }

            .icone-sucesso {
                font-size: 3rem;
            }

            .btn-voltar {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
<a href="exibir_materias.php" class="btn-voltar">
    <i class="fas fa-arrow-left"></i>
</a>

<div class="overlay"></div>
<div class="mensagem-sucesso">
    <div class="ornamento ornamento-tl"></div>
    <div class="ornamento ornamento-tr"></div>
    <div class="ornamento ornamento-bl"></div>
    <div class="ornamento ornamento-br"></div>

    <div class="icone-sucesso">
        <i class="fas fa-check-circle"></i>
    </div>

    <h2>Sucesso!</h2>
    <div class="separador"></div>
    <p>Seus conteúdos foram</p>
    <p>atualizados com sucesso!</p>
    <div class="separador"></div>
    <p style="font-style: italic;">Redirecionando...</p>
</div>

<script>
    setTimeout(() => {
        window.location.href = 'exibir_materias.php';
    }, 2000);
</script>
</body>
</html>