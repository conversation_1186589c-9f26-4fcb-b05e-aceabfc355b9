<?php
include 'conexao_POST.php';
include 'processa_index.php';
include_once 'funcoes.php'; // Inclua as funções aqui

// Verifica se $id_planejamento e $id_usuario estão definidos
if (!isset($id_planejamento) || !isset($id_usuario)) {
    die("ID do planejamento ou ID do usuário não definido.");
}

// Consultar os IDs e nomes das matérias associadas ao planejamento
$query_consultar_materias_planejamento = "
    SELECT pm.materia_idmateria, m.nome AS nome_materia, m.cor AS cor_materia
    FROM appEstudo.planejamento_materia pm
    INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    WHERE pm.planejamento_idplanejamento = $id_planejamento";

$resultado_materias_planejamento = pg_query($conexao, $query_consultar_materias_planejamento);

// Inicializa arrays para armazenar os dados das matérias
$materias_no_planejamento_Grafico = array();
$cores_materias = array();

// Preenche os arrays com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_materias_planejamento)) {
    $materia_id = $row['materia_idmateria'];
    $nome_materia = $row['nome_materia'];
    $cor_materia = $row['cor_materia'];

    $materias_no_planejamento_Grafico[$materia_id] = $nome_materia;
    $cores_materias[$nome_materia] = $cor_materia;
}

// Obtém a data atual
$data_atual = date('Y-m-d');

// Realiza a consulta ao banco de dados para obter os dados de estudo por ponto estudado de cada matéria no planejamento
$query_consulta_pontos_estudo = "
    SELECT m.nome AS nome_materia, 
           e.ponto_estudado AS ponto_estudado,
           SUM(e.tempo_liquido) AS tempo_estudo
    FROM appEstudo.estudos e
    INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
    INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
    LEFT JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria
    WHERE u.idusuario = $id_usuario 
      AND e.materia_idmateria IN (" . implode(',', array_keys($materias_no_planejamento_Grafico)) . ")
      AND DATE(e.data) = '$data_atual'
    GROUP BY m.nome, e.ponto_estudado";

$resultado_consulta_pontos_estudo = pg_query($conexao, $query_consulta_pontos_estudo);

// Verifica se houve algum estudo
$houve_estudo = pg_num_rows($resultado_consulta_pontos_estudo) > 0;

// Inicializa um array para armazenar os dados de tempo de estudo por ponto estudado para cada matéria
$pontos_estudo_por_materia = array();
$tempo_total_por_materia = array();

// Preenche o array com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_consulta_pontos_estudo)) {
    $materia = $row['nome_materia'];
    $ponto_estudado = $row['ponto_estudado'];
    $tempo_estudo = $row['tempo_estudo']; // Tempo no formato "HH:MM:SS"

    // Converte o tempo de estudo para segundos
    $tempo_estudo_segundos = converterParaSegundos($tempo_estudo);

    // Adiciona os tempos de estudo por ponto estudado para a matéria correspondente
    if (!isset($pontos_estudo_por_materia[$materia])) {
        $pontos_estudo_por_materia[$materia] = array();
    }
    // Convertendo para segundos e armazenando
    $pontos_estudo_por_materia[$materia][$ponto_estudado] = $tempo_estudo_segundos;

    // Calcula o tempo total por matéria
    if (!isset($tempo_total_por_materia[$materia])) {
        $tempo_total_por_materia[$materia] = 0;
    }
    $tempo_total_por_materia[$materia] += $tempo_estudo_segundos;
}

// Convertendo os arrays para JSON para serem utilizados no JavaScript
$dados_json = json_encode($pontos_estudo_por_materia);
$cores_json = json_encode($cores_materias);
$tempo_total_json = json_encode($tempo_total_por_materia);
$houve_estudo_json = json_encode($houve_estudo);

// Consulta para obter o tempo de planejamento
$query = "SELECT tempo_planejamento FROM appEstudo.planejamento WHERE usuario_idusuario = $id_usuario";
$result = pg_query($conexao, $query);

if ($result && pg_num_rows($result) > 0) {
    $row = pg_fetch_assoc($result);
    $tempo_planejamento = $row['tempo_planejamento'];

    // Converter o tempo do formato hh:mm para segundos
    function converterTempoParaSegundos_E($tempo) {
        $partes = explode(':', $tempo);
        return ($partes[0] * 3600) + ($partes[1] * 60);
    }

    // Armazenar o tempo de planejamento em segundos na sessão
    $tempoPlanejamento = converterTempoParaSegundos_E($tempo_planejamento);
}

function segundosParaHoraMinuto($segundos) {
    $horas = floor($segundos / 3600);
    $minutos = floor(($segundos % 3600) / 60);
    return sprintf("%02dh:%02dmin", $horas, $minutos);
}

$tempo_meta = $tempoPlanejamento;

// Função para calcular o tempo restante ou excedente em relação à meta diária
// Função para calcular o tempo restante ou excedente em relação à meta diária
function calcularTempoRestanteOuExcedente($tempoPlanejamento, $tempoTotalDoDia) {
    if ($tempoTotalDoDia >= $tempoPlanejamento) {
        return "Parabéns! Você bateu a meta diária!";
    } else {
        $diferenca = $tempoPlanejamento - $tempoTotalDoDia;
        return "<b>Falta</b> <span style=\"color: orange;\"><b>" . segundosParaHoraMinuto($diferenca) . "</b></span> para bater a meta diária de <span style=\"color: green; background-color: red; font-weight: bold;\">" . segundosParaHoraMinuto($tempoPlanejamento) . "</span>";
    }
}



$tempoRestanteOuExcedente = calcularTempoRestanteOuExcedente($tempoPlanejamento, array_sum($tempo_total_por_materia));

?>

<div id="pontoChart2" class="chart-container"></div>
<div id="mensagemSemEstudo" class="mensagem-container" style="display: none;">
    <h5><strong><span style="color: white; background-color: red;">ATENÇÃO!</span> Você Ainda não estudou Hoje</strong></h5>
    <p style="text-align: center;">A <b>CONSTÂNCIA</b> é mais importante que a INTENSIDADE!</p>
    <p style="text-align: right;  font-family: 'Courier Prime', monospace; font-size: 20px;">
        Sua meta <b>DIÁRIA</b> é <span style="color: white; background-color: red; font-weight: bold;"><?php echo segundosParaHoraMinuto($tempo_meta); ?></span>
    </p>
</div>

<script>
    // Recuperando os dados do PHP
    var dados = <?php echo $dados_json; ?>;
    var cores = <?php echo $cores_json; ?>;
    var tempoTotalPorMateria = <?php echo $tempo_total_json; ?>;
    var houveEstudo = <?php echo $houve_estudo_json; ?>;

    var tempoPlanejamento = <?php echo $tempoPlanejamento ?>;
    var metadeTempoPlanejamentoSegundos = tempoPlanejamento / 2;
    var tempoRestanteOuExcedente = '<?php echo $tempoRestanteOuExcedente; ?>';

    if (!houveEstudo) {
        document.getElementById('mensagemSemEstudo').style.display = 'block';
    } else {
        document.getElementById('mensagemSemEstudo').style.display = 'none';

        // Calculando o tempo total de estudo do dia
        var tempoTotalDoDia = 0;
        for (var materia in tempoTotalPorMateria) {
            tempoTotalDoDia += tempoTotalPorMateria[materia];
        }
        var horasTotal = Math.floor(tempoTotalDoDia / 3600);
        var minutosTotal = Math.floor((tempoTotalDoDia % 3600) / 60);

        // Formatando o tempo total com partes menores para "h:" e "min"
        var tempoTotalFormatado = horasTotal + '<span style="font-size: 20px;">h</span>' + minutosTotal + '<span style="font-size: 20px;">min</span>';

        // Verificar o tempo total em horas (ou converter conforme necessário)
        var tempoTotal = tempoTotalDoDia; // Converter para número, se necessário
        console.log("Tempo Total: ", tempoTotal);
        console.log("Metade do Tempo de Planejamento: ", tempoPlanejamento);

        // Definir a cor com base no tempo total
        var corTexto = '';
        if (tempoTotal < metadeTempoPlanejamentoSegundos) {
            corTexto = 'rgba(255,0,0,0.74)'; // Vermelho claro para menos ou igual a metade do tempo de planejamento
        } else if (tempoTotal < tempoPlanejamento) {
            corTexto = '#cabb53'; // Amarelo claro para menos do que o tempo de planejamento, mas mais do que a metade
        } else {
            corTexto = '#269c0f'; // Verde claro para mais ou igual ao tempo de planejamento
        }

        // Construir o texto do subtitle com o estilo condicional
        var metaDiariaHTML = '<p style="text-align: center; font-family: \'Courier Prime\', monospace; font-size: 12px;">meta DIÁRIA de <span style="color: #269c0f; background-color: red; font-weight: bold;"><?php echo segundosParaHoraMinuto($tempo_meta); ?></span></p>';
        // Construir o texto do subtitle com o estilo condicional
        var subtitleText = '<span style="font-weight: bold; font-size: 20px; font-family: \'Courier Prime\', monospace; color: rgba(0,0,0,0.53);">Tempo Total: </span>' +
            '<span style="font-weight: bold; font-size: 30px; font-family: \'Courier Prime\', monospace; color: ' + corTexto + ';">' + tempoTotalFormatado + '</span><br>' +
            '<p style="text-align: center; font-family: \'Courier Prime\', monospace; font-size: 12px;">' + tempoRestanteOuExcedente + '</p>';

        // Transformando os dados no formato esperado pelo Highcharts para o gráfico de pontos estudados
        var seriesData = [];
        var drilldownSeries = [];

        for (var materia in tempoTotalPorMateria) {
            var totalTempoEstudo = tempoTotalPorMateria[materia];
            var horas = Math.floor(totalTempoEstudo / 3600);
            var minutos = Math.floor((totalTempoEstudo % 3600) / 60);
            var formattedTime = horas + 'h ' + minutos + 'm';
            seriesData.push({
                name: materia,
                y: totalTempoEstudo,
                formattedTime: formattedTime,
                drilldown: materia,
                color: cores[materia] // Atribui a cor específica da matéria
            });
            var data = [];
            for (var pontoEstudado in dados[materia]) {
                var tempoEstudo = parseInt(dados[materia][pontoEstudado]);
                var horasPontoEstudo = Math.floor(tempoEstudo / 3600);
                var minutosPontoEstudo = Math.floor((tempoEstudo % 3600) / 60);
                var formattedTimePontoEstudo = horasPontoEstudo + 'h ' + minutosPontoEstudo + 'm';
                data.push({ name: pontoEstudado, y: tempoEstudo, formattedTime: formattedTimePontoEstudo });
            }
            drilldownSeries.push({
                name: materia,
                id: materia,
                data: data.map(function(item) {
                    return { name: item.name, y: item.y, formattedTime: item.formattedTime };
                })
            });
        }

        // Configuração do gráfico de pontos estudados
        Highcharts.chart('pontoChart2', {
            chart: {
                type: 'pie',
                backgroundColor: 'transparent' // Fundo transparente
            },
            title: {
                text: null
            },
            credits: {
                enabled: false // Desativar créditos
            },
            plotOptions: {
                series: {
                    dataLabels: {
                        enabled: true,
                        format: '{point.name}: {point.percentage:.2f}% ({point.formattedTime})',
                        style: {
                            textOutline: 'none', // Remover sombra branca ao redor do texto
                            textAlign: 'right' // Alinha o texto à direita
                        }
                    }
                }
            },
            tooltip: {
                pointFormat: '<span style="color:{point.color}">\u25CF</span> {series.name}: <b>{point.percentage:.2f}%</b> ({point.formattedTime})<br/>',
                style: {
                    textOutline: 'none' // Remover sombra branca ao redor do texto da tooltip
                }
            },
            series: [{
                name: 'Matéria',
                colorByPoint: true,
                data: seriesData
            }],
            drilldown: {
                series: drilldownSeries
            },
            // Utilizar o subtitle com o texto construído
            subtitle: {
                text: subtitleText
            }
        });
    }
</script>
