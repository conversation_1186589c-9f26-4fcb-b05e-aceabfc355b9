<?php
include_once("../session_config.php");
include_once("../assets/config.php");

header('Content-Type: application/json');

if (!verificarSessaoValida()) {
    echo json_encode(['success' => false]);
    exit;
}

$resposta_id = (int)$_POST['resposta_id'];
$conteudo = $_POST['conteudo'];

// Atualiza a resposta sem validações adicionais
$query = "UPDATE appestudo.forum_respostas 
          SET conteudo = $1, 
              updated_at = CURRENT_TIMESTAMP 
          WHERE id = $2 
          AND usuario_id = $3 
          RETURNING id";

$result = pg_query_params(
    $conexao,
    $query,
    array($conteudo, $resposta_id, $_SESSION['idusuario'])
);

if ($result && pg_affected_rows($result) > 0) {
    echo json_encode(['success' => true]);
} else {
    echo json_encode(['success' => false]);
}
?>
