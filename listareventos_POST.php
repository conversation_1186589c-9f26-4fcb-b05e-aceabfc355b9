<?php
session_start();
// Arquivo: carregar_eventos.php

date_default_timezone_set('America/Sao_Paulo');

include_once("conexao_POST.php");
//if (isset($_SESSION['idusuario'])) {
//$id_planejamento = $_SESSION['idusuario'];
$id_usuario = $_SESSION['idusuario'];


// Consultar as matérias relacionadas ao estudo
$query_consultar_materias = "SELECT m.* FROM appEstudo.materia m 
INNER JOIN appEstudo.estudos em ON m.idmateria = em.materia_idmateria
WHERE em.planejamento_usuario_idusuario = $id_usuario;";
$resultado_materias = pg_query($conexao, $query_consultar_materias);


$materias = array();

// Percorrer os resultados da consulta e preencher o array $materias
while ($registro_materia = pg_fetch_assoc($resultado_materias)) {
  $materia = $registro_materia['nome'];
  $cor = $registro_materia['cor'];

  // Adicionar a matéria e sua cor ao array $materias
  $materias[$materia] = $cor;
}
//}
// Consultar os dados dos estudos pelo ID do usuario
$query_consultar_estudos = "
SELECT e.*, 
       m_estudo.nome AS nome_materia_estudo, 
       m_estudo.cor AS cor_materia_estudo,
       c.nome AS nome_curso,
       e.descricao
FROM appEstudo.estudos e
INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
LEFT JOIN appEstudo.materia m_estudo ON e.materia_idmateria = m_estudo.idmateria
LEFT JOIN appEstudo.curso c ON e.idcurso = c.idcurso
WHERE u.idusuario = $id_usuario";

$resultado_estudos = pg_query($conexao, $query_consultar_estudos);

// Consultar os dados do planejamento pelo usuario, incluindo o nome do usuário
$query_consultar_planejamento = "SELECT p.*, u.nome as nome_usuario 
                                     FROM appEstudo.planejamento p 
                                     INNER JOIN appEstudo.usuario u ON p.usuario_idusuario = u.idusuario
                                     WHERE p.usuario_idusuario = $id_usuario";
$resultado_planejamento = pg_query($conexao, $query_consultar_planejamento);

if (pg_num_rows($resultado_planejamento) > 0) {
  $planejamento = pg_fetch_assoc($resultado_planejamento);
  $id_planejamento = $planejamento['idplanejamento'];
  $nome_planejamento = $planejamento['nome'];
  $planejamento['nome_usuario'];
  $data_inicio_planejamento = $planejamento['data_inicio'];
  $data_inicio_planejamento = date('d-m-Y', strtotime($data_inicio_planejamento));
  $data_fim_planejamento = $planejamento['data_fim'];
}

$eventos = array();

$event = array(
    'id' => $id_planejamento,
    'title' => $nome_planejamento,
    'start' => date('Y-m-d H:i', strtotime($data_fim_planejamento)),
    'hora_liquida' => 'Planejamento',
  'inicio' => $data_inicio_planejamento,
  //'end' => date('Y-m-d H:i', strtotime($registro['data_fim'])),
  //'detalhes' => $registro['detalhes'],
  //'tipo' => $registro['tipo_evento'], // Adicione o campo tipo_evento
  //'realizado' => $registro['realizado'],
    'color' => 'black'
    //'borderColor' => 'red'
);
$eventos[] = $event;

// Definir um array associativo de cores para disciplinas/materias específicas
$cores = $materias;

// Definir um array associativo de cores das bordas do evento
$coresBorda = array(
    'Direito Penal' => '#FF1215', // ???
    'Direito Processual Penal' => '#FF1215', // ???
    'Direito Constitucional' => '#FFC107', // Verde
    'Direito Administrativo' => '#FFC107', // Azul
    'Simulado' => '#4F4F4F', // Preto
    'Lei Seca' => '#607D8B',
    'Leis Extravagantes' => '#512DA8',
    'Estatística' => '#512DA8',


);


while ($registro = pg_fetch_assoc($resultado_estudos)) {
  $materia = $registro['nome_materia_estudo'];

  $tempoInicioEstudo = $registro['hora_inicio'];
  $tempoInicioEstudoFracionado = explode(':', $tempoInicioEstudo);
  $iniciohoras = $tempoInicioEstudoFracionado[0];
  $iniciominutos = $tempoInicioEstudoFracionado[1];
  $iniciosegundos = $tempoInicioEstudoFracionado[2];

  $tempoFimEstudo = $registro['hora_fim'];
  $tempoFimEstudoFracionado = explode(':', $tempoFimEstudo);
  $fimhoras = $tempoFimEstudoFracionado[0];
  $fimminutos = $tempoFimEstudoFracionado[1];
  $fimsegundos = $tempoFimEstudoFracionado[2];

  $tempoperdido = $registro['tempo_perdido'];
  $tempoperdidoFracionado = explode(':', $tempoperdido);
  $perdidohoras = $tempoperdidoFracionado[0];
  $perdidominutos = $tempoperdidoFracionado[1];
  $perdidosegundos = $tempoperdidoFracionado[2];

  $tempoliquido = $registro['tempo_liquido'];
  $tempoliquidoFracionado = explode(':', $tempoliquido);
  $liquidohoras = $tempoliquidoFracionado[0];
  $liquidominutos = $tempoliquidoFracionado[1];
  $liquidosegundos = $tempoliquidoFracionado[2];

  $tempobruto = $registro['tempo_bruto'];
  $tempobrutoFracionado = explode(':', $tempobruto);
  $brutohoras = $tempobrutoFracionado[0];
  $brutominutos = $tempobrutoFracionado[1];
  $brutosegundos = $tempobrutoFracionado[2];

  // Verificar se a disciplina/materia tem uma cor associada
  if (array_key_exists($materia, $cores)) {
    $evento = array(
        'id' => $registro['idestudos'],
        'title' => $materia,
        'nome_curso' => $registro['nome_curso'],
        'start' => date('Y-m-d\TH:i:s', strtotime($registro['data'].'T'.$registro['hora_inicio'])),
        'end' => date('Y-m-d\TH:i:s', strtotime($registro['data'].'T'.$registro['hora_fim'])),
        'hora_liquida' => $registro['tempo_liquido'].' Liquido',
        'color' => $cores[$materia], // Atribuir a cor correspondente à disciplina/materia
      //'borderColor' => $coresBorda[$materia],
        'tempo_bruto' => $registro['tempo_bruto'],
        'tempo_perdido_horas' => $perdidohoras,
        'tempo_perdido_minutos' => $perdidominutos,
        'ponto_estudado' => $registro['ponto_estudado'],
        'tempo_inicio_horas' => $iniciohoras,
        'tempo_inicio_minutos' => $iniciominutos,
        'tempo_fim_horas' => $fimhoras,
        'tempo_fim_minutos' => $fimminutos,
        'tempo_liquido_horas' => $liquidohoras,
        'tempo_liquido_minutos' => $liquidominutos,
        'metodo' => $registro['metodo'],
        'q_total' => $registro['q_total'],
        'q_errada' => $registro['q_errada'],
        'q_certa' => $registro['q_certa'],
        'descricao' => $registro['descricao']
      //'tempo_inicio_estudo' => $registro['tempo_inicio_estudo'],
      //'tempo_fim_estudo' => $registro['tempo_fim_estudo'],
      // Adicione outras propriedades do evento, se necessário
    );
  } else {
    $evento = array(
        'id' => $registro['idestudos'],
        'title' => $materia,
        'nome_curso' => $registro['nome_curso'],
        'start' => date('Y-m-d\TH:i:s', strtotime($registro['data'].'T'.$registro['hora_inicio'])),
        'end' => date('Y-m-d\TH:i:s', strtotime($registro['data'].'T'.$registro['hora_fim'])),
        'hora_liquida' => $registro['tempo_liquido'].' Hora Liquida',
        'color' => '#CCCCCC', // Caso a disciplina/materia não tenha uma cor associada, use uma cor padrão
      //'borderColor' => 'black',// Caso a disciplina/materia não tenha uma cor associada, use uma cor padrão
        'tempo_bruto' => $registro['tempo_bruto'],
        'tempo_perdido_horas' => $perdidohoras,
        'tempo_perdido_minutos' => $perdidominutos,
        'ponto_estudado' => $registro['ponto_estudado'],
        'tempo_inicio_horas' => $iniciohoras,
        'tempo_inicio_minutos' => $iniciominutos,
        'tempo_fim_horas' => $fimhoras,
        'tempo_fim_minutos' => $fimminutos,
        'tempo_liquido_horas' => $liquidohoras,
        'tempo_liquido_minutos' => $liquidominutos,
        'metodo' => $registro['metodo'],
        'q_total' => $registro['q_total'],
        'q_errada' => $registro['q_errada'],
        'q_certa' => $registro['q_certa'],
      //'tempo_inicio_estudo' => $registro['tempo_inicio_estudo'],
      //'tempo_fim_estudo' => $registro['tempo_fim_estudo'],
      // Adicione outras propriedades do evento, se necessário
    );
  }

  $eventos[] = $evento;
}

header('Content-Type: application/json');
echo json_encode($eventos);
?>
