<?php
//0listar_ultimo_estudo_caixa.php

if ($semRegistro === true): ?>
    <div class="ultimo-estudo empty-state">
        <div class="empty-icon">
            <i class="fas fa-book-reader"></i>
        </div>
        <div class="titulo-estudo">Último Estudo</div>
        <div class="status-message error">
            <i class="fas fa-exclamation-circle"></i>
            Não há registros de estudo
        </div>
    </div>
<?php else: ?>
    <div class="ultimo-estudo"
         data-estudo='<?php echo htmlspecialchars(json_encode($ultimo_estudo), ENT_QUOTES, 'UTF-8'); ?>'
         style="cursor: pointer;">
        <div class="card-header-custom">
            <div class="titulo-estudo">Último Estudo</div>
            <div class="materia-badge">
                <i class="fas fa-bookmark"></i>
                <?php echo $ultimo_estudo['nome_materia_estudo']; ?>
            </div>

            <div class="card-content">
                <!-- <PERSON><PERSON> e Método -->
                <div class="content-row">
                    <div class="info-chip-ultimo">
                        <i class="fas fa-book"></i>
                        <?php echo $ultimo_estudo['ponto_estudado']; ?>
                    </div>
                    <div class="info-chip-ultimo">
                        <i class="fas fa-tasks"></i>
                        <?php echo $ultimo_estudo['metodo']; ?>
                    </div>
                </div>

                <!-- Informações de tempo -->
                <div class="tempo-grid">
                    <div class="tempo-item-ultimo">
                        <div class="tempo-icon-ultimo">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                        <div class="tempo-info">
                            <div class="tempo-label-ultimo">Dia da Semana</div>
                            <div class="tempo-valor-ultimo"><?php echo $dias_semana[$dia_semana]; ?></div>
                        </div>
                    </div>
                    <div class="tempo-item-ultimo">
                        <div class="tempo-icon-ultimo">
                            <i class="fas fa-calendar"></i>
                        </div>
                        <div class="tempo-info-ultimo">
                            <div class="tempo-label-ultimo">Data</div>
                            <div class="tempo-valor-ultimo highlight"><?php echo $data_estudo; ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endif; ?>

<style>
    .ultimo-estudo {
        background: rgba(255, 99, 132, 0.3);
        border-radius: 15px;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.02);
        overflow: hidden;
        transition: transform 0.3s ease;
    }

    .ultimo-estudo:hover {
        transform: translateY(-3px);
    }

    .card-header-custom {
        background: linear-gradient(135deg, #CC4759, rgba(255, 99, 132, 0.3));
        padding: 1.5rem;
        color: white;
    }

    .titulo-estudo {
        font-size: 1.2rem;
        font-weight: 600;
        letter-spacing: 0.5px;
        opacity: 0.9;
        margin-bottom: 1rem;
    }

    .materia-badge {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.1);
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 1.1rem;
        font-weight: 500;
        backdrop-filter: blur(5px);
    }

    .content-row {
        display: flex;
        flex-wrap: wrap;
        gap: 0.75rem;
        margin: 1rem 0;
    }

    .info-chip-ultimo {
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        background: #ffffff;
        padding: 0.5rem 1rem;
        border-radius: 15px;
        font-size: 0.9rem;
        color: #fca3a3;
        border: 1px solid rgba(128, 128, 128, 0.2);
    }

    .tempo-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        margin: 1rem 0;
    }

    .tempo-item-ultimo {
        display: flex;
        align-items: center;
        gap: 1rem;
        background: #eaeaea;
        padding: 1rem;
        border-radius: 12px;
        transition: background-color 0.2s ease;
    }

    .tempo-item-ultimo:hover {
        background: #d9d9d9;
    }

    .tempo-icon-ultimo {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: white;
        border-radius: 10px;
        color: #fca3a3;
        box-shadow: 0 2px 5px rgb(255, 255, 255);
    }

    .tempo-info {
        flex: 1;
    }

    .tempo-label-ultimo {
        font-size: 0.8rem;
        color: #7a7a7a;
        margin-bottom: 0.25rem;
    }

    .tempo-valor-ultimo {
        font-size: 1rem;
        color: #fca3a3;
        font-weight: 500;
    }

    .tempo-valor-ultimo.highlight {
        color: #fca3a3;
        font-weight: 600;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 2rem;
        background: #f5f5f5;
        border-radius: 15px;
        box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    }

    .empty-icon {
        width: 60px;
        height: 60px;
        background: #e0e0e0;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1rem;
        color: #000000;
    }

    .status-message.error {
        color: #000000;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 1rem;
        font-weight: 500;
    }

    @media (max-width: 768px) {
        .tempo-grid {
            grid-template-columns: 1fr;
        }

        .content-row {
            flex-direction: column;
        }
    }

</style>
