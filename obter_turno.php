<?php
// obter_turno.php
include_once "assets/config.php";

// Verifica se o ID do usuário e do planejamento foram passados
if (isset($_GET['id_usuario']) && isset($_GET['id_planejamento'])) {
    $id_usuario = $_GET['id_usuario'];
    $id_planejamento = $_GET['id_planejamento'];
    
    // Consultar os IDs e nomes das matérias associadas ao planejamento
    $query_consultar_materias_planejamento = "
        SELECT pm.materia_idmateria, m.nome AS nome_materia, m.cor AS cor_materia
        FROM appEstudo.planejamento_materia pm
        INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
        WHERE pm.planejamento_idplanejamento = $id_planejamento";

    $resultado_materias_planejamento = pg_query($conexao, $query_consultar_materias_planejamento);

    // Inicializa arrays para armazenar os dados das matérias
    $materias_no_planejamento = array();
    $cores_materias = array();

    // Preenche os arrays com os resultados da consulta
    while ($row = pg_fetch_assoc($resultado_materias_planejamento)) {
        $materia_id = $row['materia_idmateria'];
        $nome_materia = $row['nome_materia'];
        $cor_materia = $row['cor_materia'];

        $materias_no_planejamento[$materia_id] = $nome_materia;
        $cores_materias[$nome_materia] = $cor_materia;
    }

    // Realiza a consulta ao banco de dados para obter os dados de estudo por ponto estudado de cada matéria no planejamento
    $query_consulta_pontos_estudo = "
        SELECT e.hora_inicio,
            e.hora_fim,
            m.nome AS nome_materia
        FROM appEstudo.estudos e
        INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
        INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
        LEFT JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria
        WHERE u.idusuario = $id_usuario AND e.materia_idmateria IN (" . implode(',', array_keys($materias_no_planejamento)) . ")
        ORDER BY m.nome, e.hora_inicio";

    $resultado_consulta_pontos_estudo = pg_query($conexao, $query_consulta_pontos_estudo);

    // Função para calcular o tempo em cada turno
    function calcular_tempo_por_turno($hora_inicio, $hora_fim) {
        $turnos = [
            "Madrugada" => [0, 6],
            "Manhã" => [6, 12],
            "Tarde" => [12, 18],
            "Noite" => [18, 24]
        ];
        $tempo_por_turno = array_fill_keys(array_keys($turnos), 0);
        $hora_inicio = strtotime($hora_inicio);
        $hora_fim = strtotime($hora_fim);

        foreach ($turnos as $turno => $horas) {
            $inicio_turno = strtotime(date('Y-m-d', $hora_inicio) . " +" . $horas[0] . " hours");
            $fim_turno = strtotime(date('Y-m-d', $hora_inicio) . " +" . $horas[1] . " hours");
            if ($hora_fim < $inicio_turno) continue;
            if ($hora_inicio > $fim_turno) continue;
            $inicio = max($hora_inicio, $inicio_turno);
            $fim = min($hora_fim, $fim_turno);
            $tempo_por_turno[$turno] += $fim - $inicio;
        }
        return $tempo_por_turno;
    }

    // Inicializa um array para armazenar os tempos por turno
    $tempos_por_turno = array_fill_keys(["Madrugada", "Manhã", "Tarde", "Noite"], 0);

    // Preenche o array com os resultados da consulta
    while ($row = pg_fetch_assoc($resultado_consulta_pontos_estudo)) {
        $hora_inicio = $row['hora_inicio'];
        $hora_fim = $row['hora_fim'];
        $tempo_por_turno = calcular_tempo_por_turno($hora_inicio, $hora_fim);
        foreach ($tempo_por_turno as $turno => $tempo) {
            $tempos_por_turno[$turno] += $tempo;
        }
    }

    // Converte os tempos de segundos para horas
    foreach ($tempos_por_turno as $turno => $tempo) {
        $tempos_por_turno[$turno] = $tempo / 3600;
    }

    // Adicione esta nova função para formatar o tempo em formato amigável
    function formatarTempoAmigavel($horas) {
        $horas_inteiras = floor($horas);
        $minutos = round(($horas - $horas_inteiras) * 60);

        if ($horas_inteiras == 0) {
            return "{$minutos}min";
        } elseif ($minutos == 0) {
            return "{$horas_inteiras}h";
        } else {
            return "{$horas_inteiras}h:{$minutos}min";
        }
    }

    function getTurnoMaisEstudado($tempos_por_turno) {
        // Verifica se há algum tempo registrado
        $tem_tempo = false;
        foreach ($tempos_por_turno as $tempo) {
            if ($tempo > 0) {
                $tem_tempo = true;
                break;
            }
        }

        if (!$tem_tempo) {
            return [
                'turno' => 'Sem Registros',
                'tempo' => '0min',
                'icon' => 'clock',
                'texto' => 'Modo Preguiça Ativado',
                'desc' => 'Tá mais parado que internet discada! Bora estudar?'
            ];
        }

        $turno_max = '';
        $tempo_max = 0;

        foreach ($tempos_por_turno as $turno => $tempo) {
            if ($tempo > $tempo_max) {
                $tempo_max = $tempo;
                $turno_max = $turno;
            }
        }

        // Mensagens criativas e divertidas para cada turno
        $turnos_info = [
            'Madrugada' => [
                'icon' => 'moon',
                'texto' => [
                    'Vampiro do Conhecimento',
                    'Batman dos Estudos',
                    'Coruja PhDiurna',
                    'Mestre das Sombras',
                    'Guerreiro da Madrugada'
                ],
                'desc' => [
                    'Enquanto outros dormem, você conquista. Edward Cullen que se cuide!',
                    'Transformando café em conhecimento desde que o galo está dormindo',
                    'Trocou a contagem de carneirinhos pela contagem de parágrafos',
                    'Seu cérebro funciona melhor que Wi-Fi de madrugada',
                    'Faz o conhecimento brilhar mais que lua cheia'
                ]
            ],
            'Manhã' => [
                'icon' => 'sun',
                'texto' => [
                    'Guerreiro do Primeiro Sol',
                    'Mestre do Alvorada',
                    'Raio de Sol dos Estudos',
                    'Herói da Aurora',
                    'Gladiador Matinal'
                ],
                'desc' => [
                    'Tão produtivo que faz o galo parecer preguiçoso',
                    'Enquanto outros tomam café, você devora conhecimento',
                    'Seu foco matinal é mais forte que café extra forte',
                    'Faz o sol nascer só pra te ver estudando',
                    'Transformando orvalho da manhã em sabedoria pura'
                ]
            ],
            'Tarde' => [
                'icon' => 'sun',
                'texto' => [
                    'Ninja do Sol a Pino',
                    'Mestre do Meridiano',
                    'Samurai da Siesta',
                    'Guerreiro do Sol',
                    'Trovão da Tarde'
                ],
                'desc' => [
                    'Nem o calor do meio-dia derrete seu foco',
                    'Quando o sono pós-almoço ataca, você contra-ataca!',
                    'Transforma sonolência da tarde em conhecimento puro',
                    'Seu poder de concentração é mais quente que asfalto no verão',
                    'Faz a preguiça da tarde parecer piada'
                ]
            ],
            'Noite' => [
                'icon' => 'star-and-crescent',
                'texto' => [
                    'Guardião da Noite',
                    'Mestre das Estrelas',
                    'Feiticeiro Noturno',
                    'Senhor da Escuridão',
                    'Lenda da Lua'
                ],
                'desc' => [
                    'As estrelas são sua luz de leitura particular',
                    'Faz as corujas parecerem amadoras',
                    'Seu foco noturno é mais brilhante que lua cheia',
                    'Transforma o silêncio da noite em sabedoria',
                    'Enquanto a cidade dorme, você conquista o mundo'
                ]
            ]
        ];

        if (!isset($turnos_info[$turno_max])) {
            return [
                'turno' => 'Erro',
                'tempo' => '0min',
                'icon' => 'exclamation-circle',
                'texto' => 'Bug no Matrix',
                'desc' => 'Até o Neo ficou confuso com esses dados!'
            ];
        }

        // Seleciona aleatoriamente uma das mensagens para o turno
        $textos = $turnos_info[$turno_max]['texto'];
        $descs = $turnos_info[$turno_max]['desc'];
        $indice_aleatorio = array_rand($textos);

        return [
            'turno' => $turno_max,
            'tempo' => formatarTempoAmigavel($tempo_max),
            'icon' => $turnos_info[$turno_max]['icon'],
            'texto' => $textos[$indice_aleatorio],
            'desc' => $descs[$indice_aleatorio]
        ];
    }

    // Retorna o turno mais estudado em formato JSON
    header('Content-Type: application/json');
    echo json_encode(getTurnoMaisEstudado($tempos_por_turno));
    exit;
} else {
    // Se os parâmetros não foram fornecidos, retorna um erro
    header('Content-Type: application/json');
    echo json_encode([
        'turno' => 'Erro',
        'tempo' => '0min',
        'icon' => 'exclamation-circle',
        'texto' => 'Dados Incompletos',
        'desc' => 'Não foi possível obter as informações necessárias'
    ]);
    exit;
}
?>