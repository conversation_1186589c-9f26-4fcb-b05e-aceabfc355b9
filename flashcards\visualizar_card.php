<?php
// visualizar_card.php
session_start();
include_once("assets/config.php");
include_once("helpers.php");

if (!isset($_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit();
}

if (!isset($_GET['id'])) {
    header("Location: flashcards.php");
    exit();
}

$card_id = (int)$_GET['id'];

// Buscar informações do card e suas associações
$query_card = "
    SELECT 
        f.*,
        t.nome as topic_name,
        t.id as topic_id,
        d.nome as deck_name,
        c.nome as categoria_name,
        m.nome as materia_nome
    FROM appestudo.flashcards f
    JOIN appestudo.flashcard_topic_association ft ON ft.flashcard_id = f.id
    JOIN appestudo.flashcard_topics t ON t.id = ft.topic_id
    JOIN appestudo.flashcard_deck_association fd ON fd.flashcard_id = f.id
    JOIN appestudo.flashcard_decks d ON d.id = fd.deck_id
    JOIN appestudo.flashcard_categories c ON c.id = d.category_id
    JOIN appestudo.materia m ON m.idmateria = d.materia_id
    WHERE f.id = $1
    LIMIT 1";
$result_card = pg_query_params($conexao, $query_card, array($card_id));
$card = pg_fetch_assoc($result_card);

if (!$card) {
    header("Location: flashcards.php");
    exit();
}

// Buscar mapa mental se existir
$query_mindmap = "SELECT imagem_base64 FROM appestudo.flashcard_mindmaps WHERE flashcard_id = $1";
$result_mindmap = pg_query_params($conexao, $query_mindmap, array($card_id));
$mindmap = pg_fetch_assoc($result_mindmap);

// Buscar estatísticas do card
$query_stats = "
    SELECT 
        COUNT(DISTINCT fp.usuario_id) as total_estudantes,
        ROUND(AVG(fp.nivel_conhecimento)::numeric, 1) as media_conhecimento,
        COUNT(DISTINCT frh.id) as total_revisoes
    FROM appestudo.flashcard_progress fp
    LEFT JOIN appestudo.flashcard_review_history frh ON frh.progress_id = fp.id
    WHERE fp.flashcard_id = $1";
$result_stats = pg_query_params($conexao, $query_stats, array($card_id));
$stats = pg_fetch_assoc($result_stats);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualizar Card</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #00008B;
            --paper-color: #FFFFFF;
            --secondary-color: #4169E1;
            --background-color: #E8ECF3;
            --text-color: #2C3345;
            --border-color: #B8C2CC;
            --success-color: #28a745;
        }

        body {
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 40px;
            line-height: 1.6;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .card-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            overflow: hidden;
        }

        .breadcrumb {
            color: var(--secondary-color);
            margin-bottom: 25px;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }

        .header {
            background: var(--primary-color);
            color: white;
            padding: 25px;
        }

        .title {
            font-size: 1.4rem;
            font-weight: 600;
            margin: 0;
            opacity: 0.9;
        }

        .stats {
            display: flex;
            gap: 25px;
            padding: 20px 25px;
            background: rgba(0,0,0,0.02);
            border-bottom: 1px solid var(--border-color);
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .stat-icon {
            font-size: 1.2rem;
            color: var(--primary-color);
            opacity: 0.8;
        }

        .stat-info {
            line-height: 1.2;
        }

        .stat-number {
            font-weight: 600;
            font-size: 1.1rem;
            color: var(--primary-color);
        }

        .stat-label {
            font-size: 0.85rem;
            color: #666;
        }

        .content-section {
            padding: 25px;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 1.1rem;
        }

        .content-text {
            color: var(--text-color);
        }

        .mindmap-section {
            text-align: center;
            padding: 25px;
            background: #f8f9fa;
        }

        .mindmap-image {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            padding: 20px 25px;
            background: white;
            border-top: 1px solid var(--border-color);
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-family: inherit;
            font-size: 0.95rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            border: none;
        }

        .btn-secondary {
            background: white;
            color: var(--primary-color);
            border: 2px solid currentColor;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
            border: none;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .btn-back {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 50px;
            height: 50px;
            background: white;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.2s;
            z-index: 1000;
        }

        .btn-back:hover {
            transform: translateY(-2px);
            background: var(--primary-color);
            color: white;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background: white;
            padding: 25px;
            border-radius: 12px;
            max-width: 400px;
            width: 90%;
            text-align: center;
        }
        
        .modal-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--text-color);
        }
        
        .modal-buttons {
            display: flex;
            justify-content: center;
            gap: 12px;
            margin-top: 20px;
        }
        
        .modal-btn {
            padding: 8px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-family: inherit;
            font-size: 0.9rem;
            font-weight: 500;
            border: none;
        }
        
        .modal-btn-cancel {
            background: #6c757d;
            color: white;
        }
        
        .modal-btn-confirm {
            background: #dc3545;
            color: white;
        }

        @media (max-width: 768px) {
            body {
                padding: 20px;
            }

            .stats {
                flex-direction: column;
                gap: 15px;
            }

            .actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <a href="ver_cards.php?topico=<?php echo $card['topic_id']; ?>" class="btn-back">
        <i class="fas fa-arrow-left"></i>
    </a>

    <div class="container">
        <div class="breadcrumb">
            <i class="fas fa-layer-group"></i>
            <?php echo htmlspecialchars($card['categoria_name']); ?> > 
            <?php echo htmlspecialchars($card['materia_nome']); ?> >
            <?php echo htmlspecialchars($card['topic_name']); ?>
        </div>

        <div class="card-container">
            <div class="header">
                <h1 class="title">Card de <?php echo htmlspecialchars($card['topic_name']); ?></h1>
            </div>

            <div class="stats">
                <div class="stat-item">
                    <i class="fas fa-users stat-icon"></i>
                    <div class="stat-info">
                        <div class="stat-number"><?php echo $stats['total_estudantes']; ?></div>
                        <div class="stat-label">Estudantes</div>
                    </div>
                </div>
                <?php if ($stats['media_conhecimento']): ?>
                    <div class="stat-item">
                        <i class="fas fa-chart-line stat-icon"></i>
                        <div class="stat-info">
                            <div class="stat-number"><?php echo $stats['media_conhecimento']; ?>/5</div>
                            <div class="stat-label">Média de Conhecimento</div>
                        </div>
                    </div>
                <?php endif; ?>
                <div class="stat-item">
                    <i class="fas fa-sync-alt stat-icon"></i>
                    <div class="stat-info">
                        <div class="stat-number"><?php echo $stats['total_revisoes']; ?></div>
                        <div class="stat-label">Revisões Totais</div>
                    </div>
                </div>
            </div>

            <div class="content-section">
                <div class="section-title">
                    <i class="fas fa-question-circle"></i>
                    Pergunta
                </div>
                <div class="content-text">
                    <?php echo $card['pergunta']; ?>
                </div>
            </div>

            <div class="content-section">
                <div class="section-title">
                    <i class="fas fa-comment-dots"></i>
                    Resposta
                </div>
                <div class="content-text">
                    <?php echo $card['resposta']; ?>
                </div>
            </div>

            <?php if (!empty($card['resumo'])): ?>
                <div class="content-section">
                    <div class="section-title">
                        <i class="fas fa-file-alt"></i>
                        Resumo
                    </div>
                    <div class="content-text">
                        <?php echo $card['resumo']; ?>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (!empty($card['previsao_legal'])): ?>
                <div class="content-section">
                    <div class="section-title">
                        <i class="fas fa-balance-scale"></i>
                        Previsão Legal
                    </div>
                    <div class="content-text">
                        <?php echo $card['previsao_legal']; ?>
                    </div>
                </div>
            <?php endif; ?>

            <?php if (!empty($card['jurisprudencia'])): ?>
                <div class="content-section">
                    <div class="section-title">
                        <i class="fas fa-gavel"></i>
                        Jurisprudência
                    </div>
                    <div class="content-text">
                        <?php echo $card['jurisprudencia']; ?>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($mindmap): ?>
                <div class="mindmap-section">
                    <div class="section-title">
                        <i class="fas fa-project-diagram"></i>
                        Mapa Mental
                    </div>
                    <img src="data:image/jpeg;base64,<?php echo $mindmap['imagem_base64']; ?>" 
                         alt="Mapa Mental" class="mindmap-image">
                </div>
            <?php endif; ?>

            <?php if(checkAdmin($conexao, $_SESSION['idusuario'])): ?>
                <div class="actions">
                    <a href="editar_card.php?id=<?php echo $card_id; ?>" class="btn btn-secondary">
                        <i class="fas fa-edit"></i>
                        Editar Card
                    </a>
                    <button onclick="mostrarConfirmacaoExclusao()" class="btn btn-danger">
                        <i class="fas fa-trash"></i>
                        Excluir Card
                    </button>
                    <a href="#" class="btn btn-primary">
                        <i class="fas fa-play"></i>
                        Estudar Agora
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Modal de confirmação de exclusão -->
    <div id="modalExclusao" class="modal">
        <div class="modal-content">
            <div class="modal-title">Confirmar Exclusão</div>
            <p>Tem certeza que deseja excluir este card? Esta ação não pode ser desfeita.</p>
            <div class="modal-buttons">
                <button onclick="esconderConfirmacaoExclusao()" class="modal-btn modal-btn-cancel">Cancelar</button>
                <button onclick="excluirCard()" class="modal-btn modal-btn-confirm">Confirmar Exclusão</button>
            </div>
        </div>
    </div>

    <script>
        function mostrarConfirmacaoExclusao() {
            document.getElementById('modalExclusao').style.display = 'flex';
        }

        function esconderConfirmacaoExclusao() {
            document.getElementById('modalExclusao').style.display = 'none';
        }

        function excluirCard() {
            window.location.href = `excluir_card.php?id=<?php echo $card_id; ?>`;
        }
    </script>
</body>
</html>