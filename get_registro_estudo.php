<?php
session_start();
include_once 'conexao_POST.php';

header('Content-Type: application/json');

if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['error' => 'Usuário não autenticado']);
    exit;
}

if (!isset($_GET['id'])) {
    http_response_code(400);
    echo json_encode(['error' => 'ID do estudo não especificado']);
    exit;
}

$id_estudo = (int)$_GET['id'];
$id_usuario = (int)$_SESSION['idusuario'];

try {
    $query = "
        SELECT
            idestudos,
            to_char(data, 'YYYY-MM-DD') as data_estudo,
            ponto_estudado,
            descricao,
            tempo_liquido,
            q_total,
            q_certa,
            q_errada,
            metodo,
            link_conteudo -- Adicionando a coluna link_conteudo
        FROM appEstudo.estudos
        WHERE idestudos = $1 
        AND planejamento_usuario_idusuario = $2";

    $result = pg_query_params($conexao, $query, array($id_estudo, $id_usuario));

    if (!$result) {
        throw new Exception(pg_last_error($conexao));
    }

    $estudo = pg_fetch_assoc($result);
    
    if (!$estudo) {
        http_response_code(404);
        echo json_encode(['error' => 'Registro não encontrado']);
        exit;
    }

    echo json_encode($estudo);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Erro ao buscar dados: ' . $e->getMessage()]);
}



