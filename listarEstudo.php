<?php
// Aqui você deve realizar a conexão com o banco de dados PostgreSQL
include_once("conexao_POST.php");

// Verificar se a conexão foi estabelecida com sucesso
if (!$conexao) {
    die("Erro na conexão: " . pg_last_error());
}

// Montar a query para selecionar os dados da tabela "estudos" juntamente com os nomes do usuário e matéria
$query_listar_estudos = "SELECT e.idestudos, e.data, e.tempo_liquido, e.tempo_bruto, e.tempo_perdido, e.ponto_estudado, e.hora_inicio, e.hora_fim, e.metodo, e.q_total, e.q_errada, e.q_certa, e.planejamento_idplanejamento, u.nome AS nome_usuario, m.nome AS nome_materia
                        FROM appEstudo.estudos e
                        INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
                        INNER JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria";

// Executar a query de seleção
$resultado_listar_estudos = pg_query($conexao, $query_listar_estudos);

// Verificar se a consulta foi bem-sucedida
if ($resultado_listar_estudos) {
    // Verificar se há registros na tabela
    if (pg_num_rows($resultado_listar_estudos) > 0) {
        // Cabeçalho da tabela
        echo "<table border='1'>
                <tr>
                    <th>ID</th>
                    <th>Data</th>
                    <th>Tempo Líquido</th>
                    <th>Tempo Bruto</th>
                    <th>Tempo Perdido</th>
                    <th>Ponto Estudado</th>
                    <th>Hora de Início</th>
                    <th>Hora de Fim</th>
                    <th>Método</th>
                    <th>Total de Questões</th>
                    <th>Questões Erradas</th>
                    <th>Questões Certas</th>
                    <th>ID Planejamento</th>
                    <th>Nome do Usuário</th>
                    <th>Nome da Matéria</th>
                </tr>";

        // Loop para exibir os dados de cada registro
        while ($linha_estudo = pg_fetch_assoc($resultado_listar_estudos)) {
            echo "<tr>";
            echo "<td>{$linha_estudo['idestudos']}</td>";
            echo "<td>{$linha_estudo['data']}</td>";
            echo "<td>{$linha_estudo['tempo_liquido']}</td>";
            echo "<td>{$linha_estudo['tempo_bruto']}</td>";
            echo "<td>{$linha_estudo['tempo_perdido']}</td>";
            echo "<td>{$linha_estudo['ponto_estudado']}</td>";
            echo "<td>{$linha_estudo['hora_inicio']}</td>";
            echo "<td>{$linha_estudo['hora_fim']}</td>";
            echo "<td>{$linha_estudo['metodo']}</td>";
            echo "<td>{$linha_estudo['q_total']}</td>";
            echo "<td>{$linha_estudo['q_errada']}</td>";
            echo "<td>{$linha_estudo['q_certa']}</td>";
            echo "<td>{$linha_estudo['planejamento_idplanejamento']}</td>";
            echo "<td>{$linha_estudo['nome_usuario']}</td>";
            echo "<td>{$linha_estudo['nome_materia']}</td>";
            echo "</tr>";
        }

        // Fechamento da tabela
        echo "</table>";
    } else {
        echo "Nenhum registro encontrado na tabela 'estudos'.";
    }
} else {
    echo "Erro ao listar estudos: " . pg_last_error($conexao);
}

// Fechar a conexão com o banco de dados PostgreSQL
pg_close($conexao);
?>
