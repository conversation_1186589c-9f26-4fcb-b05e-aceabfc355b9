<?php


// Verifique se o usuário está logado
if (isset($_SESSION['username'])) {
    date_default_timezone_set('America/Sao_Paulo');

    include_once("conexao_POST.php");

    if (isset($_SESSION['idusuario'])) {
        $id_usuario = $_SESSION['idusuario'];


        // Consulta para obter o tempo de planejamento
        $query = "SELECT tempo_planejamento FROM appEstudo.planejamento WHERE usuario_idusuario = $id_usuario";
        $result = pg_query($conexao, $query);

        if ($result && pg_num_rows($result) > 0) {
            $row = pg_fetch_assoc($result);
            $tempo_planejamento = $row['tempo_planejamento'];

            // Converter o tempo do formato hh:mm para minutos
            function converterTempoParaMinutos($tempo)
            {
                $partes = explode(':', $tempo);
                return ($partes[0] * 3600) + ($partes[1] * 60);
            }

            // Armazenar o tempo de planejamento em minutos na sessão
            $tempoPlanejamento = converterTempoParaMinutos($tempo_planejamento);

        }





        // Obtém o valor atual do contador do formulário ou define como 1 se não existir
        $contador = isset($_POST['contador']) ? $_POST['contador'] : 1;

        // Verifica a ação e atualiza o contador
        if (isset($_POST['acao'])) {
            if ($_POST['acao'] === 'anterior') {
                $contador++;
            } elseif ($_POST['acao'] === 'frente') {
                if ($contador <= 0) {
                    $contador = 1;
                } else {
                    $contador--;
                }
            }
        }

        // Atualiza a sessão com o novo valor do contador
        $_SESSION['contador'] = $contador;

        $diasRetroceder = 15;

        // Consulta os dados dos estudos com base no intervalo escolhido
        $query_consultar_estudos = "SELECT data, tempo_liquido FROM appEstudo.estudos WHERE planejamento_usuario_idusuario = $id_usuario AND data >= CURRENT_DATE - INTERVAL '$diasRetroceder days' ORDER BY data ASC";
        $tempoEstudadoPorDia = array(); // Array para armazenar o tempo estudado por dia

        $resultado_estudos = pg_query($conexao, $query_consultar_estudos);

        if (!$resultado_estudos) {
            die("Erro na consulta SQL: " . pg_last_error($conexao));
        }

        while ($exibirRegistros = pg_fetch_array($resultado_estudos)) {
            $data_estudo = $exibirRegistros['data'];
            $tempo_liquido = $exibirRegistros['tempo_liquido'];

            // Adicione o tempo estudado por dia ao array
            $data_estudo_sem_horas = date('Y-m-d', strtotime($data_estudo));

            // Certifique-se de que a chave existe antes de acessá-la
            if (!isset($tempoEstudadoPorDia[$data_estudo_sem_horas])) {
                $tempoEstudadoPorDia[$data_estudo_sem_horas] = 0;
            }

            // Converta o tempo líquido para segundos e some
            $tempo_liquido_seconds = strtotime("1970-01-01 $tempo_liquido UTC");
            $tempoEstudadoPorDia[$data_estudo_sem_horas] += $tempo_liquido_seconds;
        }

        // Preparar os dados para retorno em JSON
        $datas = [];
        $tempos = [];
        $temposEstudados = [];

        foreach ($tempoEstudadoPorDia as $data => $tempo) {
            $datas[] = date('d/m/Y', strtotime($data));
            $tempos[] = $tempo;
            $temposEstudados[] = [
                'data' => $data,
                'tempo' => $tempo
            ];
        }

        $response = [
            'datas' => $datas,
            'tempos' => $tempos,
            'temposEstudados' => $temposEstudados
        ];

        //echo json_encode($response);
    }
} else {
    // Se não estiver logado, você pode fazer algo aqui
    echo json_encode(["error" => "Usuário não logado"]);
}
?>
