/*
 * Home Mãe CSS - Design Elegante e Moderno
 * Herda variáveis do style.css principal
 */

/* Reset e configurações base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--light-bg);
    background-image:
        radial-gradient(circle at 15% 85%, rgba(102, 126, 234, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 85% 15%, rgba(118, 75, 162, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(74, 74, 74, 0.02) 0%, transparent 50%);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
    transition: var(--transition);
    overflow-x: hidden;
}

/* Tema escuro - Gradientes sutis */
[data-theme="dark"] body {
    background-image:
        radial-gradient(circle at 15% 85%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 85% 15%, rgba(118, 75, 162, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 50% 50%, rgba(255, 255, 255, 0.01) 0%, transparent 50%);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header-barra - Mesmo estilo consistente */
.header-barra {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 24px;
    border-bottom: 1px solid var(--border);
    box-shadow: var(--shadow-light);
    background: var(--card-bg);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: var(--transition);
    backdrop-filter: blur(20px);
}

.header-left {
    display: flex;
    align-items: center;
}

.logo img {
    height: 55px;
    width: auto;
    transition: opacity 0.3s ease;
}

/* Controle de visibilidade dos logos */
.logo .logo-light {
    opacity: 1;
}

.logo .logo-dark {
    opacity: 0;
    position: absolute;
}

[data-theme="dark"] .logo .logo-light {
    opacity: 0;
}

[data-theme="dark"] .logo .logo-dark {
    opacity: 1;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 18px;
    border-radius: 24px;
    font-size: 0.95rem;
    background: var(--hover);
    color: var(--text-primary);
    transition: var(--transition);
}

.user-info i {
    color: var(--primary);
    font-size: 1.3rem;
}

.user-name {
    color: var(--text);
    font-weight: 600;
    letter-spacing: 0.01em;
}

.theme-toggle {
    display: flex;
    align-items: center;
}

.theme-btn {
    background: var(--card-bg);
    border: 2px solid var(--border);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-primary);
    font-size: 1.2rem;
    box-shadow: var(--shadow-light);
}

.theme-btn:hover {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-medium);
}

.theme-btn:active {
    transform: scale(0.95);
}

/* Seção Principal */
.main-section {
    padding: 140px 0 60px; /* Espaço para header fixo */
    background: var(--light-bg);
    position: relative;
    overflow: hidden;
}

.main-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(circle at 30% 30%, rgba(102, 126, 234, 0.08) 0%, transparent 60%),
        radial-gradient(circle at 70% 70%, rgba(118, 75, 162, 0.08) 0%, transparent 60%);
    opacity: 0.6;
    z-index: 1;
    animation: float 25s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-30px) rotate(180deg); }
}

.welcome-header {
    text-align: center;
    margin-bottom: 80px;
    position: relative;
    z-index: 2;
}

.welcome-title {
    font-size: clamp(2.8rem, 6vw, 4rem);
    font-weight: 800;
    margin-bottom: 24px;
    color: var(--text-primary);
    letter-spacing: -0.04em;
    line-height: 1.1;
}

.highlight {
    background: linear-gradient(135deg, 
        rgba(102, 126, 234, 1) 0%, 
        rgba(118, 75, 162, 1) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
}

.highlight::after {
    content: '';
    position: absolute;
    bottom: -6px;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, 
        rgba(102, 126, 234, 0.4) 0%, 
        rgba(118, 75, 162, 0.4) 100%);
    border-radius: 2px;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { opacity: 0.4; }
    50% { opacity: 0.8; }
}

.welcome-subtitle {
    font-size: 1.4rem;
    opacity: 0.85;
    font-weight: 500;
    color: var(--text-secondary);
    letter-spacing: 0.02em;
    line-height: 1.5;
}

/* Cards Section */
.cards-section {
    padding: 0 0 100px;
    background: var(--light-bg);
    position: relative;
    z-index: 2;
}

/* Cards Grid */
.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 48px;
    padding: 0;
    max-width: 1000px;
    margin: 0 auto;
}

/* Main Cards - Design Ultra Moderno */
.main-card {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border-radius: 32px;
    padding: 56px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    border: 1px solid var(--border);
    height: 380px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-shadow: 
        0 16px 48px rgba(0, 0, 0, 0.08),
        0 6px 20px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* Modo claro - Cards mais sólidos */
[data-theme="light"] .main-card {
    background: rgba(255, 255, 255, 0.98);
    border: 1px solid rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(8px);
}

[data-theme="light"] .main-card:hover {
    background: rgba(255, 255, 255, 1);
    border-color: rgba(102, 126, 234, 0.3);
    box-shadow: 
        0 24px 80px rgba(102, 126, 234, 0.15),
        0 12px 40px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 1);
}

/* Modo escuro - Glassmorphism */
[data-theme="dark"] .main-card {
    background: rgba(255, 255, 255, 0.06);
    border: 1px solid rgba(255, 255, 255, 0.12);
    backdrop-filter: blur(20px);
}

[data-theme="dark"] .main-card:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(102, 126, 234, 0.5);
}

.main-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, 
        rgba(102, 126, 234, 0.06) 0%, 
        rgba(118, 75, 162, 0.06) 100%);
    opacity: 0;
    transition: all 0.4s ease;
    z-index: 1;
}

.main-card:hover {
    transform: translateY(-12px) scale(1.02);
    box-shadow:
        0 20px 60px rgba(102, 126, 234, 0.15),
        0 8px 32px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    border-color: rgba(102, 126, 234, 0.3);
}

.main-card:hover::before {
    opacity: 1;
}

.card-icon {
    width: 96px;
    height: 96px;
    border-radius: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.4rem;
    color: white;
    background: linear-gradient(135deg, 
        rgba(102, 126, 234, 1) 0%, 
        rgba(118, 75, 162, 1) 100%);
    box-shadow: 
        0 20px 60px rgba(102, 126, 234, 0.3),
        0 8px 24px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 2;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.main-card:hover .card-icon {
    transform: scale(1.05) rotate(2deg);
    box-shadow:
        0 16px 50px rgba(102, 126, 234, 0.2),
        0 6px 20px rgba(0, 0, 0, 0.15);
}

.card-content {
    position: relative;
    z-index: 2;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin: 32px 0;
}

.card-title {
    font-size: 2.4rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: var(--text-primary);
    transition: all 0.4s ease;
    letter-spacing: -0.03em;
    line-height: 1.2;
}

.main-card:hover .card-title {
    color: var(--primary);
    transform: translateY(-2px);
}

/* Melhorar contraste do texto no modo claro */
[data-theme="light"] .card-title {
    color: #1a1a1a;
    font-weight: 700;
}

[data-theme="light"] .card-description {
    color: #4a4a4a;
    opacity: 0.9;
}

.card-description {
    font-size: 1.15rem;
    opacity: 0.85;
    line-height: 1.6;
    color: var(--text-secondary);
    transition: all 0.4s ease;
    letter-spacing: 0.01em;
}

.main-card:hover .card-description {
    opacity: 1;
    transform: translateY(-1px);
}

.card-arrow {
    position: relative;
    z-index: 2;
    align-self: flex-end;
    width: 64px;
    height: 64px;
    border-radius: 50%;
    background: linear-gradient(135deg, 
        rgba(102, 126, 234, 1) 0%, 
        rgba(118, 75, 162, 1) 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.4rem;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 
        0 16px 40px rgba(102, 126, 234, 0.3),
        0 6px 16px rgba(0, 0, 0, 0.1);
}

.main-card:hover .card-arrow {
    transform: translateX(12px) scale(1.1);
    box-shadow:
        0 16px 40px rgba(102, 126, 234, 0.4),
        0 6px 16px rgba(0, 0, 0, 0.15);
}

/* Animações de entrada */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(80px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes slideInFromLeft {
    from {
        opacity: 0;
        transform: translateX(-80px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateX(0) scale(1);
    }
}

.welcome-header {
    animation: slideInFromLeft 1s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
    opacity: 0;
    animation-delay: 0.3s;
}

.main-card {
    animation: fadeInUp 1s cubic-bezier(0.175, 0.885, 0.32, 1.275) forwards;
    opacity: 0;
}

.main-card:nth-child(1) {
    animation-delay: 0.6s;
}

.main-card:nth-child(2) {
    animation-delay: 0.8s;
}

/* Responsividade */
@media (max-width: 1024px) {
    .cards-grid {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 40px;
        max-width: 900px;
    }

    .main-card {
        height: 360px;
        padding: 48px;
    }

    .card-icon {
        width: 88px;
        height: 88px;
        font-size: 2.2rem;
    }

    .card-title {
        font-size: 2.2rem;
    }

    .card-description {
        font-size: 1.1rem;
    }

    .card-arrow {
        width: 60px;
        height: 60px;
        font-size: 1.3rem;
    }
}

@media (max-width: 768px) {
    .header-barra {
        padding: 10px 20px;
    }

    .logo img {
        height: 48px;
    }

    .user-info {
        font-size: 0.9rem;
        padding: 8px 14px;
    }

    .theme-btn {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }

    .main-section {
        padding: 120px 0 40px;
    }

    .welcome-title {
        font-size: clamp(2.2rem, 8vw, 3rem);
        margin-bottom: 20px;
    }

    .welcome-subtitle {
        font-size: 1.2rem;
    }

    .welcome-header {
        margin-bottom: 60px;
    }

    .cards-grid {
        grid-template-columns: 1fr;
        gap: 32px;
        padding: 0 10px;
    }

    .main-card {
        height: auto;
        padding: 40px;
        margin: 0;
        border-radius: 28px;
    }

    .card-icon {
        width: 80px;
        height: 80px;
        font-size: 2rem;
        border-radius: 24px;
    }

    .card-title {
        font-size: 2rem;
        margin-bottom: 16px;
    }

    .card-description {
        font-size: 1.05rem;
    }

    .card-content {
        margin: 28px 0;
    }

    .card-arrow {
        width: 56px;
        height: 56px;
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .header-barra {
        padding: 8px 15px;
    }

    .header-right {
        gap: 15px;
    }

    .user-info {
        font-size: 0.85rem;
        padding: 6px 12px;
    }

    .user-name {
        display: none; /* Ocultar nome em telas muito pequenas */
    }

    .theme-btn {
        width: 42px;
        height: 42px;
        font-size: 1rem;
    }

    .main-section {
        padding: 100px 0 30px;
    }

    .welcome-title {
        font-size: clamp(1.8rem, 10vw, 2.5rem);
        margin-bottom: 16px;
    }

    .welcome-subtitle {
        font-size: 1.1rem;
    }

    .welcome-header {
        margin-bottom: 50px;
    }

    .cards-grid {
        gap: 24px;
        padding: 0 5px;
    }

    .main-card {
        padding: 32px;
        border-radius: 24px;
    }

    .card-icon {
        width: 72px;
        height: 72px;
        font-size: 1.8rem;
        border-radius: 20px;
    }

    .card-title {
        font-size: 1.8rem;
        margin-bottom: 14px;
    }

    .card-description {
        font-size: 1rem;
        line-height: 1.5;
    }

    .card-content {
        margin: 24px 0;
    }

    .card-arrow {
        width: 52px;
        height: 52px;
        font-size: 1.1rem;
    }

    .cards-section {
        padding: 0 0 80px;
    }
}

/* Efeitos especiais para interação */
.main-card:active {
    transform: scale(0.98);
}

/* Melhorias para acessibilidade */
@media (prefers-reduced-motion: reduce) {
    .main-card,
    .welcome-header,
    .card-icon,
    .card-arrow {
        animation: none;
        transition: none;
    }

    .main-section::before {
        animation: none;
    }

    .highlight::after {
        animation: none;
    }
}

/* Estados de foco para acessibilidade */
.main-card:focus {
    outline: 3px solid var(--primary);
    outline-offset: 4px;
}

.theme-btn:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}
