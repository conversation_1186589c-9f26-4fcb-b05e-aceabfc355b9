/* =====================
   Variáveis de Cores
====================== */
:root {
    --primary: #000080;
    --primary-light: #3949ab;
    --secondary: #f8f9fa;
    --accent: #0056b3;
    --border: #e0e0e0;
    --text: #333333;
    --text-secondary: #6c757d;
    --background: #f8f9fa;
    --card-background: #fff;
    --upvote: #ff4500;
    --downvote: #7193ff;
    --shadow: rgba(0, 0, 0, 0.08);
    --hover: #f5f5f5;
}
.dark-mode {
    --primary: #4a90e2;
    --primary-light: #6ba7e7;
    --secondary: #2d2d2d;
    --accent: #64b5f6;
    --border: #404040;
    --text: #e0e0e0;
    --text-secondary: #a0a0a0;
    --background: #1a1a1a;
    --card-background: #2d2d2d;
    --upvote: #ff6b6b;
    --downvote: #7ba7ff;
    --shadow: rgba(0, 0, 0, 0.18);
    --hover: #404040;
}

body {
    font-family: 'Inter', sans-serif;
    background: var(--background);
    color: var(--text);
    margin: 0;
    min-height: 100vh;
    transition: background 0.2s, color 0.2s;
}

/* =====================
   Header Admin
====================== */
.admin-header {
    position: sticky;
    top: 0;
    z-index: 10;
    background: var(--card-background);
    border-bottom: 1px solid var(--border);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 32px;
    height: 64px;
    box-shadow: 0 2px 8px var(--shadow);
}
.admin-logo {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--primary);
    letter-spacing: 0.5px;
    text-decoration: none;
}
.admin-header-actions {
    display: flex;
    align-items: center;
    gap: 18px;
}
.icon-button {
    background: none;
    border: none;
    color: var(--primary);
    font-size: 1.3rem;
    cursor: pointer;
    padding: 6px;
    border-radius: 50%;
    transition: background 0.2s;
}
.icon-button:hover {
    background: var(--hover);
}

/* =====================
   Container e Títulos
====================== */
.container {
    max-width: 1200px;
    margin: 32px auto 0 auto;
    padding: 24px;
}
h1 {
    color: var(--primary);
    font-size: 2.1rem;
    font-weight: 700;
    margin-bottom: 32px;
    text-align: left;
}

/* =====================
   Botões
====================== */
.btn-primary {
    background: var(--primary);
    color: #fff;
    border: none;
    border-radius: 6px;
    padding: 8px 20px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    transition: background 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}
.btn-primary:hover {
    background: var(--primary-light);
}
.btn-secondary {
    background: var(--accent);
    color: #fff;
    border: none;
    border-radius: 6px;
    padding: 8px 20px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    text-decoration: none;
    transition: background 0.2s;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}
.btn-secondary:hover {
    opacity: 0.9;
}
.btn-view {
    background: #4CAF50;
}
.btn-delete {
    background: #f44336;
}
.btn-restore {
    background: #2196F3;
}
.btn-view, .btn-delete, .btn-restore {
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 8px;
    cursor: pointer;
    transition: opacity 0.2s;
    font-size: 1rem;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}
.btn-view:hover, .btn-delete:hover, .btn-restore:hover {
    opacity: 0.8;
}

/* =====================
   Cards e Grids
====================== */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 24px;
    margin-bottom: 40px;
}
.stat-card {
    background: var(--card-background);
    border-radius: 12px;
    padding: 32px 24px 24px 24px;
    text-align: center;
    box-shadow: 0 2px 8px var(--shadow);
    border: 1px solid var(--border);
    transition: box-shadow 0.2s, border 0.2s;
}
.stat-card i {
    font-size: 2.2rem;
    color: var(--primary);
    margin-bottom: 12px;
}
.stat-card h3 {
    margin: 10px 0 0 0;
    color: var(--primary);
    font-size: 1.1rem;
    font-weight: 600;
}
.stat-card p {
    font-size: 2.1rem;
    font-weight: 700;
    color: var(--text);
    margin: 8px 0 18px 0;
}

/* Categorias - Grid em duas colunas */
.categories-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(340px, 1fr));
    gap: 20px;
    margin-top: 20px;
}
.category-card {
    background: var(--card-background);
    border: 1px solid var(--border);
    border-radius: 10px;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px var(--shadow);
    transition: transform 0.3s ease;
}
.category-card:hover {
    transform: translateY(-2px);
}
.category-card.inactive {
    opacity: 0.7;
}
.category-info {
    flex: 1;
}
.category-info h3 {
    margin: 0 0 10px 0;
    color: var(--primary);
}
.category-info p {
    color: var(--accent);
    margin: 0 0 10px 0;
}
.order-badge {
    background: var(--secondary);
    color: var(--text);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9em;
}
.category-actions {
    display: flex;
    gap: 10px;
}
.btn-edit, .btn-delete {
    padding: 8px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    color: white;
    transition: opacity 0.3s ease;
}
.btn-edit {
    background: #4CAF50;
}
.btn-delete {
    background: #f44336;
}
.btn-edit:hover, .btn-delete:hover {
    opacity: 0.8;
}

/* =====================
   Tópicos - Cards e Filtros
====================== */
.filters {
    margin: 20px 0;
    padding: 15px;
    background: var(--card-background);
    border-radius: 5px;
    box-shadow: 0 2px 4px var(--shadow);
}
.filter-form {
    display: flex;
    gap: 15px;
    align-items: flex-end;
    flex-wrap: wrap;
}
.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}
.filter-group label {
    font-weight: bold;
    font-size: 0.9em;
    color: var(--text);
}
.filter-group select,
.filter-group input {
    padding: 8px;
    border: 1px solid var(--border);
    border-radius: 4px;
    background: var(--card-background);
    color: var(--text);
}
.topics-list {
    margin-top: 18px;
}
.topic-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    margin: 10px 0;
    background: var(--card-background);
    border: 1px solid var(--border);
    border-radius: 5px;
    box-shadow: 0 2px 4px var(--shadow);
}
.topic-card.inactive {
    opacity: 0.7;
}
.topic-info {
    flex: 1;
}
.topic-info h3 {
    margin: 0 0 10px 0;
    color: var(--primary);
}
.topic-meta {
    display: flex;
    gap: 15px;
    font-size: 0.9em;
    color: var(--accent);
    flex-wrap: wrap;
}
.topic-actions {
    display: flex;
    gap: 10px;
}

/* =====================
   Modais
====================== */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    justify-content: center;
    align-items: center;
    z-index: 1000;
}
.modal-content {
    background: var(--card-background);
    padding: 30px;
    border-radius: 10px;
    width: 100%;
    max-width: 500px;
    box-shadow: 0 4px 6px var(--shadow);
}
.modal h2 {
    color: var(--primary);
    margin: 0 0 20px 0;
}
.form-group {
    margin-bottom: 20px;
}
.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text);
    font-weight: 500;
}
.form-group input[type="text"],
.form-group input[type="number"],
.form-group textarea {
    width: 100%;
    padding: 8px;
    border: 1px solid var(--border);
    border-radius: 4px;
    background: var(--background);
    color: var(--text);
}
.form-group textarea {
    min-height: 100px;
    resize: vertical;
}
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 20px;
}

/* =====================
   Responsividade
====================== */
@media (max-width: 900px) {
    .container {
        padding: 10px;
    }
    .admin-header {
        padding: 0 10px;
    }
    h1 {
        font-size: 1.3rem;
    }
    .stat-card {
        padding: 18px 8px 16px 8px;
    }
    .categories-list {
        grid-template-columns: 1fr;
    }
}
@media (max-width: 700px) {
    .filter-form {
        flex-direction: column;
    }
    .filter-group {
        width: 100%;
    }
    .topic-card {
        flex-direction: column;
        gap: 15px;
    }
    .topic-meta {
        flex-direction: column;
        gap: 5px;
    }
    .topic-actions {
        width: 100%;
        justify-content: center;
    }
}
