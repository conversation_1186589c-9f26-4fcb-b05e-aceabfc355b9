<?php
session_start();
include_once("assets/config.php");
include_once("helpers.php");

// Verificar se o usuário está logado e é admin
if (!isset($_SESSION['idusuario']) || !checkAdmin($conexao, $_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit();
}

if (!isset($_GET['id'])) {
    header("Location: flashcards.php");
    exit();
}

$card_id = (int)$_GET['id'];

// Buscar o topic_id usando as relações corretas
$query_topic = "
    SELECT ft.topic_id 
    FROM appestudo.flashcard_topic_association ft 
    WHERE ft.flashcard_id = $1
    LIMIT 1";
$result_topic = pg_query_params($conexao, $query_topic, array($card_id));

if (!$result_topic) {
    header("Location: flashcards.php?error=" . urlencode("Erro ao buscar informações do card"));
    exit();
}

$topic_data = pg_fetch_assoc($result_topic);
$topic_id = $topic_data['topic_id'];

// Iniciar uma transação
pg_query($conexao, "BEGIN") or die("Não foi possível iniciar a transação");

try {
    // 1. Primeiro excluir os registros do histórico de revisão
    $query_delete_history = "
        DELETE FROM appestudo.flashcard_review_history 
        WHERE progress_id IN (
            SELECT id FROM appestudo.flashcard_progress 
            WHERE flashcard_id = $1
        )";
    $result = pg_query_params($conexao, $query_delete_history, array($card_id));
    if (!$result) {
        throw new Exception("Erro ao excluir histórico de revisões: " . pg_last_error($conexao));
    }

    // 2. Excluir registros de progresso
    $query_delete_progress = "DELETE FROM appestudo.flashcard_progress WHERE flashcard_id = $1";
    $result = pg_query_params($conexao, $query_delete_progress, array($card_id));
    if (!$result) {
        throw new Exception("Erro ao excluir progresso do flashcard: " . pg_last_error($conexao));
    }

    // 3. Excluir mapas mentais
    $query_delete_mindmaps = "DELETE FROM appestudo.flashcard_mindmaps WHERE flashcard_id = $1";
    $result = pg_query_params($conexao, $query_delete_mindmaps, array($card_id));
    if (!$result) {
        throw new Exception("Erro ao excluir mapas mentais: " . pg_last_error($conexao));
    }

    // 4. Excluir associações com tópicos
    $query_delete_topic_assoc = "DELETE FROM appestudo.flashcard_topic_association WHERE flashcard_id = $1";
    $result = pg_query_params($conexao, $query_delete_topic_assoc, array($card_id));
    if (!$result) {
        throw new Exception("Erro ao excluir associação com tópico: " . pg_last_error($conexao));
    }

    // 5. Excluir associações com decks
    $query_delete_deck_assoc = "DELETE FROM appestudo.flashcard_deck_association WHERE flashcard_id = $1";
    $result = pg_query_params($conexao, $query_delete_deck_assoc, array($card_id));
    if (!$result) {
        throw new Exception("Erro ao excluir associação com deck: " . pg_last_error($conexao));
    }

    // 6. Por fim, excluir o flashcard
    $query_delete_flashcard = "DELETE FROM appestudo.flashcards WHERE id = $1";
    $result = pg_query_params($conexao, $query_delete_flashcard, array($card_id));
    if (!$result) {
        throw new Exception("Erro ao excluir o flashcard: " . pg_last_error($conexao));
    }

    // Se chegou até aqui, commit na transação
    if (!pg_query($conexao, "COMMIT")) {
        throw new Exception("Erro ao finalizar a transação: " . pg_last_error($conexao));
    }

    // Redirecionar para ver_cards.php com o topic_id e mensagem de sucesso
    header("Location: ver_cards.php?topico=" . $topic_id . "&message=" . urlencode("Card excluído com sucesso"));
    exit();

} catch (Exception $e) {
    // Em caso de erro, fazer rollback e registrar o erro
    pg_query($conexao, "ROLLBACK");
    
    // Log do erro
    error_log("Erro ao excluir card #$card_id: " . $e->getMessage());
    
    // Redirecionar para ver_cards.php com o topic_id e mensagem de erro
    header("Location: ver_cards.php?topico=" . $topic_id . "&error=" . urlencode("Erro ao excluir o card: " . $e->getMessage()));
    exit();
}