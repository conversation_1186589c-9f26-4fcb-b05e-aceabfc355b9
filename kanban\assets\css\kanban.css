:root {
    --primary-color: #00008B; /* Azul mais suave */
    --paper-color: #FFFFFF;
    --secondary-color: #3949ab; /* Verde-água como secundária */
    --background-color: #F4F7FA; /* Fundo mais claro */
    --text-color: #333A45; /* Texto um pouco mais escuro */
    --border-color: #D8DEE4;
    --success-color: #28a745; /* Verde sucesso */
    --warning-color: #ffc107; /* Amarelo aviso */
    --danger-color: #dc3545; /* Vermelho perigo */
    --shadow-color: rgba(74, 144, 226, 0.15); /* Sombra baseada no azul primário */
}

body {
    font-family: 'Quicksand', sans-serif;
    background-color: var(--background-color);
    color: var(--text-color);
    margin: 0;
    padding: 25px; /* Aumentar padding geral */
    min-height: 100vh;
    line-height: 1.6; /* Melhorar legibilidade */
}

.kanban-container {
    max-width: 1400px;
    margin: 0 auto;
}

.kanban-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: var(--paper-color);
    border-radius: 10px;
    box-shadow: 0 4px 12px var(--shadow-color); /* Sombra mais pronunciada */
}

.kanban-header h1 {
    margin: 0;
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 12px; /* Aumentar gap */
    font-weight: 700; /* Fonte mais forte */
}

.btn-add {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%); /* Gradiente no botão */
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px; /* Bordas mais arredondadas */
    cursor: pointer;
    font-family: 'Quicksand', sans-serif;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-add:hover {
    opacity: 0.9; /* Leve fade */
    transform: translateY(-3px); /* Efeito de levantar mais pronunciado */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.kanban-board {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    padding: 20px 0;
    align-items: start; /* Impede que as colunas se estiquem para a altura da maior coluna */
}

.kanban-column {
    background: var(--paper-color);
    border-radius: 12px; /* Bordas mais arredondadas */
    padding: 20px;
    box-shadow: 0 3px 8px var(--shadow-color); /* Sombra mais suave */
    display: flex; /* Usar flex para melhor controle */
    flex-direction: column; /* Empilhar header e lista */
}

.column-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px; /* Aumentar padding */
    border-bottom: 1px solid var(--border-color); /* Borda mais sutil */
}

.column-header h2 {
    margin: 0;
    font-size: 1.15rem; /* Ajustar tamanho */
    color: var(--primary-color);
    display: flex;
    align-items: center;
    gap: 10px; /* Aumentar gap */
    font-weight: 600;
}

.task-count {
    background: var(--primary-color); /* Fundo com cor primária */
    color: white; /* Texto branco */
    padding: 5px 10px; /* Ajustar padding */
    border-radius: 15px; /* Mais arredondado */
    font-size: 0.85rem; /* Ajustar tamanho */
    font-weight: 600;
    min-width: 25px; /* Garantir largura mínima */
    text-align: center;
}

.task-list {
    min-height: 200px;
    flex-grow: 1; /* Permitir que a lista cresça */
    padding-top: 10px; /* Espaço acima do primeiro card */
}

.task-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); /* Gradiente sutil no card */
    border-radius: 8px; /* Bordas mais arredondadas */
    padding: 18px; /* Aumentar padding */
    margin-bottom: 15px; /* Aumentar espaçamento */
    border-left: 5px solid var(--primary-color); /* Borda esquerda mais grossa */
    box-shadow: 0 2px 5px var(--shadow-color);
    cursor: grab; /* Cursor de agarrar */
    transition: all 0.3s ease;
    position: relative; /* Para posicionar ações */
}
.task-card:active {
    cursor: grabbing; /* Cursor ao arrastar */
}

.task-card:hover {
    transform: translateY(-3px) scale(1.01); /* Efeito de levantar e escalar */
    box-shadow: 0 5px 10px var(--shadow-color);
}

.task-card h3 {
    margin: 0 0 12px 0; /* Aumentar margem inferior */
    font-size: 1.05rem; /* Aumentar tamanho da fonte */
    font-weight: 600;
    color: var(--text-color);
}

.task-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12px; /* Aumentar gap */
    margin-top: 12px; /* Aumentar margem superior */
    align-items: center;
    font-size: 0.85rem;
    color: #555;
}

.task-priority, .task-date, .task-deadline { /* Aplicar a todos os metadados */
    display: inline-flex;
    align-items: center;
    gap: 6px; /* Aumentar gap */
}
.task-priority i, .task-date i, .task-deadline i {
    font-size: 0.9em;
}

.task-actions {
    position: absolute; /* Posicionar no canto */
    top: 10px;
    right: 10px;
    display: flex;
    gap: 5px; /* Diminuir gap das ações */
    opacity: 0; /* Esconder por padrão */
    transition: opacity 0.3s ease;
}

.task-card:hover .task-actions {
    opacity: 1; /* Mostrar no hover do card */
}

.task-actions button {
    background: none;
    border: none;
    cursor: pointer;
    color: var(--text-color);
    opacity: 0.6;
    transition: opacity 0.3s ease;
}

.task-actions button {
    background: rgba(255, 255, 255, 0.8); /* Fundo semi-transparente */
    border: 1px solid var(--border-color);
    border-radius: 50%; /* Botões redondos */
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: var(--text-color);
    transition: all 0.2s ease;
    font-size: 0.8rem;
}

.task-actions button:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: scale(1.1);
}

/* Estilos para os prazos (mantendo a lógica, ajustando cores) */
.task-deadline {
    /* display, align-items, gap já definidos em .task-meta */
    padding: 4px 10px; /* Ajustar padding */
    border-radius: 15px; /* Mais arredondado */
    font-size: 0.8rem;
    font-weight: 600; /* Mais forte */
    margin-top: 0; /* Remover margem, já controlada pelo gap do flex */
}

.deadline-expired,
.deadline-today {
    color: var(--danger-color) !important;
    background-color: rgba(220, 53, 69, 0.1); /* Fundo leve */
}

.deadline-expired i,
.deadline-today i {
    color: var(--danger-color) !important;
}

.deadline-soon {
    color: var(--warning-color) !important;
    background-color: rgba(255, 193, 7, 0.1); /* Fundo leve */
}

.deadline-soon i {
    color: var(--warning-color) !important;
}

.deadline-future {
    color: var(--success-color) !important;
    background-color: rgba(40, 167, 69, 0.1); /* Fundo leve */
}

.deadline-future i {
    color: var(--success-color) !important;
}

/* Bordas para cartões com prazos (usando novas cores) */
.task-card:has(.deadline-expired),
.task-card:has(.deadline-today) {
    border-left-color: var(--danger-color);
}

.task-card:has(.deadline-soon) {
    border-left-color: var(--warning-color);
}

.task-card:has(.deadline-future) {
    border-left-color: var(--success-color);
}

/* Estilos para o TinyMCE */
.tox-tinymce {
    border-radius: 5px !important;
    border: 1px solid var(--border-color) !important;
}

.tox .tox-toolbar__group {
    border-color: var(--border-color) !important;
}

.tox .tox-tbtn {
    color: var(--text-color) !important;
}

.tox .tox-tbtn:hover {
    background-color: var(--background-color) !important;
}

/* Estilos para a descrição da tarefa */
.task-description {
    margin-bottom: 10px;
    font-size: 0.9rem;
    line-height: 1.5;
    color: var(--text-color);
    max-height: 100px; /* Limitar altura */
    overflow: hidden;
    position: relative;
    word-break: break-word; /* Quebrar palavras longas */
}

/* Adicionar fade-out no final do texto truncado */
.task-description::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30px;
    background: linear-gradient(transparent, white);
    pointer-events: none;
}

/* Garantir que imagens e tabelas não vazem */
.task-description img,
.task-description table {
    max-width: 100%;
    height: auto;
}

/* Garantir que listas fiquem dentro do card */
.task-description ul,
.task-description ol {
    padding-left: 1.5em;
    margin-bottom: 0.5em;
}

/* Ajustar tamanho dos títulos dentro dos cards */
.task-description h1 {
    font-size: 1.2rem;
}

.task-description h2 {
    font-size: 1.1rem;
}

.task-description h3,
.task-description h4,
.task-description h5,
.task-description h6 {
    font-size: 1rem;
}

.task-description p {
    margin-bottom: 0.5em;
}

.task-description ul, 
.task-description ol {
    padding-left: 1.5em;
    margin-bottom: 0.5em;
}

.task-description h1, 
.task-description h2, 
.task-description h3 {
    margin-top: 0.5em;
    margin-bottom: 0.3em;
}

/* Estilos para o textarea de descrição */
textarea#descricao {
    resize: vertical;
    min-height: 100px;
    font-family: inherit;
    line-height: 1.5;
}

@media (max-width: 768px) {
    .kanban-board {
        grid-template-columns: 1fr;
    }
}

/* Estilos para o modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal {
    background: white;
    border-radius: 10px;
    padding: 20px;
    width: 90%;
    max-width: 650px; /* Aumentar largura máxima */
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2); /* Sombra mais forte */
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px; /* Aumentar margem */
    padding-bottom: 15px; /* Adicionar padding inferior */
    border-bottom: 1px solid var(--border-color); /* Adicionar borda */
}

.modal-header h2 {
    margin: 0;
    font-size: 1.6rem; /* Aumentar fonte */
    color: var(--primary-color); /* Usar cor primária */
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.8rem; /* Aumentar tamanho */
    cursor: pointer;
    color: #999; /* Cor mais suave */
    transition: color 0.2s ease;
}
.modal-close:hover {
    color: var(--danger-color); /* Cor de perigo no hover */
}

.form-modern {
    display: flex;
    flex-direction: column;
    gap: 20px; /* Aumentar gap */
}

.form-group {
    margin-bottom: 0; /* Remover margem, controlada pelo gap do flex */
}

.form-group label {
    display: block;
    margin-bottom: 8px; /* Aumentar margem */
    color: #444; /* Cor mais escura */
    font-weight: 600; /* Mais forte */
    font-size: 0.9rem;
}

.form-control {
    width: 100%; /* Manter largura total */
    padding: 12px 15px; /* Aumentar padding */
    border: 1px solid var(--border-color);
    border-radius: 6px; /* Bordas mais arredondadas */
    font-size: 1rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    box-sizing: border-box; /* Incluir padding e borda na largura */
}
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2); /* Sombra de foco */
    outline: none;
}

.form-row {
    display: flex;
    gap: 20px; /* Aumentar gap */
}

.col-md-6 {
    flex: 1;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px; /* Aumentar gap */
    margin-top: 30px; /* Aumentar margem */
    padding-top: 20px; /* Adicionar padding */
    border-top: 1px solid var(--border-color); /* Adicionar borda */
}

.btn {
    padding: 10px 20px; /* Aumentar padding */
    border: none;
    border-radius: 6px; /* Bordas mais arredondadas */
    cursor: pointer;
    font-weight: 600; /* Mais forte */
    display: inline-flex; /* Mudar para inline-flex */
    align-items: center;
    gap: 8px; /* Aumentar gap */
    transition: all 0.3s ease;
    font-size: 0.9rem;
    text-transform: uppercase; /* Texto em maiúsculas */
    letter-spacing: 0.5px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}
.btn-primary:hover {
    background-color: #3a7bc8; /* Cor primária mais escura no hover */
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}
.btn-secondary:hover {
    background-color: #5a6268; /* Cor secundária mais escura no hover */
    transform: translateY(-2px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.btn i {
    font-size: 0.9em;
}

/* Estilos personalizados para o SweetAlert2 */
.swal-custom-popup {
    font-family: 'Roboto', sans-serif;
    padding: 20px;
    border-radius: 15px !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

.swal-custom-title {
    font-size: 1.5rem !important;
    color: #333 !important;
    font-weight: 600 !important;
}

.swal-custom-content {
    font-size: 1.1rem !important;
    color: #666 !important;
}

.swal-custom-confirm {
    padding: 10px 24px !important;
    font-weight: 500 !important;
    letter-spacing: 0.5px !important;
    text-transform: uppercase !important;
    transition: all 0.3s ease !important;
}

.swal-custom-confirm:hover {
    transform: translateY(-2px) !important;
}

.swal-custom-cancel {
    padding: 10px 24px !important;
    font-weight: 500 !important;
    letter-spacing: 0.5px !important;
    text-transform: uppercase !important;
    transition: all 0.3s ease !important;
}

.swal-custom-cancel:hover {
    transform: translateY(-2px) !important;
}

/* Estilos para expandir/recolher colunas */
.header-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.collapse-icon {
    cursor: pointer;
    font-size: 0.9em;
    color: var(--primary-color);
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.collapse-icon:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

/* Estilo para a lista de tarefas quando a coluna está recolhida */
.kanban-column.coluna-recolhida > .task-list {
    display: none !important; /* Teste de volta com !important */
    height: 0 !important;
    min-height: 0 !important;
    overflow: hidden !important;
    padding: 0 !important;
    margin: 0 !important;
    border: none !important;
    visibility: hidden !important;
}

.kanban-column.coluna-recolhida {
    height: auto !important; /* Testar com !important aqui também */
    min-height: 0 !important; /* Garante que a coluna possa encolher */
    /* Você pode querer um padding-bottom menor aqui se o padding original da coluna for grande */
    /* padding-bottom: 10px; */ 
}




