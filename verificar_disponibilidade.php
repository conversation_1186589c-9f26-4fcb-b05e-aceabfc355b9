<?php
session_start();

// Configurar headers de segurança
header('Content-Type: application/json');
header('X-Frame-Options: DENY');
header('X-Content-Type-Options: nosniff');
header('X-XSS-Protection: 1; mode=block');
header('Referrer-Policy: strict-origin-when-cross-origin');

include 'conexao_POST.php';

// Implementar rate limiting
$now = time();
$rate_limit_key = 'verificar_disponibilidade_' . $_SERVER['REMOTE_ADDR'];
$rate_limit_window = 60; // 60 segundos
$max_requests = 10;      // 10 requisições por minuto

if (!isset($_SESSION[$rate_limit_key])) {
    $_SESSION[$rate_limit_key] = [
        'count' => 0,
        'window_start' => $now
    ];
}

if ($now - $_SESSION[$rate_limit_key]['window_start'] > $rate_limit_window) {
    $_SESSION[$rate_limit_key] = [
        'count' => 1,
        'window_start' => $now
    ];
} else {
    $_SESSION[$rate_limit_key]['count']++;
    if ($_SESSION[$rate_limit_key]['count'] > $max_requests) {
        http_response_code(429); // Too Many Requests
        echo json_encode(['erro' => 'Muitas requisições. Tente novamente mais tarde.']);
        exit;
    }
}

// Processar dados recebidos
try {
    $data = json_decode(file_get_contents('php://input'), true);

    if (!isset($data['tipo']) || !isset($data['valor'])) {
        throw new Exception('Parâmetros inválidos');
    }

    $tipo = $data['tipo'];
    $valor = trim($data['valor']);

    // Validar tipo de forma segura
    $tipos_permitidos = ['usuario', 'email'];
    if (!in_array($tipo, $tipos_permitidos)) {
        throw new Exception('Tipo inválido');
    }

    // Sanitizar valor
    if ($tipo === 'email') {
        $valor = filter_var($valor, FILTER_SANITIZE_EMAIL);
        if (!filter_var($valor, FILTER_VALIDATE_EMAIL) && !empty($valor)) {
            throw new Exception('Formato de email inválido');
        }
    } else {
        // Sanitizar nome de usuário
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $valor) && !empty($valor)) {
            throw new Exception('Formato de usuário inválido');
        }
    }

    // Usar consulta preparada com nome da coluna seguro
    $coluna = ($tipo === 'usuario') ? 'usuario' : 'email';
    $sql = "SELECT COUNT(*) FROM usuario WHERE $coluna = $1";
    $result = pg_query_params($conexao, $sql, array($valor));
    
    if (!$result) {
        throw new Exception('Erro na consulta ao banco de dados');
    }
    
    $count = pg_fetch_result($result, 0, 0);

    // Resposta mais genérica para reduzir vazamento de informações
    // Ainda mantém a funcionalidade original, mas com mensagens mais neutras
    echo json_encode([
        'disponivel' => $count == 0,
        'mensagem' => $count == 0 ? 
            ($tipo === 'usuario' ? 'Disponível para uso' : 'Disponível para uso') : 
            ($tipo === 'usuario' ? 'Indisponível para uso' : 'Indisponível para uso')
    ]);

} catch (Exception $e) {
    http_response_code(400); // Bad Request
    echo json_encode(['erro' => $e->getMessage()]);
}
?>
