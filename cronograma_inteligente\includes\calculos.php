<?php
class Calculos {
    private $prova;
    private $total_conteudos;
    private $conexao;
    private $usuario_id;
    private $dias_estudo;

    public function __construct($prova, $total_conteudos, $conexao) {
        $this->prova = $prova;
        $this->total_conteudos = $total_conteudos;
        $this->conexao = $conexao;
        $this->usuario_id = $_SESSION['idusuario'];
        $this->carregarDiasEstudo();
    }

    private function carregarDiasEstudo() {
        $query = "SELECT dia_semana, horas_estudo 
                 FROM appestudo.usuario_dias_estudo 
                 WHERE usuario_id = $this->usuario_id 
                 AND ativo = true
                 ORDER BY dia_semana";
        $result = pg_query($this->conexao, $query);
        
        $this->dias_estudo = [];
        while ($row = pg_fetch_assoc($result)) {
            $this->dias_estudo[$row['dia_semana']] = floatval($row['horas_estudo']); // Convertendo para float
        }
        
        // Se não houver dias configurados, usar padrão (seg-sex, 4h por dia)
        if (empty($this->dias_estudo)) {
            for ($i = 1; $i <= 5; $i++) {
                $this->dias_estudo[$i] = 3.0;
            }
        }
    }

    public function getDiasParaProva() {
        $hoje = new DateTime();
        $data_prova = new DateTime($this->prova['data_prova']);
        
        // Zera as horas para comparação apenas das datas
        $hoje->setTime(0, 0, 0);
        $data_prova->setTime(0, 0, 0);
        
        // Calcula a diferença em dias
        $interval = $hoje->diff($data_prova);
        
        // Se a data da prova já passou, retorna 0
        if ($interval->invert) {
            return 0;
        }
        
        return $interval->days;
    }

    public function getTotalDias() {
        $data_inicio = new DateTime($this->prova['data_inicio_estudo']);
        $data_prova = new DateTime($this->prova['data_prova']);
        
        // Zera as horas para comparação apenas das datas
        $data_inicio->setTime(0, 0, 0);
        $data_prova->setTime(0, 0, 0);
        
        // Calcula a diferença em dias
        $interval = $data_inicio->diff($data_prova);
        
        // Se a data de início é depois da data da prova, retorna 0
        if ($interval->invert) {
            return 0;
        }
        
        return $interval->days;
    }
    
    public function getDiasUteis() {
        // Data de início e fim
        $data_inicio = new DateTime($this->prova['data_inicio_estudo']);
        $data_prova = new DateTime($this->prova['data_prova']);
        $data_prova->modify('-1 day'); // Exclui o dia da prova
        
        $dias_uteis = 0;
        $data_temp = clone $data_inicio;
        
        // Conta os dias de estudo até um dia antes da prova
        while ($data_temp <= $data_prova) {
            $dia_semana = (int)$data_temp->format('N');
            if (isset($this->dias_estudo[$dia_semana])) {
                $dias_uteis++;
            }
            $data_temp->modify('+1 day');
        }
        
        return $dias_uteis;
    }
    

    public function getHorasTotais() {
        $data_inicio = new DateTime($this->prova['data_inicio_estudo']);
        $data_prova = new DateTime($this->prova['data_prova']);
        $data_limite = clone $data_prova;
        $data_limite->modify('-1 day');
        
        $horas_totais = 0;
        $data_temp = clone $data_inicio;
        
        // Soma as horas até um dia antes da prova
        while ($data_temp <= $data_limite) {
            $dia_semana = (int)$data_temp->format('N');
            if (isset($this->dias_estudo[$dia_semana])) {
                $horas_totais += $this->dias_estudo[$dia_semana];
            }
            $data_temp->modify('+1 day');
        }
        
        return $horas_totais;
    }

    public function getHorasPorDia($dia_semana) {
        return isset($this->dias_estudo[$dia_semana]) ? $this->dias_estudo[$dia_semana] : 0;
    }

    public function getCardsPorDia($dia) {
        $total_horas_semana = array_sum($this->dias_estudo);
        $horas_dia = isset($this->dias_estudo[$dia]) ? $this->dias_estudo[$dia] : 0;
        
        if ($total_horas_semana > 0 && $horas_dia > 0) {
            $proporcao = $horas_dia / $total_horas_semana;
            return ceil(($this->total_conteudos / $this->getDiasUteis()) * ($proporcao * count($this->dias_estudo)));
        }
        
        return 0;
    }

    
    
    public function ajustarUltimoDia($cards_distribuidos) {
        $total_distribuido = array_sum($cards_distribuidos);
        $restantes = $this->total_conteudos - $total_distribuido;
    
        if ($restantes > 0) {
            $data_inicio = new DateTime($this->prova['data_inicio_estudo']);
            $data_prova = new DateTime($this->prova['data_prova']);
    
            // Filtra os dias válidos (antes da data da prova)
            $dias_validos = [];
            foreach ($this->dias_estudo as $dia => $horas) {
                $data_temp = clone $data_inicio;
                while ($data_temp->format('N') != $dia) {
                    $data_temp->modify('+1 day');
                }
    
                if ($horas > 0 && $data_temp < $data_prova) {
                    $dias_validos[$dia] = $horas;
                }
            }
    
            if (!empty($dias_validos)) {
                arsort($dias_validos); // Ordena os dias por horas de estudo (decrescente)
                $total_horas = array_sum($dias_validos);
    
                foreach ($dias_validos as $dia => $horas) {
                    if ($restantes <= 0) break;
    
                    $proporcao = $horas / $total_horas;
                    $adicionar = min(ceil($restantes * $proporcao), $restantes);
    
                    if (isset($cards_distribuidos[$dia - 1])) {
                        $cards_distribuidos[$dia - 1] += $adicionar;
                        $restantes -= $adicionar;
                    }
                }
            }
        }
    
        return $cards_distribuidos;
    }
    
    public function getTotalSemanas() {
        $data_inicio = new DateTime($this->prova['data_inicio_estudo']);
        $data_prova = new DateTime($this->prova['data_prova']);
        
        // Lista as semanas envolvidas
        $semanas = [];
        $data_atual = clone $data_inicio;
        
        // Ajusta para o início da semana atual
        $primeira_segunda = clone $data_inicio;
        if ($primeira_segunda->format('N') != 1) { // Se não for segunda-feira
            $primeira_segunda->modify('this week monday');
        }
        
        // Enquanto não chegar na semana da prova
        while ($data_atual <= $data_prova) {
            $inicio_semana = clone $data_atual;
            $inicio_semana->modify('this week monday');
            
            $fim_semana = clone $inicio_semana;
            $fim_semana->modify('this week sunday');
            
            $semana_key = $inicio_semana->format('W');
            
            // Verifica se tem dias de estudo nesta semana
            $tem_dias_estudo = false;
            $data_temp = clone $inicio_semana;
            while ($data_temp <= $fim_semana) {
                $dia_semana = (int)$data_temp->format('N');
                if (isset($this->dias_estudo[$dia_semana]) && 
                    $data_temp >= $data_inicio && 
                    $data_temp <= $data_prova) {
                    $tem_dias_estudo = true;
                    break;
                }
                $data_temp->modify('+1 day');
            }
            
            if ($tem_dias_estudo && !isset($semanas[$semana_key])) {
                $semanas[$semana_key] = true;
            }
            
            $data_atual->modify('+1 week');
        }
        
        // Retorna o número de semanas
        return count($semanas);
    }

    public function getDiasEstudo() {
        return array_keys($this->dias_estudo);
    }

    public function getNomeDiaSemana($numero_dia) {
        $dias = [
            1 => 'Segunda-feira',
            2 => 'Terça-feira',
            3 => 'Quarta-feira',
            4 => 'Quinta-feira',
            5 => 'Sexta-feira',
            6 => 'Sábado',
            7 => 'Domingo'
        ];
        return $dias[$numero_dia] ?? '';
    }
}
