/**
 * Dashboard de Estatísticas Avançadas
 * Sistema completo de visualização de dados por lei
 */

class DashboardEstatisticas {
    constructor() {
        this.dados = {};
        this.graficos = {};
        this.intervalos = {};
        this.leiAtual = null;
        
        this.init();
    }
    
    init() {
        this.criarInterface();
        this.bindEventos();
        this.carregarDados();
    }
    
    /**
     * Cria a interface do dashboard
     */
    criarInterface() {
        // Verificar se já existe
        if (document.getElementById('dashboardEstatisticas')) return;
        
        const dashboard = document.createElement('div');
        dashboard.id = 'dashboardEstatisticas';
        dashboard.className = 'dashboard-estatisticas';
        dashboard.innerHTML = `
            <div class="dashboard-header">
                <h2><i class="fas fa-chart-bar"></i> Dashboard de Estatísticas</h2>
                <div class="dashboard-controls">
                    <select id="filtroLei" class="form-select">
                        <option value="">Todas as Leis</option>
                    </select>
                    <select id="filtroPeriodo" class="form-select">
                        <option value="7">Última Semana</option>
                        <option value="30" selected>Último Mês</option>
                        <option value="90">Últimos 3 Meses</option>
                        <option value="365">Último Ano</option>
                    </select>
                    <button id="btnAtualizarDados" class="btn btn-primary">
                        <i class="fas fa-sync-alt"></i> Atualizar
                    </button>
                    <button id="btnFecharDashboard" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            
            <div class="dashboard-content">
                <!-- Resumo Geral -->
                <div class="resumo-geral">
                    <div class="card-resumo" id="cardArtigosLidos">
                        <div class="card-icone" style="background: #3498db">
                            <i class="fas fa-book-open"></i>
                        </div>
                        <div class="card-info">
                            <h3>Artigos Lidos</h3>
                            <div class="card-valor">0</div>
                            <div class="card-meta">de 0 disponíveis</div>
                        </div>
                    </div>
                    
                    <div class="card-resumo" id="cardRevisoes">
                        <div class="card-icone" style="background: #e74c3c">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="card-info">
                            <h3>Revisões</h3>
                            <div class="card-valor">0</div>
                            <div class="card-meta">0 pendentes</div>
                        </div>
                    </div>
                    
                    <div class="card-resumo" id="cardDominados">
                        <div class="card-icone" style="background: #27ae60">
                            <i class="fas fa-trophy"></i>
                        </div>
                        <div class="card-info">
                            <h3>Dominados</h3>
                            <div class="card-valor">0</div>
                            <div class="card-meta">0% do total</div>
                        </div>
                    </div>
                    
                    <div class="card-resumo" id="cardTempoEstudo">
                        <div class="card-icone" style="background: #f39c12">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="card-info">
                            <h3>Tempo de Estudo</h3>
                            <div class="card-valor">0h 0m</div>
                            <div class="card-meta">esta semana</div>
                        </div>
                    </div>
                </div>
                
                <!-- Gráficos -->
                <div class="dashboard-graficos">
                    <div class="grafico-container">
                        <div class="grafico-header">
                            <h3>Progresso por Lei</h3>
                        </div>
                        <canvas id="graficoProgressoLeis"></canvas>
                    </div>
                    
                    <div class="grafico-container">
                        <div class="grafico-header">
                            <h3>Distribuição de Status</h3>
                        </div>
                        <canvas id="graficoDistribuicaoStatus"></canvas>
                    </div>
                    
                    <div class="grafico-container">
                        <div class="grafico-header">
                            <h3>Atividade ao Longo do Tempo</h3>
                        </div>
                        <canvas id="graficoAtividadeTempo"></canvas>
                    </div>
                    
                    <div class="grafico-container">
                        <div class="grafico-header">
                            <h3>Ranking de Leis</h3>
                        </div>
                        <div id="rankingLeis" class="ranking-container"></div>
                    </div>
                </div>
                
                <!-- Detalhes por Lei -->
                <div class="detalhes-leis" id="detalhesLeis">
                    <!-- Será preenchido dinamicamente -->
                </div>
            </div>
            
            <div class="dashboard-loading" id="dashboardLoading">
                <div class="loading-spinner"></div>
                <p>Carregando estatísticas...</p>
            </div>
        `;
        
        document.body.appendChild(dashboard);
    }
    
    /**
     * Vincula eventos do dashboard
     */
    bindEventos() {
        // Fechar dashboard
        document.getElementById('btnFecharDashboard').addEventListener('click', () => {
            this.fechar();
        });
        
        // Atualizar dados
        document.getElementById('btnAtualizarDados').addEventListener('click', () => {
            this.carregarDados();
        });
        
        // Filtros
        document.getElementById('filtroLei').addEventListener('change', () => {
            this.aplicarFiltros();
        });
        
        document.getElementById('filtroPeriodo').addEventListener('change', () => {
            this.aplicarFiltros();
        });
        
        // Fechar com ESC
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isAberto()) {
                this.fechar();
            }
        });
    }
    
    /**
     * Carrega todos os dados necessários
     */
    async carregarDados() {
        this.mostrarLoading(true);
        
        try {
            // Carregar dados em paralelo
            const [dashboard, comparativo, ranking, tempoEstudo] = await Promise.all([
                this.carregarDashboard(),
                this.carregarComparativo(),
                this.carregarRanking(),
                this.carregarTempoEstudo()
            ]);
            
            this.dados = {
                dashboard,
                comparativo,
                ranking,
                tempoEstudo
            };
            
            this.atualizarInterface();
            this.criarGraficos();
            
        } catch (error) {
            console.error('Erro ao carregar dados:', error);
            this.mostrarErro('Erro ao carregar estatísticas');
        } finally {
            this.mostrarLoading(false);
        }
    }
    
    /**
     * Carrega dados do dashboard principal
     */
    async carregarDashboard() {
        const filtros = this.obterFiltros();
        const params = new URLSearchParams({
            acao: 'dashboard',
            lei: filtros.lei,
            periodo: filtros.periodo
        });

        const response = await fetch(`./api/estatisticas-simples.php?${params}`);
        const data = await response.json();

        if (!data.sucesso) {
            throw new Error(data.erro || 'Erro ao carregar dashboard');
        }

        return data.dashboard;
    }

    /**
     * Carrega dados comparativos
     */
    async carregarComparativo() {
        const filtros = this.obterFiltros();
        const params = new URLSearchParams({
            acao: 'comparativo',
            lei: filtros.lei,
            periodo: filtros.periodo
        });

        const response = await fetch(`./api/estatisticas-simples.php?${params}`);
        const data = await response.json();

        if (!data.sucesso) {
            throw new Error(data.erro || 'Erro ao carregar comparativo');
        }

        return data.comparativo;
    }

    /**
     * Carrega ranking de leis
     */
    async carregarRanking() {
        const filtros = this.obterFiltros();
        const params = new URLSearchParams({
            acao: 'ranking',
            lei: filtros.lei,
            periodo: filtros.periodo
        });

        const response = await fetch(`./api/estatisticas-simples.php?${params}`);
        const data = await response.json();

        if (!data.sucesso) {
            throw new Error(data.erro || 'Erro ao carregar ranking');
        }

        return data.rankings;
    }

    /**
     * Carrega dados de tempo de estudo
     */
    async carregarTempoEstudo() {
        const filtros = this.obterFiltros();
        const params = new URLSearchParams({
            acao: 'tempo_estudo',
            lei: filtros.lei,
            periodo: filtros.periodo
        });

        const response = await fetch(`./api/estatisticas-simples.php?${params}`);
        const data = await response.json();

        if (!data.sucesso) {
            throw new Error(data.erro || 'Erro ao carregar tempo de estudo');
        }

        return data.tempo_estudo;
    }
    
    /**
     * Atualiza interface com os dados carregados
     */
    atualizarInterface() {
        if (!this.dados.dashboard) return;

        // Debug: verificar dados recebidos
        console.log('📊 Dashboard: Atualizando interface...');
        console.log('📋 Total de leis para filtro:', this.dados.dashboard.leis?.length || 0);
        console.log('📊 Total de leis com dados:', this.dados.dashboard.leis_dados?.length || 0);

        if (this.dados.dashboard.leis_dados) {
            console.log('🎯 Dados filtrados:', this.dados.dashboard.leis_dados.map(l => l.codigo));
        }

        this.atualizarResumoGeral();
        this.atualizarFiltroLeis();
        this.atualizarDetalhesLeis();
    }
    
    /**
     * Atualiza cards do resumo geral
     */
    atualizarResumoGeral() {
        // Usar dados filtrados se disponíveis, senão usar todos
        const leis = this.dados.dashboard.leis_dados || this.dados.dashboard.leis;

        // Calcular resumo baseado nos dados filtrados
        const resumo = this.calcularResumoGeral(leis);

        console.log('📊 Dashboard: Resumo calculado para', leis.length, 'lei(s):', resumo);

        // Card Artigos Lidos
        const cardArtigos = document.getElementById('cardArtigosLidos');
        if (cardArtigos) {
            cardArtigos.querySelector('.card-valor').textContent = resumo.total_artigos_lidos;
            cardArtigos.querySelector('.card-meta').textContent = `de ${resumo.total_artigos_disponiveis} disponíveis`;
        }

        // Card Revisões
        const cardRevisoes = document.getElementById('cardRevisoes');
        if (cardRevisoes) {
            cardRevisoes.querySelector('.card-valor').textContent = resumo.total_revisoes;
            cardRevisoes.querySelector('.card-meta').textContent = `${resumo.total_pendentes} pendentes`;
        }

        // Card Dominados
        const cardDominados = document.getElementById('cardDominados');
        if (cardDominados) {
            const percentualDominados = resumo.total_artigos_disponiveis > 0 ?
                Math.round((resumo.total_dominados / resumo.total_artigos_disponiveis) * 100) : 0;
            cardDominados.querySelector('.card-valor').textContent = resumo.total_dominados;
            cardDominados.querySelector('.card-meta').textContent = `${percentualDominados}% do total`;
        }
        
        // Card Tempo de Estudo
        const cardTempo = document.getElementById('cardTempoEstudo');
        if (cardTempo) {
            const tempoFormatado = this.formatarTempo(resumo.tempo_total_estudo || 0);
            cardTempo.querySelector('.card-valor').textContent = tempoFormatado;
        }
    }

    /**
     * Calcula resumo geral baseado nas leis fornecidas
     */
    calcularResumoGeral(leis) {
        if (!leis || leis.length === 0) {
            return {
                total_artigos_lidos: 0,
                total_artigos_disponiveis: 0,
                total_revisoes: 0,
                total_pendentes: 0,
                total_dominados: 0,
                total_favoritos: 0,
                total_listas: 0,
                total_anotacoes: 0,
                percentual_geral: 0
            };
        }

        const total_artigos_lidos = leis.reduce((sum, lei) => sum + (lei.artigos_lidos || 0), 0);
        const total_artigos_disponiveis = leis.reduce((sum, lei) => sum + (lei.total_artigos || 0), 0);
        const total_revisoes = leis.reduce((sum, lei) => sum + (lei.revisao?.total || 0), 0);
        const total_pendentes = leis.reduce((sum, lei) => sum + (lei.revisao?.pendentes || 0), 0);
        const total_dominados = leis.reduce((sum, lei) => sum + (lei.revisao?.dominados || 0), 0);
        const total_favoritos = leis.reduce((sum, lei) => sum + (lei.favoritos || 0), 0);
        const total_listas = leis.reduce((sum, lei) => sum + (lei.listas || 0), 0);
        const total_anotacoes = leis.reduce((sum, lei) => sum + (lei.anotacoes || 0), 0);
        const tempo_total_estudo = leis.reduce((sum, lei) => sum + (lei.tempo_estudo?.total_segundos || 0), 0);

        const percentual_geral = total_artigos_disponiveis > 0 ?
            Math.round((total_artigos_lidos / total_artigos_disponiveis) * 100) : 0;

        return {
            total_artigos_lidos,
            total_artigos_disponiveis,
            total_revisoes,
            total_pendentes,
            total_dominados,
            total_favoritos,
            total_listas,
            total_anotacoes,
            tempo_total_estudo,
            percentual_geral
        };
    }

    /**
     * Atualiza filtro de leis
     */
    atualizarFiltroLeis() {
        const filtro = document.getElementById('filtroLei');
        const valorAtual = filtro.value; // Preservar seleção atual

        // Limpar opções existentes (exceto "Todas as Leis")
        while (filtro.children.length > 1) {
            filtro.removeChild(filtro.lastChild);
        }

        // Adicionar leis
        this.dados.dashboard.leis.forEach(lei => {
            const option = document.createElement('option');
            option.value = lei.codigo;
            option.textContent = lei.nome;
            filtro.appendChild(option);
        });

        // Restaurar seleção se ainda existir
        if (valorAtual && filtro.querySelector(`option[value="${valorAtual}"]`)) {
            filtro.value = valorAtual;
        }
    }
    
    /**
     * Formata tempo em segundos para formato legível
     */
    formatarTempo(segundos) {
        if (segundos < 60) return `${segundos}s`;
        if (segundos < 3600) return `${Math.floor(segundos / 60)}m`;
        
        const horas = Math.floor(segundos / 3600);
        const minutos = Math.floor((segundos % 3600) / 60);
        return `${horas}h ${minutos}m`;
    }
    
    /**
     * Mostra/oculta loading
     */
    mostrarLoading(mostrar) {
        const loading = document.getElementById('dashboardLoading');
        if (loading) {
            loading.style.display = mostrar ? 'flex' : 'none';
        }
    }
    
    /**
     * Mostra erro
     */
    mostrarErro(mensagem) {
        console.error(mensagem);
        // Implementar notificação de erro
    }
    
    /**
     * Abre o dashboard
     */
    abrir() {
        const dashboard = document.getElementById('dashboardEstatisticas');
        if (dashboard) {
            dashboard.classList.add('ativo');
            document.body.style.overflow = 'hidden';
            this.carregarDados();
        }
    }
    
    /**
     * Fecha o dashboard
     */
    fechar() {
        const dashboard = document.getElementById('dashboardEstatisticas');
        if (dashboard) {
            dashboard.classList.remove('ativo');
            document.body.style.overflow = '';
        }
    }
    
    /**
     * Verifica se dashboard está aberto
     */
    isAberto() {
        const dashboard = document.getElementById('dashboardEstatisticas');
        return dashboard && dashboard.classList.contains('ativo');
    }

    /**
     * Cria todos os gráficos
     */
    criarGraficos() {
        this.criarGraficoProgressoLeis();
        this.criarGraficoDistribuicaoStatus();
        this.criarGraficoAtividadeTempo();
        this.criarRankingLeis();
    }

    /**
     * Cria gráfico de progresso por lei
     */
    criarGraficoProgressoLeis() {
        const ctx = document.getElementById('graficoProgressoLeis');
        if (!ctx || !this.dados.dashboard) return;

        // Destruir gráfico anterior se existir
        if (this.graficos.progressoLeis) {
            this.graficos.progressoLeis.destroy();
        }

        const leis = this.dados.dashboard.leis_dados || this.dados.dashboard.leis;

        this.graficos.progressoLeis = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: leis.map(lei => lei.nome),
                datasets: [{
                    label: 'Progresso (%)',
                    data: leis.map(lei => lei.percentual_progresso),
                    backgroundColor: leis.map(lei => lei.cor_tema + '80'),
                    borderColor: leis.map(lei => lei.cor_tema),
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const lei = leis[context.dataIndex];
                                return `${lei.nome}: ${lei.artigos_lidos}/${lei.total_artigos} artigos (${lei.percentual_progresso}%)`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Cria gráfico de distribuição de status
     */
    criarGraficoDistribuicaoStatus() {
        const ctx = document.getElementById('graficoDistribuicaoStatus');
        if (!ctx || !this.dados.dashboard) return;

        // Destruir gráfico anterior se existir
        if (this.graficos.distribuicaoStatus) {
            this.graficos.distribuicaoStatus.destroy();
        }

        // Somar todos os status das leis filtradas
        const leis = this.dados.dashboard.leis_dados || this.dados.dashboard.leis;
        const statusTotais = leis.reduce((acc, lei) => {
            acc.aprendendo += lei.revisao.aprendendo;
            acc.revisando += lei.revisao.revisando;
            acc.dominados += lei.revisao.dominados;
            acc.dificeis += lei.revisao.dificeis;
            return acc;
        }, { aprendendo: 0, revisando: 0, dominados: 0, dificeis: 0 });

        this.graficos.distribuicaoStatus = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Aprendendo', 'Revisando', 'Dominados', 'Difíceis'],
                datasets: [{
                    data: [
                        statusTotais.aprendendo,
                        statusTotais.revisando,
                        statusTotais.dominados,
                        statusTotais.dificeis
                    ],
                    backgroundColor: [
                        '#3498db',
                        '#f39c12',
                        '#27ae60',
                        '#e74c3c'
                    ],
                    borderWidth: 3,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((context.parsed * 100) / total).toFixed(1);
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Cria gráfico de atividade ao longo do tempo
     */
    criarGraficoAtividadeTempo() {
        const ctx = document.getElementById('graficoAtividadeTempo');
        if (!ctx || !this.dados.tempoEstudo) return;

        // Destruir gráfico anterior se existir
        if (this.graficos.atividadeTempo) {
            this.graficos.atividadeTempo.destroy();
        }

        const historico = this.dados.tempoEstudo.historico_diario || [];

        this.graficos.atividadeTempo = new Chart(ctx, {
            type: 'line',
            data: {
                labels: historico.map(item => {
                    const data = new Date(item.dia);
                    return data.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' });
                }),
                datasets: [{
                    label: 'Tempo de Estudo (minutos)',
                    data: historico.map(item => Math.round(item.tempo_segundos / 60)),
                    borderColor: '#3498db',
                    backgroundColor: '#3498db20',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#3498db',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const minutos = context.parsed.y;
                                const horas = Math.floor(minutos / 60);
                                const mins = minutos % 60;
                                return `Tempo: ${horas}h ${mins}m`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + 'min';
                            }
                        }
                    }
                }
            }
        });
    }

    /**
     * Cria ranking visual de leis
     */
    criarRankingLeis() {
        const container = document.getElementById('rankingLeis');
        if (!container || !this.dados.ranking) return;

        const ranking = this.dados.ranking.por_progresso || [];

        container.innerHTML = ranking.map((lei, index) => {
            const posicao = index + 1;
            let classePos = '';
            if (posicao === 1) classePos = 'primeiro';
            else if (posicao === 2) classePos = 'segundo';
            else if (posicao === 3) classePos = 'terceiro';

            return `
                <div class="ranking-item">
                    <div class="ranking-posicao ${classePos}">${posicao}</div>
                    <div class="ranking-lei">
                        <div class="ranking-icone" style="background: ${lei.cor_tema}">
                            <i class="${lei.icone}"></i>
                        </div>
                        <div class="ranking-info">
                            <h4>${lei.nome}</h4>
                            <p>${lei.artigos_lidos}/${lei.total_artigos} artigos</p>
                        </div>
                    </div>
                    <div class="ranking-valor">${lei.percentual_progresso}%</div>
                </div>
            `;
        }).join('');
    }

    /**
     * Atualiza detalhes por lei
     */
    atualizarDetalhesLeis() {
        const container = document.getElementById('detalhesLeis');
        if (!container || !this.dados.dashboard) return;

        const leis = this.dados.dashboard.leis_dados || this.dados.dashboard.leis;
        container.innerHTML = leis.map(lei => `
            <div class="detalhe-lei" style="border-left-color: ${lei.cor_tema}">
                <div class="detalhe-lei-header">
                    <div class="detalhe-lei-icone" style="background: ${lei.cor_tema}">
                        <i class="${lei.icone}"></i>
                    </div>
                    <div class="detalhe-lei-info">
                        <h3>${lei.nome}</h3>
                        <p>${lei.nome_completo}</p>
                    </div>
                </div>

                <div class="detalhe-lei-stats">
                    <div class="detalhe-stat">
                        <div class="detalhe-stat-valor">${lei.percentual_progresso}%</div>
                        <div class="detalhe-stat-label">Progresso</div>
                    </div>
                    <div class="detalhe-stat">
                        <div class="detalhe-stat-valor">${lei.revisao.total}</div>
                        <div class="detalhe-stat-label">Revisões</div>
                    </div>
                    <div class="detalhe-stat">
                        <div class="detalhe-stat-valor">${lei.revisao.dominados}</div>
                        <div class="detalhe-stat-label">Dominados</div>
                    </div>
                    <div class="detalhe-stat">
                        <div class="detalhe-stat-valor">${lei.favoritos}</div>
                        <div class="detalhe-stat-label">Favoritos</div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    /**
     * Obtém os filtros selecionados
     */
    obterFiltros() {
        const filtroLei = document.getElementById('filtroLei');
        const filtroPeriodo = document.getElementById('filtroPeriodo');

        return {
            lei: filtroLei ? filtroLei.value : '',
            periodo: filtroPeriodo ? filtroPeriodo.value : '30'
        };
    }

    /**
     * Aplica filtros selecionados
     */
    aplicarFiltros() {
        console.log('🔧 Dashboard: Aplicando filtros...');
        const filtros = this.obterFiltros();
        console.log('📊 Filtros selecionados:', filtros);

        if (filtros.lei) {
            console.log('🎯 Dashboard: Filtrando por lei:', filtros.lei);
        } else {
            console.log('📋 Dashboard: Mostrando todas as leis');
        }

        // Recarregar dados com filtros
        this.carregarDados();
    }
}

// Instância global
let dashboardEstatisticas;

// Inicializar quando DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    dashboardEstatisticas = new DashboardEstatisticas();
    
    // Disponibilizar globalmente
    window.dashboardEstatisticas = dashboardEstatisticas;
});

// Exportar para uso em outros módulos
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DashboardEstatisticas;
}
