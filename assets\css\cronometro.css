/* Adicione estas variáveis CSS para o modo escuro */
:root {
    --primary: #00008B;
    --secondary: #f8f0e3;
    --accent: #4a4a4a;
    --border: #e0e0e0;
    --text: #2c3e50;
    --hover: #f5f5f5;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
    --error: #8B0000;
    --success: #2E8B57;
    --warning: #FFA500;
    --blue-gradient: linear-gradient(45deg, var(--primary), #0000CD, var(--primary));
    --background: #f5f5f5;
    --card-background: white;
}

/* Variáveis para modo escuro */
[data-theme="dark"] {
    --primary: #4169E1;
    --secondary: #1a1a2e;
    --accent: #6c7293;
    --border: #2d2d42;
    --text: #e4e6f0;
    --hover: #232338;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.2);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.25);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.3);
    --error: #ff5252;
    --success: #4caf50;
    --warning: #ffb74d;
    --blue-gradient: linear-gradient(45deg, #4169E1, #6495ED, #4169E1);
    --background: #0a0a1f;
    --card-background: #13132b;
}

body {
    font-family: 'Quicksand', sans-serif;
    background: var(--background);
    color: var(--text);
    line-height: 1.6;
    padding: 2rem 0;
    transition: background-color 0.3s, color 0.3s;
}

.container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0 2rem;
}

.elegant-paper {
    background: var(--card-background);
    border: 1px solid var(--border);
    box-shadow: var(--shadow-lg);
    padding: 4rem;
    position: relative;
    border-radius: 10px;
    margin: 2rem auto;
    transition: background-color 0.3s, border-color 0.3s, box-shadow 0.3s;
}

h1 {
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    color: var(--primary);
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 3rem;
    text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);
    letter-spacing: 2px;
    position: relative;
    padding-bottom: 1.5rem;
}

.timer-label{
    font-family: 'Varela Round', 'Quicksand', sans-serif;
}

h1::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 200px;
    height: 2px;
    background: var(--primary);
}

.timer-display {
    text-align: center;
    margin: 3rem 0;
    padding: 3rem;
    background: var(--hover);
    border: 2px solid var(--primary);
    border-radius: 10px;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.lost-time-counter {
    position: absolute;
    top: 10px;
    right: 10px;
    font-family: 'Digital-7 Mono', sans-serif;
    font-size: 1.5rem;
    color: var(--bs-danger);
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 10px;
    border-radius: 5px;
    border: 1px solid rgba(255, 0, 0, 0.3);
}

.timer-display h2 {
    font-family: 'Digital-7 Mono', sans-serif;
    font-size: 5rem;
    color: var(--primary);
    margin: 0;
    text-shadow: 2px 2px 3px rgba(0, 0, 0, 0.1);
}

.btn-elegant {
    font-family: 'Quicksand', sans-serif;
    background: var(--primary);
    color: white;
    padding: 1rem 2rem;
    margin: 0.5rem;
    font-size: 1rem;
    border: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    min-width: 120px;
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}
.btn-elegant:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    opacity: 0.9;
    background: var(--primary);
    color: white;
}

.btn-elegant:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    opacity: 0.9;
}

.btn-zerar {
    background: var(--error);
}
.btn-zerar:hover {
    background: var(--error);
    color: white;
}

.btn-pausar {
    background: var(--accent);
}

.btn-continuar {
    background: var(--primary);
}

.btn-salvar {
    background: var(--success);
}

.button-group {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 1rem;
    margin: 2rem 0;
}



.form-select {
    width: 100%;
    padding: 0.8rem;
    font-size: 1rem;
    border: 1px solid var(--border);
    border-radius: 8px;
    background-color: white;
    color: var(--text);
    transition: all 0.3s ease;
}

.form-select:focus, .form-control:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: var(--shadow-sm);
}

.form-group {
    margin-bottom: 2rem;
    position: relative;
}

.form-label {
    font-family: 'Quicksand', sans-serif;
    color: var(--primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.8rem;
    display: block;
}

.form-control {
    width: 100%;
    padding: 0.8rem;
    font-size: 1rem;
    border: 1px solid var(--border);
    border-radius: 8px;
    background-color: white;
    color: var(--text);
    transition: all 0.3s ease;
}

/* Estilo para o toggle switch moderno */
.elegant-checkbox {
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 0.8rem 1.2rem;
    border: 1px solid var(--border);
    background: var(--hover);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.elegant-checkbox:hover {
    background: var(--primary-light);
    border-color: var(--primary);
}

/* Esconder o checkbox original */
.elegant-checkbox input[type="checkbox"] {
    height: 0;
    width: 0;
    visibility: hidden;
    position: absolute;
}

/* Criar o novo toggle switch */
.elegant-checkbox label {
    position: relative;
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    user-select: none;
    color: var(--text);
}

.elegant-checkbox label::before {
    content: '';
    width: 50px;
    height: 26px;
    background: #e0e0e0;
    display: inline-block;
    border-radius: 26px;
    position: relative;
    transition: 0.3s;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.elegant-checkbox label::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 3px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: 0.3s;
    transform: translateY(-50%);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* Estilo quando o checkbox está marcado */
.elegant-checkbox input:checked + label::before {
    background: var(--primary);
}

.elegant-checkbox input:checked + label::after {
    left: 47px;
    transform: translateX(-100%) translateY(-50%);
}

/* Efeito de foco para acessibilidade */
.elegant-checkbox input:focus + label::before {
    box-shadow: 0 0 0 3px rgba(0, 0, 139, 0.2);
}

/* Ajustes para o modo escuro */
[data-theme="dark"] .elegant-checkbox {
    background: var(--secondary);
    border-color: var(--border);
}

[data-theme="dark"] .elegant-checkbox:hover {
    background: var(--hover);
    border-color: var(--primary);
}

[data-theme="dark"] .elegant-checkbox label::before {
    background: #3a3a5a;
}

[data-theme="dark"] .elegant-checkbox label::after {
    background: #e4e6f0;
}

[data-theme="dark"] .elegant-checkbox input:checked + label::before {
    background: var(--primary);
}

.time-inputs {
    display: grid;
    grid-template-columns: repeat(3, minmax(150px, 1fr));
    gap: 1.5rem;
    margin: 2rem 0;
    padding: 1.5rem;
    background: var(--hover);
    border: 1px solid var(--border);
    border-radius: 10px;
}

@media (max-width: 820px) {
    .time-inputs {
        grid-template-columns: 1fr;
    }
}

#campos-simulado {
    background: var(--hover);
    padding: 2rem;
    border: 1px solid var(--border);
    border-radius: 10px;
    margin: 2rem 0;
    position: relative;
}

/* Adicione estes estilos para ajustar o tamanho dos inputs de tempo */
.time-inputs input[type="time"] {
    width: auto;
    min-width: 190px;
    max-width: 190px;
    padding: 0.6rem;
    text-align: center;
    margin: 0 auto;
    display: block;
}

/* Ajuste o container para centralizar os inputs */
.time-inputs .form-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 0.5rem;
}

.time-inputs {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin: 1.5rem 0;
    padding: 1rem;
    background: var(--hover);
    border: 1px solid var(--border);
    border-radius: 10px;
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(3px);
}

.modal-content {
    background: var(--hover);
    padding: 2rem;
    /*border-radius: 10px;*/
    box-shadow: var(--shadow-lg);
    max-width: 400px;
    width: 90%;
    position: relative;
}

.modal-title {
    font-family: 'Cinzel', serif;
    color: var(--primary);
    text-align: center;
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
}

/* Responsividade */
@media (max-width: 820px) {
    .elegant-paper {
        padding: 2rem;
    }

    h1 {
        font-size: 2rem;
    }

    .timer-display h2 {
        font-size: 4rem;
    }

    .button-group {
        flex-direction: column;
    }

    .btn-elegant {
        width: 100%;
    }

    .time-inputs {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* Estados de erro */
.input-error {
    border-color: var(--error) !important;
    box-shadow: 0 0 0 1px var(--error) !important;
    background-color: rgba(139, 0, 0, 0.05) !important;
}

.error-message {
    color: var(--error);
    font-size: 0.85rem;
    margin-top: 0.5rem;
    font-family: 'Quicksand', sans-serif;
}

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateY(-20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* Estilos para o modal de confirmação */
.modal-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

.modal-btn {
    font-family: 'Quicksand', sans-serif;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    min-width: 120px;
    box-shadow: var(--shadow-sm);
}

.modal-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.modal-btn-confirm {
    background-color: #00008B; /* Usando a cor do tema */
    color: white;
}

.modal-btn-cancel {
    background-color: #8B0000; /* Vermelho escuro */
    color: white;
}

.modal-title {
    font-family: 'Cinzel', serif;
    color: var(--primary);
    text-align: center;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.modal-message {
    text-align: center;
    margin-bottom: 20px;
    font-family: 'Quicksand', sans-serif;
    color: var(--text);
}

/* Cantos decorativos do modal */
.modal-corner {
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid var(--primary);
}

.modal-corner-tl { top: -2px; left: -2px; border-right: 0; border-bottom: 0; }
.modal-corner-tr { top: -2px; right: -2px; border-left: 0; border-bottom: 0; }
.modal-corner-bl { bottom: -2px; left: -2px; border-right: 0; border-top: 0; }
.modal-corner-br { bottom: -2px; right: -2px; border-left: 0; border-top: 0; }

/* Estilo para o botão de alternar tema */
.theme-toggle-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    background: transparent;
    border: none;
    color: var(--primary);
    font-size: 1.5rem;
    cursor: pointer;
    z-index: 10;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s, color 0.3s;
}

.theme-toggle-btn:hover {
    background-color: var(--hover);
}

[data-theme="dark"] .timer-display {
    background: var(--secondary);
    border-color: var(--primary);
}

[data-theme="dark"] .time-inputs,
[data-theme="dark"] #campos-simulado {
    background: var(--secondary);
    border-color: var(--border);
}

[data-theme="dark"] .form-select,
[data-theme="dark"] .form-control {
    background-color: var(--background);
    border-color: var(--border);
    color: var(--text);
}
.text-muted-1 {
    color: var(--text);
}
[data-theme="dark"] .text-muted-1 {
    color: white;
}



[data-theme="dark"] .elegant-checkbox {
    background: var(--secondary);
    border-color: var(--border);
}
/* Estilo para o placeholder no modo claro e escuro */
::placeholder {
    color: var(--text);
    opacity: 0.7;
}

[data-theme="dark"] ::placeholder {
    color: rgba(255, 255, 255, 0.7);
}

/* Suporte para diferentes navegadores */
::-webkit-input-placeholder {
    color: var(--text);
    opacity: 0.7;
}

[data-theme="dark"] ::-webkit-input-placeholder {
    color: rgba(255, 255, 255, 0.7);
}

::-moz-placeholder {
    color: var(--text);
    opacity: 0.7;
}

[data-theme="dark"] ::-moz-placeholder {
    color: rgba(255, 255, 255, 0.7);
}

:-ms-input-placeholder {
    color: var(--text);
    opacity: 0.7;
}

[data-theme="dark"] :-ms-input-placeholder {
    color: rgba(255, 255, 255, 0.7);
}
/* Estilo para o ícone do calendário no seletor de data */
input[type="date"]::-webkit-calendar-picker-indicator {
    cursor: pointer;
    opacity: 0.8;
    transition: all 0.3s ease;
}

input[type="date"]::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
}

/* Ícone do calendário no modo escuro - solução mais forte */
[data-theme="dark"] input[type="date"]::-webkit-calendar-picker-indicator {
    filter: invert(1) brightness(1.8) !important; /* Inverte e aumenta o brilho */
    opacity: 1 !important;
    background-color: transparent !important;
}

/* Solução alternativa para Firefox e outros navegadores */
[data-theme="dark"] input[type="date"] {
    color-scheme: dark !important; /* Indica ao navegador que estamos usando um esquema de cores escuro */
}

/* Solução específica para Edge/Chrome em Windows */
@supports (-ms-ime-align:auto) {
    [data-theme="dark"] input[type="date"]::-webkit-calendar-picker-indicator {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' viewBox='0 0 16 16'%3E%3Cpath d='M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4H1z'/%3E%3C/svg%3E") !important;
    }
}

/* Estilo específico para o input de data no modo escuro */
[data-theme="dark"] .dark-date-input::-webkit-calendar-picker-indicator {
    filter: invert(1) brightness(1.8) !important;
    opacity: 1 !important;
}

/* Hack para forçar o ícone branco em todos os navegadores */
@media (prefers-color-scheme: dark) {
    [data-theme="dark"] input[type="date"] {
        color-scheme: dark;
    }
}
/* Solução com ícone personalizado */
.date-input-wrapper {
    position: relative;
    display: block;
    width: 100%;
}

.date-custom-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text);
    pointer-events: none;
    z-index: 1;
}

[data-theme="dark"] .date-custom-icon {
    color: white;
}

/* Esconder completamente o ícone original do calendário */
input[type="date"]::-webkit-calendar-picker-indicator {
    opacity: 0;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    cursor: pointer;
    z-index: 2; /* Garantir que esteja acima para capturar cliques */
    background: transparent; /* Remover qualquer fundo */
}

/* Garantir que o input de data não mostre o ícone nativo */
input[type="date"] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    padding-right: 30px;
    background-color: transparent;
}

/* Ajustar a posição do nosso ícone personalizado */
.date-custom-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text);
    pointer-events: none;
    z-index: 1;
}

[data-theme="dark"] .date-custom-icon {
    color: white;
}
/* Estilo para os ícones de tempo no modo escuro */
[data-theme="dark"] input[type="time"]::-webkit-calendar-picker-indicator,
[data-theme="dark"] input[type="time"]::-webkit-inner-spin-button,
[data-theme="dark"] input[type="time"]::-webkit-clear-button {
    filter: invert(1) brightness(1.8) !important;
    opacity: 0.8 !important;
}

/* Solução para Firefox */


/* Solução para Edge/Chrome */
@supports (-ms-ime-align:auto) {
    [data-theme="dark"] input[type="time"]::-webkit-calendar-picker-indicator,
    [data-theme="dark"] input[type="time"]::-webkit-inner-spin-button {
        filter: invert(1) brightness(1.8) !important;
    }
}

/* Solução mais forte para os ícones de tempo no modo escuro */
[data-theme="dark"] input[type="time"]::-webkit-calendar-picker-indicator {
    filter: invert(1) brightness(1.8) !important;
    opacity: 1 !important;
    background-color: transparent !important;
}

[data-theme="dark"] input[type="time"]::-webkit-inner-spin-button {
    filter: invert(1) brightness(1.8) !important;
    opacity: 1 !important;
}

[data-theme="dark"] input[type="time"]::-webkit-clear-button {
    filter: invert(1) brightness(1.8) !important;
    opacity: 1 !important;
}

/* Solução para Firefox e outros navegadores */


/* Forçar o esquema de cores escuro para inputs de tempo */
@media (prefers-color-scheme: dark) {
    [data-theme="dark"] input[type="time"] {
        color-scheme: dark;
    }
}

/* Solução específica para inputs de tempo no modo escuro */
[data-theme="dark"] input[type="time"] {
    color: var(--text) !important; /* Usar variável de texto do tema escuro */
    background-color: var(--card-background) !important; /* Usar variável de fundo de cartão do tema escuro */
    border: 1px solid var(--border) !important; /* Adicionar borda consistente com o tema */
    color-scheme: dark; /* Reforçar o esquema de cores */
}

/* Seletores específicos para cada parte do input de tempo */
[data-theme="dark"] input[type="time"]::-webkit-datetime-edit { color: var(--text) !important; } /* Ajustado para usar variável de tema */
[data-theme="dark"] input[type="time"]::-webkit-datetime-edit-fields-wrapper { color: var(--text) !important; } /* Ajustado para usar variável de tema */
[data-theme="dark"] input[type="time"]::-webkit-datetime-edit-text { color: var(--text) !important; } /* Ajustado para usar variável de tema */
[data-theme="dark"] input[type="time"]::-webkit-datetime-edit-hour-field { color: var(--text) !important; } /* Ajustado para usar variável de tema */
[data-theme="dark"] input[type="time"]::-webkit-datetime-edit-minute-field { color: var(--text) !important; } /* Ajustado para usar variável de tema */
[data-theme="dark"] input[type="time"]::-webkit-datetime-edit-second-field { color: var(--text) !important; } /* Ajustado para usar variável de tema */
[data-theme="dark"] input[type="time"]::-webkit-datetime-edit-ampm-field { color: var(--text) !important; } /* Ajustado para usar variável de tema */

/* Ícones e botões (deixando o navegador tratar com color-scheme: dark) */
[data-theme="dark"] input[type="time"]::-webkit-calendar-picker-indicator {
    opacity: 1 !important; /* Manter opacidade se necessário */
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' viewBox='0 0 16 16'%3E%3Cpath d='M8 3.5a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .252.434l3.5 2a.5.5 0 0 0 .496-.868L8 8.71V3.5z'/%3E%3Cpath d='M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm7-8A7 7 0 1 1 1 8a7 7 0 0 1 14 0z'/%3E%3C/svg%3E") !important; /* SVG de relógio branco */
    background-repeat: no-repeat !important;
    background-position: center center !important;
    color: transparent !important; /* Esconde a cor do ícone de fonte nativo, se houver */
    filter: none !important; /* Remove qualquer filtro para não interferir com o SVG branco */
}

[data-theme="dark"] input[type="time"]::-webkit-inner-spin-button {
    /* filter: invert(1) !important; */ /* Removido para testar o default com color-scheme: dark */
    opacity: 1 !important; /* Manter opacidade se necessário */
}

[data-theme="dark"] input[type="time"]::-webkit-clear-button {
    /* filter: invert(1) !important; */ /* Removido para testar o default com color-scheme: dark */
    opacity: 1 !important; /* Manter opacidade se necessário */
}

/* Solução para Firefox */
@-moz-document url-prefix() {
    [data-theme="dark"] input[type="time"] {
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23e4e6f0' viewBox='0 0 16 16'%3E%3Cpath d='M8 3.5a.5.5 0 0 0-1 0V9a.5.5 0 0 0 .252.434l3.5 2a.5.5 0 0 0 .496-.868L8 8.71V3.5z'/%3E%3Cpath d='M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16zm7-8A7 7 0 1 1 1 8a7 7 0 0 1 14 0z'/%3E%3C/svg%3E"); /* Alterado fill para var(--text) do modo escuro */
        background-repeat: no-repeat;
        background-position: calc(100% - 10px) center;
        color: var(--text) !important;
        background-color: var(--card-background) !important;
    }
}

#modalSucesso.modal-overlay {
    position: fixed;
    top: 0; left: 0;
    width: 100vw; height: 100vh;
    background: rgba(0,0,0,0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
}
#modalSucesso .modal-content {
    background: var(--hover);
    /*border-radius: 18px;*/
    box-shadow: 0 8px 32px rgba(0,0,0,0.18);
    border: 2px solid #00008B;
    max-width: 68vw;
    max-height: 73vh;
    /*width: 420px;*/
    padding: 1rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
}
@media (max-width: 500px) {
    #modalSucesso .modal-content {
        width: 98vw;
        padding: 1rem;
    }
}

/* Estilo para o botão de tela cheia */
.fullscreen-toggle-btn {
    position: absolute;
    top: 20px;
    right: 70px; /* Posicionado ao lado do botão de tema */
    background: transparent;
    border: none;
    color: var(--primary);
    font-size: 1.5rem;
    cursor: pointer;
    z-index: 10;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s, color 0.3s;
}

.fullscreen-toggle-btn:hover {
    background-color: var(--hover);
}

/* Estilo para o modo tela cheia */
.fullscreen-mode {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--background);
    z-index: 9999;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0; /* Removido o padding para permitir centralização perfeita */
    overflow: hidden;
}

.fullscreen-mode .timer-display {
    transform: translate(-50%, -50%) scale(2);
    margin: 0;
    text-align: center;
    position: absolute;
    top: 40%; /* Ajustado para 40% para dar espaço para os botões abaixo */
    left: 50%;
}

.fullscreen-mode #timer {
    font-size: 5rem;
    margin: 1rem 0;
}

.fullscreen-mode .timer-label {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.fullscreen-mode .lost-time-counter {
    font-size: 15px;
}

.fullscreen-mode .button-group {
    position: absolute;
    bottom: 28%; /* Posicionado a 15% do fundo da tela */
    left: 50%;
    transform: translateX(-50%) scale(1.2);
    display: flex;
    gap: 1rem;
    margin: 0;
    z-index: 10000; /* Garante que os botões fiquem acima de outros elementos */
}

/* Ajuste para telas menores */
@media (max-height: 800px) {
    .fullscreen-mode .timer-display {
        top: 51%; /* Ajustado para telas menores */
    }
    
    .fullscreen-mode .button-group {
        bottom: 4%; /* Ajustado para telas menores */
        transform: translateX(-50%) scale(1); /* Reduz a escala em telas menores */
    }
}

.fullscreen-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
}

.exit-fullscreen-btn {
    background: transparent;
    border: none;
    color: var(--primary);
    font-size: 1.5rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s, color 0.3s;
}

.exit-fullscreen-btn:hover {
    background-color: var(--hover);
}

/* Ajustes para o modo escuro em tela cheia */
[data-theme="dark"] .fullscreen-mode {
    background: var(--background);
}

[data-theme="dark"] .exit-fullscreen-btn {
    color: var(--primary);
}

/* Estilo para o botão de alternar tela cheia */
.fullscreen-toggle-btn {
    position: absolute;
    top: 20px;
    right: 70px; /* Posicionado ao lado do botão de tema */
    background: transparent;
    border: none;
    color: var(--primary);
    font-size: 1.5rem;
    cursor: pointer;
    z-index: 10;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s, color 0.3s;
}

.fullscreen-toggle-btn:hover {
    background-color: var(--hover);
}

/* Estilo para o botão de sair da tela cheia */
.exit-fullscreen-btn {
    background: transparent;
    border: none;
    color: var(--primary);
    font-size: 1.5rem;
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.3s, color 0.3s;
}

.exit-fullscreen-btn:hover {
    background-color: var(--hover);
}

/* Estilo para o botão de tema no modo tela cheia */
.exit-fullscreen-btn.theme-btn {
    background-color: var(--paper-color);
    border: 2px solid var(--primary);
    color: var(--primary);
    box-shadow: 0 2px 5px var(--shadow-color);
}

.exit-fullscreen-btn.theme-btn:hover {
    background-color: var(--hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-color);
}

/* Ajustes para o modo escuro */
[data-theme="dark"] .exit-fullscreen-btn {
    color: var(--text);
}

[data-theme="dark"] .exit-fullscreen-btn.theme-btn {
    color: var(--warning-color);
    border-color: var(--secondary-color);
    background-color: var(--hover-color);
}

[data-theme="dark"] .exit-fullscreen-btn.theme-btn:hover {
    background-color: rgba(255, 183, 77, 0.15);
    color: var(--warning-color);
}

/* Ajustes para o modo tela cheia */
.fullscreen-mode .exit-fullscreen-btn {
    margin: 0 5px;
}

.fullscreen-controls {
    position: absolute;
    top: 20px;
    right: 20px;
    display: flex;
    gap: 10px;
    z-index: 10001; /* Garantir que fique acima do modal */
}

/* Estilos para o modal no modo tela cheia */
#fullscreenConfirmModal.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

#fullscreenConfirmModal .modal-content {
    background: white;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 450px;
    width: 90%;
    position: relative;
    animation: modalAppear 0.3s ease-out;
    border: 2px solid var(--primary);
}

@keyframes modalAppear {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

/* Ajuste para o modo escuro */
[data-theme="dark"] #fullscreenConfirmModal .modal-content {
    background: var(--background);
    color: var(--text);
}

.study-details {
    margin: 1.5rem 0 1.2rem 0;
    padding: 1.2rem 1rem;
    background: var(--border);
    border-radius: 10px;
    border: 1px solid rgba(13, 110, 253, 0.25);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    word-break: break-word;
    overflow-wrap: break-word;
    max-width: 100%;
    width: 100%;
    text-align: center;
}
/* Adicione ou ajuste estes estilos em assets/css/cronometro.css se preferir */
/* Ou serão embutidos no PHP como no exemplo abaixo */

/* Overlay para a mensagem de erro */
.error-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.75); /* Mais escuro para dar ênfase */
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 3000; /* Bem na frente */
    backdrop-filter: blur(5px);
    opacity: 0; /* Para animação */
    animation: fadeInOverlay 0.3s forwards;
}

@keyframes fadeInOverlay {
    to { opacity: 1; }
}

.error-modal-content {
    background: var(--card-background);
    padding: 2.5rem 3rem;
    border-radius: 10px;
    box-shadow: var(--shadow-lg), 0 0 0 2px var(--primary); /* Sombra e borda temática */
    max-width: 480px;
    width: 90%;
    position: relative;
    text-align: center;
    transform: scale(0.9); /* Para animação */
    opacity: 0; /* Para animação */
    animation: zoomInModal 0.4s 0.1s forwards;
}

@keyframes zoomInModal {
    to {
        opacity: 1;
        transform: scale(1);
    }
}


.error-modal-title {
    font-family: 'Cinzel', serif; /* Usando uma fonte elegante que você já tem */
    color: var(--primary);
    font-size: 2rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
}

.error-modal-icon {
    font-size: 2.2rem;
    color: var(--warning); /* Usando a cor de aviso do seu tema */
}

.error-modal-message {
    font-family: 'Quicksand', sans-serif;
    color: var(--text);
    font-size: 1.1rem;
    margin-bottom: 2.5rem;
    line-height: 1.7;
}

.error-modal-button {
    font-family: 'Quicksand', sans-serif;
    background: var(--primary);
    color: white;
    padding: 0.8rem 2rem;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    box-shadow: var(--shadow-sm);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.error-modal-button:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-md);
    opacity: 0.95;
}

/* Para os cantos decorativos que você já tem, apenas garantindo a cor */
.error-modal-content .modal-corner {
    border-color: var(--primary);
}




.header-left {
    position: absolute;
    top: 8px;
    left: 21px;
   
}
.logo {
    position: relative;
}

.logo img {
    height: 50px;
    width: auto;
}
.fullscreen-mode .logo img{
    height: 30px;
}


.logo-dark {
    position: absolute;
    top: 0px;
    left: 0px;
    opacity: 0;
}


/* Controle de visibilidade baseado no tema */
[data-theme="dark"] .logo-light {
    opacity: 0;
}

[data-theme="dark"] .logo-dark {
    opacity: 1;
}