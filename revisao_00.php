<?php
include 'processa_index.php';
include_once("conexao_POST.php");

if (!$conexao) {
    die("Erro na conexão: " . pg_last_error());
}

$id_usuario = $_SESSION['idusuario'];

$query_contagem = "SELECT 
    idplanejamento
FROM 
    appEstudo.planejamento p
WHERE 
    usuario_idusuario = $id_usuario;
";

$result = pg_query($conexao, $query_contagem);

if ($result) {
    $row = pg_fetch_assoc($result);

    if ($row) {
        $id_planejamento = $row['idplanejamento'];
    } else {
        echo "Nenhum planejamento encontrado.";
        exit;
    }
} else {
    echo "Erro na consulta: " . pg_last_error($conexao);
    exit;
}

$data_atual = strtotime(date('Y-m-d'));
$intervalos = [
    '1' => [
        'descrição' => 'Primeira Revisão',
        'dias' => 1,
        'explicação' => 'Revisão intensiva para fortalecer o aprendizado recente. Com duração média de 10 minutos'
    ],
    '3' => [
        'descrição' => 'Segunda Revisão',
        'dias' => 3,
        'explicação' => 'Revisão para consolidar o conhecimento após alguns dias. Com duração máxima de 10 minutos'
    ],
    '7' => [
        'descrição' => 'Terceira Revisão',
        'dias' => 7,
        'explicação' => 'Revisão para manter a memória a longo prazo. Com duração máxima de 10 minutos'
    ],
    '14' => [
        'descrição' => 'Quarta Revisão',
        'dias' => 14,
        'explicação' => 'Revisão para garantir que o conteúdo seja lembrado após duas semanas. Com duração média de 5 minutos'
    ],
    '30' => [
        'descrição' => 'Quinta Revisão',
        'dias' => 30,
        'explicação' => 'Revisão para reforçar o conhecimento de forma espaçada e eficiente. Com duração média de 5 minutos'
    ]
];

$revisoes = [];
foreach ($intervalos as $dias => $detalhes) {
    $data_revisao = date('Y-m-d', strtotime("-$dias days", $data_atual));
    $revisoes[$data_revisao] = $detalhes;
}

$query_materias_revisao = "
    SELECT m.idmateria, m.nome AS nome_materia, m.cor AS cor_materia, e.data AS data_estudo, e.ponto_estudado, e.metodo
    FROM appEstudo.materia m
    INNER JOIN appEstudo.estudos e ON e.materia_idmateria = m.idmateria
    WHERE e.planejamento_usuario_idusuario = $id_usuario
    AND e.data IN ('" . implode("','", array_keys($revisoes)) . "')
";

$resultado_materias_revisao = pg_query($conexao, $query_materias_revisao);

$materias_por_revisao = [];
if ($resultado_materias_revisao) {
    while ($materia_revisao = pg_fetch_assoc($resultado_materias_revisao)) {
        if(($materia_revisao['metodo'] === "Revisão") or ($materia_revisao['metodo'] === "Questões") or ($materia_revisao['metodo'] === "Simulado")){

        }else{
            $data_estudo = $materia_revisao['data_estudo'];
            $intervalo_descricao = $revisoes[$data_estudo]['descrição'];
            $dias_revisao = $revisoes[$data_estudo]['dias'];

            if (!isset($materias_por_revisao[$dias_revisao])) {
                $materias_por_revisao[$dias_revisao] = [];
            }

            $materias_por_revisao[$dias_revisao][] = array(
                'id' => $materia_revisao['idmateria'],
                'nome' => $materia_revisao['nome_materia'],
                'cor' => $materia_revisao['cor_materia'],
                'dias' => $dias_revisao,
                'ponto_estudado' => $materia_revisao['ponto_estudado']
            );

        }
    }
} else {
    echo "Erro na consulta de matérias para revisão: " . pg_last_error($conexao);
}

pg_close($conexao);

ksort($materias_por_revisao);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Revisões de Hoje</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">

    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css"
          integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">
    <!-- Incluir Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <style>
        body {
            font-family: 'Courier Prime', monospace;
            background-color: #f0f0f0;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .caixa-revisao {
            background-color: rgba(222, 173, 69, 0.29);
            border: 1px solid #ccc;
            border-radius: 8px;
            padding: 20px;
            margin: 10px 0;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        .caixa-revisao h2 {
            margin: 0 0 10px 0;
            font-size: 1.5em;
            color: #555;
        }
        .caixa-revisao ul {
            list-style: none;
            padding: 0;
        }
        .caixa-revisao ul li {
            margin: 5px 0;
            font-size: 1.1em;
        }
        .caixa-revisao ul li strong {
            font-weight: bold;
        }
        .caixa-revisao ul li span {
            font-style: italic;
            color: #2b2723;
        }
        .btn-gold {
            color: #F5DEB3; /* Cor dourada */
            font-family: 'Courier Prime', monospace;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>Revisões de Hoje</h1>

    <?php if (!empty($materias_por_revisao)): ?>
        <?php foreach ($materias_por_revisao as $dias => $materias): ?>
            <?php $intervalo_descricao = $intervalos[$dias]['descrição']; ?>
            <?php $explicacao = $intervalos[$dias]['explicação']; ?>
            <div class="caixa-revisao">
                <h2>
                    <?php echo htmlspecialchars($intervalo_descricao); ?>
                    - <?php echo htmlspecialchars($dias); ?>
                    <?php echo ($dias == 1) ? ' Dia' : ' Dias'; ?>
                    <i class="fas fa-info-circle info-icon" title="<?php echo htmlspecialchars($explicacao); ?>"></i>
                </h2>
                <ul>
                    <?php foreach ($materias as $materia): ?>
                        <li style="color: <?php echo htmlspecialchars($materia['cor']); ?>;">
                            <strong><center>- <?php echo htmlspecialchars($materia['nome']); ?> -</strong>
                            </center><span><center>(<?php echo htmlspecialchars($materia['ponto_estudado']); ?>)</center><br></span>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        <?php endforeach; ?>
    <?php else: ?>
        <p>Não há matérias para revisar hoje.</p>
    <?php endif; ?>
    <div><center>
        <!-- Botão para voltar à página index.php com a font definida -->
        <a href="#" class="btn btn-dark btn-sm btn-gold"
           onclick="window.close(); return false;">Fechar Página</a>
        </center></div>
</div>
</body>
</html>
