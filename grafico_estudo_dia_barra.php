<?php
include 'conexao_POST.php';
include 'processa_index.php';
include_once 'funcoes.php'; // Inclua as funções aqui

// Verifica se $id_planejamento e $id_usuario estão definidos
if (!isset($id_planejamento) || !isset($id_usuario)) {
    die("ID do planejamento ou ID do usuário não definido.");
}

// Consultar os IDs e nomes das matérias associadas ao planejamento
$query_consultar_materias_planejamento = "
    SELECT pm.materia_idmateria, m.nome AS nome_materia, m.cor AS cor_materia
    FROM appEstudo.planejamento_materia pm
    INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    WHERE pm.planejamento_idplanejamento = $id_planejamento";

$resultado_materias_planejamento = pg_query($conexao, $query_consultar_materias_planejamento);

// Inicializa arrays para armazenar os dados das matérias
$materias_no_planejamento_Grafico = array();
$cores_materias = array();

// Preenche os arrays com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_materias_planejamento)) {
    $materia_id = $row['materia_idmateria'];
    $nome_materia = $row['nome_materia'];
    $cor_materia = $row['cor_materia'];

    $materias_no_planejamento_Grafico[$materia_id] = $nome_materia;
    $cores_materias[$nome_materia] = $cor_materia;
}

// Obtém a data atual
$data_atual = date('Y-m-d');

// Realiza a consulta ao banco de dados para obter os dados de estudo por ponto estudado de cada matéria no planejamento
$query_consulta_pontos_estudo = "
    SELECT m.nome AS nome_materia, 
           e.ponto_estudado AS ponto_estudado,
           SUM(e.tempo_liquido) AS tempo_estudo
    FROM appEstudo.estudos e
    INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
    INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
    LEFT JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria
    WHERE u.idusuario = $id_usuario 
      AND e.materia_idmateria IN (" . implode(',', array_keys($materias_no_planejamento_Grafico)) . ")
      AND DATE(e.data) = '$data_atual'
    GROUP BY m.nome, e.ponto_estudado";

$resultado_consulta_pontos_estudo = pg_query($conexao, $query_consulta_pontos_estudo);

// Verifica se houve algum estudo
$houve_estudo = pg_num_rows($resultado_consulta_pontos_estudo) > 0;

// Inicializa um array para armazenar os dados de tempo de estudo por ponto estudado para cada matéria
$pontos_estudo_por_materia = array();
$tempo_total_por_materia = array();

// Preenche o array com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_consulta_pontos_estudo)) {
    $materia = $row['nome_materia'];
    $ponto_estudado = $row['ponto_estudado'];
    $tempo_estudo = $row['tempo_estudo']; // Tempo no formato "HH:MM:SS"

    // Converte o tempo de estudo para segundos
    $tempo_estudo_segundos = converterParaSegundos($tempo_estudo);

    // Adiciona os tempos de estudo por ponto estudado para a matéria correspondente
    if (!isset($pontos_estudo_por_materia[$materia])) {
        $pontos_estudo_por_materia[$materia] = array();
    }
    // Convertendo para segundos e armazenando
    $pontos_estudo_por_materia[$materia][$ponto_estudado] = $tempo_estudo_segundos;

    // Calcula o tempo total por matéria
    if (!isset($tempo_total_por_materia[$materia])) {
        $tempo_total_por_materia[$materia] = 0;
    }
    $tempo_total_por_materia[$materia] += $tempo_estudo_segundos;
}

// Função para converter segundos para o formato "HH:MM:SS"
function converterParaHorasMinutosSegundos($segundos) {
    $horas = floor($segundos / 3600);
    $minutos = floor(($segundos % 3600) / 60);
    $segundos = $segundos % 60;
    return sprintf("%02d:%02d:%02d", $horas, $minutos, $segundos);
}

// Convertendo os arrays para JSON para serem utilizados no JavaScript
$dados_json = json_encode($pontos_estudo_por_materia);
$cores_json = json_encode($cores_materias);
$tempo_total_json = json_encode($tempo_total_por_materia);
$houve_estudo_json = json_encode($houve_estudo);
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Gráfico de Estudos</title>
    <script src="https://code.highcharts.com/highcharts.js"></script>
    <script src="https://code.highcharts.com/modules/drilldown.js"></script>
</head>
<body>
<div id="mensagemSemEstudo" style="display: none;">
    <h5><strong><span style="color: white; background-color: red;">ATENÇÃO!</span> Você Ainda não estudou Hoje</strong></h5>
    <p style="text-align: center;">A <b>CONSTÂNCIA</b> é mais importante que a INTENSIDADE!</p>
</div>
<div id="pontoChart2" style="height: 400px;"></div>

<script>
    // Recuperando os dados do PHP
    var dados = <?php echo $dados_json; ?>;
    var cores = <?php echo $cores_json; ?>;
    var tempoTotalPorMateria = <?php echo $tempo_total_json; ?>;
    var houveEstudo = <?php echo $houve_estudo_json; ?>;

    // Função para converter segundos para o formato "HH:MM:SS"
    function converterParaHorasMinutosSegundos(segundos) {
        var horas = Math.floor(segundos / 3600);
        var minutos = Math.floor((segundos % 3600) / 60);
        segundos = segundos % 60;
        return String(horas).padStart(2, '0') + ':' + String(minutos).padStart(2, '0') + ':' + String(segundos).padStart(2, '0');
    }

    if (!houveEstudo) {
        document.getElementById('mensagemSemEstudo').style.display = 'block';
    } else {
        document.getElementById('mensagemSemEstudo').style.display = 'none';

        // Transformando os dados no formato esperado pelo Highcharts para o gráfico de barras horizontais
        var seriesData = [];
        var drilldownSeries = [];
        for (var materia in tempoTotalPorMateria) {
            seriesData.push({
                name: materia,
                y: tempoTotalPorMateria[materia],
                color: cores[materia], // Atribui a cor específica da matéria
                drilldown: materia,
                tempoFormatado: converterParaHorasMinutosSegundos(tempoTotalPorMateria[materia]) // Tempo formatado para exibição
            });

            var data = [];
            for (var pontoEstudado in dados[materia]) {
                var tempoEstudo = parseInt(dados[materia][pontoEstudado]);
                data.push({
                    name: pontoEstudado,
                    y: tempoEstudo,
                    tempoFormatado: converterParaHorasMinutosSegundos(tempoEstudo)
                });
            }

            drilldownSeries.push({
                name: materia,
                id: materia,
                data: data
            });
        }

        // Calculando o tempo total de estudo do dia
        var tempoTotalDoDia = seriesData.reduce(function (sum, item) {
            return sum + item.y;
        }, 0);
        var tempoTotalFormatado = converterParaHorasMinutosSegundos(tempoTotalDoDia);

        // Configuração do gráfico de barras horizontais com drilldown
        Highcharts.chart('pontoChart2', {
            chart: {
                type: 'bar',
                backgroundColor: 'transparent' // Fundo transparente
            },
            title: {
                text: 'Tempo de Estudo por Matéria'
            },
            credits: {
                enabled: false // Desativar créditos
            },
            xAxis: {
                type: 'category',
                title: {
                    text: 'Matérias'
                }
            },
            yAxis: {
                title: {
                    text: 'Tempo de Estudo'
                },
                labels: {
                    formatter: function () {
                        return converterParaHorasMinutosSegundos(this.value); // Formata os rótulos do eixo y
                    }
                }
            },
            tooltip: {
                formatter: function() {
                    var percentage = (this.y / tempoTotalDoDia * 100).toFixed(2);
                    return '<b>' + this.point.name + '</b>: ' + this.point.tempoFormatado + ' (' + percentage + '%)';
                }
            },
            plotOptions: {
                series: {
                    dataLabels: {
                        enabled: true,
                        formatter: function () {
                            var percentage = (this.y / tempoTotalDoDia * 100).toFixed(2);
                            return  this.point.name + ': ' + this.point.tempoFormatado + ' (' + percentage + '%)';
                        }
                    },
                    showInLegend: false // Oculta o nome da série na legenda e no rodapé
                }
            },
            series: [{
                data: seriesData
            }],
            drilldown: {
                series: drilldownSeries
            },
            // Adicionando o tempo total de estudo do dia no rodapé do gráfico
            subtitle: {
                text: 'Tempo Total de Estudo do Dia: ' + tempoTotalFormatado
            }
        });
    }
</script>
</body>
</html>


