<?php
/**
 * API Simplificada para gerenciamento de leis no sistema multi-leis
 * Versão compatível com pg_query para funcionar com a estrutura existente
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Tratar requisições OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Verificar se o usuário está logado
session_start();
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit();
}

// Incluir conexão existente
require_once '../../conexao_POST.php';

$usuario_id = $_SESSION['idusuario'];
$metodo = $_SERVER['REQUEST_METHOD'];
$acao = $_GET['acao'] ?? '';

try {
    switch ($metodo) {
        case 'GET':
            switch ($acao) {
                case 'listar':
                    listarLeis($conexao, $usuario_id);
                    break;
                case 'carregar':
                    carregarLei($conexao, $usuario_id);
                    break;
                case 'estatisticas':
                    obterEstatisticas($conexao, $usuario_id);
                    break;
                case 'atual':
                    // REMOVIDO: Sistema multi-leis não usa mais preferência de lei padrão
                    http_response_code(410);
                    echo json_encode(['erro' => 'Funcionalidade removida - sistema multi-leis']);
                    break;
                default:
                    listarLeis($conexao, $usuario_id);
                    break;
            }
            break;

        case 'POST':
            switch ($acao) {
                case 'trocar':
                    trocarLei($conexao, $usuario_id);
                    break;
                default:
                    http_response_code(400);
                    echo json_encode(['erro' => 'Ação não reconhecida']);
                    break;
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['erro' => 'Método não permitido']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['erro' => 'Erro interno do servidor: ' . $e->getMessage()]);
}

/**
 * Lista todas as leis disponíveis
 */
function listarLeis($conexao, $usuario_id) {
    $query = "
        SELECT
            l.*,
            COALESCE(r.total_revisoes, 0) as total_revisoes,
            COALESCE(r.pendentes, 0) as pendentes,
            COALESCE(p.lidos, 0) as artigos_lidos,
            COALESCE(f.favoritos, 0) as favoritos,
            CASE
                WHEN l.total_artigos > 0 THEN
                    ROUND((COALESCE(p.lidos, 0) * 100.0 / l.total_artigos), 1)
                ELSE 0
            END as percentual_progresso
        FROM appestudo.lexjus_leis l
        LEFT JOIN (
            SELECT
                lei_id,
                COUNT(*) as total_revisoes,
                COUNT(CASE WHEN data_proxima_revisao <= CURRENT_TIMESTAMP THEN 1 END) as pendentes
            FROM appestudo.lexjus_revisoes
            WHERE usuario_id = $1
            GROUP BY lei_id
        ) r ON l.id = r.lei_id
        LEFT JOIN (
            SELECT
                lei_id,
                COUNT(*) as lidos
            FROM appestudo.lexjus_progresso
            WHERE usuario_id = $1 AND lido = true
            GROUP BY lei_id
        ) p ON l.id = p.lei_id
        LEFT JOIN (
            SELECT
                lei_id,
                COUNT(*) as favoritos
            FROM appestudo.lexjus_favoritos
            WHERE usuario_id = $1
            GROUP BY lei_id
        ) f ON l.id = f.lei_id
        WHERE l.ativa = true
        ORDER BY l.ordem_exibicao
    ";

    $result = pg_query_params($conexao, $query, [$usuario_id]);

    if (!$result) {
        throw new Exception('Erro ao buscar leis: ' . pg_last_error($conexao));
    }

    $leis = [];
    while ($row = pg_fetch_assoc($result)) {
        $leis[] = $row;
    }

    echo json_encode([
        'sucesso' => true,
        'leis' => $leis
    ]);
}

/**
 * Carrega os artigos de uma lei específica
 */
function carregarLei($conexao, $usuario_id) {
    $lei_codigo = $_GET['codigo'] ?? '';

    if (empty($lei_codigo)) {
        http_response_code(400);
        echo json_encode(['erro' => 'Código da lei é obrigatório']);
        return;
    }

    // Buscar informações da lei
    $query = "SELECT * FROM appestudo.lexjus_leis WHERE codigo = $1 AND ativa = true";
    $result = pg_query_params($conexao, $query, [$lei_codigo]);

    if (!$result || pg_num_rows($result) === 0) {
        http_response_code(404);
        echo json_encode(['erro' => 'Lei não encontrada']);
        return;
    }

    $lei = pg_fetch_assoc($result);

    // Carregar artigos do arquivo JSON
    $arquivo_json = __DIR__ . '/../banco/' . $lei['arquivo_json'];

    if (!file_exists($arquivo_json)) {
        http_response_code(404);
        echo json_encode(['erro' => 'Arquivo JSON da lei não encontrado: ' . $lei['arquivo_json']]);
        return;
    }

    $artigos_json = file_get_contents($arquivo_json);
    $artigos = json_decode($artigos_json, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        http_response_code(500);
        echo json_encode(['erro' => 'Erro ao decodificar JSON da lei: ' . json_last_error_msg()]);
        return;
    }

    // Atualizar total de artigos se necessário
    if ($lei['total_artigos'] != count($artigos)) {
        $query_update = "UPDATE appestudo.lexjus_leis SET total_artigos = $1 WHERE id = $2";
        pg_query_params($conexao, $query_update, [count($artigos), $lei['id']]);
        $lei['total_artigos'] = count($artigos);
    }

    echo json_encode([
        'sucesso' => true,
        'lei' => $lei,
        'artigos' => $artigos,
        'total_artigos' => count($artigos)
    ]);
}

/**
 * Obtém estatísticas básicas de uma lei
 */
function obterEstatisticas($conexao, $usuario_id) {
    $lei_codigo = $_GET['codigo'] ?? '';

    if (empty($lei_codigo)) {
        http_response_code(400);
        echo json_encode(['erro' => 'Código da lei é obrigatório']);
        return;
    }

    // Buscar ID da lei
    $query_lei = "SELECT id FROM appestudo.lexjus_leis WHERE codigo = $1";
    $result_lei = pg_query_params($conexao, $query_lei, [$lei_codigo]);

    if (!$result_lei || pg_num_rows($result_lei) === 0) {
        http_response_code(404);
        echo json_encode(['erro' => 'Lei não encontrada']);
        return;
    }

    $lei = pg_fetch_assoc($result_lei);
    $lei_id = $lei['id'];

    // Buscar informações da lei
    $query_lei_info = "SELECT * FROM appestudo.lexjus_leis WHERE id = $1";
    $result_lei_info = pg_query_params($conexao, $query_lei_info, [$lei_id]);
    $lei_info = pg_fetch_assoc($result_lei_info);

    // Estrutura de estatísticas completa
    $stats = [
        'lei' => [
            'codigo' => $lei_info['codigo'],
            'nome' => $lei_info['nome'],
            'total_artigos' => (int)$lei_info['total_artigos'],
            'cor_tema' => $lei_info['cor_tema']
        ],
        'revisao' => [
            'total' => 0,
            'pendentes' => 0,
            'aprendendo' => 0,
            'revisando' => 0,
            'dominado' => 0,
            'dificil' => 0,
            'facilidade_media' => 0
        ],
        'progresso' => [
            'lidos' => 0,
            'percentual' => 0
        ],
        'favoritos' => 0,
        'listas' => 0,
        'anotacoes' => 0
    ];

    // Revisões detalhadas
    $query_revisoes = "
        SELECT
            COUNT(*) as total,
            COUNT(CASE WHEN data_proxima_revisao <= CURRENT_TIMESTAMP THEN 1 END) as pendentes,
            COUNT(CASE WHEN status = 'aprendendo' THEN 1 END) as aprendendo,
            COUNT(CASE WHEN status = 'revisando' THEN 1 END) as revisando,
            COUNT(CASE WHEN status = 'dominado' THEN 1 END) as dominado,
            COUNT(CASE WHEN status = 'dificil' THEN 1 END) as dificil,
            COALESCE(AVG(facilidade), 0) as facilidade_media
        FROM appestudo.lexjus_revisoes
        WHERE usuario_id = $1 AND lei_id = $2";

    $result_revisoes = pg_query_params($conexao, $query_revisoes, [$usuario_id, $lei_id]);
    if ($result_revisoes) {
        $revisoes = pg_fetch_assoc($result_revisoes);
        $stats['revisao']['total'] = (int)$revisoes['total'];
        $stats['revisao']['pendentes'] = (int)$revisoes['pendentes'];
        $stats['revisao']['aprendendo'] = (int)$revisoes['aprendendo'];
        $stats['revisao']['revisando'] = (int)$revisoes['revisando'];
        $stats['revisao']['dominado'] = (int)$revisoes['dominado'];
        $stats['revisao']['dificil'] = (int)$revisoes['dificil'];
        $stats['revisao']['facilidade_media'] = round((float)$revisoes['facilidade_media'], 2);
    }

    // Favoritos
    $query_favoritos = "SELECT COUNT(*) as total FROM appestudo.lexjus_favoritos WHERE usuario_id = $1 AND lei_id = $2";
    $result_favoritos = pg_query_params($conexao, $query_favoritos, [$usuario_id, $lei_id]);
    if ($result_favoritos) {
        $favoritos = pg_fetch_assoc($result_favoritos);
        $stats['favoritos'] = (int)$favoritos['total'];
    }

    // Progresso de leitura
    $query_progresso = "SELECT COUNT(*) as lidos FROM appestudo.lexjus_progresso WHERE usuario_id = $1 AND lei_id = $2 AND lido = true";
    $result_progresso = pg_query_params($conexao, $query_progresso, [$usuario_id, $lei_id]);
    if ($result_progresso) {
        $progresso = pg_fetch_assoc($result_progresso);
        $stats['progresso']['lidos'] = (int)$progresso['lidos'];

        // Calcular percentual
        if ($stats['lei']['total_artigos'] > 0) {
            $stats['progresso']['percentual'] = round(($stats['progresso']['lidos'] / $stats['lei']['total_artigos']) * 100, 1);
        }
    }

    // Listas
    $query_listas = "SELECT COUNT(*) as total FROM appestudo.lexjus_listas WHERE usuario_id = $1 AND lei_id = $2";
    $result_listas = pg_query_params($conexao, $query_listas, [$usuario_id, $lei_id]);
    if ($result_listas) {
        $listas = pg_fetch_assoc($result_listas);
        $stats['listas'] = (int)$listas['total'];
    }

    // Anotações
    $query_anotacoes = "SELECT COUNT(*) as total FROM appestudo.lexjus_anotacoes WHERE usuario_id = $1 AND lei_id = $2";
    $result_anotacoes = pg_query_params($conexao, $query_anotacoes, [$usuario_id, $lei_id]);
    if ($result_anotacoes) {
        $anotacoes = pg_fetch_assoc($result_anotacoes);
        $stats['anotacoes'] = (int)$anotacoes['total'];
    }

    echo json_encode([
        'sucesso' => true,
        'estatisticas' => $stats
    ]);
}

// REMOVIDO: Funções obterLeiAtual() e trocarLei() - sistema multi-leis não usa mais preferência de lei padrão
// O sistema agora funciona com seleção direta de lei via URL ou menu


?>
