<?php
// atualizar_status.php
session_start();
require_once 'assets/config.php';

// Verifica se o usuário está autenticado
if (!isset($_SESSION['idusuario'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Usuário não autenticado']);
    exit;
}

$usuario_id = $_SESSION['idusuario'];

// Verifica se os parâmetros necessários estão presentes
if (!isset($_POST['conteudo_id']) || !isset($_POST['status'])) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'error' => 'Parâmetros inválidos']);
    exit;
}

$conteudo_id = intval($_POST['conteudo_id']);
$status = filter_var($_POST['status'], FILTER_VALIDATE_BOOLEAN);

// Verifica se estamos atualizando após um replanejamento
$apos_replanejamento = isset($_POST['apos_replanejamento']) ? 
    filter_var($_POST['apos_replanejamento'], FILTER_VALIDATE_BOOLEAN) : false;

try {
    // Iniciar transação
    pg_query($conexao, "BEGIN");

    // Define o novo status
    $status_texto = $status ? 'Estudado' : 'Pendente';

    // Atualiza o status do conteúdo
    // Atualiza o status do conteúdo
    $query = "UPDATE appestudo.usuario_conteudo
              SET status_estudo = $1";
    
    // Se estiver sendo desmarcado (status_estudo = Pendente), limpamos o replanejado_em
    if (!$status) {
        $query .= ", replanejado_em = NULL";
    } else {
        // Lógica de segurança: se estiver marcando como Estudado, garanta que replanejado_em seja NULL
        // Ele só deve ser preenchido no momento do replanejamento para cards JÁ estudados.
        $query .= ", replanejado_em = NULL";
    }
    
    $query .= " WHERE id = $2 AND usuario_id = $3";
    
    $result = pg_query_params($conexao, $query, array(
        $status_texto,
        $conteudo_id,
        $usuario_id
    ));

    if (!$result) {
        throw new Exception('Erro ao atualizar status: ' . pg_last_error($conexao));
    }

    // Commit
    pg_query($conexao, "COMMIT");

    // Retorna sucesso
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'status' => $status_texto,
        'message' => 'Status atualizado com sucesso'
    ]);

} catch (Exception $e) {
    // Rollback em caso de erro
    pg_query($conexao, "ROLLBACK");
    
    // Retorna erro
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>