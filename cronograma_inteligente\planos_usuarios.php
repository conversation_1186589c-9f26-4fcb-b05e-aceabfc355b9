<?php
session_start();
require_once 'assets/config.php';

// Verificar se está logado
if (!isset($_SESSION['idusuario'])) {
    header("Location: ../index.html");
    exit();
}

$usuario_id = $_SESSION['idusuario'];

// Verifica se já tem plano ativo
$query_plano = "SELECT * FROM appestudo.assinaturas 
                WHERE usuario_id = $usuario_id 
                AND modulo = 'cronograma_inteligente'
                AND status = true 
                AND data_fim > CURRENT_DATE";

$resultado_plano = pg_query($conexao, $query_plano);

if ($resultado_plano && pg_num_rows($resultado_plano) > 0) {
    header("Location: cronograma/index.php");
    exit();
}

// Preços especiais para usuários existentes
$preco_mensal = 00.00;
$preco_anual = 00.00;
$desconto = "30%";

// Busca nome do usuário
$query_usuario = "SELECT nome FROM appestudo.usuario WHERE idusuario = $usuario_id";
$resultado_usuario = pg_query($conexao, $query_usuario);
$usuario = pg_fetch_assoc($resultado_usuario);
$nome_usuario = explode(' ', $usuario['nome'])[0];
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Planeja Aqui - Módulo Cronograma Inteligente</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <!-- [CSS styles do arquivo original aqui] -->
    <style>
        :root {
            --primary: #00008B;
            --white: #ffffff;
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-600: #4b5563;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Quicksand', sans-serif;
        }

        body {
            background-color: var(--white);
            color: var(--gray-600);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Botão Voltar */
        .back-btn {
            position: fixed;
            top: 20px;
            left: 20px;
            background-color: var(--white);
            color: var(--primary);
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            z-index: 100;
        }

        .back-btn:hover {
            transform: translateX(-5px);
            box-shadow: 0 4px 8px rgba(0, 0, 139, 0.2);
        }

        .back-btn i {
            font-size: 1.2rem;
        }

        /* Hero Section */
        .hero {
            background-color: var(--primary);
            color: var(--white);
            padding: 80px 0;
            text-align: center;
        }

        .hero-icon {
            font-size: 4rem;
            margin-bottom: 1.5rem;
        }

        .hero h1 {
            font-size: 3.5rem;
            margin-bottom: 1rem;
        }

        .hero p {
            font-size: 1.25rem;
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .hero-logo {
            max-width: 300px;
            height: auto;
            margin-bottom: 2rem;
        }

        /* Botões */
        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            border-radius: 9999px;
            font-weight: 700;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            text-transform: uppercase;
            letter-spacing: 1px;
            width: 100%;
        }

        .btn-primary {
            background-color: var(--white);
            color: var(--primary);
        }

        .btn:hover {
            transform: scale(1.05);
            box-shadow: 0 5px 15px rgba(0, 0, 139, 0.3);
        }

        .btn-primary:hover {
            background-color: var(--gray-100);
        }

        /* Benefícios */
        .benefits {
            padding: 80px 0;
            background-color: var(--gray-50);
        }

        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }

        .benefit-card {
            background: var(--white);
            padding: 2rem;
            border-radius: 0.5rem;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .benefit-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 139, 0.2);
        }

        .benefit-card i {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 1rem;
            transition: transform 0.3s ease;
        }

        .benefit-card:hover i {
            transform: scale(1.1);
        }

        /* Planos */
        .plans {
            padding: 80px 0;
        }

        .plans-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            max-width: 1000px;
            margin: 3rem auto 0;
        }

        .plan-card {
            border: 2px solid var(--primary);
            border-radius: 0.5rem;
            padding: 2rem;
            text-align: center;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
            transform: translateY(0);
        }

        .plan-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 10px 20px rgba(0, 0, 139, 0.2);
        }

        .plan-card.popular {
            background-color: var(--primary);
            color: var(--white);
            border: 2px solid transparent;
        }

        .plan-card.popular:hover {
            background-color: #000099;
        }

        .plan-card.popular .btn {
            background-color: var(--white);
            color: var(--primary);
        }

        .plan-card.popular .btn:hover {
            background-color: #f0f0f0;
        }

        /* Efeito de brilho nos cards */
        .plan-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            background-size: 200% 200%;
            animation: shine 3s infinite;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .plan-card:hover::after {
            opacity: 1;
        }

        .popular-badge {
            position: absolute;
            top: -12px;
            right: 20px;
            background: #FFD700;
            color: var(--primary);
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 600;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }

        .plan-card:hover .popular-badge {
            transform: translateY(-3px);
        }

        .plan-price {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 1rem 0;
            position: relative;
            display: inline-block;
        }

        .plan-price::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 2px;
            background: currentColor;
            opacity: 0.2;
        }

        .plan-features {
            list-style: none;
            margin: 1.5rem 0;
        }

        .plan-features li {
            margin: 0.75rem 0;
            transition: transform 0.2s ease;
            padding: 0.5rem;
        }

        .plan-features li:hover {
            transform: translateX(5px);
            background: rgba(0, 0, 139, 0.05);
            border-radius: 5px;
        }

        .plan-features i {
            margin-right: 0.5rem;
            color: #22c55e;
        }

        /* Animações */
        @keyframes shine {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        @keyframes spin {
            0% { transform: translateY(-50%) rotate(0deg); }
            100% { transform: translateY(-50%) rotate(360deg); }
        }

        /* Estado de Loading */
        .btn.loading {
            position: relative;
            pointer-events: none;
            opacity: 0.8;
        }

        .btn.loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            border: 2px solid transparent;
            border-top-color: currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5rem;
            }

            .container {
                padding: 0 1rem;
            }
            
            .hero-logo {
                max-width: 200px;
            }

            .back-btn {
                top: 10px;
                left: 10px;
            }
        }

        /* Mensagens de Erro/Alerta */
        .alert-error {
            background-color: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
            padding: 1rem;
            border-radius: 0.5rem;
            margin: 1rem auto;
            max-width: 800px;
            text-align: center;
        }

        .alert-error i {
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <a href="../index.php" class="back-btn">
        <i class="fas fa-arrow-left"></i> Voltar ao Sistema
    </a>

    <!-- Hero Section Personalizada para Usuários -->
    <section class="hero">
        <div class="container">
            <img src="assets/img/cerebro_azul.png" alt="Planeja Aqui Logo" class="hero-logo">
            <h1>Olá, <?php echo htmlspecialchars($nome_usuario); ?>!<br> Você ainda não possui Este Módulo.</h1>
            <h2>Desbloqueie GRATUITAMENTE</h2>
            <p>Para você que já é um membro do Planeja Aqui, o Custo é Zero.</p>
            <a href="#planos" class="btn btn-primary">Ver Preços Exclusivos</a>
        </div>
    </section>

    <!-- Benefícios Focados em Usuários -->
    <section class="benefits">
        <div class="container">
            <h2 style="text-align: center; color: var(--primary); font-size: 2rem; margin-bottom: 2rem;">
                Benefícios Exclusivos para Membros
            </h2>
            <div class="benefits-grid">
                <div class="benefit-card">
                    <i class="fas fa-sync-alt"></i>
                    <h3>Integração Total</h3>
                    <p>Sincronize com seus outros módulos do Planeja Aqui</p>
                </div>
                <div class="benefit-card">
                    <i class="fas fa-clock"></i>
                    <h3>Revisões Programadas</h3>
                    <p>Sistema inteligente de revisões baseado na curva do esquecimento</p>
                </div>
                <div class="benefit-card">
                    <i class="fas fa-calendar-alt"></i>
                    <h3>Planejamento Personalizado</h3>
                    <p>Cronogramas adaptados ao seu perfil e objetivos de estudo</p>
                </div>
                <div class="benefit-card">
                    <i class="fas fa-percentage"></i>
                    <h3>Desconto Especial</h3>
                    <p>Preços exclusivos para membros ativos</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Planos com Preços Especiais -->
    <section id="planos" class="plans">
        <div class="container">
            <h2 style="text-align: center; color: var(--primary); font-size: 2rem; margin-bottom: 2rem;">
                Seus Preços Exclusivos de Membro
            </h2>
            <div class="plans-grid">
                <!-- Plano Mensal -->
                <div class="plan-card">
                    <h3>Plano Mensal</h3>
                    <div class="plan-price">
                        R$ <?php echo number_format($preco_mensal, 2, ',', '.'); ?><span style="font-size: 1rem;">/mês</span>
                    </div>
                    <p style="color: var(--primary); margin-bottom: 1rem;">Preço especial de membro</p>
                    <ul class="plan-features">
                        <li><i class="fas fa-check-circle"></i>Acesso ao Cronograma Inteligente</li>
                        <li><i class="fas fa-check-circle"></i>Integração com outros módulos</li>
                        <li><i class="fas fa-check-circle"></i>Planejamento Personalizado</li>
                        <li><i class="fas fa-check-circle"></i>Revisões Programadas</li>
                        <li><i class="fas fa-check-circle"></i>Progresso do Estudo</li>
                    </ul>
                    <form action="processar_pagamento.php" method="POST">
                        <input type="hidden" name="plano_id" value="1">
                        <input type="hidden" name="tipo" value="mensal">
                        <button type="submit" class="btn btn-primary">Selecionar Plano</button>
                    </form>
                </div>

                <!-- Plano Anual -->
                <div class="plan-card popular">
                    <span class="popular-badge">Mais Vantajoso</span>
                    <h3>Plano Anual</h3>
                    <div class="plan-price">
                        R$ <?php echo number_format($preco_anual, 2, ',', '.'); ?><span style="font-size: 1rem;">/ano</span>
                    </div>
                    <p style="color: #FFD700; margin-bottom: 1rem;">Economize <?php echo $desconto; ?></p>
                    <ul class="plan-features">
                    <li><i class="fas fa-check-circle"></i>Acesso ao Cronograma Inteligente</li>
                        <li><i class="fas fa-check-circle"></i>Integração com outros módulos</li>
                        <li><i class="fas fa-check-circle"></i>Planejamento Personalizado</li>
                        <li><i class="fas fa-check-circle"></i>Revisões Programadas</li>
                        <li><i class="fas fa-check-circle"></i>Progresso do Estudo</li>
                        <li><i class="fas fa-check-circle"></i>Maior economia</li>                        
                    </ul>
                    <form action="processar_pagamento.php" method="POST">
                        <input type="hidden" name="plano_id" value="2">
                        <input type="hidden" name="tipo" value="anual">
                        <button type="submit" class="btn btn-primary">Selecionar Plano</button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- Mensagens de Erro/Alerta -->
    <?php if (isset($_SESSION['erro_pagamento'])): ?>
    <div class="alert-error">
        <i class="fas fa-exclamation-circle"></i>
        <?php 
        echo $_SESSION['erro_pagamento'];
        unset($_SESSION['erro_pagamento']);
        ?>
    </div>
    <?php endif; ?>

    <!-- Scripts do arquivo original aqui -->
    <script>
        // Smooth scroll para as âncoras
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });

        // Adiciona classe loading ao botão durante o envio do formulário
        document.querySelectorAll('form').forEach(form => {
            form.addEventListener('submit', function() {
                const button = this.querySelector('button');
                button.classList.add('loading');
                button.disabled = true;
            });
        });
    </script>
</body>
</html>