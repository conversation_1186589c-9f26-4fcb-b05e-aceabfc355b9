<?php
include_once("../assets/config.php");
include_once("../includes/auth.php");

// Verifica autenticação
$id_usuario = verificarAutenticacao($conexao);

// Recebe e decodifica os dados JSON
$dados = json_decode(file_get_contents('php://input'), true);
$resposta = ['success' => false, 'message' => ''];

// Verifica se os dados foram recebidos corretamente
if (!$dados) {
    $resposta['message'] = 'Dados não recebidos corretamente.';
    echo json_encode($resposta);
    exit;
}

// Validação básica dos dados
if (empty($dados['nome']) || empty($dados['data_inicio']) || empty($dados['data_fim']) || empty($dados['tempo_planejamento'])) {
    $resposta['message'] = 'Todos os campos obrigatórios devem ser preenchidos.';
    echo json_encode($resposta);
    exit;
}

// Validação das matérias e cursos
if (empty($dados['materias']) || !is_array($dados['materias'])) {
    $resposta['message'] = 'Selecione pelo menos uma matéria.';
    echo json_encode($resposta);
    exit;
}

if (empty($dados['cursos']) || !is_array($dados['cursos'])) {
    $resposta['message'] = 'Selecione pelo menos um curso.';
    echo json_encode($resposta);
    exit;
}

try {
    // Inicia transação
    pg_query($conexao, "BEGIN");
    
    // Insere o planejamento
    $query_inserir_planejamento = "INSERT INTO appEstudo.planejamento 
                                  (nome, data_inicio, data_fim, tempo_planejamento, usuario_idusuario) 
                                  VALUES ($1, $2, $3, $4, $5) 
                                  RETURNING idplanejamento";
    
    $resultado = pg_query_params(
        $conexao, 
        $query_inserir_planejamento, 
        [
            $dados['nome'],
            $dados['data_inicio'],
            $dados['data_fim'],
            $dados['tempo_planejamento'],
            $id_usuario
        ]
    );
    
    if (!$resultado) {
        throw new Exception("Erro ao inserir planejamento: " . pg_last_error($conexao));
    }
    
    $row = pg_fetch_assoc($resultado);
    $id_planejamento = $row['idplanejamento'];
    
    // Insere as matérias selecionadas
    foreach ($dados['materias'] as $id_materia) {
        $query_inserir_materia = "INSERT INTO appEstudo.planejamento_materia 
                                 (planejamento_idplanejamento, materia_idmateria) 
                                 VALUES ($1, $2)";
        
        $resultado_materia = pg_query_params(
            $conexao, 
            $query_inserir_materia, 
            [$id_planejamento, $id_materia]
        );
        
        if (!$resultado_materia) {
            throw new Exception("Erro ao inserir matéria: " . pg_last_error($conexao));
        }
    }
    
    // Insere os cursos selecionados (ou verifica se já existem)
    foreach ($dados['cursos'] as $id_curso) {
        // Verifica se o usuário já tem este curso
        $query_verificar = "SELECT COUNT(*) FROM appEstudo.usuario_has_curso 
                           WHERE usuario_idusuario = $1 AND curso_idcurso = $2";
        
        $resultado_verificar = pg_query_params(
            $conexao, 
            $query_verificar, 
            [$id_usuario, $id_curso]
        );
        
        if (!$resultado_verificar) {
            throw new Exception("Erro ao verificar curso: " . pg_last_error($conexao));
        }
        
        $row = pg_fetch_row($resultado_verificar);
        
        // Se o usuário ainda não tem este curso, insere
        if ($row[0] == 0) {
            $query_inserir_curso = "INSERT INTO appEstudo.usuario_has_curso 
                                  (usuario_idusuario, curso_idcurso) 
                                  VALUES ($1, $2)";
            
            $resultado_curso = pg_query_params(
                $conexao, 
                $query_inserir_curso, 
                [$id_usuario, $id_curso]
            );
            
            if (!$resultado_curso) {
                throw new Exception("Erro ao inserir curso: " . pg_last_error($conexao));
            }
        }
    }
    
    // Commit da transação
    pg_query($conexao, "COMMIT");
    
    $resposta['success'] = true;
    $resposta['message'] = 'Planejamento criado com sucesso!';
    
} catch (Exception $e) {
    // Rollback em caso de erro
    pg_query($conexao, "ROLLBACK");
    $resposta['success'] = false;
    $resposta['message'] = $e->getMessage();
}

// Retorna a resposta em formato JSON
echo json_encode($resposta);
exit;
?>

