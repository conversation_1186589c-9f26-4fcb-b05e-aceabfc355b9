<?php
// Topo de dashboard-estatistica.php

if (!defined('MEU_SISTEMA_PHP_DASHBOARD_VALIDO')) {
    // Se a constante não estiver definida, o arquivo foi acessado diretamente.
    // Mostra a mensagem SweetAlert e redireciona.

    echo '<!DOCTYPE html>
    <html>
    <head>
        <title>Acesso Negado - Redirecionando...</title>
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <style>
            body { margin: 0; padding: 0; font-family: "Quicksand", sans-serif; display: flex; align-items: center; justify-content: center; min-height: 100vh; background-color: #f0f2f5; }
            .swal2-popup { font-family: "Quicksand", sans-serif !important; }
        </style>
    </head>
    <body>
        <script>
            document.addEventListener("DOMContentLoaded", function() {
                Swal.fire({
                    title: "Acesso Direto Negado",
                    text: "Esta página não pode ser acessada diretamente. Você será redirecionado.",
                    icon: "error",
                    confirmButtonText: "OK",
                    confirmButtonColor: "#00008B",
                    allowOutsideClick: false
                }).then((result) => {
                    window.location.href = "index.php"; // Redireciona para a página principal
                });
            });
        </script>
    </body>
    </html>';
    exit; // Para a execução do script.
}

// O restante do código original de dashboard-inicio.php continua aqui...
// Ex: echo "<h1>Conteúdo do Dashboard de Início</h1>";
// Suas consultas e lógica específica para este dashboard...
?>

    <style>
        * {
            margin: 0;
            /*padding: 0;*/
            box-sizing: border-box;
        }



        .dashboard-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 10px;
        }

        .dashboard-grid-estatitisticas {
            display: grid;
            grid-template-columns: repeat(1, 1fr);
            gap: 10px;
        }

        .dashboard-card {
            background: var(--white);
            box-shadow: 0 4px 6px var(--shadow);
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
        }

        .card-header {
            background: var(--primary-blue);
            color: var(--white);
            padding: 0.5rem;
            margin: 0;
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            font-size: 1.2rem;
            font-weight: 600;
            letter-spacing: 0.5px;
            text-shadow: none;

            /* Adicione estas propriedades para centralizar */
            display: flex; /* Ativa o Flexbox */
            justify-content: center; /* Centraliza horizontalmente */
            align-items: center; /* Centraliza verticalmente */
            text-align: center; /* Garante que o texto seja centralizado */
            
        }

        .card-content {
            background: var(--card-text);
            min-height: 300px;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: var(--primary-blue);
        }

        @media (max-width: 1200px) {
            .dashboard-grid-estatitisticas {
                grid-template-columns: 1fr;
            }

            .dashboard-card {
                margin-bottom: 20px;
            }
        }
    </style>


    <div class="dashboard-container">
        <div class="dashboard-grid-estatitisticas">
            <?php 
            include './variaveis_compartilhadas.php'; // se estiver no mesmo diretório
            // OU
            // include '../variaveis_compartilhadas.php'; // se estiver um nível acima
            // OU
            // include 'includes/variaveis_compartilhadas.php'; // se estiver em uma pasta includes
            ?>
            
            <!-- Gráfico Radar -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Distribuição de Tempo por Matéria</h3>
                </div>
                <div class="card-content grafico_radar">
                    <?php include '0grafico_teia_aranha.php'; ?>
                </div>
            </div>

            <!-- Gráfico Horas Líquidas por Dia -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Hora Líquida por Dia</h3>
                </div>
                <div class="card-content grafico_hora_liquida_dia">
                    <?php include '0calcula_dias.php'; ?>
                </div>
            </div>

            <!-- Heatmap (Largura Total) -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Mapa Anual de Calor dos Estudos</h3>
                </div>
                <div class="card-content grafico_heatmap">
                    <?php include '0heatmap_calendario.php'; ?>
                </div>
            </div>

            <!-- Gráfico Estudo Dia Selecionável -->
             <div class="dashboard-card">
                <div class="card-header">
                    <h3>Análise de Estudo por Dia e Mês</h3>
                </div>
                <div class="card-content grafico_estudo_dia_selecionavel">
                    <?php include '0grafico_estudo_dia_selecionavel.php'; ?>
                </div>
            </div>

            <!-- Gráfico Dias Estudados por Mês -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Dias Estudados por Mês</h3>
                </div>
                <div class="card-content grafico_dia_estudado_mes">
                    <?php include '0estatistica.php'; ?>
                </div>
            </div>

            <!-- Gráfico Questões -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Análise de Questões por Matéria</h3>
                </div>
                <div class="card-content questoes">
                    <?php include '0grafico_questoes.php'; ?>
                </div>
            </div>

            <!-- Gráfico Tempo por Turno -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Distribuição do Tempo de Estudo por Turno</h3>
                </div>
                <div class="card-content grafico_tempo_turno">
                    <?php include '0grafico_tempo_por_turno.php'; ?>
                </div>
            </div>

            <!-- Simulados -->
            <div class="dashboard-card">
                <div class="card-header">
                    <h3>Histórico de Simulados</h3>
                </div>
                <div class="card-content simulado">
                    <?php include 'simulado.php'; ?>
                </div>
            </div>

        </div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Verifica se os componentes carregaram corretamente
        const cards = document.querySelectorAll('.card-content');
        cards.forEach(card => {
            if (!card.innerHTML.trim()) {
                card.innerHTML = '<div class="loading"><i class="fas fa-circle-notch fa-spin"></i> Carregando...</div>';
            }
        });

        // Trata erros gerais
        window.onerror = function(msg, url, lineNo, columnNo, error) {
            console.error('Erro:', msg, 'URL:', url, 'Linha:', lineNo);
            return false;
        };
    });
    </script>