/*
 * Home Netflix CSS - Herda variáveis do style.css principal
 * Variáveis específicas do home podem ser definidas aqui se necessário
 */



/* Estilos específicos do Home */

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--light-bg);
    background-image:
        radial-gradient(circle at 20% 80%, rgba(0, 0, 139, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(57, 73, 171, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(74, 74, 74, 0.03) 0%, transparent 50%);
    min-height: 100vh;
    color: var(--text-primary);
    line-height: 1.6;
    transition: var(--transition);
    overflow-x: hidden;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header-barra - <PERSON><PERSON> estilo da index.php */
.header-barra {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 7px 20px;
    border-bottom: 1px solid var(--border);
    box-shadow: var(--shadow-light);
    background: var(--card-bg);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: var(--transition);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.logo img {
    height: 50px;
    width: auto;
    transition: opacity 0.3s ease;
}

/* Controle de visibilidade dos logos */
.logo .logo-light {
    opacity: 1;
}

.logo .logo-dark {
    opacity: 0;
    position: absolute;
}

[data-theme="dark"] .logo .logo-light {
    opacity: 0;
}

[data-theme="dark"] .logo .logo-dark {
    opacity: 1;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    background: var(--hover);
    color: var(--text-primary);
}

.user-info i {
    color: var(--primary);
    font-size: 1.2rem;
}

.user-name {
    color: var(--text);
    font-weight: 600;
}

.theme-toggle {
    display: flex;
    align-items: center;
}

.theme-btn {
    background: var(--card-bg);
    border: 2px solid var(--border);
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-primary);
    font-size: 1.1rem;
    box-shadow: var(--shadow-light);
}

.theme-btn:hover {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.theme-btn:active {
    transform: scale(0.95);
}

.back-btn {
    background: var(--card-bg);
    border: 2px solid var(--border);
    border-radius: 12px;
    padding: 8px 15px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-primary);
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    box-shadow: var(--shadow-light);
}

.back-btn:hover {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* Seção Principal */
.main-section {
    padding: 100px 0 20px; /* Espaço para header fixo */
    background: var(--light-bg);
}

.welcome-header {
    text-align: center;
    margin-bottom: 40px;
}

.welcome-title {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 700;
    margin-bottom: 15px;
    color: var(--text-primary);
}

.highlight {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.welcome-subtitle {
    font-size: 1.2rem;
    opacity: 0.8;
    font-weight: 400;
    color: var(--text-secondary);
}

/* Cards Section */
.cards-section {
    padding: 20px 0 60px;
    background: var(--light-bg);
}

/* Cards Grid */
.cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 40px;
    padding: 40px 0;
    max-width: 1000px;
    margin: 0 auto;
}

/* Grid de Leis */
.leis-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    padding: 20px 0;
}

/* Card de Lei */
.lei-card {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    padding: 0;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    border: 1px solid var(--border);
    height: 420px;
    display: flex;
    flex-direction: column;
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

/* Modo claro - Cards mais sólidos e definidos */
[data-theme="light"] .lei-card,
[data-theme="light"] .main-card {
    background: rgba(255, 255, 255, 0.98);
    border: 1px solid rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(8px);
    box-shadow:
        0 8px 32px rgba(0, 0, 0, 0.08),
        0 2px 8px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 1);
}

[data-theme="light"] .lei-card:hover,
[data-theme="light"] .main-card:hover {
    background: rgba(255, 255, 255, 0.98);
    border-color: rgba(102, 126, 234, 0.3);
    box-shadow:
        0 20px 60px rgba(102, 126, 234, 0.12),
        0 8px 32px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 1);
}

/* Modo escuro - Glassmorphism */
[data-theme="dark"] .lei-card,
[data-theme="dark"] .main-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
}

[data-theme="dark"] .lei-card:hover,
[data-theme="dark"] .main-card:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(102, 126, 234, 0.4);
}

/* Melhorar contraste do texto no modo claro */
[data-theme="light"] .lei-card-title,
[data-theme="light"] .card-title {
    color: #1a1a1a;
    font-weight: 700;
}

[data-theme="light"] .lei-card-subtitle,
[data-theme="light"] .lei-card-description,
[data-theme="light"] .card-description {
    color: #4a4a4a;
    opacity: 0.9;
}

[data-theme="light"] .btn-estudar-card {
    box-shadow:
        0 8px 24px rgba(102, 126, 234, 0.25),
        0 2px 8px rgba(0, 0, 0, 0.08);
}

/* Fallback para navegadores sem suporte a backdrop-filter */
@supports not (backdrop-filter: blur(10px)) {
    [data-theme="light"] .lei-card,
    [data-theme="light"] .main-card {
        background: rgba(255, 255, 255, 1);
        border: 1px solid rgba(0, 0, 0, 0.12);
    }
}

.lei-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0;
    transition: var(--transition);
    z-index: 1;
}

.lei-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary);
}

.lei-card:hover::before {
    opacity: 0.1;
}

.lei-card-header {
    padding: 15px;
    position: relative;
    z-index: 2;
}

.lei-card-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin-bottom: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.lei-card-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.lei-card-subtitle {
    font-size: 0.9rem;
    opacity: 0.7;
    font-weight: 400;
    line-height: 1.4;
    color: var(--text-secondary);
}

.lei-card-body {
    padding: 0 30px;
    flex: 1;
    position: relative;
    z-index: 2;
}

.lei-card-description {
    font-size: 0.95rem;
    opacity: 0.8;
    line-height: 1.5;
    margin-bottom: 20px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.lei-card-footer {
    padding: 30px;
    position: relative;
    z-index: 2;
}

.btn-estudar-card {
    width: 100%;
    background: var(--primary-gradient);
    border: none;
    padding: 15px;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    box-shadow: var(--shadow-light);
}

.btn-estudar-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

/* Main Cards */
.main-card {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border-radius: 28px;
    padding: 48px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    border: 1px solid var(--border);
    height: 320px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.08),
        0 4px 16px rgba(0, 0, 0, 0.04),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.main-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--primary-gradient);
    opacity: 0;
    transition: all 0.4s ease;
    z-index: 1;
}

.main-card:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: var(--shadow-heavy);
    border-color: var(--primary);
}

.main-card:hover::before {
    opacity: 0.08;
}

.card-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: white;
    background: var(--primary-gradient);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
    position: relative;
    z-index: 2;
    transition: all 0.4s ease;
}

.main-card:hover .card-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 20px 40px rgba(102, 126, 234, 0.4);
}

.card-content {
    position: relative;
    z-index: 2;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.card-title {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.main-card:hover .card-title {
    color: var(--primary);
}

.card-description {
    font-size: 1.1rem;
    opacity: 0.8;
    line-height: 1.6;
    color: var(--text-secondary);
    transition: all 0.3s ease;
}

.main-card:hover .card-description {
    opacity: 1;
}


.card-arrow {
    position: relative;
    z-index: 2;
    align-self: flex-end;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    transition: all 0.4s ease;
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.main-card:hover .card-arrow {
    transform: translateX(10px) scale(1.1);
    box-shadow: 0 15px 30px rgba(102, 126, 234, 0.4);
}

/* Animações */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.main-card {
    animation: fadeInUp 0.8s ease-out forwards;
    opacity: 0;
}

.main-card:nth-child(1) { animation-delay: 0.2s; }
.main-card:nth-child(2) { animation-delay: 0.4s; }

.lei-card {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

.lei-card:nth-child(1) { animation-delay: 0.1s; }
.lei-card:nth-child(2) { animation-delay: 0.2s; }
.lei-card:nth-child(3) { animation-delay: 0.3s; }
.lei-card:nth-child(4) { animation-delay: 0.4s; }
.lei-card:nth-child(5) { animation-delay: 0.5s; }

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.ativo {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    transform: scale(0.8) translateY(50px);
    transition: var(--transition);
    box-shadow: var(--shadow-heavy);
    border: 1px solid var(--border);
}

.modal-overlay.ativo .modal-content {
    transform: scale(1) translateY(0);
}

.modal-close {
    position: absolute;
    top: 20px;
    right: 20px;
    background: var(--hover);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    color: var(--text-primary);
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
    z-index: 10;
}

.modal-close:hover {
    background: var(--primary);
    color: white;
    transform: rotate(90deg);
}

.modal-header {
    padding: 40px 40px 20px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.modal-lei-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    background: var(--primary-gradient);
}

.modal-lei-nome {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 5px;
    color: var(--text-primary);
}

.modal-lei-completo {
    font-size: 0.9rem;
    opacity: 0.7;
    margin: 0;
    color: var(--text-secondary);
}

.modal-body {
    padding: 0 40px 20px;
}



.modal-description {
    margin-bottom: 30px;
}

.modal-description h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #667eea;
}

.modal-desc-text {
    font-size: 0.95rem;
    line-height: 1.6;
    opacity: 0.8;
}

.modal-preview h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #667eea;
}

.preview-artigos {
    background: rgba(255, 255, 255, 0.03);
    border-radius: 10px;
    padding: 20px;
    max-height: 200px;
    overflow-y: auto;
}

.preview-artigo {
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.preview-artigo:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.preview-artigo-numero {
    font-weight: 600;
    color: #667eea;
    margin-bottom: 5px;
}

.preview-artigo-texto {
    font-size: 0.9rem;
    opacity: 0.8;
    line-height: 1.5;
}

.modal-footer {
    padding: 20px 40px 40px;
}

.btn-estudar {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 18px;
    border-radius: 15px;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.btn-estudar:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

/* Footer */
.footer {
    background: #0a0a0a;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 60px 0 30px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #667eea;
}

.footer-section p {
    opacity: 0.7;
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 10px;
    opacity: 0.7;
    transition: opacity 0.3s ease;
    cursor: pointer;
}

.footer-section ul li:hover {
    opacity: 1;
    color: #667eea;
}

.footer-bottom {
    text-align: center;
    padding-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.5;
}

/* Responsividade */
@media (max-width: 768px) {
    .header-content {
        padding: 10px 20px;
    }

    .logo-text {
        font-size: 1.5rem;
    }

    .header-actions {
        flex-direction: column;
        gap: 10px;
        align-items: flex-end;
    }

    .user-info {
        font-size: 0.9rem;
    }

    .btn-logout {
        padding: 8px 16px;
        font-size: 0.8rem;
    }

    .welcome-title {
        font-size: 2rem;
    }

    .welcome-subtitle {
        font-size: 1rem;
    }

    .cards-grid {
        grid-template-columns: 1fr;
        gap: 30px;
        padding: 20px 0;
    }

    .main-card {
        height: auto;
        margin: 0 10px;
        padding: 30px;
    }

    .card-title {
        font-size: 1.8rem;
    }

    .card-description {
        font-size: 1rem;
    }

    .leis-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 10px 0;
    }

    .lei-card {
        height: auto;
        margin: 0 10px;
    }

    .lei-card-header {
        padding: 20px;
    }

    .lei-card-body {
        padding: 0 20px;
    }

    .lei-card-footer {
        padding: 20px;
    }



    .modal-content {
        margin: 20px;
        width: calc(100% - 40px);
        max-height: 90vh;
    }

    .modal-header {
        padding: 30px 20px 15px;
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .modal-body,
    .modal-footer {
        padding-left: 20px;
        padding-right: 20px;
    }




}

@media (max-width: 480px) {
    .container {
        padding: 0 10px;
    }

    .hero-content {
        padding: 0 15px;
    }

    .welcome-title {
        font-size: 1.8rem;
    }

    .welcome-subtitle {
        font-size: 0.9rem;
    }

    .main-card {
        margin: 0 5px;
        padding: 25px;
    }

    .card-title {
        font-size: 1.6rem;
    }

    .card-description {
        font-size: 0.95rem;
    }

    .lei-card {
        margin: 0 5px;
    }

    .lei-card-header {
        padding: 15px;
    }

    .lei-card-body {
        padding: 0 15px;
    }

    .lei-card-footer {
        padding: 15px;
    }



    .modal-header {
        padding: 20px 15px 10px;
    }

    .modal-body,
    .modal-footer {
        padding-left: 15px;
        padding-right: 15px;
    }

    .btn-estudar {
        padding: 15px;
        font-size: 1rem;
    }
}
