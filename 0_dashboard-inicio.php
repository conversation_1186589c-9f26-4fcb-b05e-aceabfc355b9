<?php
// Topo de dashboard-inicio.php

if (!defined('MEU_SISTEMA_PHP_DASHBOARD_VALIDO')) {
    // Se a constante não estiver definida, o arquivo foi acessado diretamente.
    // Mostra a mensagem SweetAlert e redireciona.

    echo '<!DOCTYPE html>
    <html>
    <head>
        <title>Acesso Negado - Redirecionando...</title>
        <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
        <style>
            body { margin: 0; padding: 0; font-family: "Quicksand", sans-serif; display: flex; align-items: center; justify-content: center; min-height: 100vh; background-color: #f0f2f5; }
            .swal2-popup { font-family: "Quicksand", sans-serif !important; }
        </style>
    </head>
    <body>
        <script>
            document.addEventListener("DOMContentLoaded", function() {
                Swal.fire({
                    title: "Acesso Direto Negado",
                    text: "Esta página não pode ser acessada diretamente. Você será redirecionado.",
                    icon: "error",
                    confirmButtonText: "OK",
                    confirmButtonColor: "#00008B",
                    allowOutsideClick: false
                }).then((result) => {
                    window.location.href = "index.php"; // Redireciona para a página principal
                });
            });
        </script>
    </body>
    </html>';
    exit; // Para a execução do script.
}

// O restante do código original de dashboard-inicio.php continua aqui...
// Ex: echo "<h1>Conteúdo do Dashboard de Início</h1>";
// Suas consultas e lógica específica para este dashboard...
?>

<?php
//dashboard-inicio.php
include_once '0constancia.php';
?>


    <style>
        .modal-dashboard {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .modal-dashboard.ativo {
            visibility: visible;
            opacity: 1;
        }
        .dashboard-grid-duasColunas {
            display: grid;
            grid-template-columns: 8fr 1fr;
            gap: 0rem;
            max-width: 1400px;
            margin: 0 auto;
            padding-top: 0rem;
            padding-right: 0rem;
            padding-left: 0rem;
            padding-bottom: 0.5rem; /* valor aumentado para a parte inferior */
        }

        .dashboard-grid-tresColunas {
            display: grid;
            grid-template-columns: 3fr 1fr;  /* Agora define três colunas */
            gap: 0.5rem;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0rem;
        }

        .dashboard-grid-umaColuna {

            max-width: 1400px;


        }

        .cards-stack {
            display: flex;
            flex-direction: column;
            
        }

        .card {
            background: var(--white);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 4px 6px var(--shadow);
            transition: transform 0.3s ease;

        }
        .card-centralizado {
            background: var(--white);
            border: 1px;
            overflow: hidden;
            /*box-shadow: 0 4px 6px var(--shadow);*/
            transition: transform 0.3s ease;
            border: 1px;
            /* Adicionando centralização */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            /*padding: 1rem;*/
        }

        /* Se houver elementos internos que precisam ocupar toda largura */
        .card > * {
            width: 100%;
        }
        .card:hover {
            transform: translateY(-5px);
        }



        .card-header.blue {
            background: var(--primary-blue);
        }

        .card-header.red {
            background: var(--dark-red);
        }

        .card-title {
            font-family: 'Quicksand', sans-serif;
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
        }

        .card-subtitle {
            font-size: 0.9rem;
            opacity: 0.8;
            font-family: 'Courier Prime', monospace;
        }

        .card-content {
            padding: 0.5rem;
          
        }

        .card-content_planejamento {
            padding: 1rem;
          
        }

        .chart-container {
            min-height: 400px;
            width: 100%;
            margin: 0 auto;
        }

        .empty-state {
            text-align: center;
            padding: 2rem;
            color: #666;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #ccc;
        }

        .info-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255, 255, 255, 0.1);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-family: 'Courier Prime', monospace;
        }

        .meta-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 1rem;
            border-radius: 10px;
            margin-top: 1rem;
            text-align: center;
            font-family: 'Courier Prime', monospace;
        }

        .meta-info .tempo {
            font-size: 1.2rem;
            font-weight: bold;
            color: white;
        }

        /* Estados de aviso */
        .warning-message {
            background: #fff3cd;
            border: 1px solid #ffeeba;
            color: #856404;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            text-align: center;
        }

        /* Responsividade */
        @media (max-width: 1024px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .dashboard-grid {
                padding: 0.5rem;
            }

            .card {
                margin-bottom: 1rem;
            }

            .card-header {
                padding: 1rem;
            }
        }

        .dashboard-card-inicio {
            background: var(--white);
            border-radius: 15px;

            overflow: hidden;

            transition: transform 0.3s ease;
            max-width: 1200px;

        }


        /* Estilos do Modal */
        .modal-estudo {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 9999;
            visibility: hidden;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .modal-estudo.ativo {
            visibility: visible;
            opacity: 1;
        }

        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(3px);
        }



        .modal-estudo.ativo .modal-content {
            transform: translate(-50%, -50%) scale(1);
        }



        .modal-titulo {
            font-family: 'Quicksand', sans-serif;
            color: var(--dark-bg);
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
            background: rgba(0, 0, 0, 0.1);
            border-bottom: 2px solid var(--vintage-gold);

            /* Centralização vertical e horizontal */
            display: flex;
            align-items: center;
            justify-content: center;

            /* Ajuste do padding para criar a faixa */
            padding: 0.8rem 0;

            /* Opcional: arredondar as bordas da faixa */
            border-radius: 4px;
        }

        .modal-body {
            overflow-y: auto;
            max-height: calc(90vh - 6rem);
            padding-right: 1rem;
        }

        /* Estilização da barra de rolagem */
        .modal-body::-webkit-scrollbar {
            width: 8px;
        }

        .modal-body::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
        }

        .modal-body::-webkit-scrollbar-thumb {
            background: var(--vintage-gold);
            border-radius: 4px;
        }

        .turno-linha {
            display: flex;
            align-items: center;
            gap: 1rem;
            background-color: var(--white);
        }

        .turno-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .turno-item h3 {
            margin: 0;
            font-size: 1rem;
            white-space: nowrap;
        }

        .turno-item strong {
            color: #0000cc;
            white-space: nowrap;
        }

        .turno-nome {
            font-weight: bold;
            color: #000099;
            margin-right: 0.5rem;
        }

        .tempo {
            color: #666;
            font-size: 0.9rem;
        }

        .turno-item small {
            color: #666;
            font-style: italic;
        }

        /* Para telas pequenas */
        @media (max-width: 768px) {
            .turno-linha {
                flex-wrap: wrap;
            }
        }

    </style>

    <div class="dashboard-grid-duasColunas">
        <div class="cards-stack">
        <!-- Calendário de Bolinhas -->
            <div class="dias-grid">
                <?php foreach ($dias_estudo as $index => $dia):
                    $dia_semana = date('w', strtotime($dia['data']));
                    $is_weekend = ($dia_semana == 0 || $dia_semana == 6);

                    // Define a classe para o dia atual
                    if ($dia['data'] == $hoje_constacia && $dia['estudou'] == 'f') {
                        $classe_dia = 'dia-atual'; // Neutro
                    } else {
                        $classe_dia = $dia['estudou'] == 't' ? 'estudou' : 'nao-estudou';
                    }

                    if ($is_weekend) $classe_dia .= ' weekend';
                    ?>
                    <div class="dia-item <?php echo $classe_dia; ?>"
                         title="<?php echo date('d/m/Y', strtotime($dia['data'])); ?>">
                        <span class="dia-numero"><?php echo date('d', strtotime($dia['data'])); ?></span>
                        <?php if ($dia['estudou'] == 't'): ?>
                            <div class="dia-emoji">✨</div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
            <div class="card-centralizado">
                <h1 class="titulo-mensagem"><?php echo $mensagem['título']; ?></h1>
                <p class="texto-mensagem"><?php echo $mensagem['mensagem']; ?></p>
            </div>

        <!-- NOVO BLOCO - Seu Turno -->
        <!-- NOVO BLOCO - Seu Turno -->
        <div class="card-centralizado">
            <div class="turno-linha" id="turno-container">
                <div class="turno-item">
                    <h3>Turno que você mais estuda:</h3>
                </div>
                <div class="turno-header">
                    <i class="fas fa-spinner fa-spin"></i>
                    <strong>Carregando...</strong>
                </div>
            </div>
        </div>

        <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Busca os dados do turno via AJAX
            fetch('obter_turno.php?id_usuario=<?php echo $id_usuario; ?>&id_planejamento=<?php echo $id_planejamento; ?>')
                .then(response => response.json())
                .then(data => {
                    // Atualiza o HTML com os dados recebidos
                    const container = document.getElementById('turno-container');
                    container.innerHTML = `
                        <div class="turno-item">
                            <h3>Turno que você mais estuda:</h3>
                        </div>
                        <div class="turno-header">
                            <i class="fas fa-${data.icon}"></i>
                            <strong>${data.texto}</strong>
                        </div>
                        <div class="turno-info">
                            <span class="turno-nome">${data.turno}</span>
                            <span class="tempo">${data.tempo}</span>
                        </div>
                        <div class="turno-item">
                            <small>${data.desc}</small>
                        </div>
                    `;
                })
                .catch(error => {
                    console.error('Erro ao carregar dados do turno:', error);
                    // Em caso de erro, mostra uma mensagem padrão
                    const container = document.getElementById('turno-container');
                    container.innerHTML = `
                        <div class="turno-item">
                            <h3>Turno que você mais estuda:</h3>
                        </div>
                        <div class="turno-header">
                            <i class="fas fa-exclamation-circle"></i>
                            <strong>Erro ao Carregar</strong>
                        </div>
                        <div class="turno-info">
                            <span class="turno-nome">Sem Dados</span>
                            <span class="tempo">0min</span>
                        </div>
                        <div class="turno-item">
                            <small>Não foi possível buscar suas estatísticas.</small>
                        </div>
                    `;
                });
        });
        </script>

        </div>
        <div class="cards-stack">
            <!-- Contador de Streak -->
            <div class="card-centralizado">
                <div class="streak-number">
                    <?php echo $sequencia_atual; ?>
                    <span class="streak-label">dias seguidos</span>
                </div>
                <?php if ($sequencia_atual > 0): ?>
                    <div class="streak-flames">
                        <?php for ($i = 0; $i < min($sequencia_atual, 5); $i++): ?>
                            <div class="flame">🔥</div>
                        <?php endfor; ?>
                    </div>
                <?php endif; ?>

                <!-- ADICIONAR ESSE TRECHO AQUI -->
                <?php if ($maior_sequencia['tamanho'] >= $sequencia_atual): ?>
                    <div class="maior-sequencia">
                        <div class="maior-sequencia-numero" title="<?php echo $maior_sequencia['periodo']; ?>">
                            <span class="maior-sequencia-label">Seu recorde:</span>
                            <?php echo $maior_sequencia['tamanho']; ?> dias
                        </div>
                        <div class="maior-sequencia-trofeu">🏆</div>
                    </div>
                <?php endif; ?>
            </div>
        </div>


    </div>

    <div class="dashboard-grid-tresColunas">
        <div class="cards-stack">

            <!-- Gráfico de Estudos -->
            <div class="dashboard-card-inicio">
                <div class="card-planejamento">
                    <?php include '0grafico_estudo_dia.php'; ?>
                </div>
            </div>
            <!-- Avisos -->
                <div class="card-planejamento">
                    <?php include 'avisos.php'; ?>
                </div>
        </div>



        <div class="cards-stack">
        <div class="card-planejamento">
                    <div class="card-header">
                        <h3 class="handwritten" onclick="abrirPopUp()" style="cursor: pointer;">Planejamento</h3>
                    </div>
                    <div class="card-content_planejamento">
                        <!-- Nome do Plano -->
                        <div class="info-item">
                            <span class="info-label">Nome do Plano</span>
                            <span class="info-value"><?php echo $nome_planejamento; ?></span>
                        </div>

                        <!-- Datas -->
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="info-label">Início</span>
                                <span class="info-value texto-azul"><?php echo $data_inicio_planejamento_formatada; ?></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Término</span>
                                <span class="info-value texto-vermelho"><?php echo $data_fim_planejamento_formatada; ?></span>
                            </div>
                        </div>

                        <?php $diferenca_inicio_fim = $data_inicio->diff($data_fim);
                        if ($hoje < $data_inicio): ?>
                            <div class="status-badge pendente">
                                <i class="fas fa-clock"></i>
                                <span>Ainda não iniciado</span>
                            </div>
                        <?php elseif ($hoje <= $data_fim):
                            $diferenca_hoje_fim = $hoje->diff($data_fim);
                            $dias_planejamento = $diferenca_inicio_fim->days;
                            $dias_restantes = $diferenca_hoje_fim->days;
                            $porcentagem_passada = (($dias_planejamento - $dias_restantes) / $dias_planejamento) * 100;
                            $porcentagem_passada_formatada = number_format($porcentagem_passada, 1);
                            ?>
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="info-label">Total</span>
                                    <span class="info-value"><?php echo $dias_planejamento; ?> Dias</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">Faltam</span>
                                    <span class="info-value"><?php echo $dias_restantes; ?> Dias</span>
                                </div>
                            </div>
                            <div class="progresso">
                                <div class="progresso-header">
                                    <span>Progresso do Planejamento</span>
                                    <span class="porcentagem"><?php echo $porcentagem_passada_formatada; ?>%</span>
                                </div>
                                <div class="barra-progresso">
                                    <div class="progresso-preenchimento" style="width: <?php echo $porcentagem_passada_formatada; ?>%"></div>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="status-badge concluido">
                                <i class="fas fa-check-circle"></i>
                                <span>Período finalizado</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Total de Dias</span>
                                <span class="info-value"><?php echo $diferenca_inicio_fim->days; ?></span>
                            </div>
                        <?php endif; ?>
                </div>
            </div>




            <div class="card-planejamento">
                <div class="card-header">
                    <h3 class="handwritten" onclick="abrirPopUp5()" style="cursor: pointer;">Agenda Pessoal</h3>
                </div>
                <div class="card-content">

                <?php
                if (empty($eventosPendentes) && empty($eventos_do_Dia) && empty($evento_proximo)) {
                    echo '<div class="sem-eventos" style="text-align: center; padding: 20px; background: #f9f9f9; border-radius: 10px; box-shadow: 0 2px 5px rgba(0,0,0,0.1);">';
                    echo '<i class="fas fa-calendar-times" style="font-size: 48px; color: #ccc; margin-bottom: 10px;"></i>';
                    echo '<p style="color: #666; font-size: 18px;">Não há eventos agendados</p>';
                    echo '</div>';
                } else {
                    if ($eventosPendentes != null) {
                        echo '<div class="quadrado_pendente" style="margin: 10px 0; padding: 10px; background: #fff5f5; border-left: 4px solid red; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">';
                        echo "<strong style='font-size: 14px;'>📍 Pendente: ";
                        if (is_array($eventosPendentes)) {
                            $eventos = array_map(function($evento) {
                                return sprintf(
                                    "<span class='evento-link-dashboard' data-id='%s' style='color: red; cursor: pointer; text-decoration: underline;'>%s</span>",
                                    htmlspecialchars($evento['id']),
                                    htmlspecialchars($evento['titulo'])
                                );
                            }, $eventosPendentes);
                            echo implode("<strong style='color: #666;'> • </strong>", $eventos);
                        } else {
                            echo "<span class='evento-link-dashboard' style='color: red; cursor: pointer; text-decoration: underline;'>" . htmlspecialchars($eventosPendentes) . "</span>";
                        }
                        echo "</strong>";
                        echo '</div>';
                    }

                    if ($eventos_do_Dia !== null) {
                        echo '<div class="quadrado_hoje" style="margin: 10px 0; padding: 10px; background: #f0fff4; border-left: 4px solid green; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">';
                        echo "<strong style='font-size: 14px;'>⌚ Hoje: ";
                        if (is_array($eventos_do_Dia)) {
                            $eventos = array_map(function($evento) {
                                return sprintf(
                                    "<span class='evento-link-dashboard' data-id='%s' style='color: green; cursor: pointer; text-decoration: underline;'>%s</span>",
                                    htmlspecialchars($evento['id']),
                                    htmlspecialchars($evento['titulo'])
                                );
                            }, $eventos_do_Dia);
                            echo implode("<strong style='color: #666;'> • </strong>", $eventos);
                        } else {
                            echo "<span class=evento-link-dashboard' style='color: green; cursor: pointer; text-decoration: underline;'>" . htmlspecialchars($eventos_do_Dia) . "</span>";
                        }
                        echo "</strong>";
                        echo '</div>';
                    }

                    if ($evento_proximo !== null) {
                        echo '<div class="quadrado_proximo_1" style="margin: 10px 0; padding: 10px; background: #fff9f0; border-left: 4px solid #df7201; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">';
                        echo "<strong style='font-size: 14px;'>🗓️ Data Mais Próxima: <span style='color: #df7201;'>" . date('d-m-Y', $evento_proximo) . "</span></strong><br>";
                        echo "<strong style='font-size: 14px;'>📌 Eventos: ";
                        if (is_array($eventosProximos)) {
                            $eventos = array_map(function($evento) {
                                return sprintf(
                                    "<span class='evento-link-dashboard' data-id='%s' style='color: #df7201; cursor: pointer; text-decoration: underline;'>%s</span>",
                                    htmlspecialchars($evento['id']),
                                    htmlspecialchars($evento['titulo'])
                                );
                            }, $eventosProximos);
                            echo implode("<span style='color: #666;'> • </span>", $eventos);
                        } else {
                            echo "<span class='evento-link-dashboard' style='color: #df7201; cursor: pointer; text-decoration: underline;'>" . htmlspecialchars($eventosProximos) . "</span>";
                        }
                        echo "</strong><br>";
                        $palavraDias = ($diasAteProximoEvento <= 1) ? "Dia" : "Dias";
                        echo "<strong style='font-size: 14px;'>⏳ Falta: <span style='color: #df7201;'>$diasAteProximoEvento $palavraDias</span></strong>";
                        echo '</div>';
                    }
                }
                ?>
                </div>
                
                </div>

            </div>
        </div>

    <script>
        // Remova o abrirPopUp5() existente e substitua por:
        function abrirPopUp5() {
            const agendaTab = document.querySelector('.tab[data-tab="agenda"]');
            if (agendaTab) {
                agendaTab.click();
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            // Inicializa o AgendaModalManager apenas se não existir

            // Resto do código permanece igual...
            const components = [
                '0grafico_estudo_dia.php',
                '0listar_ultimo_estudo_caixa.php',
                '0listar_proximo_estudo.php'
            ];

            components.forEach(component => {
                const elem = document.querySelector(`[data-component="${component}"]`);
                if (!elem || !elem.innerHTML.trim()) {
                    console.warn(`Componente ${component} pode não ter carregado corretamente`);
                }
            });
        });

        // Detecta erros de carregamento
        window.onerror = function(msg, url, lineNo, columnNo, error) {
            console.error('Erro:', msg, 'URL:', url, 'Linha:', lineNo);
            return false;
        };
    </script>
    <script src="assets/js/calendar_modal.js"></script>

