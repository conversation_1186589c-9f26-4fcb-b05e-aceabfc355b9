<?php
include_once("conexao_POST.php");

// Dados do formulário de login
$username = $_POST['username'];
$senha = $_POST['password'];


// Função para autenticar o usuário e a senha
function autenticarUsuario($conexao, $username, $senha) {
  // Query para verificar se o usuário e a senha são válidos
  $query = "SELECT * FROM appEstudo.usuario WHERE usuario = $1 AND senha = $2";
  $result = pg_query_params($conexao, $query, array($username, $senha));

// Verifica se a consulta retornou algum resultado
  if (pg_num_rows($result) > 0) {
    $row = pg_fetch_assoc($result);
    return $row['idusuario']; // Retorna o idusuario autenticado
  } else {
    return false; // Autenticação inválida
  }
}

// Verifica se o usuário e a senha estão corretos
$idusuario = autenticarUsuario($conexao, $username, $senha);
if ($idusuario !== false) {
  // Login bem-sucedido, define a sessão de validação
  session_start();
    $_SESSION['validacao'] = true;
    $_SESSION['username'] = $username;
    $_SESSION['senha'] = $senha;
    $_SESSION['idusuario'] = $idusuario;
  
  // Redireciona para a página principal
  header("Location: processa_POST.php");
  exit();
} else {
  // Login inválido, exibe uma mensagem de erro
  //echo "Usuário ou senha inválidos.";
}

// Fecha a conexão com o banco de dados
pg_close($conexao);
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css" integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">
</head>
<body>
<div class="alert alert-danger" role="alert">

 <p style="text-align: center;">
        Você Não tem acesso ao Sistema!<br>
        Entre em Contato: <a href="mailto:<EMAIL>"><EMAIL></a>
    </p>
</div>
<input type="button" value="Voltar" class="btn btn-dark btn-primary btn-block" onclick="window.location.href='login.php';"> 
</body>
</html>


