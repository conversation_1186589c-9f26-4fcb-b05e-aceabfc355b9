<?php
//grafico_tempo_por_turno.php
// Consultar os IDs e nomes das matérias associadas ao planejamento
$query_consultar_materias_planejamento = "
    SELECT pm.materia_idmateria, m.nome AS nome_materia, m.cor AS cor_materia
    FROM appEstudo.planejamento_materia pm
    INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    WHERE pm.planejamento_idplanejamento = $id_planejamento";

$resultado_materias_planejamento = pg_query($conexao, $query_consultar_materias_planejamento);

// Inicializa arrays para armazenar os dados das matérias
$materias_no_planejamento = array();
$cores_materias = array();

// Preenche os arrays com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_materias_planejamento)) {
    $materia_id = $row['materia_idmateria'];
    $nome_materia = $row['nome_materia'];
    $cor_materia = $row['cor_materia'];

    $materias_no_planejamento[$materia_id] = $nome_materia;
    $cores_materias[$nome_materia] = $cor_materia;
}

// Realiza a consulta ao banco de dados para obter os dados de estudo por ponto estudado de cada matéria no planejamento
$query_consulta_pontos_estudo = "
    SELECT e.hora_inicio,
           e.hora_fim,
           m.nome AS nome_materia
    FROM appEstudo.estudos e
    INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
    INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
    LEFT JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria
    WHERE u.idusuario = $id_usuario AND e.materia_idmateria IN (" . implode(',', array_keys($materias_no_planejamento)) . ")
    ORDER BY m.nome, e.hora_inicio";

$resultado_consulta_pontos_estudo = pg_query($conexao, $query_consulta_pontos_estudo);

// Função para calcular o tempo em cada turno
function calcular_tempo_por_turno($hora_inicio, $hora_fim) {
    $turnos = [
        "Madrugada" => [0, 6],
        "Manhã" => [6, 12],
        "Tarde" => [12, 18],
        "Noite" => [18, 24]
    ];
    $tempo_por_turno = array_fill_keys(array_keys($turnos), 0);
    $hora_inicio = strtotime($hora_inicio);
    $hora_fim = strtotime($hora_fim);

    foreach ($turnos as $turno => $horas) {
        $inicio_turno = strtotime(date('Y-m-d', $hora_inicio) . " +" . $horas[0] . " hours");
        $fim_turno = strtotime(date('Y-m-d', $hora_inicio) . " +" . $horas[1] . " hours");
        if ($hora_fim < $inicio_turno) continue;
        if ($hora_inicio > $fim_turno) continue;
        $inicio = max($hora_inicio, $inicio_turno);
        $fim = min($hora_fim, $fim_turno);
        $tempo_por_turno[$turno] += $fim - $inicio;
    }
    return $tempo_por_turno;
}

// Inicializa um array para armazenar os tempos por turno
$tempos_por_turno = array_fill_keys(["Madrugada", "Manhã", "Tarde", "Noite"], 0);

// Preenche o array com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_consulta_pontos_estudo)) {
    $hora_inicio = $row['hora_inicio'];
    $hora_fim = $row['hora_fim'];
    $tempo_por_turno = calcular_tempo_por_turno($hora_inicio, $hora_fim);
    foreach ($tempo_por_turno as $turno => $tempo) {
        $tempos_por_turno[$turno] += $tempo;
    }
}

// Função para converter segundos em HH:MM:SS
function formatar_tempo($segundos) {
    $horas = floor($segundos / 3600);
    $minutos = floor(($segundos % 3600) / 60);
    $segundos = $segundos % 60;
    return sprintf('%02d:%02d:%02d', $horas, $minutos, $segundos);
}

// Converte os tempos de segundos para horas
foreach ($tempos_por_turno as $turno => $tempo) {
    $tempos_por_turno[$turno] = $tempo / 3600;
}

// Adicione esta nova função para formatar o tempo em formato amigável
function formatarTempoAmigavel($horas) {
    $horas_inteiras = floor($horas);
    $minutos = round(($horas - $horas_inteiras) * 60);

    if ($horas_inteiras == 0) {
        return "{$minutos}min";
    } elseif ($minutos == 0) {
        return "{$horas_inteiras}h";
    } else {
        return "{$horas_inteiras}h:{$minutos}min";
    }
}

// Modifique a função getTurnoMaisEstudado para usar a nova formatação
function getTurnoMaisEstudado($tempos_por_turno) {
    // Verifica se há algum tempo registrado
    $tem_tempo = false;
    foreach ($tempos_por_turno as $tempo) {
        if ($tempo > 0) {
            $tem_tempo = true;
            break;
        }
    }

    if (!$tem_tempo) {
        return [
            'turno' => 'Sem Registros',
            'tempo' => '0min',
            'icon' => 'clock',
            'texto' => 'Modo Hibernação Ativado',
            'desc' => 'Hora de acordar esse cérebro e começar a brilhar!'
        ];
    }

    $turno_max = '';
    $tempo_max = 0;

    foreach ($tempos_por_turno as $turno => $tempo) {
        if ($tempo > $tempo_max) {
            $tempo_max = $tempo;
            $turno_max = $turno;
        }
    }

    // Mensagens criativas e divertidas para cada turno
    $turnos_info = [
        'Madrugada' => [
            'icon' => 'moon',
            'texto' => [
                'Vampiro do Conhecimento',
                'Batman dos Estudos',
                'Coruja PhDiurna',
                'Mestre das Sombras',
                'Guerreiro da Madrugada'
            ],
            'desc' => [
                'Enquanto outros dormem, você conquista. Edward Cullen que se cuide!',
                'Transformando café em conhecimento desde que o galo está dormindo',
                'Trocou a contagem de carneirinhos pela contagem de parágrafos',
                'Seu cérebro funciona melhor que Wi-Fi de madrugada',
                'Faz o conhecimento brilhar mais que lua cheia'
            ]
        ],
        'Manhã' => [
            'icon' => 'sun',
            'texto' => [
                'Guerreiro do Primeiro Sol',
                'Mestre do Alvorada',
                'Raio de Sol dos Estudos',
                'Herói da Aurora',
                'Gladiador Matinal'
            ],
            'desc' => [
                'Tão produtivo que faz o galo parecer preguiçoso',
                'Enquanto outros tomam café, você devora conhecimento',
                'Seu foco matinal é mais forte que café extra forte',
                'Faz o sol nascer só pra te ver estudando',
                'Transformando orvalho da manhã em sabedoria pura'
            ]
        ],
        'Tarde' => [
            'icon' => 'sun',
            'texto' => [
                'Ninja do Sol a Pino',
                'Mestre do Meridiano',
                'Samurai da Siesta',
                'Guerreiro do Sol',
                'Trovão da Tarde'
            ],
            'desc' => [
                'Nem o calor do meio-dia derrete seu foco',
                'Quando o sono pós-almoço ataca, você contra-ataca!',
                'Transforma sonolência da tarde em conhecimento puro',
                'Seu poder de concentração é mais quente que asfalto no verão',
                'Faz a preguiça da tarde parecer piada'
            ]
        ],
        'Noite' => [
            'icon' => 'star-and-crescent',
            'texto' => [
                'Guardião da Noite',
                'Mestre das Estrelas',
                'Feiticeiro Noturno',
                'Senhor da Escuridão',
                'Lenda da Lua'
            ],
            'desc' => [
                'As estrelas são sua luz de leitura particular',
                'Faz as corujas parecerem amadoras',
                'Seu foco noturno é mais brilhante que lua cheia',
                'Transforma o silêncio da noite em sabedoria',
                'Enquanto a cidade dorme, você conquista o mundo'
            ]
        ]
    ];

    if (!isset($turnos_info[$turno_max])) {
        return [
            'turno' => 'Erro',
            'tempo' => '0min',
            'icon' => 'exclamation-circle',
            'texto' => 'Bug no Matrix',
            'desc' => 'Até o Neo ficou confuso com esses dados!'
        ];
    }

    // Seleciona aleatoriamente uma das mensagens para o turno
    $textos = $turnos_info[$turno_max]['texto'];
    $descs = $turnos_info[$turno_max]['desc'];
    $indice_aleatorio = array_rand($textos);

    return [
        'turno' => $turno_max,
        'tempo' => formatarTempoAmigavel($tempo_max),
        'icon' => $turnos_info[$turno_max]['icon'],
        'texto' => $textos[$indice_aleatorio],
        'desc' => $descs[$indice_aleatorio]
    ];
}

// Também vamos atualizar o tooltip do gráfico para usar o mesmo formato
// Modifique a parte do script JavaScript que define o tooltip:

/*
tooltip: {
    callbacks: {
        label: function(context) {
            const totalHours = context.raw;
            const hours = Math.floor(totalHours);
            const minutes = Math.floor((totalHours - hours) * 60);

            if (hours === 0) {
                return `${minutes}min`;
            } else if (minutes === 0) {
                return `${hours}h`;
            } else {
                return `${hours}h ${minutes}min`;
            }
        }
    }
}
*/

// Após o processamento dos dados e antes do HTML
$tem_dados = false;
if (pg_num_rows($resultado_consulta_pontos_estudo) > 0) {
    foreach ($tempos_por_turno as $tempo) {
        if ($tempo > 0) {
            $tem_dados = true;
            break;
        }
    }
}

// Só processa o turno mais estudado se houver dados
if ($tem_dados) {
    $turno_info = getTurnoMaisEstudado($tempos_por_turno);
    $_SESSION['turno_mais_estudado'] = $turno_info;
}

// No final do arquivo, após processar os dados dos turnos
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

$turno_info = getTurnoMaisEstudado($tempos_por_turno);
$_SESSION['turno_mais_estudado'] = $turno_info;

?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <style>

        .estatistica-header {
            background: #800020;
            padding: 1.5rem;
            color: white;
        }

        .estatistica-titulo {
            font-size: 1.2rem;
            font-weight: 600;
            letter-spacing: 0.5px;
            font-family: 'Cinzel', serif;
            margin-bottom: 0.5rem;
        }

        .estatistica-subtitulo {
            font-size: 0.9rem;
            opacity: 0.8;
            font-family: 'Quicksand, sans-serif',
        }

        .grafico-content {
            padding: 1.5rem;
            height: 400px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin: 1rem;
        }
    </style>
</head>
<body>


    <?php if ($tem_dados): ?>
      
            <canvas id="turnoChart"></canvas>
     
    <?php endif; ?>


<?php if ($tem_dados): ?>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const temposPorTurno = {
            Madrugada: <?php echo $tempos_por_turno['Madrugada']; ?>,
            Manhã: <?php echo $tempos_por_turno['Manhã']; ?>,
            Tarde: <?php echo $tempos_por_turno['Tarde']; ?>,
            Noite: <?php echo $tempos_por_turno['Noite']; ?>
        };

        const categories = [
            "Madrugada (00:00 - 06:00)",
            "Manhã (06:00 - 12:00)",
            "Tarde (12:00 - 18:00)",
            "Noite (18:00 - 24:00)"
        ];

        const dataValues = [
            temposPorTurno.Madrugada,
            temposPorTurno.Manhã,
            temposPorTurno.Tarde,
            temposPorTurno.Noite
        ];

        const maxValue = Math.max(...dataValues);

        const ctx = document.getElementById('turnoChart').getContext('2d');
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: categories,
                datasets: [{
                    label: 'Tempo de Estudo (Horas)',
                    data: dataValues,
                    backgroundColor: dataValues.map(value =>
                        value === maxValue ?
                            'rgba(0, 0, 139, 0.8)' :  // Azul mais vibrante para o maior valor
                            'rgba(108, 117, 125, 0.3)'  // Cinza suave para outros valores
                    ),
                    borderWidth: 0,  // Remove as bordas (era 2)
                    borderRadius: {  // Configura bordas arredondadas apenas no topo
                        topLeft: 8,
                        topRight: 8,
                        bottomLeft: 0,
                        bottomRight: 0
                    },
                    borderSkipped: false,
                    barThickness: 40,
                    maxBarThickness: 50
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(255, 255, 255, 0.9)',
                        titleColor: '#000',
                        titleFont: {
                            size: 14,
                            weight: 'bold',
                            family: 'Quicksand, sans-serif',
                        },
                        bodyColor: '#000',
                        bodyFont: {
                            size: 13,
                            family: 'Quicksand, sans-serif',
                        },
                        padding: 12,
                        borderColor: 'rgba(0, 0, 0, 0.1)',
                        borderWidth: 1,
                        callbacks: {
                            label: function(context) {
                                const totalHours = context.raw;
                                const hours = Math.floor(totalHours);
                                const minutes = Math.floor((totalHours - hours) * 60);
                                const percentage = ((totalHours / dataValues.reduce((a, b) => a + b, 0)) * 100).toFixed(1);

                                let timeStr = '';
                                if (hours === 0) {
                                    timeStr = `${minutes}min`;
                                } else if (minutes === 0) {
                                    timeStr = `${hours}h`;
                                } else {
                                    timeStr = `${hours}h ${minutes}min`;
                                }

                                return `Tempo: ${timeStr} (${percentage}%)`;
                            }
                        }
                    },
                    datalabels: {
                        display: false  // Isso desativa os rótulos nas barras
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            font: {
                                family: 'Quicksand, sans-serif',
                                size: 13
                            },
                            color: '#495057'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)',
                            drawBorder: false
                        },
                        ticks: {
                            font: {
                                family: 'Quicksand, sans-serif',
                                size: 12
                            },
                            color: '#495057',
                            padding: 10,
                            callback: function(value) {
                                const hours = Math.floor(value);
                                const minutes = Math.floor((value - hours) * 60);
                                if (hours === 0) return `${minutes}min`;
                                if (minutes === 0) return `${hours}h`;
                                return `${hours}h${minutes}min`;
                            }
                        },
                        title: {
                            display: true,
                            text: 'Tempo de Estudo',
                            font: {
                                family: 'Quicksand, sans-serif',
                                size: 14,
                                weight: 'bold'
                            },
                            color: '#495057',
                            padding: {
                                top: 10,
                                bottom: 10
                            }
                        }
                    }
                },
                animation: {
                    duration: 1500,
                    easing: 'easeInOutQuart'
                },
                layout: {
                    padding: {
                        left: 15,
                        right: 15,
                        top: 15,
                        bottom: 15
                    }
                }
            }
        });
    });
</script>
<?php endif; ?>

</body>
</html>
