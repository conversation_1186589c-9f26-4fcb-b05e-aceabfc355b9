<?php
/**
 * Arquivo de debug para testar a configuração da API
 */

header('Content-Type: application/json; charset=utf-8');

$debug = [
    'server_info' => [
        'REQUEST_URI' => $_SERVER['REQUEST_URI'] ?? 'N/A',
        'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD'] ?? 'N/A',
        'QUERY_STRING' => $_SERVER['QUERY_STRING'] ?? 'N/A',
        'HTTP_HOST' => $_SERVER['HTTP_HOST'] ?? 'N/A',
        'SCRIPT_NAME' => $_SERVER['SCRIPT_NAME'] ?? 'N/A',
        'PATH_INFO' => $_SERVER['PATH_INFO'] ?? 'N/A',
    ],
    'get_params' => $_GET,
    'post_params' => $_POST,
    'php_version' => phpversion(),
    'mysql_available' => extension_loaded('pdo_mysql'),
    'mod_rewrite' => function_exists('apache_get_modules') ? in_array('mod_rewrite', apache_get_modules()) : 'Unknown',
    'current_time' => date('Y-m-d H:i:s'),
];

// Teste de conexão com banco
try {
    require_once 'config/database.php';
    $database = new Database();
    $db = $database->getConnection();
    $debug['database_connection'] = 'SUCCESS';
    $database->closeConnection();
} catch (Exception $e) {
    $debug['database_connection'] = 'ERROR: ' . $e->getMessage();
}

echo json_encode($debug, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
?>
