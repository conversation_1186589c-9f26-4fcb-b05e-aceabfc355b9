<?php
/**
 * Script simples para verificar dados atuais
 */

// Configurações do banco
$host = 'barbeiro_br.mysql.dbaas.com.br';
$db_name = 'barbeiro_br';
$username = 'barbeiro_br';
$password = 'Lucasb90#';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== VERIFICAÇÃO DOS DADOS ATUAIS ===\n\n";
    
    // Total de usuários
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM usuarios");
    $total = $stmt->fetch()['total'];
    echo "📊 Total de usuários: $total\n\n";
    
    // Listar usuários atuais
    $stmt = $pdo->query("SELECT id, cpf, nome, tipo FROM usuarios ORDER BY tipo, nome");
    $users = $stmt->fetchAll();
    
    echo "📋 Usuários atuais:\n";
    foreach ($users as $user) {
        echo "- {$user['nome']} ({$user['tipo']})\n";
        echo "  ID atual: {$user['id']}\n";
        echo "  CPF: {$user['cpf']}\n";
        echo "  Novo ID seria: {$user['cpf']}-" . strtoupper($user['tipo']) . "\n\n";
    }
    
    // Verificar se já há IDs no novo formato
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM usuarios WHERE id REGEXP '^[0-9]{11}-(CLIENTE|BARBEIRO)$'");
    $newFormat = $stmt->fetch()['total'];
    echo "✨ Usuários já no novo formato: $newFormat\n";
    echo "🔄 Usuários a migrar: " . ($total - $newFormat) . "\n\n";
    
    // Verificar tabelas relacionadas
    $tables = [
        'perfis_clientes' => 'usuario_id',
        'pontuacao_fidelidade' => 'cliente_id',
        'historico_atendimentos' => 'cliente_id',
        'brindes_pendentes' => 'cliente_id'
    ];
    
    echo "📊 Registros em tabelas relacionadas:\n";
    foreach ($tables as $table => $column) {
        $stmt = $pdo->query("SELECT COUNT(*) as total FROM $table");
        $count = $stmt->fetch()['total'];
        echo "- $table: $count registros\n";
    }
    
    echo "\n✅ Verificação concluída!\n";
    
} catch (PDOException $e) {
    echo "❌ Erro de conexão: " . $e->getMessage() . "\n";
}
?>
