<?php
include 'processa_index.php';
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Porcentagem do Ciclo de Estudo</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>
    <link rel="stylesheet" href="css/index1.css">
    <link rel="stylesheet" href="css/index1.css">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <style>
        /* Estilos adicionais para garantir o ajuste do tamanho do canvas */
        .chart-container {
            position: relative;
            width: 80%; /* Ajuste a largura conforme necessário */
            height: 80vh; /* Ajuste a altura conforme necessário */
            margin: auto;
        }

        canvas {
            display: block;
            width: 100% !important;
            height: 100% !important;
        }
    </style>
</head>
<body>



<h1 class="titulo">Porcentagem de Estudo Por Matéria</h1>
<div class="caixa-titulo1 chart-container">
    <canvas id="myPieChart"></canvas>
</div>




<?php
// Transformar os dados PHP em arrays JavaScript
$materias = [];
$tempos = [];
$cores = [];

foreach ($materias_no_planejamento as $materiaInfo) {
    $materia = $materiaInfo['nome'];
    $cor = $materiaInfo['cor'];
    $materias[] = $materia;
    $tempos[] = isset($tempoEstudadoPorMateria[$materia]) ? $tempoEstudadoPorMateria[$materia] / 3600 : 0; // Convertendo para horas
    $cores[] = $cor;
}

// Convertendo arrays PHP para JSON
$materias_json = json_encode($materias);
$tempos_json = json_encode($tempos);
$cores_json = json_encode($cores);
//echo "<pre>";
//print_r($tempos);
//echo "</pre>";
?>

<script>
    // Recuperando os dados do PHP
    var materias = <?php echo $materias_json; ?>;
    var tempos = <?php echo $tempos_json; ?>;
    var cores = <?php echo $cores_json; ?>;

    var ctx = document.getElementById('myPieChart').getContext('2d');
    var myPieChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: materias,
            datasets: [{
                data: tempos,
                backgroundColor: cores
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        // This more specific font property overrides the global property
                        font: {
                            size: 18,
                            family: "Times New Roman",
                            weight: "bold"
                        }
                    },
                },
                tooltip: {
                    bodyFont: {
                        family: 'Times New Roman', // Nome da fonte
                        size: 16,        // Tamanho da fonte
                        style: 'normal',
                        weight: 'bold'
                    },
                    callbacks: {
                        label: function(context) {
                            var label = '';
                            var materia = context.label || '';
                            var totalSeconds = context.raw * 3600;
                            var hours = Math.floor(totalSeconds / 3600);
                            var minutes = Math.floor((totalSeconds % 3600) / 60);
                            var seconds = totalSeconds % 60;
                            var total = context.dataset.data.reduce((acc, curr) => acc + curr, 0); // Calcula a soma total
                            var percentage = (context.raw / total) * 100;
                            percentage = percentage.toFixed(2); // Formata para 2 casas decimais
                            //label += `${materia}: ${hours}h ${minutes}m ${seconds}s (${percentage}%)`;
                            label += `${materia}: ${hours}h ${minutes}m (${percentage}%)`;
                            return label;
                        },
                        title: function() {
                            return ''; // Retorna uma string vazia para remover o título
                        }
                    }
                },

                datalabels: {
                    color: '#fff',
                    useHTML: true, // Permite o uso de HTML nos rótulos de dados
                    formatter: function(value, context) {
                        var totalSeconds = value * 3600;
                        var hours = Math.floor(totalSeconds / 3600);
                        var minutes = Math.floor((totalSeconds % 3600) / 60);
                        var seconds = totalSeconds % 60;
                        var total = context.dataset.data.reduce((acc, curr) => acc + curr, 0);
                        var percentage = (value / total) * 100;
                        percentage = percentage.toFixed(2);
                        //return `${hours}h ${minutes}m ${seconds}s\n(${percentage}%)`;
                        // Utilize CSS inline para aplicar negrito
                        return percentage + '%';
                    }
                }
            }
        },
        plugins: [ChartDataLabels]
    });
</script>
</body>
</html>
