<?php

// Inclua o arquivo de conexão com o banco de dados
include 'conexao_POST.php';

// Inclua o arquivo que fornece a próxima matéria estudada
include 'consulta_banco_ultima_proxima.php';




// Query para selecionar as matérias associadas ao planejamento com detalhes, ordenadas pelo nome da matéria
$query_consultar_materias_planejamento = "
    SELECT pm.materia_idmateria AS id, m.nome AS nome, m.cor AS cor, a.detalhe AS detalhe
    FROM appEstudo.planejamento_materia pm
    INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    LEFT JOIN appEstudo.anotacao a ON pm.planejamento_idplanejamento = a.planejamento_idplanejamento
                                     AND pm.materia_idmateria = a.materia_idmateria
                                     AND a.planejamento_usuario_idusuario = $id_usuario
    WHERE pm.planejamento_idplanejamento = $id_planejamento
    ORDER BY m.nome ASC";

$result = pg_query($conexao, $query_consultar_materias_planejamento);

if (!$result) {
    echo "Erro na consulta.\n";
    exit;
}

$materias = array();
while ($materia = pg_fetch_assoc($result)) {
    $materias[] = $materia;
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link href="https://fonts.googleapis.com/css2?family=Courier+Prime:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <style>
        body {
            font-family: 'Courier Prime', monospace;
        }
        .materia-box h4 {
            font-size: 2em; /* Ajuste o tamanho conforme necessário */
        }
        .destacada1 {
            background-color: #CCCCCC;
        }
    </style>
    <script src="https://clevert.com.br/lib/ckeditor/ckeditor.js"></script>
</head>
<body>
<div class="container">
    <?php foreach ($materias as $materia): ?>
        <?php
        $destacadaClass = ($proximaMateriaEstudada === $materia['nome']) ? 'destacada1' : '';
        $backgroundColor = ($destacadaClass !== '') ? '#CCCCCC' : '#f0f0f0';
        ?>
        <div class="materia-box <?= $destacadaClass ?>" style="background-color: <?= $backgroundColor ?>; padding: 20px; margin-bottom: 20px;">
            <h4 style="color: <?= $materia['cor'] ?>;"><?= $materia['nome'] ?></h4>
            <p><?= $materia['detalhe'] ?></p>
            <button type="button" class="btn btn-outline-primary btn-sm" onclick="mostrarFormulario('formEditar<?= $materia['id'] ?>')">Editar</button>

            <div id="formEditar<?= $materia['id'] ?>" class="form-editar" style="display: none; margin-top: 20px;">
                <form action="modal_planejamento_editar.php" method="POST">
                    <input type="hidden" name="id" value="<?= $materia['id'] ?>">
                    <input type="hidden" name="id_planejamento" value="<?= $id_planejamento ?>">
                    <input type="hidden" name="materia" value="<?= $materia['nome'] ?>">
                    <div class="form-group">
                        <label for="nome<?= $materia['id'] ?>">Nome:</label>
                        <input type="text" class="form-control" id="nome<?= $materia['id'] ?>" name="nome" value="<?= $materia['nome'] ?>" disabled>
                    </div>
                    <div class="form-group">
                        <label for="detalhe<?= $materia['id'] ?>">Detalhe:</label>
                        <textarea class="form-control" id="editor<?= $materia['id'] ?>" name="detalhe" style="width: 100%; min-height: 400px;"><?= $materia['detalhe'] ?></textarea>
                        <script>
                            CKEDITOR.replace('editor<?= $materia['id'] ?>');
                        </script>
                    </div>
                    <button type="button" class="btn btn-secondary" onclick="esconderFormulario('formEditar<?= $materia['id'] ?>')">Cancelar</button>

                </form>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<script>
    function mostrarFormulario(id) {
        document.getElementById(id).style.display = 'block';
    }

    function esconderFormulario(id) {
        document.getElementById(id).style.display = 'none';
    }
</script>
</body>
</html>
