<?php
session_start();
require_once("assets/config.php");
require_once('includes/verify_admin.php');

// Verifica se é admin
verificarAcessoAdmin($conexao, false);

// Headers de segurança
header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.tailwindcss.com https://cdnjs.cloudflare.com https://code.jquery.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; img-src 'self' data:;");
header("X-Frame-Options: DENY");
header("X-Content-Type-Options: nosniff");
header("X-XSS-Protection: 1; mode=block");
header("Referrer-Policy: strict-origin-when-cross-origin");

if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

$query = "SELECT idcurso, nome, logo_url FROM appEstudo.curso ORDER BY nome";
$resultado = pg_query($conexao, $query);
if (!$resultado) {
    die("Erro ao buscar cursos: " . pg_last_error($conexao));
}
$cursos = pg_fetch_all($resultado);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciamento de Cursos</title>
    <link href="https://fonts.googleapis.com/css2?family=Varela+Round&family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert2/11.7.32/sweetalert2.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert2/11.7.32/sweetalert2.all.min.js"></script>
    <style>
        :root {
            --primary: #00008B;
            --primary-light: #3949ab;
            --secondary: #f8f0e3;
            --accent: #4a4a4a;
            --border: #e0e0e0;
            --text: #2c3e50;
            --active: #00008B;
            --hover: #f5f5f5;
            --primary-blue: #00008B;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --background: #f5f5f5;
            --card-background: white;
        }

        [data-theme="dark"] {
            --primary: #4169E1;
            --secondary: #1a1a2e;
            --accent: #6c7293;
            --border: #2d2d42;
            --text: #e4e6f0;
            --active: #4169E1;
            --hover: #232338;
            --primary-blue: #4169E1;
            --shadow-color: rgba(0, 0, 0, 0.3);
            --background: #0a0a1f;
            --card-background: #13132b;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background: var(--background);
            color: var(--text);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px;
            background: var(--primary);
            box-shadow: 0 2px 4px var(--shadow-color);
            margin-bottom: 30px;
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .theme-toggle {
            cursor: pointer;
        }

        .theme-btn {
            background: none;
            border: none;
            color: white;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: background-color 0.3s;
        }

        .theme-btn:hover {
            background-color: var(--shadow-color);
        }

        h1 {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            text-align: center;
            color: var(--primary);
            font-size: 2rem;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .status-card {
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 6px var(--shadow-color);
            margin: 0 20px 25px 20px;
            transition: transform 0.3s ease;
            border: 1px solid var(--border);
            background: var(--card-background);
        }

        .status-card:hover {
            transform: translateY(-5px);
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: var(--primary);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-family: 'Quicksand', sans-serif;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
            opacity: 0.9;
        }

        .btn-secondary {
            background: var(--accent);
        }

        .curso-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            padding: 20px;
        }

        .curso-card {
            background: var(--card-background);
            border-radius: 15px;
            box-shadow: 0 4px 6px var(--shadow-color);
            transition: transform 0.3s ease;
            border: 1px solid var(--border);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .curso-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px var(--shadow-color);
        }

        .curso-header {
            padding: 20px;
            border-bottom: 1px solid var(--border);
            text-align: center;
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
        }

        .curso-logo {
            width: 120px;
            height: 120px;
            margin: 0 auto 15px;
            border-radius: 12px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--card-background);
            border: 2px solid var(--border);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        .curso-logo-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .curso-logo-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--background);
            color: var(--primary);
        }

        .curso-logo-placeholder i {
            font-size: 3rem;
        }

        .curso-titulo {
            /*color: white;*/
            font-size: 1.5rem;
            margin: 0;
            font-weight: 600;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
        }

        .curso-body {
            padding: 20px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
        }

        .curso-actions {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: auto;
        }

        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
            opacity: 0.9;
        }

        .btn i {
            font-size: 1rem;
        }

        .edit-input {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--border);
            border-radius: 4px;
            background: var(--card-background);
            color: var(--text);
        }

        @media (max-width: 1200px) {
            .curso-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .curso-grid {
                grid-template-columns: 1fr;
            }
            
            .container {
                padding: 10px;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
            }
        }

        .menu-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin-bottom: 15px;
}

.menu-icon i {
    color: var(--primary);
}
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <div class="logo">
                <img src="../logo/logo_vertical.png" alt="Logo" style="height: 50px;">
            </div>
        </div>
        <div class="header-right">
            <div class="theme-toggle">
                <button id="theme-toggle-btn" class="theme-btn">
                    <i id="theme-icon" class="fas fa-moon"></i>
                </button>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- Botão Voltar -->
        <div style="margin: 20px 0;">
            <a href="index.php" class="btn" style="background-color: var(--primary); color: white;">
                <i class="fas fa-arrow-left"></i> Voltar
            </a>
        </div>

        <div class="header_centro">
            <div class="menu-icon">
                <i class="fas fa-graduation-cap fa-3x" style="color: var(--primary); margin-bottom: 15px;"></i>
            </div>
            <h1>Gerenciamento de Cursos</h1>
        </div>

        <div class="text-center" style="margin-bottom: 30px;">
            <button onclick="abrirModalCadastro()" class="btn-novo">
                <i class="fas fa-plus-circle"></i>
                <span>Adicionar Novo Curso</span>
            </button>
        </div>

        <div class="curso-grid">
            <?php if ($cursos): ?>
                <?php foreach ($cursos as $curso): ?>
                    <div id="curso-<?php echo $curso['idcurso']; ?>" class="curso-card">
                        <div class="curso-header">
                            <div class="curso-logo">
                                <?php if (!empty($curso['logo_url'])): ?>
                                    <img src="<?php echo htmlspecialchars($curso['logo_url']); ?>" 
                                         alt="Logo <?php echo htmlspecialchars($curso['nome']); ?>"
                                         class="curso-logo-img">
                                <?php else: ?>
                                    <div class="curso-logo-placeholder">
                                        <i class="fas fa-graduation-cap"></i>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <h2 class="curso-titulo">
                                <?php echo htmlspecialchars($curso['nome']); ?>
                            </h2>
                        </div>
                        <div class="curso-body">
                            <div class="curso-actions">
                                <button onclick="abrirModalEdicao(
                                    <?php echo $curso['idcurso']; ?>, 
                                    '<?php echo addslashes($curso['nome']); ?>', 
                                    '<?php echo addslashes($curso['logo_url']); ?>'
                                )" class="btn btn-primary">
                                    <i class="fas fa-edit"></i>
                                    Editar
                                </button>
                                <button onclick="confirmarExclusao(<?php echo $curso['idcurso']; ?>)" class="btn btn-danger">
                                    <i class="fas fa-trash"></i>
                                    Excluir
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php else: ?>
                <div class="status-card" style="text-align: center; grid-column: 1/-1;">
                    <p>Nenhum curso cadastrado.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Modal de Edição -->
    <div id="editarCursoModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-edit"></i> Editar Curso</h2>
                <button onclick="fecharModalEdicao()" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="editarCursoForm" class="modal-body" enctype="multipart/form-data">
                <input type="hidden" id="idCursoEdicao" name="idcurso">
                <input type="hidden" name="csrf_token" value="<?php echo $_SESSION['csrf_token']; ?>">
                <div class="form-grid">
                    <!-- Logo Upload -->
                    <div class="form-group logo-section">
                        <label class="form-label">Logo do Curso</label>
                        <div class="logo-upload-container">
                            <div class="logo-preview" id="logoPreviewContainerEdicao">
                                <img id="logoPreviewEdicao" class="hidden" alt="Preview">
                                <div id="uploadPlaceholderEdicao" class="upload-placeholder">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <span>Clique para fazer upload</span>
                                </div>
                            </div>
                            <input type="file" id="logoEdicao" name="logo" accept="image/*" class="hidden">
                        </div>
                    </div>

                    <!-- Nome do Curso -->
                    <div class="form-group">
                        <label for="nomeEdicao" class="form-label">Nome do Curso</label>
                        <div class="input-group">
                            <i class="fas fa-graduation-cap input-icon"></i>
                            <input type="text" 
                                   id="nomeEdicao" 
                                   name="nome" 
                                   required 
                                   class="form-input"
                                   placeholder="Ex: Direito Constitucional">
                        </div>
                    </div>
                </div>

                <!-- Botões -->
                <div class="modal-footer">
                    <button type="button" onclick="fecharModalEdicao()" class="btn-secondary">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save"></i> Salvar Alterações
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal de Cadastro -->
    <div id="cadastroCursoModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-graduation-cap"></i> Novo Curso</h2>
                <button onclick="fecharModalCadastro()" class="close-btn">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="cadastroCursoForm" class="modal-body" onsubmit="cadastrarCurso(event)">
                <div class="form-grid">
                    <!-- Logo Upload -->
                    <div class="form-group logo-section">
                        <label class="form-label">Logo do Curso</label>
                        <div class="logo-upload-container">
                            <div class="logo-preview" id="logoPreviewContainer">
                                <img id="logoPreviewCadastro" class="hidden" alt="Preview">
                                <div id="uploadPlaceholderCadastro" class="upload-placeholder">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <span>Clique para fazer upload</span>
                                </div>
                            </div>
                            <input type="file" id="logoCadastro" name="logo" accept="image/*" class="hidden">
                        </div>
                    </div>

                    <!-- Nome do Curso -->
                    <div class="form-group">
                        <label for="nomeCadastro" class="form-label">Nome do Curso</label>
                        <div class="input-group">
                            <i class="fas fa-book input-icon"></i>
                            <input type="text" 
                                   id="nomeCadastro" 
                                   name="nome" 
                                   required 
                                   class="form-input"
                                   placeholder="Ex: Curso X">
                        </div>
                    </div>
                </div>

                <!-- Botões -->
                <div class="modal-footer">
                    <button type="button" onclick="fecharModalCadastro()" class="btn-secondary">
                        <i class="fas fa-times"></i> Cancelar
                    </button>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save"></i> Salvar Curso
                    </button>
                </div>
            </form>
        </div>
    </div>

    <style>
    /* Botão Novo Curso */
    .btn-novo {
        background: var(--primary);
        color: white;
        border: none;
        padding: 12px 24px;
        border-radius: 50px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 10px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 139, 0.2);
        margin: 0 auto;
    }

    .btn-novo:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(0, 0, 139, 0.3);
    }

    .btn-novo i {
        font-size: 1.2rem;
    }

    /* Modal */
    .modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(5px);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
    }

    .modal-content {
        background: var(--card-background);
        border-radius: 20px;
        width: 90%;
        max-width: 600px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        animation: modalSlideIn 0.3s ease-out;
    }

    @keyframes modalSlideIn {
        from {
            transform: translateY(-20px);
            opacity: 0;
        }
        to {
            transform: translateY(0);
            opacity: 1;
        }
    }

    .modal-header {
        padding: 25px 30px;
        border-bottom: 1px solid var(--border);
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h2 {
        color: var(--primary);
        margin: 0;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .modal-body {
        padding: 30px;
    }

    .form-grid {
        display: grid;
        gap: 25px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        display: block;
        margin-bottom: 10px;
        color: var(--text);
        font-weight: 600;
        font-size: 0.95rem;
    }

    .input-group {
        position: relative;
        display: flex;
        align-items: center;
    }

    .input-icon {
        position: absolute;
        left: 12px;
        color: var(--primary);
    }

    .form-input {
        width: 100%;
        padding: 12px 12px 12px 40px;
        border: 2px solid var(--border);
        border-radius: 10px;
        background: var(--card-background);
        color: var(--text);
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .form-input:focus {
        border-color: var(--primary);
        outline: none;
        box-shadow: 0 0 0 3px rgba(0, 0, 139, 0.1);
    }

    .logo-upload-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .logo-preview {
        width: 150px;
        height: 150px;
        border: 2px dashed var(--border);
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .logo-preview:hover {
        border-color: var(--primary);
        background: rgba(0, 0, 139, 0.05);
    }

    .logo-preview img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .upload-placeholder {
        text-align: center;
        color: var(--accent);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .upload-placeholder i {
        font-size: 2rem;
        color: var(--primary);
    }

    .modal-footer {
        padding: 20px 30px;
        border-top: 1px solid var(--border);
        display: flex;
        justify-content: flex-end;
        gap: 15px;
    }

    .btn-primary, .btn-secondary {
        padding: 12px 24px;
        border-radius: 10px;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        border: none;
    }

    .btn-primary {
        background: var(--primary);
        color: white;
    }

    .btn-secondary {
        background: var(--border);
        color: var(--text);
    }

    .btn-primary:hover, .btn-secondary:hover {
        transform: translateY(-2px);
    }

    .close-btn {
        background: none;
        border: none;
        color: var(--text);
        cursor: pointer;
        font-size: 1.2rem;
        padding: 8px;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .close-btn:hover {
        background: var(--hover);
    }

    .hidden {
        display: none !important;
    }

    /* Responsividade */
    @media (max-width: 768px) {
        .modal-content {
            width: 95%;
            margin: 20px;
        }

        .modal-header, .modal-body, .modal-footer {
            padding: 15px;
        }

        .btn-primary, .btn-secondary {
            padding: 10px 20px;
        }
    }
    </style>

    <style>
    .modal-resposta {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(2px);
    }

    .modal-content {
        position: relative;
        background: var(--card-background);
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        max-width: 500px;
        width: 90%;
        z-index: 1;
        animation: modalFadeIn 0.3s ease;
    }

    @keyframes modalFadeIn {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding-bottom: 15px;
        border-bottom: 1px solid var(--border);
    }

    .modal-header h3 {
        margin: 0;
        color: var(--text);
        font-size: 1.5em;
    }

    .modal-close {
        background: none;
        border: none;
        font-size: 1.5em;
        cursor: pointer;
        color: var(--text);
        padding: 5px;
        border-radius: 50%;
        transition: background-color 0.3s;
    }

    .modal-close:hover {
        background-color: var(--hover);
    }

    .modal-success-content,
    .modal-error-content {
        display: flex;
        align-items: center;
        gap: 20px;
        padding: 10px;
    }

    .success-icon {
        color: #28a745;
    }

    .error-icon {
        color: #dc3545;
    }

    .success-message,
    .error-message {
        flex: 1;
    }

    .success-message h4,
    .error-message h4 {
        margin: 0 0 10px 0;
        color: var(--text);
    }

    .success-message p,
    .error-message p {
        margin: 0;
        color: var(--accent);
    }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Configuração do tema
            const themeToggleBtn = document.getElementById('theme-toggle-btn');
            if (themeToggleBtn) {
                themeToggleBtn.addEventListener('click', function() {
                    const currentTheme = document.documentElement.getAttribute('data-theme');
                    const newTheme = currentTheme === 'dark' ? '' : 'dark';
                    document.documentElement.setAttribute('data-theme', newTheme);
                    localStorage.setItem('theme', newTheme);
                    updateThemeIcon(newTheme);
                });
            }

            // Configuração do upload de logo no cadastro
            const logoInputCadastro = document.getElementById('logoCadastro');
            const logoPreviewCadastro = document.getElementById('logoPreviewCadastro');
            const uploadPlaceholderCadastro = document.getElementById('uploadPlaceholderCadastro');
            const logoPreviewContainerCadastro = document.getElementById('logoPreviewContainer');

            if (logoPreviewContainerCadastro) {
                logoPreviewContainerCadastro.addEventListener('click', () => logoInputCadastro.click());
            }

            if (logoInputCadastro) {
                logoInputCadastro.addEventListener('change', function(e) {
                    if (e.target.files && e.target.files[0]) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            logoPreviewCadastro.src = e.target.result;
                            logoPreviewCadastro.classList.remove('hidden');
                            uploadPlaceholderCadastro.classList.add('hidden');
                        }
                        reader.readAsDataURL(e.target.files[0]);
                    }
                });
            }

            // Configuração do upload de logo na edição
            const logoInputEdicao = document.getElementById('logoEdicao');
            const logoPreviewEdicao = document.getElementById('logoPreviewEdicao');
            const uploadPlaceholderEdicao = document.getElementById('uploadPlaceholderEdicao');
            const logoPreviewContainerEdicao = document.getElementById('logoPreviewContainerEdicao');

            if (logoPreviewContainerEdicao) {
                logoPreviewContainerEdicao.addEventListener('click', () => logoInputEdicao.click());
            }

            if (logoInputEdicao) {
                logoInputEdicao.addEventListener('change', function(e) {
                    if (e.target.files && e.target.files[0]) {
                        const reader = new FileReader();
                        reader.onload = function(e) {
                            logoPreviewEdicao.src = e.target.result;
                            logoPreviewEdicao.classList.remove('hidden');
                            uploadPlaceholderEdicao.classList.add('hidden');
                        }
                        reader.readAsDataURL(e.target.files[0]);
                    }
                });
            }
        });

        // Funções do Modal de Cadastro
        function abrirModalCadastro() {
            document.getElementById('cadastroCursoModal').classList.remove('hidden');
        }

        function fecharModalCadastro() {
            document.getElementById('cadastroCursoModal').classList.add('hidden');
            document.getElementById('cadastroCursoForm').reset();
            document.getElementById('logoPreviewCadastro').classList.add('hidden');
            document.getElementById('uploadPlaceholderCadastro').classList.remove('hidden');
        }

        // Funções do Modal de Edição
        function abrirModalEdicao(idcurso, nome, logoUrl) {
            document.getElementById('idCursoEdicao').value = idcurso;
            document.getElementById('nomeEdicao').value = nome;
            
            const logoPreview = document.getElementById('logoPreviewEdicao');
            const uploadPlaceholder = document.getElementById('uploadPlaceholderEdicao');
            
            if (logoUrl) {
                logoPreview.src = logoUrl;
                logoPreview.classList.remove('hidden');
                uploadPlaceholder.classList.add('hidden');
            } else {
                logoPreview.classList.add('hidden');
                uploadPlaceholder.classList.remove('hidden');
            }
            
            document.getElementById('editarCursoModal').classList.remove('hidden');
        }

        function fecharModalEdicao() {
            document.getElementById('editarCursoModal').classList.add('hidden');
            document.getElementById('editarCursoForm').reset();
        }

        // Função para atualizar o ícone do tema
        function updateThemeIcon(theme) {
            const icon = document.getElementById('theme-icon');
            if (icon) {
                icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }

        // Aplicar tema salvo ao carregar
        const savedTheme = localStorage.getItem('theme') || '';
        document.documentElement.setAttribute('data-theme', savedTheme);
        updateThemeIcon(savedTheme);

        function atualizarCurso(formData) {
            return new Promise((resolve, reject) => {
                $.ajax({
                    url: 'atualizar_curso.php',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        resolve(response);
                    },
                    error: function(xhr) {
                        reject(new Error('Erro na requisição'));
                    }
                });
            });
        }

        document.getElementById('editarCursoForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            try {
                const formData = new FormData(this);
                const response = await atualizarCurso(formData);
                mostrarModalResposta(response);
                
                if (response.success) {
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                }
            } catch (error) {
                console.error('Erro no formulário:', error);
                mostrarModalResposta({
                    success: false,
                    message: {
                        titulo: 'Erro',
                        conteudo: `
                            <div class="modal-error-content">
                                <div class="error-icon">
                                    <i class="fas fa-exclamation-circle fa-3x"></i>
                                </div>
                                <div class="error-message">
                                    <h4>Erro ao atualizar curso</h4>
                                    <p>Por favor, tente novamente.</p>
                                </div>
                            </div>
                        `
                    }
                });
            }
        });

        function mostrarModalResposta(data) {
            const modalHTML = `
                <div class="modal-resposta">
                    <div class="modal-overlay"></div>
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3>${data.message.titulo}</h3>
                            <button class="modal-close">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="modal-body">
                            ${data.message.conteudo}
                        </div>
                    </div>
                </div>
            `;

            // Remove modal anterior se existir
            const oldModal = document.querySelector('.modal-resposta');
            if (oldModal) {
                oldModal.remove();
            }

            // Adiciona o novo modal
            document.body.insertAdjacentHTML('beforeend', modalHTML);

            // Adiciona os eventos de fechar
            const modal = document.querySelector('.modal-resposta');
            const closeBtn = modal.querySelector('.modal-close');
            const overlay = modal.querySelector('.modal-overlay');

            const closeModal = () => {
                modal.remove();
                if (data.success) {
                    window.location.reload();
                }
            };

            closeBtn.addEventListener('click', closeModal);
            overlay.addEventListener('click', closeModal);
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    closeModal();
                }
            });
        }
    </script>
    <script>
        async function cadastrarCurso(event) {
            event.preventDefault();
            
            const form = document.getElementById('cadastroCursoForm');
            const formData = new FormData(form);

            try {
                const response = await fetch('save_curso.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    mostrarModalResposta({
                        success: true,
                        message: {
                            titulo: 'Sucesso!',
                            conteudo: 'Curso cadastrado com sucesso!'
                        }
                    });
                    
                    // Fecha o modal de cadastro
                    fecharModalCadastro();
                    
                    // Recarrega a página após 2 segundos
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    throw new Error(result.error || 'Erro ao cadastrar curso');
                }
            } catch (error) {
                mostrarModalResposta({
                    success: false,
                    message: {
                        titulo: 'Erro',
                        conteudo: `Erro ao cadastrar curso: ${error.message}`
                    }
                });
            }
        }

        // Preview da imagem
        document.getElementById('logoCadastro').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = document.getElementById('logoPreviewCadastro');
                    const placeholder = document.getElementById('uploadPlaceholderCadastro');
                    
                    preview.src = e.target.result;
                    preview.classList.remove('hidden');
                    placeholder.classList.add('hidden');
                };
                reader.readAsDataURL(file);
            }
        });
    </script>
    <script>
        function confirmarExclusao(cursoId) {
            Swal.fire({
                title: 'Confirmar exclusão?',
                text: 'Esta ação não poderá ser desfeita!',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                confirmButtonText: 'Sim, excluir!',
                cancelButtonText: 'Cancelar',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    excluirCurso(cursoId);
                }
            });
        }

        function excluirCurso(cursoId) {
            fetch('excluir_curso.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `cursoId=${cursoId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        title: 'Sucesso!',
                        text: 'Curso excluído com sucesso!',
                        icon: 'success',
                        confirmButtonColor: '#28a745'
                    }).then(() => {
                        // Remove o card do curso da interface
                        const cursoElement = document.getElementById(`curso-${cursoId}`);
                        if (cursoElement) {
                            cursoElement.remove();
                        }
                        
                        // Se não houver mais cursos, mostra mensagem
                        const cursoGrid = document.querySelector('.curso-grid');
                        if (!cursoGrid.children.length) {
                            cursoGrid.innerHTML = `
                                <div class="status-card" style="text-align: center; grid-column: 1/-1;">
                                    <p>Nenhum curso cadastrado.</p>
                                </div>
                            `;
                        }
                    });
                } else {
                    Swal.fire({
                        title: 'Erro!',
                        text: data.message || 'Ocorreu um erro ao excluir o curso.',
                        icon: 'error',
                        confirmButtonColor: '#dc3545'
                    });
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                Swal.fire({
                    title: 'Erro!',
                    text: 'Ocorreu um erro ao processar a solicitação.',
                    icon: 'error',
                    confirmButtonColor: '#dc3545'
                });
            });
        }
    </script>
</body>
</html>




