<?php
// obter_estudo_data.php
header('Content-Type: application/json');
include_once("conexao_POST.php");
include_once('funcoes.php');

if (!isset($_SESSION['idusuario']) || !isset($_SESSION['idusuario'])) {
    session_start();
}

if (!isset($_GET['data']) || empty($_GET['data'])) {
    echo json_encode(['erro' => 'Data não fornecida']);
    exit;
}

$id_usuario = $_SESSION['idusuario'];
$data_estudo = $_GET['data'];

// Validar formato da data (YYYY-MM-DD)
if (!preg_match('/^\d{4}-\d{2}-\d{2}$/', $data_estudo)) {
    echo json_encode(['erro' => 'Formato de data inválido: ' . $data_estudo]);
    exit;
}

// Log para depuração
error_log("Processando dados para a data: $data_estudo");

// Consultar os dados do planejamento pelo usuario
$query_consultar_planejamento = "SELECT idplanejamento FROM appEstudo.planejamento 
                                 WHERE usuario_idusuario = $id_usuario";
$resultado_planejamento = pg_query($conexao, $query_consultar_planejamento);

if (!$resultado_planejamento || pg_num_rows($resultado_planejamento) == 0) {
    echo json_encode(['erro' => 'Planejamento não encontrado']);
    exit;
}

$planejamento = pg_fetch_assoc($resultado_planejamento);
$id_planejamento = $planejamento['idplanejamento'];

// Consultar as matérias do planejamento
$query_consultar_materias_planejamento = "
    SELECT pm.materia_idmateria, m.nome AS nome_materia, m.cor AS cor_materia
    FROM appEstudo.planejamento_materia pm
    INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    WHERE pm.planejamento_idplanejamento = $id_planejamento";

$resultado_materias_planejamento = pg_query($conexao, $query_consultar_materias_planejamento);

$materias_no_planejamento = array();
$cores_materias = array();

while ($row = pg_fetch_assoc($resultado_materias_planejamento)) {
    $materia_id = $row['materia_idmateria'];
    $nome_materia = $row['nome_materia'];
    $cor_materia = $row['cor_materia'];

    $materias_no_planejamento[$materia_id] = $nome_materia;
    $cores_materias[$nome_materia] = $cor_materia;
}

// Consultar os dados de estudo para a data específica - Adicionado campo descricao
$query_consulta_pontos_estudo = "
    SELECT 
        m.nome AS nome_materia, 
        e.ponto_estudado AS ponto_estudado,
        e.tempo_liquido AS tempo_estudo,
        e.tempo_bruto AS tempo_bruto,
        e.tempo_perdido AS tempo_perdido,
        e.hora_inicio AS hora_inicio,
        e.hora_fim AS hora_fim,
        e.metodo AS metodo,
        e.descricao AS descricao,
        c.nome AS nome_curso
    FROM appEstudo.estudos e
    INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
    INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
    LEFT JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria
    LEFT JOIN appEstudo.curso c ON e.idcurso = c.idcurso
    WHERE u.idusuario = $id_usuario 
    AND e.data = '$data_estudo'";

$resultado_consulta_pontos_estudo = pg_query($conexao, $query_consulta_pontos_estudo);

$estudos = array();
$tempoTotalSegundos = 0;

while ($row = pg_fetch_assoc($resultado_consulta_pontos_estudo)) {
    $materia = $row['nome_materia'];
    $ponto_estudado = $row['ponto_estudado'];
    $tempo_estudo = $row['tempo_estudo'];
    $cor = isset($cores_materias[$materia]) ? $cores_materias[$materia] : '#CCCCCC';
    
    $tempo_segundos = converterParaSegundos($tempo_estudo);
    $tempoTotalSegundos += $tempo_segundos;

    $estudos[] = array(
        'nome_materia' => $materia,
        'ponto_estudado' => $ponto_estudado,
        'tempo_estudo' => $tempo_estudo,
        'tempo_bruto' => $row['tempo_bruto'],
        'tempo_perdido' => $row['tempo_perdido'],
        'hora_inicio' => $row['hora_inicio'],
        'hora_fim' => $row['hora_fim'],
        'metodo' => $row['metodo'],
        'nome_curso' => $row['nome_curso'],
        'descricao' => $row['descricao'], // Adicionado campo descricao
        'cor' => $cor,
        'tempo_segundos' => $tempo_segundos
    );
}

// Consultar o tempo planejado
$query_tempo_planejado = "SELECT tempo_planejamento FROM appEstudo.planejamento WHERE usuario_idusuario = $id_usuario";
$result_tempo = pg_query($conexao, $query_tempo_planejado);
$tempo_planejamento = "00:00";
$tempoPlanejamento = 0;

if ($result_tempo && pg_num_rows($result_tempo) > 0) {
    $row = pg_fetch_assoc($result_tempo);
    $tempo_planejamento = $row['tempo_planejamento'];
    $tempoPlanejamento = converterParaSegundos($tempo_planejamento);
}

// Formatação do tempo total
$horasTotal = floor($tempoTotalSegundos / 3600);
$minutosTotal = floor(($tempoTotalSegundos % 3600) / 60);
$tempoTotalFormatado = sprintf("%02d:%02d", $horasTotal, $minutosTotal);

$resposta = array(
    'data' => $data_estudo,
    'estudos' => $estudos,
    'tempo_total_segundos' => $tempoTotalSegundos,
    'tempo_total_formatado' => $tempoTotalFormatado,
    'tempo_planejado' => $tempo_planejamento,
    'tempo_planejado_segundos' => $tempoPlanejamento
);

echo json_encode($resposta);
?>