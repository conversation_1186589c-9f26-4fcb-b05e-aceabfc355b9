<?php
include_once("../session_config.php");
include_once("../assets/config.php");

if (!verificarSessaoValida()) {
    header("Location: ../login_index.php");
    exit();
}

$termo = isset($_GET['termo']) ? trim($_GET['termo']) : '';

$resultados = [];
if ($termo !== '') {
    $query = "
        SELECT t.id, t.titulo, t.conteudo, t.created_at
        FROM appestudo.forum_topicos t
        WHERE t.status = true
          AND (t.titulo ILIKE $1 OR t.conteudo ILIKE $1)
        ORDER BY t.created_at DESC
        LIMIT 30
    ";
    $result = pg_query_params($conexao, $query, ['%' . $termo . '%']);
    while ($row = pg_fetch_assoc($result)) {
        $resultados[] = $row;
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <title>Buscar: <?php echo htmlspecialchars($termo); ?></title>
    <link rel="stylesheet" href="css/forum.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        .search-results-container { max-width: 800px; margin: 40px auto; background: #fff; border-radius: 10px; box-shadow: 0 2px 12px #0001; padding: 32px; }
        .search-title { font-size: 1.4em; font-weight: 700; margin-bottom: 18px; color: var(--text-color, #222); }
        .search-result-item { padding: 18px 0; border-bottom: 1px solid #eee; }
        .search-result-item:last-child { border-bottom: none; }
        .search-result-title { font-size: 1.1em; font-weight: 600; color: var(--accent-color, #2a7ae4); text-decoration: none; }
        .search-result-title:hover { text-decoration: underline; }
        .search-result-date { font-size: 0.93em; color: #888; margin-left: 8px; }
        .search-result-excerpt { color: #444; font-size: 1em; margin-top: 4px; }
        .no-results { color: #888; text-align: center; margin: 32px 0; }
        .btn-voltar-busca {
            display: inline-flex;
            align-items: center;
            gap: 7px;
            margin: 32px 0 0 32px;
            padding: 7px 18px;
            background: var(--accent-color, #2a7ae4);
            color: #fff;
            border: none;
            border-radius: 6px;
            font-size: 1em;
            font-weight: 500;
            text-decoration: none;
            box-shadow: 0 2px 8px #0001;
            transition: background 0.18s;
        }
        .btn-voltar-busca:hover {
            background: #1956a3;
            color: #fff;
            text-decoration: none;
        }
        @media (max-width: 700px) {
            .btn-voltar-busca { margin-left: 12px; }
        }
    </style>
</head>
<body>
    <a href="index.php" class="btn-voltar-busca"><i class="fas fa-arrow-left"></i> Voltar ao Fórum</a>
    <div class="search-results-container">
        <div class="search-title"><i class="fas fa-search"></i> Resultados para "<?php echo htmlspecialchars($termo); ?>"</div>
        <?php if ($termo === ''): ?>
            <p class="no-results">Digite um termo para buscar.</p>
        <?php elseif (empty($resultados)): ?>
            <p class="no-results">Nenhum resultado encontrado.</p>
        <?php else: ?>
            <?php foreach ($resultados as $topico): ?>
                <div class="search-result-item">
                    <a href="ver_topico.php?id=<?php echo $topico['id']; ?>" class="search-result-title">
                        <?php echo htmlspecialchars($topico['titulo']); ?>
                    </a>
                    <span class="search-result-date">
                        <?php echo date('d/m/Y', strtotime($topico['created_at'])); ?>
                    </span>
                    <div class="search-result-excerpt">
                        <?php
                        $resumo = strip_tags($topico['conteudo']);
                        echo strlen($resumo) > 120 ? substr($resumo, 0, 120) . '...' : $resumo;
                        ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</body>
</html> 