/* Estilo elegante e atual para calendario_agenda_AUTOMATICO.php */
:root {  
  --primary: #00008B;
  --primary-light: #c7d8eb;  
  --primary-dark: #4169E1;
  --secondary: #6c757d;  
  --success: #28a745;
  --info: #17a2b8;  
  --warning: #ffc107;
  --danger: #dc3545;  
  --light: #f8f9fa;
  --dark: #343a40;  
  --white: #ffffff;
  --body-bg: #f5f7fa;  
  --body-color: #212529;
  --border: #e0e0e0;
  --border-radius: 0.5rem;  
  --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --transition: all 0.3s ease;  
  --font-family-sans: 'Quicksand', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'Courier Prime', monospace;
  --display-light: block;
  --display-dark: none;
}

/* Variáveis para modo escuro */
[data-theme="dark"] {
  --primary: #4169E1;          /* Azul royal mais claro */
  --primary-light: #6c92ff;    /* Versão mais clara do azul royal */
  --primary-dark: #3a5cd0;     /* Versão mais escura do azul royal */
  --secondary: #1a1a2e;        /* Azul escuro quase preto */
  --body-bg: #0a0a1f;          /* Fundo principal escuro com tom azulado */
  --body-color: #e4e6f0;       /* Texto claro com tom azulado */
  --border: #2d2d42;           /* Borda escura com tom azulado */
  --white: #13132b;            /* Fundo dos cards escuro */
  --dark: #e4e6f0;             /* Invertido para o tema escuro */
  --light: #232338;            /* Versão escura do light */
  --box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
  --display-light: none;
  --display-dark: block;
}
/* Estilos base */
body {
  font-family: var(--font-family-sans);
  background-color: var(--body-bg);
  color: var(--body-color);
  line-height: 1.6;  margin: 0;
  padding: 0;
}
.container {
  max-width: 1200px;  margin: 2rem auto;
  padding: 0 1rem;}
/* Header e títulos */
#header {
  background: var(--white);
  border: none !important;  border-radius: var(--border-radius) !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;  padding: 2.5rem !important;
  margin-bottom: 2rem;  position: relative;
  overflow: hidden;}
#header::before {
  content: '';  
  position: absolute;
  top: 0;  
  left: 0;
  width: 100%;  
  height: 5px;
  background: linear-gradient(90deg, var(--primary), var(--primary), var(--primary));
}

.titulo {
  font-family: var(--font-family-sans);  
  color: var(--primary);
  font-weight: 600;  
  /*margin-bottom: 1.5rem;*/
  position: relative;  
  /*padding-bottom: 0.5rem;*/
}
h2.titulo {  font-size: 1.8rem;
  text-align: center;  margin-bottom: 2rem;
}
h2.titulo::after {  content: '';
  position: absolute;  bottom: 0;
  left: 50%;  transform: translateX(-50%);
  width: 100px;  height: 3px;
  /*background: linear-gradient(90deg, var(--primary-light), var(--primary), var(--primary-light));*/  border-radius: 3px;
}
/* Formulários e inputs 
form {
  margin-top: 1.5rem;
}*/
label {
  font-weight: 500;  color: var(--primary);
  margin-bottom: 0.5rem;  display: inline-block;
}
input[type="date"],input[type="number"],
select {
  display: block;
  /*width: 100%;  */
  padding: 0.75rem 1rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--body-color);
  background-color: var(--white);
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: var(--border-radius);
  transition: var(--transition);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}
input[type="date"]:focus,input[type="number"]:focus,
select:focus {  border-color: var(--primary);
  outline: 0;  box-shadow: 0 0 0 0.25rem rgba(58, 110, 165, 0.25);
}
.campoTipo {  font-family: var(--font-family-sans);
  border-radius: var(--border-radius);  border: 1px solid #ced4da;
  padding: 0.75rem 1rem;  width: 100%;
  max-width: 300px;}
/* Checkboxes estilizados */
input[type="checkbox"] {  position: relative;
  width: 18px;  height: 18px;
  margin-right: 8px;  cursor: pointer;
  vertical-align: middle;  -webkit-appearance: none;
  -moz-appearance: none;  appearance: none;
  border: 2px solid var(--primary);  border-radius: 4px;
  transition: var(--transition);}
input[type="checkbox"]:checked {
  background-color: var(--primary);  border-color: var(--primary);
}
/* A regra ::after foi removida para evitar o símbolo ✓ duplicado */
input[type="checkbox"]:focus {  outline: none;
  box-shadow: 0 0 0 3px rgba(58, 110, 165, 0.25);}
/* Botões */
.btn {  
  display: inline-block;
  font-weight: 500;  
  text-align: center;
  white-space: nowrap;  
  vertical-align: middle;
  border: 1px solid transparent;
  padding: 0.75rem 1.5rem;  font-size: 1rem;
  line-height: 1.5;  border-radius: var(--border-radius);
  transition: var(--transition);  cursor: pointer;
}
.btn-warning {  color: #212529;
  background: linear-gradient(135deg, #ffc107, #ffb300);  border-color: #ffc107;
  box-shadow: 0 4px 6px rgba(255, 193, 7, 0.3);}
.btn-warning:hover {
  background: linear-gradient(135deg, #ffb300, #ffa000);  border-color: #ffb300;
  transform: translateY(-2px);  box-shadow: 0 6px 8px rgba(255, 193, 7, 0.4);
}
.btn-dark {  color: #fff;
  background: linear-gradient(135deg, #343a40, #23272b);  border-color: #343a40;
  box-shadow: 0 4px 6px rgba(52, 58, 64, 0.3);}
.btn-dark:hover {
  background: linear-gradient(135deg, #23272b, #1d2124);  border-color: #23272b;
  transform: translateY(-2px);  box-shadow: 0 6px 8px rgba(52, 58, 64, 0.4);
}
.btn-gold {  color: #F5DEB3 !important;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);}
.btn-sm {
  padding: 0.5rem 1rem;  font-size: 0.875rem;
}
.btn-help {  background: none;
  border: none;  color: var(--primary);
  font-size: 18px;  cursor: pointer;
  padding: 5px;  transition: transform 0.2s ease;
  display: inline-flex;  align-items: center;
  justify-content: center;  width: 30px;
  height: 30px;  border-radius: 50%;
}
.btn-help:hover {  transform: scale(1.2);
  background-color: rgba(58, 110, 165, 0.1);}
.btn-help:focus {
  outline: none;  box-shadow: 0 0 0 3px rgba(58, 110, 165, 0.25);
}
/* Layout de grid para formulários */.form-row {
  display: flex;  flex-wrap: wrap;
  margin-right: -10px;  margin-left: -10px;
}
.form-group {  flex: 1 0 0%;
  padding: 0 10px;  margin-bottom: 1rem;
}
/* Dias da semana */
.dias-semana-container label {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  /*background-color: white;*/
  border-radius: var(--border-radius);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: var(--transition);
  cursor: pointer;
  font-family: var(--font-family-sans);
  font-weight: 500;
}
.dias-semana-container label:hover {
  background-color: var(--primary-light);  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Configuração avançada */

.config-avancada-toggle:hover {
  background-color: var(--primary-light);  border-color: var(--primary);
}
.config-avancada-toggle label {  font-family: var(--font-family-sans);
  font-weight: 500;  cursor: pointer;
  color: var(--primary);  margin-bottom: 0;
}

.dias-semana-avancado {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.dia-config-avancada {
  border: 1px solid rgba(0, 0, 0, 0.1);  
  padding: 20px;
  border-radius: var(--border-radius);
  /*background-color: var(--body-bg);*/
  transition: var(--transition);  
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  /*min-height: 180px;  display: flex;*/
  flex-direction: column;
}
.dia-config-avancada:hover {
  transform: translateY(-3px);  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}
.dia-titulo {  font-weight: 600;
  color: var(--primary-dark);  margin-bottom: 15px;
  display: flex;  align-items: center;
  justify-content: space-between;}
.materias-dia {
  margin-left: 10px;
  padding: 15px;
  border-radius: 8px;
  background-color: #f9f9f9;
  max-height: 200px;
  overflow-y: auto !important; /* Forçar overflow-y sempre como auto */
  transition: all 0.3s ease;
  opacity: 1;
  display: block;
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}

.materias-dia::-webkit-scrollbar {
  width: 8px !important;
  display: block !important;
}

.materias-dia::-webkit-scrollbar-track {
  background: #f1f1f1 !important;
  border-radius: 4px !important;
}

.materias-dia::-webkit-scrollbar-thumb {
  background: #888 !important;
  border-radius: 4px !important;
}

.materias-dia::-webkit-scrollbar-thumb:hover {
  background: #555 !important;
}

/* Indicador visual de overflow */
.materias-dia.has-overflow {
  border-right: 3px solid #3a6ea5;
}

/* Garantir altura mínima para o container quando ativo */
.dia-materias.active {
  /*min-height: 220px;*/
}

/* Estilo para o container quando tem muitos itens */
.materias-dia-container {
  position: relative;
  padding-right: 5px;
}
/* Botões de selecionar/limpar */.botoes-selecao {
  display: flex;  gap: 10px;
  margin-bottom: 10px;}
.btn-selecionar, .btn-limpar {
  padding: 6px 12px;  font-size: 0.875rem;
  border-radius: var(--border-radius);  cursor: pointer;
  font-family: var(--font-family-sans);  transition: var(--transition);
  border: 1px solid transparent;}
.btn-selecionar {
  background-color: var(--primary-light);  color: var(--primary-dark);
  border-color: var(--primary);}
.btn-selecionar:hover {
  background-color: var(--primary);  color: white;
}
.btn-limpar {  background-color: #f5f5f5;
  color: var(--secondary);  border-color: #ced4da;
}
.btn-limpar:hover {  background-color: #e9ecef;
  color: var(--dark);}
/* Legenda de eventos */
.legenda-eventos {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 30px;
  padding: 15px;
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}
/* Seletor separado para o tema escuro */
[data-theme="dark"] .legenda-eventos {
  background-color: var(--white); /* Usando var(--white) que no tema escuro é #13132b */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.legenda-item {  display: flex;
  flex-wrap: wrap;  gap: 15px;
}
.legenda-cor {  width: 20px;
  height: 20px;  border-radius: 4px;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);}
.legenda-texto {
  font-family: var(--font-family-sans);  font-size: 0.875rem;
  color: var(--body-color);}
.legenda-pendente, .legenda-realizado {
  width: 20px;  height: 20px;
  margin-right: 8px;  display: inline-block;
  border-radius: 4px;  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}
.legenda-pendente {  border: 2px solid red;
  background-color: transparent;}
.legenda-realizado {
  border: 2px solid #28a745;  background-color: transparent;
}
/* Calendário */
#calendar {
  border: none !important;
  border-radius: var(--border-radius) !important;
  padding: 20px !important;
  box-shadow: var(--box-shadow) !important;
  background-color: var(--white) !important;
  max-width: 1400px;
  width: 95%;
  margin: 0 auto;
  --fc-today-bg-color: rgba(58, 110, 165, 0.1);
  --fc-border-color: rgba(0, 0, 0, 0.1);
  --fc-non-business-color: rgba(245, 245, 245, 0.5);
  --fc-button-text-color: #fff;
  --fc-button-bg-color: var(--primary);
  --fc-button-border-color: var(--primary);
  --fc-button-hover-bg-color: var(--primary-dark);
  --fc-button-hover-border-color: var(--primary-dark);
  --fc-button-active-bg-color: var(--primary-dark);
  --fc-button-active-border-color: var(--primary-dark);
  --fc-event-bg-color: var(--primary);
  --fc-event-border-color: var(--primary-dark);
  --fc-event-text-color: #fff;
}

[data-theme="dark"] #calendar {
  --fc-border-color: var(--border);
  --fc-page-bg-color: var(--white);
  --fc-neutral-bg-color: var(--secondary);
  --fc-list-event-hover-bg-color: var(--secondary);
  --fc-today-bg-color: rgba(65, 105, 225, 0.2);
  --fc-event-bg-color: var(--primary);
  --fc-event-border-color: var(--primary-dark);
  --fc-event-text-color: #fff;
}

[data-theme="dark"] .fc-day-today {
  background-color: rgba(65, 105, 225, 0.15) !important;
}

[data-theme="dark"] .fc-col-header-cell,
[data-theme="dark"] .fc-daygrid-day-number,
[data-theme="dark"] .fc-daygrid-day-top {
  color: var(--body-color);
}

#calendar-container {
  width: 100%;
  max-width: 1500px;
  margin: 2rem auto;
  padding: 0 1rem;
}
/* Modal */
.modal-content {  font-family: var(--font-family-sans);
  border: none;  border-radius: var(--border-radius);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);  overflow: hidden;
}
.modal-header {  background-color: var(--primary);
  color: white;  border-bottom: none;
  padding: 1.5rem;}
.modal-title {
  font-weight: 600;
}
  .modal-body {
    padding: 1.5rem;
    color: var(--body-color);
    text-align: justify;
}
.modal-footer {
  border-top: none;
  padding: 1.5rem;
  background-color: #f8f9fa;
}
/* Seletor separado para o tema escuro */
[data-theme="dark"] .modal-footer {
  background-color: var(--border);
}
.close {  color: white;
  opacity: 0.8;  transition: var(--transition);
}
.close:hover {  color: white;
  opacity: 1;
}

/* Alertas */
.alert {
  position: relative;
  padding: 1rem 1.5rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: var(--border-radius);
  font-family: var(--font-family-sans);
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.alert-warning {
  color: #856404;
  background-color: #fff3cd;
  border-color: #ffeeba;
}

.alert-info {
  color: #0c5460;
  background-color: #d1ecf1;
  border-color: #bee5eb;
}

/* Animações */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease forwards;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(58, 110, 165, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(58, 110, 165, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(58, 110, 165, 0);
  }
}

.pulse {
  animation: pulse 2s infinite;
}

/* Matérias */
.materia-checkbox {
  margin-bottom: 10px;
  padding: 8px 12px;
  border-radius: var(--border-radius);
  transition: background-color 0.2s;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.materia-checkbox:hover {
  background-color: var(--primary-light);
}

.materia-checkbox label {
  margin-left: 8px;
  font-family: var(--font-family-sans);
  cursor: pointer;
  margin-bottom: 0;
}

.materia-checkbox input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

/* Botão de envio */
.btn-submit {
  display: block;
  width: 100%;
  max-width: 300px;
  margin: 2rem auto 0;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: 600;
  text-align: center;
  color: white;
  background: linear-gradient(135deg, var(--primary), var(--primary-dark));
  border: none;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 6px rgba(58, 110, 165, 0.3);
  transition: var(--transition);
  cursor: pointer;
}

.btn-submit:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(58, 110, 165, 0.4);
  background: linear-gradient(135deg, var(--primary-dark), var(--primary));
}

.btn-submit:active {
  transform: translateY(-1px);
}

/* Cards */
.card {
  background-color: white;
  border-radius: var(--border-radius);
  border: none;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  transition: var(--transition);
  overflow: hidden;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
  background-color: var(--primary);
  color: white;
  padding: 1.25rem 1.5rem;
  border-bottom: none;
  font-weight: 600;
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  background-color: #f8f9fa;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1.25rem 1.5rem;
}

/* Badges */
.badge {
  display: inline-block;
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 0.25rem;
}

.badge-primary {
  color: white;
  background-color: var(--primary);
}

.badge-secondary {
  color: white;
  background-color: var(--secondary);
}

.badge-success {
  color: white;
  background-color: var(--success);
}

.badge-info {
  color: white;
  background-color: var(--info);
}

.badge-warning {
  color: #212529;
  background-color: var(--warning);
}

.badge-danger {
  color: white;
  background-color: var(--danger);
}

/* Responsividade */
@media (max-width: 992px) {
  .container {
    max-width: 100%;
    padding: 0 15px;
  }
  
  #header {
    padding: 2rem !important;
  }
  
  h2.titulo {
    font-size: 1.6rem;
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .form-group {
    margin-bottom: 1.5rem;
  }
  
  .dias-semana-container {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .dias-semana-avancado {
    grid-template-columns: 1fr;
  }
  
  .config-avancada-container {
    padding: 15px;
  }
  
  .legenda-item {
    justify-content: center;
    margin-bottom: 10px;
  }
  
  h2.titulo {
    font-size: 1.4rem;
  }
  
  .btn-submit {
    max-width: 100%;
  }
}

@media (max-width: 576px) {
  #header {
    padding: 1.5rem !important;
  }
  
  .dias-semana-container {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .dias-semana-container label {
    width: 100%;
  }
  
  .config-avancada-toggle {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .config-avancada-toggle label {
    margin-bottom: 10px;
  }
  
  .btn-help {
    position: absolute;
    top: 10px;
    right: 10px;
  }
}

/* Efeitos de foco para acessibilidade */
a:focus,
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(58, 110, 165, 0.25);
}

/* Estilos para o FullCalendar */
.fc .fc-toolbar-title {
  font-family: var(--font-family-sans);
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--primary);
}

.fc .fc-button {
  font-family: var(--font-family-sans);
  font-weight: 500;
  border-radius: var(--border-radius);
  padding: 0.5rem 1rem;
  transition: var(--transition);
}

.fc .fc-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.fc .fc-daygrid-day-number {
  font-family: var(--font-family-sans);
  font-weight: 500;
  color: var(--body-color);
  padding: 8px;
}

.fc .fc-col-header-cell-cushion {
  font-family: var(--font-family-sans);
  font-weight: 600;
  color: var(--primary-dark);
  padding: 10px;
}

.fc-event {
  border-radius: 4px;
  border: none;
  padding: 2px 5px;
  font-size: 0.85rem;
  transition: var(--transition);
}

.fc-event:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.fc-event-title {
  font-weight: 600;
  padding: 2px 0;
}

.fc-event-time {
  font-weight: 400;
  opacity: 0.8;
}

/* Estilos para o rodapé */
#rodape {
  text-align: center;
  padding: 2rem 0;
  margin-top: 3rem;
  background-color: white;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

#rodape img {
  max-width: 120px;
  height: auto;
  transition: var(--transition);
}

#rodape img:hover {
  transform: scale(1.05);
}

/* Estilos para o botão de voltar */
.btn-voltar {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  color: var(--primary);
  background-color: white;
  border: 1px solid var(--primary);
  border-radius: var(--border-radius);
  transition: var(--transition);
  text-decoration: none;
  margin-bottom: 1.5rem;
}

.btn-voltar:hover {
  color: white;
  background-color: var(--primary);
  transform: translateX(-5px);
}

.btn-voltar i {
  transition: transform 0.2s ease;
}

.btn-voltar:hover i {
  transform: translateX(-3px);
}

/* Estilos para o loader */
.loader-container {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 9999;
  justify-content: center;
  align-items: center;
}

.loader {
  width: 50px;
  height: 50px;
  border: 5px solid var(--primary-light);
  border-radius: 50%;
  border-top-color: var(--primary);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Estilos para tooltips */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 200px;
  background-color: var(--dark);
  color: white;
  text-align: center;
  border-radius: 6px;
  padding: 10px;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 0.875rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.tooltip .tooltiptext::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--dark) transparent transparent transparent;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}

/* Estilo para seção desabilitada */
.disabled-section {
  position: relative;
  opacity: 0.5;
  pointer-events: none;
  transition: opacity 0.3s ease;
}

.disabled-section::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  z-index: 1;
}

/* Estilo para campos desabilitados */
input:disabled,
select:disabled,
textarea:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
  opacity: 0.7;
}

/* Estilo para o container de configuração básica */
#config_basica {
  transition: opacity 0.3s ease, filter 0.3s ease;
  padding: 15px;
  border-radius: 8px;
  /*background-color: #fff;*/
  border: 1px solid #e9ecef;
  margin-bottom: 20px;
}

/* Estilo para o container de configuração avançada */


/* Estilo para o toggle de configuração avançada */
.config-avancada-toggle {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  /*background-color: #f8f9fa;*/
  border-radius: 8px;
  border: 1px solid #e9ecef;
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.config-avancada-toggle:hover {
  background-color: #e9ecef;
  border-color: #dee2e6;
}

.config-avancada-toggle input[type="checkbox"] {
  margin-right: 10px;
}

/* Estilo para o label do toggle */
.config-avancada-toggle label {
  margin-bottom: 0;
  cursor: pointer;
  font-weight: 500;
}

/* Estilos para alertas personalizados */
.alerta-personalizado {
  position: fixed;
  top: 20px;
  right: 20px;
  max-width: 400px;
  z-index: 1050;
  transform: translateX(110%);
  transition: transform 0.3s ease;
}

.alerta-personalizado.show {
  transform: translateX(0);
}

.alerta-conteudo {
  display: flex;
  align-items: center;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.alerta-icone {
  margin-right: 15px;
  font-size: 24px;
}

.alerta-mensagem {
  flex: 1;
  font-size: 14px;
}

.alerta-fechar {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  margin-left: 10px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.alerta-fechar:hover {
  opacity: 1;
}

/* Estilos para diferentes tipos de alertas */
.alerta-error .alerta-conteudo {
  background-color: #FEE2E2;
  border-left: 4px solid #DC2626;
  color: #B91C1C;
}

.alerta-success .alerta-conteudo {
  background-color: #D1FAE5;
  border-left: 4px solid #10B981;
  color: #047857;
}

.alerta-warning .alerta-conteudo {
  background-color: #FEF3C7;
  border-left: 4px solid #F59E0B;
  color: #B45309;
}

.alerta-info .alerta-conteudo {
  background-color: #DBEAFE;
  border-left: 4px solid #3B82F6;
  color: #1D4ED8;
}

/* Estilo para elementos desabilitados */
.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  /*background-color: #f8f9fa;*/
}

button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #e9ecef !important;
  color: #6c757d !important;
  border-color: #ced4da !important;
  pointer-events: none;
}



#mensagem-materias-basicas-desabilitadas,
#mensagem-materias-desabilitadas {
  margin-bottom: 15px;
  font-size: 0.9rem;
}

/* Estilo para dias da semana desabilitados */
.dias-semana-container label.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #f5f5f5;
  color: #999;
}

.dias-semana-container label.disabled:hover {
  transform: none;
  background-color: #f5f5f5;
  box-shadow: none;
}

/* Estilo para o container de dias da semana */
.dias-semana-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
  /*background-color: rgba(255, 255, 255, 0.7);*/
  padding: 15px;
  border-radius: 8px;
  /*box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);*/
}

/* Estilo para o container de configuração avançada */
.config-avancada-container {
  /*background-color: white;*/
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 20px;
  margin-top: 15px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* Estilo para o container de dias da semana avançado */
.dias-semana-avancado {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

/* Estilo para o container de dia da semana avançado */


/* Estilo para dia da semana avançado desabilitado */
.dia-config-avancada.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: var(--border);
}

.dia-config-avancada.disabled:hover {
  transform: none;
  box-shadow: none;
}

    /* Estilo para o overlay de carregamento */
    .loading-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        z-index: 9999;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }
    
    /* Estilo para o spinner de carregamento */
    .spinner {
        border: 5px solid #f3f3f3;
        border-top: 5px solid #FFD700; /* Cor dourada para combinar com seu tema */
        border-radius: 50%;
        width: 50px;
        height: 50px;
        animation: spin 1s linear infinite;
        margin-bottom: 15px;
    }
    
    /* Texto de carregamento */
    .loading-text {
        color: white;
        font-family: 'Courier Prime', monospace;
        font-size: 18px;
        text-align: center;
    }
    
    /* Animação do spinner */
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
/* Checkboxes estilizados */

input[type="checkbox"]:checked {
  background-color: var(--primary);
  border-color: var(--primary);
}
input[type="checkbox"]:checked::after { /* <<< ESTA É A REGRA PROBLEMÁTICA */
  content: '✓'; /* <<< Adiciona o símbolo de check */
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
}


/* --- CSS para Múltiplas Colunas na Lista de Matérias Básica --- */
#materias-basico-container {
  column-count: 3; /* Define o número de colunas desejado (ajuste conforme necessário) */
  column-gap: 20px; /* Espaço entre as colunas */
  margin-top: 10px; /* Adiciona um espaço acima da lista */
}

/* Para evitar que um item seja quebrado entre colunas */
.materia-item-basico {
  display: inline-block; /* Necessário para break-inside funcionar bem */
  width: 100%; /* Ocupa a largura da coluna */
  break-inside: avoid-column; /* Tenta evitar quebrar o item */
  margin-bottom: 5px; /* Espaçamento inferior entre itens */
}

/* Botão de submit personalizado */
.btn-submit-agenda {
  display: inline-block;
  font-weight: 600;
  padding: 12px 30px;
  font-size: 1rem;
  background: linear-gradient(135deg, var(--primary), var(--primary));
  color: white;
  border: none;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 8px rgba(7, 48, 255, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.btn-submit-agenda:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(7, 77, 255, 0.4);
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-dark));
}

.btn-submit-agenda:active {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(7, 57, 255, 0.3);
}

.btn-submit-agenda i {
  margin-right: 8px;
}

/* Efeito de pulse para o botão */

@keyframes pulse-submit {
  0% {
    box-shadow: 0 0 0 0 rgba(7, 57, 255, 0.5);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 193, 7, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
  }
}

/* Adicionar estilos personalizados para o SweetAlert2 */

   /* Estilos personalizados para o SweetAlert2 */
 .swal2-popup {
   font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
   border-radius: 10px;
   padding: 2rem;
   box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
 }

.swal2-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.swal2-html-container {
  font-size: 1rem;
  color: #555;
  margin-bottom: 1.5rem;
}

.swal2-icon {
  margin: 1.5rem auto;
  border-width: 3px;
}

.swal2-confirm {
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.swal2-confirm:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
}

.swal2-cancel {
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.swal2-cancel:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

/* Animação de entrada personalizada */
.swal2-show {
  animation: swal2-show-custom 0.3s;
}

@keyframes swal2-show-custom {
  0% {
    transform: scale(0.9);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

   /* Animação para o modal */
 .modal-especifico {
   transition: opacity 0.3s ease;
 }

.modal-especifico.ativo {
  opacity: 1;
}

.modal-especifico.fechando {
  opacity: 0;
}

.modal-especifico-content {
  transition: transform 0.3s ease, opacity 0.3s ease;
  transform: scale(1);
}

.modal-especifico.ativo .modal-especifico-content {
  transform: scale(1);
  opacity: 1;
}

.modal-especifico.fechando .modal-especifico-content {
  transform: scale(0.9);
  opacity: 0;
}

/* Capitalizar a primeira letra do mês no título do calendário */
.fc .fc-toolbar-title {
  text-transform: capitalize;
}

/* Capitalizar a primeira letra dos nomes dos meses nos botões e cabeçalhos */
.fc-theme-standard .fc-toolbar h2,
.fc-col-header-cell-cushion,
.fc-daygrid-day-number,
.fc-list-day-text,
.fc-list-day-side-text {
  text-transform: capitalize;
}

/* Botão de fechar página */
.btn-fechar-pagina {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 500;
  color: white;
  background: linear-gradient(135deg, var(--danger), #c82333);
  border: none;
  border-radius: var(--border-radius);
  box-shadow: 0 4px 6px rgba(220, 53, 69, 0.3);
  transition: var(--transition);
  cursor: pointer;
  text-decoration: none;
  margin: 1rem 0;
}

.btn-fechar-pagina:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(220, 53, 69, 0.4);
  background: linear-gradient(135deg, #c82333, #bd2130);
}

.btn-fechar-pagina:active {
  transform: translateY(-1px);
  box-shadow: 0 3px 6px rgba(220, 53, 69, 0.3);
}

.btn-fechar-pagina i {
  font-size: 1.1rem;
  transition: transform 0.2s ease;
}

.btn-fechar-pagina:hover i {
  transform: translateX(-3px);
}

/* Variante menor do botão fechar */
.btn-fechar-pagina-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

/* Variante com borda */
.btn-fechar-pagina-outline {
  background: transparent;
  color: var(--danger);
  border: 1px solid var(--danger);
  box-shadow: none;
}

.btn-fechar-pagina-outline:hover {
  color: white;
  background: var(--danger);
  box-shadow: 0 4px 6px rgba(220, 53, 69, 0.3);
}

/* Estilos para eventos realizados e pendentes */
.fc-event.evento-realizado {
  border-left: 4px solid #28a745 !important; /* Verde para eventos realizados */
  border-right: 4px solid #28a745 !important;
  border-top: 1px solid #28a745 !important;
  border-bottom: 1px solid #28a745 !important;
}

.fc-event.evento-pendente {
  border-left: 4px solid #dc3545 !important; /* Vermelho para eventos pendentes */
  border-right: 4px solid #dc3545 !important;
  border-top: 1px solid #dc3545 !important;
  border-bottom: 1px solid #dc3545 !important;
}

/* Adicionar um indicador visual mais claro */
.fc-event.evento-realizado:before {
  content: "✓";
  position: absolute;
  left: 2px;
  top: 2px;
  color: #28a745;
  font-weight: bold;
  font-size: 10px;
}

.fc-event.evento-pendente:before {
  content: "!";
  position: absolute;
  left: 4px;
  top: 2px;
  color: #dc3545;
  font-weight: bold;
  font-size: 10px;
}

.h4, h4 {
  font-size: 0.9rem;
}


.modal-footer.d-flex.justify-content-center {
    border-top: 1px solid #dee2e6;
    padding: 1rem;
    display: flex;
    justify-content: center;
}
/* Seletor separado para o tema escuro */
[data-theme="dark"] .modal-footer.d-flex.justify-content-center {
  border-top: 1px solid var(--border);
}

.excluirEventoModal {
    min-width: 120px;
    padding: 0.5rem 1rem;
}
.header {
  background-color: var(--primary);
  box-shadow: 0 2px 10px var(--shadow-color);
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-left, .header-right {
  display: flex;
  align-items: center;
}

.logo img {
  height: 50px;
  transition: opacity 0.3s ease;
}

.logo-light {
  display: var(--display-light);
}

.logo-dark {
  display: var(--display-dark);
}

.user-info {
  display: flex;
  align-items: center;
  margin-right: 20px;
  color: white;
}

.user-info i {
  font-size: 1.5rem;
  margin-right: 10px;
}

.user-name {
  font-weight: 600;
}

.theme-btn {
  background: transparent;
  border: none;
  color: var(--primary);
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  position: absolute;
  top: 8px;
  right: 12px;
  z-index: 1000;
  /*width: 40px;
  height: 40px;*/
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  /*background-color: var(--white);*/
  font-size: 0.8rem;
}

.theme-btn:hover {
  transform: rotate(30deg);
  background-color: var(--primary-light);
}

.theme-btn i {
  /*font-size: 1.2rem;*/
  color: white;
}

[data-theme="dark"] .theme-btn i {
  color: white;
}

.header_centro {
  text-align: center;
  margin-bottom: 1.5rem;
}

.header_centro h1 {
  font-family: 'Varela Round', 'Quicksand', sans-serif;
  font-size: 2.5rem;
  margin-bottom: 1rem;
  text-transform: uppercase;
  letter-spacing: 2px;
  color: var(--primary);
}

.header_centro p {
  color: var(--body-color);
  font-size: 1.1rem;
  opacity: 0.8;
}

/* Estilo para botões desabilitados */
.btn-selecionar.disabled, .btn-limpar.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: #e9ecef !important;
  color: #6c757d !important;
  border-color: #ced4da !important;
  pointer-events: none;
}

/* Garantir que a seção desabilitada realmente bloqueie interações */
.disabled-section {
  position: relative;
  opacity: 0.5;
  pointer-events: none !important;
  transition: opacity 0.3s ease;
}

/* Estilo para o ícone do calendário no seletor de data */
input[type="date"]::-webkit-calendar-picker-indicator {
  cursor: pointer;
  opacity: 0.8;
  transition: var(--transition);
}

input[type="date"]::-webkit-calendar-picker-indicator:hover {
  opacity: 1;
}

/* Ícone do calendário no modo escuro - solução mais forte */
[data-theme="dark"] input[type="date"]::-webkit-calendar-picker-indicator {
  filter: invert(1) brightness(1.8) !important; /* Inverte e aumenta o brilho */
  opacity: 1 !important;
  background-color: transparent !important;
}

/* Solução alternativa para navegadores específicos */
@-moz-document url-prefix() {
  [data-theme="dark"] input[type="date"] {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-calendar' viewBox='0 0 16 16'%3E%3Cpath d='M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4H1z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: calc(100% - 10px) center;
  }
  
  [data-theme="dark"] input[type="date"]::-webkit-calendar-picker-indicator {
    opacity: 0;
  }
}

/* Solução para Edge/Chrome */
@supports (-ms-ime-align:auto) {
  [data-theme="dark"] input[type="date"]::-webkit-calendar-picker-indicator {
    background-color: white !important;
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-calendar' viewBox='0 0 16 16'%3E%3Cpath d='M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4H1z'/%3E%3C/svg%3E");
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-calendar' viewBox='0 0 16 16'%3E%3Cpath d='M3.5 0a.5.5 0 0 1 .5.5V1h8V.5a.5.5 0 0 1 1 0V1h1a2 2 0 0 1 2 2v11a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V3a2 2 0 0 1 2-2h1V.5a.5.5 0 0 1 .5-.5zM1 4v10a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V4H1z'/%3E%3C/svg%3E");
  }
}