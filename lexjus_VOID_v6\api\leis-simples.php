<?php
/**
 * API Simplificada para gerenciamento de leis no sistema multi-leis
 * Versão compatível com pg_query para funcionar com a estrutura existente
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Tratar requisições OPTIONS (CORS preflight)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Verificar se o usuário está logado
session_start();
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit();
}

// Incluir conexão existente
require_once '../../conexao_POST.php';

$usuario_id = $_SESSION['idusuario'];
$metodo = $_SERVER['REQUEST_METHOD'];
$acao = $_GET['acao'] ?? '';

try {
    switch ($metodo) {
        case 'GET':
            switch ($acao) {
                case 'listar':
                    listarLeis($conexao, $usuario_id);
                    break;
                case 'carregar':
                    carregarLei($conexao, $usuario_id);
                    break;
                case 'estatisticas':
                    obterEstatisticas($conexao, $usuario_id);
                    break;
                case 'atual':
                    obterLeiAtual($conexao, $usuario_id);
                    break;
                default:
                    listarLeis($conexao, $usuario_id);
                    break;
            }
            break;
            
        case 'POST':
            switch ($acao) {
                case 'trocar':
                    trocarLei($conexao, $usuario_id);
                    break;
                default:
                    http_response_code(400);
                    echo json_encode(['erro' => 'Ação não reconhecida']);
                    break;
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['erro' => 'Método não permitido']);
            break;
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['erro' => 'Erro interno do servidor: ' . $e->getMessage()]);
}

/**
 * Lista todas as leis disponíveis
 */
function listarLeis($conexao, $usuario_id) {
    $query = "
        SELECT 
            l.*,
            COALESCE(r.total_revisoes, 0) as total_revisoes,
            COALESCE(r.pendentes, 0) as pendentes,
            COALESCE(p.lidos, 0) as artigos_lidos,
            COALESCE(f.favoritos, 0) as favoritos,
            CASE 
                WHEN l.total_artigos > 0 THEN 
                    ROUND((COALESCE(p.lidos, 0) * 100.0 / l.total_artigos), 1)
                ELSE 0 
            END as percentual_progresso
        FROM appestudo.lexjus_leis l
        LEFT JOIN (
            SELECT 
                lei_id,
                COUNT(*) as total_revisoes,
                COUNT(CASE WHEN data_proxima_revisao <= CURRENT_TIMESTAMP THEN 1 END) as pendentes
            FROM appestudo.lexjus_revisoes 
            WHERE usuario_id = $1
            GROUP BY lei_id
        ) r ON l.id = r.lei_id
        LEFT JOIN (
            SELECT 
                lei_id,
                COUNT(*) as lidos
            FROM appestudo.lexjus_progresso 
            WHERE usuario_id = $1 AND lido = true
            GROUP BY lei_id
        ) p ON l.id = p.lei_id
        LEFT JOIN (
            SELECT 
                lei_id,
                COUNT(*) as favoritos
            FROM appestudo.lexjus_favoritos 
            WHERE usuario_id = $1
            GROUP BY lei_id
        ) f ON l.id = f.lei_id
        WHERE l.ativa = true
        ORDER BY l.ordem_exibicao
    ";
    
    $result = pg_query_params($conexao, $query, [$usuario_id]);
    
    if (!$result) {
        throw new Exception('Erro ao buscar leis: ' . pg_last_error($conexao));
    }
    
    $leis = [];
    while ($row = pg_fetch_assoc($result)) {
        $leis[] = $row;
    }
    
    echo json_encode([
        'sucesso' => true,
        'leis' => $leis
    ]);
}

/**
 * Carrega os artigos de uma lei específica
 */
function carregarLei($conexao, $usuario_id) {
    $lei_codigo = $_GET['codigo'] ?? '';
    
    if (empty($lei_codigo)) {
        http_response_code(400);
        echo json_encode(['erro' => 'Código da lei é obrigatório']);
        return;
    }
    
    // Buscar informações da lei
    $query = "SELECT * FROM appestudo.lexjus_leis WHERE codigo = $1 AND ativa = true";
    $result = pg_query_params($conexao, $query, [$lei_codigo]);
    
    if (!$result || pg_num_rows($result) === 0) {
        http_response_code(404);
        echo json_encode(['erro' => 'Lei não encontrada']);
        return;
    }
    
    $lei = pg_fetch_assoc($result);
    
    // Carregar artigos do arquivo JSON
    $arquivo_json = __DIR__ . '/../banco/' . $lei['arquivo_json'];
    
    if (!file_exists($arquivo_json)) {
        http_response_code(404);
        echo json_encode(['erro' => 'Arquivo JSON da lei não encontrado: ' . $lei['arquivo_json']]);
        return;
    }
    
    $artigos_json = file_get_contents($arquivo_json);
    $artigos = json_decode($artigos_json, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        http_response_code(500);
        echo json_encode(['erro' => 'Erro ao decodificar JSON da lei: ' . json_last_error_msg()]);
        return;
    }
    
    // Atualizar total de artigos se necessário
    if ($lei['total_artigos'] != count($artigos)) {
        $query_update = "UPDATE appestudo.lexjus_leis SET total_artigos = $1 WHERE id = $2";
        pg_query_params($conexao, $query_update, [count($artigos), $lei['id']]);
        $lei['total_artigos'] = count($artigos);
    }
    
    echo json_encode([
        'sucesso' => true,
        'lei' => $lei,
        'artigos' => $artigos,
        'total_artigos' => count($artigos)
    ]);
}

/**
 * Obtém estatísticas básicas de uma lei
 */
function obterEstatisticas($conexao, $usuario_id) {
    $lei_codigo = $_GET['codigo'] ?? '';
    
    if (empty($lei_codigo)) {
        http_response_code(400);
        echo json_encode(['erro' => 'Código da lei é obrigatório']);
        return;
    }
    
    // Buscar ID da lei
    $query_lei = "SELECT id FROM appestudo.lexjus_leis WHERE codigo = $1";
    $result_lei = pg_query_params($conexao, $query_lei, [$lei_codigo]);
    
    if (!$result_lei || pg_num_rows($result_lei) === 0) {
        http_response_code(404);
        echo json_encode(['erro' => 'Lei não encontrada']);
        return;
    }
    
    $lei = pg_fetch_assoc($result_lei);
    $lei_id = $lei['id'];
    
    // Estatísticas básicas
    $stats = [
        'total_revisoes' => 0,
        'pendentes' => 0,
        'favoritos' => 0,
        'progresso' => 0
    ];
    
    // Revisões
    $query_revisoes = "
        SELECT 
            COUNT(*) as total,
            COUNT(CASE WHEN data_proxima_revisao <= CURRENT_TIMESTAMP THEN 1 END) as pendentes
        FROM appestudo.lexjus_revisoes 
        WHERE usuario_id = $1 AND lei_id = $2";
    
    $result_revisoes = pg_query_params($conexao, $query_revisoes, [$usuario_id, $lei_id]);
    if ($result_revisoes) {
        $revisoes = pg_fetch_assoc($result_revisoes);
        $stats['total_revisoes'] = (int)$revisoes['total'];
        $stats['pendentes'] = (int)$revisoes['pendentes'];
    }
    
    // Favoritos
    $query_favoritos = "SELECT COUNT(*) as total FROM appestudo.lexjus_favoritos WHERE usuario_id = $1 AND lei_id = $2";
    $result_favoritos = pg_query_params($conexao, $query_favoritos, [$usuario_id, $lei_id]);
    if ($result_favoritos) {
        $favoritos = pg_fetch_assoc($result_favoritos);
        $stats['favoritos'] = (int)$favoritos['total'];
    }
    
    // Progresso
    $query_progresso = "SELECT COUNT(*) as total FROM appestudo.lexjus_progresso WHERE usuario_id = $1 AND lei_id = $2 AND lido = true";
    $result_progresso = pg_query_params($conexao, $query_progresso, [$usuario_id, $lei_id]);
    if ($result_progresso) {
        $progresso = pg_fetch_assoc($result_progresso);
        $stats['progresso'] = (int)$progresso['total'];
    }
    
    echo json_encode([
        'sucesso' => true,
        'estatisticas' => $stats
    ]);
}

/**
 * Obtém a lei atual do usuário
 */
function obterLeiAtual($conexao, $usuario_id) {
    $query = "
        SELECT 
            p.lei_atual,
            l.nome,
            l.nome_completo,
            l.cor_tema,
            l.icone
        FROM appestudo.lexjus_user_preferencias p
        JOIN appestudo.lexjus_leis l ON p.lei_atual = l.codigo
        WHERE p.usuario_id = $1
    ";
    
    $result = pg_query_params($conexao, $query, [$usuario_id]);
    
    if ($result && pg_num_rows($result) > 0) {
        $lei_atual = pg_fetch_assoc($result);
    } else {
        // Se não tem preferência, usar CF como padrão e criar preferência
        $lei_atual = [
            'lei_atual' => 'CF',
            'nome' => 'Constituição Federal',
            'nome_completo' => 'Constituição da República Federativa do Brasil de 1988',
            'cor_tema' => '#e74c3c',
            'icone' => 'fas fa-flag'
        ];
        
        // Criar preferência padrão
        $query_insert = "
            INSERT INTO appestudo.lexjus_user_preferencias (usuario_id, lei_atual)
            VALUES ($1, 'CF')
            ON CONFLICT (usuario_id) DO NOTHING
        ";
        pg_query_params($conexao, $query_insert, [$usuario_id]);
    }
    
    echo json_encode([
        'sucesso' => true,
        'lei_atual' => $lei_atual
    ]);
}

/**
 * Troca a lei atual do usuário
 */
function trocarLei($conexao, $usuario_id) {
    $input = json_decode(file_get_contents('php://input'), true);
    $lei_codigo = $input['codigo'] ?? '';
    
    if (empty($lei_codigo)) {
        http_response_code(400);
        echo json_encode(['erro' => 'Código da lei é obrigatório']);
        return;
    }
    
    // Verificar se a lei existe
    $query_verificar = "SELECT * FROM appestudo.lexjus_leis WHERE codigo = $1 AND ativa = true";
    $result_verificar = pg_query_params($conexao, $query_verificar, [$lei_codigo]);
    
    if (!$result_verificar || pg_num_rows($result_verificar) === 0) {
        http_response_code(404);
        echo json_encode(['erro' => 'Lei não encontrada ou inativa']);
        return;
    }
    
    $lei = pg_fetch_assoc($result_verificar);
    
    // Atualizar preferência do usuário
    $query_update = "
        INSERT INTO appestudo.lexjus_user_preferencias (usuario_id, lei_atual, data_atualizacao)
        VALUES ($1, $2, CURRENT_TIMESTAMP)
        ON CONFLICT (usuario_id) 
        DO UPDATE SET lei_atual = $2, data_atualizacao = CURRENT_TIMESTAMP
    ";
    
    $result_update = pg_query_params($conexao, $query_update, [$usuario_id, $lei_codigo]);
    
    if (!$result_update) {
        http_response_code(500);
        echo json_encode(['erro' => 'Erro ao atualizar preferência: ' . pg_last_error($conexao)]);
        return;
    }
    
    echo json_encode([
        'sucesso' => true,
        'lei' => $lei,
        'mensagem' => 'Lei alterada com sucesso'
    ]);
}
?>
