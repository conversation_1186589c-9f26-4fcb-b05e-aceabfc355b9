<?php
//configurar_prova.php
session_start();
require_once 'includes/verificar_modulo.php';
require_once 'includes/mensagens.php';
verificarModulo();
if (!isset($_SESSION['idusuario'])) {
    header("Location: /login_index.php");
    exit();
}

include_once("assets/config.php");
include_once("components/mensagens.php");  // Adicionado include do componente
$usuario_id = $_SESSION['idusuario'];

// Primeiro verificar se o usuário tem edital selecionado
$query_edital = "    SELECT e.* 
    FROM appestudo.usuario_edital ue
    JOIN appestudo.edital e ON ue.edital_id = e.id_edital
    WHERE ue.usuario_id = $1
    ORDER BY ue.data_inscricao DESC 
    LIMIT 1";

$result_edital = pg_query_params($conexao, $query_edital, array($usuario_id));
$edital_atual = pg_fetch_assoc($result_edital);

if (!$edital_atual) {
    $_SESSION['erro'] = "Você precisa primeiro selecionar um edital antes de configurar uma prova.";
    header("Location: selecionar_edital.php");
    exit();
}

// Buscar prova ativa (se existir) - Removida referência a horas_estudo_dia
$query_prova_ativa = "    SELECT 
        p.*
    FROM appestudo.provas p 
    WHERE p.usuario_id = $1 
    AND p.status = true 
    ORDER BY p.created_at DESC 
    LIMIT 1";
$result_prova = pg_query_params($conexao, $query_prova_ativa, array($usuario_id));
$prova_ativa = pg_fetch_assoc($result_prova);

// Limpar mensagem de sucesso se veio da página de plano
if (isset($_SESSION['sucesso']) && strpos($_SESSION['sucesso'], 'plano de estudos está pronto') !== false) {
    unset($_SESSION['sucesso']);
}

// Consulta para buscar o nome do usuário com base no ID
$query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = $usuario_id";
$resultado_nome = pg_query($conexao, $query_buscar_nome);

if ($resultado_nome && pg_num_rows($resultado_nome) > 0) {
    $row = pg_fetch_assoc($resultado_nome);
    $nome_usuario = $row['nome'];
} else {
    echo "Usuário não encontrado.";
}

// Buscar matérias do edital selecionado com seus pesos e dificuldades
$query_materias = "
   WITH pesos_calculados AS (
    SELECT 
        m.idmateria,
        COALESCE(pm.peso, 1) AS peso_atual,
        COALESCE(pm.nivel_dificuldade, 3) AS dificuldade_atual
    FROM appestudo.materia m
    LEFT JOIN appestudo.pesos_materias pm 
        ON m.idmateria = pm.materia_id 
        AND pm.prova_id = $2
),
conteudo_valido AS (
    SELECT 
        m.idmateria,
        m.nome,
        m.cor,
        uc.status_estudo,
        uc.id as conteudo_id
    FROM 
        appestudo.usuario_conteudo AS uc
    JOIN 
        appestudo.conteudo_edital AS ce ON uc.conteudo_id = ce.id_conteudo
    JOIN 
        appestudo.materia AS m ON ce.materia_id = m.idmateria
    WHERE 
        uc.usuario_id = $3
        AND uc.status = true
        AND ce.edital_id = $1
        AND ce.capitulo ~ '^[0-9.]+$'
)
SELECT 
    cv.idmateria, 
    cv.nome,
    cv.cor,
    pc.peso_atual,
    pc.dificuldade_atual,
    COUNT(cv.conteudo_id) AS total_conteudos_status_true
FROM 
    conteudo_valido cv
JOIN 
    pesos_calculados pc ON cv.idmateria = pc.idmateria
GROUP BY 
    cv.idmateria, 
    cv.nome,
    cv.cor,
    pc.peso_atual,
    pc.dificuldade_atual
ORDER BY 
    cv.nome;
";

$result_materias = pg_query_params($conexao, $query_materias, array(
    $edital_atual['id_edital'],
    $prova_ativa['id'] ?? null,
    $usuario_id
));

// Se não houver matérias no edital
if (pg_num_rows($result_materias) == 0) {
    $_SESSION['erro'] = "O edital selecionado não possui matérias cadastradas.";
    header("Location: selecionar_edital.php");
    exit();
}

// Preparar dados para o debug (opcional)
$debug_data = array(
    'prova' => $prova_ativa,
    'materias' => array()
);

while ($row = pg_fetch_assoc($result_materias)) {
    $debug_data['materias'][] = $row;
}
pg_result_seek($result_materias, 0);

// Verificação de datas APENAS se existir uma prova ativa com datas definidas
if ($prova_ativa && isset($prova_ativa['data_prova']) && isset($prova_ativa['data_inicio_estudo'])) {
    $data_prova_obj = new DateTime($prova_ativa['data_prova']);
    $data_inicio_obj = new DateTime($prova_ativa['data_inicio_estudo']);

    // Calcula a data do início da semana da prova (segunda-feira da semana)
    $inicio_semana_prova = clone $data_prova_obj;
    while ($inicio_semana_prova->format('N') != 1) { // 1 = segunda-feira
        $inicio_semana_prova->modify('-1 day');
    }

    // Se a data de início cair na semana da prova
    if ($data_inicio_obj >= $inicio_semana_prova) {
        $_SESSION['erro'] = "A data de início deve ser anterior à semana da prova (antes de " . $inicio_semana_prova->format('d/m/Y') . "). Escolha uma data pelo menos uma semana antes da prova.";
        header("Location: configurar_prova.php");
        exit();
    }
}

// Adicionar debug como comentário HTML (opcional)
echo "<!-- Debug Data: " . json_encode($debug_data, JSON_PRETTY_PRINT) . " -->";
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configurar Plano</title>
    <link href="https://fonts.googleapis.com/css2?  family=Playfair+Display:wght@400;700&family=Old+Standard+TT:wght@400;700&family=Crimson+Text:wght@400;600&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <style>
        /* Adicione isso no início do seu CSS, dentro do :root */
:root {
    /* Tema claro (padrão) */
    --primary: #00008B;
    --secondary: #f8f0e3;
    --accent: #4a4a4a;
    --border: #e0e0e0;
    --text: #2c3e50;
    --active: #00008B;
    --hover: #f5f5f5;
    --primary-blue: #00008B;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --background: #f5f5f5;
    --card-background: #ffffff;
    --header-background: #ffffff;
}

/* Tema escuro */
[data-theme="dark"] {
    --primary: #4169E1;  /* Azul Royal mais claro para contraste */
    --secondary: #1a1a2e; /* Azul muito escuro */
    --accent: #e0e0e0;
    --border: #2a2a3a;
    --text: #e0e0e0;
    --active: #4169E1;
    --hover: #1a1a2e;
    --primary-blue: #4169E1;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --background: #0a0a1a; /* Fundo azul escuro */
    --card-background: #1a1a2e; /* Cards com azul escuro */
    --header-background: #151525;
}

        body {
            font-family: 'Quicksand', sans-serif;
            background-color: var(--hover);
            color: var(--text);
            line-height: 1.6;
            margin: 0;
            min-height: 100vh;
            box-sizing: border-box;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
            box-sizing: border-box;
        }

        h1 {
            font-family: 'Quicksand', sans-serif;
            text-align: center;
            color: var(--primary);
            font-size: 2rem;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .btn-voltar {
            position: fixed;
            top: 140px;
            left: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: white;
            border: 1px solid var(--border);
            border-radius: 50%;
            box-shadow: 0 2px 4px var(--shadow-color);
            color: var(--primary);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .btn-voltar:hover {
            background: var(--primary);
            color: white;
            transform: scale(1.1);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        .prova-config {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 6px var(--shadow-color);
            margin: 0 20px 25px 20px;
            transition: transform 0.3s ease;
            border: 1px solid var(--border);
        }

        .edital-info {
            background: var(--card-background);
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid var(--border);
        }

        .edital-info h3 {
            color: var(--primary);
            margin: 0 0 10px 0;
            font-family: 'Quicksand', sans-serif;
        }

        .form-group {
            position: relative;
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-family: 'Quicksand', sans-serif;
            color: var(--primary);
            font-weight: 600;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border);
            border-radius: 8px;
            font-family: 'Quicksand', sans-serif;
            font-size: 1rem;
            background-color: white;
        }

        .materias-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .materia-card {
           
            padding: 20px;
            border-radius: 8px;
           
            box-shadow: 0 2px 4px var(--shadow-color);
            background: var(--card-background);
            border: 1px solid var(--border);
        }

        .materia-card h4 {
            color: var(--primary);
            margin: 0 0 15px 0;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border);
        }

        .peso-controls {
            margin-top: 15px;
        }

        .peso-controls label {
            display: block;
            margin-bottom: 5px;
            color: var(--text);
        }

        .peso-controls select {
            width: 100%;
            padding: 8px;
            border: 1px solid var(--border);
            border-radius: 8px;
            background-color: white;
        }

        .btn-submit {
            display: block;
            padding: 12px 24px;
            background: var(--primary);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-family: 'Quicksand', sans-serif;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            margin: 20px auto;
            width: fit-content;
            min-width: 200px;
            max-width: 300px;
            box-sizing: border-box;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            background: linear-gradient(135deg, var(--primary) 0%, #0000a3 100%);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .btn-submit:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
            opacity: 0.95;
        }

        @media (max-width: 768px) {
            body {
                padding: 20px;
            }

            .materias-grid {
                grid-template-columns: 1fr;
            }

            .btn-voltar {
                top: 10px;
                left: 10px;
                width: 40px;
                height: 40px;
            }
        }

        .flow-info {
            background: var(--card-background);
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid var(--border);
            box-shadow: 0 2px 4px var(--shadow-color);
            border-radius: 8px;
        }

        .steps-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
            max-width: 800px;
            margin: 0 auto;
            position: relative;
        }

        .steps-container::before {
            content: "";
            position: absolute;
            top: 25px;
            left: 50px;
            right: 50px;
            height: 2px;
            background: var(--border);
            z-index: 0;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            z-index: 1;
        }

        .step-number {
            width: 50px;
            height: 50px;
            background: white;
            border: 2px solid var(--border);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Quicksand', sans-serif;
            font-size: 1.2rem;
            color: var(--text);
        }

        .step.active .step-number {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        .step.completed .step-number {
            background: var(--accent);
            color: white;
            border-color: var(--accent);
        }

        .step-text {
            font-family: 'Quicksand', sans-serif;
            color: var(--text);
            font-size: 0.9rem;
            text-align: center;
        }

        .step.active .step-text {
            color: var(--primary);
            font-weight: 600;
        }

        .step.completed .step-text {
            color: var(--accent);
        }

        .intro-text {
            text-align: center;
            margin-bottom: 30px;
            font-family: 'Quicksand', sans-serif;
            color: var(--text);
            font-style: italic;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Melhorias na visualização dos campos */
        .form-group input,
        .form-group select {
            background-color: var(--card-background);
    color: var(--text);
    border-color: var(--border);
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(0, 0, 139, 0.1);
        }

        /* Novo estilo para o contador de conteúdos */
        .materia-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .conteudos-count {
            background: var(--primary);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            margin-left: 10px;
        }

        @media (max-width: 768px) {
            .steps-container {
                flex-direction: column;
                gap: 10px;
            }

            .steps-container::before {
                width: 2px;
                height: auto;
                top: 50px;
                bottom: 50px;
                left: 24px;
                right: auto;
            }

            .step {
                width: 100%;
                flex-direction: row;
                gap: 20px;
            }

            .step-text {
                text-align: left;
            }
        }

        .data-notification,
        .error-notification {
            position: absolute;
            top: calc(100% + 5px); /* 5px de espaço do campo */
            left: 50%; /* Centralizar horizontalmente */
            transform: translateX(-50%); /* Centralizar horizontalmente */
            width: 300px;
            background: white;
            border: 2px solid var(--border);
            box-shadow: 5px 5px 0 var(--shadow-color);
            border-radius: 8px;
            padding: 15px;
            display: flex;
            align-items: flex-start;
            gap: 12px;
            animation: slideIn 0.3s ease-out;
            z-index: 1000;
        }

        .error-notification {
            border-color: #dc3545;
            box-shadow: 5px 5px 0 rgba(220, 53, 69, 0.2);
        }

        .notification-icon {
            font-size: 1.2rem;
            color: var(--primary);
            background: rgba(0, 0, 139, 0.1);
            width: 35px;
            height: 35px;
            min-width: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
        }

        .notification-icon.error {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .notification-content {
            flex: 1;
        }

        .notification-content h4 {
            color: var(--primary);
            margin: 0 0 3px 0;
            font-family: 'Quicksand', sans-serif;
            font-size: 1rem;
        }

        .error-notification .notification-content h4 {
            color: #dc3545;
        }

        .notification-content p {
            margin: 0;
            color: var(--text);
            font-style: italic;
            font-size: 0.9rem;
        }

        .date-highlight {
            margin-top: 8px;
            text-align: left;
            background: var(--secondary);
            padding: 8px;
            border-radius: 4px;
            border: 1px solid var(--border);
        }

        .date-highlight .day-name {
            display: inline-block;
            color: var(--primary);
            font-size: 0.95rem;
            font-weight: 600;
            font-family: 'Quicksand', sans-serif;
            margin-right: 5px;
        }

        .date-highlight .date {
            display: inline-block;
            color: var(--accent);
            font-size: 0.9rem;
        }

        .notification-close {
            background: none;
            border: none;
            color: var(--text);
            opacity: 0.5;
            cursor: pointer;
            padding: 3px;
            font-size: 1rem;
            transition: all 0.3s ease;
            margin-left: auto;
        }

        .notification-close:hover {
            opacity: 1;
            color: var(--primary);
        }

        .notification-fade-out {
            animation: fadeOut 0.3s ease-out forwards;
        }

        /* Atualizar a animação para vir de cima */
        @keyframes slideIn {
            from {
                transform: translate(-50%, -10px);
                opacity: 0;
            }
            to {
                transform: translate(-50%, 0);
                opacity: 1;
            }
        }

        @keyframes fadeOut {
            to {
                transform: translate(-50%, -10px);
                opacity: 0;
            }
        }

        /* Ajuste para mobile */
        @media (max-width: 768px) {
            .data-notification,
            .error-notification {
                width: calc(100% - 20px);
                max-width: 300px;
            }
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: white;
            border-bottom: 1px solid var(--border);
            margin-bottom: 30px;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo img {
            height: 50px;
            width: auto;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 15px;
            background: var(--hover);
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .user-info i {
            color: var(--primary);
            font-size: 1.2rem;
        }

        .user-name {
            color: var(--text);
            font-weight: 600;
        }


        @media (max-width: 768px) {
            .header {
                padding: 10px;
            }

            .logo img {
                height: 40px;
            }

            .user-info {
                font-size: 0.8rem;
                padding: 6px 12px;
            }
        }

/* Adicione estes estilos no seu CSS */
.toggle-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 5px;
    margin-bottom: 15px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 20px;
    margin: 0;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: var(--primary);
}

input:checked + .slider:before {
    transform: translateX(20px);
}

.slider.round {
    border-radius: 20px;
}

.slider.round:before {
    border-radius: 50%;
}

.toggle-label {
    font-size: 0.85rem;
    color: #666;
    font-weight: 500;
}

/* Remover os estilos antigos do switch e adicionar estes */
.theme-toggle {
    margin-left: 15px;
}

.theme-btn {
    background: transparent;
    border: none;
    color: var(--primary);
    cursor: pointer;
    font-size: 1.2rem;
    padding: 8px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.theme-btn:hover {
    background: var(--hover);
}

/* Atualize os estilos existentes para usar as variáveis */
body {
    background-color: var(--background);
}

.header {
    background: var(--header-background);
}

.prova-config, .materia-card {
    background: var(--card-background);
}

/* ... e assim por diante para outros elementos */

/* Estilos para os logos */
.logo {
    position: relative;
}

.logo img {
    height: 50px;
    width: auto;
    transition: opacity 0.3s ease;
}

.logo-dark {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
}

/* Controle de visibilidade baseado no tema */
[data-theme="dark"] .logo-light {
    opacity: 0;
}

[data-theme="dark"] .logo-dark {
    opacity: 1;
}
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
        <div class="logo">
    <img src="../logo/logo_vertical_azul.png" alt="Logo" class="logo-light">
    <img src="../logo/logo_vertical.png" alt="Logo" class="logo-dark">
</div>
        </div>
        <div class="header-right">
    <div class="user-info">
        <i class="fas fa-user-circle"></i>
        <span class="user-name"><?php echo htmlspecialchars($nome_usuario); ?></span>
    </div>
    <div class="theme-toggle">
        <button id="theme-toggle-btn" class="theme-btn">
            <i class="fas fa-moon"></i>
        </button>
    </div>
</div>
     </div>
    <?= $prova_ativa ? ' <a href="plano_estudo_inteligente.php" class="btn-voltar">' : '<a href="selecionar_conteudos.php" class="btn-voltar">' ?>
    <i class="fas fa-arrow-left"></i>
</a>

<div class="container">
    <h1>Configurar Plano</h1>

    <div class="flow-info">
    <div class="steps-container">
    <div class="step completed">
        <div class="step-number"><i class="fas fa-check"></i></div>
        <div class="step-text">Selecionar Edital</div>
    </div>
    <div class="step completed">
        <div class="step-number"><i class="fas fa-check"></i></div>
        <div class="step-text">Selecionar Conteúdos</div>
    </div>
    <div class="step active">
        <div class="step-number">3</div>
        <div class="step-text">Configurar Plano</div>
    </div>
    <div class="step">
        <div class="step-number">4</div>
        <div class="step-text">Definir Horários</div>
    </div>
</div>
    </div>

    <div class="intro-text">
        <p>Configure seu plano de estudos ajustando as datas e os pesos das matérias.
            Essas informações serão usadas para criar um cronograma personalizado.</p>
    </div>

    <?php mostrarMensagens(); ?>

    <form method="POST" action="salvar_prova.php" class="prova-config">
    <?php if (isset($_POST['from_main'])): ?>
        <input type="hidden" name="from_main" value="true">
    <?php endif; ?>
        <div class="edital-info">
            <h3>Edital Selecionado</h3>
            <p><strong>Concurso:</strong> <?= htmlspecialchars($edital_atual['nome']) ?></p>
            <p><strong>Órgão:</strong> <?= htmlspecialchars($edital_atual['orgao']) ?></p>
        </div>

        <input type="hidden" name="prova_id" value="<?= $prova_ativa['id'] ?? '' ?>">

        <div class="form-group">
            <label for="nome_prova">Nome da Prova</label>
            <input type="text" id="nome_prova" name="nome_prova" required
                   value="<?= $prova_ativa['nome'] ?? '' ?>"
                   placeholder="Ex: Concurso TJ-SP 2024">
        </div>

        <!-- Nova checkbox para sincronizar com planejamento -->
<!-- Substituir a div do checkbox por: -->
<!-- Modifique o HTML para: -->
<!-- No arquivo configurar_prova.php, após o campo nome_prova -->
<div class="toggle-wrapper">
    <label class="switch">
        <input type="checkbox" name="sincronizar_planejamento" id="sincronizar_planejamento">
        <span class="slider round"></span>
    </label>
    <span class="toggle-label">Copiar nome para o seu Planejamento Geral</span>
</div>
        <div class="form-group">
            <label for="data_inicio_estudo">Data de Início dos Estudos</label>
			
			    <!-- Adicione o alerta aqui -->
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i>
        <strong>Nota:</strong> A data de início deve ser, no mínimo, uma semana antes da data da prova para permitir a distribuição adequada do conteúdo.
    </div>
	
            <input type="date" 
       id="data_inicio_estudo" 
       name="data_inicio_estudo" 
       required
       value="<?= date('Y-m-d', strtotime($prova_ativa['data_inicio_estudo'])) ?? date('Y-m-d') ?>">
        </div>

        <div class="form-group">
            <label for="data_prova">Data da Prova</label>
            <input type="date" id="data_prova" name="data_prova" required
                   value="<?= $prova_ativa['data_prova'] ?? '' ?>">
        </div>

        <!-- Adicione após o toggle existente do nome -->
<div class="toggle-wrapper">
    <label class="switch">
        <input type="checkbox" name="sincronizar_datas" id="sincronizar_datas">
        <span class="slider round"></span>
    </label>
    <span class="toggle-label">Sincronizar datas com o Planejamento Geral</span>
</div>


        <div class="materias-grid">
            <?php while ($materia = pg_fetch_assoc($result_materias)): ?>
                <div class="materia-card" style="border-left: 4px solid <?= $materia['cor'] ?>">
                    <h4><?= htmlspecialchars($materia['nome']) ?></h4>
                    <span class="conteudos-count"><?= $materia['total_conteudos_status_true'] ?> conteúdos</span>

                    <div class="peso-controls">
                        <label>Peso na prova:</label>
                        <select name="peso_<?= $materia['idmateria'] ?>">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <option value="<?= $i ?>" <?= $materia['peso_atual'] == $i ? 'selected' : '' ?>>
                                    <?= $i ?> - <?= $i == 1 ? 'Menor' : ($i == 5 ? 'Maior' : '') ?>
                                </option>
                            <?php endfor; ?>
                        </select>
                    </div>

                    <div class="peso-controls">
                        <label>Nível de dificuldade pessoal:</label>
                        <select name="dificuldade_<?= $materia['idmateria'] ?>">
                            <?php for ($i = 1; $i <= 5; $i++): ?>
                                <option value="<?= $i ?>" <?= $materia['dificuldade_atual'] == $i ? 'selected' : '' ?>>
                                    <?= $i ?> - <?= $i == 1 ? 'Fácil' : ($i == 5 ? 'Difícil' : '') ?>
                                </option>
                            <?php endfor; ?>
                        </select>
                    </div>
                </div>
            <?php endwhile; ?>
        </div>

        <button type="submit" class="btn-submit">
            Definir Dia e Hora de Estudo
        </button>
    </form>
</div>

<script>
    document.addEventListener("DOMContentLoaded", function () {
        // Função para verificar se é dia útil
        function isDiaUtil(data) {
            const diaSemana = data.getDay();
            return diaSemana >= 1 && diaSemana <= 6; // 1 (segunda) a 6 (sábado)
        }

        // Função para obter nome do dia da semana
        function getNomeDiaSemana(data) {
            const diasSemana = ['Domingo', 'Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sexta-feira', 'Sábado'];
            return diasSemana[data.getDay()];
        }

        // Função para mostrar notificação de data
        function mostrarNotificacaoData(data, diaSemana) {
            // Remover notificação anterior se existir
            const notificacaoExistente = document.querySelector('.data-notification, .error-notification');
            if (notificacaoExistente) {
                notificacaoExistente.remove();
            }

            // Criar nova notificação
            const notification = document.createElement('div');
            notification.className = 'data-notification';
            notification.innerHTML = `
            <div class="notification-icon">
                <i class="fas fa-calendar-check"></i>
            </div>
            <div class="notification-content">
                <h4>Data de Início Selecionada</h4>
                <p>Seu plano de estudos começará em</p>
                <div class="date-highlight">
                    <span class="day-name">${diaSemana}</span>
                    <span class="date">${data}</span>
                </div>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;

            // Adicionar ao DOM após o campo de data de início
            const dataInicioEstudoElement = document.getElementById('data_inicio_estudo');
            if (dataInicioEstudoElement) {
                dataInicioEstudoElement.parentElement.appendChild(notification);

                // Configurar botão de fechar
                const closeButton = notification.querySelector('.notification-close');
                closeButton.addEventListener('click', function () {
                    notification.remove();
                });
            }

            // Auto-remover após 5 segundos
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.classList.add('notification-fade-out');
                    setTimeout(() => notification.remove(), 300);
                }
            }, 5000);
        }

        // Função para mostrar notificação de erro
        function mostrarNotificacaoErro(mensagem) {
            // Remover notificação anterior se existir
            const notificacaoExistente = document.querySelector('.data-notification, .error-notification');
            if (notificacaoExistente) {
                notificacaoExistente.remove();
            }

            // Criar nova notificação de erro
            const notification = document.createElement('div');
            notification.className = 'error-notification';
            notification.innerHTML = `
            <div class="notification-icon error">
                <i class="fas fa-exclamation-circle"></i>
            </div>
            <div class="notification-content">
                <h4>Atenção</h4>
                <p>${mensagem}</p>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
         `;

            // Adicionar ao DOM após o campo ativo
            document.activeElement.parentElement.appendChild(notification);

            // Configurar botão de fechar
            const closeButton = notification.querySelector('.notification-close');
            closeButton.addEventListener('click', function () {
                notification.remove();
            });

            // Auto-remover após 5 segundos
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.classList.add('notification-fade-out');
                    setTimeout(() => notification.remove(), 300);
                }
            }, 5000);
        }

        // Referências aos elementos de entrada
        const inputDataProva = document.getElementById('data_prova');
        const inputDataInicioEstudo = document.getElementById('data_inicio_estudo');

        // Validação da data de início dos estudos
        
      
      

if (inputDataInicioEstudo) {
    function validarData(event) {
        // Pegando a data do input e ajustando o fuso horário
        const [ano, mes, dia] = event.target.value.split('-');
        const dataInicio = new Date(ano, mes - 1, dia);
        
        // Ajustando para meio-dia para evitar problemas com fuso horário
        dataInicio.setHours(12, 0, 0, 0);
        
        const diaSemana = getNomeDiaSemana(dataInicio);
        const dataFormatada = dataInicio.toLocaleDateString('pt-BR', {
            day: 'numeric',
            month: 'long',
            year: 'numeric'
        });

        mostrarNotificacaoData(dataFormatada, diaSemana);
    }

    // Evento de change para mostrar a notificação
    inputDataInicioEstudo.addEventListener('change', validarData);
}

function getNomeDiaSemana(data) {
    const diasSemana = ['Domingo', 'Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sexta-feira', 'Sábado'];
    return diasSemana[data.getDay()];
}

function isDiaUtil(data) {
    const diaSemana = data.getDay();
    return diaSemana >= 1 && diaSemana <= 6;
}

function getNomeDiaSemana(data) {
    const diasSemana = ['Domingo', 'Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sexta-feira', 'Sábado'];
    return diasSemana[data.getDay()];
}

// Função auxiliar para verificar se é dia útil
function isDiaUtil(data) {
    const diaSemana = data.getDay();
    return diaSemana >= 1 && diaSemana <= 6; // 1 (segunda) a 6 (sábado)
}

// Função para obter nome do dia da semana
function getNomeDiaSemana(data) {
    const diasSemana = ['Domingo', 'Segunda-feira', 'Terça-feira', 'Quarta-feira', 'Quinta-feira', 'Sexta-feira', 'Sábado'];
    return diasSemana[data.getDay()];
}
    });
// Remova o script antigo e adicione este
document.addEventListener('DOMContentLoaded', () => {
    const themeToggleBtn = document.getElementById('theme-toggle-btn');
    const themeIcon = themeToggleBtn.querySelector('i');
    
    // Verifica se há preferência salva
    const currentTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', currentTheme);
    updateIcon(currentTheme);
    
    themeToggleBtn.addEventListener('click', () => {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        updateIcon(newTheme);
    });
    
    function updateIcon(theme) {
        if (theme === 'dark') {
            themeIcon.classList.remove('fa-moon');
            themeIcon.classList.add('fa-sun');
        } else {
            themeIcon.classList.remove('fa-sun');
            themeIcon.classList.add('fa-moon');
        }
    }
});
</script>
</body>
</html>
