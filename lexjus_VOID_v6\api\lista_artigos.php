<?php
session_start();
require_once '../../conexao_POST.php';

// Verificar se o usuário está autenticado
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit;
}

$usuario_id = $_SESSION['idusuario'];
$metodo = $_SERVER['REQUEST_METHOD'];

// Obter dados do corpo da requisição
$dados = json_decode(file_get_contents('php://input'), true);

// Função para obter ID da lei atual do usuário
function obterLeiAtualUsuario($conexao, $usuario_id) {
    $query = "SELECT l.id
              FROM appestudo.lexjus_user_preferencias p
              JOIN appestudo.lexjus_leis l ON l.codigo = p.lei_atual
              WHERE p.usuario_id = $1";

    $result = pg_query_params($conexao, $query, [$usuario_id]);

    if ($result && pg_num_rows($result) > 0) {
        $row = pg_fetch_assoc($result);
        return $row['id'];
    }

    // Fallback: retornar ID da Constituição Federal
    $query_cf = "SELECT id FROM appestudo.lexjus_leis WHERE codigo = 'CF'";
    $result_cf = pg_query($conexao, $query_cf);

    if ($result_cf && pg_num_rows($result_cf) > 0) {
        $row_cf = pg_fetch_assoc($result_cf);
        return $row_cf['id'];
    }

    return null;
}

switch ($metodo) {
    case 'POST':
        // Adicionar artigo a uma lista
        if (!isset($dados['lista_id']) || !isset($dados['artigo_numero'])) {
            http_response_code(400);
            echo json_encode(['erro' => 'Dados incompletos']);
            exit;
        }
        
        $lista_id = $dados['lista_id'];
        $artigo_numero = $dados['artigo_numero'];

        // CORREÇÃO: Usar lei especificada ou buscar lei da lista
        if (isset($dados['lei_codigo']) && !empty($dados['lei_codigo'])) {
            $lei_codigo = $dados['lei_codigo'];

            $query_lei = "SELECT id FROM appestudo.lexjus_leis WHERE codigo = $1";
            $result_lei = pg_query_params($conexao, $query_lei, [$lei_codigo]);

            if ($result_lei && pg_num_rows($result_lei) > 0) {
                $row_lei = pg_fetch_assoc($result_lei);
                $lei_id = $row_lei['id'];
                error_log("API Lista Artigos: ✅ Usando lei especificada: $lei_codigo (ID: $lei_id)");
            } else {
                error_log("API Lista Artigos: ❌ Lei não encontrada: $lei_codigo");
                http_response_code(400);
                echo json_encode(['erro' => 'Lei não encontrada']);
                exit;
            }
        } else {
            // Buscar lei da lista existente
            $query_lei_lista = "SELECT l.lei_id, le.codigo
                               FROM appestudo.lexjus_listas l
                               JOIN appestudo.lexjus_leis le ON l.lei_id = le.id
                               WHERE l.id = $1 AND l.usuario_id = $2";

            $result_lei_lista = pg_query_params($conexao, $query_lei_lista, [$lista_id, $usuario_id]);

            if ($result_lei_lista && pg_num_rows($result_lei_lista) > 0) {
                $row_lei_lista = pg_fetch_assoc($result_lei_lista);
                $lei_id = $row_lei_lista['lei_id'];
                error_log("API Lista Artigos: ✅ Usando lei da lista: {$row_lei_lista['codigo']} (ID: $lei_id)");
            } else {
                error_log("API Lista Artigos: ❌ Lista não encontrada: ID $lista_id");
                http_response_code(403);
                echo json_encode(['erro' => 'Lista não encontrada']);
                exit;
            }
        }

        // Verificar se a lista pertence ao usuário (sem filtro por lei, já verificado acima)
        $query_verificar = "SELECT id FROM appestudo.lexjus_listas
                           WHERE id = $1 AND usuario_id = $2";

        $result_verificar = pg_query_params($conexao, $query_verificar, [$lista_id, $usuario_id]);
        
        if (!$result_verificar || pg_num_rows($result_verificar) === 0) {
            http_response_code(403);
            echo json_encode(['erro' => 'Lista não encontrada ou sem permissão']);
            exit;
        }
        
        // Adicionar artigo à lista
        $query = "INSERT INTO appestudo.lexjus_lista_artigos (lista_id, artigo_numero) 
                 VALUES ($1, $2) 
                 ON CONFLICT (lista_id, artigo_numero) DO NOTHING";
        
        $result = pg_query_params($conexao, $query, [$lista_id, $artigo_numero]);
        
        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao adicionar artigo à lista']);
            exit;
        }
        
        echo json_encode(['sucesso' => true, 'mensagem' => 'Artigo adicionado à lista']);
        break;
        
    case 'DELETE':
        // Remover artigo de uma lista
        if (!isset($dados['lista_id']) || !isset($dados['artigo_numero'])) {
            http_response_code(400);
            echo json_encode(['erro' => 'Dados incompletos']);
            exit;
        }
        
        $lista_id = $dados['lista_id'];
        $artigo_numero = $dados['artigo_numero'];

        $lei_id = obterLeiAtualUsuario($conexao, $usuario_id);

        if (!$lei_id) {
            http_response_code(500);
            echo json_encode(['erro' => 'Lei atual não encontrada']);
            exit;
        }

        // Verificar se a lista pertence ao usuário e à lei atual
        $query_verificar = "SELECT id FROM appestudo.lexjus_listas
                           WHERE id = $1 AND usuario_id = $2 AND lei_id = $3";

        $result_verificar = pg_query_params($conexao, $query_verificar, [$lista_id, $usuario_id, $lei_id]);
        
        if (!$result_verificar || pg_num_rows($result_verificar) === 0) {
            http_response_code(403);
            echo json_encode(['erro' => 'Lista não encontrada ou sem permissão']);
            exit;
        }
        
        // Remover artigo da lista
        $query = "DELETE FROM appestudo.lexjus_lista_artigos 
                 WHERE lista_id = $1 AND artigo_numero = $2";
        
        $result = pg_query_params($conexao, $query, [$lista_id, $artigo_numero]);
        
        if (!$result) {
            http_response_code(500);
            echo json_encode(['erro' => 'Erro ao remover artigo da lista']);
            exit;
        }
        
        echo json_encode(['sucesso' => true, 'mensagem' => 'Artigo removido da lista']);
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['erro' => 'Método não permitido']);
}
?>