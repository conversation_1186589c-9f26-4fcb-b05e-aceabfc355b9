<?php
declare(strict_types=1);
session_start();

// Ajuste nas configurações de headers de segurança
header("Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'");
header("X-Frame-Options: DENY");
header("X-Content-Type-Options: nosniff");
header("X-XSS-Protection: 1; mode=block");
header("Referrer-Policy: strict-origin-when-cross-origin");

// Função para log seguro
function logSeguranca(string $mensagem, string $tipo = 'INFO'): void {
    $data = date('Y-m-d H:i:s');
    $ip = $_SERVER['REMOTE_ADDR'];
    $usuario = $_SESSION['idusuario'] ?? 'Não autenticado';
    error_log("[$data][$tipo] IP: $ip | Usuário: $usuario | $mensagem");
}

// Função para verificar rate limit
function verificarRateLimit(): void {
    $max_requisicoes = 10; // máximo de salvamentos por minuto
    $janela_tempo = 60; // segundos
    
    if (!isset($_SESSION['tentativas_salvamento'])) {
        $_SESSION['tentativas_salvamento'] = [];
    }
    
    $tempo_atual = time();
    
    // Limpar tentativas antigas
    $_SESSION['tentativas_salvamento'] = array_filter(
        $_SESSION['tentativas_salvamento'],
        fn($tempo) => ($tempo_atual - $tempo) < $janela_tempo
    );
    
    $_SESSION['tentativas_salvamento'][] = $tempo_atual;
    
    if (count($_SESSION['tentativas_salvamento']) > $max_requisicoes) {
        logSeguranca("Rate limit excedido", "ALERTA");
        http_response_code(429);
        die(json_encode([
            'erro' => true,
            'mensagem' => 'Muitas requisições. Aguarde um momento.'
        ]));
    }
}

// Função para validar dados
function validarDados(): array {
    if (!isset($_SESSION['idusuario']) || !is_numeric($_SESSION['idusuario'])) {
        logSeguranca("Tentativa de acesso sem sessão válida", "ERRO");
        http_response_code(401);
        die(json_encode([
            'erro' => true,
            'mensagem' => 'Sessão inválida'
        ]));
    }

    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        logSeguranca("Método HTTP inválido: " . $_SERVER['REQUEST_METHOD'], "ERRO");
        http_response_code(405);
        die(json_encode([
            'erro' => true,
            'mensagem' => 'Método não permitido'
        ]));
    }

    $dados = [
        'id_usuario' => filter_var($_SESSION['idusuario'], FILTER_VALIDATE_INT),
        'id_materia' => filter_var($_POST['id_materia'] ?? null, FILTER_VALIDATE_INT),
        'id_planejamento' => filter_var($_POST['id_planejamento'] ?? null, FILTER_VALIDATE_INT),
        'detalhe' => $_POST['detalhe'] ?? ''
    ];

    if (!$dados['id_usuario'] || !$dados['id_materia'] || !$dados['id_planejamento']) {
        logSeguranca("Dados inválidos recebidos", "ERRO");
        http_response_code(400);
        die(json_encode([
            'erro' => true,
            'mensagem' => 'Dados inválidos'
        ]));
    }

    // Validação do tamanho do conteúdo
    if (empty($dados['detalhe']) || strlen($dados['detalhe']) > 1000000) {
        logSeguranca("Conteúdo inválido ou muito grande", "ERRO");
        http_response_code(400);
        die(json_encode([
            'erro' => true,
            'mensagem' => 'Conteúdo inválido ou muito grande'
        ]));
    }

    return $dados;
}

// Função principal para salvar conteúdo
function salvarConteudo(array $dados): void {
    try {
        require 'conexao_POST.php';

        // Verificar se o registro existe
        $query_check = "
            SELECT COUNT(*) AS total 
            FROM appEstudo.anotacao 
            WHERE materia_idmateria = $1 
            AND planejamento_idplanejamento = $2
            AND planejamento_usuario_idusuario = $3";

        $result_check = pg_query_params(
            $conexao,
            $query_check,
            [$dados['id_materia'], $dados['id_planejamento'], $dados['id_usuario']]
        );

        if (!$result_check) {
            throw new Exception("Erro na verificação: " . pg_last_error($conexao));
        }

        $row_check = pg_fetch_assoc($result_check);
        $registro_existe = (int)$row_check['total'] > 0;

        // Preparar a query apropriada
        if ($registro_existe) {
            $query = "
                UPDATE appEstudo.anotacao 
                SET detalhe = $1 
                WHERE materia_idmateria = $2 
                AND planejamento_idplanejamento = $3
                AND planejamento_usuario_idusuario = $4";
        } else {
            $query = "
                INSERT INTO appEstudo.anotacao 
                    (detalhe, materia_idmateria, planejamento_idplanejamento, 
                     planejamento_usuario_idusuario) 
                VALUES ($1, $2, $3, $4)";
        }

        $result = pg_query_params(
            $conexao,
            $query,
            [$dados['detalhe'], $dados['id_materia'], $dados['id_planejamento'], $dados['id_usuario']]
        );

        if (!$result) {
            throw new Exception("Erro na operação: " . pg_last_error($conexao));
        }

        logSeguranca("Conteúdo salvo com sucesso para matéria ID: " . $dados['id_materia'], "INFO");

        // Método alternativo de redirecionamento usando cabeçalhos HTTP
        header('Content-Type: text/html; charset=utf-8');
        echo '
            <!DOCTYPE html>
            <html>
            <head>
                <title>Redirecionando...</title>
            </head>
            <body>
                <form id="redirectForm" action="painel_editar_materias.php" method="POST">
                    <input type="hidden" name="id_materia" value="' . htmlspecialchars((string)$dados['id_materia']) . '">
                    <input type="hidden" name="id_planejamento" value="' . htmlspecialchars((string)$dados['id_planejamento']) . '">
                </form>
                <script src="assets/js/redirect.js"></script>
            </body>
            </html>';
        exit();

    } catch (Exception $e) {
        logSeguranca("Erro ao salvar conteúdo: " . $e->getMessage() . 
                    " | Matéria ID: " . $dados['id_materia'] . 
                    " | Planejamento ID: " . $dados['id_planejamento'], "ERRO");
        http_response_code(500);
        die(json_encode([
            'erro' => true,
            'mensagem' => 'Erro ao processar a requisição'
        ]));
    }
}

// Execução principal
try {
    verificarRateLimit();
    $dados = validarDados();
    salvarConteudo($dados);
} catch (Exception $e) {
    logSeguranca("Erro crítico: " . $e->getMessage(), "CRITICO");
    http_response_code(500);
    die(json_encode([
        'erro' => true,
        'mensagem' => 'Erro interno do servidor'
    ]));
}
?>
