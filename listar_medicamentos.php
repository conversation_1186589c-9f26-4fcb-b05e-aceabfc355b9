<?php
// Conectar ao banco de dados
include_once("conexao_POST.php");

// Verificar se a conexão foi estabelecida
if (!isset($conexao)) {
    die("Erro: Não foi possível conectar ao banco de dados.");
}

// Consulta SQL
try {
    $query = "SELECT *, to_char(data_inicio, 'DD-MM-YYYY') AS data_formatada 
              FROM appEstudo.medicamento 
              ORDER BY data_inicio DESC";

    $result = pg_query($conexao, $query);

    if (!$result) {
        throw new Exception("Erro ao executar a consulta: " . pg_last_error($conexao));
    }
} catch (Exception $e) {
    die("Erro: " . $e->getMessage());
}
?>

    <!DOCTYPE html>
    <html lang="pt-BR">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Listagem de Medicamentos Maya Rafaela</title>
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            }

            body {
                background: #f5f6fa;
                color: #2d3436;
                line-height: 1.6;
            }

            .container {
                max-width: 1200px;
                margin: 0 auto;
                padding: 20px;
            }

            .header {
                background: #6c5ce7;
                color: white;
                padding: 20px 0;
                margin-bottom: 30px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            }

            .header h1 {
                margin: 0;
                font-size: 2em;
                text-align: center;
            }

            .back-button {
                display: inline-block;
                padding: 10px 20px;
                background: #a29bfe;
                color: white;
                text-decoration: none;
                border-radius: 5px;
                margin: 20px 0;
                transition: background 0.3s;
            }

            .back-button:hover {
                background: #6c5ce7;
            }

            .med-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
                gap: 20px;
                padding: 20px 0;
            }

            .med-card {
                background: white;
                border-radius: 10px;
                padding: 20px;
                box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                transition: transform 0.3s;
            }

            .med-card:hover {
                transform: translateY(-5px);
            }

            .med-image {
                width: 100%;
                height: 200px;
                object-fit: cover;
                border-radius: 5px;
                margin-bottom: 15px;
            }

            .med-info h3 {
                color: #2d3436;
                margin-bottom: 10px;
            }

            .med-info p {
                color: #636e72;
                margin: 5px 0;
            }

            .med-actions {
                display: flex;
                gap: 10px;
                margin-top: 15px;
            }

            .btn {
                padding: 8px 15px;
                border-radius: 5px;
                text-decoration: none;
                color: white;
                font-size: 0.9em;
                transition: opacity 0.3s;
            }

            .btn:hover {
                opacity: 0.9;
            }

            .btn-edit {
                background: #74b9ff;
            }

            .btn-delete {
                background: #ff7675;
            }

            .label {
                font-weight: bold;
                color: #6c5ce7;
            }

            .error-message {
                background: #ff7675;
                color: white;
                padding: 15px;
                border-radius: 5px;
                margin: 20px 0;
            }

            .no-records {
                text-align: center;
                padding: 20px;
                background: #fff;
                border-radius: 10px;
                margin: 20px 0;
            }

            @media (max-width: 768px) {
                .container {
                    padding: 10px;
                }

                .header h1 {
                    font-size: 1.5em;
                }

                .med-grid {
                    grid-template-columns: 1fr;
                }
            }
        </style>
    </head>
    <body>
    <div class="header">
        <div class="container">
            <h1>Medicamentos da Maya Rafaela</h1>
        </div>
    </div>

    <div class="container">
        <a href="medicamento.php" class="back-button">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>

        <?php if (isset($result) && pg_num_rows($result) > 0) { ?>
            <div class="med-grid">
                <?php while ($row = pg_fetch_assoc($result)) { ?>
                    <div class="med-card">
                        <?php if (!empty($row['foto'])) { ?>
                            <img src="data:image/jpeg;base64,<?= base64_encode($row['foto']) ?>"
                                 alt="<?= htmlspecialchars($row['nome']) ?>"
                                 class="med-image">
                        <?php } else { ?>
                            <div class="med-image" style="background: #eee; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-pills" style="font-size: 3em; color: #6c5ce7;"></i>
                            </div>
                        <?php } ?>
                        <div class="med-info">
                            <h3><?= htmlspecialchars($row['nome']) ?></h3>
                            <p><span class="label">Detalhes:</span> <?= htmlspecialchars($row['detalhes']) ?></p>
                            <p><span class="label">Data de Início:</span> <?= htmlspecialchars($row['data_formatada']) ?></p>
                            <p><span class="label">Dosagem:</span> <?= htmlspecialchars($row['dosagem']) ?></p>

                            <div class="med-actions">
                                <a href="editar_medicamento.php?id=<?= $row['id_medicamento'] ?>" class="btn btn-edit">
                                    <i class="fas fa-edit"></i> Editar
                                </a>
                                <a href="excluir_medicamento.php?id=<?= $row['id_medicamento'] ?>"
                                   class="btn btn-delete"
                                   onclick="return confirm('Tem certeza que deseja excluir este medicamento?');">
                                    <i class="fas fa-trash"></i> Excluir
                                </a>
                            </div>
                        </div>
                    </div>
                <?php } ?>
            </div>
        <?php } else { ?>
            <div class="no-records">
                <i class="fas fa-info-circle"></i>
                <p>Nenhum medicamento encontrado.</p>
            </div>
        <?php } ?>
    </div>
    </body>
    </html>
<?php
// Fechar a conexão
if (isset($conexao)) {
    pg_close($conexao);
}
?>