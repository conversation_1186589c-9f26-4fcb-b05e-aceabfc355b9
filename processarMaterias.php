<?php
session_start();
include_once("conexao_POST.php");

// Função para limpar caracteres especiais e evitar injeção de SQL
function limpar_entrada($conexao, $entrada) {
    return pg_escape_string($conexao, htmlspecialchars($entrada));
}

// Verificação de sessão e permissão de usuário
if (isset($_SESSION['idusuario'])) {
    $id_usuario = $_SESSION['idusuario'];
    if ($id_usuario != 1) {
        header("Location: login_index.php");
        exit;
    }
} else {
    $_SESSION['validacao'] = false;
    header("Location: login_index.php");
    exit;
}

// Inserir matéria
if (isset($_POST['adicionar_materia'])) {
    $nome_materia = limpar_entrada($conexao, $_POST['nome_materia']);
    $cor_materia = limpar_entrada($conexao, $_POST['cor_materia']);

    $query_inserir_materia = "INSERT INTO appEstudo.materia (nome, cor) VALUES ('$nome_materia', '$cor_materia')";
    $resultado_inserir = pg_query($conexao, $query_inserir_materia);

    if ($resultado_inserir) {
        header("Location: {$_SERVER['PHP_SELF']}");
        exit;
    } else {
        echo "Erro ao inserir matéria.";
    }
}

// Editar matéria
if (isset($_POST['editar_materia'])) {
    $id_materia = limpar_entrada($conexao, $_POST['id_materia']);
    $nome_materia = limpar_entrada($conexao, $_POST['nome_materia']);
    $cor_materia = limpar_entrada($conexao, $_POST['cor_materia']);

    $query_editar_materia = "UPDATE appEstudo.materia SET nome = '$nome_materia', cor = '$cor_materia' WHERE idmateria = $id_materia";
    $resultado_editar = pg_query($conexao, $query_editar_materia);

    if ($resultado_editar) {
        header("Location: {$_SERVER['PHP_SELF']}");
        exit;
    } else {
        echo "Erro ao editar matéria.";
    }
}

// Excluir matéria
if (isset($_POST['excluir_materia'])) {
    $id_materia = limpar_entrada($conexao, $_POST['id_materia']);

    $query_excluir_materia = "DELETE FROM appEstudo.materia WHERE idmateria = $id_materia";
    $resultado_excluir = pg_query($conexao, $query_excluir_materia);

    if ($resultado_excluir) {
        header("Location: {$_SERVER['PHP_SELF']}");
        exit;
    } else {
        echo "Erro ao excluir matéria.";
    }
}

// Consultar matérias
$query_consultar_materias = "SELECT * FROM appEstudo.materia";
$resultado_materias = pg_query($conexao, $query_consultar_materias);

$materias = array();
while ($row = pg_fetch_assoc($resultado_materias)) {
    $materias[] = $row;
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <title>Gerenciador de Matérias</title>
    <!-- Adicionando o CSS do Bootstrap -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <style>
        /* Estilo global da página com a fonte Courier Prime, monospace */
        body {
            font-family: "Courier Prime", monospace;
        }
        /* Classe personalizada para alinhar os botões à direita */
        .align-right {
            display: flex;
            align-items: center;
            margin-left: auto;
        }

        /* Espaçamento entre os botões */
        .edit-button {
            margin-right: 8px;
        }
        /* Estilo para o círculo colorido */
        .color-circle {
            display: inline-block;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .btn-burlywood:hover {
            background-color: darkgoldenrod;
            /* Cor mais clara ao passar o mouse */
        }
        /* Classe personalizada para a cor burlywood no botão */
        .btn-burlywood {
            background-color: #DEB887;
            color: white;
        }
        /* Estilo para o botão "Excluir" quando o mouse estiver sobre ele */
        .btn-danger:hover {
            color: black;
        }
    </style>
</head>
<body>
<div class="container">
    <h1 class="my-4">Gerenciador de Matérias</h1>

    <!-- Abas -->
    <ul class="nav nav-tabs">
        <li class="nav-item">
            <a class="nav-link active" data-toggle="tab" href="#ListarMaterias">Listar Matérias</a>
        </li>
        <li class="nav-item">
            <a class="nav-link" data-toggle="tab" href="#AdicionarMateria">Adicionar Matéria</a>
        </li>
    </ul>

    <!-- Conteúdo das abas -->
    <div class="tab-content my-4">
        <div id="ListarMaterias" class="tab-pane active">
            <h2>Matérias existentes:</h2>
            <ul class="list-group">
                <?php
                $contador = 1;
                foreach ($materias as $materia) {
                    echo '<li class="list-group-item d-flex justify-content-between align-items-center">' .
                        '<div><span class="badge badge-secondary mr-2">' . $contador . '</span><span class="color-circle" style="background-color:' . $materia['cor'] . '"></span>' . htmlspecialchars($materia['nome']) . '</div>' .
                        '<div class="align-right">' .
                        '<button class="btn btn-burlywood edit-button" onclick="fillEditForm(' . $materia['idmateria'] . ', \'' . htmlspecialchars($materia['nome']) . '\', \'' . $materia['cor'] . '\')">Editar</button>' .
                        '<form method="post" style="display: inline-block;" onsubmit="return confirm(\'Tem certeza que deseja excluir a matéria?\')">' .
                        '<input type="hidden" name="id_materia" value="' . $materia['idmateria'] . '">' .
                        '<button type="submit" class="btn btn-danger" name="excluir_materia">Excluir</button>' .
                        '</form>' .
                        '</div></li>';
                    $contador++;
                }
                ?>
            </ul>
        </div>

        <div id="AdicionarMateria" class="tab-pane">
            <h2>Adicionar nova matéria:</h2>
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                <label for="nome_materia">Nome da matéria:</label>
                <input type="text" id="nome_materia" name="nome_materia" required><br>

                <label for="cor_materia">Cor:</label>
                <input type="color" id="cor_materia" name="cor_materia" required><br>

                <input type="submit" value="Adicionar" name="adicionar_materia" class="btn btn-success">
            </form>
        </div>

        <div id="EditarMateria" class="tab-pane">
            <h2>Editar matéria:</h2>
            <form action="<?php echo $_SERVER['PHP_SELF']; ?>" method="post">
                <input type="hidden" id="id_materia" name="id_materia" value="">
                <label for="nome_materia_edit">Nome da matéria:</label>
                <input type="text" id="nome_materia_edit" name="nome_materia" required><br>

                <label for="cor_materia_edit">Cor:</label>
                <input type="color" id="cor_materia_edit" name="cor_materia" required><br>

                <input type="submit" value="Salvar" name="editar_materia" class="btn btn-success">
            </form>
            <!-- Botão "Voltar" -->
            <button class="btn btn-secondary mt-3" onclick="openTab('ListarMaterias', document.querySelector('.nav-link.active'))">Voltar</button>
        </div>
    </div>
</div>

<!-- Adicionando o JavaScript do Bootstrap -->
<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.9.1/dist/umd/popper.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

<script>
    // Função para alternar as abas
    function openTab(tabName, element) {
        // Esconder todas as abas
        var tabcontent = document.getElementsByClassName("tab-pane");
        for (var i = 0; i < tabcontent.length; i++) {
            tabcontent[i].style.display = "none";
        }
        // Remover a classe "active" de todos os links de aba
        var tablinks = document.getElementsByClassName("nav-link");
        for (var i = 0; i < tablinks.length; i++) {
            tablinks[i].classList.remove("active");
        }
        // Mostrar a aba selecionada
        document.getElementById(tabName).style.display = "block";
        // Adicionar a classe "active" ao link de aba selecionado
        element.classList.add("active");
    }

    // Adiciona evento de clique aos links de aba
    var tabLinks = document.getElementsByClassName("nav-link");
    for (var i = 0; i < tabLinks.length; i++) {
        tabLinks[i].addEventListener("click", function(event) {
            event.preventDefault();
            var tabName = this.getAttribute("href").substring(1);
            openTab(tabName, this);
        });
    }

    // Função para preencher o formulário de edição
    function fillEditForm(id, nome, cor) {
        document.getElementById("id_materia").value = id;
        document.getElementById("nome_materia_edit").value = nome;
        document.getElementById("cor_materia_edit").value = cor;
        openTab("EditarMateria", document.querySelector(".nav-link.active"));
    }
</script>

</body>
</html>
