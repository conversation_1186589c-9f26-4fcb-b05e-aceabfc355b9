<?php
session_start();
require_once('../includes/auth.php');
require_once('../conexao_POST.php');
require_once('../cadastros/includes/verify_admin.php');

// Verifica se é o super admin
verificarSuperAdmin($conexao, false);

date_default_timezone_set('America/Sao_Paulo');

function listarBackups($diretorio) {
    $backups = [];
    if (is_dir($diretorio)) {
        $arquivos = scandir($diretorio);
        foreach ($arquivos as $arquivo) {
            if ($arquivo != "." && $arquivo != "..") {
                $caminho_completo = $diretorio . '/' . $arquivo;
                
                // Extrair a data do nome do arquivo e converter para o timezone correto
                preg_match('/(\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2})/', $arquivo, $matches);
                if (!empty($matches)) {
                    $data_str = str_replace(['_', '-'], [' ', ':'], $matches[1]);
                    $data = DateTime::createFromFormat('Y m d H:i:s', $data_str, new DateTimeZone('America/Sao_Paulo'));
                    $data_formatada = $data ? $data->format('d/m/Y H:i:s') : date("d/m/Y H:i:s", filemtime($caminho_completo));
                } else {
                    $data_formatada = date("d/m/Y H:i:s", filemtime($caminho_completo));
                }
                
                $backups[] = [
                    'nome' => $arquivo,
                    'data' => $data_formatada,
                    'tamanho' => formatarTamanho(filesize($caminho_completo)),
                    'caminho' => $caminho_completo
                ];
            }
        }
    }
    return $backups;
}

function formatarTamanho($bytes) {
    $unidades = ['B', 'KB', 'MB', 'GB'];
    $i = 0;
    while ($bytes >= 1024 && $i < count($unidades) - 1) {
        $bytes /= 1024;
        $i++;
    }
    return round($bytes, 2) . ' ' . $unidades[$i];
}

function realizarBackupEstrutura($conexao) {
    try {
        // Definir timezone
        date_default_timezone_set('America/Sao_Paulo');

        // Aumentar limite de tempo
        set_time_limit(300);
        ini_set('memory_limit', '512M');

        $data = date('Y-m-d_H-i-s');
        $diretorio_backup = '../backups/';

        if (!file_exists($diretorio_backup)) {
            mkdir($diretorio_backup, 0755, true);
        }

        $arquivo_backup = $diretorio_backup . "estrutura_{$data}.sql";

        // Tentar diferentes caminhos do pg_dump
        $possiveis_caminhos = [
            'C:\Program Files\PostgreSQL\17\bin\pg_dump.exe',
            'C:\Program Files\PostgreSQL\16\bin\pg_dump.exe',
            'C:\Program Files\PostgreSQL\15\bin\pg_dump.exe',
            'C:\Program Files\PostgreSQL\14\bin\pg_dump.exe',
            'C:\Program Files (x86)\PostgreSQL\17\bin\pg_dump.exe',
            'C:\Program Files (x86)\PostgreSQL\16\bin\pg_dump.exe',
            'pg_dump' // Caso esteja no PATH
        ];

        $pg_dump_path = null;
        foreach ($possiveis_caminhos as $caminho) {
            if (file_exists($caminho) || $caminho === 'pg_dump') {
                $pg_dump_path = $caminho;
                break;
            }
        }

        if (!$pg_dump_path) {
            throw new Exception("pg_dump não encontrado. Instale o PostgreSQL ou verifique o PATH.");
        }

        // Comando pg_dump para estrutura apenas
        $comando = sprintf(
            '"%s" -h %s -p %s -U %s -d %s -s --no-owner --no-privileges -f "%s"',
            $pg_dump_path,
            'app_estudo.postgresql.dbaas.com.br',
            '5432',
            'app_estudo',
            'app_estudo',
            $arquivo_backup
        );

        // Criar arquivo temporário com a senha
        $pgpass_temp = tempnam(sys_get_temp_dir(), 'pgpass');
        file_put_contents($pgpass_temp, "app_estudo.postgresql.dbaas.com.br:5432:app_estudo:app_estudo:Lucasb90#");

        // Definir variáveis de ambiente
        putenv("PGPASSFILE=" . $pgpass_temp);
        putenv("PGPASSWORD=Lucasb90#");

        // Log do comando para debug
        error_log("Executando comando: " . $comando);

        // Executar comando
        $output = [];
        $return_var = 0;
        exec($comando . " 2>&1", $output, $return_var);

        // Remover arquivo temporário de senha
        if (file_exists($pgpass_temp)) {
            unlink($pgpass_temp);
        }

        // Log da saída para debug
        error_log("Saída do pg_dump: " . implode("\n", $output));
        error_log("Código de retorno: " . $return_var);

        if ($return_var !== 0) {
            throw new Exception("Erro ao executar pg_dump (código $return_var): " . implode("\n", $output));
        }

        // Verificar se o arquivo foi criado
        if (!file_exists($arquivo_backup) || filesize($arquivo_backup) === 0) {
            throw new Exception("Arquivo de backup não foi criado ou está vazio");
        }

        // Compactar o backup
        $zip = new ZipArchive();
        $arquivo_zip = $diretorio_backup . "estrutura_{$data}.zip";

        if ($zip->open($arquivo_zip, ZipArchive::CREATE) === TRUE) {
            $zip->addFile($arquivo_backup, basename($arquivo_backup));
            $zip->close();

            // Remove o arquivo SQL original após compactação
            unlink($arquivo_backup);

            return [
                'sucesso' => true,
                'arquivo' => $arquivo_zip,
                'data' => $data,
                'tipo' => 'estrutura',
                'tamanho' => formatarTamanho(filesize($arquivo_zip))
            ];
        }

        throw new Exception("Erro ao compactar arquivo de backup");

    } catch (Exception $e) {
        error_log("Erro no backup de estrutura: " . $e->getMessage());
        return [
            'sucesso' => false,
            'erro' => $e->getMessage()
        ];
    }
}

$mensagem = '';
$tipo_mensagem = '';
$diretorio_backup = '../backups';

// Processar ações
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['realizar_backup'])) {
        require_once('backup_sistema.php');
        $resultado = realizarBackup($conexao);
        
        if ($resultado['sucesso']) {
            $_SESSION['mensagem'] = "Backup completo realizado com sucesso em " . $resultado['data'];
            $_SESSION['tipo_mensagem'] = "success";
        } else {
            $_SESSION['mensagem'] = "Erro ao realizar backup: " . $resultado['erro'];
            $_SESSION['tipo_mensagem'] = "error";
        }
        // Redirecionar após o POST
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;
    } 
    elseif (isset($_POST['realizar_backup_estrutura'])) {
        $resultado = realizarBackupEstrutura($conexao);
        
        if ($resultado['sucesso']) {
            $_SESSION['mensagem'] = "Backup da estrutura realizado com sucesso em " . $resultado['data'];
            $_SESSION['tipo_mensagem'] = "success";
        } else {
            $_SESSION['mensagem'] = "Erro ao realizar backup: " . $resultado['erro'];
            $_SESSION['tipo_mensagem'] = "error";
        }
        // Redirecionar após o POST
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;
    }
    // Opções de Migração
    elseif (isset($_POST['migrar_estrutura'])) {
        require_once('backup_estrutura.php');
        $resultado = migrarEstrutura(
            $conexao,
            $_POST['host'],
            $_POST['porta'],
            $_POST['usuario'],
            $_POST['senha'],
            $_POST['banco']
        );
        
        if ($resultado['sucesso']) {
            $_SESSION['mensagem'] = "Migração da estrutura realizada com sucesso";
            $_SESSION['tipo_mensagem'] = "success";
        } else {
            $_SESSION['mensagem'] = "Erro na migração da estrutura: " . $resultado['erro'];
            $_SESSION['tipo_mensagem'] = "error";
        }
        // Redirecionar após o POST
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;
    }
    elseif (isset($_POST['migrar_completo'])) {
        require_once('backup_completo.php');
        $resultado = migrarCompleto(
            $conexao,
            $_POST['host'],
            $_POST['porta'],
            $_POST['usuario'],
            $_POST['senha'],
            $_POST['banco']
        );
        
        if ($resultado['sucesso']) {
            $_SESSION['mensagem'] = "Migração completa realizada com sucesso";
            $_SESSION['tipo_mensagem'] = "success";
        } else {
            $_SESSION['mensagem'] = "Erro na migração completa: " . $resultado['erro'];
            $_SESSION['tipo_mensagem'] = "error";
        }
        // Redirecionar após o POST
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;
    }
    elseif (isset($_POST['excluir_backup'])) {
        $arquivo = filter_input(INPUT_POST, 'arquivo', FILTER_SANITIZE_STRING);
        $caminho_completo = $diretorio_backup . '/' . $arquivo;
        
        if (file_exists($caminho_completo) && unlink($caminho_completo)) {
            $_SESSION['mensagem'] = "Backup excluído com sucesso";
            $_SESSION['tipo_mensagem'] = "success";
        } else {
            $_SESSION['mensagem'] = "Erro ao excluir backup";
            $_SESSION['tipo_mensagem'] = "error";
        }
        // Redirecionar após o POST
        header("Location: " . $_SERVER['PHP_SELF']);
        exit;
    }
}

// Verificar e exibir mensagens da sessão
if (isset($_SESSION['mensagem'])) {
    $mensagem = $_SESSION['mensagem'];
    $tipo_mensagem = $_SESSION['tipo_mensagem'];
    // Limpar as mensagens da sessão após exibi-las
    unset($_SESSION['mensagem']);
    unset($_SESSION['tipo_mensagem']);
}

// Listar backups existentes
$backups = listarBackups($diretorio_backup);
usort($backups, function($a, $b) {
    return strtotime(str_replace('/', '-', $b['data'])) - strtotime(str_replace('/', '-', $a['data']));
});

?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciamento de Backup e Migração</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <style>
        :root {
    --primary-color: #00008B;
    --hover-color: #0000CD;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --admin-color: #6610f2;
    --warning-color: #ffc107;
    --light-bg: #f5f5f5;
    --white: #ffffff;
    --border-color: #ddd;
    --shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
        body {
            font-family: 'Quicksand', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .container h1 {
            color: var(--primary-color);
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: var(--primary-color); color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .alert {
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
        }
        .alert-success { background: #d4edda; color: #155724; }
        .alert-error { background: #f8d7da; color: #721c24; }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
        
        <h1><i class="fas fa-database"></i> Gerenciamento de Backup e Migração</h1>

        <?php if ($mensagem): ?>
            <div class="alert alert-<?php echo $tipo_mensagem; ?>">
                <?php echo $mensagem; ?>
            </div>
        <?php endif; ?>

        <!-- Seção de Backup -->
        <div class="section">
            <h2><i class="fas fa-download"></i> Backup do Sistema</h2>
            <div class="backup-actions">
                <form method="POST" class="inline-form" onsubmit="return confirm('Confirma a realização do backup completo?');">
                    <button type="submit" name="realizar_backup" class="btn btn-primary">
                        <i class="fas fa-download"></i> Realizar Backup Completo
                    </button>
                </form>

                <form method="POST" class="inline-form" onsubmit="return confirm('Confirma a realização do backup apenas da estrutura?');">
                    <button type="submit" name="realizar_backup_estrutura" class="btn btn-secondary">
                        <i class="fas fa-code"></i> Backup Estrutura (pg_dump)
                    </button>
                </form>

                <form method="POST" action="backup_estrutura_php.php" class="inline-form" onsubmit="return confirm('Confirma a extração da estrutura usando PHP?');">
                    <button type="submit" name="extrair_estrutura" class="btn btn-secondary" style="background: #6f42c1;">
                        <i class="fas fa-database"></i> Extrair Estrutura (PHP)
                    </button>
                </form>
            </div>

            <h3>Backups Disponíveis</h3>
            <table>
                <thead>
                    <tr>
                        <th>Nome do Arquivo</th>
                        <th>Data</th>
                        <th>Tamanho</th>
                        <th>Ações</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($backups as $backup): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($backup['nome']); ?></td>
                            <td><?php echo $backup['data']; ?></td>
                            <td><?php echo $backup['tamanho']; ?></td>
                            <td>
                                <form method="POST" style="display: inline;" 
                                      onsubmit="return confirm('Tem certeza que deseja excluir este backup?');">
                                    <input type="hidden" name="arquivo" value="<?php echo htmlspecialchars($backup['nome']); ?>">
                                    <button type="submit" name="excluir_backup" class="btn btn-danger">
                                        <i class="fas fa-trash"></i> Excluir
                                    </button>
                                </form>
                                <a href="<?php echo htmlspecialchars($backup['caminho']); ?>" 
                                   class="btn btn-secondary" download>
                                    <i class="fas fa-download"></i> Download
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- Seção de Migração -->
        <div class="section">
            <h2><i class="fas fa-exchange-alt"></i> Migração do Sistema</h2>
            
            <!-- Formulário de Migração da Estrutura -->
            <form method="POST" onsubmit="return confirm('Confirma a migração da estrutura?');" class="migration-form">
                <h3>Migrar Apenas Estrutura</h3>
                <div class="form-group">
                    <label>Host:</label>
                    <input type="text" name="host" required>
                </div>
                <div class="form-group">
                    <label>Porta:</label>
                    <input type="text" name="porta" value="5432" required>
                </div>
                <div class="form-group">
                    <label>Usuário:</label>
                    <input type="text" name="usuario" required>
                </div>
                <div class="form-group">
                    <label>Senha:</label>
                    <input type="password" name="senha" required>
                </div>
                <div class="form-group">
                    <label>Banco de Dados:</label>
                    <input type="text" name="banco" required>
                </div>
                <button type="submit" name="migrar_estrutura" class="btn btn-primary">
                    <i class="fas fa-code"></i> Migrar Estrutura
                </button>
            </form>

            <!-- Formulário de Migração Completa -->
            <form method="POST" onsubmit="return confirm('Confirma a migração completa?');" class="migration-form">
                <h3>Migrar Sistema Completo</h3>
                <div class="form-group">
                    <label>Host:</label>
                    <input type="text" name="host" required>
                </div>
                <div class="form-group">
                    <label>Porta:</label>
                    <input type="text" name="porta" value="5432" required>
                </div>
                <div class="form-group">
                    <label>Usuário:</label>
                    <input type="text" name="usuario" required>
                </div>
                <div class="form-group">
                    <label>Senha:</label>
                    <input type="password" name="senha" required>
                </div>
                <div class="form-group">
                    <label>Banco de Dados:</label>
                    <input type="text" name="banco" required>
                </div>
                <button type="submit" name="migrar_completo" class="btn btn-primary">
                    <i class="fas fa-copy"></i> Migrar Completo
                </button>
            </form>
        </div>
    </div>

    <script>
        // Adicionar confirmação para downloads grandes
        document.querySelectorAll('a[download]').forEach(link => {
            link.addEventListener('click', (e) => {
                if (!confirm('Confirma o download deste backup?')) {
                    e.preventDefault();
                }
            });
        });

        // Adicionar loading state nos botões de migração
        document.querySelectorAll('.migration-form').forEach(form => {
            form.addEventListener('submit', function() {
                if (confirm(this.getAttribute('onsubmit').slice(14, -1))) {
                    const button = this.querySelector('button');
                    button.disabled = true;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processando...';
                }
            });
        });
    </script>
</body>
</html>











