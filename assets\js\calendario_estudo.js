//calendario_estudo.js

document.addEventListener('DOMContentLoaded', function() {
    var calendarEl = document.getElementById('calendario_estudo');
    var calendar = new FullCalendar.Calendar(calendarEl, {
        headerToolbar: {
            start: 'today prev,next',
            center: 'title',
            end: 'dayGridMonth timeGridWeek timeGridDay',
        },
        eventDidMount: function(info) {
            info.el.style.borderWidth = '3px';
        },

        eventMouseEnter: function(info) {
            info.el.style.cursor = 'pointer';
            $(info.el).css('transform', 'scale(1.02)'); // Efeito sutil de hover
            $(info.el).css('transition', 'transform 0.2s ease'); // Suaviza a transição
        },

        locale: 'pt-br',
        editable: false,
        selectable: false,
        businessHours: true,
        dayMaxEvents: true,
        eventDisplay: 'block',
        displayEventTime: true,
        events: 'listareventos_POST.php',
        eventContent: function(info) {
            return {
                html: `
                    <div class="fc-event-title">${info.event.title}</div>
                    <div class="fc-event-description">${info.event.extendedProps.hora_liquida}</div>
                `
            };
        },

        eventClick: function(info) {
            let modalContent = `
                <div id="modal-estudo" class="modal-estudo ativo">
                    <div class="modal-overlay"></div>
                    <div class="modal-content">
                        <button class="modal-close">
                            <i class="fas fa-times"></i>
                        </button>
                        <div class="modal-body">
                            <div class="modal-header-estudo">
                                <h3>${info.event.title}</h3>
                                ${info.event.extendedProps.hora_liquida !== 'Planejamento' ? `
                                    <div class="tempo-total">
                                        <span class="tempo-label">Tempo Total:</span>
                                        <span class="tempo-valor">${info.event.extendedProps.tempo_liquido_horas}h${info.event.extendedProps.tempo_liquido_minutos}min</span>
                                    </div>
                                ` : ''}
                            </div>
                            <div class="estudos-container">
                                <div class="estudo-item" style="border-left: 4px solid ${info.event.backgroundColor || '#666'}">
                                    ${info.event.extendedProps.hora_liquida === 'Planejamento' ? `
                                        <div class="estudo-detalhes">
                                            <div class="detalhe-linha"><strong>Data de Início:</strong> ${info.event.extendedProps.inicio}</div>
                                        </div>
                                    ` : info.event.extendedProps.metodo === 'Simulado' || info.event.extendedProps.metodo === 'Questões' ? `
                                        <div class="estudo-cabecalho">
                                            <h4 class="materia-nome">${info.event.extendedProps.metodo}</h4>
                                            <div class="estudo-tempo">${info.event.extendedProps.tempo_liquido_horas}h${info.event.extendedProps.tempo_liquido_minutos}min</div>
                                        </div>
                                        <div class="estudo-detalhes">
                                            <div class="detalhe-linha"><strong>Início:</strong> ${info.event.extendedProps.tempo_inicio_horas}h${info.event.extendedProps.tempo_inicio_minutos}min</div>
                                            <div class="detalhe-linha"><strong>Fim:</strong> ${info.event.extendedProps.tempo_fim_horas}h${info.event.extendedProps.tempo_fim_minutos}min</div>
                                            <div class="detalhe-linha"><strong>Questões Totais:</strong> ${info.event.extendedProps.q_total}</div>
                                            <div class="detalhe-linha"><strong>Questões Certas:</strong> ${info.event.extendedProps.q_certa}</div>
                                            <div class="detalhe-linha"><strong>Questões Erradas:</strong> ${info.event.extendedProps.q_errada}</div>
                                            <div class="detalhe-linha ponto-estudado"><strong>Ponto Estudado:</strong> ${info.event.extendedProps.ponto_estudado}</div>
                                            ${info.event.extendedProps.descricao ? `
                                                <div class="detalhe-linha descricao"><strong>Observações:</strong> ${info.event.extendedProps.descricao}</div>
                                            ` : ''}
                                        </div>
                                    ` : `
                                        <div class="estudo-cabecalho">
                                            <h4 class="materia-nome">${info.event.title}</h4>
                                            <div class="estudo-tempo">${info.event.extendedProps.tempo_liquido_horas}h${info.event.extendedProps.tempo_liquido_minutos}min</div>
                                        </div>
                                        <div class="estudo-detalhes">
                                            <div class="detalhe-linha"><strong>Início:</strong> ${info.event.extendedProps.tempo_inicio_horas}h${info.event.extendedProps.tempo_inicio_minutos}min</div>
                                            <div class="detalhe-linha"><strong>Fim:</strong> ${info.event.extendedProps.tempo_fim_horas}h${info.event.extendedProps.tempo_fim_minutos}min</div>
                                            <div class="detalhe-linha"><strong>Tempo Perdido:</strong> ${info.event.extendedProps.tempo_perdido_horas}h${info.event.extendedProps.tempo_perdido_minutos}min</div>
                                            <div class="detalhe-linha"><strong>Método:</strong> ${info.event.extendedProps.metodo}</div>
                                            <div class="detalhe-linha"><strong>Curso:</strong> ${info.event.extendedProps.nome_curso}</div>
                                            <div class="detalhe-linha ponto-estudado"><strong>Ponto Estudado:</strong> ${info.event.extendedProps.ponto_estudado}</div>
                                            ${info.event.extendedProps.descricao ? `
                                                <div class="detalhe-linha descricao"><strong>Observações:</strong> ${info.event.extendedProps.descricao}</div>
                                            ` : ''}
                                        </div>
                                    `}
                                </div>
                            </div>
                            <button class="excluirEventoModal">Excluir Registro</button>
                        </div>
                    </div>
                </div>
            `;

            const modalElement = $(modalContent).appendTo('body');

            modalElement.find('.modal-close, .modal-overlay').on('click', function() {
                modalElement.remove();
            });

            modalElement.find('.excluirEventoModal').on('click', function() {
                Swal.fire({
                    title: 'Confirmar exclusão',
                    html: `
                        <div style="text-align: left; padding: 10px;">
                            <p><strong>Atenção!</strong> Ao excluir este Registro de Estudo:</p>
                            <ul style="list-style-type: none; padding-left: 10px;">
                                <li><i class="fas fa-sync-alt"></i> O sistema irá recalcular todos os dados</li>
                                <li><i class="fas fa-clock"></i> A página será recarregada completamente</li>
                                <li><i class="fas fa-chart-line"></i> As estatísticas serão atualizadas</li>
                            </ul>
                            <p style="margin-top: 10px;">Deseja continuar com a exclusão?</p>
                        </div>
                    `,
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Sim, excluir e recarregar',
                    cancelButtonText: 'Cancelar'
                }).then((result) => {
                    if (result.isConfirmed) {
                        const eventoId = info.event.id;
                        
                        // Mostrar loading
                        Swal.fire({
                            title: 'Excluindo e atualizando...',
                            html: `
                                <div style="text-align: left; padding: 10px;">
                                    <p><i class="fas fa-spinner fa-spin"></i> Excluindo o registro...</p>
                                    <p><i class="fas fa-calculator"></i> Recalculando dados...</p>
                                    <p><i class="fas fa-sync-alt"></i> Preparando para recarregar...</p>
                                </div>
                            `,
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });
                        
                        $.ajax({
                            url: 'dashboard-estudo_atualizar_estado_evento.php',
                            method: 'POST',
                            data: { eventoIdexcluir: eventoId },
                            dataType: 'json',
                            success: function(result) {
                                if (result.success) {
                                    modalElement.remove();
                                    
                                    // Recarregar a página index.php
                                    window.location.href = 'index.php';
                                } else {
                                    Swal.fire({
                                        icon: 'error',
                                        title: 'Erro',
                                        text: 'Não foi possível excluir o evento: ' + result.message
                                    });
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('Erro na requisição:', {
                                    status: status,
                                    error: error,
                                    response: xhr.responseText
                                });
                                Swal.fire({
                                    icon: 'error',
                                    title: 'Erro',
                                    text: 'Erro ao excluir o evento. Por favor, tente novamente.'
                                });
                            }
                        });
                    }
                });
            });
        },

        eventMouseLeave: function(info) {
            $(info.el).tooltip({
                title: info.event.extendedProps.ponto_estudado,
                placement: "top",
                trigger: "hover",
                html: true

            });
        },
    });

    calendar.render();
    calendar.updateSize();

    // Eventos da agenda
// Substitua a parte dos eventos da agenda no seu calendario_estudo.js por este código:


// Função para mostrar o modal com os detalhes do evento

});
