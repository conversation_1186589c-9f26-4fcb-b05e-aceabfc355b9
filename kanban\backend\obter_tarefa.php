<?php
session_start();
include_once '../../conexao_POST.php';

// Verificar se o usuário está logado
if (!isset($_SESSION['idusuario'])) {
    echo json_encode(['success' => false, 'erro' => 'Usuário não autenticado']);
    exit;
}

// Verificar se o ID da tarefa foi fornecido
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo json_encode(['success' => false, 'erro' => 'ID da tarefa não fornecido']);
    exit;
}

try {
    $usuario_id = $_SESSION['idusuario'];
    $tarefa_id = (int)$_GET['id'];
    
    // Buscar dados da tarefa
    $query = "SELECT id, titulo, descricao, status, prioridade, 
                    TO_CHAR(data_limite, 'YYYY-MM-DD') as data_limite,
                    TO_CHAR(data_criacao, 'DD/MM/YYYY') as data_criacao,
                    TO_CHAR(data_conclusao, 'DD/MM/YYYY') as data_conclusao
              FROM appEstudo.kanban_tarefas 
              WHERE id = $1 AND usuario_id = $2";
    
    $result = pg_query_params($conexao, $query, [$tarefa_id, $usuario_id]);
    
    if (!$result) {
        throw new Exception(pg_last_error($conexao));
    }
    
    if (pg_num_rows($result) === 0) {
        echo json_encode(['success' => false, 'erro' => 'Tarefa não encontrada']);
        exit;
    }
    
    $tarefa = pg_fetch_assoc($result);
    
    echo json_encode(['success' => true, 'tarefa' => $tarefa]);
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'erro' => $e->getMessage()]);
}
?>