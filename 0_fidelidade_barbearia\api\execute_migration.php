<?php
/**
 * Script de Migração Simplificado
 */

// Configurações do banco
$host = 'barbeiro_br.mysql.dbaas.com.br';
$db_name = 'barbeiro_br';
$username = 'barbeiro_br';
$password = 'Lucasb90#';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== EXECUTANDO MIGRAÇÃO PARA NOVO SISTEMA DE ID ===\n\n";
    
    // 1. Criar backups
    echo "💾 Criando backups...\n";
    $tables = ['usuarios', 'perfis_clientes', 'pontuacao_fidelidade', 'historico_atendimentos', 'brindes_pendentes', 'historico_brindes_entregues', 'recompensas_resgatadas', 'barbeiro_master'];
    
    foreach ($tables as $table) {
        $pdo->exec("DROP TABLE IF EXISTS {$table}_backup");
        $pdo->exec("CREATE TABLE {$table}_backup AS SELECT * FROM $table");
        echo "- Backup criado: {$table}_backup\n";
    }
    
    // 2. Iniciar transação
    echo "\n🔄 Iniciando migração...\n";
    $pdo->beginTransaction();
    
    // Desabilitar verificações de chave estrangeira
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    
    // 3. Criar tabela de mapeamento
    $pdo->exec("DROP TEMPORARY TABLE IF EXISTS id_mapping");
    $pdo->exec("CREATE TEMPORARY TABLE id_mapping (
        old_id VARCHAR(100),
        new_id VARCHAR(100),
        cpf VARCHAR(11),
        tipo ENUM('cliente', 'barbeiro'),
        PRIMARY KEY (old_id),
        UNIQUE KEY (new_id)
    )");
    
    // 4. Popular tabela de mapeamento
    $pdo->exec("INSERT INTO id_mapping (old_id, new_id, cpf, tipo)
                SELECT 
                    id as old_id,
                    CONCAT(cpf, '-', UPPER(tipo)) as new_id,
                    cpf,
                    tipo
                FROM usuarios
                WHERE id NOT REGEXP '^[0-9]{11}-(CLIENTE|BARBEIRO)$'");
    
    // Verificar mapeamento
    $stmt = $pdo->query("SELECT * FROM id_mapping");
    $mappings = $stmt->fetchAll();
    
    echo "📋 Mapeamento de IDs:\n";
    foreach ($mappings as $map) {
        echo "- {$map['old_id']} → {$map['new_id']}\n";
    }
    
    // 5. Atualizar tabelas
    echo "\n🔧 Atualizando tabelas...\n";
    
    // Usuarios
    $affected = $pdo->exec("UPDATE usuarios u
                           JOIN id_mapping m ON u.id = m.old_id
                           SET u.id = m.new_id");
    echo "- usuarios: $affected registros atualizados\n";
    
    // Perfis clientes
    $affected = $pdo->exec("UPDATE perfis_clientes pc
                           JOIN id_mapping m ON pc.usuario_id = m.old_id
                           SET pc.usuario_id = m.new_id");
    echo "- perfis_clientes: $affected registros atualizados\n";
    
    // Pontuação fidelidade
    $affected = $pdo->exec("UPDATE pontuacao_fidelidade pf
                           JOIN id_mapping m ON pf.cliente_id = m.old_id
                           SET pf.cliente_id = m.new_id");
    echo "- pontuacao_fidelidade: $affected registros atualizados\n";
    
    // Histórico atendimentos
    $affected = $pdo->exec("UPDATE historico_atendimentos ha
                           JOIN id_mapping m ON ha.cliente_id = m.old_id
                           SET ha.cliente_id = m.new_id");
    echo "- historico_atendimentos: $affected registros atualizados\n";
    
    // Brindes pendentes
    $affected = $pdo->exec("UPDATE brindes_pendentes bp
                           JOIN id_mapping m ON bp.cliente_id = m.old_id
                           SET bp.cliente_id = m.new_id");
    echo "- brindes_pendentes: $affected registros atualizados\n";
    
    // Brindes entregues
    $affected = $pdo->exec("UPDATE historico_brindes_entregues hbe
                           JOIN id_mapping m ON hbe.cliente_id = m.old_id
                           SET hbe.cliente_id = m.new_id");
    echo "- historico_brindes_entregues: $affected registros atualizados\n";
    
    // Recompensas resgatadas
    $affected = $pdo->exec("UPDATE recompensas_resgatadas rr
                           JOIN id_mapping m ON rr.cliente_id = m.old_id
                           SET rr.cliente_id = m.new_id");
    echo "- recompensas_resgatadas: $affected registros atualizados\n";
    
    // Barbeiro master
    $affected = $pdo->exec("UPDATE barbeiro_master bm
                           JOIN id_mapping m ON bm.dono_id = m.old_id
                           SET bm.dono_id = m.new_id");
    echo "- barbeiro_master: $affected registros atualizados\n";
    
    // Reabilitar verificações de chave estrangeira
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    
    // 6. Verificar resultado
    echo "\n🔍 Verificando resultado...\n";
    
    // Verificar formato dos IDs
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM usuarios WHERE id REGEXP '^[0-9]{11}-(CLIENTE|BARBEIRO)$'");
    $correctFormat = $stmt->fetch()['total'];
    
    $stmt = $pdo->query("SELECT COUNT(*) as total FROM usuarios");
    $totalUsers = $stmt->fetch()['total'];
    
    echo "- Usuários com formato correto: $correctFormat/$totalUsers\n";
    
    // Verificar IDs duplicados
    $stmt = $pdo->query("SELECT id, COUNT(*) as count FROM usuarios GROUP BY id HAVING COUNT(*) > 1");
    $duplicates = $stmt->fetchAll();
    
    if (empty($duplicates)) {
        echo "- Nenhum ID duplicado encontrado ✅\n";
    } else {
        echo "- ❌ IDs duplicados encontrados:\n";
        foreach ($duplicates as $dup) {
            echo "  ID: {$dup['id']}, Count: {$dup['count']}\n";
        }
        throw new Exception("IDs duplicados encontrados!");
    }
    
    // Mostrar resultado final
    echo "\n📊 RESULTADO FINAL:\n";
    $stmt = $pdo->query("SELECT id, cpf, nome, tipo FROM usuarios ORDER BY tipo, nome");
    $users = $stmt->fetchAll();
    
    foreach ($users as $user) {
        echo "- {$user['nome']} ({$user['tipo']}): {$user['id']} ✅\n";
    }
    
    // Confirmar transação
    $pdo->commit();
    
    echo "\n✅ MIGRAÇÃO CONCLUÍDA COM SUCESSO!\n";
    echo "🎉 Todos os IDs foram migrados para o novo formato!\n";
    
} catch (Exception $e) {
    if (isset($pdo)) {
        $pdo->rollback();
    }
    echo "❌ ERRO NA MIGRAÇÃO: " . $e->getMessage() . "\n";
    echo "🔄 Transação revertida. Dados não foram alterados.\n";
}
?>
