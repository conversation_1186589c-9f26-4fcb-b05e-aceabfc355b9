<?php
// api/medicamentos.php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

require_once '../includes/functions.php';
require_once '../includes/medicamento.php';
require_once '../includes/registro.php';
require_once '../config/database.php';

// Instanciar objetos
$medicamento = new Medicamento();
$registro = new Registro();

// Verificar se a classe Database está disponível
if (!class_exists('Database')) {
    require_once '../config/database.php';
}

// Método HTTP
$method = $_SERVER['REQUEST_METHOD'];

// Verificar parâmetro de rota
$route = isset($_GET['route']) ? $_GET['route'] : '';

// Obter ID da URL se fornecido
$id = null;
if (isset($_GET['id'])) {
    $id = intval($_GET['id']);
}

// Verificar autenticação
$headers = getallheaders();
$auth_header = isset($headers['Authorization']) ? $headers['Authorization'] : '';

// Se a rota exigir autenticação, remover esta verificação ou implementar sua lógica de autenticação
// if (empty($auth_header) || !preg_match('/Bearer\s+(.*)/', $auth_header, $matches)) {
//     http_response_code(401);
//     echo json_encode(['success' => false, 'message' => 'Token de autenticação não fornecido']);
//     exit;
// }

// Processar requisição com base no método HTTP
switch ($method) {
    case 'GET':
        // Rotas específicas primeiro
        if ($route === 'pendentes') {
            // Buscar medicamentos com doses pendentes (não confirmadas e hora já passou)
            $query = "SELECT m.id, m.nome, m.dosagem, m.intervalo_horas, m.data_inicio, 
                    m.dias_tratamento, m.horario_inicial, m.observacoes, m.status,
                    r.data_hora as proximo_horario, r.id as registro_id
              FROM medicamentos m
              INNER JOIN registros_uso r ON m.id = r.medicamento_id
              WHERE m.status = true 
              AND r.confirmado = false 
              AND r.data_hora <= NOW() 
              ORDER BY r.data_hora ASC";

            $database = new Database();
            $conn = $database->getConnection();
            $stmt = $conn->prepare($query);
            $stmt->execute();

            $medicamentos = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $medicamentos[] = [
                    'id' => $row['id'],
                    'nome' => $row['nome'],
                    'dosagem' => $row['dosagem'],
                    'intervalo_horas' => $row['intervalo_horas'],
                    'data_inicio' => $row['data_inicio'],
                    'dias_tratamento' => $row['dias_tratamento'],
                    'horario_inicial' => $row['horario_inicial'],
                    'observacoes' => $row['observacoes'],
                    'status' => $row['status'],
                    'proximo_horario' => $row['proximo_horario'],
                    'registro_id' => $row['registro_id']
                ];
            }

            echo json_encode([
                'success' => true,
                'message' => 'Medicamentos pendentes obtidos com sucesso',
                'data' => $medicamentos
            ]);
            exit; // Adicionado para garantir que não continue a execução

            echo json_encode([
                'success' => true,
                'message' => 'Medicamentos pendentes obtidos com sucesso',
                'data' => $medicamentos
            ]);
        }
        else if ($route === 'proximos') {
            // Buscar próximos medicamentos a serem tomados (não confirmados e hora ainda não passou)
            $query = "SELECT m.id, m.nome, m.dosagem, m.intervalo_horas, m.data_inicio, 
                            m.dias_tratamento, m.horario_inicial, m.observacoes, m.status,
                            r.data_hora as proximo_horario, r.id as registro_id
                      FROM medicamentos m
                      JOIN registros_uso r ON m.id = r.medicamento_id
                      WHERE m.status = true 
                      AND r.confirmado = false 
                      AND r.data_hora > NOW() 
                      ORDER BY r.data_hora ASC";

            $database = new Database();
            $conn = $database->getConnection();
            $stmt = $conn->prepare($query);
            $stmt->execute();

            $medicamentos = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $medicamentos[] = [
                    'id' => $row['id'],
                    'nome' => $row['nome'],
                    'dosagem' => $row['dosagem'],
                    'intervalo_horas' => $row['intervalo_horas'],
                    'data_inicio' => $row['data_inicio'],
                    'dias_tratamento' => $row['dias_tratamento'],
                    'horario_inicial' => $row['horario_inicial'],
                    'observacoes' => $row['observacoes'],
                    'status' => $row['status'],
                    'proximo_horario' => $row['proximo_horario'],
                    'registro_id' => $row['registro_id']
                ];
            }

            echo json_encode([
                'success' => true,
                'message' => 'Próximos medicamentos obtidos com sucesso',
                'data' => $medicamentos
            ]);
        }
        else if ($route === 'medicamentos/buscar') {
            // Implementar busca com filtros
            $query = isset($_GET['q']) ? $_GET['q'] : '';
            $status = isset($_GET['status']) ? $_GET['status'] : '';
            $dataInicio = isset($_GET['data_inicio']) ? $_GET['data_inicio'] : '';
            $ordenacao = isset($_GET['ordenacao']) ? $_GET['ordenacao'] : '';

            // Implementar lógica de filtro e ordenação
            $stmt = $medicamento->listar();
            $medicamentos = [];

            while ($row = $stmt->fetch()) {
                // Implemente aqui a lógica de filtro com base nos parâmetros
                // Esta é uma implementação simplificada, ajuste conforme necessário
                $adicionar = true;

                if (!empty($query) && stripos($row['nome'], $query) === false) {
                    $adicionar = false;
                }

                if (!empty($status) && $row['status'] !== $status) {
                    $adicionar = false;
                }

                if (!empty($dataInicio) && $row['data_inicio'] !== $dataInicio) {
                    $adicionar = false;
                }

                if ($adicionar) {
                    $medicamentos[] = [
                        'id' => $row['id'],
                        'nome' => $row['nome'],
                        'dosagem' => $row['dosagem'],
                        'intervalo_horas' => $row['intervalo_horas'],
                        'data_inicio' => $row['data_inicio'],
                        'dias_tratamento' => $row['dias_tratamento'],
                        'horario_inicial' => $row['horario_inicial'],
                        'observacoes' => $row['observacoes'],
                        'status' => $row['status']
                    ];
                }
            }

            // Ordenação
            if (!empty($ordenacao)) {
                usort($medicamentos, function($a, $b) use ($ordenacao) {
                    switch ($ordenacao) {
                        case 'nome_asc':
                            return strcmp($a['nome'], $b['nome']);
                        case 'nome_desc':
                            return strcmp($b['nome'], $a['nome']);
                        case 'data_inicio_asc':
                            return strcmp($a['data_inicio'], $b['data_inicio']);
                        case 'data_inicio_desc':
                            return strcmp($b['data_inicio'], $a['data_inicio']);
                        default:
                            return 0;
                    }
                });
            }

            echo json_encode([
                'success' => true,
                'message' => 'Busca realizada com sucesso',
                'data' => $medicamentos
            ]);
        }
        // Dentro do switch case no arquivo medicamentos.php, adicione este case:
        else if ($route === 'listar_todos') {
            // Buscar TODOS os medicamentos ativos, sem filtros de registros
            $query = "SELECT m.id, m.nome, m.dosagem, m.intervalo_horas, m.data_inicio, 
                    m.dias_tratamento, m.horario_inicial, m.observacoes, m.status
              FROM medicamentos m
              WHERE m.status = true 
              ORDER BY m.nome ASC";

            $database = new Database();
            $conn = $database->getConnection();
            $stmt = $conn->prepare($query);
            $stmt->execute();

            $medicamentos = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $medicamentos[] = [
                    'id' => $row['id'],
                    'nome' => $row['nome'],
                    'dosagem' => $row['dosagem'],
                    'intervalo_horas' => $row['intervalo_horas'],
                    'data_inicio' => $row['data_inicio'],
                    'dias_tratamento' => $row['dias_tratamento'],
                    'horario_inicial' => $row['horario_inicial'],
                    'observacoes' => $row['observacoes'],
                    'status' => $row['status'],
                    'proximo_horario' => null, // Como estamos listando todos, alguns podem não ter registro
                    'registro_id' => null
                ];
            }

            echo json_encode([
                'success' => true,
                'message' => 'Todos os medicamentos obtidos com sucesso',
                'data' => $medicamentos
            ]);
        }

        else if ($route === 'medicamentos') {
            // Listar todos os medicamentos com próximo horário
            $query = "SELECT m.id, m.nome, m.dosagem, m.intervalo_horas, m.data_inicio, 
                           m.dias_tratamento, m.horario_inicial, m.observacoes, m.status,
                           MIN(r.data_hora) as proximo_horario, MIN(r.id) as registro_id
                     FROM medicamentos m
                     LEFT JOIN registros_uso r ON m.id = r.medicamento_id
                     WHERE m.status = true AND (r.confirmado = false OR r.confirmado IS NULL)
                     GROUP BY m.id
                     ORDER BY m.nome";

            $database = new Database();
            $conn = $database->getConnection();
            $stmt = $conn->prepare($query);
            $stmt->execute();

            $medicamentos = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $medicamentos[] = [
                    'id' => $row['id'],
                    'nome' => $row['nome'],
                    'dosagem' => $row['dosagem'],
                    'intervalo_horas' => $row['intervalo_horas'],
                    'data_inicio' => $row['data_inicio'],
                    'dias_tratamento' => $row['dias_tratamento'],
                    'horario_inicial' => $row['horario_inicial'],
                    'observacoes' => $row['observacoes'],
                    'status' => $row['status'],
                    'proximo_horario' => $row['proximo_horario'],
                    'registro_id' => $row['registro_id']
                ];
            }

            echo json_encode([
                'success' => true,
                'message' => 'Lista de medicamentos obtida com sucesso',
                'data' => $medicamentos
            ]);
        }
        else if ($route === 'medicamento' && $id) {
            // Obter medicamento específico
            if ($medicamento->obter($id)) {
                // Obter registros do medicamento
                $historicoRegistros = [];

                // Instanciar objeto de registro se ainda não existir
                if (!isset($registro)) {
                    $registro = new Registro();
                }

                // Buscar registros do medicamento
                $stmtRegistros = $registro->listarPorMedicamento($id);

                while ($rowRegistro = $stmtRegistros->fetch(PDO::FETCH_ASSOC)) {
                    $historicoRegistros[] = [
                        'id' => $rowRegistro['id'],
                        'data_hora' => $rowRegistro['data_hora'],
                        'confirmado' => $rowRegistro['confirmado'] ? true : false,
                        'status' => $rowRegistro['confirmado'] ? 'Confirmado' : 'Pendente',
                        'observacao' => $rowRegistro['observacao']
                    ];
                }

                echo json_encode([
                    'success' => true,
                    'message' => 'Medicamento obtido com sucesso',
                    'data' => [
                        'id' => $medicamento->id,
                        'nome' => $medicamento->nome,
                        'dosagem' => $medicamento->dosagem,
                        'intervalo_horas' => $medicamento->intervalo_horas,
                        'data_inicio' => $medicamento->data_inicio,
                        'dias_tratamento' => $medicamento->dias_tratamento,
                        'horario_inicial' => $medicamento->horario_inicial,
                        'observacoes' => $medicamento->observacoes,
                        'status' => $medicamento->status,
                        'historico' => $historicoRegistros
                    ]
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Medicamento não encontrado'
                ]);
            }
        }
        else if ($id) {
            // Para compatibilidade com o código anterior
            if ($medicamento->obter($id)) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Medicamento obtido com sucesso',
                    'data' => [
                        'id' => $medicamento->id,
                        'nome' => $medicamento->nome,
                        'dosagem' => $medicamento->dosagem,
                        'intervalo_horas' => $medicamento->intervalo_horas,
                        'data_inicio' => $medicamento->data_inicio,
                        'dias_tratamento' => $medicamento->dias_tratamento,
                        'horario_inicial' => $medicamento->horario_inicial,
                        'observacoes' => $medicamento->observacoes,
                        'status' => $medicamento->status
                    ]
                ]);
            } else {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Medicamento não encontrado'
                ]);
            }
        }
        else {
            // Comportamento padrão (como estava antes)
            $stmt = $medicamento->listar();
            $medicamentos = [];

            while ($row = $stmt->fetch()) {
                $medicamentos[] = [
                    'id' => $row['id'],
                    'nome' => $row['nome'],
                    'dosagem' => $row['dosagem'],
                    'intervalo_horas' => $row['intervalo_horas'],
                    'data_inicio' => $row['data_inicio'],
                    'dias_tratamento' => $row['dias_tratamento'],
                    'horario_inicial' => $row['horario_inicial'],
                    'observacoes' => $row['observacoes'],
                    'status' => $row['status']
                ];
            }

            echo json_encode([
                'success' => true,
                'message' => 'Lista de medicamentos',
                'data' => $medicamentos
            ]);
        }
        break;

    case 'POST':
        if ($route === 'confirmar') {
            // Confirmar uso de medicamento
            $data = json_decode(file_get_contents('php://input'), true);

            if (!$data || !isset($data['registro_id'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID do registro não fornecido'
                ]);
                break;
            }

            $registro_id = intval($data['registro_id']);

            // Implementação real da confirmação de uso
            $query = "UPDATE registros_uso SET confirmado = true, data_confirmacao = NOW() WHERE id = :registro_id";
            $database = new Database();
            $conn = $database->getConnection();
            $stmt = $conn->prepare($query);
            $stmt->bindParam(":registro_id", $registro_id);
            $resultado = $stmt->execute();

            if ($resultado) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Uso confirmado com sucesso'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erro ao confirmar uso do medicamento'
                ]);
            }
        }
        else if ($route === 'adicionar_medicamento') {
            // Adicionar novo medicamento
            $data = json_decode(file_get_contents('php://input'), true);

            if (!$data) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Dados inválidos'
                ]);
                break;
            }

            // Definir valores
            $medicamento->nome = $data['nome'] ?? '';
            $medicamento->dosagem = $data['dosagem'] ?? '';
            $medicamento->intervalo_horas = $data['intervalo_horas'] ?? 24;
            $medicamento->data_inicio = $data['data_inicio'] ?? date('Y-m-d');
            $medicamento->dias_tratamento = $data['dias_tratamento'] ?? 7;
            $medicamento->horario_inicial = $data['horario_inicial'] ?? date('H:i:s');
            $medicamento->observacoes = $data['observacoes'] ?? null;

            // Validação básica
            if (empty($medicamento->nome) || empty($medicamento->dosagem)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Nome e dosagem são obrigatórios'
                ]);
                break;
            }

            // Adicionar medicamento
            if ($medicamento->adicionar()) {
                http_response_code(201);
                echo json_encode([
                    'success' => true,
                    'message' => 'Medicamento adicionado com sucesso',
                    'data' => ['id' => $medicamento->id]
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erro ao adicionar medicamento'
                ]);
            }
        }
        else {
            // Comportamento padrão (como estava antes)
            $data = json_decode(file_get_contents('php://input'), true);

            if (!$data) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Dados inválidos'
                ]);
                break;
            }

            // Definir valores
            $medicamento->nome = $data['nome'] ?? '';
            $medicamento->dosagem = $data['dosagem'] ?? '';
            $medicamento->intervalo_horas = $data['intervalo_horas'] ?? 24;
            $medicamento->data_inicio = $data['data_inicio'] ?? date('Y-m-d');
            $medicamento->dias_tratamento = $data['dias_tratamento'] ?? 7;
            $medicamento->horario_inicial = $data['horario_inicial'] ?? date('H:i:s');
            $medicamento->observacoes = $data['observacoes'] ?? null;

            // Validação básica
            if (empty($medicamento->nome) || empty($medicamento->dosagem)) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Nome e dosagem são obrigatórios'
                ]);
                break;
            }

            // Adicionar medicamento
            if ($medicamento->adicionar()) {
                http_response_code(201);
                echo json_encode([
                    'success' => true,
                    'message' => 'Medicamento adicionado com sucesso',
                    'data' => ['id' => $medicamento->id]
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erro ao adicionar medicamento'
                ]);
            }
        }
        break;

    case 'PUT':
        if ($route === 'medicamento') {
            // Atualizar medicamento existente
            $data = json_decode(file_get_contents('php://input'), true);

            if (!$data || !isset($data['id'])) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'ID não fornecido'
                ]);
                break;
            }

            $id = intval($data['id']);

            // Obter dados do medicamento
            if (!$medicamento->obter($id)) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Medicamento não encontrado'
                ]);
                break;
            }

            // Atualizar valores
            $medicamento->nome = $data['nome'] ?? $medicamento->nome;
            $medicamento->dosagem = $data['dosagem'] ?? $medicamento->dosagem;
            $medicamento->intervalo_horas = $data['intervalo_horas'] ?? $medicamento->intervalo_horas;
            $medicamento->data_inicio = $data['data_inicio'] ?? $medicamento->data_inicio;
            $medicamento->dias_tratamento = $data['dias_tratamento'] ?? $medicamento->dias_tratamento;
            $medicamento->horario_inicial = $data['horario_inicial'] ?? $medicamento->horario_inicial;
            $medicamento->observacoes = $data['observacoes'] ?? $medicamento->observacoes;

            // Atualizar medicamento
            if ($medicamento->atualizar()) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Medicamento atualizado com sucesso'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erro ao atualizar medicamento'
                ]);
            }
        }
        else if ($id) {
            // Comportamento padrão (como estava antes)
            // Obter dados do medicamento
            if (!$medicamento->obter($id)) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Medicamento não encontrado'
                ]);
                break;
            }

            $data = json_decode(file_get_contents('php://input'), true);

            if (!$data) {
                http_response_code(400);
                echo json_encode([
                    'success' => false,
                    'message' => 'Dados inválidos'
                ]);
                break;
            }

            // Atualizar valores
            $medicamento->nome = $data['nome'] ?? $medicamento->nome;
            $medicamento->dosagem = $data['dosagem'] ?? $medicamento->dosagem;
            $medicamento->intervalo_horas = $data['intervalo_horas'] ?? $medicamento->intervalo_horas;
            $medicamento->data_inicio = $data['data_inicio'] ?? $medicamento->data_inicio;
            $medicamento->dias_tratamento = $data['dias_tratamento'] ?? $medicamento->dias_tratamento;
            $medicamento->horario_inicial = $data['horario_inicial'] ?? $medicamento->horario_inicial;
            $medicamento->observacoes = $data['observacoes'] ?? $medicamento->observacoes;

            // Atualizar medicamento
            if ($medicamento->atualizar()) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Medicamento atualizado com sucesso'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erro ao atualizar medicamento'
                ]);
            }
        }
        else {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'ID não fornecido'
            ]);
        }
        break;

    case 'DELETE':
        if ($route === 'medicamento' && $id) {
            // Obter dados do medicamento
            if (!$medicamento->obter($id)) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Medicamento não encontrado'
                ]);
                break;
            }

            // Desativar medicamento
            if ($medicamento->desativar()) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Medicamento desativado com sucesso'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erro ao desativar medicamento'
                ]);
            }
        }
        else if ($id) {
            // Comportamento padrão (como estava antes)
            // Obter dados do medicamento
            if (!$medicamento->obter($id)) {
                http_response_code(404);
                echo json_encode([
                    'success' => false,
                    'message' => 'Medicamento não encontrado'
                ]);
                break;
            }

            // Desativar medicamento
            if ($medicamento->desativar()) {
                echo json_encode([
                    'success' => true,
                    'message' => 'Medicamento desativado com sucesso'
                ]);
            } else {
                http_response_code(500);
                echo json_encode([
                    'success' => false,
                    'message' => 'Erro ao desativar medicamento'
                ]);
            }
        }
        else {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'ID não fornecido'
            ]);
        }
        break;



    default:
        http_response_code(405);
        echo json_encode([
            'success' => false,
            'message' => 'Método não permitido'
        ]);
        break;
}
?>