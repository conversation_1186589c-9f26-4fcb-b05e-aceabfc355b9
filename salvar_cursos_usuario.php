<?php
session_start();
include_once("conexao_POST.php");

if (!$conexao) {
    die("Erro na conexão: " . pg_last_error());
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $id_usuario = $_SESSION['idusuario'];
    $cursos = $_POST['cursos'];

    // Remover cursos antigos do usuário
    $query_remover_cursos = "DELETE FROM appEstudo.usuario_has_curso WHERE usuario_idusuario = $id_usuario";
    $resultado_remover = pg_query($conexao, $query_remover_cursos);

    $sucesso = true;
    // Adicionar cursos selecionados
    if (!empty($cursos)) {
        foreach ($cursos as $curso_id) {
            $query_adicionar_cursos = "INSERT INTO appEstudo.usuario_has_curso (usuario_idusuario, curso_idcurso) VALUES ($id_usuario, $curso_id)";
            $resultado = pg_query($conexao, $query_adicionar_cursos);
            if (!$resultado) {
                $sucesso = false;
                break;
            }
        }
    }

    if ($sucesso) {
        ?>
        <!DOCTYPE html>
        <html lang="pt-br">
        <head>
            <meta charset="UTF-8">
            <title>Sucesso</title>
            <style>
                :root {
                    --parchment: #f8f0e3;
                    --vintage-gold: #b8860b;
                    --burgundy: #800020;
                    --gold-accent: #daa520;
                    --shadow-color: rgba(0, 0, 0, 0.2);
                    --elegant-border: linear-gradient(45deg, var(--vintage-gold), #ffd700, var(--vintage-gold));
                }

                body {
                    margin: 0;
                    padding: 0;
                    font-family: 'Quicksand', sans-serif;
                    background: rgba(0, 0, 0, 0.5);
                }

                .modal-overlay {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 1000;
                    backdrop-filter: blur(3px);
                }

                .modal-content {
                    background: var(--hover);
                    padding: 2rem;
                    border: 1px solid var(--vintage-gold);
                    box-shadow:
                            0 0 0 1px var(--vintage-gold),
                            0 0 0 10px var(--parchment),
                            0 0 0 11px var(--vintage-gold),
                            0 2px 5px 11px var(--shadow-color);
                    max-width: 400px;
                    width: 90%;
                    position: relative;
                    animation: slideIn 0.3s ease-out;
                }

                .modal-corner {
                    position: absolute;
                    width: 20px;
                    height: 20px;
                    border: 1px solid var(--vintage-gold);
                    pointer-events: none;
                }

                .modal-corner-tl { top: 10px; left: 10px; border-right: 0; border-bottom: 0; }
                .modal-corner-tr { top: 10px; right: 10px; border-left: 0; border-bottom: 0; }
                .modal-corner-bl { bottom: 10px; left: 10px; border-right: 0; border-top: 0; }
                .modal-corner-br { bottom: 10px; right: 10px; border-left: 0; border-top: 0; }

                .modal-title {
                    font-family: 'Cinzel', serif;
                    color: var(--burgundy);
                    text-align: center;
                    font-size: 1.5rem;
                    margin-bottom: 1.5rem;
                    padding-bottom: 1rem;
                    position: relative;
                }

                .modal-title::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 100px;
                    height: 2px;
                    background: var(--elegant-border);
                }

                .modal-message {
                    text-align: center;
                    font-size: 1.1rem;
                    margin-bottom: 2rem;
                    line-height: 1.6;
                }

                .btn-close {
                    font-family: 'Cinzel', serif;
                    background: transparent;
                    border: 2px solid #8B0000;
                    color: #8B0000;
                    padding: 1rem 2rem;
                    font-size: 1.1rem;
                    text-transform: uppercase;
                    letter-spacing: 2px;
                    cursor: pointer;
                    position: relative;
                    overflow: hidden;
                    z-index: 1;
                    transition: all 0.3s ease;
                }

                .btn-close:hover {
                    background: #8B0000;
                    color: white;
                }

                @keyframes slideIn {
                    from { transform: translateY(-20px); opacity: 0; }
                    to { transform: translateY(0); opacity: 1; }
                }
            </style>
        </head>
        <body>
        <div class="modal-overlay">
            <div class="modal-content">
                <div class="modal-corner modal-corner-tl"></div>
                <div class="modal-corner modal-corner-tr"></div>
                <div class="modal-corner modal-corner-bl"></div>
                <div class="modal-corner modal-corner-br"></div>

                <h3 class="modal-title">Sucesso!</h3>
                <p class="modal-message">Os cursos foram salvos com sucesso.</p>

                <div style="text-align: center;">
                    <button class="btn-close" onclick="redirecionarParaEdicao()">Fechar</button>
                </div>
            </div>
        </div>

        <script>
            function redirecionarParaEdicao() {
                // Atualiza a página principal se existir
                if (window.opener) {
                    window.opener.location.reload();
                }
                // Redireciona para a página de edição de cursos
                window.location.href = '0editar_curso.php';
            }
        </script>
        </body>
        </html>
        <?php
    } else {
        echo "<script> alert('Erro ao salvar os cursos.'); window.location.href = '0editar_curso.php'; </script>";
    }
}

pg_close($conexao);
?>