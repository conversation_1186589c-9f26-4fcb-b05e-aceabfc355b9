<?php
// Iniciar a sessão no início do arquivo
session_start();

// Definir o fuso horário para Brasil (São Paulo)
date_default_timezone_set('America/Sao_Paulo');

include_once("conexao_POST.php");

// Verificar se o usuário está logado
if (!isset($_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit;
}

if (!$conexao) {
    die("Erro na conexão: " . pg_last_error());
}

$id_usuario = $_SESSION['idusuario'];

// Após obter o ID do usuário, adicione a consulta para buscar o nome
$query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = $id_usuario";
$resultado_nome = pg_query($conexao, $query_buscar_nome);
$nome_usuario = ($resultado_nome && pg_num_rows($resultado_nome) > 0) 
    ? pg_fetch_assoc($resultado_nome)['nome'] 
    : "Usuário";

$query_contagem = "SELECT 
    idplanejamento
FROM 
    appEstudo.planejamento p
WHERE 
    usuario_idusuario = $id_usuario;
";

$result = pg_query($conexao, $query_contagem);

if ($result) {
    $row = pg_fetch_assoc($result);

    if ($row) {
        $id_planejamento = $row['idplanejamento'];
    } else {
        echo "Nenhum planejamento encontrado.";
        exit;
    }
} else {
    echo "Erro na consulta: " . pg_last_error($conexao);
    exit;
}

$data_atual = strtotime(date('Y-m-d'));
$intervalos = [
    '1' => [
        'descrição' => 'Primeira Revisão',
        'dias' => 1,
        'explicação' => 'Revisão intensiva para fortalecer o aprendizado recente. Com duração média de 10 minutos'
    ],
    '3' => [
        'descrição' => 'Segunda Revisão',
        'dias' => 3,
        'explicação' => 'Revisão para consolidar o conhecimento após alguns dias. Com duração máxima de 10 minutos'
    ],
    '7' => [
        'descrição' => 'Terceira Revisão',
        'dias' => 7,
        'explicação' => 'Revisão para manter a memória a longo prazo. Com duração máxima de 10 minutos'
    ],
    '14' => [
        'descrição' => 'Quarta Revisão',
        'dias' => 14,
        'explicação' => 'Revisão para garantir que o conteúdo seja lembrado após duas semanas. Com duração média de 5 minutos'
    ],
    '30' => [
        'descrição' => 'Quinta Revisão',
        'dias' => 30,
        'explicação' => 'Revisão para reforçar o conhecimento de forma espaçada e eficiente. Com duração média de 5 minutos'
    ]
];

$revisoes = [];
foreach ($intervalos as $dias => $detalhes) {
    $data_revisao = date('Y-m-d', strtotime("-$dias days", $data_atual));
    $revisoes[$data_revisao] = $detalhes;
}
$data_atual_teste = date('Y-m-d');

// Após a consulta atual, adicione uma consulta para revisões atrasadas
$data_atual_str = date('Y-m-d');

// Consulta para buscar revisões já concluídas hoje
$query_revisoes_concluidas = "
    SELECT 
        m.idmateria, 
        m.nome AS nome_materia, 
        m.cor AS cor_materia, 
        e.data AS data_estudo, 
        e.ponto_estudado, 
        e.metodo, 
        e.revisoes, 
        e.idestudos,
        e.revisao_concluida
    FROM 
        appEstudo.materia m
    INNER JOIN 
        appEstudo.estudos e ON e.materia_idmateria = m.idmateria
    WHERE 
        e.planejamento_usuario_idusuario = $id_usuario
        AND DATE(e.revisao_concluida) = CURRENT_DATE
    ORDER BY 
        e.revisao_concluida DESC
";

// Para debug, vamos imprimir a consulta e o resultado
error_log("Query revisões concluídas: " . $query_revisoes_concluidas);
$resultado_revisoes_concluidas = pg_query($conexao, $query_revisoes_concluidas);
error_log("Número de revisões concluídas encontradas: " . pg_num_rows($resultado_revisoes_concluidas));
$revisoes_concluidas = [];
$revisoes_concluidas_count = 0;

if ($resultado_revisoes_concluidas) {
    while ($revisao = pg_fetch_assoc($resultado_revisoes_concluidas)) {
        // Extrair a hora e minuto diretamente do timestamp
        $hora_revisao = substr($revisao['revisao_concluida'], 11, 5);
        
        error_log("Hora original: " . $revisao['revisao_concluida'] . ", Hora formatada: " . $hora_revisao);
        
        $revisoes_concluidas[] = array(
            'id' => $revisao['idmateria'],
            'nome' => $revisao['nome_materia'],
            'cor' => $revisao['cor_materia'],
            'data_estudo' => $revisao['data_estudo'],
            'ponto_estudado' => $revisao['ponto_estudado'],
            'idestudos' => $revisao['idestudos'],
            'hora_revisao' => $hora_revisao
        );
        $revisoes_concluidas_count++;
    }
}

error_log("Número de revisões concluídas após processamento: " . $revisoes_concluidas_count);

// Modificar a consulta para separar revisões do dia e atrasadas
$query_materias_revisao = "
    SELECT 
        m.idmateria, 
        m.nome AS nome_materia, 
        m.cor AS cor_materia, 
        e.data AS data_estudo, 
        e.ponto_estudado, 
        e.metodo, 
        e.revisoes, 
        e.idestudos,
        CASE 
            WHEN e.data IN ('" . implode("','", array_keys($revisoes)) . "') THEN false
            ELSE true 
        END AS atrasada
    FROM 
        appEstudo.materia m
    INNER JOIN 
        appEstudo.estudos e ON e.materia_idmateria = m.idmateria
    WHERE 
        e.planejamento_usuario_idusuario = $id_usuario
        AND (
            -- Revisões para hoje baseadas nos intervalos
            e.data IN ('" . implode("','", array_keys($revisoes)) . "')
            -- OU revisões atrasadas (data anterior que não foi revisada)
            OR (e.data < '$data_atual_str' AND (e.revisoes IS NULL OR e.revisoes < e.data))
        )
        AND e.metodo NOT IN ('Revisão', 'Questões', 'Simulado')
        AND (DATE(e.revisao_concluida) IS NULL OR DATE(e.revisao_concluida) != CURRENT_DATE)
";



$resultado_materias_revisao = pg_query($conexao, $query_materias_revisao);

$materias_por_revisao = [];
$revisoes_atrasadas = [];

if ($resultado_materias_revisao) {
    $ids_revisar = [];
    while ($materia_revisao = pg_fetch_assoc($resultado_materias_revisao)) {
        if(($materia_revisao['metodo'] === "Revisão") or ($materia_revisao['metodo'] === "Questões") or ($materia_revisao['metodo'] === "Simulado") or ($materia_revisao['revisoes'] === $data_atual_teste)){
            // Pular este item
        } else {
            // Verificar se é uma revisão atrasada
            if ($materia_revisao['atrasada'] == 't') {
                $revisoes_atrasadas[] = array(
                    'id' => $materia_revisao['idmateria'],
                    'nome' => $materia_revisao['nome_materia'],
                    'cor' => $materia_revisao['cor_materia'],
                    'data_estudo' => $materia_revisao['data_estudo'],
                    'ponto_estudado' => $materia_revisao['ponto_estudado'],
                    'idestudos' => $materia_revisao['idestudos']
                );
            } else {
                // Esta é uma revisão do dia
                $data_estudo = $materia_revisao['data_estudo'];
                $intervalo_descricao = $revisoes[$data_estudo]['descrição'];
                $dias_revisao = $revisoes[$data_estudo]['dias'];
                
                // Verifica se a data de revisão é diferente da data atual
                $data_revisao = strtotime($data_estudo);
                $data_atual = strtotime(date('Y-m-d'));

                if ($data_revisao != $data_atual) {
                    if (!isset($materias_por_revisao[$dias_revisao])) {
                        $materias_por_revisao[$dias_revisao] = [];
                    }

                    $materias_por_revisao[$dias_revisao][] = array(
                        'id' => $materia_revisao['idmateria'],
                        'nome' => $materia_revisao['nome_materia'],
                        'cor' => $materia_revisao['cor_materia'],
                        'dias' => $dias_revisao,
                        'ponto_estudado' => $materia_revisao['ponto_estudado'],
                        'idestudos' => $materia_revisao['idestudos'] // Inclui o ID estudos aqui
                    );
                }
            }
        }
    }
} else {
    echo "Erro na consulta de matérias para revisão: " . pg_last_error($conexao);
}


pg_close($conexao);

ksort($materias_por_revisao);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Language" content="pt-br">
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="timezone" content="America/Sao_Paulo">
    <title>Revisões de Hoje</title>
    <link href="https://fonts.googleapis.com/css2?family=Varela+Round&family=Quicksand:wght@300..700&family=Roboto+Condensed:wght@400;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">

    <style>
        :root {
            --primary: #00008B;
            --primary-light: #3949ab;
            --secondary: #f8f0e3;
            --accent: #4a4a4a;
            --border: #e0e0e0;
            --text: #2c3e50;
            --active: #00008B;
            --hover: #f5f5f5;
            --primary-blue: #00008B;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --background: #f5f5f5;
            --card-background: white;
            --danger: #d9534f;
            --danger-light: #f2dede;
            --success: #5cb85c;
            --success-light: #dff0d8;
        }

        [data-theme="dark"] {
            --primary: #4169E1;
            --secondary: #1a1a2e;
            --accent: #6c7293;
            --border: #2d2d42;
            --text: #e4e6f0;
            --active: #4169E1;
            --hover: #232338;
            --primary-blue: #4169E1;
            --shadow-color: rgba(0, 0, 0, 0.3);
            --background: #0a0a1f;
            --card-background: #13132b;
            --danger: #ff6b6b;
            --danger-light: #2d1e1e;
            --success: #5cb85c;
            --success-light: #1e2d1e;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Quicksand', sans-serif;
            background-color: var(--background);
            color: var(--text);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background-color: var(--primary);
            box-shadow: 0 2px 10px var(--shadow-color);
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .header-left, .header-right {
            display: flex;
            align-items: center;
        }

        .logo img {
            height: 50px;
            transition: opacity 0.3s ease;
        }

        .logo-light {
            display: var(--display-light);
        }

        .logo-dark {
            display: var(--display-dark);
        }

        .user-info {
            display: flex;
            align-items: center;
            margin-right: 20px;
            color: white;
        }

        .user-info i {
            font-size: 1.5rem;
            margin-right: 10px;
        }

        .user-name {
            font-weight: 600;
        }

        .btn-back {
            position: fixed;
            left: 20px;
            top: 92px;
            background-color: var(--primary);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            z-index: 100;
            transition: transform 0.3s ease, background-color 0.3s ease;
        }

        .btn-back:hover {
            transform: scale(1.1);
            /*background-color: var(--primary-dark);*/
        }

        h1 {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            text-align: center;
            color: var(--primary);
            font-size: 1.8rem;
            margin: 0;
            font-weight: 600;
        }

        /* Tabs Navigation */
        .tabs-container {
            margin-bottom: 30px;
        }

        .tabs-nav {
            display: flex;
            background-color: var(--card-background);
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 6px var(--shadow-color);
            margin-bottom: 20px;
        }

        .tab-btn {
            flex: 1;
            padding: 15px;
            text-align: center;
            background: none;
            border: none;
            color: var(--text);
            font-family: 'Quicksand', sans-serif;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            font-size: 0.95rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .tab-btn i {
            font-size: 1.1rem;
        }

        .tab-btn:hover {
            background-color: var(--hover);
        }

        .tab-btn.active {
            color: var(--primary);
            background-color: var(--card-background);
        }

        .tab-btn.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: var(--primary);
        }

        .tab-btn .badge {
            background-color: var(--danger);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 0.7rem;
            margin-left: 5px;
        }

        /* Tab Content */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .revisao-card {
            background-color: var(--card-background);
            border-radius: 10px;
            box-shadow: 0 4px 6px var(--shadow-color);
            margin-bottom: 25px;
            overflow: hidden;
            border: 1px solid var(--border);
            transition: transform 0.3s ease;
        }

        .revisao-card:hover {
            transform: translateY(-5px);
        }

        .revisao-header {
            background-color: var(--primary);
            color: white;
            padding: 15px 20px;
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .revisao-header .info-icon {
            cursor: help;
            margin-left: 10px;
        }

        .revisao-list {
            list-style: none;
            padding: 0;
        }

        .revisao-item {
            padding: 15px 20px;
            border-bottom: 1px solid var(--border);
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .revisao-item:last-child {
            border-bottom: none;
        }

        .revisao-item:hover {
            background-color: var(--hover);
        }

        .materia-nome {
            font-weight: 600;
            font-size: 1.1rem;
            flex: 1;
        }

        .ponto-estudado {
            flex: 2;
            padding: 0 15px;
            color: var(--accent);
        }

        .data-atrasada {
            color: var(--danger);
            font-size: 0.9rem;
            margin-right: 15px;
        }

        .btn {
            display: inline-block;
            padding: 8px 16px;
            background: var(--primary);
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-family: 'Quicksand', sans-serif;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            min-width: 100px;
        }

        .btn:hover {
            opacity: 0.9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        .btn-close {
            background: var(--accent);
            display: block;
            margin: 20px auto;
            padding: 10px 20px;
        }

        .atrasadas .revisao-header {
            background-color: var(--danger);
        }

        .no-revisoes {
            text-align: center;
            padding: 40px 20px;
            background-color: var(--card-background);
            border-radius: 10px;
            box-shadow: 0 4px 6px var(--shadow-color);
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            color: var(--accent);
            font-size: 1.2rem;
        }

        /* Stats Cards */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background-color: var(--card-background);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 6px var(--shadow-color);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-icon {
            font-size: 2rem;
            color: var(--primary);
            margin-bottom: 10px;
        }

        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--text);
            margin-bottom: 5px;
        }

        .stat-label {
            color: var(--accent);
            font-size: 0.9rem;
        }

        /* Toast Notification */
        .toast {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background-color: var(--success);
            color: white;
            padding: 15px 20px;
            border-radius: 5px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            gap: 10px;
            z-index: 1000;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
        }

        .toast.show {
            opacity: 1;
            transform: translateY(0);
        }

        .toast.error {
            background-color: var(--danger);
        }

        /* Tema escuro toggle */
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 100;
        }

        .theme-btn {
            background: transparent;
            border: none;
            color: white;
            /*font-size: 1.5rem;*/
            cursor: pointer;
           /* padding: 5px; */
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .theme-btn:hover {
            transform: rotate(30deg);
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .revisao-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
            
            .btn {
                width: 100%;
                margin-top: 10px;
            }
            
            .materia-nome, .ponto-estudado {
                width: 100%;
            }

            .tabs-nav {
                flex-wrap: wrap;
            }

            .tab-btn {
                padding: 10px;
                font-size: 0.85rem;
            }

            .header {
                padding: 10px 0;
            }

            h1 {
                font-size: 1.5rem;
            }

            .back-button {
                left: 10px;
            }
        }
        .header_centro {
    text-align: center;
    margin-bottom: 1.5rem;
}

.header_centro h1 {
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 2px;
    /*color: var(--text);*/
}

.header_centro p {
    color: var(--text-secondary, #666);
    font-size: 1.1rem;
    opacity: 0.8;
}

.menu-icon {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 10px;
}
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <div class="logo">
                <img src="logo/logo_vertical.png" alt="Logo" class="logo-light">
                
            </div>
        </div>
        <div class="header-right">
            <div class="user-info">
                <i class="fas fa-user-circle"></i>
                <span class="user-name"><?php echo htmlspecialchars($nome_usuario); ?></span>
            </div>
            <div class="theme-toggle">
                <button id="theme-toggle-btn" class="theme-btn">
                <i id="theme-icon" class="fas fa-moon"></i>
                </button>
            </div>
        </div>
    </div>

    <a href="index.php" class="btn-back" title="Voltar">
        <i class="fas fa-arrow-left"></i>
    </a>

    <div class="container">
        <header class="header_centro">
            <div class="menu-icon">
                <i class="fas fa-sync fa-3x" style="color: var(--primary); margin-bottom: 15px;"></i>
            </div>
            <h1>Revisões de Hoje</h1>
            <p>Acompanhe e conclua suas Revisões</p>
        </header>
        
        <!-- Estatísticas Rápidas -->
        <div class="stats-container">
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-calendar-day"></i></div>
                <div class="stat-value"><?php echo count($materias_por_revisao) ? array_sum(array_map('count', $materias_por_revisao)) : 0; ?></div>
                <div class="stat-label">Revisões do Dia</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-clock"></i></div>
                <div class="stat-value"><?php echo count($revisoes_atrasadas); ?></div>
                <div class="stat-label">Revisões Atrasadas</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                <div class="stat-value" id="revisoes-concluidas"><?php echo $revisoes_concluidas_count; ?></div>
                <div class="stat-label">Concluídas Hoje</div>
            </div>
        </div>

        <!-- Abas de Navegação -->
        <div class="tabs-container">
            <div class="tabs-nav">
                <button class="tab-btn active" data-tab="programadas">
                    <i class="fas fa-calendar-check"></i> Revisões Programadas
                </button>
                <button class="tab-btn" data-tab="atrasadas">
                    <i class="fas fa-clock"></i> Atrasadas
                    <?php if (count($revisoes_atrasadas) > 0): ?>
                    <span class="badge"><?php echo count($revisoes_atrasadas); ?></span>
                    <?php endif; ?>
                </button>
                <button class="tab-btn" data-tab="concluidas">
                    <i class="fas fa-check-double"></i> Concluídas
                </button>
            </div>

            <!-- Conteúdo da Aba: Revisões Programadas -->
            <div class="tab-content active" id="programadas">
                <?php if (!empty($materias_por_revisao)): ?>
                    <?php foreach ($materias_por_revisao as $dias => $materias): ?>
                        <?php if (isset($intervalos[$dias])): ?>
                            <?php
                            $intervalo_descricao = $intervalos[$dias]['descrição'];
                            $explicacao = $intervalos[$dias]['explicação'];
                            ?>
                            <div class="revisao-card">
                                <div class="revisao-header">
                                    <span><?php echo htmlspecialchars($intervalo_descricao); ?> - <?php echo htmlspecialchars($dias); ?> <?php echo ($dias == 1) ? 'Dia' : 'Dias'; ?></span>
                                    <i class="fas fa-info-circle info-icon" title="<?php echo htmlspecialchars($explicacao); ?>"></i>
                                </div>
                                <ul class="revisao-list">
                                    <?php foreach ($materias as $materia): ?>
                                        <li class="revisao-item" id="revisao-<?php echo $materia['idestudos']; ?>">
                                            <span class="materia-nome" style="color: <?php echo htmlspecialchars($materia['cor']); ?>;">
                                                <?php echo htmlspecialchars($materia['nome']); ?>
                                            </span>
                                            <span class="ponto-estudado">
                                                <?php echo htmlspecialchars($materia['ponto_estudado']); ?>
                                            </span>
                                            <button type="button" data-id="<?php echo $materia['idestudos']; ?>" class="btn btn-revisao">
                                                Feito
                                            </button>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                <?php else: ?>
                    <div class="no-revisoes">
                        <i class="fas fa-check-circle" style="font-size: 3rem; color: var(--primary); margin-bottom: 20px;"></i>
                        <p>Não há revisões programadas para hoje.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Conteúdo da Aba: Revisões Atrasadas -->
            <div class="tab-content" id="atrasadas">
                <?php if (!empty($revisoes_atrasadas)): ?>
                    <div class="revisao-card atrasadas">
                        <div class="revisao-header">
                            <span>Revisões Atrasadas</span>
                            <i class="fas fa-exclamation-circle" title="Estas revisões estão atrasadas e precisam ser feitas o quanto antes"></i>
                        </div>
                        <ul class="revisao-list">
                            <?php foreach ($revisoes_atrasadas as $materia): ?>
                                <li class="revisao-item" id="revisao-<?php echo $materia['idestudos']; ?>">
                                    <span class="materia-nome" style="color: <?php echo htmlspecialchars($materia['cor']); ?>;">
                                        <?php echo htmlspecialchars($materia['nome']); ?>
                                    </span>
                                    <span class="ponto-estudado">
                                        <?php echo htmlspecialchars($materia['ponto_estudado']); ?>
                                    </span>
                                    <span class="data-atrasada">
                                        <?php echo date('d/m/Y', strtotime($materia['data_estudo'])); ?>
                                    </span>
                                    <button type="button" data-id="<?php echo $materia['idestudos']; ?>" class="btn btn-revisao">
                                        Feito
                                    </button>
                                </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php else: ?>
                    <div class="no-revisoes">
                        <i class="fas fa-check-circle" style="font-size: 3rem; color: var(--success); margin-bottom: 20px;"></i>
                        <p>Não há revisões atrasadas. Parabéns!</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Conteúdo da Aba: Revisões Concluídas -->
            <div class="tab-content" id="concluidas">
                <div id="revisoes-concluidas-lista">
                    <?php if (!empty($revisoes_concluidas)): ?>
                        <?php foreach ($revisoes_concluidas as $revisao): ?>
                            <div class="revisao-card">
                                <div class="revisao-header" style="background-color: var(--success);">
                                    <span>Concluída às <?php echo htmlspecialchars($revisao['hora_revisao']); ?></span>
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <ul class="revisao-list">
                                    <li class="revisao-item">
                                        <span class="materia-nome" style="color: <?php echo htmlspecialchars($revisao['cor']); ?>;">
                                            <?php echo htmlspecialchars($revisao['nome']); ?>
                                        </span>
                                        <span class="ponto-estudado">
                                            <?php echo htmlspecialchars($revisao['ponto_estudado']); ?>
                                        </span>
                                    </li>
                                </ul>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="no-revisoes">
                            <i class="fas fa-check-circle" style="font-size: 3rem; color: var(--primary); margin-bottom: 20px;"></i>
                            <p>Nenhuma revisão foi concluída hoje.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <button onclick="window.close();" class="btn btn-close">
            Fechar Página
        </button>
    </div>

    <!-- Toast Notification -->
    <div class="toast" id="toast">
        <i class="fas fa-check-circle"></i>
        <span id="toast-message">Revisão concluída com sucesso!</span>
    </div>

    <script>
        // Função para alternar entre abas
        document.querySelectorAll('.tab-btn').forEach(button => {
            button.addEventListener('click', () => {
                // Remove a classe active de todas as abas
                document.querySelectorAll('.tab-btn').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                
                // Adiciona a classe active na aba clicada
                button.classList.add('active');
                document.getElementById(button.dataset.tab).classList.add('active');
            });
        });

        // Contador de revisões concluídas
        let revisoesConcluidasCount = <?php echo $revisoes_concluidas_count; ?>;
        const revisoesConcluidasElement = document.getElementById('revisoes-concluidas');
        const revisoesConcluidasLista = document.getElementById('revisoes-concluidas-lista');

        // Função para mostrar toast
        function showToast(message, isError = false) {
            const toast = document.getElementById('toast');
            const toastMessage = document.getElementById('toast-message');
            
            toastMessage.textContent = message;
            
            if (isError) {
                toast.classList.add('error');
            } else {
                toast.classList.remove('error');
            }
            
            toast.classList.add('show');
            
            setTimeout(() => {
                toast.classList.remove('show');
            }, 3000);
        }

        // Função para marcar revisão como concluída
        document.querySelectorAll('.btn-revisao').forEach(button => {
            button.addEventListener('click', async function() {
                const idEstudo = this.dataset.id;
                const revisaoItem = document.getElementById(`revisao-${idEstudo}`);
                
                // Desabilita o botão e mostra loading
                this.disabled = true;
                this.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
                
                try {
                    // Envia a requisição para o servidor
                    const formData = new FormData();
                    formData.append('idestudos', idEstudo);
                    
                    const response = await fetch('salvar_revisao.php', {
                        method: 'POST',
                        body: formData
                    });
                    
                    const result = await response.text();
                    console.log('Resposta do servidor:', result);
                    
                    // Considera sucesso se contiver "Revisão Concluída" ou "já registrada"
                    if (result.includes('Revisão Concluída') || result.includes('já registrada')) {
                        // Incrementa contador apenas se for uma nova revisão concluída
                        if (result.includes('Revisão Concluída')) {
                            revisoesConcluidasCount++;
                            if (revisoesConcluidasElement) {
                                revisoesConcluidasElement.textContent = revisoesConcluidasCount;
                            }
                            
                            // Adiciona à lista de concluídas
                            const materiaInfo = revisaoItem.querySelector('.materia-nome').textContent.trim();
                            const pontoInfo = revisaoItem.querySelector('.ponto-estudado').textContent.trim();
                            const materiaColor = revisaoItem.querySelector('.materia-nome').style.color;
                            
                            // Se for a primeira revisão concluída, limpa a mensagem padrão
                            if (revisoesConcluidasCount === 1 && revisoesConcluidasLista) {
                                revisoesConcluidasLista.innerHTML = '';
                            }
                            
                            // Adiciona o item à lista de concluídas
                            if (revisoesConcluidasLista) {
                                // Formatar a hora atual no formato HH:MM
                                const horaAtual = obterHoraBrasil();
                                
                                const concluido = document.createElement('div');
                                concluido.className = 'revisao-card';
                                concluido.innerHTML = `
                                    <div class="revisao-header" style="background-color: var(--success);">
                                        <span>Concluída às ${horaAtual}</span>
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <ul class="revisao-list">
                                        <li class="revisao-item">
                                            <span class="materia-nome" style="color: ${materiaColor};">
                                                ${materiaInfo}
                                            </span>
                                            <span class="ponto-estudado">
                                                ${pontoInfo}
                                            </span>
                                        </li>
                                    </ul>
                                `;
                                revisoesConcluidasLista.prepend(concluido);
                            }
                        }
                        
                        // Anima a remoção do item
                        revisaoItem.style.height = revisaoItem.offsetHeight + 'px';
                        revisaoItem.style.overflow = 'hidden';
                        
                        setTimeout(() => {
                            revisaoItem.style.height = '0';
                            revisaoItem.style.padding = '0';
                            revisaoItem.style.margin = '0';
                            revisaoItem.style.opacity = '0';
                            
                            setTimeout(() => {
                                revisaoItem.remove();
                                
                                // Verifica se a lista ficou vazia
                                const lista = revisaoItem.closest('.revisao-list');
                                if (lista && lista.children.length === 0) {
                                    const card = lista.closest('.revisao-card');
                                    if (card) {
                                        card.remove();
                                        
                                        // Se não houver mais cards na aba, mostra mensagem
                                        const tab = card.closest('.tab-content');
                                        if (tab && tab.querySelectorAll('.revisao-card').length === 0) {
                                            tab.innerHTML = `
                                                <div class="no-revisoes">
                                                    <i class="fas fa-check-circle" style="font-size: 3rem; color: var(--success); margin-bottom: 20px;"></i>
                                                    <p>Todas as revisões foram concluídas. Parabéns!</p>
                                                </div>
                                            `;
                                        }
                                    }
                                }
                            }, 300);
                        }, 10);
                        
                        // Mostra mensagem apropriada
                        if (result.includes('já registrada')) {
                            showToast('Esta revisão já foi registrada hoje!');
                        } else {
                            showToast('Revisão concluída com sucesso!');
                        }
                    } else {
                        // Restaura o botão
                        this.disabled = false;
                        this.textContent = 'Feito';
                        showToast('Erro ao salvar revisão: ' + result, true);
                    }
                } catch (error) {
                    console.error('Erro:', error);
                    // Restaura o botão
                    this.disabled = false;
                    this.textContent = 'Feito';
                    showToast('Erro ao salvar revisão. Tente novamente.', true);
                }
            });
        });

        // Função para aplicar tema salvo ou preferência do sistema
        function applyTheme(theme) {
            if (theme === 'dark') {
                document.documentElement.setAttribute('data-theme', 'dark');
            } else {
                document.documentElement.setAttribute('data-theme', '');
            }
        }
        
        // Detecta preferência do sistema
        function getPreferredTheme() {
            if (localStorage.getItem('theme')) {
                return localStorage.getItem('theme');
            }
            return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
        }
        
        // Função para atualizar o ícone do tema
        function updateThemeIcon(theme) {
            var icon = document.getElementById('theme-icon');
            if (icon) {
                if (theme === 'dark') {
                    icon.classList.remove('fa-moon');
                    icon.classList.add('fa-sun');
                } else {
                    icon.classList.remove('fa-sun');
                    icon.classList.add('fa-moon');
                }
            }
        }
        
        // Aplica tema ao carregar
        document.addEventListener('DOMContentLoaded', function() {
            var currentTheme = getPreferredTheme();
            applyTheme(currentTheme);
            updateThemeIcon(currentTheme);
            
            var btn = document.getElementById('theme-toggle-btn');
            if (btn) {
                btn.addEventListener('click', function() {
                    var currentTheme = document.documentElement.getAttribute('data-theme');
                    var newTheme = (currentTheme === 'dark') ? 'light' : 'dark';
                    applyTheme(newTheme);
                    updateThemeIcon(newTheme);
                    localStorage.setItem('theme', newTheme);
                });
            }
        });

        // Função para obter a hora atual no Brasil (São Paulo)
        function obterHoraBrasil() {
            const agora = new Date();
            
            // Ajuste para o fuso horário do Brasil (UTC-3)
            // Obtém a diferença em minutos entre o fuso horário local e UTC
            const offsetMinutos = agora.getTimezoneOffset();
            
            // Diferença em minutos para o fuso horário de São Paulo (UTC-3)
            const offsetSaoPaulo = -180; // -3 horas em minutos
            
            // Calcula a diferença em minutos entre o fuso horário local e São Paulo
            const diferencaMinutos = offsetMinutos + offsetSaoPaulo;
            
            // Ajusta a hora
            const horaAjustada = new Date(agora.getTime() + diferencaMinutos * 60000);
            
            // Formata a hora
            const horas = horaAjustada.getHours().toString().padStart(2, '0');
            const minutos = horaAjustada.getMinutes().toString().padStart(2, '0');
            
            return `${horas}:${minutos}`;
        }

        // Função para ajustar as horas exibidas na página
        function ajustarHorasExibidas() {
            // Não fazemos nada, pois agora as horas são exibidas diretamente do banco de dados
            // sem conversão de fuso horário
        }

        // Executar o ajuste quando a página carrega
        document.addEventListener('DOMContentLoaded', function() {
            ajustarHorasExibidas();
            // Resto do código...
        });
    </script>
</body>
</html>
