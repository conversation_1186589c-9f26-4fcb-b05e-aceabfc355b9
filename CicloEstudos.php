<?php
class CicloEstudos {
    private $conn;

    public function __construct($conexao) {
        $this->conn = $conexao;
    }

    // Método para atualizar a próxima matéria após um estudo
    public function atualizarCicloAposEstudo($materia_id, $planejamento_id) {
        $sql = "SELECT m.idmateria, m.nome
                FROM materia_has_planejamento mhp
                JOIN materia m ON m.idmateria = mhp.materia_idmateria
                WHERE mhp.planejamento_idplanejamento = $1
                AND m.idmateria > $2
                ORDER BY m.idmateria ASC
                LIMIT 1";

        $result = pg_query_params($this->conn, $sql, array($planejamento_id, $materia_id));

        if (!$result || pg_num_rows($result) == 0) {
            // Se não encontrou próxima, volta para a primeira
            $sql = "SELECT m.idmateria, m.nome
                    FROM materia_has_planejamento mhp
                    JOIN materia m ON m.idmateria = mhp.materia_idmateria
                    WHERE mhp.planejamento_idplanejamento = $1
                    ORDER BY m.idmateria ASC
                    LIMIT 1";

            $result = pg_query_params($this->conn, array($planejamento_id));
        }

        return pg_fetch_assoc($result);
    }
}
?>