<?php
/**
 * Endpoint de Relatórios
 * Sistema de Fidelidade da Barbearia
 */

function handleReports($method, $action, $id, $input, $db) {
    if ($method !== 'GET') {
        ApiResponse::methodNotAllowed();
    }
    
    switch ($action) {
        case 'clients':
            getClientReport($db);
            break;
            
        case 'stats':
            getBarberShopStats($db);
            break;
            
        case 'vip-status':
            getClientsByVipStatus($db);
            break;
            
        case 'services':
            if ($id === 'period') {
                getServicesByPeriod($db);
            } else {
                ApiResponse::error('Sub-ação não especificada', 400);
            }
            break;
            
        case 'clients':
            if ($id === 'top') {
                getTopClients($db);
            } else {
                getClientReport($db);
            }
            break;
            
        case 'birthdays':
            if ($id === 'month') {
                getBirthdaysThisMonth($db);
            } else {
                ApiResponse::error('Sub-ação não especificada', 400);
            }
            break;
            
        case 'performance':
            if ($id === 'monthly') {
                getMonthlyPerformance($db);
            } else {
                ApiResponse::error('Sub-ação não especificada', 400);
            }
            break;
            
        case 'export':
            exportData($db);
            break;
            
        default:
            ApiResponse::error('Ação não especificada', 400);
    }
}

/**
 * Relatório completo de clientes
 */
function getClientReport($db) {
    try {
        $sql = "SELECT * FROM vw_relatorio_clientes ORDER BY nome";
        
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $clients = $stmt->fetchAll();
        
        ApiResponse::success($clients);
        
    } catch (PDOException $e) {
        error_log("Get client report error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar relatório de clientes');
    }
}

/**
 * Estatísticas gerais da barbearia
 */
function getBarberShopStats($db) {
    try {
        $sql = "SELECT * FROM vw_estatisticas_barbearia";
        
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $stats = $stmt->fetch();
        
        ApiResponse::success($stats);
        
    } catch (PDOException $e) {
        error_log("Get barbershop stats error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar estatísticas da barbearia');
    }
}

/**
 * Clientes por status VIP
 */
function getClientsByVipStatus($db) {
    try {
        $result = [
            'diamante' => [],
            'ouro' => [],
            'bronze' => [],
            'novo' => []
        ];
        
        $sql = "SELECT u.id, u.nome, pf.pontuacao_geral,
                CASE 
                    WHEN pf.pontuacao_geral >= 50 THEN 'diamante'
                    WHEN pf.pontuacao_geral >= 20 THEN 'ouro'
                    WHEN pf.pontuacao_geral >= 5 THEN 'bronze'
                    ELSE 'novo'
                END as status_vip
                FROM usuarios u
                LEFT JOIN pontuacao_fidelidade pf ON u.id = pf.cliente_id
                WHERE u.tipo = 'cliente'
                ORDER BY pf.pontuacao_geral DESC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $clients = $stmt->fetchAll();
        
        foreach ($clients as $client) {
            $status = $client['status_vip'];
            $result[$status][] = $client;
        }
        
        ApiResponse::success($result);
        
    } catch (PDOException $e) {
        error_log("Get clients by VIP status error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar clientes por status VIP');
    }
}

/**
 * Atendimentos por período
 */
function getServicesByPeriod($db) {
    $startDate = $_GET['start_date'] ?? '';
    $endDate = $_GET['end_date'] ?? '';
    
    if (empty($startDate) || empty($endDate)) {
        ApiResponse::validation(['start_date', 'end_date'], 'Datas de início e fim são obrigatórias');
    }
    
    try {
        $sql = "SELECT ha.*, u.nome as cliente_nome 
                FROM historico_atendimentos ha 
                JOIN usuarios u ON ha.cliente_id = u.id 
                WHERE ha.data_atendimento BETWEEN ? AND ? 
                ORDER BY ha.data_atendimento DESC";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$startDate, $endDate]);
        $services = $stmt->fetchAll();
        
        ApiResponse::success($services);
        
    } catch (PDOException $e) {
        error_log("Get services by period error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar atendimentos por período');
    }
}

/**
 * Clientes mais frequentes
 */
function getTopClients($db) {
    $limit = $_GET['limit'] ?? 10;
    
    try {
        $sql = "SELECT u.id, u.nome, COUNT(ha.id) as total_atendimentos,
                pf.pontuacao_geral, pf.pontos_cabelo, pf.pontos_barba
                FROM usuarios u
                LEFT JOIN historico_atendimentos ha ON u.id = ha.cliente_id
                LEFT JOIN pontuacao_fidelidade pf ON u.id = pf.cliente_id
                WHERE u.tipo = 'cliente'
                GROUP BY u.id, u.nome, pf.pontuacao_geral, pf.pontos_cabelo, pf.pontos_barba
                ORDER BY total_atendimentos DESC, pf.pontuacao_geral DESC
                LIMIT ?";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([(int)$limit]);
        $clients = $stmt->fetchAll();
        
        ApiResponse::success($clients);
        
    } catch (PDOException $e) {
        error_log("Get top clients error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar clientes mais frequentes');
    }
}

/**
 * Aniversariantes do mês
 */
function getBirthdaysThisMonth($db) {
    try {
        $sql = "SELECT u.id, u.nome, pc.aniversario 
                FROM usuarios u 
                JOIN perfis_clientes pc ON u.id = pc.usuario_id 
                WHERE MONTH(pc.aniversario) = MONTH(NOW()) 
                AND pc.aniversario IS NOT NULL 
                ORDER BY DAY(pc.aniversario)";
        
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $birthdays = $stmt->fetchAll();
        
        ApiResponse::success($birthdays);
        
    } catch (PDOException $e) {
        error_log("Get birthdays this month error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar aniversariantes');
    }
}

/**
 * Performance mensal
 */
function getMonthlyPerformance($db) {
    $year = $_GET['year'] ?? date('Y');
    $month = $_GET['month'] ?? date('m');
    
    try {
        $sql = "SELECT 
                COUNT(*) as total_atendimentos,
                COUNT(DISTINCT cliente_id) as clientes_atendidos,
                SUM(CASE WHEN tipo_servico = 'cabelo' THEN 1 ELSE 0 END) as cortes_cabelo,
                SUM(CASE WHEN tipo_servico = 'barba' THEN 1 ELSE 0 END) as cortes_barba
                FROM historico_atendimentos 
                WHERE YEAR(data_atendimento) = ? AND MONTH(data_atendimento) = ?";
        
        $stmt = $db->prepare($sql);
        $stmt->execute([$year, $month]);
        $performance = $stmt->fetch();
        
        ApiResponse::success($performance);
        
    } catch (PDOException $e) {
        error_log("Get monthly performance error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar performance mensal');
    }
}

/**
 * Exportar dados para backup
 */
function exportData($db) {
    try {
        $export = [];
        
        // Exportar usuários
        $sql = "SELECT * FROM usuarios";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $export['usuarios'] = $stmt->fetchAll();
        
        // Exportar pontuação
        $sql = "SELECT * FROM pontuacao_fidelidade";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $export['pontuacao_fidelidade'] = $stmt->fetchAll();
        
        // Exportar histórico
        $sql = "SELECT * FROM historico_atendimentos";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $export['historico_atendimentos'] = $stmt->fetchAll();
        
        ApiResponse::success($export);
        
    } catch (PDOException $e) {
        error_log("Export data error: " . $e->getMessage());
        ApiResponse::error('Erro ao exportar dados');
    }
}
?>
