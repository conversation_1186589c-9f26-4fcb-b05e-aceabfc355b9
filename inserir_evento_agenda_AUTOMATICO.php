<?php
session_start();
include_once("conexao_POST.php"); // Inclua aqui o arquivo de conexão com o banco de dados
include 'processa_index.php'; // Para obter as matérias

$response = array('status' => 'error', 'message' => ''); // Inicializar um array para a resposta

if (isset($_SESSION['idusuario']) && isset($_POST['start_date']) && isset($_POST['end_date']) && isset($_POST['materias_selecionadas'])) {

    $idUsuario = $_SESSION['idusuario'];

    if ($_POST['tipo_evento'] === 'Planejamento') {

        $materias_selecionadas = $_POST['materias_selecionadas'];

        if (!empty($materias_selecionadas)) {
            // Inicializa a data de controle com a data de início
            $data_controle = new DateTime($_POST['start_date']);

            // Calcula a quantidade de dias entre as datas de início e término
            $start_date = new DateTime($_POST['start_date']);
            $end_date = new DateTime($_POST['end_date']);
            $interval = $start_date->diff($end_date);
            $Qnt_dia = $interval->days;

            // Número de matérias por dia
            $materias_per_day = intval($_POST['materias_per_day']);
            $total_materias = count($materias_selecionadas);

            // Preparar a inserção dos eventos no banco de dados
            $query = "INSERT INTO appEstudo.agenda (usuario_idusuario, titulo, data_inicio, data_fim, tipo_evento, detalhes) VALUES ($1, $2, $3, $4, $5, $6)";
            $stmt = pg_prepare($conexao, "insere_evento", $query);

            // Loop para percorrer os dias
            for ($i = 0; $i <= $Qnt_dia; $i++) {
                for ($j = 0; $j < $materias_per_day; $j++) {
                    // Calcula a posição da matéria no array
                    $materia_index = ($i * $materias_per_day + $j) % $total_materias;
                    $titulo = $materias_selecionadas[$materia_index];

                    // Verifica se a matéria está selecionada
                    if (in_array($titulo, $materias_selecionadas)) {
                        // Executar a inserção dos eventos no banco de dados
                        $dataInicio = $data_controle->format('Y-m-d');
                        $dataFim = $data_controle->format('Y-m-d');
                        $tipo_evento = "Planejamento"; // Supondo que o tipo de evento seja "Planejamento"
                        $detalhes = "Inserido no Modo Automático";

                        $result = pg_execute($conexao, "insere_evento", array($idUsuario, $titulo, $dataInicio, $dataFim, $tipo_evento, $detalhes));
                    }
                }
                // Incrementa um dia
                $data_controle->modify('+1 day');
            }


            $response['status'] = 'success';
            $response['message'] = 'Eventos inseridos com sucesso.';
        } else {
            $response['message'] = 'Nenhuma matéria encontrada para o planejamento.';
        }
    } else {
        $response['message'] = 'Tipo de evento inválido.';
    }
} else {
    $response['message'] = 'Dados incompletos ou sessão não iniciada.';
}

// Enviar a resposta no formato JSON
header('Content-Type: application/json');
echo json_encode($response);

// Redirecionar para a página "calendario_agenda"
header('Location: calendario_agenda_AUTOMATICO.php');
exit; // Certifique-se de sair do script após o redirecionamento
?>
