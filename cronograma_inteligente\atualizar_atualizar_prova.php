<?php
//atualizar_atualziar_prova.php

session_start();
if (!isset($_SESSION['idusuario'])) {
    header("Location: /login_index.php");
    exit();
}

include_once("assets/config.php");

$usuario_id = $_SESSION['idusuario'];

// Validar dados recebidos
$nome_prova = trim($_POST['nome_prova'] ?? '');
$data_prova = $_POST['data_prova'] ?? '';
$data_inicio_estudo = $_POST['data_inicio_estudo'] ?? '';
$prova_id = isset($_POST['prova_id']) && !empty($_POST['prova_id']) ? intval($_POST['prova_id']) : null;

// Verificar se existe prova_id
if (!$prova_id) {
    $_SESSION['erro'] = "Prova não identificada!";
    header("Location: plano_estudo_inteligente.php");
    exit();
}

// Validações básicas
if (empty($nome_prova) || empty($data_prova) || empty($data_inicio_estudo)) {
    $_SESSION['erro'] = "Todos os campos são obrigatórios!";
    header("Location: plano_estudo_inteligente.php");
    exit();
}

try {
    // Validar datas
    $data_atual = new DateTime();
    $data_prova_obj = new DateTime($data_prova);
    $data_inicio_obj = new DateTime($data_inicio_estudo);

    // Definir horário como meia-noite para todas as datas
    $data_atual->setTime(0, 0, 0);
    $data_prova_obj->setTime(0, 0, 0);
    $data_inicio_obj->setTime(0, 0, 0);

    // Validação da data da prova no passado
    if ($data_prova_obj < $data_atual) {
        $_SESSION['erro'] = "A data da prova não pode ser no passado!";
        header("Location: plano_estudo_inteligente.php");
        exit();
    }

    // Validação da data de início posterior à prova
    if ($data_inicio_obj >= $data_prova_obj) {
        $_SESSION['erro'] = "A data de início não pode ser IGUAL ou POSTERIOR à data da prova!";
        header("Location: plano_estudo_inteligente.php");
        exit();
    }

    // Calcular diferença em dias
    $diferenca = $data_prova_obj->diff($data_inicio_obj);
    $dias_diferenca = $diferenca->days;

    if ($dias_diferenca < 15) {
        $_SESSION['erro'] = "O período de estudos não pode ser MENOR que 15 dias! O período selecionado tem " . $dias_diferenca . " dias.";
        header("Location: plano_estudo_inteligente.php");
        exit();
    }

    // Iniciar transação
    pg_query($conexao, "BEGIN");

    // Inativar outras provas (exceto a atual)
    $query_inativar = "
        UPDATE appestudo.provas 
        SET status = false 
        WHERE usuario_id = $1 
        AND id != $2 
        AND status = true";
    pg_query_params($conexao, $query_inativar, array($usuario_id, $prova_id));

    // Atualizar prova existente
    $query_prova = "
        UPDATE appestudo.provas 
        SET nome = $1, 
            data_prova = $2, 
            data_inicio_estudo = $3
        WHERE id = $4 
        AND usuario_id = $5
        RETURNING id";
    $result_prova = pg_query_params($conexao, $query_prova, array(
        $nome_prova,
        $data_prova,
        $data_inicio_estudo,
        $prova_id,
        $usuario_id
    ));

    if (!$result_prova) {
        throw new Exception("Erro ao atualizar a prova!");
    }

    // Sincronizar com planejamento se solicitado
// Sincronizar com planejamento se solicitado
if (isset($_POST['sincronizar_planejamento']) || isset($_POST['sincronizar_datas'])) {
    $query_planejamento = "
        SELECT idplanejamento 
        FROM appestudo.planejamento 
        WHERE usuario_idusuario = $1 
        ORDER BY data_inicio DESC 
        LIMIT 1";
    
    $result_planejamento = pg_query_params($conexao, $query_planejamento, array($usuario_id));
    
    if ($planejamento = pg_fetch_assoc($result_planejamento)) {
        // Preparar a query de atualização
        $campos_update = array();
        $params = array();
        $param_count = 1;
        
        // Atualizar nome se solicitado
        if (isset($_POST['sincronizar_planejamento'])) {
            $campos_update[] = "nome = $" . $param_count;
            $params[] = $nome_prova;
            $param_count++;
        }
        
        // Atualizar datas se solicitado
        if (isset($_POST['sincronizar_datas'])) {
            $campos_update[] = "data_inicio = $" . $param_count;
            $params[] = $data_inicio_estudo;
            $param_count++;
            
            $campos_update[] = "data_fim = $" . $param_count;
            $params[] = $data_prova;
            $param_count++;
        }
        
        // Se houver campos para atualizar
        if (!empty($campos_update)) {
            $query_update_planejamento = "
                UPDATE appestudo.planejamento 
                SET " . implode(", ", $campos_update) . "
                WHERE idplanejamento = $" . $param_count;
            
            $params[] = $planejamento['idplanejamento'];
            
            $result_update = pg_query_params($conexao, $query_update_planejamento, $params);

            if (!$result_update) {
                throw new Exception("Erro ao atualizar planejamento!");
            }
        }
    }
}

    // Atualizar pesos e dificuldades das matérias
    $query_materias = "
        SELECT DISTINCT m.idmateria 
        FROM appestudo.conteudo_edital ce
        JOIN appestudo.materia m ON ce.materia_id = m.idmateria
        JOIN appestudo.usuario_edital ue ON ce.edital_id = ue.edital_id
        WHERE ue.usuario_id = $1";

    $result_materias = pg_query_params($conexao, $query_materias, array($usuario_id));

    while ($materia = pg_fetch_assoc($result_materias)) {
        $materia_id = $materia['idmateria'];
        $peso = intval($_POST["peso_$materia_id"] ?? 1);
        $dificuldade = intval($_POST["dificuldade_$materia_id"] ?? 3);

        // Atualizar ou inserir pesos
        $query_check = "SELECT id FROM appestudo.pesos_materias WHERE prova_id = $1 AND materia_id = $2";
        $result_check = pg_query_params($conexao, $query_check, array($prova_id, $materia_id));

        if (pg_num_rows($result_check) > 0) {
            $query_pesos = "
                UPDATE appestudo.pesos_materias 
                SET peso = $1, nivel_dificuldade = $2
                WHERE prova_id = $3 AND materia_id = $4";
            pg_query_params($conexao, $query_pesos, array(
                $peso,
                $dificuldade,
                $prova_id,
                $materia_id
            ));
        } else {
            $query_pesos = "
                INSERT INTO appestudo.pesos_materias 
                (prova_id, materia_id, peso, nivel_dificuldade) 
                VALUES ($1, $2, $3, $4)";
            pg_query_params($conexao, $query_pesos, array(
                $prova_id,
                $materia_id,
                $peso,
                $dificuldade
            ));
        }
    }

    // Commit da transação
    pg_query($conexao, "COMMIT");

    $_SESSION['sucesso'] = "Configurações da prova atualizadas com sucesso!";
    header("Location: plano_estudo_inteligente.php");
    exit();

} catch (Exception $e) {
    // Rollback em caso de erro
    pg_query($conexao, "ROLLBACK");

    $_SESSION['erro'] = "Erro ao atualizar configurações: " . $e->getMessage();
    header("Location: plano_estudo_inteligente.php");
    exit();
}
?>