<?php
// Endpoint para fornecer o token CSRF ao front-end via AJAX
session_start();
require_once __DIR__ . '/csrf.php';

header('Content-Type: application/json');
// Headers de segurança HTTP
header('X-Frame-Options: DENY');
header('X-Content-Type-Options: nosniff');
header('Referrer-Policy: no-referrer');
header('Permissions-Policy: interest-cohort=()');
header('Strict-Transport-Security: max-age=31536000; includeSubDomains; preload');

echo json_encode([
    'csrf_token' => getCsrfToken()
]);
