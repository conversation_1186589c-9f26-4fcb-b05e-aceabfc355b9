<?php
// Verifica se já existe uma conexão
if (!isset($conexao)) {
    require_once(__DIR__ . '/conexao_POST.php');
}

// Verifica se a função já foi definida
if (!function_exists('get_notificacoes')) {
    require_once(__DIR__ . '/functions/get_notificacoes.php');
}

// Verifica se há uma sessão ativa
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Debug para verificar o usuário
$usuario_id = isset($_SESSION['idusuario']) ? $_SESSION['idusuario'] : 0;
error_log("ID do usuário em avisos.php: " . $usuario_id);

// Verificar se a tabela existe e criar se necessário
$check_table = "SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'appestudo' 
    AND table_name = 'notificacoes'
)";

$table_exists = pg_query($conexao, $check_table);
$exists = pg_fetch_result($table_exists, 0, 0);

if ($exists === 'f') {
    $create_table = file_get_contents(__DIR__ . '/database/criar_tabela_notificacoes.sql');
    $result = pg_query($conexao, $create_table);
    
    if (!$result) {
        error_log("Erro ao criar tabela de notificações: " . pg_last_error($conexao));
    }
}

// Buscar notificações
$result = get_notificacoes($conexao, $usuario_id);

// Debug para verificar o resultado da query
if ($result) {
    error_log("Número de notificações encontradas: " . pg_num_rows($result));
} else {
    error_log("Erro ao buscar notificações: " . pg_last_error($conexao));
}

// JavaScript para marcar como lida
$js_marcar_lida = "
<script>
function marcarComoLida(notificacaoId) {
    const avisoElement = event.currentTarget;
    
    fetch('marcar_notificacao_lida.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            notificacao_id: notificacaoId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            avisoElement.classList.add('lido');
            if (!avisoElement.querySelector('.badge-lido')) {
                const badge = document.createElement('span');
                badge.className = 'badge-lido';
                badge.innerHTML = '✓';
                avisoElement.appendChild(badge);
            }
        }
    });
}
</script>

<style>
.aviso-item {
    cursor: pointer;
    transition: all 0.3s ease;
}

.aviso-item.lido {
    opacity: 0.7;
    background-color: #f5f5f5;
}

.badge-lido {
    background-color: #4CAF50;
    color: white;
    padding: 2px 6px;
    border-radius: 50%;
    margin-left: 8px;
    font-size: 12px;
}

.badge-global {
    background-color: #007bff;
    color: white;
    padding: 2px 6px;
    border-radius: 12px;
    font-size: 0.8em;
    margin-left: 5px;
}
</style>";

echo $js_marcar_lida;
?>

<div class="card-header">
    <h3 class="handwritten">
        <i class="fas fa-bullhorn"></i> Avisos
    </h3>
</div>
<div class="card-content_planejamento">
    <?php if ($result && pg_num_rows($result) > 0): ?>
        <?php while ($aviso = pg_fetch_assoc($result)): ?>
            <div class="aviso-item <?php echo getClasseTipo($aviso['tipo']); ?>">
                <span class="aviso-data">
                    <?php echo date('d/m/Y', strtotime($aviso['data_criacao'])); ?>
                    <?php if ($aviso['is_global'] == 't'): ?>
                        <span class="badge-global">Global</span>
                    <?php endif; ?>
                </span>
                <p><strong><?php echo htmlspecialchars($aviso['titulo']); ?>:</strong> 
                   <?php echo htmlspecialchars($aviso['mensagem']); ?></p>
            </div>
        <?php endwhile; ?>
    <?php else: ?>
        <div class="aviso-item">
            <p><i class="fas fa-info-circle"></i> Nenhum aviso no momento.</p>
        </div>
    <?php endif; ?>
</div>

<?php
function getClasseTipo($tipo) {
    switch ($tipo) {
        case 'info':
            return 'aviso-destaque';
        case 'warning':
            return 'aviso-alerta';
        case 'danger':
            return 'aviso-importante';
        default:
            return '';
    }
}
?>
