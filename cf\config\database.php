<?php
// Configuração direta do banco de dados
// ATENÇÃO: Nunca versionar este arquivo com dados reais em repositórios públicos!

$dbConfig = [
    'host' => 'app_estudo.postgresql.dbaas.com.br',
    'dbname' => 'app_estudo',
    'user' => 'app_estudo',
    'password' => 'Lucasb90#',
    'port' => '5432'
];

function getDbConnection() {
    global $dbConfig;
    
    try {
        $dsn = "pgsql:host={$dbConfig['host']};port={$dbConfig['port']};dbname={$dbConfig['dbname']};";
        $pdo = new PDO($dsn, $dbConfig['user'], $dbConfig['password']);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        return $pdo;
    } catch (PDOException $e) {
        die("Erro na conexão: " . $e->getMessage());
    }
}
?>