<?php
session_start();
include_once("assets/config.php");

if (!isset($_SESSION['idusuario'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Usuário não autenticado']);
    exit();
}

// Verifica se recebeu os dados via POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $data = json_decode(file_get_contents('php://input'), true);

    if (!isset($data['revisao_id'])) {
        echo json_encode(['success' => false, 'error' => 'ID da revisão não fornecido']);
        exit();
    }

    $revisao_id = $data['revisao_id'];
    $usuario_id = $_SESSION['idusuario'];

    // Atualiza o status da revisão para "ignorado"
    $query = "
        UPDATE appestudo.revisoes 
        SET status_revisao = 'ignorado',
            ultima_revisao = CURRENT_TIMESTAMP
        WHERE id = $revisao_id 
        AND usuario_id = $usuario_id
        AND status_revisao = 'pendente'
    ";

    $result = pg_query($conexao, $query);

    if ($result) {
        echo json_encode(['success' => true, 'message' => 'Revisão ignorada com sucesso']);
    } else {
        echo json_encode(['success' => false, 'error' => 'Erro ao ignorar revisão']);
    }
} else {
    echo json_encode(['error' => 'Método não permitido']);
}
?>