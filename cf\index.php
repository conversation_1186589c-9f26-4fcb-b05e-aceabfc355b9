<?php
session_start();

// Verifica se o usuário está logado
if (!isset($_SESSION['idusuario'])) {
    // Redireciona para a página de login
    header("Location: ../login_index.php");
    exit;
}
?>

<?php
// Conexão com o banco
require_once __DIR__ . '/config/database.php';
$usuario_id = $_SESSION['idusuario'];
$nome_usuario = '';

try {
    $pdo = getDbConnection();
    $stmt = $pdo->prepare("SELECT nome FROM appestudo.usuario WHERE idusuario = :idusuario");
    $stmt->execute([':idusuario' => $usuario_id]);
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($row && isset($row['nome'])) {
        $nome_usuario = $row['nome'];
    } else {
        $nome_usuario = 'Usuário';
    }
} catch (Exception $e) {
    $nome_usuario = 'Usuário';
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Legislação Brasileira</title>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;700&family=Quicksand:wght@400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Quicksand:wght@300..700&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Tektur:wght@400..900&family=Varela+Round&display=swap" rel="stylesheet">
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <style>
:root {
    /* Cores modo claro (já existentes) */
    --primary: #00008B;
    --primary-light: #3949ab;
    --secondary: #f8f0e3;
    --accent: #4a4a4a;
    --border: #e0e0e0;
    --text: #2c3e50;
    --active: #00008B;
    --hover: #f5f5f5;
    --primary-blue: #00008B;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --background: #f5f5f5;
    --card-background: white;
}
[data-theme="dark"] {
    --primary: #4169E1;
    --secondary: #1a1a2e;
    --accent: #6c7293;
    --border: #2d2d42;
    --text: #e4e6f0;
    --active: #4169E1;
    --hover: #232338;
    --primary-blue: #4169E1;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --background: #0a0a1f;
    --card-background: #13132b;
}

h1 {
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    text-align: center;
    color: var(--primary);
    font-size: 2rem;
    margin-bottom: 30px;
    font-weight: 600;
}
.status-card {
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 4px 6px var(--shadow-color);
    margin: 0 20px 25px 20px;
    transition: transform 0.3s ease;
    border: 1px solid var(--border);
    background: var(--card-background);
}
.status-title {
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    color: var(--primary);
    font-size: 1.3rem;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid var(--border);
}
.btn {
    display: inline-block;
    padding: 12px 24px;
    background: var(--primary);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    font-family: 'Quicksand', sans-serif;
    font-weight: 600;
    text-align: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    margin: 5px;
    flex: 1;
    min-width: 200px;
    max-width: 300px;
    box-sizing: border-box;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-color);
    opacity: 0.9;
}
.btn-secondary {
    background: var(--accent);
}
.btn-secondary:hover {
    background: var(--accent);
    opacity: 0.9;
}
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}
.info-item {
    padding: 15px;
    border-radius: 8px;
    border: 1px solid var(--border);
    background: var(--hover);
}
.info-item strong {
    color: var(--primary);
    font-family: 'Varela Round', 'Quicksand', sans-serif;
}
.progress-section {
    margin: 25px 0;
    padding: 20px;
    background: var(--card-background);
    border-radius: 10px;
    box-shadow: 0 2px 4px var(--shadow-color);
    border: 1px solid var(--border);
}
.progress-bar {
    width: 100%;
    height: 10px;
    background: var(--hover);
    border-radius: 5px;
    overflow: hidden;
    border: 1px solid var(--border);
}
.progress {
    height: 100%;
    background: var(--primary);
    transition: width 0.3s ease;
}

.welcome-message {
    text-align: center;
    margin-bottom: 30px;
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    color: var(--text);
    font-size: 1.1rem;
}
.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}
.progress-text {
    color: var(--text);
    font-weight: 600;
}
.progress-percentage {
    color: var(--primary);
    font-weight: 700;
    font-size: 1.1rem;
}
.progress-stats {
    margin-top: 10px;
    text-align: center;
    font-size: 0.9rem;
    color: var(--text);
}
.alert-warning {
    background: #fff3cd;
    border-left: 4px solid #ffc107;
    padding: 15px 20px;
    margin: 20px 0;
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    color: #856404;
}
.alert-warning i {
    color: #ffc107;
    font-size: 1.2rem;
}
.action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 20px;
    padding: 0 10px;
    width: 100%;
    box-sizing: border-box;
}
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border);
    margin-bottom: 30px;
    border-radius: 0 0 10px 10px;
    box-shadow: 0 2px 4px var(--shadow-color);
    background: linear-gradient(90deg, var(--primary) 60%, var(--primary) 100%);
}
[data-theme="dark"] .header {
    background: linear-gradient(90deg, var(--primary) 60%, var(--primary) 100%);
    }

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}
.logo img {
    height: 50px;
    width: auto;
    transition: opacity 0.3s ease;
}
.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}
.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    background: var(--houver);
    color: var(--hover);
}
[data-theme="dark"] .user-info {
        color: var(--text);
    }

.user-info i {
    
    font-size: 1.2rem;
}
.user-name {
    
    font-weight: 600;
}
.theme-toggle {
    margin-left: 15px;
}
.theme-btn {
    background: transparent;
    border: none;
    color: var(--hover);
    cursor: pointer;
    font-size: 1.2rem;
    padding: 8px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}
[data-theme="dark"] .theme-btn {
            color: var(--text);
        }
.theme-btn:hover {
    background: var(--hover);
}
.logo {
    position: relative;
}
.logo-dark {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
}
[data-theme="dark"] .logo-light {
    opacity: 0;
}
[data-theme="dark"] .logo-dark {
    opacity: 1;
}
@media (max-width: 768px) {
    .container {
        padding: 0;
    }
    .status-card {
        margin: 0 10px 20px 10px;
        padding: 15px;
    }
    .info-grid {
        grid-template-columns: 1fr;
        padding: 0 10px;
    }
    .action-buttons {
        flex-direction: column;
        align-items: center;
        padding: 0 20px;
        margin: 20px 0;
        gap: 10px;
    }
    .btn {
        width: 100%;
        max-width: 100%;
        min-width: auto;
        margin: 0;
    }
    .header {
        padding: 10px;
    }
    .logo img {
        height: 40px;
    }
    .user-info {
        font-size: 0.8rem;
        padding: 6px 12px;
    }
    h1 {
        font-size: 1.5rem;
        padding: 0 10px;
    }
    .welcome-message {
        font-size: 1rem;
        padding: 0 10px;
    }
}
@media (max-width: 480px) {
    .status-card {
        margin: 0 5px 15px 5px;
        padding: 10px;
    }
    .btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }
    .info-item {
        padding: 10px;
        font-size: 0.9rem;
    }
}
    


        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background: var(--card-background) !important;
            /*color: var(--text) !important;*/
        }





        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 0 auto;
            max-width: 1000px;
        }

        .menu-item {
            background: var(--card-background);
            border: 2px solid var(--primary);
            border-radius: 10px;
            padding: 2rem;
            text-align: center;
            text-decoration: none;
            color: var(--primary);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        [data-theme="dark"] .menu-item {
            color: var(--primary);
            border-color: var(--primary);
        }

        .menu-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            background: var(--primary);
            color: white;
        }

        .menu-item h2 {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            font-size: 1.5rem;
            margin-bottom: 1rem;
        }

        .menu-item p {
            font-size: 1rem;
            line-height: 1.5;
        }

        .menu-icon {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 10px;
        }

        .menu-icon i {
            transition: all 0.3s ease;
        }

        .menu-item:hover .menu-icon i {
            color: white !important;
            transform: scale(1.1);
            transition: all 0.3s ease;
        }

        .footer {
            text-align: center;
            padding: 0rem;
            background: linear-gradient(90deg, var(--primary) 60%, var(--primary) 100%);
            color: var(--hover);
            margin-top: auto;
        }
        [data-theme="dark"] .footer {
            background: var(--primary);
            color: var(--text);
        }

        .footer p {
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .menu-grid {
                grid-template-columns: 1fr;
            }
        }

        .header_centro {
            text-align: center;
            margin-bottom: 1rem;
        }

        .header_centro h1 {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            font-size: 2.5rem;
            margin-bottom: 1rem;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .header_centro p {
            color: #666;
            font-size: 1.1rem;
        }
    </style>
    <script>
    // Função para aplicar tema salvo ou preferência do sistema
    function applyTheme(theme) {
        if (theme === 'dark') {
            document.documentElement.setAttribute('data-theme', 'dark');
        } else {
            document.documentElement.setAttribute('data-theme', '');
        }
    }
    // Detecta preferência do sistema
    function getPreferredTheme() {
        if (localStorage.getItem('theme')) {
            return localStorage.getItem('theme');
        }
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    // Função para atualizar o ícone do tema
    function updateThemeIcon(theme) {
        var icon = document.getElementById('theme-icon');
        if (icon) {
            if (theme === 'dark') {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
            }
        }
    }
    // Aplica tema ao carregar
    document.addEventListener('DOMContentLoaded', function() {
        var currentTheme = getPreferredTheme();
        applyTheme(currentTheme);
        updateThemeIcon(currentTheme);
        var btn = document.getElementById('theme-toggle-btn');
        if (btn) {
            btn.addEventListener('click', function() {
                var currentTheme = document.documentElement.getAttribute('data-theme');
                var newTheme = (currentTheme === 'dark') ? 'light' : 'dark';
                applyTheme(newTheme);
                updateThemeIcon(newTheme);
                localStorage.setItem('theme', newTheme);
            });
        }
    });
    // Atualiza se preferência do sistema mudar
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', e => {
        if (!localStorage.getItem('theme')) {
            applyTheme(e.matches ? 'dark' : 'light');
        }
    });
    </script>
    
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-left">
                <div class="logo">
                    <img src="../logo/logo_vertical.png" alt="Logo" class="logo-light">
                    <img src="../logo/logo_vertical.png" alt="Logo" class="logo-dark">
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <i class="fas fa-user-circle"></i>
                    <span class="user-name"><?php echo htmlspecialchars($nome_usuario); ?></span>
                </div>
                <div class="theme-toggle">
                    <button id="theme-toggle-btn" class="theme-btn">
                    <i id="theme-icon" class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>

        <header class="header_centro">
            <div class="menu-icon">
                <i class="fas fas fa-book fa-3x" style="color: var(--primary); margin-bottom: 15px;"></i>
            </div>
            <h1>Cadernos de Lei</h1>
            <p>Selecione a legislação que deseja Estudar</p>
        </header>

        <div class="menu-grid">
            <a href="cf.php" class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-scroll fa-2x" style="color: var(--primary); margin-bottom: 15px;"></i>
                </div>
                <h2>Constituição Federal</h2>
                <p>Constituição da República Federativa do Brasil de 1988</p>
            </a>

            <a href="cp.php" class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-gavel fa-2x" style="color: var(--primary); margin-bottom: 15px;"></i>
                </div>
                <h2>Código Penal</h2>
                <p>Decreto-Lei nº 2.848, de 7 de dezembro de 1940</p>
            </a>

            <a href="cpp.php" class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-book-open fa-2x" style="color: var(--primary); margin-bottom: 15px;"></i>
                </div>
                <h2>Código de Processo Penal</h2>
                <p>Decreto-Lei nº 3.689, de 3 de outubro de 1941</p>
            </a>

            <a href="cc.php" class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-landmark fa-2x" style="color: var(--primary); margin-bottom: 15px;"></i>
                </div>
                <h2>Código Civil</h2>
                <p>Lei nº 10.406, de 10 de janeiro de 2002</p>
            </a>

            <a href="cpc.php" class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-balance-scale fa-2x" style="color: var(--primary); margin-bottom: 15px;"></i>
                </div>
                <h2>Código de Processo Civil</h2>
                <p>Lei nº 13.105, de 16 de março de 2015</p>
            </a>

            <a href="lidb.php" class="menu-item">
                <div class="menu-icon">
                    <i class="fas fa-book-reader fa-2x" style="color: var(--primary); margin-bottom: 15px;"></i>
                </div>
                <h2>LINDB</h2>
                <p>Lei de Introdução às normas do Direito Brasileiro - Decreto-Lei nº 4.657/42</p>
            </a>
        </div>
    </div>

    <footer class="footer">
        <p>&copy; <?php echo date('Y'); ?> - www.planejaaqui.com.br</p>
    </footer>
    <!-- Botão Fechar fixo -->
    <button id="close-btn-fixed" title="Fechar janela">
    <i class="fas fa-times"></i>
</button>
    <style>
        #close-btn-fixed {
            position: fixed;
            bottom: 24px;
            right: 24px;
            z-index: 9999;
            background: var(--primary);
            color: #fff;
            border: none;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            padding: 0;
            box-shadow: 0 4px 16px var(--shadow-color);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background 0.2s, color 0.2s, box-shadow 0.2s;
            font-size: 1.5rem;
        }
        #close-btn-fixed i {
            pointer-events: none;
        }
        #close-btn-fixed:hover {
            background: var(--primary-light, #3949ab);
            color: #fff;
            box-shadow: 0 6px 24px var(--shadow-color);
        }
        [data-theme="dark"] #close-btn-fixed {
            background: var(--primary);
            color: var(--text);
        }
        [data-theme="dark"] #close-btn-fixed:hover {
            background: var(--primary-light, #3949ab);
            color: var(--text);
        }
    </style>
    <script>
    document.getElementById('close-btn-fixed').onclick = function() {
        window.close();
    };
    </script>
</body>
</html>
