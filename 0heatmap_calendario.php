<?php

if (!isset($_SESSION['username'])) {
    error_log("Usuário não está logado");
    exit;
}

if (!isset($conexao)) {
    include 'conexao_POST.php';
}

$id_usuario = $_SESSION['idusuario'] ?? null;

function buscarDadosEstudoPorDia($conexao, $id_usuario) {
    if (!$conexao || !$id_usuario) {
        error_log("Conexão ou ID usuário inválido");
        return [];
    }

    // Adicionando logs para debug
    error_log("Buscando dados para usuário: " . $id_usuario);

    $query = "
        WITH dados_agrupados AS (
            SELECT 
                data::date as data_estudo,
                SUM(EXTRACT(EPOCH FROM tempo_liquido)/3600) as horas_estudo,
                EXTRACT(YEAR FROM data) as ano
            FROM appEstudo.estudos 
            WHERE planejamento_usuario_idusuario = $id_usuario 
            GROUP BY data::date, EXTRACT(YEAR FROM data)
            ORDER BY data_estudo
        )
        SELECT 
            data_estudo,
            horas_estudo,
            ano
        FROM dados_agrupados;
    ";

    $resultado = pg_query($conexao, $query);

    if (!$resultado) {
        error_log("Erro na query: " . pg_last_error($conexao));
        return [];
    }

    $dados = [];
    $anos = [];

    while ($row = pg_fetch_assoc($resultado)) {
        $data = $row['data_estudo'];
        $horas = floatval($row['horas_estudo']);
        $ano = $row['ano'];

        // Formatação da data para garantir consistência
        $data_formatada = date('Y-m-d', strtotime($data));
        $dados[$data_formatada] = $horas;

        if (!in_array($ano, $anos)) {
            $anos[] = $ano;
        }

        // Log para debug
        error_log("Data: $data_formatada, Horas: $horas");
    }

    return ['dados' => $dados, 'anos' => $anos];
}

$resultado = buscarDadosEstudoPorDia($conexao, $id_usuario);
$dados_estudo = $resultado['dados'];
$anos_disponiveis = $resultado['anos'];
?>



    
   

    <div class="year-selector">
        <select id="yearSelect" class="year-select">
            <?php foreach ($anos_disponiveis as $ano): ?>
                <option value="<?php echo $ano; ?>"><?php echo $ano; ?></option>
            <?php endforeach; ?>
        </select>
    </div>



    <div class="dashboard-grid-heatmap">
        <!-- Painel de estatísticas -->
        <div class="stats-panel">
            <div class="stat-card">
                <div class="stat-title">Total de Horas</div>
                <div id="totalHoras" class="stat-value">0h</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Média por Dia</div>
                <div id="mediaHoras" class="stat-value">0h</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Dias de Estudo</div>
                <div id="diasEstudo" class="stat-value">0</div>
            </div>
            <div class="stat-card">
                <div class="stat-title">Maior Sequência</div>
                <div id="maiorSequencia" class="stat-value">0 dias</div>
            </div>
        </div>

        <!-- Heatmap existente -->
        <div class="heatmap-content">
        <div class="heatmap-legend">
            <span>Menos</span>
            <div class="legend-cells">
            <div class="legend-cell" style="background-color: rgba(0, 0, 139, 0.1)"></div>
<div class="legend-cell" style="background-color: rgba(0, 0, 139, 0.3)"></div>
<div class="legend-cell" style="background-color: rgba(0, 0, 139, 0.5)"></div>
<div class="legend-cell" style="background-color: rgba(0, 0, 139, 0.7)"></div>
<div class="legend-cell" style="background-color: rgba(0, 0, 139, 0.9)"></div>
            </div>
            <span>Mais</span>
        </div>
        <div id="heatmap-calendar-grid"></div>
    </div>
</div>


<script>
    document.addEventListener('DOMContentLoaded', function() {
        const dadosEstudo = <?php echo json_encode($dados_estudo); ?>;
        const anosDisponiveis = <?php echo json_encode($anos_disponiveis); ?>;
        let anoAtual = new Date().getFullYear();

        const yearSelect = document.getElementById('yearSelect');

        // Inicializar com o ano mais recente disponível
        if (anosDisponiveis.length > 0) {
            anoAtual = Math.max(...anosDisponiveis); // Define o ano atual como o mais recente disponível
            yearSelect.value = anoAtual; // Define o valor inicial do seletor de ano
            atualizarHeatmap(anoAtual); // Carrega o heatmap e estatísticas para o ano atual ao abrir a página
        }



        function calcularEstatisticas(dadosAnoAtual) {
            const estatisticas = {
                totalHoras: 0,
                diasEstudo: Object.keys(dadosAnoAtual).length,
                maiorSequencia: 0,
                mediaHoras: 0
            };

            // Ordenar as datas em ordem decrescente (mais recente primeiro)
            const datasOrdenadas = Object.keys(dadosAnoAtual)
                .sort((a, b) => new Date(b) - new Date(a));

            //console.log('Datas ordenadas:', datasOrdenadas);

            let sequenciaAtual = 0;
            const hoje = new Date();
            hoje.setHours(0, 0, 0, 0);

            // Para cada data, do mais recente ao mais antigo
            for (let i = 0; i < datasOrdenadas.length; i++) {
                const dataAtual = new Date(datasOrdenadas[i]);
                dataAtual.setHours(0, 0, 0, 0);

                // Se é hoje, conta se tiver estudo
                if (dataAtual.getTime() === hoje.getTime()) {
                    if (dadosAnoAtual[datasOrdenadas[i]] > 0) {
                        sequenciaAtual++;
                    }
                    continue;
                }

                // Para outros dias
                if (dadosAnoAtual[datasOrdenadas[i]] > 0) {
                    // Verifica se é consecutivo com o dia anterior
                    if (i > 0) {
                        const dataAnterior = new Date(datasOrdenadas[i-1]);
                        dataAnterior.setHours(0, 0, 0, 0);
                        const diffDias = Math.round((dataAnterior - dataAtual) / (1000 * 60 * 60 * 24));

                        if (diffDias === 1) {
                            sequenciaAtual++;
                        } else {
                            if (sequenciaAtual > estatisticas.maiorSequencia) {
                                estatisticas.maiorSequencia = sequenciaAtual;
                            }
                            sequenciaAtual = 1;
                        }
                    } else {
                        sequenciaAtual = 1;
                    }
                }

                estatisticas.totalHoras += dadosAnoAtual[datasOrdenadas[i]];
            }

            // Verifica uma última vez a maior sequência
            if (sequenciaAtual > estatisticas.maiorSequencia) {
                estatisticas.maiorSequencia = sequenciaAtual;
            }

            estatisticas.mediaHoras = estatisticas.totalHoras / estatisticas.diasEstudo;

           // console.log('Sequência final:', sequenciaAtual);
           // console.log('Estatísticas calculadas:', estatisticas);

            return estatisticas;
        }

        function atualizarEstatisticas(dadosAnoAtual) {
            const stats = calcularEstatisticas(dadosAnoAtual);

            document.getElementById('totalHoras').textContent = formatarTempoHHMM(stats.totalHoras);
            document.getElementById('mediaHoras').textContent = formatarTempoHHMM(stats.mediaHoras);
            document.getElementById('diasEstudo').textContent = stats.diasEstudo;
            document.getElementById('maiorSequencia').textContent = `${stats.maiorSequencia} dias`;
        }

        function atualizarHeatmap(ano) {
            const heatmap = document.getElementById('heatmap-calendar-grid');
            heatmap.innerHTML = '';

            // Função para formatar data sem problemas de timezone
            function formatarData(data) {
                const year = data.getFullYear();
                const month = String(data.getMonth() + 1).padStart(2, '0');
                const day = String(data.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            }

            // Converter dados para estrutura mais fácil de verificar
            const dadosAnoAtual = {};
            Object.entries(dadosEstudo).forEach(([data, valor]) => {
                if (data.startsWith(ano)) {
                    // Só adiciona se realmente tiver horas de estudo
                    if (valor > 0) {
                        dadosAnoAtual[data] = valor;
                    }
                }
            });

            // Debug - mostrar todas as datas com estudo
           // console.log('Dias com estudo:', dadosAnoAtual);

            const maxHoras = Math.max(...Object.values(dadosAnoAtual), 1);

            // Renderização dos labels dos dias
            const emptyCell = document.createElement('div');
            emptyCell.className = 'day-label';
            heatmap.appendChild(emptyCell);

            for (let i = 1; i <= 31; i++) {
                const dayLabel = document.createElement('div');
                dayLabel.className = 'day-label';
                dayLabel.textContent = i.toString().padStart(2, '0');
                heatmap.appendChild(dayLabel);
            }

            // Renderização dos meses
            const meses = ['Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun', 'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'];
            meses.forEach((mes, index) => {
                const label = document.createElement('div');
                label.className = 'month-label';
                label.textContent = mes;
                label.style.gridRow = index + 2;
                heatmap.appendChild(label);
            });

            // Definição das datas inicial e final do ano
            const inicioAno = new Date(ano, 0, 1);
            const fimAno = new Date(ano, 11, 31);

            // Renderização das células dos dias
            for (let d = new Date(inicioAno); d <= fimAno; d.setDate(d.getDate() + 1)) {
                const dataFormatada = formatarData(d);  // Usando nossa função local
                const horasEstudo = dadosAnoAtual[dataFormatada];

                const cell = document.createElement('div');
                cell.className = 'day-cell';

                // Só renderiza como dia com estudo se explicitamente tiver horas registradas
                if (horasEstudo !== undefined && horasEstudo > 0) {
                    const intensidade = horasEstudo / maxHoras;
                    cell.style.backgroundColor = `rgba(0, 0, 139, ${intensidade * 0.9})`;

                    const dayNumber = document.createElement('span');
                    dayNumber.className = 'day-number';
                    dayNumber.textContent = d.getDate();
                    cell.appendChild(dayNumber);

                    const tempoFormatado = formatarTempoHHMM(horasEstudo);

                    const tooltip = document.createElement('div');
                    tooltip.className = 'day-cell-tooltip';
                    tooltip.textContent = `${d.toLocaleDateString('pt-BR')}: ${tempoFormatado}`;
                    cell.appendChild(tooltip);

                    // Debug - confirmar dados renderizados
                  //  console.log(`Renderizando dia ${dataFormatada} com ${horasEstudo} horas`);
                }

                cell.style.gridColumn = d.getDate() + 1;
                cell.style.gridRow = d.getMonth() + 2;

                heatmap.appendChild(cell);
            }

            // Atualiza as estatísticas
            atualizarEstatisticas(dadosAnoAtual);
        }

        function formatarTempoHHMM(horas) {
            const horasInteiras = Math.floor(horas);
            const minutos = Math.round((horas - horasInteiras) * 60);
            const horasFormatadas = horasInteiras.toString().padStart(2, '0');
            const minutosFormatados = minutos.toString().padStart(2, '0');
            return `${horasFormatadas}h:${minutosFormatados}m`;
        }

        // Evento para atualizar o heatmap quando o usuário muda o ano
        yearSelect.addEventListener('change', function() {
            anoAtual = parseInt(this.value);
            atualizarHeatmap(anoAtual);
        });
    });
</script>

<style>
    .estatistica-header {
        background: #00008B;
        padding: 1.5rem;
        color: white;
        border-radius: 15px 15px 0 0; /* Arredonda apenas os cantos superiores */
    }

    .dashboard-grid-heatmap {
        display: grid;
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .stats-panel {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        padding: 1rem;
        /*background: white;*/
        border-radius: 8px;
        
    }

    .stat-card {
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 6px;
        text-align: center;
        transition: transform 0.2s ease;
    }

    .stat-card:hover {
        transform: translateY(-2px);
    }

    .stat-title {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 0.5rem;
    }

    .stat-value {
        font-size: 1.5rem;
        font-weight: bold;
        color: #00008B;
    }

    @media (max-width: 768px) {
        .stats-panel {
            grid-template-columns: repeat(2, 1fr);
        }
    }

    @media (max-width: 480px) {
        .stats-panel {
            grid-template-columns: 1fr;
        }
    }
    .day-label {
        text-align: center;
        font-size: 0.85rem;
        color: #666;
        font-weight: bold;
    }

    .year-selector {
        display: flex;
        justify-content: center; /* Centraliza o seletor */
        margin: 1rem 0;
    }

    .year-select {
        padding: 0.5rem 1rem;
        font-size: 1rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: white;
        cursor: pointer;
        color: var(--primary);
    }


    .year-nav-button {
        padding: 0.5rem 1rem;
        font-size: 1rem;
        border: 1px solid #ddd;
        border-radius: 4px;
        background-color: white;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .year-nav-button:hover {
        background-color: #f0f0f0;
    }

    .year-nav-button:disabled {
        opacity: 0.5;
        cursor: not-allowed;
    }

    .sem-dados-container {
        padding: 2rem;
        text-align: center;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .sem-dados-mensagem i {
        font-size: 2rem;
        color: #00008B;
        margin-bottom: 1rem;
    }

    .sem-dados-mensagem h3 {
        color: #00008B;
        margin: 1rem 0;
    }

    .heatmap-content {
        padding: 1.5rem;
        /*background: white;*/
        min-height: 400px;
    }

    .heatmap-legend {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        margin-bottom: 1.5rem;
    }

    .legend-cells {
        display: flex;
        gap: 2px;
    }

    .legend-cell {
        width: 20px;
        height: 20px;
        border-radius: 4px;
    }

    #heatmap-calendar-grid {
        display: grid;
        grid-template-columns: auto repeat(31, 1fr);
        gap: 4px;
        padding: 0.5rem;
    }

    .month-label {
        grid-column: 1;
        padding-right: 0.75rem;
        font-size: 0.85rem;
        color: #666;
        text-align: right;
    }

    .day-cell {
        aspect-ratio: 1;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s ease;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .day-number {
        font-size: 0.7rem;
        color: white;
        font-weight: bold;
        pointer-events: none;
    }

    .day-cell:hover {
        transform: scale(1.2);
        z-index: 2;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .day-cell-tooltip {
        position: absolute;
        bottom: 120%;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 139, 0.9);
        color: white;
        padding: 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        white-space: nowrap;
        z-index: 1000;
        display: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .day-cell:hover .day-cell-tooltip {
        display: block;
    }

    @media (max-width: 1200px) {
        #heatmap-calendar-grid {
            grid-template-columns: auto repeat(20, 1fr);
        }
    }

    @media (max-width: 768px) {
        #heatmap-calendar-grid {
            grid-template-columns: auto repeat(15, 1fr);
        }
    }

    @media (max-width: 480px) {
        #heatmap-calendar-grid {
            grid-template-columns: auto repeat(7, 1fr);
        }
        .month-label {
            font-size: 0.75rem;
        }
        .day-number {
            font-size: 0.6rem;
        }
    }
</style>