<?php
// avisos_nao_lidos.php

// Inicia sessão e inclui conexão
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
if (!isset($conexao)) {
    require_once(__DIR__ . '/conexao_POST.php');
}
if (!function_exists('get_notificacoes')) {
    require_once(__DIR__ . '/functions/get_notificacoes.php');
}

header('Content-Type: application/json');

$usuario_id = isset($_SESSION['idusuario']) ? $_SESSION['idusuario'] : 0;
if (!$usuario_id) {
    echo json_encode(['nao_lidos' => 0]);
    exit;
}

// Busca todas as notificações válidas para o usuário
$result = get_notificacoes($conexao, $usuario_id);
$nao_lidos = 0;
if ($result && pg_num_rows($result) > 0) {
    while ($aviso = pg_fetch_assoc($result)) {
        // Considera como não lido se não existir registro em notificacoes_usuarios com lida = true
        $query_lida = "SELECT lida FROM appestudo.notificacoes_usuarios WHERE notificacao_id = $1 AND usuario_id = $2";
        $res_lida = pg_query_params($conexao, $query_lida, array($aviso['id'], $usuario_id));
        $lida = false;
        if ($res_lida && pg_num_rows($res_lida) > 0) {
            $row = pg_fetch_assoc($res_lida);
            $lida = ($row['lida'] === 't');
        }
        if (!$lida) {
            $nao_lidos++;
        }
    }
}
echo json_encode(['nao_lidos' => $nao_lidos]); 