<?php
// adicionar_medicamento.php
require_once 'includes/functions.php';
require_once 'includes/medicamento.php';

// Verificar se o formulário foi enviado
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Instanciar objeto
    $medicamento = new Medicamento();
    
    // Definir valores do medicamento
    $medicamento->nome = $_POST['nome'];
    $medicamento->dosagem = $_POST['dosagem'];
    $medicamento->intervalo_horas = $_POST['intervalo_horas'];
    $medicamento->data_inicio = $_POST['data_inicio'];
    $medicamento->dias_tratamento = $_POST['dias_tratamento'];
    $medicamento->horario_inicial = $_POST['horario_inicial'];
    $medicamento->observacoes = $_POST['observacoes'];
    
    // Adicionar o medicamento
    if ($medicamento->adicionar()) {
        // Redirecionar para a página inicial com mensagem de sucesso
        header("Location: index.php?success=1");
        exit();
    } else {
        $erro = "Ocorreu um erro ao adicionar o medicamento.";
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Adicionar Medicamento - Controle de Medicamentos</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="container my-4">
        <header class="mb-4">
            <h1 class="text-center">Adicionar Novo Medicamento</h1>
        </header>
        
        <?php if (isset($erro)): ?>
            <div class="alert alert-danger"><?= $erro ?></div>
        <?php endif; ?>
        
        <div class="card">
            <div class="card-body">
                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="nome" class="form-label">Nome do Medicamento</label>
                        <input type="text" class="form-control" id="nome" name="nome" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="dosagem" class="form-label">Dosagem</label>
                        <input type="text" class="form-control" id="dosagem" name="dosagem" placeholder="Ex: 500mg, 1 comprimido, 10ml" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="intervalo_horas" class="form-label">Intervalo entre Doses (em horas)</label>
                        <select class="form-select" id="intervalo_horas" name="intervalo_horas" required>
                            <option value="4">4 horas (6 vezes ao dia)</option>
                            <option value="6">6 horas (4 vezes ao dia)</option>
                            <option value="8">8 horas (3 vezes ao dia)</option>
                            <option value="12">12 horas (2 vezes ao dia)</option>
                            <option value="24" selected>24 horas (1 vez ao dia)</option>
                            <option value="48">48 horas (Dia sim, dia não)</option>
                        </select>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="data_inicio" class="form-label">Data de Início</label>
                            <input type="date" class="form-control" id="data_inicio" name="data_inicio" 
                                   value="<?= date('Y-m-d') ?>" required>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="horario_inicial" class="form-label">Horário da Primeira Dose</label>
                            <input type="time" class="form-control" id="horario_inicial" name="horario_inicial" 
                                   value="<?= date('H:i') ?>" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="dias_tratamento" class="form-label">Duração do Tratamento (em dias)</label>
                        <input type="number" class="form-control" id="dias_tratamento" name="dias_tratamento" 
                               min="1" value="7" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="observacoes" class="form-label">Observações (opcional)</label>
                        <textarea class="form-control" id="observacoes" name="observacoes" rows="3"></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="index.php" class="btn btn-secondary">Cancelar</a>
                        <button type="submit" class="btn btn-primary">Adicionar Medicamento</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="assets/js/app.js"></script>
</body>
</html>