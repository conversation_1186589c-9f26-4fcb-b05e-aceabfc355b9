<?php
// editar_medicamento.php
require_once 'includes/functions.php';
require_once 'includes/medicamento.php';

// Verificar se o ID foi fornecido
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: index.php");
    exit();
}

// Instanciar objeto
$medicamento = new Medicamento();

// Obter dados do medicamento
if (!$medicamento->obter($_GET['id'])) {
    header("Location: index.php");
    exit();
}

// Verificar se o formulário foi enviado
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Definir valores do medicamento
    $medicamento->nome = $_POST['nome'];
    $medicamento->dosagem = $_POST['dosagem'];
    $medicamento->intervalo_horas = $_POST['intervalo_horas'];
    $medicamento->data_inicio = $_POST['data_inicio'];
    $medicamento->dias_tratamento = $_POST['dias_tratamento'];
    $medicamento->horario_inicial = $_POST['horario_inicial'];
    $medicamento->observacoes = $_POST['observacoes'];
    
    // Atualizar o medicamento
    if ($medicamento->atualizar()) {
        // Redirecionar para a página inicial com mensagem de sucesso
        header("Location: index.php?success=2");
        exit();
    } else {
        $erro = "Ocorreu um erro ao atualizar o medicamento.";
    }
}

// Formatar data e hora para exibição nos campos
$data_inicio = date('Y-m-d', strtotime($medicamento->data_inicio));
$horario_inicial = date('H:i', strtotime($medicamento->horario_inicial));
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Medicamento - Controle de Medicamentos</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="container my-4">
        <header class="mb-4">
            <h1 class="text-center">Editar Medicamento</h1>
        </header>
        
        <?php if (isset($erro)): ?>
            <div class="alert alert-danger"><?= $erro ?></div>
        <?php endif; ?>
        
        <div class="card">
            <div class="card-body">
                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="nome" class="form-label">Nome do Medicamento</label>
                        <input type="text" class="form-control" id="nome" name="nome" value="<?= htmlspecialchars($medicamento->nome) ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="dosagem" class="form-label">Dosagem</label>
                        <input type="text" class="form-control" id="dosagem" name="dosagem" value="<?= htmlspecialchars($medicamento->dosagem) ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="intervalo_horas" class="form-label">Intervalo entre Doses (em horas)</label>
                        <select class="form-select" id="intervalo_horas" name="intervalo_horas" required>
                            <option value="4" <?= $medicamento->intervalo_horas == 4 ? 'selected' : '' ?>>4 horas (6 vezes ao dia)</option>
                            <option value="6" <?= $medicamento->intervalo_horas == 6 ? 'selected' : '' ?>>6 horas (4 vezes ao dia)</option>
                            <option value="8" <?= $medicamento->intervalo_horas == 8 ? 'selected' : '' ?>>8 horas (3 vezes ao dia)</option>
                            <option value="12" <?= $medicamento->intervalo_horas == 12 ? 'selected' : '' ?>>12 horas (2 vezes ao dia)</option>
                            <option value="24" <?= $medicamento->intervalo_horas == 24 ? 'selected' : '' ?>>24 horas (1 vez ao dia)</option>
                            <option value="48" <?= $medicamento->intervalo_horas == 48 ? 'selected' : '' ?>>48 horas (Dia sim, dia não)</option>
                        </select>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="data_inicio" class="form-label">Data de Início</label>
                            <input type="date" class="form-control" id="data_inicio" name="data_inicio" value="<?= $data_inicio ?>" required>
                        </div>
                        
                        <div class="col-md-6">
                            <label for="horario_inicial" class="form-label">Horário da Primeira Dose</label>
                            <input type="time" class="form-control" id="horario_inicial" name="horario_inicial" value="<?= $horario_inicial ?>" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="dias_tratamento" class="form-label">Duração do Tratamento (em dias)</label>
                        <input type="number" class="form-control" id="dias_tratamento" name="dias_tratamento" min="1" value="<?= $medicamento->dias_tratamento ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="observacoes" class="form-label">Observações (opcional)</label>
                        <textarea class="form-control" id="observacoes" name="observacoes" rows="3"><?= htmlspecialchars($medicamento->observacoes) ?></textarea>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="index.php" class="btn btn-secondary">Cancelar</a>
                        <div>
                            <a href="javascript:void(0)" onclick="confirmarDesativacao(<?= $medicamento->id ?>)" class="btn btn-danger me-2">Desativar</a>
                            <button type="submit" class="btn btn-primary">Salvar Alterações</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmarDesativacao(id) {
            if (confirm('Tem certeza que deseja desativar este medicamento?')) {
                window.location.href = 'desativar_medicamento.php?id=' + id;
            }
        }
    </script>
</body>
</html>