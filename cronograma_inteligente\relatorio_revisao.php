<?php
session_start();
if (!isset($_SESSION['idusuario']) || $_SESSION['idusuario'] != 1) {
    header("Location: login.php");
    exit();
}

include_once("assets/config.php");

$usuario_id = $_SESSION['idusuario'];

// Consulta para obter conteúdos com data de revisão
$query = "
    SELECT 
        m.idmateria,
        m.nome AS materia_nome,
        m.cor,
        ce.descricao,
        ce.capitulo,
        uc.data_revisao
    FROM 
        appestudo.usuario_conteudo AS uc
    JOIN 
        appestudo.conteudo_edital AS ce ON uc.conteudo_id = ce.id_conteudo
    JOIN 
        appestudo.materia AS m ON ce.materia_id = m.idmateria
    WHERE 
        uc.usuario_id = $usuario_id
        AND uc.data_revisao IS NOT NULL
    ORDER BY 
        uc.data_revisao, m.nome;
";

$result = pg_query($conexao, $query);
if (!$result) {
    die("Erro ao buscar dados de revisão.");
}

// Organizar os conteúdos para exibição por data de revisão
$revisoes_por_data = [];
while ($row = pg_fetch_assoc($result)) {
    $data_revisao = $row['data_revisao'];
    if (!isset($revisoes_por_data[$data_revisao])) {
        $revisoes_por_data[$data_revisao] = [];
    }
    $revisoes_por_data[$data_revisao][] = $row;
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Relatório de Datas de Revisão</title>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier Prime', monospace;
            background: #f4f1ea;
            color: #2c1810;
            padding: 20px;
            line-height: 1.6;
            background-image: linear-gradient(rgba(244, 241, 234, 0.9), rgba(244, 241, 234, 0.9)),
            url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23d3c5b8' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
        }

        .header-vintage {
            background: linear-gradient(to right, #8B0000, #B22222);
            padding: 30px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 30px;
            position: relative;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .header-vintage h2 {
            font-family: 'Cinzel', serif;
            font-size: 28px;
            color: #fff;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 1.5px;
        }

        .header-vintage::after {
            content: '';
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 2px;
            background: rgba(255,255,255,0.5);
        }

        .revisoes-container {
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #8B4513;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 20px;
            color: #d3c5b8;
        }

        .revisao {
            margin-bottom: 40px;
        }

        .data-header {
            display: flex;
            align-items: center;
            gap: 15px;
            font-family: 'Cinzel', serif;
            font-size: 1.3rem;
            color: #8B4513;
            padding: 15px 20px;
            background: #fdfbf7;
            border-radius: 8px;
            border: 1px solid #d3c5b8;
            margin-bottom: 20px;
        }

        .data-icon {
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #8B4513;
            color: #fff;
            border-radius: 50%;
        }

        .data-info {
            display: flex;
            flex-direction: column;
        }

        .data-label {
            font-size: 0.9rem;
            color: #666;
        }

        .conteudos-grid {
            display: grid;
            gap: 15px;
        }

        .conteudo-item {
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #d3c5b8;
            background: #fff;
            transition: transform 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .conteudo-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
        }

        .conteudo-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .materia-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .materia-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            color: #fff;
            font-size: 1rem;
        }

        .materia-nome {
            font-family: 'Cinzel', serif;
            font-size: 1.2rem;
            color: #2c1810;
            font-weight: bold;
        }

        .capitulo {
            display: inline-block;
            padding: 4px 8px;
            background: #f4f1ea;
            border-radius: 4px;
            font-weight: bold;
            color: #8B4513;
            margin-bottom: 10px;
        }

        .descricao {
            color: #4a4a4a;
            line-height: 1.5;
        }

        .dias-restantes {
            position: absolute;
            top: 20px;
            right: 20px;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .dias-proximo { background: #ffe4e4; color: #d32f2f; }
        .dias-medio { background: #fff3e0; color: #f57c00; }
        .dias-longe { background: #e8f5e9; color: #388e3c; }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .header-vintage {
                padding: 20px;
                margin-bottom: 20px;
            }

            .revisoes-container {
                padding: 15px;
            }

            .data-header {
                flex-direction: column;
                text-align: center;
            }

            .conteudo-item {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header-vintage">
        <h2>Relatório de Datas de Revisão</h2>
    </div>

    <div class="revisoes-container">
        <?php if (empty($revisoes_por_data)): ?>
            <div class="empty-state">
                <i class="fas fa-calendar-times"></i>
                <p>Nenhuma data de revisão definida para os conteúdos selecionados.</p>
            </div>
        <?php else: ?>
            <?php foreach ($revisoes_por_data as $data => $conteudos): ?>
                <?php
                $data_revisao = strtotime($data);
                $hoje = strtotime('today');
                $diff_dias = floor(($data_revisao - $hoje) / (60 * 60 * 24));

                $dias_classe = '';
                if ($diff_dias <= 3) {
                    $dias_classe = 'dias-proximo';
                } elseif ($diff_dias <= 7) {
                    $dias_classe = 'dias-medio';
                } else {
                    $dias_classe = 'dias-longe';
                }
                ?>
                <div class="revisao">
                    <div class="data-header">
                        <div class="data-icon">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="data-info">
                            <div class="data-label">Data de Revisão</div>
                            <div><?= date("d/m/Y", strtotime($data)) ?></div>
                        </div>
                        <span class="dias-restantes <?= $dias_classe ?>">
                                <?php
                                if ($diff_dias < 0) {
                                    echo "Atrasado " . abs($diff_dias) . " dias";
                                } elseif ($diff_dias == 0) {
                                    echo "Hoje";
                                } else {
                                    echo "Faltam " . $diff_dias . " dias";
                                }
                                ?>
                            </span>
                    </div>

                    <div class="conteudos-grid">
                        <?php foreach ($conteudos as $conteudo): ?>
                            <div class="conteudo-item" style="border-color: <?= htmlspecialchars($conteudo['cor']) ?>">
                                <div class="materia-header">
                                    <div class="materia-icon" style="background-color: <?= htmlspecialchars($conteudo['cor']) ?>">
                                        <i class="fas fa-book"></i>
                                    </div>
                                    <div class="materia-nome"><?= htmlspecialchars($conteudo['materia_nome']) ?></div>
                                </div>
                                <div class="capitulo">Capítulo <?= htmlspecialchars($conteudo['capitulo']) ?></div>
                                <div class="descricao"><?= htmlspecialchars($conteudo['descricao']) ?></div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>
</body>
</html>
