<?php
//get_estatisticas.php
session_start();
include_once("assets/config.php");

if (!isset($_SESSION['idusuario'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Usuário não autenticado']);
    exit();
}

$usuario_id = $_SESSION['idusuario'];

$query = "
WITH niveis_do_edital AS (
    SELECT 
        edital_id,
        materia_id,
        MIN(capitulo) as primeiro_capitulo
    FROM 
        appestudo.conteudo_edital
    GROUP BY 
        edital_id, materia_id
),
pesos_calculados AS (
    SELECT 
        m.idmateria,
        COALESCE(pm.peso, 1) * COALESCE(pm.nivel_dificuldade, 1) * 
        CASE 
            WHEN (SELECT data_prova::date - CURRENT_DATE <= 7 
                  FROM appestudo.provas 
                  WHERE usuario_id = $usuario_id AND status = true) 
            THEN 2 
            ELSE 1 
        END as prioridade_calculada
    FROM appestudo.materia m
    LEFT JOIN appestudo.pesos_materias pm ON m.idmateria = pm.materia_id
    LEFT JOIN appestudo.provas p ON pm.prova_id = p.id AND p.status = true
    WHERE p.usuario_id = $usuario_id
),
conteudo_valido AS (
    SELECT 
        m.idmateria,
        m.nome AS materia_nome,
        m.cor,
        uc.status_estudo,
        uc.id as conteudo_id
    FROM 
        appestudo.usuario_conteudo AS uc
    JOIN 
        appestudo.conteudo_edital AS ce ON uc.conteudo_id = ce.id_conteudo
    JOIN 
        appestudo.materia AS m ON ce.materia_id = m.idmateria
    JOIN
        pesos_calculados pc ON m.idmateria = pc.idmateria
    WHERE 
        uc.usuario_id = $usuario_id
        AND uc.status = true
        AND ce.capitulo ~ '^[0-9.]+$'
),
estatisticas_finais AS (
    SELECT 
        materia_nome,
        cor,
        COUNT(*) AS total_itens,
        COUNT(CASE WHEN status_estudo = 'Estudado' THEN 1 END) as itens_estudados,
        ROUND((COUNT(CASE WHEN status_estudo = 'Estudado' THEN 1 END)::numeric / COUNT(*)::numeric * 100), 2) as percentual
    FROM 
        conteudo_valido
    GROUP BY 
        materia_nome, cor
    ORDER BY 
         materia_nome ASC  -- Ordena alfabeticamente pelo nome da matéria
)
SELECT * FROM estatisticas_finais";

$result = pg_query($conexao, $query);

if (!$result) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Erro ao buscar estatísticas']);
    exit();
}

$estatisticas = array();
while ($row = pg_fetch_assoc($result)) {
    $estatisticas[] = array(
        'materia_nome' => $row['materia_nome'],
        'cor' => $row['cor'],
        'total_itens' => $row['total_itens'],
        'itens_estudados' => $row['itens_estudados'],
        'percentual' => $row['percentual']
    );
}

header('Content-Type: application/json');
echo json_encode($estatisticas);