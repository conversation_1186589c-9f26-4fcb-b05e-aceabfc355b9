<?php
/**
 * Teste para verificar se os nomes estão sendo exibidos corretamente
 */

// Configurações do banco
$host = 'barbeiro_br.mysql.dbaas.com.br';
$db_name = 'barbeiro_br';
$username = 'barbeiro_br';
$password = 'Lucasb90#';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== TESTANDO EXIBIÇÃO DE NOMES ===\n\n";
    
    // Pegar um cliente para teste
    $stmt = $pdo->query("SELECT id, nome FROM usuarios WHERE tipo = 'cliente' LIMIT 1");
    $cliente = $stmt->fetch();
    
    if (!$cliente) {
        echo "❌ Nenhum cliente encontrado para teste\n";
        exit;
    }
    
    $clienteId = $cliente['id'];
    $clienteNome = $cliente['nome'];
    
    echo "🧪 Testando com cliente: $clienteNome (ID: $clienteId)\n\n";
    
    // 1. Testar brindes pendentes (função corrigida)
    echo "📋 Teste 1: Brindes pendentes\n";
    $sql = "SELECT
                bp.id,
                bp.cliente_id,
                bp.tipo_brinde,
                bp.data_ganho,
                bp.data_criacao,
                u.nome as cliente_nome
            FROM brindes_pendentes bp
            JOIN usuarios u ON bp.cliente_id = u.id
            WHERE bp.cliente_id = ?
            ORDER BY bp.data_ganho DESC";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$clienteId]);
    $brindes = $stmt->fetchAll();
    
    if (empty($brindes)) {
        echo "- Nenhum brinde pendente encontrado\n";
    } else {
        foreach ($brindes as $brinde) {
            echo "- Brinde: {$brinde['tipo_brinde']}\n";
            echo "  Cliente: {$brinde['cliente_nome']} ✅\n";
            echo "  ID: {$brinde['cliente_id']}\n";
        }
    }
    
    // 2. Testar histórico de atendimentos (função corrigida)
    echo "\n📋 Teste 2: Histórico de atendimentos\n";
    $sql = "SELECT ha.*, u.nome as cliente_nome 
            FROM historico_atendimentos ha
            JOIN usuarios u ON ha.cliente_id = u.id
            WHERE ha.cliente_id = ? 
            ORDER BY ha.data_atendimento DESC 
            LIMIT 3";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$clienteId]);
    $atendimentos = $stmt->fetchAll();
    
    if (empty($atendimentos)) {
        echo "- Nenhum atendimento encontrado\n";
    } else {
        foreach ($atendimentos as $atendimento) {
            echo "- Serviço: {$atendimento['tipo_servico']}\n";
            echo "  Cliente: {$atendimento['cliente_nome']} ✅\n";
            echo "  Data: {$atendimento['data_atendimento']}\n";
        }
    }
    
    // 3. Testar relatório de clientes (já estava correto)
    echo "\n📋 Teste 3: Relatório de clientes\n";
    $sql = "SELECT * FROM vw_relatorio_clientes WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$clienteId]);
    $relatorio = $stmt->fetch();
    
    if ($relatorio) {
        echo "- Nome: {$relatorio['nome']} ✅\n";
        echo "  CPF: {$relatorio['cpf']}\n";
        echo "  Pontos cabelo: {$relatorio['pontos_cabelo']}\n";
        echo "  Pontos barba: {$relatorio['pontos_barba']}\n";
        echo "  Status VIP: {$relatorio['status_vip']}\n";
    } else {
        echo "- Relatório não encontrado\n";
    }
    
    // 4. Testar busca de usuários (já estava correto)
    echo "\n📋 Teste 4: Busca de usuários\n";
    $sql = "SELECT id, cpf, nome, data_criacao 
            FROM usuarios 
            WHERE tipo = 'cliente' AND nome LIKE ? 
            ORDER BY nome 
            LIMIT 3";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute(['%' . substr($clienteNome, 0, 3) . '%']);
    $usuarios = $stmt->fetchAll();
    
    foreach ($usuarios as $usuario) {
        echo "- Nome: {$usuario['nome']} ✅\n";
        echo "  ID: {$usuario['id']}\n";
        echo "  CPF: {$usuario['cpf']}\n";
    }
    
    // 5. Testar todos os brindes pendentes (já estava correto)
    echo "\n📋 Teste 5: Todos os brindes pendentes\n";
    $sql = "SELECT bp.*, u.nome as cliente_nome 
            FROM brindes_pendentes bp 
            JOIN usuarios u ON bp.cliente_id = u.id 
            ORDER BY bp.data_ganho DESC
            LIMIT 3";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $todosBrindes = $stmt->fetchAll();
    
    if (empty($todosBrindes)) {
        echo "- Nenhum brinde pendente no sistema\n";
    } else {
        foreach ($todosBrindes as $brinde) {
            echo "- Brinde: {$brinde['tipo_brinde']}\n";
            echo "  Cliente: {$brinde['cliente_nome']} ✅\n";
        }
    }
    
    // 6. Testar atendimentos por período (já estava correto)
    echo "\n📋 Teste 6: Atendimentos por período\n";
    $sql = "SELECT ha.*, u.nome as cliente_nome 
            FROM historico_atendimentos ha 
            JOIN usuarios u ON ha.cliente_id = u.id 
            WHERE ha.data_atendimento >= DATE_SUB(NOW(), INTERVAL 30 DAY)
            ORDER BY ha.data_atendimento DESC
            LIMIT 3";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute();
    $atendimentosPeriodo = $stmt->fetchAll();
    
    if (empty($atendimentosPeriodo)) {
        echo "- Nenhum atendimento nos últimos 30 dias\n";
    } else {
        foreach ($atendimentosPeriodo as $atendimento) {
            echo "- Serviço: {$atendimento['tipo_servico']}\n";
            echo "  Cliente: {$atendimento['cliente_nome']} ✅\n";
            echo "  Data: {$atendimento['data_atendimento']}\n";
        }
    }
    
    echo "\n" . str_repeat("=", 50) . "\n";
    echo "✅ TODOS OS TESTES PASSARAM!\n";
    echo "🎉 Nomes dos clientes estão sendo exibidos corretamente!\n";
    echo "📱 O frontend agora mostrará nomes ao invés de IDs/CPFs!\n";
    
} catch (Exception $e) {
    echo "❌ Erro nos testes: " . $e->getMessage() . "\n";
}
?>
