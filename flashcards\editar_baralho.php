<?php
// editar_baralho.php
session_start();
include_once("assets/config.php");
include_once("helpers.php");

if (!isset($_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit();
}

redirectIfNotAdmin($conexao, $_SESSION['idusuario']);

if (!isset($_GET['id'])) {
    header("Location: flashcards.php");
    exit();
}

$baralho_id = (int)$_GET['id'];
$mensagem = '';

// Buscar informações do baralho
$query_baralho = "
    SELECT 
        d.*,
        c.nome as categoria_nome,
        c.id as categoria_id
    FROM appestudo.flashcard_decks d
    JOIN appestudo.flashcard_categories c ON c.id = d.category_id
    WHERE d.id = $1";
$result_baralho = pg_query_params($conexao, $query_baralho, array($baralho_id));
$baralho = pg_fetch_assoc($result_baralho);

if (!$baralho) {
    header("Location: flashcards.php");
    exit();
}

// Buscar todas as matérias disponíveis
$query_materias = "SELECT idmateria, nome FROM appestudo.materia ORDER BY nome";
$result_materias = pg_query($conexao, $query_materias);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $materia_id = (int)$_POST['materia_id'];
    $status = isset($_POST['status']) ? true : false;
    
    if ($materia_id > 0) {
        // Verificar se já existe outro baralho para esta matéria nesta categoria
        $query_check = "
            SELECT id FROM appestudo.flashcard_decks 
            WHERE category_id = $1 AND materia_id = $2 AND id != $3";
        $result_check = pg_query_params($conexao, $query_check, array(
            $baralho['category_id'],
            $materia_id,
            $baralho_id
        ));
        
        if (pg_num_rows($result_check) > 0) {
            $mensagem = "Já existe um baralho para esta matéria nesta categoria.";
        } else {
            // Buscar nome da matéria
            $query_materia = "SELECT nome FROM appestudo.materia WHERE idmateria = $1";
            $result_materia = pg_query_params($conexao, $query_materia, array($materia_id));
            $materia = pg_fetch_assoc($result_materia);
            
            $query = "
                UPDATE appestudo.flashcard_decks 
                SET materia_id = $1, nome = $2, status = $3
                WHERE id = $4";
            
            $result = pg_query_params($conexao, $query, array(
                $materia_id,
                $materia['nome'],
                $status,
                $baralho_id
            ));
            
            if ($result) {
                header("Location: ver_baralhos.php?categoria=" . $baralho['category_id']);
                exit();
            } else {
                $mensagem = "Erro ao atualizar baralho. Tente novamente.";
            }
        }
    } else {
        $mensagem = "Selecione uma matéria válida.";
    }
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Baralho - <?php echo htmlspecialchars($baralho['nome']); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Quicksand:wght@300..700&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Tektur:wght@400..900&family=Varela+Round&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #000080;
            --paper-color: #FFFFFF;
            --secondary-color: #4169E1;
            --background-color: #E8ECF3;
            --text-color: #2C3345;
            --border-color: #B8C2CC;
            --success-color: #000080;
        }

        body {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 40px;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .form-container {
            background: var(--paper-color);
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .breadcrumb {
            color: var(--secondary-color);
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 1.1rem;
            font-weight: 500;
        }

        h1 {
            color: var(--primary-color);
            margin-bottom: 30px;
            font-size: 1.8rem;
        }

        .form-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            color: var(--primary-color);
            margin-bottom: 8px;
            font-weight: 500;
        }

        select {
            width: 100%;
            padding: 12px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-family: inherit;
            font-size: 1rem;
            color: var(--text-color);
            background-color: white;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        select:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(0,0,128,0.1);
        }

        .switch-container {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: var(--success-color);
        }

        input:focus + .slider {
            box-shadow: 0 0 1px var(--success-color);
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        .switch-text {
            flex: 1;
        }

        .switch-label {
            font-size: 1rem;
            font-weight: 500;
        }

        .switch-description {
            font-size: 0.85rem;
            color: #666;
            margin-top: 4px;
        }

        .status-icon {
            margin-right: 6px;
            font-size: 1.1rem;
        }

        .status-active {
            color: var(--success-color);
        }

        .status-inactive {
            color: #dc3545;
        }

        .actions {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-family: inherit;
            font-size: 1rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,128,0.15);
        }

        .btn-secondary {
            background: white;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .error-message {
            background: #FEE;
            border: 1px solid #FAA;
            color: #A00;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .error-message i {
            font-size: 1.2rem;
        }

        .btn-back {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 50px;
            height: 50px;
            background: white;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.2s;
            z-index: 1000;
        }

        .btn-back:hover {
            transform: translateY(-2px);
            background: var(--primary-color);
            color: white;
        }

        @media (max-width: 768px) {
            body {
                padding: 20px;
            }

            .actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <a href="ver_baralhos.php?categoria=<?php echo $baralho['category_id']; ?>" class="btn-back">
        <i class="fas fa-arrow-left"></i>
    </a>

    <div class="container">
        <div class="form-container">
            <div class="breadcrumb">
                <i class="fas fa-layer-group"></i>
                <span><?php echo htmlspecialchars($baralho['categoria_nome']); ?></span>
            </div>

            <h1>Editar Baralho</h1>

            <?php if (!empty($mensagem)): ?>
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($mensagem); ?>
                </div>
            <?php endif; ?>

            <form method="POST">
                <div class="form-group">
                    <label for="materia_id">Selecione a Matéria*</label>
                    <select id="materia_id" name="materia_id" required>
                        <option value="">Selecione uma matéria...</option>
                        <?php while ($materia = pg_fetch_assoc($result_materias)): ?>
                            <option value="<?php echo $materia['idmateria']; ?>"
                                    <?php echo $materia['idmateria'] == $baralho['materia_id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($materia['nome']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>

                <div class="form-group">
                    <div class="switch-container">
                        <div class="switch-text">
                            <div class="switch-label">
                                <i class="fas fa-circle status-icon <?php echo $baralho['status'] ? 'status-active' : 'status-inactive'; ?>"></i>
                                Status do Baralho
                            </div>
                            <div class="switch-description">
                                <?php echo $baralho['status'] ? 
                                    'O baralho está visível para estudos' : 
                                    'O baralho está oculto para estudos'; ?>
                            </div>
                        </div>
                        <label class="switch">
                            <input type="checkbox" name="status" <?php echo $baralho['status'] ? 'checked' : ''; ?>>
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>

                <div class="actions">
                    <a href="ver_baralhos.php?categoria=<?php echo $baralho['category_id']; ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Salvar Alterações
                    </button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>