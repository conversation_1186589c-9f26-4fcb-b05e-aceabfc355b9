/**
 * Sistema Multi-Leis - Gerenciador Principal
 * Coordena o carregamento dinâmico de diferentes leis
 */

class SistemaMultiLeis {
    constructor() {
        console.log('SistemaMultiLeis constructor called');
        this.leiAtual = null;
        this.artigos = [];
        this.leiInfo = null;
        this.carregando = false;
        
        this.init();
    }
    
    init() {
        // Zerar interface imediatamente ao inicializar
        this.zerarEstatisticasInterface();

        this.bindEventos();
        this.carregarLeiAtual();
    }
    
    /**
     * Vincula eventos do sistema
     */
    bindEventos() {
        // Eventos removidos - sistema agora usa página inicial para seleção de lei
        console.log('✅ Sistema de seleção de lei movido para página inicial');
    }
    
    /**
     * Carrega a lei atual do usuário
     */
    async carregarLeiAtual() {
        try {
            this.mostrarLoading('Carregando lei atual...');

            // Verificar primeiro se há parâmetro ?lei= na URL
            const urlParams = new URLSearchParams(window.location.search);
            const leiURL = urlParams.get('lei');

            if (leiURL) {
                // Se há parâmetro na URL, usar ele (prioridade máxima)
                console.log(`🎯 Lei especificada na URL: ${leiURL}`);
                this.leiAtual = leiURL;
                await this.carregarLei(leiURL);
                return;
            }

            // Se não há parâmetro na URL, consultar preferência do usuário
            const response = await fetch('api/leis.php?acao=atual');
            const data = await response.json();

            if (data.sucesso) {
                console.log(`📋 Lei das preferências do usuário: ${data.lei_atual.lei_atual}`);
                this.leiAtual = data.lei_atual.lei_atual;
                await this.carregarLei(this.leiAtual);
            } else {
                // Se não tem lei atual, usar CF como padrão
                console.log(`🔧 Usando lei padrão: CF`);
                this.leiAtual = 'CF';
                await this.carregarLei('CF');
            }
        } catch (error) {
            console.error('Erro ao carregar lei atual:', error);
            this.mostrarErro('Erro ao carregar lei atual');
        }
    }

    /**
     * Limpa completamente o localStorage de dados do LexJus
     */
    limparLocalStorageCompleto() {
        console.log('🧹 Limpando localStorage completo...');

        // Lista de todas as chaves possíveis do LexJus
        const chavesLexJus = [
            // Chaves antigas (globais)
            'artigosLidos',
            'favoritos',
            'listas',
            'notasArtigos',
            'lexjus_artigos_lidos',
            'lexjus_favoritos',
            'lexjus_listas',
            'lexjus_notas',

            // Chaves específicas por lei
            'artigosLidos_CF', 'artigosLidos_CC', 'artigosLidos_CPC', 'artigosLidos_CP', 'artigosLidos_CPP',
            'lexjus_artigos_lidos_CF', 'lexjus_artigos_lidos_CC', 'lexjus_artigos_lidos_CPC', 'lexjus_artigos_lidos_CP', 'lexjus_artigos_lidos_CPP',
            'favoritos_CF', 'favoritos_CC', 'favoritos_CPC', 'favoritos_CP', 'favoritos_CPP',
            'listas_CF', 'listas_CC', 'listas_CPC', 'listas_CP', 'listas_CPP',
            'notasArtigos_CF', 'notasArtigos_CC', 'notasArtigos_CPC', 'notasArtigos_CP', 'notasArtigos_CPP'
        ];

        // Remover todas as chaves conhecidas
        let removidas = 0;
        chavesLexJus.forEach(chave => {
            if (localStorage.getItem(chave) !== null) {
                localStorage.removeItem(chave);
                removidas++;
            }
        });

        // Buscar e remover qualquer chave que contenha 'lexjus', 'artigos', 'favoritos', etc.
        const todasChaves = Object.keys(localStorage);
        const chavesRelacionadas = todasChaves.filter(chave =>
            chave.toLowerCase().includes('lexjus') ||
            chave.toLowerCase().includes('artigos') ||
            chave.toLowerCase().includes('favoritos') ||
            chave.toLowerCase().includes('listas') ||
            chave.toLowerCase().includes('notas') ||
            chave.toLowerCase().includes('revisao')
        );

        chavesRelacionadas.forEach(chave => {
            if (!chavesLexJus.includes(chave)) {
                localStorage.removeItem(chave);
                removidas++;
                console.log(`🧹 Chave adicional removida: ${chave}`);
            }
        });

        console.log(`✅ localStorage limpo: ${removidas} chaves removidas`);
    }

    /**
     * Reseta o estado atual da lei
     */
    resetarEstado() {
        console.log('🔄 Resetando estado da lei anterior...');

        // LIMPAR COMPLETAMENTE O LOCALSTORAGE DE DADOS DO LEXJUS
        this.limparLocalStorageCompleto();

        // Limpar a grade de artigos
        const container = document.getElementById('gridContainer');
        if (container) {
            container.innerHTML = '<div class="loading-artigos"><div class="loading-spinner"></div><p>Carregando nova lei...</p></div>';
        }

        // Resetar propriedades internas
        this.artigos = [];
        this.leiInfo = null;

        // Zerar estatísticas na interface imediatamente
        this.zerarEstatisticasInterface();

        // Limpar variáveis globais do sistema principal (com verificação de segurança)
        if (window.artigosLidos && typeof window.artigosLidos.clear === 'function') {
            window.artigosLidos.clear();
            console.log('🧹 window.artigosLidos limpo');
        } else if (window.artigosLidos) {
            window.artigosLidos = new Set();
            console.log('🧹 window.artigosLidos reinicializado');
        }

        if (window.favoritos && typeof window.favoritos.clear === 'function') {
            window.favoritos.clear();
            console.log('🧹 window.favoritos limpo');
        } else if (window.favoritos) {
            window.favoritos = new Set();
            console.log('🧹 window.favoritos reinicializado');
        }

        if (window.listas) {
            window.listas = {};
            console.log('🧹 window.listas limpo');
        }

        if (window.notasArtigos) {
            window.notasArtigos = {};
            console.log('🧹 window.notasArtigos limpo');
        }

        // Disparar um evento para notificar outros scripts a limparem seus estados
        document.dispatchEvent(new CustomEvent('leiResetada'));
        console.log('✅ Estado resetado e evento "leiResetada" disparado.');
    }

    /**
     * Carrega uma lei específica
     */
    async carregarLei(codigoLei) {
        if (this.carregando) return;

        // Resetar o estado antes de carregar a nova lei
        this.resetarEstado();
        
        try {
            this.carregando = true;
            this.mostrarLoading(`Carregando ${codigoLei}...`);
            
            const response = await fetch(`api/leis.php?acao=carregar&codigo=${codigoLei}`);
            const data = await response.json();
            
            if (data.sucesso) {
                console.log(`📊 Dados recebidos da API para ${codigoLei}:`, data.lei);

                this.leiInfo = data.lei;
                this.artigos = data.artigos;
                this.leiAtual = codigoLei;

                // CORREÇÃO: Atualizar data attributes do body para nova lei
                console.log(`🔄 Atualizando data attributes...`);
                console.log(`   - Antes: codigo=${document.body.getAttribute('data-lei-codigo')}, id=${document.body.getAttribute('data-lei-id')}`);

                document.body.setAttribute('data-lei-codigo', data.lei.codigo);
                document.body.setAttribute('data-lei-id', data.lei.id);
                document.body.setAttribute('data-lei-nome', data.lei.nome);

                console.log(`   - Depois: codigo=${document.body.getAttribute('data-lei-codigo')}, id=${document.body.getAttribute('data-lei-id')}`);
                console.log(`✅ Data attributes atualizados: lei=${data.lei.codigo}, id=${data.lei.id}`);

                // VERIFICAÇÃO EXTRA: Confirmar que data attributes foram realmente atualizados
                setTimeout(() => {
                    const verificacaoCodigo = document.body.getAttribute('data-lei-codigo');
                    const verificacaoId = document.body.getAttribute('data-lei-id');

                    if (verificacaoCodigo !== data.lei.codigo || verificacaoId != data.lei.id) {
                        console.error(`🚨 ERRO: Data attributes não foram atualizados corretamente!`);
                        console.error(`   Esperado: codigo=${data.lei.codigo}, id=${data.lei.id}`);
                        console.error(`   Atual: codigo=${verificacaoCodigo}, id=${verificacaoId}`);

                        // Tentar forçar novamente
                        document.body.setAttribute('data-lei-codigo', data.lei.codigo);
                        document.body.setAttribute('data-lei-id', data.lei.id);
                        document.body.setAttribute('data-lei-nome', data.lei.nome);

                        console.log(`🔄 FORÇADO: Data attributes atualizados novamente`);
                    } else {
                        console.log(`✅ CONFIRMADO: Data attributes estão corretos`);
                    }
                }, 100);

                this.atualizarInterface();
                this.renderizarArtigos();
                this.atualizarTema();

                // Notificar outros sistemas
                this.notificarMudancaLei();
                
            } else {
                throw new Error(data.erro || 'Erro ao carregar lei');
            }
        } catch (error) {
            console.error('Erro ao carregar lei:', error);
            this.mostrarErro(`Erro ao carregar ${codigoLei}`);
        } finally {
            this.carregando = false;
            this.ocultarLoading();
        }
    }
    
    /**
     * Atualiza a interface com informações da lei
     */
    atualizarInterface() {
        if (!this.leiInfo) return;

        // Atualizar título e subtítulo
        const titulo = document.getElementById('leiTitulo');
        const subtitulo = document.getElementById('leiSubtitulo');
        const breadcrumb = document.getElementById('breadcrumbLei');

        if (titulo) {
            titulo.textContent = this.leiInfo.nome;
            titulo.style.color = this.leiInfo.cor_tema;
        }

        if (subtitulo) {
            subtitulo.textContent = this.leiInfo.nome_completo;
        }

        if (breadcrumb) {
            breadcrumb.textContent = this.leiInfo.nome;
        }
        
        // Atualizar ícone da marca
        const navBrand = document.querySelector('.nav-brand i');
        if (navBrand) {
            navBrand.className = this.leiInfo.icone;
        }
        
        // Atualizar título da página
        document.title = `LexJus - ${this.leiInfo.nome}`;
        
        // Atualizar estatísticas
        this.atualizarEstatisticas();
    }
    
    /**
     * Renderiza os artigos da lei atual
     */
    renderizarArtigos() {
        const container = document.getElementById('gridContainer');
        if (!container || !this.artigos) return;

        // Limpar container
        container.innerHTML = '';

        // Renderizar cada artigo
        this.artigos.forEach((artigo, index) => {
            const card = this.criarCardArtigo(artigo, index);
            container.appendChild(card);
        });

        // Animar entrada dos cards
        this.animarEntradaCards();

        // Reinicializar funcionalidades dos cards
        this.inicializarEventListenersCards();

        // Forçar remoção de classes de loading após um pequeno delay
        setTimeout(() => {
            this.limparClassesLoading();
        }, 100);
    }
    
    /**
     * Cria um card de artigo
     */
    criarCardArtigo(artigo, index) {
        const card = document.createElement('div');
        card.className = 'card';
        card.style.animationDelay = `${index * 0.05}s`;
        
        // Preparar dados para atributos
        const numeroArtigo = artigo.artigo || 'Artigo não encontrado';
        const caputArtigo = artigo.caput || '';
        const incisosJson = JSON.stringify(artigo.incisos || []);
        const paragrafoUnico = artigo.paragrafo_unico || '';
        const paragrafosNumerados = JSON.stringify(artigo.paragrafos_numerados || []);
        const alineasArtigo = JSON.stringify(artigo.alineas_do_artigo || []);
        
        // Definir atributos data-*
        card.setAttribute('data-artigo', numeroArtigo);
        card.setAttribute('data-caput', caputArtigo);
        card.setAttribute('data-incisos', incisosJson);
        card.setAttribute('data-paragrafo-unico', paragrafoUnico);
        card.setAttribute('data-paragrafos-numerados', paragrafosNumerados);
        card.setAttribute('data-alineas-artigo', alineasArtigo);
        
        // Conteúdo do card - incluir texto do caput
        const caputPreview = caputArtigo.length > 100 ?
            caputArtigo.substring(0, 100) + '...' :
            caputArtigo;

        card.innerHTML = `
            <h2>${numeroArtigo}</h2>
            <p class="card-preview">${caputPreview}</p>
        `;
        
        return card;
    }
    
    /**
     * Anima a entrada dos cards
     */
    animarEntradaCards() {
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            // Remover classes de loading que podem estar aplicadas
            card.classList.remove('loading', 'card-loading');

            // NÃO aplicar animação que deixa cards transparentes
            // Garantir que os cards estejam visíveis
            card.style.opacity = '1';
            card.style.visibility = 'visible';
            card.style.transform = '';
            card.style.transition = '';
        });

        console.log('Animação de entrada aplicada a', cards.length, 'cards');
    }

    /**
     * Inicializa event listeners dos cards
     */
    inicializarEventListenersCards() {
        const cards = document.querySelectorAll('.card');

        cards.forEach((card, index) => {
            // Remover listeners existentes
            card.replaceWith(card.cloneNode(true));
        });

        // Reobter cards após clonagem
        const cardsAtualizados = document.querySelectorAll('.card');

        cardsAtualizados.forEach((card, index) => {
            // Garantir que não há classes de loading
            card.classList.remove('loading', 'card-loading');

            // Event listener para abrir modal
            card.addEventListener('click', () => {
                if (window.abrirModalComNavegacao) {
                    window.abrirModalComNavegacao(index);
                } else {
                    // Aguardar função estar disponível
                    const aguardarFuncao = () => {
                        if (window.abrirModalComNavegacao) {
                            window.abrirModalComNavegacao(index);
                        } else {
                            setTimeout(aguardarFuncao, 100);
                        }
                    };
                    aguardarFuncao();
                }
            });
        });

        // Notificar script.js para atualizar referências
        const evento = new CustomEvent('cardsAtualizados', {
            detail: { cards: cardsAtualizados }
        });
        document.dispatchEvent(evento);
    }

    /**
     * Limpa classes de loading dos cards
     */
    limparClassesLoading() {
        const cards = document.querySelectorAll('.card');
        cards.forEach(card => {
            card.classList.remove('loading', 'card-loading');

            // Forçar visibilidade dos cards
            card.style.opacity = '1 !important';
            card.style.visibility = 'visible !important';
            card.style.display = 'flex !important';

            // Remover estilos inline após um tempo
            setTimeout(() => {
                card.style.opacity = '';
                card.style.visibility = '';
                card.style.display = '';
            }, 1000);
        });

        console.log('Classes de loading removidas de', cards.length, 'cards');

        // Debug: verificar estado dos cards
        this.debugCardsState();
    }

    /**
     * Debug: verifica estado dos cards
     */
    debugCardsState() {
        const cards = document.querySelectorAll('.card');
        console.log('=== DEBUG CARDS STATE ===');
        console.log('Total de cards:', cards.length);

        cards.forEach((card, index) => {
            const classes = Array.from(card.classList);
            const opacity = window.getComputedStyle(card).opacity;
            const visibility = window.getComputedStyle(card).visibility;

            if (opacity !== '1' || visibility !== 'visible') {
                console.log(`Card ${index}:`, {
                    classes: classes,
                    opacity: opacity,
                    visibility: visibility,
                    inlineStyle: card.style.cssText
                });
            }
        });
        console.log('=== FIM DEBUG ===');
    }
    
    /**
     * Atualiza o tema da interface
     */
    atualizarTema() {
        if (!this.leiInfo) return;
        
        const root = document.documentElement;
        root.style.setProperty('--cor-primaria', this.leiInfo.cor_tema);
        
        // Atualizar meta theme-color
        let metaTheme = document.querySelector('meta[name="theme-color"]');
        if (!metaTheme) {
            metaTheme = document.createElement('meta');
            metaTheme.name = 'theme-color';
            document.head.appendChild(metaTheme);
        }
        metaTheme.content = this.leiInfo.cor_tema;
    }

    /**
     * Zera imediatamente todas as estatísticas na interface
     */
    zerarEstatisticasInterface() {
        console.log('🔄 Zerando estatísticas da interface...');

        // Zerar contadores principais
        const totalArtigos = document.getElementById('totalArtigos');
        const artigosLidos = document.getElementById('artigosLidos');
        const progressText = document.getElementById('progressText');
        const progressFill = document.getElementById('progressFill');

        if (totalArtigos) totalArtigos.textContent = '0';
        if (artigosLidos) artigosLidos.textContent = '0';
        if (progressText) progressText.textContent = '0 de 0 artigos lidos (0%)';
        if (progressFill) progressFill.style.width = '0%';

        // Zerar contadores da navegação
        const favoritosCount = document.getElementById('favoritosCount');
        const listasCount = document.getElementById('listasCount');
        const revisaoCount = document.getElementById('revisaoCount');

        if (favoritosCount) favoritosCount.textContent = '0';
        if (listasCount) listasCount.textContent = '0';
        if (revisaoCount) revisaoCount.textContent = '0';

        // Desabilitar botão de revisão temporariamente
        const btnRevisao = document.getElementById('btnRevisao');
        if (btnRevisao) {
            btnRevisao.style.opacity = '0.5';
            btnRevisao.title = 'Carregando...';
        }

        console.log('✅ Estatísticas zeradas na interface');
    }

    /**
     * Atualiza estatísticas da lei
     */
    async atualizarEstatisticas() {
        try {
            // ZERAR DADOS IMEDIATAMENTE antes de carregar novos dados
            this.zerarEstatisticasInterface();

            const response = await fetch(`api/leis.php?acao=estatisticas&codigo=${this.leiAtual}`);
            const data = await response.json();
            
            if (data.sucesso) {
                const stats = data.estatisticas;
                
                // Atualizar contadores
                const totalArtigos = document.getElementById('totalArtigos');
                const artigosLidos = document.getElementById('artigosLidos');
                const progressText = document.getElementById('progressText');
                const progressFill = document.getElementById('progressFill');
                
                if (totalArtigos) totalArtigos.textContent = stats.lei.total_artigos;
                if (artigosLidos) artigosLidos.textContent = stats.progresso.lidos;
                
                if (progressText) {
                    progressText.textContent = `${stats.progresso.lidos} de ${stats.lei.total_artigos} artigos lidos (${stats.progresso.percentual}%)`;
                }
                
                if (progressFill) {
                    progressFill.style.width = `${stats.progresso.percentual}%`;
                }
                
                // Atualizar contadores da navegação
                const favoritosCount = document.getElementById('favoritosCount');
                const listasCount = document.getElementById('listasCount');
                const revisaoCount = document.getElementById('revisaoCount');
                
                if (favoritosCount) favoritosCount.textContent = stats.favoritos;
                if (listasCount) listasCount.textContent = stats.listas;
                if (revisaoCount) revisaoCount.textContent = stats.revisao.pendentes;
                
                // Restaurar e configurar botão de revisão
                const btnRevisao = document.getElementById('btnRevisao');
                if (btnRevisao) {
                    btnRevisao.style.display = 'flex';

                    // Se não há artigos na revisão, desabilitar o botão
                    if (stats.revisao.total === 0) {
                        btnRevisao.classList.add('disabled');
                        btnRevisao.style.opacity = '0.5';
                        btnRevisao.style.cursor = 'not-allowed';
                        btnRevisao.title = 'Nenhum artigo na revisão';
                    } else {
                        btnRevisao.classList.remove('disabled');
                        btnRevisao.style.opacity = '1';
                        btnRevisao.style.cursor = 'pointer';
                        btnRevisao.title = 'Sistema de Revisão';
                    }
                }

                console.log(`✅ Estatísticas atualizadas: ${stats.progresso.lidos}/${stats.lei.total_artigos} artigos lidos`);
            }
        } catch (error) {
            console.error('Erro ao carregar estatísticas:', error);
        }
    }
    
    /**
     * Notifica outros sistemas sobre mudança de lei
     */
    notificarMudancaLei() {
        // Disparar evento customizado
        const evento = new CustomEvent('leiAlterada', {
            detail: {
                leiAnterior: this.leiAtual,
                leiAtual: this.leiAtual,
                leiInfo: this.leiInfo,
                artigos: this.artigos
            }
        });
        
        document.dispatchEvent(evento);
        
        // Notificar sistema de revisão se existir
        if (window.sistemaRevisao) {
            window.sistemaRevisao.onLeiAlterada(this.leiAtual);
        }
    }
    
    /**
     * Mostra loading
     */
    mostrarLoading(mensagem = 'Carregando...') {
        const loading = document.getElementById('globalLoading');
        const status = document.getElementById('loadingStatus');
        
        if (loading) loading.style.display = 'flex';
        if (status) status.textContent = mensagem;
    }
    
    /**
     * Oculta loading
     */
    ocultarLoading() {
        const loading = document.getElementById('globalLoading');
        if (loading) {
            setTimeout(() => {
                loading.style.display = 'none';
            }, 500);
        }
    }
    
    /**
     * Mostra erro
     */
    mostrarErro(mensagem) {
        console.error(mensagem);
        
        // Criar notificação de erro
        const notificacao = document.createElement('div');
        notificacao.className = 'notificacao-erro';
        notificacao.innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            <span>${mensagem}</span>
        `;
        
        document.body.appendChild(notificacao);
        
        setTimeout(() => {
            notificacao.remove();
        }, 5000);
    }
    
    /**
     * Obtém informações da lei atual
     */
    getLeiAtual() {
        return {
            codigo: this.leiAtual,
            info: this.leiInfo,
            artigos: this.artigos
        };
    }
    
    /**
     * Obtém artigo por número
     */
    getArtigo(numeroArtigo) {
        return this.artigos.find(artigo => artigo.artigo === numeroArtigo);
    }
}

// Instância global
const sistemaMultiLeis = new SistemaMultiLeis();

// Disponibilizar globalmente
window.sistemaMultiLeis = sistemaMultiLeis;
console.log('window.sistemaMultiLeis is now available', window.sistemaMultiLeis);

// Atualização: 2025-06-28 00:15:00 - REMOVIDO sistema de preferências de lei padrão

// Exportar para uso em outros módulos (se aplicável, para ambientes Node.js, etc.)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SistemaMultiLeis;
}
