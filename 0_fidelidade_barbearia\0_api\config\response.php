<?php
/**
 * Classe para padronizar as respostas da API
 * Sistema de Fidelidade da Barbearia
 */

class ApiResponse {
    
    /**
     * Enviar resposta de sucesso
     */
    public static function success($data = null, $message = 'Operação realizada com sucesso', $code = 200) {
        http_response_code($code);
        
        $response = [
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * Enviar resposta de erro
     */
    public static function error($message = 'Erro interno do servidor', $code = 500, $details = null) {
        http_response_code($code);
        
        $response = [
            'success' => false,
            'message' => $message,
            'error_code' => $code,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        if ($details !== null) {
            $response['details'] = $details;
        }
        
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * Enviar resposta de validação
     */
    public static function validation($errors, $message = 'Dados inválidos') {
        self::error($message, 422, $errors);
    }
    
    /**
     * Enviar resposta de não encontrado
     */
    public static function notFound($message = 'Recurso não encontrado') {
        self::error($message, 404);
    }
    
    /**
     * Enviar resposta de não autorizado
     */
    public static function unauthorized($message = 'Acesso não autorizado') {
        self::error($message, 401);
    }
    
    /**
     * Enviar resposta de método não permitido
     */
    public static function methodNotAllowed($message = 'Método não permitido') {
        self::error($message, 405);
    }
}

/**
 * Classe para validação de dados
 */
class Validator {
    
    /**
     * Validar CPF
     */
    public static function validateCPF($cpf) {
        $cpf = preg_replace('/\D/', '', $cpf);
        
        if (strlen($cpf) !== 11) {
            return false;
        }
        
        // Verificar se todos os dígitos são iguais
        if (preg_match('/(\d)\1{10}/', $cpf)) {
            return false;
        }
        
        // Validar dígitos verificadores
        for ($t = 9; $t < 11; $t++) {
            for ($d = 0, $c = 0; $c < $t; $c++) {
                $d += $cpf[$c] * (($t + 1) - $c);
            }
            $d = ((10 * $d) % 11) % 10;
            if ($cpf[$c] != $d) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Validar senha
     */
    public static function validatePassword($password) {
        return strlen($password) >= 6;
    }
    
    /**
     * Validar nome
     */
    public static function validateName($name) {
        return strlen(trim($name)) >= 2;
    }
    
    /**
     * Sanitizar string
     */
    public static function sanitizeString($string) {
        return trim(htmlspecialchars($string, ENT_QUOTES, 'UTF-8'));
    }
}

/**
 * Configurar headers CORS
 */
function setupCORS() {
    // Permitir requisições de qualquer origem (ajustar em produção)
    header("Access-Control-Allow-Origin: *");
    header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
    header("Access-Control-Allow-Headers: Content-Type, Authorization, X-Requested-With");
    
    // Responder a requisições OPTIONS
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit;
    }
}

// Configurar CORS automaticamente
setupCORS();
?>
