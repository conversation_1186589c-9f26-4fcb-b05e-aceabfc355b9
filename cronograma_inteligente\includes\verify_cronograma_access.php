<?php
// includes/verify_cronograma_access.php

function verificarAcessoCronograma($conexao, $usuario_id) {
    $query = "
        SELECT 
            u.cronograma_inteligente,
            a.data_fim,
            a.tipo_assinatura,
            a.status,
            CURRENT_TIMESTAMP < COALESCE(a.data_fim, CURRENT_TIMESTAMP - INTERVAL '1 day') as assinatura_vigente
        FROM appEstudo.usuario u
        LEFT JOIN (
            SELECT * FROM appestudo.assinaturas 
            WHERE usuario_id = $1
            AND modulo = 'cronograma_inteligente'
            AND status = true 
            ORDER BY data_fim DESC 
            LIMIT 1
        ) a ON a.usuario_id = u.idusuario 
        WHERE u.idusuario = $1";
    
    $resultado = pg_query_params($conexao, $query, array($usuario_id));
    
    if (!$resultado) {
        return [
            'acesso' => false,
            'mensagem' => 'Erro ao consultar banco de dados.'
        ];
    }

    if (pg_num_rows($resultado) == 0) {
        return [
            'acesso' => false,
            'mensagem' => 'Usuário não encontrado.'
        ];
    }

    $row = pg_fetch_assoc($resultado);
    
    // Se não tem registro de assinatura
    if ($row['data_fim'] === null) {
        return [
            'acesso' => false,
            'mensagem' => 'Você ainda não possui uma assinatura.'
        ];
    }
    
    // Se não tem acesso ao módulo
    if (!$row['cronograma_inteligente']) {
        return [
            'acesso' => false,
            'mensagem' => 'Você não tem acesso a este módulo.'
        ];
    }
    
    // Se a assinatura não está vigente
    if (!$row['assinatura_vigente'] || !$row['status']) {
        return [
            'acesso' => false,
            'mensagem' => 'Sua assinatura expirou em ' . date('d/m/Y', strtotime($row['data_fim'])),
            'tipo' => 'expirado',
            'data_fim' => $row['data_fim']
        ];
    }
    
    // Verifica se está próximo do vencimento (7 dias)
    $data_fim = new DateTime($row['data_fim']);
    $hoje = new DateTime();
    $diferenca = $hoje->diff($data_fim);
    
    if ($diferenca->days <= 7) {
        return [
            'acesso' => true,
            'mensagem' => 'Sua assinatura expira em ' . $diferenca->days . ' dias',
            'tipo' => 'alerta_vencimento',
            'data_fim' => $row['data_fim']
        ];
    }
    
    return [
        'acesso' => true,
        'tipo_assinatura' => $row['tipo_assinatura'],
        'data_fim' => $row['data_fim']
    ];
}