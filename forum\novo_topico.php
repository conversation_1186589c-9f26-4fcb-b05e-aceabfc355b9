<?php
include_once("../session_config.php");
include_once("../assets/config.php");

if (!verificarSessaoValida()) {
    header("Location: ../login_index.php");
    exit();
}

$categoria_id = (int)$_GET['categoria'];

// Buscar informações da categoria
$query_categoria = "SELECT * FROM appestudo.forum_categorias WHERE id = $1 AND status = true";
$result_categoria = pg_query_params($conexao, $query_categoria, array($categoria_id));
$categoria = pg_fetch_assoc($result_categoria);

if (!$categoria) {
    header("Location: index.php");
    exit();
}

// Buscar nome do usuário
$usuario_id = $_SESSION['idusuario'];
$query_usuario = "SELECT nome FROM appestudo.usuario WHERE idusuario = $1";
$result_usuario = pg_query_params($conexao, $query_usuario, array($usuario_id));
$row = pg_fetch_assoc($result_usuario);
$nome_usuario = $row ? $row['nome'] : 'Usuário';

$mensagem = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $titulo = trim($_POST['titulo']);
    $conteudo = trim($_POST['conteudo']);
    
    if (!empty($titulo) && !empty($conteudo)) {
        $query = "INSERT INTO appestudo.forum_topicos 
                 (categoria_id, usuario_id, titulo, conteudo) 
                 VALUES ($1, $2, $3, $4) RETURNING id";
        
        $result = pg_query_params($conexao, $query, array(
            $categoria_id,
            $_SESSION['idusuario'],
            $titulo,
            $conteudo
        ));
        
        if ($result) {
            $topico = pg_fetch_assoc($result);
            header("Location: ver_topico.php?id=" . $topico['id']);
            exit();
        } else {
            $mensagem = "Erro ao criar tópico. Tente novamente.";
        }
    } else {
        $mensagem = "Título e conteúdo são obrigatórios.";
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Novo Tópico - <?php echo htmlspecialchars($categoria['nome']); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Varela+Round&family=Quicksand:wght@300..700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/forum.css">
    <!-- Substitua pela sua chave real do TinyMCE -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.7.2/tinymce.min.js"></script>
    <script>
        // Definição do idioma português do Brasil
        tinymce.addI18n('pt_BR', {
            "Paragraph": "Parágrafo",
            "Heading 1": "Título 1",
            "Heading 2": "Título 2",
            "Heading 3": "Título 3",
            "Heading 4": "Título 4",
            "Heading 5": "Título 5",
            "Heading 6": "Título 6",
            "Bold": "Negrito",
            "Italic": "Itálico",
            "Underline": "Sublinhado",
            "Strikethrough": "Tachado",
            "Align left": "Alinhar à esquerda",
            "Align center": "Centralizar",
            "Align right": "Alinhar à direita",
            "Justify": "Justificar",
            "Bullet list": "Lista não ordenada",
            "Numbered list": "Lista ordenada",
            "Decrease indent": "Diminuir recuo",
            "Increase indent": "Aumentar recuo",
            "Insert/edit link": "Inserir/editar link",
            "Remove link": "Remover link",
            "Select all": "Selecionar tudo",
            "Undo": "Desfazer",
            "Redo": "Refazer",
            "Text color": "Cor do texto",
            "Background color": "Cor de fundo",
            "Custom...": "Personalizar..."
        });

        tinymce.init({
            selector: '#conteudo',
            height: 300,
            language: 'pt_BR',
            menubar: false,
            branding: false,
            promotion: false,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'charmap',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'table', 'wordcount'
            ],
            toolbar: 'undo redo | styles | bold italic | alignleft aligncenter alignright alignjustify | ' +
                    'bullist numlist | link | forecolor backcolor',
            content_style: 'body { font-family: "Varela Round", sans-serif; font-size: 14px }',
            style_formats: [
                { title: 'Parágrafo', format: 'p' },
                { title: 'Título 1', format: 'h1' },
                { title: 'Título 2', format: 'h2' },
                { title: 'Título 3', format: 'h3' }
            ],
            // Nova configuração de cores para TinyMCE 6
            colors: [
                { title: 'Preto', value: '#000000' },
                { title: 'Marrom queimado', value: '#993300' },
                { title: 'Verde escuro', value: '#333300' },
                { title: 'Verde floresta', value: '#003300' },
                { title: 'Azul escuro', value: '#003366' },
                { title: 'Azul marinho', value: '#000080' },
                { title: 'Índigo', value: '#333399' },
                { title: 'Cinza muito escuro', value: '#333333' },
                { title: 'Marrom', value: '#800000' },
                { title: 'Laranja', value: '#FF6600' },
                { title: 'Verde oliva', value: '#808000' },
                { title: 'Verde', value: '#008000' },
                { title: 'Verde-azulado', value: '#008080' },
                { title: 'Azul', value: '#0000FF' },
                { title: 'Cinza-azulado', value: '#666699' },
                { title: 'Cinza', value: '#808080' },
                { title: 'Vermelho', value: '#FF0000' },
                { title: 'Âmbar', value: '#FF9900' },
                { title: 'Verde amarelado', value: '#99CC00' },
                { title: 'Verde mar', value: '#339966' },
                { title: 'Turquesa', value: '#33CCCC' },
                { title: 'Azul royal', value: '#3366FF' },
                { title: 'Roxo', value: '#800080' },
                { title: 'Cinza médio', value: '#999999' },
                { title: 'Magenta', value: '#FF00FF' },
                { title: 'Dourado', value: '#FFCC00' },
                { title: 'Amarelo', value: '#FFFF00' },
                { title: 'Lima', value: '#00FF00' },
                { title: 'Ciano', value: '#00FFFF' },
                { title: 'Azul céu', value: '#00CCFF' },
                { title: 'Roxo acinzentado', value: '#993366' },
                { title: 'Branco', value: '#FFFFFF' }
            ]
        });
    </script>
    <style>
        :root {
            --primary: #00008B;
            --primary-light: #3949ab;
            --secondary: #f8f0e3;
            --accent: #4a4a4a;
            --border: #e0e0e0;
            --text: #2c3e50;
            --active: #00008B;
            --hover: #f5f5f5;
            --background: #f5f5f5;
            --card-background: white;
        }

        body {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            background: var(--background);
            color: var(--text);
            line-height: 1.6;
            margin: 0;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .form-container {
            background: var(--card-background);
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--text);
        }

        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--border);
            border-radius: 5px;
            font-size: 16px;
            font-family: inherit;
        }

        .btn-primary {
            background: var(--primary);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary:hover {
            background: var(--primary-light);
        }

        .back-link {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            color: var(--primary);
            text-decoration: none;
            margin-bottom: 20px;
        }

        .alert {
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .alert-error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="ver_categoria.php?id=<?php echo $categoria_id; ?>" class="back-link">
            <i class="fas fa-arrow-left"></i> Voltar para <?php echo htmlspecialchars($categoria['nome']); ?>
        </a>

        <div class="form-container">
            <h1>Novo Tópico em <?php echo htmlspecialchars($categoria['nome']); ?></h1>

            <?php if ($mensagem): ?>
            <div class="alert alert-error">
                <?php echo $mensagem; ?>
            </div>
            <?php endif; ?>

            <form method="POST" class="topic-form">
                <div class="form-group">
                    <label for="titulo">Título*</label>
                    <input type="text" id="titulo" name="titulo" required 
                           value="<?php echo isset($_POST['titulo']) ? htmlspecialchars($_POST['titulo']) : ''; ?>">
                </div>

                <div class="form-group">
                    <label for="conteudo">Conteúdo*</label>
                    <textarea id="conteudo" name="conteudo" required>
                        <?php echo isset($_POST['conteudo']) ? htmlspecialchars($_POST['conteudo']) : ''; ?>
                    </textarea>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-check"></i> Criar Tópico
                    </button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>













