<?php
// Incluir o arquivo de conexão com o banco de dados
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
include_once("conexao_POST.php");
$proximaMateriaEstudada = null;
$proximoMateriaNome = null;
$resultado_estudo_proxima_materia_0 = false;
$resultado_estudo_proxima_materia_1 = false;
$resultado_primeira_materia_0 = false;
$id_proxima_materia = null;
$materias_no_planejamento_2 = array();
// Verificar se a conexão foi estabelecida com sucesso
if (!$conexao) {
    // Em produção, não exponha detalhes do erro para o usuário
    error_log("Erro na conexão: " . pg_last_error());
    exit("Erro ao conectar ao banco de dados.");
}
// Segurança: garantir autenticação
if (!isset($_SESSION['idusuario'])) {
    http_response_code(403);
    exit('Acesso não autorizado.');
}
// Cast seguro do id_usuario
$id_usuario = (int)$_SESSION['idusuario'];
//echo '<div class="caixa-titulo3">' . $titulo . '</div>';
//echo "<div class='caixa-titulo3'> Sua Agenda Pessoal --> $id_usuario <--</div>";

// Definir o ID do usuário (você precisa atribuir um valor a $id_usuario)

$query_contagem = "SELECT 
    idplanejamento,
    (
        SELECT COUNT(*) 
        FROM appEstudo.planejamento_materia 
        WHERE planejamento_idplanejamento = p.idplanejamento
    ) AS count_materia
FROM 
    appEstudo.planejamento p
WHERE 
    usuario_idusuario = $id_usuario;
";
// ALERTA: Para máxima segurança, utilize consultas parametrizadas (pg_query_params) ao invés de interpolação direta.
$result = pg_query($conexao, $query_contagem);

if ($result) {
    $row = pg_fetch_assoc($result);

    if ($row && isset($row['count_materia'])) {
        $id_planejamento = $row['idplanejamento'];
        //echo "O número de linhas na tabela planejamento_materia é: " . $count_materia. " ----  ";
    } else {
        echo "Nenhum resultado encontrado."; // Não exponha detalhes do banco para o usuário
    }
} else {
    // echo "Erro na consulta: " . pg_last_error($conn);
}
// Consultar os dados do último estudo
$query_consultar_ultimo_estudo = "
    SELECT e.*, 
           m_estudo.nome AS nome_materia_estudo, 
           m_estudo.cor AS cor_materia_estudo
    FROM appEstudo.estudos e
    INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
    INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
    LEFT JOIN appEstudo.materia m_estudo ON e.materia_idmateria = m_estudo.idmateria
    WHERE u.idusuario = $id_usuario
    ORDER BY e.data DESC, e.hora_fim DESC, e.hora_inicio DESC
    LIMIT 1";
// ALERTA: Para máxima segurança, utilize consultas parametrizadas (pg_query_params) ao invés de interpolação direta.
$resultado_ultimo_estudo = pg_query($conexao, $query_consultar_ultimo_estudo);

// Verificar se a consulta retornou resultados
if (pg_num_rows($resultado_ultimo_estudo) > 0) {
    // Extrair os detalhes do último estudo
    $ultimo_estudo = pg_fetch_assoc($resultado_ultimo_estudo);

    // Exibir os detalhes do último estudo
    $dia_semana = date('N', strtotime($ultimo_estudo['data']));
    // Array com os nomes dos dias da semana em português
    $dias_semana = array(
        1 => 'Segunda-feira',
        2 => 'Terça-feira',
        3 => 'Quarta-feira',
        4 => 'Quinta-feira',
        5 => 'Sexta-feira',
        6 => 'Sábado',
        7 => 'Domingo'
    );
    $data_estudo = date('d-m-Y', strtotime($ultimo_estudo['data']));
$ultimaMateriaEstudada = $ultimo_estudo['nome_materia_estudo'];




// Fechar a conexão com o banco de dados
//pg_close($conexao);
?>



<?php
// Consultar os IDs e nomes das matérias associadas ao planejamento
    $query_consultar_materias_planejamento = "SELECT pm.materia_idmateria, m.nome AS nome_materia, m.cor AS cor_materia
FROM appEstudo.planejamento_materia pm
INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
WHERE pm.planejamento_idplanejamento = $id_planejamento
ORDER BY pm.ordem ASC";

    $resultado_materias_planejamento = pg_query($conexao, $query_consultar_materias_planejamento);

// Consultar os dados do último estudo
$query_consultar_ultimo_estudo = "SELECT e.*, 
       m_estudo.nome AS nome_materia_estudo, 
       m_estudo.cor AS cor_materia_estudo
FROM appEstudo.estudos e
INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
LEFT JOIN appEstudo.materia m_estudo ON e.materia_idmateria = m_estudo.idmateria
WHERE u.idusuario = $id_usuario AND e.planejamento_idplanejamento = $id_planejamento
ORDER BY e.idestudos DESC
LIMIT 1";

$resultado_ultimo_estudo = pg_query($conexao, $query_consultar_ultimo_estudo);

// Verificar se a consulta retornou resultados
define('ID_MATERIA_NENHUMA', -1);
$idMateriaUltimoEstudo = ID_MATERIA_NENHUMA;
if (pg_num_rows($resultado_ultimo_estudo) > 0) {
    // Extrair os detalhes do último estudo
    $ultimo_estudo = pg_fetch_assoc($resultado_ultimo_estudo);
    // Adicione mais detalhes conforme necessário
    $idMateriaUltimoEstudo = $ultimo_estudo['materia_idmateria'];
}

// Construir um array com os IDs e nomes das matérias associadas ao planejamento
$materias_no_planejamento_2 = array();
while ($materia_planejamento = pg_fetch_assoc($resultado_materias_planejamento)) {
    $materias_no_planejamento_2[] = array(
        'id' => $materia_planejamento['materia_idmateria'],
        'nome' => $materia_planejamento['nome_materia'],
        'cor' => $materia_planejamento['cor_materia']
    );
}

// Determinar a próxima matéria a ser estudada
$proximaMateriaEstudada = null;
$id_proxima_materia = null;


if (!empty($materias_no_planejamento_2)) {
    if ($idMateriaUltimoEstudo !== ID_MATERIA_NENHUMA) {
        $posUltima = null;
        foreach ($materias_no_planejamento_2 as $i => $mat) {
            if ($mat['id'] == $idMateriaUltimoEstudo) {
                $posUltima = $i;
                break;
            }
        }
        if ($posUltima !== null) {
            // Se não for a última do array, pega a próxima
            if (isset($materias_no_planejamento_2[$posUltima + 1])) {
                $id_proxima_materia = $materias_no_planejamento_2[$posUltima + 1]['id'];
                $proximaMateriaEstudada = $materias_no_planejamento_2[$posUltima + 1]['nome'];
            } else {
                // Se for a última, volta para a primeira
                $id_proxima_materia = $materias_no_planejamento_2[0]['id'];
                $proximaMateriaEstudada = $materias_no_planejamento_2[0]['nome'];
            }
        } else {
            // Se não encontrou a última estudada, pega a primeira
            $id_proxima_materia = $materias_no_planejamento_2[0]['id'];
            $proximaMateriaEstudada = $materias_no_planejamento_2[0]['nome'];
        }
    } else {
        // Se nunca estudou, pega a primeira
        $id_proxima_materia = $materias_no_planejamento_2[0]['id'];
        $proximaMateriaEstudada = $materias_no_planejamento_2[0]['nome'];
    }
}
// Consultar os dados do último estudo
$query_consultar_ultimo_estudo = "SELECT e.*, 
       m_estudo.nome AS nome_materia_estudo, 
       m_estudo.cor AS cor_materia_estudo
FROM appEstudo.estudos e
INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
LEFT JOIN appEstudo.materia m_estudo ON e.materia_idmateria = m_estudo.idmateria
WHERE u.idusuario = $id_usuario AND e.planejamento_idplanejamento = $id_planejamento
ORDER BY e.idestudos DESC
LIMIT 1";

$resultado_ultimo_estudo = pg_query($conexao, $query_consultar_ultimo_estudo);

// Verificar se a consulta retornou resultados
if (pg_num_rows($resultado_ultimo_estudo) > 0) {
// Extrair os detalhes do último estudo
    $ultimo_estudo = pg_fetch_assoc($resultado_ultimo_estudo);
// Adicione mais detalhes conforme necessário
    $idMateriaUltimoEstudo = $ultimo_estudo['materia_idmateria'];
} else {
    echo "<p>Nenhum estudo encontrado..</p>";
}


// Query para contar o número de linhas na tabela planejamento_materia
//$query_contagem = "SELECT COUNT(*) FROM appEstudo.planejamento_materia";
//$query_contagem = "SELECT COUNT(*) FROM appEstudo.planejamento_materia WHERE planejamento_idplanejamento = '$id_planejamento'";


// Executar a query
//$resultado_contagem = pg_query($conexao, $query_contagem);

// Extrair o resultado como uma matriz associativa
//$linha_contagem = pg_fetch_array($resultado_contagem);

// Armazenar o resultado da contagem em uma variável
//$numero_de_linhas = $linha_contagem['count'] - 1;
//$numero_de_linhas = $count_materia - 1;
//echo $numero_de_linhas;

// Exibir o número de linhas
//echo "Número de linhas na tabela: " . $numero_de_linhas;

// Montar a query para selecionar o ID da próxima matéria a ser estudada
    $query_proxima_materia = "SELECT COALESCE(
    (SELECT pm.materia_idmateria
     FROM appEstudo.planejamento_materia pm
     WHERE pm.planejamento_idplanejamento = $id_planejamento
     AND pm.ordem > (
         SELECT pm2.ordem 
         FROM appEstudo.planejamento_materia pm2 
         WHERE pm2.materia_idmateria = $idMateriaUltimoEstudo 
         AND pm2.planejamento_idplanejamento = $id_planejamento
     )
     ORDER BY pm.ordem ASC
     LIMIT 1),
    (SELECT pm.materia_idmateria
     FROM appEstudo.planejamento_materia pm
     WHERE pm.planejamento_idplanejamento = $id_planejamento
     ORDER BY pm.ordem ASC
     LIMIT 1)
) AS proximo_idmateria;";

// Executar a query para obter os detalhes da próxima matéria
$resultado_proxima_materia = pg_query($conexao, $query_proxima_materia);

?>


<?php
if ($resultado_proxima_materia && pg_num_rows($resultado_proxima_materia) > 0) {
    // Exibir os detalhes da próxima matéria
    $linha_proxima_materia = pg_fetch_assoc($resultado_proxima_materia);
    // NÃO sobrescrever $id_proxima_materia aqui! Usar sempre o ID calculado pelo ciclo.

    $query_estudo_proxima_materia = "
    SELECT e.*, c.nome AS nome_curso
    FROM appEstudo.estudos e
    LEFT JOIN appEstudo.curso c ON e.idcurso = c.idcurso
    WHERE e.materia_idmateria = $id_proxima_materia
      AND e.planejamento_usuario_idusuario = $id_usuario
      AND e.planejamento_idplanejamento = $id_planejamento
    ORDER BY e.data DESC, e.hora_fim DESC, e.hora_inicio DESC
    LIMIT 1
";


    //echo "usuario: " . $id_usuario;
    //echo " Materia: " . $id_proxima_materia;

    // Executar a query para obter os dados do estudo relacionado à próxima matéria
    $resultado_estudo_proxima_materia = pg_query($conexao, $query_estudo_proxima_materia);


    if ($resultado_estudo_proxima_materia && pg_num_rows($resultado_estudo_proxima_materia) > 0) {
        $resultado_estudo_proxima_materia_0 = true;
        // Exibir os detalhes do estudo relacionado à próxima matéria dentro da tabela
        $linha_estudo_proxima_materia = pg_fetch_assoc($resultado_estudo_proxima_materia);
        // $proximaMateriaEstudada não deve ser sobrescrita aqui. Ela já foi definida corretamente pelo ciclo.
// Se precisar de detalhes da próxima matéria estudada, use $linha_estudo_proxima_materia separadamente.


       } else {
        $resultado_estudo_proxima_materia_1 = true;
        if ($proximaMateriaEstudada === null and $resultado_estudo_proxima_materia_1 === false) {
            // Se a primeira consulta retornar null, executar uma nova consulta para pegar a primeira linha da tabela
            $query_primeira_materia = "SELECT pm.materia_idmateria, m.nome AS nome_materia
    FROM appEstudo.planejamento_materia pm
    JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    WHERE pm.planejamento_idplanejamento = $id_planejamento
    ORDER BY pm.materia_idmateria ASC
    LIMIT 1;
";

            $resultado_primeira_materia = pg_query($conexao, $query_primeira_materia);

            if ($resultado_primeira_materia && pg_num_rows($resultado_primeira_materia) > 0) {
                $resultado_primeira_materia_0 = true;
                $linha_primeira_materia = pg_fetch_assoc($resultado_primeira_materia);
                $proximoMateriaNome = $linha_primeira_materia['nome_materia'];
                //echo "Próxima matéria a ser estudada (primeira linha): " . $proximoIdMateria;
            } else {
                echo "Nenhuma matéria encontrada no planejamento.";
            }


        } else {
            // Caso não encontre nenhum estudo para a próxima matéria
            $query_primeira_materia = "SELECT pm.materia_idmateria, m.nome AS nome_materia
    FROM appEstudo.planejamento_materia pm
    JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    WHERE pm.planejamento_idplanejamento = $id_planejamento AND m.idmateria = $id_proxima_materia;";

            $resultado_primeira_materia = pg_query($conexao, $query_primeira_materia);
            if ($resultado_primeira_materia && pg_num_rows($resultado_primeira_materia) > 0) {
                $linha_primeira_materia = pg_fetch_assoc($resultado_primeira_materia);
                $proximaMateriaEstudada = $linha_primeira_materia['nome_materia'];
            } else {
                echo "Nenhuma matéria encontrada no planejamento.";
            }

        }
    }
}
    $semRegistro = false;
} else {
    //echo "<p>Nenhum estudo encontrado.llll</p>";
    $semRegistro = true;
}
//// $proximaMateriaEstudada não deve ser sobrescrita aqui. Ela já foi definida corretamente pelo ciclo.
// Se precisar de detalhes da próxima matéria estudada, use $linha_estudo_proxima_materia separadamente.
// Fechar a conexão com o banco de dados PostgreSQL
//pg_close($conexao);
?>


<script>
    // Passa o valor da variável PHP para o JavaScript
    var materia = <?php echo json_encode($proximaMateriaEstudada); ?>;

    // Imprime o valor no console do navegador
    // console.log(materia);
</script>

