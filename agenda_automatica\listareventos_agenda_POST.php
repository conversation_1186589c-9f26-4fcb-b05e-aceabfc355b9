<?php
session_start();

if (isset($_SESSION['idusuario'])) {
    $idUsuario = $_SESSION['idusuario'];

    include_once("../conexao_POST.php"); // Inclua aqui o arquivo de conexão com o banco de dados

    // Consulta SQL para recuperar os eventos agendados do banco de dados
    $query_consultar_agenda = "SELECT * FROM appEstudo.agenda WHERE usuario_idusuario = $1";
    $resultado_agenda = pg_prepare($conexao, "consulta_agenda", $query_consultar_agenda);
    $resultado_agenda = pg_execute($conexao, "consulta_agenda", array($idUsuario));

    $events = array(); // Inicializar um array para os eventos

    while ($registro = pg_fetch_assoc($resultado_agenda)) {
        if ($registro['tipo_evento'] == "Faculdade") {
            $cor = '#1976D2';
        }
        if ($registro['tipo_evento'] == "Trabalho") {
            $cor = 'gray';
        }
        if ($registro['tipo_evento'] == "Concurso") {
            $cor = 'blue';
        }
        if ($registro['tipo_evento'] == "Pessoal") {
            $cor = '#00796B';
        }
        if ($registro['tipo_evento'] == "Planejamento") {
            $cor = '#FFD700';
            $textColor = 'black'; // Definir texto preto para eventos de Planejamento
        } else {
            $textColor = ''; // Resetar para eventos que não sejam Planejamento
        }
        if ($registro['realizado'] == "f") {
            $corBorda = 'red';
        } else {
            $corBorda = 'green';
        }

        // Formatar o evento de acordo com o formato esperado pelo FullCalendar
        $event = array(
            'id' => $registro['id'],
            'title' => $registro['titulo'],
            'start' => date('Y-m-d H:i', strtotime($registro['data_inicio'])),
            'end' => date('Y-m-d H:i', strtotime($registro['data_fim'])),
            'detalhes' => $registro['detalhes'],
            'tipo' => $registro['tipo_evento'], // Adicione o campo tipo_evento
            'realizado' => $registro['realizado'],
            'color' => $cor,
            'borderColor' => $corBorda,
            'textColor' => $textColor // Adicionar a cor do texto
        );

        $events[] = $event;
    }

    // Enviar os eventos no formato JSON
    header('Content-Type: application/json');
    echo json_encode($events);
} else {
    echo "Sessão não iniciada.";
}
?>
