/* assets/css/style.css - Te<PERSON> Retr<PERSON>o */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

:root {
  --primary-color: #7b1fa2;      /* Roxo médio */
  --primary-hover: #6a1b9a;      /* Roxo mais escuro */
  --secondary-color: #9c27b0;    /* Roxo mais claro */
  --success-color: #43a047;
  --danger-color: #e53935;
  --warning-color: #ffb300;
  --info-color: #5e35b1;         /* Roxo-azulado */
  --light-color: #f3e5f5;        /* <PERSON><PERSON>da claro */
  --dark-color: #4a148c;         /* Roxo profundo */
  --body-bg: #f8f2fc;            /* Fundo levemente roxo */
  --card-bg: #ffffff;
  --font-main: 'Poppins', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
  --shadow-sm: 0 4px 6px rgba(123, 31, 162, 0.1);
  --shadow-md: 0 6px 12px rgba(123, 31, 162, 0.15);
  --border-radius: 16px;
  --transition: all 0.3s ease;
  
  /* Elementos retrô */
  --retro-gradient: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%);
  --retro-shadow: 0 8px 20px rgba(123, 31, 162, 0.2);
  --retro-pattern: repeating-linear-gradient(45deg, rgba(123, 31, 162, 0.03) 0px, rgba(123, 31, 162, 0.03) 2px, transparent 2px, transparent 4px);
  --retro-border: 2px solid rgba(123, 31, 162, 0.2);
}

/* Estilos Gerais */
body {
  background-color: var(--body-bg);
  background-image: var(--retro-pattern);
  font-family: var(--font-main);
  color: #4a4a4a;
  line-height: 1.6;
  padding-bottom: 2rem;
}

.container {
  max-width: 1140px;
  padding: 0 15px;
}

/* Cabeçalho */
header {
  padding: 2rem 0;
  margin-bottom: 1rem;
  border-bottom: var(--retro-border);
}

header h1 {
  color: var(--primary-color);
  font-weight: 700;
  margin-bottom: 0.5rem;
  position: relative;
  display: inline-block;
  text-transform: uppercase;
  letter-spacing: 1px;
}

header h1:after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 70px;
  height: 4px;
  background: var(--secondary-color);
  border-radius: 2px;
}

/* Cards e Containers */
.card {
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--retro-shadow);
  transition: var(--transition);
  margin-bottom: 1.5rem;
  overflow: hidden;
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: var(--retro-gradient);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-md);
}

.card-header {
  background-color: var(--card-bg);
  border-bottom: 1px solid rgba(156, 39, 176, 0.1);
  padding: 1.25rem 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h2 {
  margin: 0;
  font-size: 1.2rem;
  line-height: 1.4;
}

.card-header.bg-primary {
  background: var(--primary-color) !important;
}

.card-header.bg-info {
  background: var(--info-color) !important;
}

.card-header.bg-secondary {
  background: var(--secondary-color) !important;
}

.card-body {
  padding: 1.5rem;
}

.card-footer {
  background-color: var(--card-bg);
  border-top: 1px solid rgba(156, 39, 176, 0.1);
  padding: 1rem 1.5rem;
}

/* Listas e Itens */
.list-group {
  border-radius: var(--border-radius);
  overflow: hidden;
}

.list-group-item {
  border: none;
  border-bottom: 1px solid rgba(156, 39, 176, 0.1);
  padding: 1rem 1.25rem;
  transition: var(--transition);
}

.list-group-item:last-child {
  border-bottom: none;
}

.list-group-item:hover {
  background-color: rgba(156, 39, 176, 0.05);
}

.list-group-item-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Botões */
/* Botões */
.btn {
  border-radius: 50px;
  padding: 0.4rem 1rem;
  font-weight: 500;
  letter-spacing: 0.3px;
  transition: var(--transition);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-transform: uppercase;
  font-size: 0.75rem;
  margin: 0 2px;
  white-space: nowrap;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
}

.btn-sm {
  padding: 0.25rem 0.7rem;
  font-size: 0.7rem;
}

.gap-2 {
  gap: 0.5rem !important;
}

/* Card footer ajuste */
.card-footer {
  padding: 0.75rem 1rem;
}

/* Responsividade melhorada */
@media (max-width: 767px) {
  .card-footer .d-flex {
    flex-wrap: wrap;
    gap: 0.5rem;
  }
  
  .card-footer .btn {
    flex: 1 0 auto;
    text-align: center;
    min-width: 80px;
  }
}

.btn-primary {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover,
.btn-primary:focus {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

.btn-success {
  background-color: var(--success-color);
  border-color: var(--success-color);
}

.btn-danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
}

.btn-warning {
  background-color: var(--warning-color);
  border-color: var(--warning-color);
  color: #212529;
}

.btn-info {
  background-color: var(--info-color);
  border-color: var(--info-color);
  color: white;
}

.btn-secondary {
  background: var(--secondary-color);
  border-color: var(--secondary-color);
}

.btn-light {
  background-color: var(--light-color);
  border-color: var(--light-color);
  color: var(--dark-color);
}

/* Dropdown personalizado */
.dropdown-menu {
  border: none;
  border-radius: 12px;
  box-shadow: var(--retro-shadow);
  overflow: hidden;
}

.dropdown-item {
  padding: 0.6rem 1.5rem;
  font-weight: 500;
}

.dropdown-item:hover {
  background-color: var(--light-color);
  color: var(--dark-color);
}

.dropdown-divider {
  border-top: 1px solid rgba(156, 39, 176, 0.1);
}

/* Medicamentos Grid */
.medicamentos-grid .card {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border: var(--retro-border);
}

.medicamentos-grid .card-body {
  flex: 1 1 auto;
}

.medicamentos-grid .card-title {
  color: var(--primary-color);
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.medicamentos-grid .card-subtitle {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--secondary-color);
  margin-bottom: 1rem;
}

/* Animações */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Elementos Retrô */
.text-muted {
  color: #9575cd !important;
}

/* Responsividade para dispositivos móveis */
@media (max-width: 767px) {
  .container {
    padding: 0 10px;
  }
  
  .card-header, .card-body, .card-footer {
    padding: 1rem;
  }
  
  h1 {
    font-size: 1.8rem;
  }
  
  .card-title {
    font-size: 1.1rem;
  }
  
  .btn {
    padding: 0.4rem 1rem;
  }
}