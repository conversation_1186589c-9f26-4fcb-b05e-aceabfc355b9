<!DOCTYPE html>
<html>
<head>
    <title>Detalhes do Planejamento</title>
</head>
<body>
<h1>Detalhes do Planejamento</h1>

<?php
session_start();
include_once("conexao_POST.php");

if (!$conexao) {
    die("Erro na conexão: " . pg_last_error());
}

//if (isset($_POST['id_planejamento']) && is_numeric($_POST['id_planejamento'])) {
if (isset($_SESSION['idusuario'])) {
    $id_usuario = $_SESSION['idusuario'];
    // Consultar os dados do planejamento relacionado ao usuario logado
    $query_consultar_idplanejamento = "SELECT p.idplanejamento
        FROM appEstudo.planejamento p 
        WHERE p.usuario_idusuario = $id_usuario";

    $resultado_idplanejamento = pg_query($conexao, $query_consultar_idplanejamento);

    if ($resultado_idplanejamento) {
        $row = pg_fetch_assoc($resultado_idplanejamento);
        $id_planejamento = $row['idplanejamento'];

        // Agora $idplanejamento contém o valor do idplanejamento
    } else {
        echo "Erro ao executar a consulta.";
    }

    //$id_planejamento = $_POST['id_planejamento'];

    // Consultar os dados do planejamento pelo ID, incluindo o nome do usuário
    $query_consultar_planejamento = "SELECT p.*, u.nome as nome_usuario 
                                     FROM appEstudo.planejamento p 
                                     INNER JOIN appEstudo.usuario u ON p.usuario_idusuario = u.idusuario
                                     WHERE p.idplanejamento = $id_planejamento";
    $resultado_planejamento = pg_query($conexao, $query_consultar_planejamento);

    if (pg_num_rows($resultado_planejamento) > 0) {
        $planejamento = pg_fetch_assoc($resultado_planejamento);

        echo "<h2>Informações do Planejamento:</h2>";
        echo "<p><strong>ID do Planejamento:</strong> " . $planejamento['idplanejamento'] . "</p>";
        echo "<p><strong>Nome do Planejamento:</strong> " . $planejamento['nome'] . "</p>";
        echo "<p><strong>Nome do Usuário:</strong> " . $planejamento['nome_usuario'] . "</p>";
        echo "<p><strong>Data de Início:</strong> " . $planejamento['data_inicio'] . "</p>";
        echo "<p><strong>Data de Fim:</strong> " . $planejamento['data_fim'] . "</p>";

        // Consultar as matérias relacionadas ao planejamento
        $query_consultar_materias = "SELECT m.* FROM appEstudo.materia m 
                                     INNER JOIN appEstudo.planejamento_materia pm ON m.idmateria = pm.materia_idmateria
                                     WHERE pm.planejamento_idplanejamento = $id_planejamento";
        $resultado_materias = pg_query($conexao, $query_consultar_materias);

        if (pg_num_rows($resultado_materias) > 0) {
            echo "<h2>Matérias Relacionadas:</h2>";
            echo "<ul>";
            while ($materia = pg_fetch_assoc($resultado_materias)) {
                echo "<li><strong>ID da Matéria:</strong> " . $materia['idmateria'] . "</li>";
                echo "<li><strong>Nome da Matéria:</strong> " . $materia['nome'] . "</li>";
                echo "<li><div style='width: 20px; height: 20px; display: inline-block; margin-right: 5px; border: 1px solid #ccc; background-color: " . $materia['cor'] . ";'></div></li>";
                echo "<br>";
            }
            echo "</ul>";
        } else {
            echo "<p>Nenhuma matéria relacionada encontrada.</p>";
        }
    } else {
        echo "<p>Planejamento não encontrado.</p>";
    }
} else {
    echo "<p>ID do planejamento não especificado ou inválido.</p>";
}

pg_close($conexao);
?>

<br>
<a href="listarPlanejamento.php">Voltar para a lista de planejamentos</a>
</body>
</html>
