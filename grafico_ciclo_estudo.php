<?php
include 'conexao_POST.php';
include 'processa_index.php';
include_once 'funcoes.php'; // Inclua as funções aqui

// Consultar os IDs e nomes das matérias associadas ao planejamento
$query_consultar_materias_planejamento = "
    SELECT pm.materia_idmateria, m.nome AS nome_materia, m.cor AS cor_materia
    FROM appEstudo.planejamento_materia pm
    INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    WHERE pm.planejamento_idplanejamento = $id_planejamento";

$resultado_materias_planejamento = pg_query($conexao, $query_consultar_materias_planejamento);

// Inicializa arrays para armazenar os dados das matérias
$materias_no_planejamento = array();
$cores_materias = array();

// Preenche os arrays com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_materias_planejamento)) {
    $materia_id = $row['materia_idmateria'];
    $nome_materia = $row['nome_materia'];
    $cor_materia = $row['cor_materia'];

    $materias_no_planejamento[$materia_id] = $nome_materia;
    $cores_materias[$nome_materia] = $cor_materia;
}

// Realiza a consulta ao banco de dados para obter os dados de estudo por método de cada matéria no planejamento
$query_consulta_metodos = "
    SELECT m.nome AS nome_materia, 
           e.metodo AS metodo,
           SUM(e.tempo_liquido) AS tempo_estudo
    FROM appEstudo.estudos e
    INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
    INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
    LEFT JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria
    WHERE u.idusuario = $id_usuario AND e.materia_idmateria IN (" . implode(',', array_keys($materias_no_planejamento)) . ")
    GROUP BY m.nome, e.metodo";

$resultado_consulta_metodos = pg_query($conexao, $query_consulta_metodos);

// Inicializa um array para armazenar os dados de tempo de estudo por método para cada matéria
$metodo_estudo_por_materia = array();
$tempo_total_por_materia = array();


// Preenche o array com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_consulta_metodos)) {
    $materia = $row['nome_materia'];
    $metodo = $row['metodo'];
    $tempo_estudo = $row['tempo_estudo']; // Tempo no formato "HH:MM:SS"

    // Converte o tempo de estudo para segundos
    $tempo_estudo_segundos = converterParaSegundos($tempo_estudo);

    // Adiciona os tempos de estudo por método para a matéria correspondente
    if (!isset($metodo_estudo_por_materia[$materia])) {
        $metodo_estudo_por_materia[$materia] = array();
    }
    // Convertendo para segundos e armazenando
    $metodo_estudo_por_materia[$materia][$metodo] = $tempo_estudo_segundos;

    // Calcula o tempo total por matéria
    if (!isset($tempo_total_por_materia[$materia])) {
        $tempo_total_por_materia[$materia] = 0;
    }
    $tempo_total_por_materia[$materia] += $tempo_estudo_segundos;
}






// Convertendo os arrays para JSON para serem utilizados no JavaScript
$dados_json = json_encode($metodo_estudo_por_materia);
$cores_json = json_encode($cores_materias);
$tempo_total_json = json_encode($tempo_total_por_materia);

// Debug: imprime os dados para verificar se estão corretos
//echo "<pre>";
//print_r($metodo_estudo_por_materia);
//echo "</pre>";
//?>

<div id="myPieChart" class="chart-container"></div>

<script>
    // Recuperando os dados do PHP
    var dados = <?php echo $dados_json; ?>;
    var cores = <?php echo $cores_json; ?>;
    var tempoTotalPorMateria = <?php echo $tempo_total_json; ?>;

    //console.log("Dados recebidos:", dados); // Debug: Imprime os dados no console
    //console.log("Cores recebidas:", cores); // Debug: Imprime as cores no console
    //console.log("Tempo total por matéria:", tempoTotalPorMateria); // Debug: Imprime o tempo total no console

    // Transformando os dados no formato esperado pelo Highcharts
    var seriesData = [];
    var drilldownSeries = [];

    for (var materia in tempoTotalPorMateria) {
        var totalTempoEstudo = tempoTotalPorMateria[materia];
        var horas = Math.floor(totalTempoEstudo / 3600);
        var minutos = Math.floor((totalTempoEstudo % 3600) / 60);
        var formattedTime = horas + 'h ' + minutos + 'm';
        seriesData.push({
            name: materia,
            y: totalTempoEstudo,
            formattedTime: formattedTime,
            drilldown: materia,
            color: cores[materia] // Atribui a cor específica da matéria
        });
        var data = [];
        for (var metodo in dados[materia]) {
            var tempoEstudo = parseInt(dados[materia][metodo]);
            var horasMetodo = Math.floor(tempoEstudo / 3600);
            var minutosMetodo = Math.floor((tempoEstudo % 3600) / 60);
            var formattedTimeMetodo = horasMetodo + 'h ' + minutosMetodo + 'm';
            data.push({ name: metodo, y: tempoEstudo, formattedTime: formattedTimeMetodo });
        }
        drilldownSeries.push({
            name: materia,
            id: materia,
            data: data.map(function(item) {
                return { name: item.name, y: item.y, formattedTime: item.formattedTime };
            })
        });
    }

    // Configuração do gráfico
    Highcharts.chart('myPieChart', {
        chart: {
            type: 'pie',
            backgroundColor: 'transparent' // Fundo transparente
        },
        title: {
            text: null
        },
        credits: {
            enabled: false // Desativar créditos
        },
        plotOptions: {
            series: {
                dataLabels: {
                    enabled: true,
                    format: '{point.name}: {point.percentage:.2f}% ({point.formattedTime})',
                    style: {
                        textOutline: 'none' // Remover sombra branca ao redor do texto
                    }
                }
            }
        },
        tooltip: {
            pointFormat: '<span style="color:{point.color}">\u25CF</span> {series.name}: <b>{point.percentage:.2f}%</b> ({point.formattedTime})<br/>',
            style: {
                textOutline: 'none' // Remover sombra branca ao redor do texto da tooltip
            }
        },
        series: [{
            name: 'Matéria',
            colorByPoint: true,
            data: seriesData
        }],
        drilldown: {
            series: drilldownSeries
        }
    });

</script>

