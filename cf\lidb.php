<?php
session_start();

// Verifica se o usuário está logado
if (!isset($_SESSION['idusuario'])) {
    // Redireciona para a página de login
    header("Location: ../login_index.php");
    exit;
}

// index.php - Página principal para exibir a versão compilada da Constituição com função de grifo

// URL da versão compilada da Constituição (sem os trechos revogados)
$urlOriginal = "https://www.planalto.gov.br/ccivil_03/Decreto-Lei/Del4657compilado.htm";

// Função para buscar o conteúdo do site original
function buscarConteudoOriginal($url) {
    // Inicializa o cURL
    $ch = curl_init();

    // Configura as opções do cURL
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);

    // Configura o user agent para simular um navegador
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

    // Executa a requisição e armazena o resultado
    $response = curl_exec($ch);

    // Verifica se ocorreu algum erro
    if (curl_errno($ch)) {
        $error = curl_error($ch);
        curl_close($ch);
        return "Erro ao acessar o site original: " . $error;
    }

    // Fecha a sessão cURL
    curl_close($ch);

    // Retorna o conteúdo obtido
    return $response;
}

// Função para modificar os links e recursos do HTML original
function processarHTML($html) {
    // Cria um novo objeto DOMDocument
    $dom = new DOMDocument();

    // Suprime erros de HTML mal formatado
    libxml_use_internal_errors(true);
    @$dom->loadHTML($html, LIBXML_NOERROR);
    libxml_clear_errors();

    // Função para remover estilos inline que afetam o tamanho da fonte
    $xpath = new DOMXPath($dom);
    $elementos = $xpath->query("//*[@style]|//font[@size]");

    foreach ($elementos as $elemento) {
        // Remove atributos de tamanho de fonte
        if ($elemento->tagName === 'font') {
            $elemento->removeAttribute('size');
        }

        // Remove estilos inline de fonte
        $style = $elemento->getAttribute('style');
        if ($style) {
            // Remove apenas as propriedades relacionadas à fonte
            $style = preg_replace('/font-size:\s*[^;]+;?/', '', $style);
            $style = preg_replace('/font-family:\s*[^;]+;?/', '', $style);
            $elemento->setAttribute('style', $style);
        }
    }

    // Base URL para caminhos relativos
    $baseUrl = "https://www.planalto.gov.br/ccivil_03/constituicao/";

    // Converte caminhos relativos para absolutos nas imagens
    $images = $dom->getElementsByTagName('img');
    foreach ($images as $img) {
        $src = $img->getAttribute('src');
        if (strpos($src, 'http') !== 0 && !empty($src)) {
            // É um caminho relativo, converte para absoluto
            $img->setAttribute('src', $baseUrl . ltrim($src, '/'));
        }
    }

    // Processa os links para CSS e outros recursos
    $links = $dom->getElementsByTagName('link');
    foreach ($links as $link) {
        $href = $link->getAttribute('href');
        if (strpos($href, 'http') !== 0 && !empty($href)) {
            // É um caminho relativo, converte para absoluto
            $link->setAttribute('href', $baseUrl . ltrim($href, '/'));
        }
    }

    // Remove scripts problemáticos que podem causar erros 404
    $scripts = $dom->getElementsByTagName('script');
    $scriptsToRemove = [];
    foreach ($scripts as $script) {
        // Marca scripts externos para remoção
        $src = $script->getAttribute('src');
        if (!empty($src)) {
            $scriptsToRemove[] = $script;
        }
    }

    // Remove os scripts marcados
    foreach ($scriptsToRemove as $script) {
        $script->parentNode->removeChild($script);
    }

    // Localiza os elementos com a classe "cstf" para modificá-los
    $xpath = new DOMXPath($dom);
    $cstfElements = $xpath->query("//*[contains(@class, 'cstf')]");

    // Marca estes elementos para processamento posterior via JavaScript
    foreach ($cstfElements as $element) {
        // Mantém a classe cstf para nosso JavaScript processar
        $currentClass = $element->getAttribute('class');
        $element->setAttribute('class', $currentClass . ' meu-proxy');
    }

    // Adiciona IDs aos parágrafos para que possam ser referenciados para marcação
    $paragrafos = $dom->getElementsByTagName('p');
    $count = 0;
    foreach ($paragrafos as $paragrafo) {
        if (!$paragrafo->hasAttribute('id')) {
            $paragrafo->setAttribute('id', 'p-' . $count);
            $count++;
        }
    }

    // Adiciona o JavaScript EXTERNO para o sistema de marcação
    $scriptNode = $dom->createElement('script');
    $scriptNode->setAttribute('type', 'text/javascript');
    $scriptNode->setAttribute('src', 'marcacao.js');

    // Certifique-se de que o script está sendo adicionado ao final do body
    $body = $dom->getElementsByTagName('body')->item(0);
    $body->appendChild($scriptNode);

    // Adiciona FontAwesome para os ícones
    $fontAwesome = $dom->createElement('link');
    $fontAwesome->setAttribute('rel', 'stylesheet');
    $fontAwesome->setAttribute('href', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css');
    $head = $dom->getElementsByTagName('head')->item(0);
    $head->appendChild($fontAwesome);

    // Adiciona o favicon ao <head>
    $favicon = $dom->createElement('link');
    $favicon->setAttribute('rel', 'shortcut icon');
    $favicon->setAttribute('href', '../logo/Estudo Off/favicon_32x32.png');
    $favicon->setAttribute('type', 'image/x-icon');
    $head->appendChild($favicon);

    // Adiciona CSS para o sistema de marcação
    $styleNode = $dom->createElement('style');
    $cssContent = $dom->createTextNode("
        /* Configurações base */
        :root {
            --primary-color: #00008B;
            --text-color: #333;
            --bg-color: #f5f5f5;
        }

        body {
            padding-bottom: 40px;
            font-family: 'Roboto', sans-serif;
            line-height: 1.8;
            background-color: var(--bg-color);
            color: var(--text-color);
            margin: 0;
            font-size: 16px;
        }

        /* Estilos para o sistema de marcação - IMPORTANTE manter no topo */
        .marcacao-usuario {
            border-radius: 2px;
            cursor: pointer;
        }

        .marcacao-amarela {
            background-color: #ffffc3 !important;
        }

        .marcacao-verde {
            background-color: #d4ffd4 !important;
        }

        .marcacao-azul {
            background-color: #d4f4ff !important;
        }

        /* Botão de marcação */
        #botao-marcacao {
            position: absolute;
            z-index: 1000;
            background-color: white;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            padding: 5px;
            display: flex;
        }

        #botao-marcacao button {
            margin: 0 3px;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 3px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s;
        }

        #botao-marcacao button:hover {
            transform: scale(1.1);
        }

        #marcar-amarelo {
            background-color: #ffffc3;
        }
        #marcar-amarelo i {
            color: #b3b300;
        }
        #marcar-verde {
            background-color: #d4ffd4;
        }
        #marcar-verde i {
            color: #008a00;
        }
        #marcar-azul {
            background-color: #d4f4ff;
        }
        #marcar-azul i {
            color: #0077b3;
        }
        #remover-marcacao {
            background-color: #ffeeee;
        }
        #remover-marcacao i {
            color: #cc0000;
        }

        /* Barra de ferramentas */
        #barra-ferramentas {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background-color: white;
            border: 1px solid #ccc;
            border-radius: 4px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            padding: 10px;
        }

        #limpar-marcacoes {
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            display: flex;
            align-items: center;
            font-size: 14px;
        }

        #limpar-marcacoes i {
            margin-right: 5px;
            color: #cc0000;
        }

        /* Estilos para formatação do texto - mantenha após os estilos de marcação */
        [class*='art'],
        [class*='Art'],
        [class*='Artigo'],
        .artigo,
        .Artigo,
        .art {
            font-size: 1.1rem !important;
            line-height: 1.8 !important;
            color: var(--text-color) !important;
            font-family: 'Roboto', sans-serif !important;
        }

        [class*='art'] a,
        [class*='Art'] a,
        [class*='Artigo'] a,
        .artigo a,
        .Artigo a,
        .art a {
            font-size: inherit !important;
            line-height: inherit !important;
            font-family: inherit !important;
        }

        [class*='art']:before,
        [class*='Art']:before,
        [class*='Artigo']:before,
        .artigo:before,
        .Artigo:before,
        .art:before {
            font-size: 1.1rem !important;
            font-weight: bold !important;
        }

        span[style*='font-size'],
        font[size],
        [style*='font-size'] {
            font-size: 1.1rem !important;
        }

        p, div, span, font {
            font-size: 1.1rem !important;
            line-height: 1.8 !important;
            font-family: 'Roboto', sans-serif !important;
        }

        .content-wrapper {
            max-width: 1200px;
            margin: 2rem auto;
            background: white;
            padding: 3rem;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            font-size: 1.1rem;
        }

        .back-button {
            position: fixed;
            top: 20px;
            left: 20px;
            background: var(--primary-color);
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
            z-index: 1000;
        }

        .back-button:hover {
            background: #0000CD;
        }

        .notification-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: #f8f9fa;
            border-top: 1px solid #dee2e6;
            padding: 15px;
            text-align: center;
            z-index: 1000;
            font-size: 0.9rem;
            color: #666;
        }

        @media (max-width: 768px) {
            .content-wrapper {
                margin: 1rem;
                padding: 1.5rem;
            }

            body {
                font-size: 14px;
            }

            .back-button {
                padding: 8px 15px;
                font-size: 0.9rem;
            }
        }
    ");
    $styleNode->appendChild($cssContent);
    $head->appendChild($styleNode);

    // Adiciona botão Voltar
    $backButton = $dom->createElement('a');
    $backButton->setAttribute('href', 'index.php');
    $backButton->setAttribute('class', 'back-button');
    $backButton->textContent = '← Voltar';

    // Cria wrapper para o conteúdo
    $wrapper = $dom->createElement('div');
    $wrapper->setAttribute('class', 'content-wrapper');

    // Move todo o conteúdo do body para dentro do wrapper
    $body = $dom->getElementsByTagName('body')->item(0);
    while ($body->childNodes->length > 0) {
        $wrapper->appendChild($body->childNodes->item(0));
    }

    // Adiciona o wrapper e o botão voltar ao body
    $body->appendChild($backButton);
    $body->appendChild($wrapper);

    // Retorna o HTML modificado
    return $dom->saveHTML();
}

// Busca o conteúdo original diretamente
$conteudoOriginal = buscarConteudoOriginal($urlOriginal);

// Processa o HTML para adaptar ao nosso site
$conteudoProcessado = processarHTML($conteudoOriginal);

// Define o charset correto
header('Content-Type: text/html; charset=utf-8');

// Exibe o conteúdo processado
echo $conteudoProcessado;
?>
