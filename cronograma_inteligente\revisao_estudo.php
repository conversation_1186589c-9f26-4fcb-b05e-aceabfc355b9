<?php
session_start();
if (!isset($_SESSION['idusuario']) || $_SESSION['idusuario'] != 1) {
    header("Location: login.php");
    exit();
}

include_once("assets/config.php");

$usuario_id = $_SESSION['idusuario'];

// Consulta para buscar os conteúdos selecionados com seu status e data de revisão, organizados por matéria
$query = "
    SELECT 
        uc.id, 
        m.idmateria,
        m.nome AS materia_nome, 
        m.cor, 
        ce.descricao, 
        ce.capitulo, 
        uc.status_revisao, 
        uc.data_revisao,
        CAST(split_part(ce.capitulo, '.', 1) AS INTEGER) AS capitulo_principal,
        COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 2), '') AS INTEGER), 0) AS subnivel1,
        COALESCE(CAST(NULLIF(split_part(ce.capitulo, '.', 3), '') AS INTEGER), 0) AS subnivel2
    FROM 
        appestudo.usuario_conteudo AS uc
    JOIN 
        appestudo.conteudo_edital AS ce ON uc.conteudo_id = ce.id_conteudo
    JOIN 
        appestudo.materia AS m ON ce.materia_id = m.idmateria
    WHERE 
        uc.usuario_id = $usuario_id
    ORDER BY 
        m.nome, capitulo_principal, subnivel1, subnivel2;
";


$result = pg_query($conexao, $query);
$conteudos_por_materia = [];

// Organizar os conteúdos por matéria para exibição agrupada
while ($row = pg_fetch_assoc($result)) {
    $materia_id = $row['idmateria'];
    if (!isset($conteudos_por_materia[$materia_id])) {
        $conteudos_por_materia[$materia_id] = [
            'nome' => $row['materia_nome'],
            'cor' => $row['cor'],
            'conteudos' => []
        ];
    }
    $conteudos_por_materia[$materia_id]['conteudos'][] = $row;
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Revisão e Progresso de Estudo</title>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;600;700&family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier Prime', monospace;
            background: #f4f1ea;
            color: #2c1810;
            padding: 20px;
            line-height: 1.6;
            background-image: linear-gradient(rgba(244, 241, 234, 0.9), rgba(244, 241, 234, 0.9)),
            url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23d3c5b8' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
        }

        .header-vintage {
            background: linear-gradient(to right, #8B0000, #B22222);
            padding: 30px;
            text-align: center;
            border-radius: 8px;
            margin-bottom: 30px;
            position: relative;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .header-vintage h2 {
            font-family: 'Cinzel', serif;
            font-size: 28px;
            color: #fff;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            letter-spacing: 1.5px;
        }

        .header-vintage::after {
            content: '';
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 2px;
            background: rgba(255,255,255,0.5);
        }

        .materias-container {
            background: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .materia-group {
            margin-bottom: 25px;
            border: 1px solid #d3c5b8;
            border-radius: 8px;
            overflow: hidden;
            background: #fdfbf7;
        }

        .materia-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            background: #fff;
            border-bottom: 2px solid;
            cursor: pointer;
            user-select: none;
        }

        .materia-header-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .materia-icon {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            color: #fff;
            font-size: 1.2rem;
        }

        .materia-nome {
            font-family: 'Cinzel', serif;
            font-size: 1.2rem;
            color: #2c1810;
            margin: 0;
        }

        .toggle-icon {
            font-size: 1.2rem;
            color: #8B4513;
            transition: transform 0.3s ease;
        }

        .expanded .toggle-icon {
            transform: rotate(180deg);
        }

        .conteudo-list {
            display: none;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .expanded .conteudo-list {
            display: block;
        }

        .conteudo-item {
            padding: 20px;
            border: 1px solid #e8e0d8;
            border-radius: 6px;
            margin-bottom: 15px;
            background: #fff;
            transition: all 0.3s ease;
        }

        .conteudo-item:hover {
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .conteudo-header {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e8e0d8;
        }

        .capitulo-number {
            font-family: 'Courier Prime', monospace;
            font-weight: bold;
            color: #8B4513;
            min-width: 50px;
        }

        .form-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .input-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .input-group label {
            font-weight: bold;
            color: #8B4513;
        }

        select, input[type="date"] {
            padding: 10px;
            border: 2px solid #d3c5b8;
            border-radius: 4px;
            font-family: 'Courier Prime', monospace;
            background: #fff;
            color: #2c1810;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        select:hover, input[type="date"]:hover {
            border-color: #8B4513;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.85rem;
            font-weight: bold;
        }

        .status-nao-iniciado { background: #ffe4e4; color: #d32f2f; }
        .status-em-andamento { background: #fff3e0; color: #f57c00; }
        .status-concluido { background: #e8f5e9; color: #388e3c; }

        .btn-salvar {
            display: block;
            width: 100%;
            padding: 16px;
            background: linear-gradient(145deg, #8b4513, #a0522d);
            color: #fff;
            border: none;
            border-radius: 6px;
            font-size: 1.1rem;
            font-family: 'Cinzel', serif;
            cursor: pointer;
            margin-top: 25px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1.5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .btn-salvar:hover {
            background: linear-gradient(145deg, #a0522d, #8b4513);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            body { padding: 10px; }
            .header-vintage { padding: 20px; }
            .materias-container { padding: 15px; }
            .form-group { grid-template-columns: 1fr; }
            .conteudo-item { padding: 15px; }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header-vintage">
        <h2>Revisão e Progresso de Estudo</h2>
    </div>

    <div class="materias-container">
        <form action="atualizar_revisao.php" method="POST">
            <?php foreach ($conteudos_por_materia as $materia_id => $materia): ?>
                <div class="materia-group" style="border-color: <?= htmlspecialchars($materia['cor']) ?>">
                    <div class="materia-header" onclick="toggleMateria(this)" style="border-color: <?= htmlspecialchars($materia['cor']) ?>">
                        <div class="materia-header-content">
                            <div class="materia-icon" style="background-color: <?= htmlspecialchars($materia['cor']) ?>">
                                <i class="fas fa-book"></i>
                            </div>
                            <h3 class="materia-nome"><?= htmlspecialchars($materia['nome']) ?></h3>
                        </div>
                        <i class="fas fa-chevron-down toggle-icon"></i>
                    </div>
                    <div class="conteudo-list">
                        <?php foreach ($materia['conteudos'] as $conteudo): ?>
                            <div class="conteudo-item">
                                <div class="conteudo-header">
                                    <span class="capitulo-number">Cap. <?= htmlspecialchars($conteudo['capitulo']) ?></span>
                                    <span><?= htmlspecialchars($conteudo['descricao']) ?></span>
                                    <?php
                                    $statusClass = '';
                                    switch($conteudo['status_revisao']) {
                                        case 'Não Iniciado': $statusClass = 'status-nao-iniciado'; break;
                                        case 'Em Andamento': $statusClass = 'status-em-andamento'; break;
                                        case 'Concluído': $statusClass = 'status-concluido'; break;
                                    }
                                    ?>
                                    <span class="status-badge <?= $statusClass ?>">
                                            <?= htmlspecialchars($conteudo['status_revisao']) ?>
                                        </span>
                                </div>

                                <div class="form-group">
                                    <div class="input-group">
                                        <label for="status_<?= $conteudo['id'] ?>">Status:</label>
                                        <select name="status[<?= $conteudo['id'] ?>]" id="status_<?= $conteudo['id'] ?>"
                                                onchange="updateStatusBadge(this)">
                                            <option value="Não Iniciado" <?= $conteudo['status_revisao'] == 'Não Iniciado' ? 'selected' : '' ?>>
                                                Não Iniciado
                                            </option>
                                            <option value="Em Andamento" <?= $conteudo['status_revisao'] == 'Em Andamento' ? 'selected' : '' ?>>
                                                Em Andamento
                                            </option>
                                            <option value="Concluído" <?= $conteudo['status_revisao'] == 'Concluído' ? 'selected' : '' ?>>
                                                Concluído
                                            </option>
                                        </select>
                                    </div>
                                    <div class="input-group">
                                        <label for="data_revisao_<?= $conteudo['id'] ?>">Data da Próxima Revisão:</label>
                                        <input type="date"
                                               name="data_revisao[<?= $conteudo['id'] ?>]"
                                               id="data_revisao_<?= $conteudo['id'] ?>"
                                               value="<?= htmlspecialchars($conteudo['data_revisao']) ?>">
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>

            <button type="submit" class="btn-salvar">
                <i class="fas fa-save"></i> Salvar Alterações
            </button>
        </form>
    </div>
</div>

<script>
    function toggleMateria(header) {
        const materiaGroup = header.closest('.materia-group');
        materiaGroup.classList.toggle('expanded');
    }

    function updateStatusBadge(select) {
        const conteudoItem = select.closest('.conteudo-item');
        const statusBadge = conteudoItem.querySelector('.status-badge');

        // Remove classes antigas
        statusBadge.classList.remove('status-nao-iniciado', 'status-em-andamento', 'status-concluido');

        // Adiciona nova classe baseada no status selecionado
        switch(select.value) {
            case 'Não Iniciado':
                statusBadge.classList.add('status-nao-iniciado');
                break;
            case 'Em Andamento':
                statusBadge.classList.add('status-em-andamento');
                break;
            case 'Concluído':
                statusBadge.classList.add('status-concluido');
                break;
        }

        // Atualiza o texto do badge
        statusBadge.textContent = select.value;
    }
</script>
</body>
</html>