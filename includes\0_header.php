<?php
session_start(); // Sempre primeiro

// Inclui a conexão ANTES de qualquer coisa que a use
// Idealm<PERSON>, use um caminho absoluto ou __DIR__ para robustez
// e um nome de arquivo de config mais genérico se 'conexao_POST.php' for apenas para conexão.
// Supondo que 'conexao_POST.php' apenas define $conexao:
include_once __DIR__ . '/../conexao_POST.php'; // Ou o caminho correto

// Inclui e executa a verificação de autenticação
include_once __DIR__ . '/auth.php'; // Garante que auth.php e sua função estejam disponíveis
verificarAutenticacao($conexao); // CHAMADA CRUCIAL! Se não autenticado, o script para aqui.

define('MEU_SISTEMA_PHP_EXECUCAO_VALIDA', true); // Define a constante

// Agora que a autenticação foi verificada, podemos incluir processa_index.php
// que pode assumir que o usuário está logado e ativo.
include_once __DIR__ . '/../processa_index.php';
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Estudos</title>

    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">

    <!-- Ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <!-- Seus estilos -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/avisos.css">
    

    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">

    <!-- jQuery (apenas uma vez) -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<!-- Bootstrap e Popper -->
<script src="https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.bundle.min.js"></script>
<link rel="stylesheet" href="./public/tailwind.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Highcharts e seus módulos -->
<script src="https://code.highcharts.com/highcharts.js"></script>
<script src="https://code.highcharts.com/modules/drilldown.js"></script>
<script src="https://code.highcharts.com/modules/accessibility.js"></script>
<script src="https://code.highcharts.com/highcharts-more.js"></script>
<script src="https://code.highcharts.com/modules/exporting.js"></script>
<script src="https://code.highcharts.com/modules/export-data.js"></script>

<!-- Lucide -->
<script src="https://unpkg.com/lucide@latest"></script>

<!-- Scripts personalizados por último -->
<script src="assets/js/main.js"></script>

<!-- Gerenciadores de Modal -->
<script src="assets/js/estudo_modal_manager.js"></script>
<script src="assets/js/event-manager.js"></script>


<script src='fullcalendar-6.1.8/dist/index.global.js'></script>
<script src='fullcalendar-6.1.8/packages/core/locales/pt-br.global.js'></script>


<!-- Scripts específicos -->
<script src="assets/js/calendario_estudo.js"></script>



    <!-- Fontes -->
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">
    
    <!-- Ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
    <!-- CSS do modal de calendário -->
    <link rel="stylesheet" href="assets/css/calendar_modal.css">
    
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

<!-- Fontes -->
<link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">
    
    <!-- Ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    
<style>
body[data-theme="dark"] {
    --primary: #00008B; /* Royal Blue - Adjust if needed for dark theme */
    --secondary: #1a1a2e; /* Dark blue-ish background */
    --accent: #e94560; /* Accent color - Example: Pink/Red */
    --border: #2a2a48; /* Darker border color */
    --text: #e0e0e0; /* Light text color for dark backgrounds */
    --active: #5252ff; /* Brighter blue for active elements */
    --hover: #23233a; /* Slightly lighter dark for hover */
    --primary-blue: #5252ff; /* Brighter blue for primary actions */
    --primary-blue-rgb: 82, 82, 255; /* RGB for primary blue */

    --parchment: #1e1e32; /* Darker parchment */
    --vintage-gold: #b8860b; /* Keep gold, or adjust if it clashes */
    --burgundy: #902d58; /* Darker burgundy/purple */
    --gold-accent: #daa520; /* Keep gold accent, or adjust */
    --shadow-color: rgba(255, 255, 255, 0.05); /* Lighter shadow for dark theme */
    --elegant-border: linear-gradient(45deg, var(--vintage-gold), #ffd700, var(--vintage-gold)); /* May need adjustment */
    --success-color: #28a745; /* Keep or adjust for contrast */
    --danger-color: #dc3545; /* Keep or adjust for contrast */
    --warning-color: #ffc107; /* Keep or adjust for contrast */
    --dark-bg: #121220; /* Very dark background for some elements */

    --white: #1e1e32; /* Represents 'white' surfaces, now dark */
    --shadow: rgba(255, 255, 255, 0.08); /* Lighter shadow */

    /* Specific component adjustments */
    --card-bg: #1e1e32;
    --card-border: #2a2a48;
    --card-text: #c0c0c0;
    --modal-bg: #23233a;
    --modal-header-bg: #1a1a2e;
    --button-bg: var(--primary-blue);
    --button-text: #ffffff;
    --button-hover-bg: #6262ff;
    --input-bg: #2a2a48;
    --input-text: #e0e0e0;
    --input-border: #3a3a5a;

    /* Ensure high contrast for text on primary backgrounds */
    --text-on-primary: #ffffff;
}

/* Dark Mode Overrides - Appended to end of file */
body[data-theme="dark"] {
    background: var(--secondary) !important;
    color: var(--text) !important;
}

body[data-theme="dark"] .header {
    background-color: var(--dark-bg) !important;
    box-shadow: 2px 0 5px var(--shadow-color) !important;
}

body[data-theme="dark"] .nav-wrapper {
    background-color: var(--dark-bg) !important; /* Ensure nav wrapper also dark */
}

body[data-theme="dark"] .logo {
    border-bottom: 1px solid var(--border) !important;
}

body[data-theme="dark"] .nav-item {
    color: var(--text-on-primary) !important;
}

body[data-theme="dark"] .nav-item:hover {
    color: var(--text-on-primary) !important; /* Keep text light on hover */
    transform: translateX(8px); /* Keep existing transform */
}

body[data-theme="dark"] .nav-item:hover:before {
    background: rgba(255, 255, 255, 0.15) !important; /* Slightly more visible hover effect */
}

body[data-theme="dark"] .nav-item i {
    color: var(--text-on-primary) !important; /* Ensure icons are light */
}

body[data-theme="dark"] .user-info {
    border-top: 1px solid var(--border) !important;
    background-color: var(--dark-bg) !important; /* Match header */
}

body[data-theme="dark"] .user-info h1 {
    color: var(--text-on-primary) !important;
}

body[data-theme="dark"] .user-name:hover {
    background-color: rgba(255, 255, 255, 0.15) !important;
}
/*
body[data-theme="dark"] .dropdown-content {
    background-color: var(--hover) !important;
    box-shadow: 0 2px 10px var(--shadow-color) !important;
    border: 1px solid var(--border) !important;
}*/

body[data-theme="dark"] .dropdown-item {
    /*color: var(--primary-blue) !important;  This should be a light/bright blue */
    border-bottom: 1px solid var(--border) !important;
}
/*
body[data-theme="dark"] .dropdown-item i {
    color: var(--primary-blue) !important;
}*/

body[data-theme="dark"] .dropdown-item:hover {
    background-color: var(--secondary) !important; /* Darker hover for dropdown items */
    color: var(--active) !important; /* Brighter blue on hover */
}

body[data-theme="dark"] .menu-toggle {
    background: var(--primary-blue) !important; /* Ensure toggle button has new primary color */
    color: var(--text-on-primary) !important;
}

body[data-theme="dark"] .tabs-wrapper {
    background: var(--secondary) !important; /* Dark background for tabs wrapper */
    border-bottom: 1px solid var(--border) !important; /* Ensure border is visible */
}

body[data-theme="dark"] .tabs {
    border-bottom: 1px solid var(--border) !important;
}

body[data-theme="dark"] .tab {
    color: var(--text) !important;
    background: transparent !important;
}

body[data-theme="dark"] .tab:hover {
    background: var(--hover) !important;
    color: var(--primary-blue) !important;
}

body[data-theme="dark"] .tab.active {
    background: var(--primary-blue) !important;
    color: var(--text-on-primary) !important;
}

body[data-theme="dark"] .content {
    background: var(--secondary) !important; /* Main content area dark */
    box-shadow: 0 4px 12px var(--shadow-color) !important;
    color: var(--text) !important;
}

body[data-theme="dark"] .card {
    background: var(--card-bg) !important;
    box-shadow: 0 2px 8px var(--shadow-color) !important;
    border: 1px solid var(--card-border) !important;
    color: var(--card-text) !important;
}

body[data-theme="dark"] .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow-color) !important; /* Slightly more prominent shadow */
}

body[data-theme="dark"] .planejamento-info h3,
body[data-theme="dark"] .resumo-container h3,
body[data-theme="dark"] .ciclo-container h3,
body[data-theme="dark"] .calendario h3 {
    color: var(--text-on-primary) !important;
    background-color: var(--dark-bg) !important; /* Use the very dark background for titles */
}

body[data-theme="dark"] .agenda {
    background: var(--card-bg) !important;
    border: 1px solid var(--border) !important;
    box-shadow: 0 8px 20px var(--shadow-color) !important;
}

body[data-theme="dark"] .agenda pre { /* Text inside agenda */
    color: var(--text) !important;
    background: var(--card-bg) !important; /* Ensure pre background matches card */
}




body[data-theme="dark"] .quadrado_pendente {
    background: rgba(233, 69, 96, 0.2) !important;
    border: 1px solid rgba(233, 69, 96, 0.4) !important;
    color: var(--text) !important;
}

body[data-theme="dark"] .quadrado_hoje {
    background: rgba(75, 192, 192, 0.2) !important;
    border: 1px solid rgba(75, 192, 192, 0.4) !important;
    color: var(--text) !important;
}

body[data-theme="dark"] .quadrado_proximo_1 {
    background: rgba(54, 162, 235, 0.2) !important;
    border: 1px solid rgba(54, 162, 235, 0.4) !important;
    color: var(--text) !important;
}

body[data-theme="dark"] .turno-card-compacto {
    background: var(--card-bg) !important;
    box-shadow: 0 2px 4px var(--shadow-color) !important;
    color: var(--text) !important;
}

body[data-theme="dark"] .turno-header {
    color: var(--text) !important;
}

body[data-theme="dark"] .turno-header i {
    background: rgba(184, 134, 11, 0.2) !important; /* Gold icon background, slightly more opaque */
}

body[data-theme="dark"] .turno-info strong {
    color: var(--text) !important;
}

body[data-theme="dark"] .turno-desc {
    color: var(--card-text) !important; /* Lighter text for description */
    border-top: 1px solid var(--border) !important;
}

body[data-theme="dark"] .dias-grid {
    /* background: var(--card-bg) !important; */
    /* box-shadow: 0 4px 6px var(--shadow-color) !important; */ /* Optional: shadow for this element */
}

body[data-theme="dark"] .dia-item.estudou {
    background: var(--primary-blue) !important; /* Use the brighter blue */
    border: 1px solid var(--primary-blue) !important;
}
body[data-theme="dark"] .dia-item.estudou::before {
    color: var(--text-on-primary) !important;
}
body[data-theme="dark"] .dia-item.estudou .dia-numero {
    color: rgba(255, 255, 255, 0.9) !important;
}


body[data-theme="dark"] .dia-item.nao-estudou {
    background: var(--danger-color) !important; /* Use danger color */
    border: 1px solid var(--danger-color) !important;
}
body[data-theme="dark"] .dia-item.nao-estudou::before {
    color: var(--text-on-primary) !important;
}
body[data-theme="dark"] .dia-item.nao-estudou .dia-numero {
    color: rgba(255, 255, 255, 0.9) !important;
}

body[data-theme="dark"] .dia-item.dia-atual {
    background: var(--hover) !important; /* Dark hover color for current day */
    border: 2px dashed var(--text) !important;
}
body[data-theme="dark"] .dia-item.dia-atual .dia-numero {
    color: var(--text) !important;
}

body[data-theme="dark"] .dia-item.dia-atual::after {
    color: var(--dark-bg) !important; /* Dark text on light background for tooltip */
    background: var(--text) !important; /* Light background for tooltip */
    box-shadow: 0 2px 4px var(--shadow-color) !important;
}


body[data-theme="dark"] .fc .fc-toolbar-title {
    color: var(--text) !important;
}

body[data-theme="dark"] .fc .fc-button-primary {
    background-color: var(--primary-blue) !important;
    border-color: var(--primary-blue) !important;
    color: var(--text-on-primary) !important;
}

body[data-theme="dark"] .fc .fc-button-primary:hover {
    background-color: var(--button-hover-bg) !important;
    border-color: var(--button-hover-bg) !important;
    color: var(--text-on-primary) !important;
}

body[data-theme="dark"] .fc .fc-button-primary:not(:disabled).fc-button-active, 
body[data-theme="dark"] .fc .fc-button-primary:not(:disabled):active {
    background-color: var(--button-hover-bg) !important; /* Maintain active state color */
    border-color: var(--button-hover-bg) !important;
    color: var(--text-on-primary) !important;
}

body[data-theme="dark"] .fc-daygrid-day-number {
    color: var(--text) !important;
}

body[data-theme="dark"] .fc-col-header-cell-cushion { /* Day headers like Mon, Tue */
    color: var(--text) !important;
}

body[data-theme="dark"] .fc-day-today { /* Today's date background */
    background-color: rgba(var(--primary-blue-rgb), 0.2) !important; 
}

/*body[data-theme="dark"] .fc-event {  Calendar events 
    background-color: var(--primary-blue) !important;
    border: 1px solid var(--primary-blue) !important;
    color: var(--text-on-primary) !important;
}*/

body[data-theme="dark"] .fc-event-main {
     color: var(--text-on-primary) !important;
}

body[data-theme="dark"] .fc-popover { /* Popover for "more events" */
    background-color: var(--card-bg) !important;
    border: 1px solid var(--border) !important;
}

body[data-theme="dark"] .fc-popover-header {
    background-color: var(--dark-bg) !important;
    color: var(--text-on-primary) !important;
}

body[data-theme="dark"] .fc-list-table th { /* List view headers */
    background-color: var(--dark-bg) !important;
    color: var(--text-on-primary) !important;
    border: 1px solid var(--border) !important;
}

body[data-theme="dark"] .fc-list-day-text, 
body[data-theme="dark"] .fc-list-day-side-text {
    color: var(--text) !important;
}

body[data-theme="dark"] .fc-list-event:hover td {
    background-color: var(--hover) !important;
}

body[data-theme="dark"] .modal-estudo.ativo .modal-content {
    background: var(--modal-bg) !important;
    color: var(--text) !important;
    box-shadow: 0 4px 20px var(--shadow-color) !important;
}

body[data-theme="dark"] .modal-close {
    background: var(--accent) !important;
    color: var(--text-on-primary) !important;
}

body[data-theme="dark"] .modal-titulo {
    color: var(--text) !important;
    background: var(--modal-header-bg) !important;
    border-bottom: 2px solid var(--vintage-gold) !important; /* Keep or adjust */
}

body[data-theme="dark"] .modal-body::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1) !important;
}

body[data-theme="dark"] .modal-body::-webkit-scrollbar-thumb {
    background: var(--vintage-gold) !important; /* Keep or adjust */
}

body[data-theme="dark"] .sem-eventos {
    background: var(--hover) !important;
    color: var(--card-text) !important;
}
body[data-theme="dark"] .sem-eventos i {
    color: var(--card-text) !important;
}
body[data-theme="dark"] .sem-eventos p {
    color: var(--card-text) !important;
}

body[data-theme="dark"] .card-planejamento {
    background: var(--card-bg) !important;
    box-shadow: 0 2px 4px var(--shadow-color) !important;
}

body[data-theme="dark"] .info-item {
    background: var(--hover) !important;
    color: var(--text) !important;
}

body[data-theme="dark"] .status-badge.pendente {
    background: rgba(245, 124, 0, 0.2) !important; /* Adjusted for dark */
    color: #f57c00 !important;
}

body[data-theme="dark"] .status-badge.concluido {
    background: rgba(67, 160, 71, 0.2) !important; /* Adjusted for dark */
    color: #43a047 !important;
}

body[data-theme="dark"] .barra-progresso {
    background: var(--hover) !important;
}

body[data-theme="dark"] .progresso-preenchimento {
    background: var(--primary-blue) !important;
}

body[data-theme="dark"] .porcentagem {
    color: var(--primary-blue) !important;
}

body[data-theme="dark"] .header-nav::-webkit-scrollbar-track {
    background: var(--dark-bg) !important; /* Darker track for nav scrollbar */
}

body[data-theme="dark"] .header-nav::-webkit-scrollbar-thumb {
    background: var(--primary-blue) !important;
}

body[data-theme="dark"] .fc-popover-body::-webkit-scrollbar-track {
    background: var(--card-bg) !important;
}
body[data-theme="dark"] .fc-popover-body::-webkit-scrollbar-thumb {
    background: var(--primary-blue) !important;
}

body[data-theme="dark"] .dias-grid::-webkit-scrollbar-track {
    background: var(--card-bg) !important;
}
body[data-theme="dark"] .dias-grid::-webkit-scrollbar-thumb {
    background: var(--primary-blue) !important;
}

body[data-theme="dark"] .toggle-nav-btn {
    background: var(--primary-blue) !important;
    border: 2px solid var(--text-on-primary) !important; /* Ensure border contrasts */
    color: var(--text-on-primary) !important;
}

body[data-theme="dark"] .header.collapsed .toggle-nav-btn i {
    transform: rotate(180deg); /* Already there, just ensure it's not overridden */
}

body[data-theme="dark"] .header.collapsed .nav-item:hover::after { /* Tooltip in collapsed nav */
    background: var(--hover) !important;
    color: var(--text) !important;
    box-shadow: 0 2px 5px var(--shadow-color) !important;
}

body[data-theme="dark"] .header.collapsed .dropdown-content {
    background-color: var(--dark-bg) !important; /* Match header for collapsed dropdown */
}

body[data-theme="dark"] .header.collapsed .dropdown-content .dropdown-item:hover {
    background-color: var(--hover) !important;
}

/* Modal Ranking Styles */
body[data-theme="dark"] .modal-ranking-overlay {
  background: rgba(18, 18, 32, 0.7) !important; /* Darker overlay */
  backdrop-filter: blur(5px) saturate(1.2);
}
body[data-theme="dark"] .modal-ranking-content {
  background: linear-gradient(160deg, var(--secondary) 0%, var(--dark-bg) 100%) !important;
  border: 2.5px solid var(--border) !important;
  box-shadow: 0 10px 40px 0 var(--shadow-color), 0 1.5px 10px 0 var(--shadow-color) !important;
  color: var(--text) !important;
}
body[data-theme="dark"] .modal-ranking-close {
  background: var(--card-bg) !important;
  border: 2px solid var(--border) !important;
  color: var(--text) !important;
  box-shadow: 0 2px 8px var(--shadow-color) !important;
}
body[data-theme="dark"] .modal-ranking-close:hover {
  background: var(--accent) !important;
  color: var(--text-on-primary) !important;
}
body[data-theme="dark"] .loader-spinner {
  border: 6px solid var(--hover) !important;
  border-top: 6px solid var(--primary-blue) !important;
}
body[data-theme="dark"] #modal-ranking-body h1, 
body[data-theme="dark"] #modal-ranking-body .page-title {
  color: var(--primary-blue) !important;
}
body[data-theme="dark"] #modal-ranking-body table {
  background: var(--card-bg) !important;
  box-shadow: 0 2px 16px var(--shadow-color) !important;
}
body[data-theme="dark"] #modal-ranking-body th {
  background: linear-gradient(90deg, var(--primary-blue) 0%, var(--primary-blue) 100%) !important; /* Darker gradient for header */
  color: var(--text-on-primary) !important;
}
body[data-theme="dark"] #modal-ranking-body tr:nth-child(even) {
  background: var(--hover) !important;
}
body[data-theme="dark"] #modal-ranking-body .destaque {
  background: var(--vintage-gold) !important; /* Gold might work, or use accent */
  color: var(--dark-bg) !important; /* Dark text on gold */
  box-shadow: 0 1px 6px var(--shadow-color) !important;
}
body[data-theme="dark"] #modal-ranking-body .voce {
  color: #66bb6a !important; /* Lighter green for dark theme */
}
body[data-theme="dark"] #modal-ranking-body .footer {
  color: var(--card-text) !important;
}

/* Progress bar specific for dark theme */
body[data-theme="dark"] .progress {
    background: var(--input-bg) !important; /* Darker background for progress bar track */
    border: 1px solid var(--border) !important;
}

body[data-theme="dark"] .progress-bar {
    background: linear-gradient(45deg, var(--primary-blue), var(--active)) !important; /* Brighter gradient for bar */
    color: var(--text-on-primary) !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* Ensure .proxima-materia and .ultima-materia badges are visible */
body[data-theme="dark"] .proxima-materia::after {
    background: var(--active) !important; /* Brighter blue */
    color: var(--text-on-primary) !important;
    box-shadow: 0 2px 4px var(--shadow-color) !important;
}
body[data-theme="dark"] .ultima-materia::after {
    background: var(--accent) !important; /* Use accent color */
    color: var(--text-on-primary) !important;
    box-shadow: 0 2px 4px var(--shadow-color) !important;
}
body[data-theme="dark"] .proxima-materia {
    border: 2px solid var(--active) !important;
}
body[data-theme="dark"] .ultima-materia {
    border: 2px solid var(--accent) !important;
}

/* SweetAlert2 dark theme adjustments */
body[data-theme="dark"] .swal2-popup {
    background: var(--modal-bg) !important;
    color: var(--text) !important;
}
body[data-theme="dark"] .swal2-title {
    color: var(--text) !important;
}
body[data-theme="dark"] .swal2-html-container {
    color: var(--text) !important;
}
body[data-theme="dark"] .swal2-confirm,
body[data-theme="dark"] .swal2-cancel,
body[data-theme="dark"] .swal2-deny {
    background: var(--primary-blue) !important;
    color: var(--text-on-primary) !important;
    border: none !important; /* Remove default borders that might look off */
}
body[data-theme="dark"] .swal2-confirm:hover,
body[data-theme="dark"] .swal2-cancel:hover,
body[data-theme="dark"] .swal2-deny:hover {
    background: var(--button-hover-bg) !important;
}
body[data-theme="dark"] .swal2-styled:focus {
    box-shadow: none !important; /* Remove default focus shadow if it clashes */
}

</style>

</head>
<body>
    <header class="header">
        <div class="nav-wrapper">
            <div class="logo">
                <a href="./index.php">
                    <img src="logo/logo_vertical.png" alt="Logo" class="logo-expanded">
                    <img src="logo/logo_vertical_redu.png" alt="Logo" class="logo-collapsed">
                </a>
            </div>
            <nav class="header-nav">
                <button id="toggleNav" class="toggle-nav-btn">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <!-- Atualizando para verificar se o usuário está logado -->
                <?php if(isset($_SESSION['idusuario'])): ?>
                    <a href="#" class="nav-item" onclick="abrirPopUp50()">
                        <i class="fas fa-clock"></i> <span>Cronômetro</span>
                    </a>
                    <a href="#" class="nav-item" onclick="abrirPopUp6()">
                        <i class="fas fa-history"></i> <span>Histórico</span>
                    </a>
                    <a href="#" class="nav-item" onclick="abrirPopUp40()">
                        <i class="fas fa-sticky-note"></i> <span>Anotações</span>
                    </a>
                    <a href="#" class="nav-item" onclick="abrirPopUp80()">
                        <i class="fas fa-layer-group"></i> <span>Flashcards</span>
                    </a>
                    <a href="#" class="nav-item" onclick="abrirPopUp60()">
                        <i class="fas fa-brain"></i> <span>Cronograma Inteligente</span>
                    </a>
                    <a href="#" class="nav-item" onclick="abrirPopUp90()">
                        <i class="fas fa-book"></i> <span>Cadernos de Lei</span>
                    </a>
                    <a href="#" class="nav-item" onclick="abrirPopUpRevisao()">
                        <i class="fas fa-sync"></i> <span>Revisões</span>
                    </a>
                    <a href="#" class="nav-item" onclick="abrirPopUpForum()">
                        <i class="fas fa-comments"></i> <span>Fórum</span>
                    </a>
                    <a href="#" class="nav-item" id="abrirRanking">
                        <i class="fas fa-trophy"></i>
<span>Ranking Geral
  <span id="ranking-posicao">...</span>
</span>
                    </a>
                    <a href="#" class="nav-item" id="abrirRankingCursos">
                        <i class="fas fa-graduation-cap"></i> <span>Ranking Cursos</span>
                    </a>
                    <a href="#" class="nav-item" onclick="abrirPopUpAgendaAutomatica()">
                        <i class="fas fa-robot"></i> <span>Agenda Automática</span>
                    </a>
                <?php endif; ?>
            </nav>
            <div class="header-right">
                <button class="menu-toggle" aria-label="Menu">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <div class="user-info">
                <div class="user-dropdown">
                    <h1 class="user-name" onclick="toggleDropdown(event)">
                        <i class="fas fa-user-circle"></i> 
                        <?php echo "$nome_usuario"; ?>
                        <i class="fas fa-chevron-down dropdown-arrow"></i>
                    </h1>
                    <div class="dropdown-content" id="userDropdown">
                        <a href="#" class="dropdown-item" id="dark-mode-toggle">
                            <i class="fas fa-moon"></i> <span>Modo Escuro</span>
                        </a>
                        <a href="usuario.php" class="dropdown-item">
                            <i class="fas fa-user"></i> <span>Perfil</span>
                        </a>
                        <a href="https://wa.me/5561900000000" target="_blank" class="dropdown-item">
                            <i class="fab fa-whatsapp"></i> <span>Fale Conosco</span>
                        </a>
                        <?php if(isset($_SESSION['idusuario']) && $_SESSION['idusuario'] == 1): ?>
                            <a href="admin/index.php" target="_blank" class="dropdown-item">
                                <i class="fas fa-shield-alt"></i> <span>Área Administrativa</span>
                            </a>
                            <a href="kanban/index.php" target="_blank" class="dropdown-item">
                                <i class="fas fa-tasks"></i> <span>Kanban Pessoal</span>
                            </a>
                        <?php endif; ?>
                        <?php if(isset($_SESSION['idusuario']) && ($_SESSION['idusuario'] == 1 || $_SESSION['idusuario'] == 47)): ?>
                            <a href="cadastros/index.php" target="_blank" class="dropdown-item">
                                <i class="fas fa-folder-plus"></i> <span>Área de Cadastros</span>
                            </a>
                        <?php endif; ?>
                        <a href="sair.php" class="dropdown-item">
                            <i class="fas fa-sign-out-alt"></i> <span>Sair</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </header>
<!-- Modal Ranking -->
<div id="modal-ranking" class="modal-ranking-overlay" style="display:none;">
  <div class="modal-ranking-content">
    <div class="modal-ranking-bar"></div>
    <button class="modal-ranking-close" onclick="fecharModalRanking()">&times;</button>
    <div id="modal-ranking-body">
      <div style="text-align:center;padding:2rem 0;">
        <span class="loader-spinner"></span>
        <div>Carregando ranking...</div>
      </div>
    </div>
  </div>
</div>
<style>
.modal-ranking-overlay {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0, 8, 139, 0.10);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(5px) saturate(1.2);
  transition: background 0.3s;
}
.modal-ranking-content {
  background: linear-gradient(160deg, #f8faff 0%, #e3eafd 100%);
  border-radius: 20px;
  box-shadow: 0 10px 40px 0 rgba(0,8,139,0.14), 0 1.5px 10px 0 rgba(0,0,0,0.07);
  border: 2.5px solid #00008B22;
  padding: 0 2rem 2.2rem 2rem; /* Adicionado padding lateral de 2rem */
  max-width: 800px; /* Aumentado um pouco a largura máxima */
  width: 96vw;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: popin-modal 0.35s cubic-bezier(.6,-0.28,.74,.05);
  font-family: 'Quicksand', 'Varela Round', sans-serif;
}
@keyframes popin-modal { from {transform: scale(0.92) translateY(40px); opacity:0;} to {transform: scale(1) translateY(0); opacity:1;} }
.modal-ranking-close {
  position: absolute;
  right: 1.5rem; top: 1.5rem;
  font-size: 1.8rem;
  background: #fff;
  border: 2px solid #00008B33;
  border-radius: 50%;
  width: 42px;
  height: 42px;
  color: #00008B;
  cursor: pointer;
  box-shadow: 0 2px 8px #00008b15;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  z-index: 2;
}
.modal-ranking-close:hover {
  background: #00008B;
  color: #fff;
  box-shadow: 0 4px 18px #00008b22;
}
.loader-spinner {
  border: 6px solid #e3eafd;
  border-top: 6px solid #00008B;
  border-radius: 50%;
  width: 48px;
  height: 48px;
  animation: spin 1s linear infinite;
  display: inline-block;
}
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
/* Modal header gradient bar */
.modal-ranking-content .modal-ranking-bar {
  height: 8px;
  width: 100%;
  background: linear-gradient(90deg,var(--primary-blue) 0%, var(--primary-blue) 100%);
  /* border-radius: 20px 20px 0 0; */
  margin-bottom: 0.6rem;
}
/* Ranking table harmonização */
#modal-ranking-body .container {
  background: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  border: none !important;
}
#modal-ranking-body h1, #modal-ranking-body .page-title {
  color: var(--primary-blue);
  font-family: 'Quicksand', 'Varela Round', sans-serif;
  font-size: 2.1rem;
  margin-bottom: 1.2rem;
  font-weight: 700;
  text-align: center;
  letter-spacing: 0.5px;
}
#modal-ranking-body table {
  border-radius: 16px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 16px #00008b0e;
  margin-bottom: 1.5rem;
}
#modal-ranking-body th {
  background: linear-gradient(90deg,var(--primary-blue) 0%, var(--primary-blue) 100%);
  color: #fff;
  font-weight: 600;
  font-size: 1.08rem;
  letter-spacing: 0.5px;
}
#modal-ranking-body td, #modal-ranking-body th {
  padding: 0.85rem 0.5rem;
  text-align: center;
  font-size: 1.08rem;
}
#modal-ranking-body tr:nth-child(even) {
  background: #f4f6fa;
}
#modal-ranking-body .destaque {
  background: #ffe082 !important;
  color: var(--primary-blue);
  font-weight: bold;
  box-shadow: 0 1px 6px #00008b18;
}
#modal-ranking-body .posicao {
  font-size: 1.1rem;
  font-weight: bold;
}
#modal-ranking-body .voce {
  color: #388e3c;
  font-weight: bold;
}
#modal-ranking-body .footer {
  text-align: center;
  color: #888;
  font-size: 1rem;
  margin-top: 2rem;
}
/* Destaque visual para o top 3 */
#modal-ranking-body tbody tr:nth-child(1) td.posicao { color: #FFD700; font-size:1.3rem; text-shadow: 0 1px 6px #FFD70099; }
#modal-ranking-body tbody tr:nth-child(2) td.posicao { color: #C0C0C0; font-size:1.2rem; text-shadow: 0 1px 4px #C0C0C099; }
#modal-ranking-body tbody tr:nth-child(3) td.posicao { color: #CD7F32; font-size:1.15rem; text-shadow: 0 1px 4px #CD7F3299; }
@media (max-width: 600px) {
  .modal-ranking-content { padding: 0; max-width: 99vw; }
  #modal-ranking-body h1 { font-size: 1.3rem; }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Evento para Ranking Geral
    var btnRanking = document.getElementById('abrirRanking');
    if (btnRanking) btnRanking.onclick = abrirModalRanking;

    // Evento para Ranking de Cursos
    var btnRankingCursos = document.getElementById('abrirRankingCursos');
    if (btnRankingCursos) btnRankingCursos.onclick = abrirModalRankingCursos;

    // Fechar modal ao clicar fora
    document.getElementById('modal-ranking').addEventListener('click', function(e) {
        if (e.target === this) fecharModalRanking();
    });

    // Buscar posição do usuário no ranking
    fetch('includes/ranking_posicao_usuario.php')
        .then(resp => resp.text())
        .then(pos => {
            var el = document.getElementById('ranking-posicao');
            if (el) el.textContent = (pos && !isNaN(Number(pos))) ? `${pos}º` : '-';
        })
        .catch(() => {
            var el = document.getElementById('ranking-posicao');
            if (el) el.textContent = '-';
        });
});

function abrirModalRanking() {
    document.getElementById('modal-ranking').style.display = 'flex';
    document.body.style.overflow = 'hidden';
    document.getElementById('modal-ranking-body').innerHTML = '<div style="text-align:center;padding:2rem 0;"><span class="loader-spinner"></span><div>Carregando ranking...</div></div>';
    fetch('includes/ranking_usuario_atual.php')
        .then(resp => resp.text())
        .then(html => {
            document.getElementById('modal-ranking-body').innerHTML = html;
        })
        .catch(() => {
            document.getElementById('modal-ranking-body').innerHTML = '<div style="color:#c00;text-align:center;">Erro ao carregar ranking.</div>';
        });
}

function abrirModalRankingCursos() {
    document.getElementById('modal-ranking').style.display = 'flex';
    document.body.style.overflow = 'hidden';
    document.getElementById('modal-ranking-body').innerHTML = '<div style="text-align:center;padding:2rem 0;"><span class="loader-spinner"></span><div>Carregando ranking de cursos...</div></div>';
    fetch('includes/ranking_cursos.php')
        .then(resp => resp.text())
        .then(html => {
            document.getElementById('modal-ranking-body').innerHTML = html;
        })
        .catch(() => {
            document.getElementById('modal-ranking-body').innerHTML = '<div style="color:#c00;text-align:center;">Erro ao carregar ranking de cursos.</div>';
        });
}

function fecharModalRanking() {
    document.getElementById('modal-ranking').style.display = 'none';
    document.body.style.overflow = '';
}
</script>
    <script>
        document.querySelector('.menu-toggle').addEventListener('click', function() {
            document.querySelector('.header').classList.toggle('active');
        });
    </script>
    <script>
        function toggleDropdown(event) {
            event.stopPropagation(); // Impede o clique de fechar imediatamente
            const dropdown = document.getElementById('userDropdown');
            const arrow = document.querySelector('.dropdown-arrow');
            dropdown.classList.toggle('show');
            arrow.classList.toggle('rotate');
        }

        // Fecha o dropdown ao clicar fora
        document.addEventListener('click', function(e) {
            const dropdown = document.getElementById('userDropdown');
            const arrow = document.querySelector('.dropdown-arrow');
            if (dropdown && dropdown.classList.contains('show')) {
                // Se o clique não foi no user-name nem dentro do dropdown
                if (!e.target.closest('.user-name') && !e.target.closest('#userDropdown')) {
                    dropdown.classList.remove('show');
                    if (arrow) arrow.classList.remove('rotate');
                }
            }
        });
    </script>
</body>
<script>
document.getElementById('toggleNav').addEventListener('click', function() {
    document.querySelector('.header').classList.toggle('collapsed');
    
    // Salvar o estado no localStorage
    const isCollapsed = document.querySelector('.header').classList.contains('collapsed');
    localStorage.setItem('navCollapsed', isCollapsed);
});

// Recuperar o estado salvo ao carregar a página
document.addEventListener('DOMContentLoaded', function() {
    const isCollapsed = localStorage.getItem('navCollapsed') === 'true';
    if (isCollapsed) {
        document.querySelector('.header').classList.add('collapsed');
    }
});
</script>
</body>
