<?php
session_start();
require_once '../../conexao_POST.php';

header('Content-Type: application/json');

try {
    if (!isset($_GET['id'])) {
        throw new Exception('ID da tarefa não fornecido');
    }

    $sql = "SELECT id, titulo, descricao, status, prioridade, 
                   data_limite::date::text as data_limite
            FROM appestudo.kanban_tarefas 
            WHERE id = $1 AND usuario_id = $2";

    $result = pg_query_params($conexao, $sql, array(
        $_GET['id'],
        $_SESSION['idusuario']
    ));

    if (!$result) {
        throw new Exception(pg_last_error($conexao));
    }

    $tarefa = pg_fetch_assoc($result);
    
    if (!$tarefa) {
        throw new Exception('Tarefa não encontrada ou sem permissão');
    }

    echo json_encode($tarefa);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['erro' => $e->getMessage()]);
}

