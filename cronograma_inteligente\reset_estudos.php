<?php
//reset_estudos.php
require_once 'includes/init.php';

if (!isset($_SESSION['idusuario'])) {
   http_response_code(401);
   echo json_encode(['error' => 'Usuário não autenticado']);
   exit;
}

$usuario_id = $_SESSION['idusuario'];

try {
   pg_query($conexao, "BEGIN");

   // Atualiza conteúdos para não estudado
   $query_conteudos = "UPDATE appestudo.usuario_conteudo uc
                       SET status_estudo = 'Não Estudado'
                       FROM appestudo.conteudo_edital ce
                       JOIN appestudo.usuario_edital ue ON ce.edital_id = ue.edital_id
                       WHERE uc.conteudo_id = ce.id_conteudo
                       AND uc.usuario_id = $1 
                       AND ue.usuario_id = $1
                       AND uc.status = true";
   
   $result_conteudos = pg_query_params($conexao, $query_conteudos, array($usuario_id));

   if (!$result_conteudos) {
       throw new Exception(pg_last_error($conexao));
   }

   // Remove revisões do edital atual
   $query_revisoes = "DELETE FROM appestudo.revisoes r
                     USING appestudo.usuario_edital ue
                     WHERE r.usuario_id = $1 
                     AND r.edital_id = ue.edital_id
                     AND ue.usuario_id = $1";
   
   $result_revisoes = pg_query_params($conexao, $query_revisoes, array($usuario_id));

   if (!$result_revisoes) {
       throw new Exception(pg_last_error($conexao));
   }

   // NOVA QUERY: Atualiza a flag replanejado para false na prova ativa
   $query_update_prova = "UPDATE appestudo.provas 
                          SET replanejado = false 
                          WHERE usuario_id = $1 
                          AND status = true";
   
   $result_prova = pg_query_params($conexao, $query_update_prova, array($usuario_id));
   
   if (!$result_prova) {
       throw new Exception(pg_last_error($conexao));
   }

   pg_query($conexao, "COMMIT");

   echo json_encode([
       'success' => true, 
       'message' => 'Progresso e revisões resetados com sucesso'
   ]);

} catch (Exception $e) {
   pg_query($conexao, "ROLLBACK");
   http_response_code(500);
   echo json_encode(['error' => 'Erro ao resetar progresso: ' . $e->getMessage()]);
}
?>