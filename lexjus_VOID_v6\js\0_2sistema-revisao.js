/**
 * Sistema de Revisão Inteligente para LexJus VOID
 * Baseado em repetição espaçada e algoritmos de memorização
 */

class SistemaRevisao {
    constructor() {
        this.revisoesPendentes = [];
        this.revisaoAtual = null;
        this.estatisticas = {};
        this.configuracao = {};
        this.tempoInicioRevisao = null;

        this.init();
    }

    /**
     * Inicializa o sistema de revisão
     */
    async init() {
        try {
            await this.carregarDados();
            this.criarInterface();
            this.configurarEventListeners();
            this.atualizarInterface();
        } catch (error) {
            console.error('Erro ao inicializar sistema de revisão:', error);
        }
    }

    /**
     * Carrega dados do servidor para uma lei específica
     */
    async carregarDados(leiCodigo = null) {
        try {
            // Obter lei atual se não foi especificada
            if (!leiCodigo) {
                leiCodigo = document.body.getAttribute('data-lei-codigo') || 'CF';
            }

            console.log(`📊 Carregando dados de revisão para lei: ${leiCodigo}`);

            // Carregar revisões pendentes para a lei específica
            const responsePendentes = await fetch(`api/revisao.php?acao=pendentes&lei=${leiCodigo}`);
            if (responsePendentes.ok) {
                const dataPendentes = await responsePendentes.json();
                this.revisoesPendentes = dataPendentes.revisoes_pendentes || [];

                console.log(`📚 ${this.revisoesPendentes.length} revisões pendentes carregadas para ${leiCodigo}`);
                console.log(`🔍 DEBUG: Resposta completa da API:`, dataPendentes);

                if (this.revisoesPendentes.length > 0) {
                    console.log(`📋 Artigos pendentes para ${leiCodigo}:`, this.revisoesPendentes.map(r => r.artigo_numero));
                } else {
                    console.log(`ℹ️ Nenhuma revisão pendente encontrada para lei ${leiCodigo}`);
                }
            }

            // Carregar estatísticas para a lei específica
            const responseStats = await fetch(`api/revisao.php?acao=estatisticas&lei=${leiCodigo}`);
            if (responseStats.ok) {
                const dataStats = await responseStats.json();
                this.estatisticas = dataStats.estatisticas || {};
                console.log(`📈 Estatísticas carregadas para ${leiCodigo}:`, this.estatisticas);
            }

            // Carregar configuração
            const responseConfig = await fetch('api/revisao.php?acao=configuracao');
            if (responseConfig.ok) {
                const dataConfig = await responseConfig.json();
                this.configuracao = dataConfig.configuracao || {};
            }

            // Disparar evento para notificar que dados foram carregados
            this.dispararEventoRevisaoAtualizada('carregada', leiCodigo);

        } catch (error) {
            console.error('Erro ao carregar dados de revisão:', error);
        }
    }

    /**
     * Cria a interface do sistema de revisão
     */
    criarInterface() {
        console.log('🔧 Sistema de Revisão: Criando interface...');

        // Verificar se o botão de revisão já existe no HTML
        let btnRevisao = document.getElementById('btnRevisao');

        if (!btnRevisao) {
            console.log('🔧 Sistema de Revisão: Botão não encontrado, criando novo...');
            // Adicionar botão de revisão na barra de navegação apenas se não existir
            const navControls = document.querySelector('.nav-controls');
            if (navControls) {
                btnRevisao = document.createElement('button');
                btnRevisao.id = 'btnRevisao';
                btnRevisao.className = 'nav-btn btn-revisao';
                btnRevisao.title = 'Sistema de Revisão';
                btnRevisao.innerHTML = `
                    <i class="fas fa-brain"></i>
                    <span class="btn-text">Revisão</span>
                    <span id="revisaoCount" class="count-badge">0</span>
                `;
                navControls.insertBefore(btnRevisao, navControls.lastElementChild);
                console.log('✅ Sistema de Revisão: Botão criado dinamicamente');
            }
        } else {
            console.log('✅ Sistema de Revisão: Botão já existe no HTML, reutilizando...');
            // Se o botão já existe, verificar se tem o contador
            let contador = btnRevisao.querySelector('#revisaoCount');
            if (!contador) {
                // Adicionar contador se não existir
                contador = document.createElement('span');
                contador.id = 'revisaoCount';
                contador.className = 'count-badge';
                contador.textContent = '0';
                btnRevisao.appendChild(contador);
                console.log('✅ Sistema de Revisão: Contador adicionado ao botão existente');
            } else {
                console.log('✅ Sistema de Revisão: Contador já existe');
            }
        }

        // Garantir que o botão esteja visível
        if (btnRevisao) {
            btnRevisao.style.display = 'flex';
            console.log('✅ Sistema de Revisão: Botão configurado como visível');
        }

        // Criar modal de revisão
        this.criarModalRevisao();

        // Criar modal de estatísticas
        this.criarModalEstatisticas();
    }

    /**
     * Cria o modal principal de revisão
     */
    criarModalRevisao() {
        const modalHTML = `
            <div id="revisaoModal" class="modal">
                <div class="modal-content modal-revisao">
                    <span class="modal-close-btn">&times;</span>

                    <!-- Header do Modal -->
                    <div class="modal-header">
                        <h2><i class="fas fa-brain"></i> Sistema de Revisão</h2>
                        <div class="revisao-stats-mini">
                            <span class="stat-mini">
                                <i class="fas fa-clock"></i>
                                <span id="pendentesCount">0</span> pendentes
                            </span>
                            <span class="stat-mini">
                                <i class="fas fa-trophy"></i>
                                <span id="dominadosCount">0</span> dominados
                            </span>
                        </div>
                    </div>

                    <!-- Área de Revisão -->
                    <div id="areaRevisao" class="area-revisao">
                        <!-- Estado: Aguardando -->
                        <div id="estadoAguardando" class="estado-revisao">
                            <div class="revisao-dashboard">
                                <h3>📚 Painel de Revisão</h3>

                                <div class="revisao-cards">
                                    <div class="revisao-card pendentes">
                                        <div class="card-icon"><i class="fas fa-clock"></i></div>
                                        <div class="card-content">
                                            <div class="card-number" id="dashPendentes">0</div>
                                            <div class="card-label">Pendentes</div>
                                        </div>
                                    </div>

                                    <div class="revisao-card aprendendo">
                                        <div class="card-icon"><i class="fas fa-graduation-cap"></i></div>
                                        <div class="card-content">
                                            <div class="card-number" id="dashAprendendo">0</div>
                                            <div class="card-label">Aprendendo</div>
                                        </div>
                                    </div>

                                    <div class="revisao-card dominados">
                                        <div class="card-icon"><i class="fas fa-trophy"></i></div>
                                        <div class="card-content">
                                            <div class="card-number" id="dashDominados">0</div>
                                            <div class="card-label">Dominados</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="revisao-actions">
                                    <button id="btnIniciarRevisao" class="btn-iniciar-revisao">
                                        <i class="fas fa-play"></i>
                                        Iniciar Revisão
                                    </button>
                                    <button id="btnEstatisticas" class="btn-estatisticas">
                                        <i class="fas fa-chart-bar"></i>
                                        Ver Estatísticas
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Estado: Revisando -->
                        <div id="estadoRevisando" class="estado-revisao" style="display: none;">
                            <div class="revisao-sessao">
                                <div class="sessao-header">
                                    <div class="artigo-info">
                                        <h3 id="artigoRevisaoNumero">Art. 1º</h3>
                                        <div class="artigo-meta">
                                            <span class="meta-item">
                                                <i class="fas fa-repeat"></i>
                                                <span id="artigoRepeticoes">0</span> repetições
                                            </span>
                                            <span class="meta-item">
                                                <i class="fas fa-star"></i>
                                                Facilidade: <span id="artigoFacilidade">2.5</span>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="sessao-progresso">
                                        <span id="sessaoAtual">1</span> / <span id="sessaoTotal">10</span>
                                    </div>
                                </div>

                                <div class="artigo-conteudo" id="artigoRevisaoConteudo">
                                    <!-- Conteúdo do artigo será carregado aqui -->
                                </div>

                                <div class="revisao-controles">
                                    <h4>Como você avalia seu conhecimento sobre este artigo?</h4>
                                    <div class="qualidade-buttons">
                                        <button class="btn-qualidade" data-qualidade="0">
                                            <i class="fas fa-times-circle"></i>
                                            <span>Não lembro</span>
                                        </button>
                                        <button class="btn-qualidade" data-qualidade="1">
                                            <i class="fas fa-frown"></i>
                                            <span>Muito difícil</span>
                                        </button>
                                        <button class="btn-qualidade" data-qualidade="2">
                                            <i class="fas fa-meh"></i>
                                            <span>Difícil</span>
                                        </button>
                                        <button class="btn-qualidade" data-qualidade="3">
                                            <i class="fas fa-smile"></i>
                                            <span>Normal</span>
                                        </button>
                                        <button class="btn-qualidade" data-qualidade="4">
                                            <i class="fas fa-grin"></i>
                                            <span>Fácil</span>
                                        </button>
                                        <button class="btn-qualidade" data-qualidade="5">
                                            <i class="fas fa-star"></i>
                                            <span>Muito fácil</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Estado: Resultado -->
                        <div id="estadoResultado" class="estado-revisao" style="display: none;">
                            <div class="revisao-resultado">
                                <div class="resultado-header">
                                    <i class="fas fa-check-circle resultado-icon"></i>
                                    <h3>Sessão Concluída!</h3>
                                </div>

                                <div class="resultado-stats">
                                    <div class="stat-resultado">
                                        <span class="stat-label">Artigos revisados:</span>
                                        <span class="stat-value" id="resultadoRevisados">0</span>
                                    </div>
                                    <div class="stat-resultado">
                                        <span class="stat-label">Tempo total:</span>
                                        <span class="stat-value" id="resultadoTempo">0min</span>
                                    </div>
                                    <div class="stat-resultado">
                                        <span class="stat-label">Próxima sessão:</span>
                                        <span class="stat-value" id="resultadoProxima">Amanhã</span>
                                    </div>
                                </div>

                                <div class="resultado-actions">
                                    <button id="btnNovaRevisao" class="btn-nova-revisao">
                                        <i class="fas fa-redo"></i>
                                        Nova Sessão
                                    </button>
                                    <button id="btnFecharRevisao" class="btn-fechar-revisao">
                                        <i class="fas fa-times"></i>
                                        Fechar
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    /**
     * Cria o modal de estatísticas
     */
    criarModalEstatisticas() {
        const modalHTML = `
            <div id="estatisticasModal" class="modal">
                <div class="modal-content modal-estatisticas">
                    <span class="modal-close-btn">&times;</span>

                    <div class="modal-header">
                        <h2><i class="fas fa-chart-bar"></i> Estatísticas de Revisão</h2>
                    </div>

                    <div class="estatisticas-content">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-book"></i></div>
                                <div class="stat-info">
                                    <div class="stat-number" id="statTotalArtigos">0</div>
                                    <div class="stat-label">Total de Artigos</div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-clock"></i></div>
                                <div class="stat-info">
                                    <div class="stat-number" id="statPendentes">0</div>
                                    <div class="stat-label">Pendentes</div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-trophy"></i></div>
                                <div class="stat-info">
                                    <div class="stat-number" id="statDominados">0</div>
                                    <div class="stat-label">Dominados</div>
                                </div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-icon"><i class="fas fa-star"></i></div>
                                <div class="stat-info">
                                    <div class="stat-number" id="statFacilidadeMedia">0</div>
                                    <div class="stat-label">Facilidade Média</div>
                                </div>
                            </div>
                        </div>

                        <div class="progress-section">
                            <h3>Distribuição por Status</h3>
                            <div class="progress-bars">
                                <div class="progress-item">
                                    <span class="progress-label">Novos</span>
                                    <div class="progress-bar">
                                        <div class="progress-fill novos" id="progressNovos"></div>
                                    </div>
                                    <span class="progress-value" id="valueNovos">0</span>
                                </div>

                                <div class="progress-item">
                                    <span class="progress-label">Aprendendo</span>
                                    <div class="progress-bar">
                                        <div class="progress-fill aprendendo" id="progressAprendendo"></div>
                                    </div>
                                    <span class="progress-value" id="valueAprendendo">0</span>
                                </div>

                                <div class="progress-item">
                                    <span class="progress-label">Revisando</span>
                                    <div class="progress-bar">
                                        <div class="progress-fill revisando" id="progressRevisando"></div>
                                    </div>
                                    <span class="progress-value" id="valueRevisando">0</span>
                                </div>

                                <div class="progress-item">
                                    <span class="progress-label">Dominados</span>
                                    <div class="progress-bar">
                                        <div class="progress-fill dominados" id="progressDominados"></div>
                                    </div>
                                    <span class="progress-value" id="valueDominados">0</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', modalHTML);
    }

    /**
     * Configura event listeners
     */
    configurarEventListeners() {
        // Botão principal de revisão
        const btnRevisao = document.getElementById('btnRevisao');
        if (btnRevisao) {
            btnRevisao.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('🖱️ Clique no botão de revisão detectado');
                console.log(`📊 Revisões pendentes: ${this.revisoesPendentes.length}`);

                if (this.revisoesPendentes.length === 0) {
                    console.log('⚠️ Nenhuma revisão pendente - recarregando dados...');
                    this.carregarDados().then(() => {
                        console.log(`📊 Após recarregar: ${this.revisoesPendentes.length} revisões`);
                        if (this.revisoesPendentes.length > 0) {
                            this.abrirModalRevisao();
                        } else {
                            alert('Nenhum artigo disponível para revisão. Marque alguns artigos como lidos primeiro.');
                        }
                    });
                } else {
                    this.abrirModalRevisao();
                }
            });
            console.log('✅ Event listener adicionado ao botão de revisão');
        } else {
            console.error('❌ Botão de revisão não encontrado para adicionar event listener');
        }

        // Botão iniciar revisão
        document.getElementById('btnIniciarRevisao')?.addEventListener('click', () => {
            this.iniciarSessaoRevisao();
        });

        // Botão estatísticas
        document.getElementById('btnEstatisticas')?.addEventListener('click', () => {
            this.abrirModalEstatisticas();
        });

        // Botões de qualidade
        document.querySelectorAll('.btn-qualidade').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const qualidade = parseInt(e.currentTarget.dataset.qualidade);
                this.processarResposta(qualidade);
            });
        });

        // Botões de resultado
        document.getElementById('btnNovaRevisao')?.addEventListener('click', () => {
            this.iniciarSessaoRevisao();
        });

        document.getElementById('btnFecharRevisao')?.addEventListener('click', () => {
            this.fecharModalRevisao();
        });

        // Fechar modais
        document.querySelectorAll('.modal-close-btn').forEach(btn => {
            btn.addEventListener('click', async (e) => {
                const modal = e.target.closest('.modal');
                if (modal) {
                    // Se for o modal de revisão, usar a função específica que salva progresso
                    if (modal.id === 'revisaoModal') {
                        await this.fecharModalRevisao();
                    } else {
                        modal.style.display = 'none';
                    }
                }
            });
        });

        // Integração com sistema existente - adicionar artigos à revisão
        this.integrarComSistemaExistente();

        // Adicionar botão para limpar revisão (temporário para resolver o problema)
        this.adicionarBotaoLimparRevisao();
    }

    /**
     * Monitora mudanças no status de leitura dos artigos para sincronizar com revisão
     */
    integrarComSistemaExistente() {
        console.log('Integrando sistema de revisão automático baseado em status de leitura');

        // Interceptar chamadas para a API de progresso
        this.interceptarAPIProgresso();

        // Monitorar mudanças no localStorage
        this.monitorarLocalStorage();

        // Verificar artigos já lidos ao inicializar
        this.sincronizarArtigosLidos();
    }

    /**
     * Intercepta chamadas para a API de progresso para detectar mudanças
     */
    interceptarAPIProgresso() {
        const originalFetch = window.fetch;
        window.fetch = async (...args) => {
            const response = await originalFetch(...args);

            // Se é uma chamada para progresso.php
            if (args[0] && args[0].includes('progresso.php')) {
                try {
                    const requestBody = args[1]?.body;

                    if (requestBody) {
                        const data = JSON.parse(requestBody);
                        if (data.artigo_numero !== undefined && data.lido !== undefined) {
                            console.log('Detectada mudança de status:', data);
                            // Aguardar um pouco para garantir que a API processou
                            setTimeout(() => {
                                this.processarMudancaStatusLeitura(data.artigo_numero, data.lido);
                            }, 500);
                        }
                    }
                } catch (error) {
                    console.log('Erro ao interceptar progresso:', error);
                }
            }

            return response;
        };
    }

    /**
     * Monitora mudanças no localStorage para detectar alterações
     */
    monitorarLocalStorage() {
        // Verificar mudanças no localStorage a cada 2 segundos
        setInterval(() => {
            this.verificarMudancasLocalStorage();
        }, 2000);
    }

    /**
     * Verifica se houve mudanças no localStorage
     */
    verificarMudancasLocalStorage() {
        try {
            const artigosLidos = JSON.parse(localStorage.getItem('lexjus_artigos_lidos') || '[]');
            const artigosLidosAnteriores = this.artigosLidosAnteriores || [];

            // Verificar artigos que foram adicionados
            const novosLidos = artigosLidos.filter(artigo => !artigosLidosAnteriores.includes(artigo));
            novosLidos.forEach(artigo => {
                console.log('Artigo marcado como lido:', artigo);
                this.processarMudancaStatusLeitura(artigo, true);
            });

            // Verificar artigos que foram removidos
            const removidosLidos = artigosLidosAnteriores.filter(artigo => !artigosLidos.includes(artigo));
            removidosLidos.forEach(artigo => {
                console.log('Artigo desmarcado como lido:', artigo);
                this.processarMudancaStatusLeitura(artigo, false);
            });

            // Atualizar referência
            this.artigosLidosAnteriores = [...artigosLidos];
        } catch (error) {
            console.error('Erro ao verificar localStorage:', error);
        }
    }

    /**
     * Sincroniza artigos já lidos com o sistema de revisão
     */
    async sincronizarArtigosLidos() {
        try {
            const artigosLidos = JSON.parse(localStorage.getItem('lexjus_artigos_lidos') || '[]');
            console.log('Inicializando monitoramento para', artigosLidos.length, 'artigos lidos');

            // Apenas inicializar referência, SEM sincronizar automaticamente
            // Isso evita adicionar artigos antigos que o usuário não quer na revisão
            this.artigosLidosAnteriores = [...artigosLidos];

            console.log('Monitoramento inicializado - apenas mudanças futuras serão processadas');
        } catch (error) {
            console.error('Erro ao inicializar monitoramento:', error);
        }
    }

    /**
     * Processa mudança no status de leitura de um artigo
     */
    async processarMudancaStatusLeitura(artigoNumero, lido) {
        try {
            console.log(`Processando mudança: Artigo ${artigoNumero} - Lido: ${lido}`);

            if (lido) {
                // Artigo foi marcado como lido - verificar se já está na revisão
                const jaEstaRevisao = await this.verificarArtigoNaRevisao(artigoNumero);

                if (!jaEstaRevisao) {
                    await this.garantirArtigoNaRevisao(artigoNumero);
                    this.mostrarNotificacao(`📚 Artigo ${artigoNumero} adicionado à revisão automática!`, 'sucesso');

                    // CORREÇÃO: Atualizar contador do botão após adicionar artigo
                    console.log('🔄 Atualizando contador do botão após adicionar artigo...');
                    await this.carregarDados();
                    this.atualizarInterface();
                } else {
                    console.log(`Artigo ${artigoNumero} já está na revisão, ignorando`);
                }
            } else {
                // Artigo foi desmarcado como lido - remover da revisão
                await this.removerArtigoDaRevisao(artigoNumero);
                this.mostrarNotificacao(`📖 Artigo ${artigoNumero} removido da revisão`, 'info');

                // CORREÇÃO: Atualizar contador do botão após remover artigo
                console.log('🔄 Atualizando contador do botão após remover artigo...');
            }

            // Recarregar dados para atualizar interface
            await this.carregarDados();
            this.atualizarInterface();

        } catch (error) {
            console.error('Erro ao processar mudança de status:', error);
        }
    }

    /**
     * Verifica se um artigo já está no sistema de revisão
     */
    async verificarArtigoNaRevisao(artigoNumero) {
        try {
            // Limpar formatação do número do artigo
            const numeroLimpo = artigoNumero.replace(/^Art\.\s*/, '').replace(/\.$/, '').trim();

            // Verificar se está na lista de revisões carregadas
            if (this.revisoesPendentes && this.revisoesPendentes.length > 0) {
                const encontrado = this.revisoesPendentes.some(revisao =>
                    revisao.artigo_numero === numeroLimpo ||
                    revisao.artigo_numero === artigoNumero
                );
                if (encontrado) {
                    return true;
                }
            }

            // Verificar no servidor se não encontrou localmente
            const response = await fetch('./api/revisao.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    acao: 'verificar',
                    artigo_numero: numeroLimpo
                })
            });

            if (response.ok) {
                const data = await response.json();
                return data.existe || false;
            }

            return false;
        } catch (error) {
            console.error('Erro ao verificar artigo na revisão:', error);
            return false;
        }
    }

    /**
     * Garante que um artigo está no sistema de revisão
     */
    async garantirArtigoNaRevisao(artigoNumero) {
        try {
            // Limpar formatação do número do artigo
            artigoNumero = artigoNumero.replace(/^Art\.\s*/, '').replace(/\.$/, '').trim();

            // Obter lei atual dos data attributes (atualizados pelo multi-leis)
            const leiAtual = document.body.getAttribute('data-lei-codigo') || 'CF';
            const leiId = document.body.getAttribute('data-lei-id');

            console.log(`🎯 Garantindo artigo na revisão: artigo=${artigoNumero}, lei=${leiAtual}, lei_id=${leiId}`);

            const response = await fetch('./api/revisao.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    acao: 'iniciar',
                    artigo_numero: artigoNumero,
                    lei_codigo: leiAtual,
                    lei_id: parseInt(leiId)
                })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.sucesso) {
                    console.log(`Artigo ${artigoNumero} garantido na revisão:`, data.mensagem);

                    // Disparar evento para notificar outros sistemas
                    this.dispararEventoRevisaoAtualizada('adicionada', artigoNumero);

                    return true;
                }
            }

            return false;
        } catch (error) {
            console.error('Erro ao garantir artigo na revisão:', error);
            return false;
        }
    }

    /**
     * Remove um artigo do sistema de revisão
     */
    async removerArtigoDaRevisao(artigoNumero) {
        try {
            // Limpar formatação do número do artigo
            artigoNumero = artigoNumero.replace(/^Art\.\s*/, '').replace(/\.$/, '').trim();

            // Obter lei atual dos data attributes (atualizados pelo multi-leis)
            const leiAtual = document.body.getAttribute('data-lei-codigo') || 'CF';
            const leiId = document.body.getAttribute('data-lei-id');

            console.log(`🗑️ Removendo artigo da revisão: artigo=${artigoNumero}, lei=${leiAtual}, lei_id=${leiId}`);

            const response = await fetch('./api/revisao.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    acao: 'remover',
                    artigo_numero: artigoNumero,
                    lei_codigo: leiAtual,
                    lei_id: parseInt(leiId)
                })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.sucesso) {
                    console.log(`Artigo ${artigoNumero} removido da revisão`);

                    // Disparar evento para notificar outros sistemas
                    this.dispararEventoRevisaoAtualizada('removida', artigoNumero);

                    return true;
                }
            }

            return false;
        } catch (error) {
            console.error('Erro ao remover artigo da revisão:', error);
            return false;
        }
    }

    /**
     * Adiciona botão temporário para limpar revisão
     */
    adicionarBotaoLimparRevisao() {
        // Verificar se já existe
        if (document.getElementById('btnLimparRevisao')) {
            return;
        }

        // Encontrar onde adicionar o botão
        const headerActions = document.querySelector('.header-actions');
        if (headerActions) {
            const btnLimpar = document.createElement('button');
            btnLimpar.id = 'btnLimparRevisao';
            btnLimpar.className = 'btn-limpar-revisao';
            btnLimpar.innerHTML = '<i class="fas fa-trash"></i> Limpar Revisão';
            btnLimpar.title = 'Remove todos os artigos da revisão (temporário)';
            btnLimpar.style.cssText = `
                background: #e74c3c;
                color: white;
                border: none;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 12px;
                cursor: pointer;
                margin-left: 10px;
            `;

            btnLimpar.addEventListener('click', async () => {
                if (confirm('Tem certeza que deseja remover TODOS os artigos da revisão?')) {
                    await this.limparTodasRevisoes();
                }
            });

            headerActions.appendChild(btnLimpar);
            console.log('Botão de limpar revisão adicionado');
        }
    }

    /**
     * Remove todos os artigos da revisão
     */
    async limparTodasRevisoes() {
        try {
            const response = await fetch('./api/revisao.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    acao: 'limpar_todas'
                })
            });

            if (response.ok) {
                const data = await response.json();
                if (data.sucesso) {
                    this.mostrarNotificacao('🗑️ Todas as revisões foram removidas!', 'sucesso');
                    await this.carregarDados();
                    this.atualizarInterface();
                } else {
                    throw new Error(data.erro || 'Erro ao limpar revisões');
                }
            } else {
                throw new Error('Erro na requisição');
            }
        } catch (error) {
            console.error('Erro ao limpar revisões:', error);
            this.mostrarNotificacao('❌ Erro ao limpar revisões: ' + error.message, 'erro');
        }
    }

    /**
     * Abre o modal principal de revisão
     */
    async abrirModalRevisao() {
        const modal = document.getElementById('revisaoModal');
        if (modal) {
            modal.style.display = 'block';
            this.mostrarEstado('aguardando');

            // CORREÇÃO: Recarregar dados ao abrir modal para garantir sincronização
            console.log('🔄 Modal aberto: Recarregando dados de revisão...');
            await this.carregarDados();

            this.atualizarDashboard();

            console.log(`📊 Modal atualizado: ${this.revisoesPendentes.length} revisões pendentes`);
        }
    }

    /**
     * Fecha o modal de revisão
     */
    async fecharModalRevisao() {
        const modal = document.getElementById('revisaoModal');
        if (modal) {
            modal.style.display = 'none';
        }

        // Se há uma sessão ativa, salvar progresso e atualizar dados
        if (this.sessaoAtual && this.sessaoAtual.respostas.length > 0) {
            console.log('Salvando progresso da sessão interrompida...');

            // Recarregar dados para atualizar contadores
            await this.carregarDados();
            this.atualizarInterface();

            console.log('Progresso salvo e interface atualizada');
        }

        // Limpar sessão atual
        this.sessaoAtual = null;
        this.revisaoAtual = null;
    }

    /**
     * Abre o modal de estatísticas
     */
    abrirModalEstatisticas() {
        const modal = document.getElementById('estatisticasModal');
        if (modal) {
            modal.style.display = 'block';
            this.atualizarEstatisticas();
        }
    }

    /**
     * Mostra um estado específico do modal
     */
    mostrarEstado(estado) {
        const estados = ['aguardando', 'revisando', 'resultado'];
        estados.forEach(e => {
            const elemento = document.getElementById(`estado${e.charAt(0).toUpperCase() + e.slice(1)}`);
            if (elemento) {
                elemento.style.display = e === estado ? 'block' : 'none';
            }
        });
    }

    /**
     * Atualiza a interface principal
     */
    atualizarInterface() {
        // Atualizar contador na barra de navegação
        const contador = document.getElementById('revisaoCount');
        if (contador) {
            contador.textContent = this.revisoesPendentes.length;
        }

        // Atualizar contadores mini no header
        const pendentesCount = document.getElementById('pendentesCount');
        if (pendentesCount) {
            pendentesCount.textContent = this.revisoesPendentes.length;
        }

        const dominadosCount = document.getElementById('dominadosCount');
        if (dominadosCount && this.estatisticas.dominados !== undefined) {
            dominadosCount.textContent = this.estatisticas.dominados;
        }
    }

    /**
     * Atualiza o dashboard de revisão
     */
    atualizarDashboard() {
        if (!this.estatisticas) return;

        const elementos = {
            'dashPendentes': this.estatisticas.pendentes || 0,
            'dashAprendendo': this.estatisticas.aprendendo || 0,
            'dashDominados': this.estatisticas.dominados || 0
        };

        Object.entries(elementos).forEach(([id, valor]) => {
            const elemento = document.getElementById(id);
            if (elemento) {
                elemento.textContent = valor;
            }
        });

        // Habilitar/desabilitar botão de iniciar revisão
        const btnIniciar = document.getElementById('btnIniciarRevisao');
        if (btnIniciar) {
            const temPendentes = this.revisoesPendentes.length > 0;
            btnIniciar.disabled = !temPendentes;

            if (temPendentes) {
                btnIniciar.innerHTML = `<i class="fas fa-play"></i> Iniciar Revisão (${this.revisoesPendentes.length})`;
                console.log(`✅ Modal: ${this.revisoesPendentes.length} revisões disponíveis para iniciar`);
            } else {
                btnIniciar.innerHTML = '<i class="fas fa-check"></i> Nenhuma revisão pendente';
                console.log(`ℹ️ Modal: Nenhuma revisão pendente para a lei atual`);
            }
        }
    }

    /**
     * Inicia uma sessão de revisão
     */
    async iniciarSessaoRevisao() {
        if (this.revisoesPendentes.length === 0) {
            this.mostrarNotificacao('Nenhuma revisão pendente no momento!', 'info');
            return;
        }

        this.sessaoAtual = {
            artigos: [...this.revisoesPendentes].slice(0, this.configuracao.max_revisoes_dia || 20),
            indiceAtual: 0,
            respostas: [],
            tempoInicio: Date.now()
        };

        this.mostrarEstado('revisando');
        this.carregarProximoArtigo();
    }

    /**
     * Carrega o próximo artigo da sessão
     */
    async carregarProximoArtigo() {
        if (!this.sessaoAtual || this.sessaoAtual.indiceAtual >= this.sessaoAtual.artigos.length) {
            this.finalizarSessao();
            return;
        }

        const artigo = this.sessaoAtual.artigos[this.sessaoAtual.indiceAtual];
        this.revisaoAtual = artigo;
        this.tempoInicioRevisao = Date.now();

        console.log('Carregando próximo artigo da sessão:', artigo);

        // Atualizar interface
        this.atualizarInterfaceRevisao(artigo);

        // Carregar conteúdo do artigo (sempre funciona, com fallback se necessário)
        try {
            await this.carregarConteudoArtigo(artigo.artigo_numero);
        } catch (error) {
            console.error('Erro ao carregar conteúdo, usando fallback:', error);
            this.mostrarConteudoFallback(artigo.artigo_numero);
        }
    }

    /**
     * Atualiza a interface durante a revisão
     */
    atualizarInterfaceRevisao(artigo) {
        // Atualizar número do artigo com prefixo "Art."
        const numeroElement = document.getElementById('artigoRevisaoNumero');
        if (numeroElement) {
            numeroElement.textContent = `Art. ${artigo.artigo_numero}`;
        }

        // Atualizar metadados
        const repeticoesElement = document.getElementById('artigoRepeticoes');
        if (repeticoesElement) {
            repeticoesElement.textContent = artigo.repeticoes;
        }

        const facilidadeElement = document.getElementById('artigoFacilidade');
        if (facilidadeElement) {
            facilidadeElement.textContent = artigo.facilidade.toFixed(1);
        }

        // Atualizar progresso da sessão
        const sessaoAtualElement = document.getElementById('sessaoAtual');
        if (sessaoAtualElement) {
            sessaoAtualElement.textContent = this.sessaoAtual.indiceAtual + 1;
        }

        const sessaoTotalElement = document.getElementById('sessaoTotal');
        if (sessaoTotalElement) {
            sessaoTotalElement.textContent = this.sessaoAtual.artigos.length;
        }

        // Resetar botões de qualidade
        document.querySelectorAll('.btn-qualidade').forEach(btn => {
            btn.classList.remove('selected');
        });

        // Scroll duplo: modal externo + conteúdo interno
        setTimeout(() => {
            console.log('Executando scroll duplo para modal de revisão...');

            // SCROLL 1: Modal externo - rolar para o início do modal de revisão
            const modalRevisao = document.querySelector('#revisaoModal');
            const modalContent = document.querySelector('#revisaoModal .modal-content');

            if (modalRevisao) {
                modalRevisao.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start',
                    inline: 'nearest'
                });
                console.log('✅ Scroll 1: Modal de revisão encontrado e rolado para o início');
            } else if (modalContent) {
                modalContent.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start',
                    inline: 'nearest'
                });
                console.log('✅ Scroll 1: Modal content encontrado e rolado para o início');
            } else {
                console.log('❌ Modal de revisão não encontrado');
            }

            // SCROLL 2: Conteúdo interno - rolar para o caput do artigo
            setTimeout(() => {
                const artigoCaput = document.querySelector('.artigo-caput');

                if (artigoCaput) {
                    artigoCaput.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start',
                        inline: 'nearest'
                    });
                    console.log('✅ Scroll 2: Artigo-caput encontrado e rolado');
                } else {
                    console.log('❌ artigo-caput não encontrado, tentando alternativas...');

                    // Fallback para conteúdo interno
                    const alternativas = [
                        document.querySelector('.artigo-content'),
                        document.querySelector('.card-content'),
                        document.querySelector('#conteudoArtigo')
                    ];

                    let scrollFeito = false;

                    alternativas.forEach((elemento, index) => {
                        if (elemento && !scrollFeito) {
                            elemento.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start',
                                inline: 'nearest'
                            });
                            console.log(`✅ Scroll 2: Alternativa ${index + 1} rolada`);
                            scrollFeito = true;
                        }
                    });

                    if (!scrollFeito) {
                        console.log('❌ Nenhum elemento de conteúdo encontrado');
                    }
                }
            }, 200); // Delay entre os dois scrolls

        }, 500);
    }

    /**
     * Carrega o conteúdo do artigo
     */
     async carregarConteudoArtigo(artigoNumero) {
        try {
            console.log('Carregando conteúdo do artigo:', artigoNumero);

            // Primeiro tentar buscar no DOM (se o artigo estiver carregado)
            const card = this.buscarCardArtigo(artigoNumero);

            if (card) {
                console.log('Artigo encontrado no DOM');
                this.carregarConteudoDoCard(card);
                return;
            }

            // Se não encontrou no DOM, mostrar conteúdo de estudo direto
            console.log('Artigo não encontrado no DOM, mostrando interface de estudo');
            this.mostrarConteudoEstudo(artigoNumero);

        } catch (error) {
            console.error('Erro ao carregar conteúdo do artigo:', error);
            this.mostrarConteudoEstudo(artigoNumero);
        }
    }

    /**
     * Busca card do artigo no DOM com diferentes formatos
     */
    buscarCardArtigo(artigoNumero) {
        // Limpar formatação
        const numeroLimpo = artigoNumero.replace(/^Art\.\s*/, '').replace(/\.$/, '').trim();

        // Tentar diferentes seletores
        const seletores = [
            `[data-artigo="${artigoNumero}"]`,
            `[data-artigo="${numeroLimpo}"]`,
            `[data-artigo="Art. ${numeroLimpo}"]`,
            `[data-artigo="Art. ${numeroLimpo}."]`,
            `[data-artigo="${numeroLimpo}º"]`,
            `[data-artigo="Art. ${numeroLimpo}º"]`
        ];

        for (const seletor of seletores) {
            const card = document.querySelector(seletor);
            if (card) {
                console.log(`Card encontrado com seletor: ${seletor}`);
                return card;
            }
        }

        console.log('Card não encontrado com nenhum seletor para:', artigoNumero);
        return null;
    }

    /**
     * Mostra conteúdo de estudo baseado apenas no banco de dados
     */
    mostrarConteudoEstudo(artigoNumero) {
        console.log('Mostrando conteúdo de estudo para artigo:', artigoNumero);

        const conteudoElement = document.getElementById('artigoRevisaoConteudo');
        if (conteudoElement) {
            conteudoElement.innerHTML = `
                <div class="artigo-estudo">
                    <div class="estudo-header">
                        <i class="fas fa-book-open"></i>
                        <h3>Art. ${artigoNumero}</h3>
                        <span class="badge-constituicao">Constituição Federal de 1988</span>
                    </div>
                    <div class="estudo-content">
                        <div class="estudo-instrucoes">
                            <h4>📚 Como estudar este artigo:</h4>
                            <ol>
                                <li><strong>Leia</strong> o Art. ${artigoNumero} na Constituição Federal</li>
                                <li><strong>Compreenda</strong> o contexto e aplicação prática</li>
                                <li><strong>Memorize</strong> os pontos principais</li>
                                <li><strong>Avalie</strong> seu conhecimento abaixo</li>
                            </ol>
                        </div>

                        <div class="estudo-dicas">
                            <h4>💡 Dicas de estudo:</h4>
                            <ul>
                                <li>🔍 <strong>Contextualize:</strong> Entenda onde este artigo se encaixa na Constituição</li>
                                <li>📖 <strong>Relacione:</strong> Conecte com outros artigos relacionados</li>
                                <li>⚖️ <strong>Aplique:</strong> Pense em casos práticos onde se aplica</li>
                                <li>🧠 <strong>Memorize:</strong> Use técnicas de memorização para fixar</li>
                            </ul>
                        </div>

                        <div class="estudo-revisao">
                            <div class="revisao-info">
                                <i class="fas fa-brain"></i>
                                <div>
                                    <h4>Sistema de Revisão Inteligente</h4>
                                    <p>Este artigo está sendo revisado usando o algoritmo de repetição espaçada.
                                    Sua avaliação determinará quando você verá este artigo novamente.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    /**
     * Carrega conteúdo de um card do DOM
     */
    carregarConteudoDoCard(card) {
        try {
            // Extrair TODOS os dados do card (igual ao modal principal)
            const numero = card.dataset.artigo;
            const caput = card.dataset.caput || '';
            const incisos = JSON.parse(card.dataset.incisos || '[]');
            const paragrafoUnico = card.dataset.paragrafoUnico || '';
            const paragrafosNumerados = JSON.parse(card.dataset.paragrafosNumerados || '[]');
            const alineasDoArtigo = JSON.parse(card.dataset.alineasArtigo || '[]'); // ← ADICIONADO!

            console.log('Dados extraídos do card:', {
                numero,
                caput: caput.length,
                incisos: incisos.length,
                paragrafoUnico: paragrafoUnico.length,
                paragrafosNumerados: paragrafosNumerados.length,
                alineasDoArtigo: alineasDoArtigo.length
            });

            this.montarConteudoHTML(numero, caput, incisos, paragrafoUnico, paragrafosNumerados, alineasDoArtigo);
        } catch (error) {
            console.error('Erro ao extrair dados do card:', error);
            throw error;
        }
    }

    /**
     * Monta o HTML do conteúdo do artigo (CÓPIA EXATA DO MODAL PRINCIPAL)
     */
    montarConteudoHTML(numero, caput, incisos, paragrafoUnico, paragrafosNumerados, alineasDoArtigo) {
        console.log('Montando conteúdo HTML para artigo:', numero);

        // Obter container principal e criar estrutura igual ao modal principal
        const conteudoElement = document.getElementById('artigoRevisaoConteudo');
        if (!conteudoElement) {
            console.error('Elemento artigoRevisaoConteudo não encontrado');
            return;
        }

        // Limpar conteúdo anterior
        conteudoElement.innerHTML = '';

        // Criar estrutura igual ao modal principal
        const estruturaHTML = `
            <div class="artigo-caput"></div>
            <div class="incisos-container" style="display: none;">
                <ul class="incisos-lista"></ul>
            </div>
            <div class="paragrafo-unico-container" style="display: none;">
                <div class="paragrafo-unico"></div>
            </div>
            <div class="paragrafos-numerados-container" style="display: none;">
                <div class="paragrafos-numerados-lista"></div>
            </div>
        `;
        conteudoElement.innerHTML = estruturaHTML;

        // Obter elementos criados
        const modalArtigoCaput = conteudoElement.querySelector('.artigo-caput');
        const modalIncisosLista = conteudoElement.querySelector('.incisos-lista');
        const modalIncisosContainer = conteudoElement.querySelector('.incisos-container');
        const modalParagrafoUnico = conteudoElement.querySelector('.paragrafo-unico');
        const modalParagrafoUnicoContainer = conteudoElement.querySelector('.paragrafo-unico-container');
        const modalParagrafosNumeradosLista = conteudoElement.querySelector('.paragrafos-numerados-lista');
        const modalParagrafosNumeradosContainer = conteudoElement.querySelector('.paragrafos-numerados-container');

        // Processar EXATAMENTE igual ao modal principal
        this.processarConteudoIgualModalPrincipal(
            caput, incisos, paragrafoUnico, paragrafosNumerados, alineasDoArtigo,
            modalArtigoCaput, modalIncisosLista, modalIncisosContainer,
            modalParagrafoUnico, modalParagrafoUnicoContainer,
            modalParagrafosNumeradosLista, modalParagrafosNumeradosContainer
        );

        console.log('Conteúdo HTML montado com sucesso');
    }



    /**
     * Processa conteúdo EXATAMENTE igual ao modal principal (script.js linhas 356-487)
     */
    processarConteudoIgualModalPrincipal(caput, incisos, paragrafoUnico, paragrafosNumerados, alineasDoArtigo,
                                        modalArtigoCaput, modalIncisosLista, modalIncisosContainer,
                                        modalParagrafoUnico, modalParagrafoUnicoContainer,
                                        modalParagrafosNumeradosLista, modalParagrafosNumeradosContainer) {

        // CAPUT (linha 356 do script.js)
        modalArtigoCaput.innerHTML = caput; // Usar innerHTML para interpretar tags <br> se houver

        // Limpa listas anteriores (linhas 358-362 do script.js)
        modalIncisosLista.innerHTML = '';
        modalParagrafoUnico.innerHTML = '';
        modalParagrafosNumeradosLista.innerHTML = '';

        // INCISOS com suas alíneas aninhadas (linhas 364-446 do script.js)
        if (incisos && incisos.length > 0) {
            // Processar alíneas do artigo para associá-las aos incisos corretos
            const alineasPorInciso = {};
            if (alineasDoArtigo && alineasDoArtigo.length > 0) {
                alineasDoArtigo.forEach(alinea => {
                    // Extrair o inciso correspondente da descrição
                    const match = alinea.match(/\(pertencente a: inciso - ([^)]+)\)/);
                    if (match) {
                        const incisoKey = match[1].trim();
                        if (!alineasPorInciso[incisoKey]) {
                            alineasPorInciso[incisoKey] = [];
                        }
                        // Extrair apenas a alínea sem a descrição de pertencimento
                        const alineaLimpa = alinea.replace(/\s*\(pertencente a:.*?\)/, '');
                        alineasPorInciso[incisoKey].push(alineaLimpa);
                    }
                });
            }

            incisos.forEach(inciso => {
                const li = document.createElement('li');
                li.innerHTML = inciso;

                // Verificar se este inciso tem alíneas
                let alineasEncontradas = [];

                // Buscar alíneas que pertencem a este inciso
                Object.keys(alineasPorInciso).forEach(key => {
                    // Extrair o número do inciso atual (ex: "XXVIII" de "XXVIII - são assegurados...")
                    const incisoNumero = inciso.split(' - ')[0].trim(); // Ex: "XXVIII"

                    // Verificar se a chave começa com o número do inciso seguido de espaço e hífen
                    if (key.startsWith(incisoNumero + ' - ')) {
                        alineasEncontradas = alineasPorInciso[key];
                    }
                });

                // Se encontrou alíneas, criar uma sublista (EXATO como script.js linhas 403-439)
                if (alineasEncontradas.length > 0) {
                    const subUl = document.createElement('ul');
                    subUl.style.cssText = `
                        margin-top: 0.75rem;
                        padding-left: 1.5rem;
                        list-style: none;
                    `;

                    alineasEncontradas.forEach(alinea => {
                        const subLi = document.createElement('li');
                        subLi.innerHTML = alinea;
                        subLi.style.cssText = `
                            margin-bottom: 0.5rem;
                            padding: 0.75rem;
                            background: var(--hover);
                            border-radius: 6px;
                            border-left: 3px solid var(--primary);
                            font-size: 0.9rem;
                            transition: all 0.2s ease;
                        `;

                        // Adicionar hover effect (EXATO como script.js linhas 424-433)
                        subLi.addEventListener('mouseenter', function() {
                            this.style.background = 'rgba(0, 0, 139, 0.1)';
                            this.style.transform = 'translateX(5px)';
                        });

                        subLi.addEventListener('mouseleave', function() {
                            this.style.background = 'var(--hover)';
                            this.style.transform = 'translateX(0)';
                        });

                        subUl.appendChild(subLi);
                    });

                    li.appendChild(subUl);
                }

                modalIncisosLista.appendChild(li);
            });
            modalIncisosContainer.style.display = 'block';
        } else {
            modalIncisosContainer.style.display = 'none';
        }

        // PARÁGRAFO ÚNICO (linhas 448-454 do script.js)
        if (paragrafoUnico) {
            modalParagrafoUnico.innerHTML = paragrafoUnico; // Usar innerHTML
            modalParagrafoUnicoContainer.style.display = 'block';
        } else {
            modalParagrafoUnicoContainer.style.display = 'none';
        }

        // PARÁGRAFOS NUMERADOS e suas Alíneas (linhas 456-487 do script.js)
        if (paragrafosNumerados && paragrafosNumerados.length > 0) {
            paragrafosNumerados.forEach(pn => {
                const pnBloco = document.createElement('div');
                pnBloco.className = 'paragrafo-numerado-bloco';

                const pnNumero = document.createElement('span');
                pnNumero.className = 'paragrafo-numero';
                pnNumero.innerHTML = pn.numero;
                pnBloco.appendChild(pnNumero);

                const pnTexto = document.createElement('span');
                pnTexto.className = 'paragrafo-texto';
                pnTexto.innerHTML = pn.texto;
                pnBloco.appendChild(pnTexto);

                if (pn.alineas && pn.alineas.length > 0) {
                    const alineasUl = document.createElement('ul');
                    alineasUl.className = 'alineas-lista';
                    pn.alineas.forEach(alinea => {
                        const li = document.createElement('li');
                        li.innerHTML = alinea;
                        alineasUl.appendChild(li);
                    });
                    pnBloco.appendChild(alineasUl);
                }
                modalParagrafosNumeradosLista.appendChild(pnBloco);
            });
            modalParagrafosNumeradosContainer.style.display = 'block';
        } else {
            modalParagrafosNumeradosContainer.style.display = 'none';
        }
    }





    /**
     * Processa a resposta do usuário
     */
    async processarResposta(qualidade) {
        if (!this.revisaoAtual) return;

        const tempoResposta = Math.round((Date.now() - this.tempoInicioRevisao) / 1000);

        // Marcar botão selecionado
        document.querySelectorAll('.btn-qualidade').forEach(btn => {
            btn.classList.remove('selected');
            if (parseInt(btn.dataset.qualidade) === qualidade) {
                btn.classList.add('selected');
            }
        });

        try {
            // Obter lei atual dos data attributes (atualizados pelo multi-leis)
            const leiAtual = document.body.getAttribute('data-lei-codigo') || 'CF';
            const leiId = document.body.getAttribute('data-lei-id');

            console.log(`🎯 Enviando qualificação: artigo=${this.revisaoAtual.artigo_numero}, qualidade=${qualidade}, lei=${leiAtual}`);

            // Dados para enviar à API
            const dadosEnvio = {
                acao: 'responder',
                artigo_numero: this.revisaoAtual.artigo_numero,
                qualidade: qualidade,
                tempo_resposta: tempoResposta,
                lei_codigo: leiAtual,
                lei_id: parseInt(leiId)
            };

            console.log(`📤 Dados enviados para API:`, dadosEnvio);

            // Enviar resposta para o servidor
            const response = await fetch('./api/revisao.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(dadosEnvio)
            });

            if (response.ok) {
                const data = await response.json();
                if (data.sucesso) {
                    // Registrar resposta na sessão
                    this.sessaoAtual.respostas.push({
                        artigo: this.revisaoAtual.artigo_numero,
                        qualidade: qualidade,
                        tempo: tempoResposta,
                        resultado: data
                    });

                    // Mostrar feedback visual
                    this.mostrarFeedbackResposta(qualidade, data);

                    // Disparar evento para atualizar estatísticas
                    this.dispararEventoRevisaoAtualizada('respondida', this.revisaoAtual.artigo_numero);

                    // Avançar para próximo artigo após delay
                    setTimeout(() => {
                        this.sessaoAtual.indiceAtual++;
                        this.carregarProximoArtigo();
                    }, 1500);
                }
            }
        } catch (error) {
            console.error('Erro ao processar resposta:', error);
            this.mostrarNotificacao('Erro ao processar resposta', 'erro');
        }
    }

    /**
     * Mostra feedback visual da resposta
     */
    mostrarFeedbackResposta(qualidade, resultado) {
        const feedbackMessages = {
            0: 'Não se preocupe, continue praticando!',
            1: 'Difícil, mas você vai melhorar!',
            2: 'Razoável, continue estudando!',
            3: 'Bom trabalho!',
            4: 'Excelente!',
            5: 'Perfeito! Você domina este artigo!'
        };

        const message = feedbackMessages[qualidade] || 'Resposta registrada!';

        // Criar elemento de feedback temporário
        const feedback = document.createElement('div');
        feedback.className = `feedback-resposta qualidade-${qualidade}`;
        feedback.innerHTML = `
            <div class="feedback-content">
                <i class="fas ${qualidade >= 3 ? 'fa-check-circle' : 'fa-times-circle'}"></i>
                <span>${message}</span>
                <small>Próxima revisão: ${this.formatarProximaRevisao(resultado.data_proxima_revisao)}</small>
            </div>
        `;

        const container = document.querySelector('.revisao-controles');
        if (container) {
            container.appendChild(feedback);

            // Remover após animação
            setTimeout(() => {
                feedback.remove();
            }, 1400);
        }
    }

    /**
     * Formata a data da próxima revisão
     */
    formatarProximaRevisao(dataString) {
        const data = new Date(dataString);
        const agora = new Date();
        const diffDias = Math.ceil((data - agora) / (1000 * 60 * 60 * 24));

        if (diffDias === 0) return 'Hoje';
        if (diffDias === 1) return 'Amanhã';
        if (diffDias < 7) return `Em ${diffDias} dias`;
        if (diffDias < 30) return `Em ${Math.ceil(diffDias / 7)} semanas`;
        return `Em ${Math.ceil(diffDias / 30)} meses`;
    }

    /**
     * Finaliza a sessão de revisão
     */
    finalizarSessao() {
        const tempoTotal = Math.round((Date.now() - this.sessaoAtual.tempoInicio) / 1000 / 60);

        // Atualizar interface de resultado
        const resultadoRevisados = document.getElementById('resultadoRevisados');
        if (resultadoRevisados) {
            resultadoRevisados.textContent = this.sessaoAtual.respostas.length;
        }

        const resultadoTempo = document.getElementById('resultadoTempo');
        if (resultadoTempo) {
            resultadoTempo.textContent = `${tempoTotal}min`;
        }

        // Calcular próxima sessão
        const proximaSessao = this.calcularProximaSessao();
        const resultadoProxima = document.getElementById('resultadoProxima');
        if (resultadoProxima) {
            resultadoProxima.textContent = proximaSessao;
        }

        // Mostrar estado de resultado
        this.mostrarEstado('resultado');

        // Recarregar dados
        this.carregarDados().then(() => {
            this.atualizarInterface();
        });
    }

    /**
     * Calcula quando será a próxima sessão
     */
    calcularProximaSessao() {
        // Lógica simples - pode ser melhorada
        const agora = new Date();
        const amanha = new Date(agora);
        amanha.setDate(amanha.getDate() + 1);

        return this.formatarProximaRevisao(amanha.toISOString());
    }

    /**
     * Atualiza as estatísticas no modal
     */
    atualizarEstatisticas() {
        if (!this.estatisticas) return;

        const elementos = {
            'statTotalArtigos': this.estatisticas.total_artigos || 0,
            'statPendentes': this.estatisticas.pendentes || 0,
            'statDominados': this.estatisticas.dominados || 0,
            'statFacilidadeMedia': (this.estatisticas.facilidade_media || 0).toFixed(1)
        };

        Object.entries(elementos).forEach(([id, valor]) => {
            const elemento = document.getElementById(id);
            if (elemento) {
                elemento.textContent = valor;
            }
        });

        // Atualizar barras de progresso
        const total = this.estatisticas.total_artigos || 1;
        const categorias = ['novos', 'aprendendo', 'revisando', 'dominados'];

        categorias.forEach(categoria => {
            const valor = this.estatisticas[categoria] || 0;
            const percentual = (valor / total) * 100;

            const progressElement = document.getElementById(`progress${categoria.charAt(0).toUpperCase() + categoria.slice(1)}`);
            if (progressElement) {
                progressElement.style.width = `${percentual}%`;
            }

            const valueElement = document.getElementById(`value${categoria.charAt(0).toUpperCase() + categoria.slice(1)}`);
            if (valueElement) {
                valueElement.textContent = valor;
            }
        });
    }

    /**
     * Mostra notificação para o usuário
     */
    mostrarNotificacao(mensagem, tipo = 'info') {
        console.log(`[${tipo.toUpperCase()}] ${mensagem}`);

        // Criar notificação toast
        this.criarToast(mensagem, tipo);
    }

    /**
     * Cria uma notificação toast
     */
    criarToast(mensagem, tipo) {
        // Remover toasts existentes
        const existingToasts = document.querySelectorAll('.toast-notification');
        existingToasts.forEach(toast => toast.remove());

        // Criar elemento toast
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${tipo}`;

        const icon = tipo === 'sucesso' ? 'fa-check-circle' :
                    tipo === 'erro' ? 'fa-times-circle' :
                    tipo === 'info' ? 'fa-info-circle' :
                    'fa-info-circle';

        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas ${icon}"></i>
                <span>${mensagem}</span>
            </div>
        `;

        // Adicionar estilos
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${tipo === 'sucesso' ? '#28a745' :
                        tipo === 'erro' ? '#dc3545' :
                        tipo === 'info' ? '#17a2b8' : '#007bff'};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10000;
            font-family: inherit;
            font-size: 14px;
            max-width: 400px;
            animation: slideInRight 0.3s ease-out;
        `;

        // Adicionar ao DOM
        document.body.appendChild(toast);

        // Remover após 4 segundos
        setTimeout(() => {
            toast.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 300);
        }, 6000);

        // Adicionar animações CSS se não existirem
        this.adicionarAnimacoesToast();
    }

    /**
     * Adiciona animações CSS para os toasts
     */
    adicionarAnimacoesToast() {
        if (document.getElementById('toast-animations')) return;

        const style = document.createElement('style');
        style.id = 'toast-animations';
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }

            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }

            .toast-content {
                display: flex;
                align-items: center;
                gap: 10px;
            }

            .toast-content i {
                font-size: 16px;
            }
        `;

        document.head.appendChild(style);
    }

    /**
     * Callback para quando a lei é alterada no sistema multi-leis
     * @param {string} novaLei - Código da nova lei selecionada
     */
    onLeiAlterada(novaLei) {
        console.log(`Sistema de revisão notificado sobre mudança de lei para: ${novaLei}`);

        // Limpar dados da lei anterior
        this.revisoesPendentes = [];
        this.estatisticas = {};

        // Recarregar dados da nova lei
        this.carregarDados();

        // Atualizar interface se necessário
        const modalRevisao = document.getElementById('revisaoModal');
        if (modalRevisao && modalRevisao.style.display !== 'none') {
            modalRevisao.style.display = 'none';
        }

        // Disparar evento para outros componentes
        const evento = new CustomEvent('sistemaRevisaoLeiAlterada', {
            detail: { novaLei: novaLei }
        });
        document.dispatchEvent(evento);
    }

    /**
     * Dispara evento quando revisão é atualizada
     */
    dispararEventoRevisaoAtualizada(acao, artigoNumero) {
        const evento = new CustomEvent('revisaoAtualizada', {
            detail: {
                acao: acao, // 'adicionada' ou 'removida'
                artigo: artigoNumero,
                lei: document.body.getAttribute('data-lei-codigo'),
                timestamp: new Date().toISOString()
            }
        });

        document.dispatchEvent(evento);
        console.log(`📡 Evento revisaoAtualizada disparado: ${acao} - ${artigoNumero}`);
    }
}

// Inicializar sistema quando o documento estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Inicializando Sistema de Revisão...');
    window.sistemaRevisao = new SistemaRevisao();
});

// Atualização: 2025-06-27 23:55:00 - CORREÇÃO: contador botão atualiza em tempo real
