<?php
include_once("../session_config.php");
include_once("../assets/config.php");

if (!verificarSessaoValida()) {
    header("Location: ../login_index.php");
    exit();
}

$categoria_id = (int)$_GET['id'];

// Buscar informações da categoria
$query_categoria = "SELECT * FROM appestudo.forum_categorias WHERE id = $1 AND status = true";
$result_categoria = pg_query_params($conexao, $query_categoria, array($categoria_id));
$categoria = pg_fetch_assoc($result_categoria);

if (!$categoria) {
    header("Location: index.php");
    exit();
}

// Buscar tópicos da categoria
$query_topicos = "
    SELECT 
        t.*,
        u.nome as autor_nome,
        COUNT(DISTINCT r.id) as total_respostas,
        MAX(r.created_at) as ultima_resposta
    FROM appestudo.forum_topicos t
    LEFT JOIN appestudo.usuario u ON u.idusuario = t.usuario_id
    LEFT JOIN appestudo.forum_respostas r ON r.topico_id = t.id AND r.status = true
    WHERE t.categoria_id = $1 AND t.status = true
    GROUP BY t.id, u.nome
    ORDER BY t.created_at DESC";

$result_topicos = pg_query_params($conexao, $query_topicos, array($categoria_id));

// Buscar nome do usuário
$usuario_id = $_SESSION['idusuario'];
$query_usuario = "SELECT nome FROM appestudo.usuario WHERE idusuario = $1";
$result_usuario = pg_query_params($conexao, $query_usuario, array($usuario_id));
$row = pg_fetch_assoc($result_usuario);
$nome_usuario = $row ? $row['nome'] : 'Usuário';

// Função para limpar e formatar o conteúdo
function limparConteudo($texto) {
    // Remove HTML entities duplicadas
    $texto = html_entity_decode($texto, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    // Remove tags HTML
    $texto = strip_tags($texto);
    // Remove espaços extras e quebras de linha
    $texto = preg_replace('/\s+/', ' ', $texto);
    // Remove espaços no início e fim
    $texto = trim($texto);
    return $texto;
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo limparConteudo($categoria['nome']); ?> - Fórum PlanejaAqui</title>
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <link rel="stylesheet" href="css/forum.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header class="main-header">
        <div class="header-container">
            <div class="header-left">
                <a href="index.php" class="logo">
                    <strong>PlanejaAqui</strong> <span>Fórum</span>
                </a>
                <form class="search-container" action="buscar.php" method="get" style="display:flex;">
                    <input type="text" name="termo" placeholder="Buscar no fórum..." class="search-input">
                    <button type="submit" class="search-button"><i class="fas fa-search"></i></button>
                </form>
            </div>
            <div class="header-right">
                <a href="novo_topico.php?categoria=<?php echo $categoria_id; ?>" class="btn-criar-post">Criar Post</a>
                <div class="notification-wrapper" style="position:relative;display:inline-block;">
                    <a href="#" class="icon-button" id="notificationBell" title="Notificações">
                        <i class="far fa-bell"></i>
                        <span class="notification-badge" id="notificationBadge" style="display:none;">2</span>
                    </a>
                    <div class="notification-dropdown" id="notificationDropdown">
                        <div class="dropdown-header">Notificações</div>
                        <ul class="dropdown-list"></ul>
                        <div class="dropdown-footer"><a href="todas_notificacoes.php">Ver todas</a></div>
                    </div>
                </div>
                <a href="#" class="icon-button" id="toggleDarkMode" title="Alternar modo escuro">
                    <i class="fas fa-moon"></i>
                </a>
            </div>
        </div>
    </header>

    <div class="main-container">
        <div class="content-container">
            <div class="forum-header">
                <div class="breadcrumb">
                    <a href="index.php">Fórum</a> > <?php echo limparConteudo($categoria['nome']); ?>
                </div>
                <h1><?php echo limparConteudo($categoria['nome']); ?></h1>
                <p class="category-description"><?php echo limparConteudo($categoria['descricao']); ?></p>
                <div class="forum-actions">
                    <a href="novo_topico.php?categoria=<?php echo $categoria_id; ?>" class="btn-criar-post">Criar Post</a>
                    <button class="btn-filtrar"><i class="fas fa-filter"></i> Filtrar</button>
                </div>
            </div>

            <div class="topics-list" id="topics-container">
                <?php if (pg_num_rows($result_topicos) > 0): ?>
                    <?php while ($topico = pg_fetch_assoc($result_topicos)): 
                        // Extrair um resumo do conteúdo
                        $resumo = limparConteudo($topico['conteudo']);
                        $resumo = (strlen($resumo) > 150) ? substr($resumo, 0, 150) . '...' : $resumo;
                        
                        // Calcular tempo desde a postagem
                        $data_postagem = strtotime($topico['created_at']);
                        $agora = time();
                        $diferenca = $agora - $data_postagem;
                        
                        if ($diferenca < 60) {
                            $tempo = "agora mesmo";
                        } elseif ($diferenca < 3600) {
                            $minutos = floor($diferenca / 60);
                            $tempo = $minutos . " minuto" . ($minutos > 1 ? "s" : "") . " atrás";
                        } elseif ($diferenca < 86400) {
                            $horas = floor($diferenca / 3600);
                            $tempo = $horas . " hora" . ($horas > 1 ? "s" : "") . " atrás";
                        } else {
                            $dias = floor($diferenca / 86400);
                            if ($dias < 30) {
                                $tempo = $dias . " dia" . ($dias > 1 ? "s" : "") . " atrás";
                            } else {
                                $tempo = date('d/m/Y', $data_postagem);
                            }
                        }
                    ?>
                    <div class="topic-card">
                        <div class="topic-content">
                            <div class="topic-category">
                                <a href="ver_categoria.php?id=<?php echo $categoria_id; ?>">c/<?php echo limparConteudo($categoria['nome']); ?></a>
                                <span class="topic-meta">• Postado por u/<?php echo strtolower(str_replace(' ', '_', limparConteudo($topico['autor_nome']))); ?> • <?php echo $tempo; ?></span>
                            </div>
                            <h2 class="topic-title">
                                <a href="ver_topico.php?id=<?php echo $topico['id']; ?>" onclick="incrementarVisualizacao(<?php echo $topico['id']; ?>)"><?php echo limparConteudo($topico['titulo']); ?></a>
                            </h2>
                            <p class="topic-excerpt"><?php echo limparConteudo($resumo); ?></p>
                            <div class="topic-actions">
                                <a href="ver_topico.php?id=<?php echo $topico['id']; ?>#comentarios" class="topic-action">
                                    <i class="far fa-comment-alt"></i> <?php echo $topico['total_respostas']; ?> comentários
                                </a>
                                <span class="topic-action">
                                    <i class="far fa-eye"></i> <?php echo $topico['views']; ?> visualizações
                                </span>
                                <button class="topic-action">
                                    <i class="fas fa-share"></i> Compartilhar
                                </button>
                            </div>
                        </div>
                    </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="no-topics">
                        <p>Nenhum tópico encontrado nesta categoria. Seja o primeiro a criar um!</p>
                        <a href="novo_topico.php?categoria=<?php echo $categoria_id; ?>" class="btn-criar-post">Criar Tópico</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <div class="sidebar">
            <div class="sidebar-card welcome-card">
                <h2>Categoria: <?php echo limparConteudo($categoria['nome']); ?></h2>
                <p><?php echo limparConteudo($categoria['descricao']); ?></p>
                <a href="novo_topico.php?categoria=<?php echo $categoria_id; ?>" class="btn-criar-post">Criar uma nova publicação</a>
            </div>

            <div class="sidebar-card">
                <h2>Regras do Fórum</h2>
                <ol class="rules-list">
                    <li>Seja respeitoso com os outros usuários</li>
                    <li>Não compartilhe informações pessoais</li>
                    <li>Evite spam e conteúdo promocional</li>
                    <li>Mantenha a discussão relacionada ao tema</li>
                    <li>Respeite as leis de direitos autorais</li>
                    <li>Posts que violarem estas regras serão removidos sem aviso prévio</li>
                </ol>
            </div>
        </div>
    </div>

    <script>
        // Função para controlar o modo escuro
        function toggleDarkMode() {
            const body = document.body;
            const icon = document.querySelector('#toggleDarkMode i');
            
            // Alterna a classe dark-mode
            body.classList.toggle('dark-mode');
            
            // Atualiza o ícone
            if (body.classList.contains('dark-mode')) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
                localStorage.setItem('darkMode', 'enabled');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
                localStorage.setItem('darkMode', 'disabled');
            }
        }

        // Verifica se o modo escuro estava ativado anteriormente
        document.addEventListener('DOMContentLoaded', function() {
            const darkMode = localStorage.getItem('darkMode');
            const icon = document.querySelector('#toggleDarkMode i');
            
            if (darkMode === 'enabled') {
                document.body.classList.add('dark-mode');
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            }

            // Adiciona o evento de clique para o botão de modo escuro
            document.getElementById('toggleDarkMode').addEventListener('click', function(e) {
                e.preventDefault();
                toggleDarkMode();
            });

            // Adicionar funcionalidade ao botão de filtro
            const btnFiltrar = document.querySelector('.btn-filtrar');
            if (btnFiltrar) {
                btnFiltrar.addEventListener('click', function() {
                    alert('Funcionalidade de filtro será implementada em breve!');
                });
            }
            
            // Adicionar funcionalidade aos botões de votação
            const voteButtons = document.querySelectorAll('.vote-up, .vote-down');
            voteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    alert('Sistema de votação será implementado em breve!');
                });
            });
            
            // Adicionar funcionalidade ao botão de compartilhar
            const shareButtons = document.querySelectorAll('.topic-action i.fas.fa-share');
            shareButtons.forEach(button => {
                button.parentElement.addEventListener('click', function() {
                    alert('Funcionalidade de compartilhamento será implementada em breve!');
                });
            });
        });

        // Função para incrementar visualização
        function incrementarVisualizacao(topicoId) {
            fetch('incrementar_visualizacao.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'topico_id=' + topicoId
            });
        }
    </script>
    <script>
        // Notificações - Dropdown
        const bell = document.getElementById('notificationBell');
        const dropdown = document.getElementById('notificationDropdown');
        const badge = document.getElementById('notificationBadge');
        
        function formatarTempo(data) {
            const d = new Date(data);
            const agora = new Date();
            const diff = (agora - d) / 1000;
            if (diff < 60) return 'agora mesmo';
            if (diff < 3600) return Math.floor(diff/60) + ' min atrás';
            if (diff < 86400) return Math.floor(diff/3600) + 'h atrás';
            return d.toLocaleDateString('pt-BR');
        }
        
        function atualizarBadgeNotificacoes() {
            fetch('buscar_notificacoes.php')
                .then(res => res.json())
                .then(data => {
                    if (badge) {
                        if (data.notificacoes && data.notificacoes.length > 0) {
                            const naoLidas = data.notificacoes.filter(n => !n.lida).length;
                            if (naoLidas > 0) {
                                badge.style.display = 'flex';
                                badge.textContent = naoLidas;
                            } else {
                                badge.style.display = 'none';
                            }
                        } else {
                            badge.style.display = 'none';
                        }
                    }
                });
        }
        
        if (bell) {
            bell.addEventListener('click', function(e) {
                e.preventDefault();
                if (dropdown.style.display === 'block') {
                    dropdown.style.display = 'none';
                    return;
                }
                fetch('buscar_notificacoes.php')
                    .then(res => res.json())
                    .then(data => {
                        const list = dropdown.querySelector('.dropdown-list');
                        list.innerHTML = '';
                        if (data.notificacoes && data.notificacoes.length > 0) {
                            let naoLidas = 0;
                            data.notificacoes.forEach(notif => {
                                if (!notif.lida) naoLidas++;
                                let link = notif.topico_id ? `ver_topico.php?id=${notif.topico_id}` : '#';
                                list.innerHTML += `
                                    <li${notif.lida ? '' : ' style=\"background:#f0f6ff;\"'}>
                                        <a href="${link}" style="text-decoration:none;color:inherit;display:block;">
                                            <span class=\"notif-title\">${notif.mensagem}</span><br>
                                            <span class=\"notif-time\">${formatarTempo(notif.data_criada)}</span>
                                        </a>
                                    </li>
                                `;
                            });
                            badge.style.display = naoLidas > 0 ? 'flex' : 'none';
                            badge.textContent = naoLidas;
                        } else {
                            list.innerHTML = '<li><span class="notif-title">Nenhuma notificação</span></li>';
                            badge.style.display = 'none';
                        }
                        dropdown.style.display = 'block';
                        fetch('marcar_notificacoes_lidas.php', { method: 'POST' })
                            .then(() => atualizarBadgeNotificacoes());
                    });
                document.addEventListener('mousedown', function handler(event) {
                    if (!dropdown.contains(event.target) && !bell.contains(event.target)) {
                        dropdown.style.display = 'none';
                        document.removeEventListener('mousedown', handler);
                    }
                });
            });
        }
        document.addEventListener('DOMContentLoaded', atualizarBadgeNotificacoes);
    </script>
</body>
</html>

