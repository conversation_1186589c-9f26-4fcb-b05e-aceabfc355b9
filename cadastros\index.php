<?php
session_start();
require_once("../includes/auth.php");
require_once("../conexao_POST.php");
require_once("includes/verify_admin.php");

// Verifica se é admin
verificarAcessoAdmin($conexao, false);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Painel de Cadastros</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <style>
        :root {
            --primary: #00008B;
            --primary-light: #3949ab;
            --secondary: #f8f0e3;
            --accent: #4a4a4a;
            --border: #e0e0e0;
            --text: #2c3e50;
            --active: #00008B;
            --hover: #f5f5f5;
            --primary-blue: #00008B;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --background: #f5f5f5;
            --card-background: white;
            --gradient-start: #00008B;
            --gradient-end: #0000CD;
        }

        [data-theme="dark"] {
            --primary: #4169E1;
            --secondary: #1a1a2e;
            --accent: #6c7293;
            --border: #2d2d42;
            --text: #e4e6f0;
            --active: #4169E1;
            --hover: #232338;
            --primary-blue: #4169E1;
            --shadow-color: rgba(0, 0, 0, 0.3);
            --background: #0a0a1f;
            --card-background: #13132b;
            --gradient-start: #4169E1;
            --gradient-end: #6495ED;
        }

        body {
            font-family: 'Quicksand', sans-serif;
            margin: 0;
            padding: 0;
            background-color: var(--background);
            color: var(--text);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 15px var(--shadow-color);
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            position: relative;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .header h1 {
            margin: 0;
            font-size: 2rem;
            display: flex;
            align-items: center;
            gap: 15px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.1);
            padding: 10px 20px;
            border-radius: 50px;
            backdrop-filter: blur(5px);
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 25px;
            padding: 10px;
        }

        .card {
            background: var(--card-background);
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 4px 15px var(--shadow-color);
            transition: all 0.3s ease;
            border: 1px solid var(--border);
            display: flex;
            flex-direction: column;
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--gradient-start), var(--gradient-end));
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px var(--shadow-color);
        }

        .card h2 {
            margin: 0 0 15px 0;
            color: var(--primary);
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 1.5rem;
        }

        .card h2 i {
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 1.8rem;
        }

        .card p {
            color: var(--accent);
            margin-bottom: 25px;
            line-height: 1.6;
            flex: 1;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 12px 24px;
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 4px 15px var(--shadow-color);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px var(--shadow-color);
            opacity: 0.9;
        }

        .btn i {
            font-size: 1.1rem;
        }

        .logout-btn {
            background: linear-gradient(135deg, #dc3545, #c82333);
        }

        .logout-btn:hover {
            background: linear-gradient(135deg, #c82333, #bd2130);
        }

        .theme-toggle {
            position: relative;
            z-index: 1000;
        }

        .theme-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            cursor: pointer;
            padding: 12px;
            border-radius: 50%;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .theme-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        @media (max-width: 1200px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .user-info {
                flex-direction: column;
                width: 100%;
            }

            .card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-left">
                <h1><i class="fas fa-cog"></i> Painel de Cadastros</h1>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span>Olá, <?php echo htmlspecialchars($_SESSION['nome']); ?></span>
                    <a href="../sair.php" class="btn logout-btn"><i class="fas fa-sign-out-alt"></i> Sair</a>
                </div>
                <div class="theme-toggle">
                    <button id="theme-toggle-btn" class="theme-btn">
                        <i id="theme-icon" class="fas fa-moon"></i>
                    </button>
                </div>
            </div>
        </div>
        <a href="../index.php" class="btn">
                    <i class="fas fa-home"></i> Início
        </a>

        <div class="grid">
            <div class="card">
                <h2><i class="fas fa-graduation-cap"></i> Cursos</h2>
                <p>Gerencie os cursos disponíveis no sistema. Adicione, edite ou remova cursos conforme necessário.</p>
                <a href="gerenciar_cursos.php" class="btn">
                    <i class="fas fa-cog"></i> Gerenciar Cursos
                </a>
            </div>

            <div class="card">
                <h2><i class="fas fa-book"></i> Matérias</h2>
                <p>Administre as matérias do sistema. Configure cores, nomes e organize o conteúdo educacional.</p>
                <a href="gerenciar_materias.php" class="btn">
                    <i class="fas fa-cog"></i> Gerenciar Matérias
                </a>
            </div>

            <div class="card">
                <h2><i class="fas fa-tasks"></i> Métodos de Estudo</h2>
                <p>Gerencie os métodos de estudo disponíveis. Adicione, edite ou desative métodos conforme necessário.</p>
                <a href="admin_metodos.php" class="btn">
                    <i class="fas fa-cog"></i> Gerenciar Métodos
                </a>
            </div>

            <div class="card">
                <h2><i class="fas fa-file-alt"></i> Editais</h2>
                <p>Gerencie os editais disponíveis no sistema. Adicione, edite ou remova editais para concursos e processos seletivos.</p>
                <a href="gerenciar_editais.php" class="btn">
                    <i class="fas fa-cog"></i> Gerenciar Editais
                </a>
            </div>

            <div class="card">
                <h2><i class="fas fa-comments"></i> Forum</h2>
                <p>Gerencie o forum do site.</p>
                <a href="../forum/admin/index.php" class="btn">
                    <i class="fas fa-cog"></i> Painel de Forum
                </a>
            </div>

            <div class="card">
                <h2><i class="fas fa-bell"></i> Notificações</h2>
                <p>Configure e gerencie notificações do sistema.</p>
                <a href="../admin/notificacoes.php" class="btn">
                    <i class="fas fa-cog"></i> Configurar
                </a>
            </div>
        </div>
    </div>

    <script>
        // Configuração do tema
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggleBtn = document.getElementById('theme-toggle-btn');
            if (themeToggleBtn) {
                themeToggleBtn.addEventListener('click', function() {
                    const currentTheme = document.documentElement.getAttribute('data-theme');
                    const newTheme = currentTheme === 'dark' ? '' : 'dark';
                    document.documentElement.setAttribute('data-theme', newTheme);
                    localStorage.setItem('theme', newTheme);
                    updateThemeIcon(newTheme);
                });
            }
        });

        function updateThemeIcon(theme) {
            const icon = document.getElementById('theme-icon');
            if (icon) {
                icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }

        // Aplicar tema salvo ao carregar
        const savedTheme = localStorage.getItem('theme') || '';
        document.documentElement.setAttribute('data-theme', savedTheme);
        updateThemeIcon(savedTheme);
    </script>
</body>
</html>
