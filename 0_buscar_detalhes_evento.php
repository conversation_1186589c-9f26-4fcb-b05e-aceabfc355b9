<?php
error_reporting(0);
header('Content-Type: application/json');

try {
    include 'conexao_POST.php';
    session_start();

    if (!isset($_SESSION['idusuario'])) {
        throw new Exception('Usuário não está logado');
    }

    if (!isset($_POST['evento_id'])) {
        throw new Exception('ID do evento não fornecido');
    }

    $idUsuario = $_SESSION['idusuario'];
    $eventoId = (int)$_POST['evento_id'];

    // Query modificada para buscar pelo ID específico
    $query = "SELECT id, usuario_idusuario, titulo, data_inicio, data_fim, detalhes, tipo_evento, realizado 
              FROM appEstudo.agenda 
              WHERE usuario_idusuario = $1 
              AND id = $2";

    $result = pg_query_params($conexao, $query, array($idUsuario, $eventoId));

    if (!$result) {
        throw new Exception("Erro na query: " . pg_last_error($conexao));
    }

    if (pg_num_rows($result) > 0) {
        $evento = pg_fetch_assoc($result);
        $data_inicio = new DateTime($evento['data_inicio']);
        $data_fim = new DateTime($evento['data_fim']);

        $dados = array(
            'id' => $evento['id'],
            'title' => $evento['titulo'],
            'tipo' => $evento['tipo_evento'],
            'detalhes' => $evento['detalhes'],
            'realizado' => $evento['realizado'],
            'data_inicio' => $data_inicio->format('d/m/Y H:i'),
            'data_fim' => $data_fim->format('d/m/Y H:i')
        );

        echo json_encode($dados);
    } else {
        echo json_encode([
            'erro' => 'Evento não encontrado',
            'id_buscado' => $eventoId
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'erro' => $e->getMessage()
    ]);
}
?>