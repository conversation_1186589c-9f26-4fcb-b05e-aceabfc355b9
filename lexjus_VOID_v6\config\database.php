<?php
/**
 * Configuração do Banco de Dados
 * Conexão PDO para PostgreSQL
 */

// Configurações do banco
$host = 'app_estudo.postgresql.dbaas.com.br';
$port = '5432';
$dbname = 'app_estudo'; // ou o nome do seu banco
$username = 'app_estudo'; // seu usuário
$password = 'Lucasb90#'; // sua senha

// String de conexão
$dsn = "pgsql:host=$host;port=$port;dbname=$dbname";

try {
    // Criar conexão PDO
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]);
    
    // Definir charset
    $pdo->exec("SET NAMES utf8");
    
} catch (PDOException $e) {
    // Em caso de erro, tentar usar a conexão existente
    if (isset($conexao) && $conexao) {
        // Converter conexão pg_connect para uso com PDO-like queries
        $pdo = new class($conexao) {
            private $connection;
            
            public function __construct($pgConnection) {
                $this->connection = $pgConnection;
            }
            
            public function prepare($sql) {
                return new class($sql, $this->connection) {
                    private $sql;
                    private $connection;
                    private $params = [];
                    
                    public function __construct($sql, $connection) {
                        $this->sql = $sql;
                        $this->connection = $connection;
                    }
                    
                    public function bindParam($param, $value, $type = null) {
                        $this->params[$param] = $value;
                    }
                    
                    public function bindValue($param, $value, $type = null) {
                        $this->params[$param] = $value;
                    }
                    
                    public function execute() {
                        $sql = $this->sql;
                        $values = [];
                        
                        // Converter parâmetros nomeados para posicionais
                        foreach ($this->params as $param => $value) {
                            if (strpos($param, ':') === 0) {
                                $param = substr($param, 1);
                            }
                            $sql = str_replace(':' . $param, '$' . (count($values) + 1), $sql);
                            $values[] = $value;
                        }
                        
                        $result = pg_query_params($this->connection, $sql, $values);
                        
                        if (!$result) {
                            throw new Exception('Erro na consulta: ' . pg_last_error($this->connection));
                        }
                        
                        return $result;
                    }
                    
                    public function fetch($mode = null) {
                        $result = $this->execute();
                        return pg_fetch_assoc($result);
                    }
                    
                    public function fetchAll($mode = null) {
                        $result = $this->execute();
                        $rows = [];
                        while ($row = pg_fetch_assoc($result)) {
                            $rows[] = $row;
                        }
                        return $rows;
                    }
                };
            }
            
            public function query($sql) {
                $result = pg_query($this->connection, $sql);
                if (!$result) {
                    throw new Exception('Erro na consulta: ' . pg_last_error($this->connection));
                }
                return $result;
            }
            
            public function exec($sql) {
                return pg_query($this->connection, $sql);
            }
        };
    } else {
        // Se não conseguir conectar de forma alguma
        throw new Exception('Erro ao conectar com o banco de dados: ' . $e->getMessage());
    }
}

// Função para obter ID do usuário da sessão
function obterUsuarioId() {
    if (!isset($_SESSION['idusuario'])) {
        return null;
    }
    return (int)$_SESSION['idusuario'];
}

// Função para verificar se usuário está logado
function verificarUsuarioLogado() {
    $usuario_id = obterUsuarioId();
    if (!$usuario_id) {
        http_response_code(401);
        echo json_encode(['erro' => 'Usuário não autenticado']);
        exit();
    }
    return $usuario_id;
}
?>
