// Adicionar esta função no início do arquivo, antes de qualquer chamada
function atualizarDiasDisponiveis() {
    var startDateInput = document.getElementById('start_date');
    var endDateInput = document.getElementById('end_date');

    if (!startDateInput.value || !endDateInput.value) {
        console.log("Datas não selecionadas");
        return;
    }

    // Criar datas com o fuso horário local, definindo a hora como meio-dia para evitar problemas
    var startDate = new Date(startDateInput.value + 'T12:00:00');
    var endDate = new Date(endDateInput.value + 'T12:00:00');

    // Desabilitar todos os dias da semana
    var diasSemanaInputs = document.querySelectorAll('input[name="dias_semana[]"]');
    diasSemanaInputs.forEach(function(input) {
        input.disabled = true;
        input.checked = false;
        input.parentElement.classList.add('disabled');
    });

    // Habilitar apenas os dias que estão dentro do intervalo
    var currentDate = new Date(startDate);
    var diasNoIntervalo = [];

    console.log("Intervalo de datas:",
        startDate.toLocaleDateString(), "(" + startDate.toISOString() + ")",
        "a",
        endDate.toLocaleDateString(), "(" + endDate.toISOString() + ")");

    while (currentDate <= endDate) {
        var diaSemana = currentDate.getDay(); // 0 (domingo) a 6 (sábado)
        diasNoIntervalo.push(diaSemana);

        console.log("Data:",
            currentDate.toLocaleDateString(),
            "Dia da semana:",
            diaSemana,
            "Nome do dia:",
            ["Domingo", "Segunda", "Terça", "Quarta", "Quinta", "Sexta", "Sábado"][diaSemana]);

        // Avançar para o próximo dia
        currentDate.setDate(currentDate.getDate() + 1);
    }

    // Remover duplicatas
    diasNoIntervalo = [...new Set(diasNoIntervalo)];
    console.log("Dias no intervalo:", diasNoIntervalo);

    // Encontrar o input correspondente a este dia da semana
    diasSemanaInputs.forEach(function(input) {
        var valorInput = parseInt(input.value);
        if (diasNoIntervalo.includes(valorInput)) {
            input.disabled = false;
            input.parentElement.classList.remove('disabled');
            console.log("Habilitando dia:", valorInput, input.parentElement.textContent.trim());
        }
    });

    // Mostrar mensagem informativa
    var diasDisponiveis = Array.from(diasSemanaInputs)
        .filter(input => !input.disabled)
        .map(input => input.parentElement.textContent.trim());

    if (diasDisponiveis.length > 0) {
        mostrarAlerta('Dias disponíveis no intervalo selecionado: ' + diasDisponiveis.join(', '), 'info');
    } else {
        mostrarAlerta('Nenhum dia disponível no intervalo selecionado. Por favor, ajuste as datas.', 'warning');
    }
}


document.addEventListener("DOMContentLoaded", function () {
    var selectBox = document.getElementById("tipo_evento");
    selectBox.addEventListener("change", function () {
        var selectedValue = selectBox.value;

        if (selectedValue === "Planejamento") {
            document.getElementById("campo_materia").style.display = "block";
            document.getElementById("materia").setAttribute("required", "required");
            document.getElementById("campo_titulo").style.display = "none";
            document.getElementById("titulo").removeAttribute("required");
        } else {
            document.getElementById("campo_materia").style.display = "none";
            document.getElementById("materia").removeAttribute("required");
            document.getElementById("campo_titulo").style.display = "block";
            document.getElementById("titulo").setAttribute("required", "required");
        }
    });

// Função para validar o intervalo de datas
    function validateDateRange() {
        var startDate = new Date(document.getElementById('start_date').value);
        var endDate = new Date(document.getElementById('end_date').value);

        // Verificar se as datas são válidas
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            return true; // Não validar se as datas não estiverem completas
        }

        var timeDiff = endDate - startDate;
        var dayDiff = timeDiff / (1000 * 3600 * 24);

        if (dayDiff > 15) {
            // Substituir o alert por mostrarAlerta
            mostrarAlerta('O intervalo entre a data de início e a data de término não pode exceder 15 dias.', 'error');

            // Limpar o campo de data de término
            document.getElementById('end_date').value = '';
            return false;
        }

        // Verificar se a data final é anterior à data inicial
        if (dayDiff < 0) {
            mostrarAlerta('A data de término deve ser posterior à data de início.', 'error');
            document.getElementById('end_date').value = '';
            return false;
        }

        return true;
    }

// Adicionar evento de mudança ao campo de data de término
    var endDateInput = document.getElementById('end_date');
    if (endDateInput) {
        endDateInput.addEventListener('change', function () {
            validateDateRange();
        });
    }

// Adicionar evento de mudança ao campo de data de início
    var startDateInput = document.getElementById('start_date');
    if (startDateInput) {
        startDateInput.addEventListener('change', function () {
            // Se a data de término já estiver preenchida, validar o intervalo
            if (document.getElementById('end_date').value) {
                validateDateRange();
            }
        });
    }

// Adicionar validação ao enviar o formulário
    var form = document.getElementById('eventoForm');
// Verificar se o formulário existe antes de adicionar o event listener
    if (form) {
        form.addEventListener('submit', function (event) {
            if (!validateDateRange()) {
                event.preventDefault();
            }
        });
    }
    /*
    // Adicionar evento para o checkbox de configuração avançada
    var configAvancadaCheckbox = document.getElementById('usar_config_avancada');
    if (configAvancadaCheckbox) {
        configAvancadaCheckbox.addEventListener('change', function() {
            // Obter o container de matérias selecionadas
            var materiasContainer = document.querySelector('.materias-container');
            if (materiasContainer) {
                if (this.checked) {
                    // Se configuração avançada estiver marcada, desabilitar todas as checkboxes de matérias
                    var checkboxes = materiasContainer.querySelectorAll('input[type="checkbox"]');
                    checkboxes.forEach(function(checkbox) {
                        checkbox.disabled = true;
                        checkbox.parentElement.classList.add('disabled');
                    });
                    materiasContainer.classList.add('disabled-container');

                    // Adicionar mensagem informativa
                    var mensagemExistente = document.getElementById('mensagem-materias-desabilitadas');
                    if (!mensagemExistente) {
                        var mensagem = document.createElement('div');
                        mensagem.id = 'mensagem-materias-desabilitadas';
                        mensagem.className = 'alert alert-info';
                        mensagem.innerHTML = 'A seleção de matérias está desabilitada no modo de configuração avançada.';
                        materiasContainer.parentNode.insertBefore(mensagem, materiasContainer);
                    }
                } else {
                    // Se configuração avançada estiver desmarcada, habilitar todas as checkboxes de matérias
                    var checkboxes = materiasContainer.querySelectorAll('input[type="checkbox"]');
                    checkboxes.forEach(function(checkbox) {
                        checkbox.disabled = false;
                        checkbox.parentElement.classList.remove('disabled');
                    });
                    materiasContainer.classList.remove('disabled-container');

                    // Remover mensagem informativa se existir
                    var mensagem = document.getElementById('mensagem-materias-desabilitadas');
                    if (mensagem) {
                        mensagem.parentNode.removeChild(mensagem);
                    }
                }
            }
        });
    }
    */
});

function validarFormulario() {
    // Validação atual
    var start_date = document.getElementById('start_date').value;
    var end_date = document.getElementById('end_date').value;
    var materias_per_day = parseInt(document.getElementById('materias_per_day').value);
    var materias_selecionadas = document.querySelectorAll('input[name="materias_selecionadas[]"]:checked');
    var usarConfigAvancada = document.getElementById('usar_config_avancada').checked;

    // Validação melhorada
    if (!start_date || !end_date) {
        mostrarAlerta('Por favor, selecione as datas de início e término.', 'error');
        return false;
    }

    if (materias_selecionadas.length === 0) {
        mostrarAlerta('Selecione pelo menos uma matéria.', 'error');
        return false;
    }

    // Validar intervalo de datas
    var startDate = new Date(start_date);
    var endDate = new Date(end_date);
    var timeDiff = endDate - startDate;
    var dayDiff = timeDiff / (1000 * 3600 * 24);

    if (dayDiff < 0) {
        mostrarAlerta('A data de término deve ser posterior à data de início.', 'error');
        return false;
    }

    if (dayDiff > 15) {
        mostrarAlerta('O intervalo entre as datas não pode exceder 15 dias.', 'error');
        return false;
    }

    // Validar seleção de dias da semana
    if (!usarConfigAvancada) {
        var diasSelecionados = document.querySelectorAll('input[name="dias_semana[]"]:checked:not([disabled])');
        if (diasSelecionados.length === 0) {
            mostrarAlerta('Selecione pelo menos um dia da semana disponível no intervalo de datas.', 'error');
            return false;
        }
    } else {
        // Validar configuração avançada
        var diasAtivos = document.querySelectorAll('input[name="dias_ativos[]"]:checked');
        if (diasAtivos.length === 0) {
            mostrarAlerta('Ative pelo menos um dia na configuração avançada.', 'error');
            return false;
        }

        // Verificar se cada dia ativo tem pelo menos uma matéria selecionada
        var diaComMateria = false;
        diasAtivos.forEach(function(diaAtivo) {
            var numDia = diaAtivo.value;
            var materiasDia = document.querySelectorAll(`input[name="materias_dia[${numDia}][]"]:checked`);
            if (materiasDia.length > 0) {
                diaComMateria = true;
            }
        });

        if (!diaComMateria) {
            mostrarAlerta('Selecione pelo menos uma matéria para cada dia ativo.', 'error');
            return false;
        }

        // Verificar se os dias ativos estão dentro do intervalo de datas
        var currentDate = new Date(startDate);
        var diasNoIntervalo = [];

        while (currentDate <= endDate) {
            diasNoIntervalo.push(currentDate.getDay().toString());
            currentDate.setDate(currentDate.getDate() + 1);
        }

        var diasForaDoIntervalo = [];
        diasAtivos.forEach(function(diaAtivo) {
            if (!diasNoIntervalo.includes(diaAtivo.value)) {
                var nomeDia = diaAtivo.closest('.dia-config-avancada').querySelector('h5').textContent;
                diasForaDoIntervalo.push(nomeDia);
            }
        });

        if (diasForaDoIntervalo.length > 0) {
            mostrarAlerta('Os seguintes dias não estão no intervalo de datas selecionado: ' + diasForaDoIntervalo.join(', '), 'error');
            return false;
        }
    }

    return true;
}

function toggleConfigAvancada() {
    var usarConfigAvancada = document.getElementById('usar_config_avancada').checked;
    var configBasica = document.getElementById('config_basica');
    var configAvancada = document.getElementById('config_avancada');

    // Obter o container de matérias básicas
    var materiasBasicoContainer = document.querySelector('.materias-container');
    // Obter os botões de selecionar/limpar
    var botoesSelecao = document.querySelector('.botoes-selecao');

    if (usarConfigAvancada) {
        // Desabilitar configuração básica
        configBasica.style.opacity = '0.5';
        configBasica.classList.add('disabled-section');

        // Desabilitar container de matérias básicas
        if (materiasBasicoContainer) {
            materiasBasicoContainer.classList.add('disabled-container');

            // Desabilitar todos os checkboxes de matérias básicas
            var checkboxesBasico = materiasBasicoContainer.querySelectorAll('input[type="checkbox"]');
            checkboxesBasico.forEach(function(checkbox) {
                checkbox.disabled = true;
                checkbox.parentElement.classList.add('disabled');
            });

            // Desabilitar os botões de selecionar/limpar
            if (botoesSelecao) {
                var botoes = botoesSelecao.querySelectorAll('button');
                botoes.forEach(function(botao) {
                    botao.disabled = true;
                    botao.classList.add('disabled');
                });
            }

            // Adicionar mensagem informativa
            var mensagemExistente = document.getElementById('mensagem-materias-basicas-desabilitadas');
            if (!mensagemExistente) {
                var mensagem = document.createElement('div');
                mensagem.id = 'mensagem-materias-basicas-desabilitadas';
                mensagem.className = 'alert alert-info';
                mensagem.innerHTML = 'A seleção de matérias está desabilitada no modo de configuração avançada.';
                materiasBasicoContainer.parentNode.insertBefore(mensagem, materiasBasicoContainer);
            }
        }

        // Mostrar configuração avançada
        configAvancada.style.display = 'block';
        setTimeout(function() {
            configAvancada.style.opacity = '1';
        }, 10);
    } else {
        // Habilitar configuração básica
        configBasica.style.opacity = '1';
        configBasica.classList.remove('disabled-section');

        // Habilitar container de matérias básicas
        if (materiasBasicoContainer) {
            materiasBasicoContainer.classList.remove('disabled-container');

            // Habilitar todos os checkboxes de matérias básicas
            var checkboxesBasico = materiasBasicoContainer.querySelectorAll('input[type="checkbox"]');
            checkboxesBasico.forEach(function(checkbox) {
                checkbox.disabled = false;
                checkbox.parentElement.classList.remove('disabled');
            });

            // Habilitar os botões de selecionar/limpar
            if (botoesSelecao) {
                var botoes = botoesSelecao.querySelectorAll('button');
                botoes.forEach(function(botao) {
                    botao.disabled = false;
                    botao.classList.remove('disabled');
                });
            }

            // Remover mensagem informativa
            var mensagem = document.getElementById('mensagem-materias-basicas-desabilitadas');
            if (mensagem) {
                mensagem.parentNode.removeChild(mensagem);
            }
        }

        // Esconder configuração avançada
        configAvancada.style.opacity = '0';
        setTimeout(function() {
            configAvancada.style.display = 'none';
        }, 300);
    }

    // Desabilitar/habilitar campos da configuração básica
    var diasSemanaInputs = document.querySelectorAll('.dias-semana-container input[type="checkbox"]');
    for (var i = 0; i < diasSemanaInputs.length; i++) {
        diasSemanaInputs[i].disabled = usarConfigAvancada;
    }

    var materiasPerDay = document.getElementById('materias_per_day');
    if (materiasPerDay) {
        materiasPerDay.disabled = usarConfigAvancada;
    }

    // Atualizar dias disponíveis na configuração avançada
    if (usarConfigAvancada) {
        atualizarDiasDisponiveisAvancados();
    }
}

function toggleDiaMaterias(numDia) {
    var materiasDia = document.getElementById('materias_dia_' + numDia);
    var diaAtivo = document.getElementById('dia_ativo_' + numDia).checked;
    var diaConfig = materiasDia.closest('.dia-config-avancada');

    if (diaAtivo) {
        materiasDia.style.display = 'block';
        setTimeout(function() {
            materiasDia.style.opacity = '1';
            materiasDia.style.maxHeight = '200px';
        }, 10);
        diaConfig.classList.add('ativo');
    } else {
        materiasDia.style.opacity = '0';
        materiasDia.style.maxHeight = '0';
        setTimeout(function() {
            materiasDia.style.display = 'none';
        }, 300);
        diaConfig.classList.remove('ativo');

        // Desmarcar todas as matérias deste dia
        var materiaCheckboxes = materiasDia.querySelectorAll('input[type="checkbox"]');
        for (var i = 0; i < materiaCheckboxes.length; i++) {
            materiaCheckboxes[i].checked = false;
        }
    }
}

function selecionarTodasMaterias(numDia) {
    var materiasDia = document.getElementById('materias_dia_' + numDia);
    var materiaCheckboxes = materiasDia.querySelectorAll('input[type="checkbox"]');
    for (var i = 0; i < materiaCheckboxes.length; i++) {
        materiaCheckboxes[i].checked = true;
    }
}

function deselecionarTodasMaterias(numDia) {
    var materiasDia = document.getElementById('materias_dia_' + numDia);
    var materiaCheckboxes = materiasDia.querySelectorAll('input[type="checkbox"]');
    for (var i = 0; i < materiaCheckboxes.length; i++) {
        materiaCheckboxes[i].checked = false;
    }
}

function atualizarDiasDisponiveisAvancados() {
    var startDateInput = document.getElementById('start_date');
    var endDateInput = document.getElementById('end_date');

    if (!startDateInput.value || !endDateInput.value) return;

    // Criar datas com o fuso horário local, definindo a hora como meio-dia para evitar problemas
    var startDate = new Date(startDateInput.value + 'T12:00:00');
    var endDate = new Date(endDateInput.value + 'T12:00:00');

    console.log("Atualizando dias disponíveis avançados");
    console.log("Data de início:",
        startDate.toLocaleDateString(), "(" + startDate.toISOString() + ")",
        "Dia da semana:",
        startDate.getDay(),
        "Nome do dia:",
        ["Domingo", "Segunda", "Terça", "Quarta", "Quinta", "Sexta", "Sábado"][startDate.getDay()]);
    console.log("Data de término:",
        endDate.toLocaleDateString(), "(" + endDate.toISOString() + ")",
        "Dia da semana:",
        endDate.getDay(),
        "Nome do dia:",
        ["Domingo", "Segunda", "Terça", "Quarta", "Quinta", "Sexta", "Sábado"][endDate.getDay()]);

    // Desabilitar todos os dias da semana na configuração avançada
    var diasConfig = document.querySelectorAll('.dia-config-avancada');
    diasConfig.forEach(function(diaConfig) {
        var numDia = diaConfig.id.split('-').pop();
        var diaCheckbox = document.getElementById('dia_ativo_' + numDia);

        diaCheckbox.disabled = true;
        diaCheckbox.checked = false;
        diaConfig.classList.add('disabled');

        // Esconder e desmarcar as matérias
        var materiasDia = document.getElementById('materias_dia_' + numDia);
        materiasDia.style.opacity = '0';
        materiasDia.style.maxHeight = '0';
        setTimeout(function() {
            materiasDia.style.display = 'none';
        }, 300);

        var materiaCheckboxes = materiasDia.querySelectorAll('input[type="checkbox"]');
        for (var i = 0; i < materiaCheckboxes.length; i++) {
            materiaCheckboxes[i].checked = false;
        }
    });

    // Habilitar apenas os dias que estão dentro do intervalo
    var currentDate = new Date(startDate);
    var diasNoIntervalo = [];

    while (currentDate <= endDate) {
        var diaSemana = currentDate.getDay(); // 0 (domingo) a 6 (sábado)
        diasNoIntervalo.push(diaSemana.toString());
        console.log("Data:",
            currentDate.toLocaleDateString(),
            "Dia da semana:",
            diaSemana,
            "Nome do dia:",
            ["Domingo", "Segunda", "Terça", "Quarta", "Quinta", "Sexta", "Sábado"][diaSemana]);
        currentDate.setDate(currentDate.getDate() + 1);
    }

    // Habilitar os dias que estão no intervalo
    diasNoIntervalo = [...new Set(diasNoIntervalo)]; // Remover duplicatas
    console.log("Dias no intervalo:", diasNoIntervalo);

    diasNoIntervalo.forEach(function(diaSemana) {
        var diaConfig = document.getElementById('dia-config-avancada-' + diaSemana);
        var diaCheckbox = document.getElementById('dia_ativo_' + diaSemana);

        if (diaConfig && diaCheckbox) {
            diaCheckbox.disabled = false;
            diaConfig.classList.remove('disabled');
            console.log("Habilitando dia:", diaSemana, diaConfig.querySelector('h5').textContent);
        }
    });

    // Mostrar mensagem informativa
    var diasDisponiveis = diasNoIntervalo.map(function(diaSemana) {
        var elemento = document.querySelector('#dia-config-avancada-' + diaSemana + ' h5');
        return elemento ? elemento.textContent : '';
    }).filter(Boolean);

    if (diasDisponiveis.length > 0) {
        mostrarAlerta('Dias disponíveis no intervalo selecionado: ' + diasDisponiveis.join(', '), 'info');
    } else {
        mostrarAlerta('Nenhum dia disponível no intervalo selecionado. Por favor, ajuste as datas.', 'warning');
    }
}



// Função para abrir o modal específico usando o modal já existente na página
function openModalEspecifico(event) {
    console.log("Abrindo modal para evento:", event);

    const modal = document.getElementById('modalEvento');
    if (!modal) {
        console.error("Modal não encontrado!");
        return;
    }

    // Adicionar classe 'ativo' ao modal
    modal.classList.add('ativo');

    // Obter elementos do modal
    const modalTitle = modal.querySelector('.modal-especifico-titulo');
    const modalStatusBadgeRealizado = modal.querySelector('.modal-especifico-badge.realizado');
    const modalStatusBadgePendente = modal.querySelector('.modal-especifico-badge.pendente');
    const modalDetails = modal.querySelector('.modal-especifico-detalhes');
    const excluirButton = modal.querySelector('.excluirEventoModal');

    // Verificar se todos os elementos necessários existem
    if (!modalTitle || !modalStatusBadgeRealizado || !modalStatusBadgePendente ||
        !modalDetails || !excluirButton) {
        console.error("Um ou mais elementos do modal não foram encontrados!");
        console.log("modalTitle:", modalTitle);
        console.log("modalStatusBadgeRealizado:", modalStatusBadgeRealizado);
        console.log("modalStatusBadgePendente:", modalStatusBadgePendente);
        console.log("modalDetails:", modalDetails);
        console.log("excluirButton:", excluirButton);
        alert("Erro ao abrir o modal. Verifique o console para mais detalhes.");
        return;
    }

    // Configura o título e detalhes do modal
    modalTitle.textContent = event.title;
    modalDetails.innerHTML = `
                <h4><strong>Evento:</strong> 
                    <span style="background-color: ${event.backgroundColor}; padding: 5px 10px; color: white; border-radius: 4px;">
                        ${event.extendedProps.tipo || "Sem tipo"}
                    </span>
                </h4>
                <p>${event.extendedProps.detalhes || "Sem detalhes"}</p>
            `;

    // Verifica o status do evento e ajusta os elementos visuais
    const realizado = event.extendedProps.realizado === 't';
    modalStatusBadgeRealizado.style.display = realizado ? 'inline-block' : 'none';
    modalStatusBadgePendente.style.display = realizado ? 'none' : 'inline-block';

    // Exibe o modal
    modal.style.display = 'flex';

    // Configura o evento de exclusão ao clicar no botão "Excluir"
    excluirButton.onclick = function () {
        excluirEvento(event.id);
    };
}

// Função para fechar o modal específico
function closeModal() {
    const modal = document.getElementById('modalEvento');
    if (!modal) return;
    
    // Adicionar classe para animação de saída
    modal.classList.add('fechando');
    
    // Aguardar a animação terminar antes de esconder o modal
    setTimeout(function() {
        modal.style.display = 'none';
        modal.classList.remove('ativo');
        modal.classList.remove('fechando');
    }, 300);
}


// Função auxiliar para mostrar alertas (se ainda não existir)
function mostrarAlerta(mensagem, tipo = 'info') {
    // Implemente sua lógica de alerta aqui, por exemplo, usando SweetAlert2
    Swal.fire({
        title: tipo === 'error' ? 'Erro!' : (tipo === 'warning' ? 'Atenção!' : 'Informação'),
        text: mensagem,
        icon: tipo, // 'success', 'error', 'warning', 'info', 'question'
        confirmButtonColor: tipo === 'error' ? '#dc3545' : (tipo === 'warning' ? '#ffc107' : '#0d6efd'),
        confirmButtonText: 'OK'
    });
}

// Função para atualizar as datas do evento ao arrastar ou redimensionar
function resizeAndDrop(info) {
    var eventoId = info.event.id;
    var novoInicio = info.event.start.toISOString();
    // Verifica se a data de fim existe, caso contrário, usa a data de início
    var novoFim = info.event.end ? info.event.end.toISOString() : novoInicio;

    console.log("Tentando atualizar evento ID:", eventoId);
    console.log("Novo Início:", novoInicio);
    console.log("Novo Fim:", novoFim);

    $.ajax({
        url: 'agenda_atualizar_data_hora_evento.php',
        method: 'POST',
        data: {
            eventoId: eventoId,
            start: novoInicio,
            end: novoFim
        },
        success: function (response) {
            console.log("Resposta do servidor (sucesso):", response);
            if (response.success) {
                // Disparar atualização automática da página index.php
                localStorage.setItem('recarregarIndex', Date.now().toString());
                
                mostrarAlerta(response.message || 'Evento atualizado com sucesso!', 'success');
            } else {
                mostrarAlerta(response.message || 'Falha ao atualizar evento.', 'error');
                info.revert(); // Reverte a alteração se o servidor indicar falha
            }
        },
        error: function (jqXHR, textStatus, errorThrown) {
            // Log aprimorado para depuração
            console.error("--- Erro na Requisição AJAX (resizeAndDrop) ---");
            console.error("URL:", 'agenda_atualizar_data_hora_evento.php');
            console.error("Dados Enviados:", { eventoId: eventoId, start: novoInicio, end: novoFim });
            console.error("Status da Requisição:", textStatus);
            console.error("Erro Lançado:", errorThrown);
            console.error("Código de Status HTTP:", jqXHR.status);
            console.error("Resposta do Servidor (se houver):", jqXHR.responseText);
            console.error("Objeto jqXHR Completo:", jqXHR);
            console.error("--- Fim do Erro AJAX ---");

            mostrarAlerta('Erro ao comunicar com o servidor para atualizar o evento. Verifique o console.', 'error');
            info.revert(); // Reverte a alteração no calendário em caso de erro de comunicação
        }
    });
}

// Configura o botão de fechar para o modal específico
document.addEventListener('DOMContentLoaded', function () {
    const closeButton = document.querySelector('.modal-especifico-close');
    if (closeButton) {
        closeButton.addEventListener('click', closeModal);
    }
});

// Função para excluir o evento
function excluirEvento(idEvento) {
    Swal.fire({
        title: 'Confirmar exclusão',
        text: 'Tem certeza que deseja excluir este evento?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: 'Sim, excluir',
        cancelButtonText: 'Cancelar',
        background: '#fff',
        customClass: {
            title: 'swal-title',
            content: 'swal-text',
            confirmButton: 'swal-confirm',
            cancelButton: 'swal-cancel'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            Swal.fire({
                title: 'Excluindo evento...',
                text: 'Por favor, aguarde',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });
            
            // Usar o ID correto do modal
            const modal = document.getElementById('modalEvento');
                                
            if (modal) {
                // Usar a função closeModal existente
                closeModal();
                console.log('Modal fechado com sucesso usando closeModal()');
            } else {
                console.error('Modal com ID "modalEvento" não encontrado para fechar.');
            }

            $.ajax({
                url: 'agenda_atualizar_estado_evento.php',
                type: 'POST',
                data: { eventoIdexcluir: idEvento },
                dataType: 'json',
                success: function(response) {
                    try {
                        var result = typeof response === 'object' ? response : JSON.parse(response);
                        if (result.success) {
                            // Disparar atualização automática da página index.php
                            localStorage.setItem('recarregarIndex', Date.now().toString());
                            
                            Swal.fire({
                                title: 'Sucesso!',
                                text: 'Evento excluído com sucesso!',
                                icon: 'success',
                                confirmButtonColor: '#28a745',
                                confirmButtonText: 'OK',
                                background: '#fff',
                                customClass: {
                                    title: 'swal-title',
                                    content: 'swal-text',
                                    confirmButton: 'swal-confirm'
                                }
                            }).then(() => {
                                // Usar o ID correto do modal
                                const modal = document.getElementById('modalEvento');
                                
                                if (modal) {
                                    // Usar a função closeModal existente
                                    closeModal();
                                    console.log('Modal fechado com sucesso usando closeModal()');
                                } else {
                                    console.error('Modal com ID "modalEvento" não encontrado para fechar.');
                                }

                                // Tentar atualizar o calendário
                                try {
                                    if (typeof calendar !== 'undefined' && calendar && typeof calendar.refetchEvents === 'function') {
                                        calendar.refetchEvents();
                                        console.log("Calendário atualizado com sucesso via refetchEvents.");
                                    } else {
                                        console.error("Variável 'calendar' não encontrada ou não possui o método 'refetchEvents'. O calendário não foi atualizado automaticamente.");
                                        mostrarAlerta('Evento excluído, mas o calendário não pôde ser atualizado automaticamente. Recarregue a página para ver as alterações.', 'warning');
                                    }
                                } catch (e) {
                                    console.error("Erro ao tentar atualizar o calendário:", e);
                                    mostrarAlerta('Erro ao tentar atualizar o calendário automaticamente. Recarregue a página.', 'error');
                                }
                            });
                        } else {
                            Swal.fire({
                                title: 'Erro!',
                                text: result.message || 'Erro ao excluir evento.',
                                icon: 'error',
                                confirmButtonColor: '#dc3545',
                                confirmButtonText: 'OK',
                                background: '#fff'
                            });
                        }
                    } catch (e) {
                        console.error('Erro ao processar resposta:', e, response);
                        Swal.fire({
                            title: 'Erro!',
                            text: 'Ocorreu um erro ao processar a resposta do servidor.',
                            icon: 'error',
                            confirmButtonColor: '#dc3545',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Erro na requisição AJAX:', status, error);
                    Swal.fire({
                        title: 'Erro de conexão!',
                        text: 'Não foi possível conectar ao servidor para excluir o evento.',
                        icon: 'error',
                        confirmButtonColor: '#dc3545',
                        confirmButtonText: 'OK',
                        background: '#fff'
                    });
                }
            });
        }
    });
}

// Função para verificar se há overflow e adicionar classe indicativa
function checkOverflow() {
    var materiasDias = document.querySelectorAll('.materias-dia');

    materiasDias.forEach(function(container) {
        if (container.scrollHeight > container.clientHeight) {
            container.classList.add('has-overflow');
        } else {
            container.classList.remove('has-overflow');
        }
    });
}

function toggleDiaMaterias(numDia) {
    var materiasDia = document.getElementById('materias_dia_' + numDia);
    var diaAtivo = document.getElementById('dia_ativo_' + numDia).checked;
    var diaConfig = materiasDia.closest('.dia-config-avancada');
    var diaMateriasDiv = diaConfig.querySelector('.dia-materias');

    if (diaAtivo) {
        // Adicionar classe active para aplicar estilos específicos
        if (diaMateriasDiv) diaMateriasDiv.classList.add('active');

        // Exibir o contêiner de matérias
        materiasDia.style.display = 'block';

        // Dar tempo para o DOM atualizar antes de animar
        setTimeout(function() {
            materiasDia.style.opacity = '1';
            materiasDia.style.maxHeight = '200px'; // Altura fixa com barra de rolagem

            // Destacar o dia ativo
            diaConfig.style.borderColor = '#00008B';
            diaConfig.style.boxShadow = '0px 4px 8px rgba(0, 0, 139, 0.2)';

            // Verificar overflow após a animação
            checkOverflow();
        }, 10);
    } else {
        // Remover classe active
        if (diaMateriasDiv) diaMateriasDiv.classList.remove('active');

        // Animar fechamento
        materiasDia.style.opacity = '0';
        materiasDia.style.maxHeight = '0';

        // Esperar a animação terminar antes de esconder
        setTimeout(function() {
            materiasDia.style.display = 'none';
        }, 300);

        // Restaurar estilo padrão
        diaConfig.style.borderColor = '#2b2723';
        diaConfig.style.boxShadow = '0px 2px 4px rgba(43, 39, 35, 0.1)';
    }
}

// Adicionar animação ao carregar a página
document.addEventListener('DOMContentLoaded', function() {
    var diasConfig = document.querySelectorAll('.dia-config-avancada');
    diasConfig.forEach(function(dia, index) {
        setTimeout(function() {
            dia.style.opacity = '1';
            dia.style.transform = 'translateY(0)';
        }, 100 + (index * 50));
    });

    // Verificar overflow inicial
    checkOverflow();

    // Verificar overflow quando a janela é redimensionada
    window.addEventListener('resize', checkOverflow);
});
// Verificar overflow inicial
checkOverflow();

// Verificar overflow quando a janela é redimensionada
window.addEventListener('resize', checkOverflow);

function mostrarAlerta(mensagem, tipo) {
    // Verificar se já existe um alerta e removê-lo
    var alertaExistente = document.querySelector('.alerta-personalizado');
    if (alertaExistente) {
        alertaExistente.remove();
    }

    // Criar o elemento de alerta
    var alerta = document.createElement('div');
    alerta.className = 'alerta-personalizado alerta-' + tipo;

    // Adicionar ícone baseado no tipo
    var icone = '';
    if (tipo === 'error') {
        icone = '<i class="fas fa-exclamation-circle"></i>';
    } else if (tipo === 'success') {
        icone = '<i class="fas fa-check-circle"></i>';
    } else if (tipo === 'warning') {
        icone = '<i class="fas fa-exclamation-triangle"></i>';
    } else if (tipo === 'info') {
        icone = '<i class="fas fa-info-circle"></i>';
    }

    // Definir o conteúdo do alerta
    alerta.innerHTML = `
                <div class="alerta-conteudo">
                    <div class="alerta-icone">${icone}</div>
                    <div class="alerta-mensagem">${mensagem}</div>
                    <button type="button" class="alerta-fechar">&times;</button>
                </div>
            `;

    // Adicionar o alerta ao corpo do documento
    document.body.appendChild(alerta);

    // Mostrar o alerta com animação
    setTimeout(function() {
        alerta.classList.add('show');
    }, 10);

    // Adicionar evento para fechar o alerta
    alerta.querySelector('.alerta-fechar').addEventListener('click', function() {
        fecharAlerta(alerta);
    });

    // Fechar automaticamente após 5 segundos
    setTimeout(function() {
        fecharAlerta(alerta);
    }, 5000);
}

function fecharAlerta(alerta) {
    alerta.classList.remove('show');
    setTimeout(function() {
        alerta.remove();
    }, 300);
}

// Adicionar validação para garantir que os dias selecionados estejam dentro do intervalo de datas
document.addEventListener('DOMContentLoaded', function() {
    // Elementos do formulário
    var startDateInput = document.getElementById('start_date');
    var endDateInput = document.getElementById('end_date');
    var diasSemanaInputs = document.querySelectorAll('input[name="dias_semana[]"]');
    var usarConfigAvancadaInput = document.getElementById('usar_config_avancada');

    // Adicionar eventos para atualizar os dias disponíveis quando as datas mudarem
    if (startDateInput && endDateInput) {
        startDateInput.addEventListener('change', atualizarDiasDisponiveis);
        endDateInput.addEventListener('change', atualizarDiasDisponiveis);

        // Também atualizar quando a configuração avançada for alterada
        if (usarConfigAvancadaInput) {
            usarConfigAvancadaInput.addEventListener('change', function() {
                if (!this.checked) {
                    atualizarDiasDisponiveis();
                }
            });
        }
    }

    // Modificar a função de validação do formulário
    window.validarFormulario = function() {
        // Validação atual
        var start_date = document.getElementById('start_date').value;
        var end_date = document.getElementById('end_date').value;
        var materias_per_day = parseInt(document.getElementById('materias_per_day').value);
        var materias_selecionadas = document.querySelectorAll('input[name="materias_selecionadas[]"]:checked');
        var usarConfigAvancada = document.getElementById('usar_config_avancada').checked;

        // Validação melhorada
        if (!start_date || !end_date) {
            mostrarAlerta('Por favor, selecione as datas de início e término.', 'error');
            return false;
        }

        if (materias_selecionadas.length === 0) {
            mostrarAlerta('Selecione pelo menos uma matéria.', 'error');
            return false;
        }

        // Validar intervalo de datas
        var startDate = new Date(start_date);
        var endDate = new Date(end_date);
        var timeDiff = endDate - startDate;
        var dayDiff = timeDiff / (1000 * 3600 * 24);

        if (dayDiff < 0) {
            mostrarAlerta('A data de término deve ser posterior à data de início.', 'error');
            return false;
        }

        if (dayDiff > 15) {
            mostrarAlerta('O intervalo entre as datas não pode exceder 15 dias.', 'error');
            return false;
        }

        // Validar seleção de dias da semana
        if (!usarConfigAvancada) {
            var diasSelecionados = document.querySelectorAll('input[name="dias_semana[]"]:checked:not([disabled])');
            if (diasSelecionados.length === 0) {
                mostrarAlerta('Selecione pelo menos um dia da semana disponível no intervalo de datas.', 'error');
                return false;
            }
        } else {
            // Validar configuração avançada
            var diasAtivos = document.querySelectorAll('input[name="dias_ativos[]"]:checked');
            if (diasAtivos.length === 0) {
                mostrarAlerta('Ative pelo menos um dia na configuração avançada.', 'error');
                return false;
            }

            // Verificar se cada dia ativo tem pelo menos uma matéria selecionada
            var diaComMateria = false;
            diasAtivos.forEach(function(diaAtivo) {
                var numDia = diaAtivo.value;
                var materiasDia = document.querySelectorAll(`input[name="materias_dia[${numDia}][]"]:checked`);
                if (materiasDia.length > 0) {
                    diaComMateria = true;
                }
            });

            if (!diaComMateria) {
                mostrarAlerta('Selecione pelo menos uma matéria para cada dia ativo.', 'error');
                return false;
            }

            // Verificar se os dias ativos estão dentro do intervalo de datas
            var currentDate = new Date(startDate);
            var diasNoIntervalo = [];

            while (currentDate <= endDate) {
                diasNoIntervalo.push(currentDate.getDay().toString());
                currentDate.setDate(currentDate.getDate() + 1);
            }

            var diasForaDoIntervalo = [];
            diasAtivos.forEach(function(diaAtivo) {
                if (!diasNoIntervalo.includes(diaAtivo.value)) {
                    var nomeDia = diaAtivo.closest('.dia-config-avancada').querySelector('h5').textContent;
                    diasForaDoIntervalo.push(nomeDia);
                }
            });

            if (diasForaDoIntervalo.length > 0) {
                mostrarAlerta('Os seguintes dias não estão no intervalo de datas selecionado: ' + diasForaDoIntervalo.join(', '), 'error');
                return false;
            }
        }

        return true;
    };

    // Inicializar a validação de dias disponíveis
    if (startDateInput.value && endDateInput.value) {
        atualizarDiasDisponiveis();
    }
});
// Adicionar evento para atualizar dias disponíveis na configuração avançada
document.addEventListener('DOMContentLoaded', function() {
    var startDateInput = document.getElementById('start_date');
    var endDateInput = document.getElementById('end_date');
    var usarConfigAvancadaInput = document.getElementById('usar_config_avancada');

    if (startDateInput && endDateInput && usarConfigAvancadaInput) {
        startDateInput.addEventListener('change', function() {
            if (usarConfigAvancadaInput.checked) {
                atualizarDiasDisponiveisAvancados();
            } else {
                atualizarDiasDisponiveis();
            }
        });

        endDateInput.addEventListener('change', function() {
            if (usarConfigAvancadaInput.checked) {
                atualizarDiasDisponiveisAvancados();
            } else {
                atualizarDiasDisponiveis();
            }
        });

        usarConfigAvancadaInput.addEventListener('change', function() {
            if (this.checked) {
                atualizarDiasDisponiveisAvancados();
            } else {
                atualizarDiasDisponiveis();
            }
        });
    }
});

// Função para depurar o mapeamento de dias da semana
function depurarDiasSemana() {
    var startDateInput = document.getElementById('start_date');
    var endDateInput = document.getElementById('end_date');

    if (!startDateInput.value || !endDateInput.value) {
        console.log("Datas não selecionadas");
        return;
    }

    // Criar datas com o fuso horário local, definindo a hora como meio-dia para evitar problemas
    var startDate = new Date(startDateInput.value + 'T12:00:00');
    var endDate = new Date(endDateInput.value + 'T12:00:00');

    console.log("Data de início (ISO):", startDate.toISOString());
    console.log("Data de término (ISO):", endDate.toISOString());
    console.log("Data de início (Local):", startDate.toLocaleDateString());
    console.log("Data de término (Local):", endDate.toLocaleDateString());
    console.log("Dia da semana início:", startDate.getDay(), "Nome:", ["Domingo", "Segunda", "Terça", "Quarta", "Quinta", "Sexta", "Sábado"][startDate.getDay()]);
    console.log("Dia da semana término:", endDate.getDay(), "Nome:", ["Domingo", "Segunda", "Terça", "Quarta", "Quinta", "Sexta", "Sábado"][endDate.getDay()]);

    var currentDate = new Date(startDate);
    console.log("Dias no intervalo:");

    while (currentDate <= endDate) {
        var diaSemana = currentDate.getDay();
        var nomeDia = ["Domingo", "Segunda", "Terça", "Quarta", "Quinta", "Sexta", "Sábado"][diaSemana];
        console.log(currentDate.toLocaleDateString(), "-", nomeDia, "(", diaSemana, ")");
        currentDate.setDate(currentDate.getDate() + 1);
    }

    // Verificar os valores dos inputs de dias da semana
    var diasSemanaInputs = document.querySelectorAll('input[name="dias_semana[]"]');
    console.log("Valores dos inputs de dias da semana:");

    diasSemanaInputs.forEach(function(input) {
        console.log(input.parentElement.textContent.trim(), "- valor:", input.value);
    });
}

// Adicionar chamada à função de depuração
document.addEventListener('DOMContentLoaded', function() {
    var startDateInput = document.getElementById('start_date');
    var endDateInput = document.getElementById('end_date');

    if (startDateInput && endDateInput) {
        startDateInput.addEventListener('change', depurarDiasSemana);
        endDateInput.addEventListener('change', depurarDiasSemana);
    }
});

// Funções de utilidade para o calendário
function showLoading() {
    document.getElementById('loadingOverlay').style.display = 'flex';
}

function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

// Funções para manipulação de eventos








// --- Funções para Selecionar/Limpar Matérias (Configuração Básica) ---
function selecionarTodasMateriasBasico() {
    const checkboxes = document.querySelectorAll('#materias-basico-container .materia-basico-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
}

function deselecionarTodasMateriasBasico() {
    const checkboxes = document.querySelectorAll('#materias-basico-container .materia-basico-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
}

var calendar;
// Inicialização do calendário
document.addEventListener('DOMContentLoaded', function() {
    // Garantir que o locale esteja carregado antes de inicializar o calendário
    function initCalendar() {
        var calendarEl = document.getElementById('calendar');

        // Verificar se o elemento do calendário existe
        if (!calendarEl) {
            console.error('Elemento do calendário não encontrado');
            return;
        }

        // Configurar o locale antes de criar o calendário
        if (FullCalendar.globalLocales) {
            // Encontrar e personalizar o locale pt-br
            var ptBrLocale = FullCalendar.globalLocales.find(function(locale) {
                return locale.code === 'pt-br';
            });

            if (ptBrLocale) {
                // Capitalizar a primeira letra de cada mês
                ptBrLocale.monthNames = [
                    'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
                    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
                ];

                // Capitalizar a primeira letra de cada mês na forma abreviada
                ptBrLocale.monthNamesShort = [
                    'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
                    'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
                ];

                // Garantir que os nomes dos dias da semana também estejam corretos
                ptBrLocale.dayNames = [
                    'Domingo', 'Segunda-feira', 'Terça-feira', 'Quarta-feira',
                    'Quinta-feira', 'Sexta-feira', 'Sábado'
                ];

                ptBrLocale.dayNamesShort = [
                    'Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'
                ];

                console.log('Locale pt-br personalizado com sucesso');
            } else {
                console.warn('Locale pt-br não encontrado');
            }
        }

        // Criar o calendário com o locale já configurado
        calendar = new FullCalendar.Calendar(calendarEl, {
            headerToolbar: {
                start: 'today prev,next',
                center: 'title',
                end: 'dayGridMonth timeGridWeek timeGridDay'
            },
            eventDidMount: function(info) {
                // Verificar se o evento tem a propriedade 'realizado'
                if (info.event.extendedProps.realizado === true || 
                    info.event.extendedProps.realizado === 't' || 
                    info.event.extendedProps.realizado === 'true') {
                    // Adicionar classe para eventos realizados
                    info.el.classList.add('evento-realizado');
                } else {
                    // Adicionar classe para eventos pendentes
                    info.el.classList.add('evento-pendente');
                }
            },
            locale: 'pt-br', // Definir o locale
            editable: true,
            selectable: true,
            businessHours: true,
            dayMaxEvents: true,
            eventDisplay: 'block',
            displayEventTime: false,
            events: 'listareventos_agenda_POST.php',

            // Personalizar o título do calendário
            titleFormat: {
                year: 'numeric',
                month: 'long' // Usar 'long' para exibir o nome completo do mês
            },

            eventClick: function (info) {
                openModalEspecifico(info.event);
            },

            eventDrop: function (info) {
                resizeAndDrop(info);
            },
            eventResize: function (info) {
                resizeAndDrop(info);
            },
        });

        // Renderizar o calendário
        calendar.render();
        console.log('Calendário renderizado com locale pt-br');

        // Esconder o overlay quando a página carrega
        hideLoading();
    }

    // Verificar se o FullCalendar e o locale pt-br estão carregados
    if (typeof FullCalendar === 'undefined') {
        console.error('FullCalendar não está carregado');
        return;
    }

    // Inicializar o calendário
    initCalendar();

    // Verificar o estado inicial do checkbox de configuração avançada
    var usarConfigAvancadaCheckbox = document.getElementById('usar_config_avancada');
    if (usarConfigAvancadaCheckbox && usarConfigAvancadaCheckbox.checked) {
        // Se já estiver marcado, chamar a função para aplicar as configurações
        toggleConfigAvancada();
    }
});