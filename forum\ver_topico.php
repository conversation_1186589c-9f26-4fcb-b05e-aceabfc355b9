<?php
include_once("../session_config.php");
include_once("../assets/config.php");

if (!verificarSessaoValida()) {
    header("Location: ../login_index.php");
    exit();
}

$topico_id = (int)$_GET['id'];

// Incrementar visualizações
pg_query_params($conexao, 
    "UPDATE appestudo.forum_topicos SET views = views + 1 WHERE id = $1",
    array($topico_id)
);

// Buscar informações do tópico
$query_topico = "
    SELECT 
        t.*,
        c.nome as categoria_nome,
        c.id as categoria_id,
        u.nome as autor_nome
    FROM appestudo.forum_topicos t
    JOIN appestudo.forum_categorias c ON c.id = t.categoria_id
    JOIN appestudo.usuario u ON u.idusuario = t.usuario_id
    WHERE t.id = $1 AND t.status = true";

$result_topico = pg_query_params($conexao, $query_topico, array($topico_id));
$topico = pg_fetch_assoc($result_topico);

// Buscar respostas
$query_respostas = "
    SELECT 
        r.*,
        u.nome as autor_nome,
        (SELECT COUNT(*) FROM appestudo.forum_votos WHERE resposta_id = r.id AND tipo_voto = 1) as upvotes,
        (SELECT COUNT(*) FROM appestudo.forum_votos WHERE resposta_id = r.id AND tipo_voto = -1) as downvotes,
        (SELECT tipo_voto FROM appestudo.forum_votos WHERE resposta_id = r.id AND usuario_id = $1) as user_voto
    FROM appestudo.forum_respostas r
    JOIN appestudo.usuario u ON u.idusuario = r.usuario_id
    WHERE r.topico_id = $2 AND r.status = true
    ORDER BY r.is_solucao DESC, (
        SELECT COUNT(*) FROM appestudo.forum_votos 
        WHERE resposta_id = r.id AND tipo_voto = 1
    ) DESC,
    r.created_at ASC";

$result_respostas = pg_query_params($conexao, $query_respostas, 
    array($_SESSION['idusuario'], $topico_id)
);

// Buscar nome do usuário
$usuario_id = $_SESSION['idusuario'];
$query_usuario = "SELECT nome FROM appestudo.usuario WHERE idusuario = $1";
$result_usuario = pg_query_params($conexao, $query_usuario, array($usuario_id));
$row = pg_fetch_assoc($result_usuario);
$nome_usuario = $row ? $row['nome'] : 'Usuário';

// Montar array de respostas agrupadas por parent_id
$respostas = [];
if ($result_respostas && pg_num_rows($result_respostas) > 0) {
    while ($r = pg_fetch_assoc($result_respostas)) {
        $pid = $r['parent_id'] ?? null;
        if ($pid === null) $pid = 0;
        $respostas[$pid][] = $r;
    }
}

// Função recursiva para exibir respostas aninhadas
function exibirRespostas($respostas, $parent_id = 0, $nivel = 0) {
    if (!isset($respostas[$parent_id])) return;
    foreach ($respostas[$parent_id] as $resposta) {
        $nivelClasse = $nivel > 7 ? 7 : $nivel;
        ?>
        <div class="comment-card comment-nivel-<?php echo $nivelClasse; ?>" data-resposta-id="<?php echo $resposta['id']; ?>">
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 2px;">
                <span style="font-weight: 600; color: var(--primary); font-size: 13px;"><i class='fas fa-user'></i> <?php echo htmlspecialchars($resposta['autor_nome']); ?></span>
                <span style="color: var(--text-secondary); font-size: 12px;">• <?php echo date('d/m/Y H:i', strtotime($resposta['created_at'])); ?></span>
            </div>
            <div style="font-size: 1rem; color: var(--text); margin-bottom: 2px; line-height: 1.6;">
                <?php echo $resposta['conteudo']; ?>
            </div>
            <div class="comment-actions">
                <button onclick="votar(<?php echo $resposta['id']; ?>, 1)" class="vote-btn upvote <?php echo $resposta['user_voto'] == 1 ? 'voted' : ''; ?>"><i class="fas fa-arrow-up"></i> <span class="vote-count"><?php echo $resposta['upvotes']; ?></span></button>
                <button onclick="votar(<?php echo $resposta['id']; ?>, -1)" class="vote-btn downvote <?php echo $resposta['user_voto'] == -1 ? 'voted' : ''; ?>"><i class="fas fa-arrow-down"></i> <span class="vote-count"><?php echo $resposta['downvotes']; ?></span></button>
                <button class="topic-action" onclick="responderComentario(<?php echo $resposta['id']; ?>, '<?php echo htmlspecialchars($resposta['autor_nome']); ?>')"><i class="fas fa-reply"></i> Responder</button>
                <?php if ($resposta['usuario_id'] == $_SESSION['idusuario']): ?>
                    <button onclick='editarResposta(<?php echo $resposta["id"]; ?>, `<?php echo htmlspecialchars($resposta["conteudo"], ENT_QUOTES); ?>`)' class="btn-action"><i class="fas fa-edit"></i> Editar</button>
                    <button onclick="excluirResposta(<?php echo $resposta['id']; ?>)" class="btn-action btn-delete"><i class="fas fa-trash"></i> Excluir</button>
                <?php endif; ?>
            </div>
        </div>
        <?php
        exibirRespostas($respostas, $resposta['id'], $nivel + 1);
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($topico['titulo']); ?> - Fórum PlanejaAqui</title>
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <link rel="stylesheet" href="css/forum.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.7.2/tinymce.min.js"></script>
    <script>
        // Definição do idioma português do Brasil
        tinymce.addI18n('pt_BR', {
            "Paragraph": "Parágrafo",
            "Heading 1": "Título 1",
            "Heading 2": "Título 2",
            "Heading 3": "Título 3",
            "Heading 4": "Título 4",
            "Heading 5": "Título 5",
            "Heading 6": "Título 6",
            "Bold": "Negrito",
            "Italic": "Itálico",
            "Underline": "Sublinhado",
            "Strikethrough": "Tachado",
            "Align left": "Alinhar à esquerda",
            "Align center": "Centralizar",
            "Align right": "Alinhar à direita",
            "Justify": "Justificar",
            "Bullet list": "Lista não ordenada",
            "Numbered list": "Lista ordenada",
            "Decrease indent": "Diminuir recuo",
            "Increase indent": "Aumentar recuo",
            "Insert/edit link": "Inserir/editar link",
            "Remove link": "Remover link",
            "Select all": "Selecionar tudo",
            "Undo": "Desfazer",
            "Redo": "Refazer",
            "Text color": "Cor do texto",
            "Background color": "Cor de fundo",
            "Custom...": "Personalizar..."
        });

        tinymce.init({
            selector: '#nova_resposta',
            height: 300,
            language: 'pt_BR',
            menubar: false,
            branding: false,
            promotion: false,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'charmap',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'table', 'wordcount'
            ],
            toolbar: 'undo redo | styles | bold italic | alignleft aligncenter alignright alignjustify | ' +
                    'bullist numlist | link | forecolor backcolor',
            content_style: 'body { font-family: "Inter", sans-serif; font-size: 14px }',
            style_formats: [
                { title: 'Parágrafo', format: 'p' },
                { title: 'Título 1', format: 'h1' },
                { title: 'Título 2', format: 'h2' },
                { title: 'Título 3', format: 'h3' }
            ]
        });
    </script>
</head>
<body>
    <header class="main-header">
        <div class="header-container">
            <div class="header-left">
                <a href="index.php" class="logo">
                    <strong>PlanejaAqui</strong> <span>Fórum</span>
                </a>
                <form class="search-container" action="buscar.php" method="get" style="display:flex;">
                    <input type="text" name="termo" placeholder="Buscar no fórum..." class="search-input">
                    <button type="submit" class="search-button"><i class="fas fa-search"></i></button>
                </form>
            </div>
            <div class="header-right">
                <a href="#" class="btn-criar-post" id="btnCriarPost">Criar Post</a>
                <div class="notification-wrapper" style="position:relative;display:inline-block;">
                    <a href="#" class="icon-button" id="notificationBell" title="Notificações">
                        <i class="far fa-bell"></i>
                        <span class="notification-badge" id="notificationBadge" style="display:none;">2</span>
                    </a>
                    <div class="notification-dropdown" id="notificationDropdown">
                        <div class="dropdown-header">Notificações</div>
                        <ul class="dropdown-list"></ul>
                        <div class="dropdown-footer"><a href="todas_notificacoes.php">Ver todas</a></div>
                    </div>
                </div>
                <a href="#" class="icon-button" id="toggleDarkMode" title="Alternar modo escuro">
                    <i class="fas fa-moon"></i>
                </a>
                </div>
                </div>
    </header>

    <div class="main-container">
        <div class="content-container">
            <div class="forum-header">
        <div class="breadcrumb">
            <a href="index.php">Fórum</a> > 
            <a href="ver_categoria.php?id=<?php echo $topico['categoria_id']; ?>"><?php echo htmlspecialchars($topico['categoria_nome']); ?></a> > 
            <?php echo htmlspecialchars($topico['titulo']); ?>
        </div>
                <h1><?php echo htmlspecialchars($topico['titulo']); ?></h1>
                <div class="topic-meta">
                    <span><i class="fas fa-user"></i> <?php echo htmlspecialchars($topico['autor_nome']); ?></span>
                    <span><i class="fas fa-clock"></i> <?php echo date('d/m/Y H:i', strtotime($topico['created_at'])); ?></span>
                    <span><i class="fas fa-eye"></i> <?php echo $topico['views']; ?> visualizações</span>
                </div>
            </div>

            <div class="main-topic-card" style="background: var(--card-background, #fff); border: 1px solid var(--border); border-radius: 8px; box-shadow: 0 2px 8px var(--shadow); padding: 28px 32px; margin-bottom: 32px;">
                <div style="display: flex; align-items: center; margin-bottom: 12px; gap: 12px;">
                    <span class="topic-category" style="font-size: 13px; color: var(--primary); font-weight: 600;">c/<?php echo htmlspecialchars($topico['categoria_nome']); ?></span>
                    <span style="color: var(--text-secondary); font-size: 13px;">• Postado por <b><?php echo htmlspecialchars($topico['autor_nome']); ?></b> • <?php echo date('d/m/Y H:i', strtotime($topico['created_at'])); ?></span>
                </div>
                <h2 style="font-size: 1.5rem; font-weight: 700; color: var(--text); margin-bottom: 18px; line-height: 1.2;">
                    <?php echo htmlspecialchars($topico['titulo']); ?>
                </h2>
                <div style="font-size: 1.08rem; color: var(--text); margin-bottom: 20px; line-height: 1.7;">
                <?php echo $topico['conteudo']; ?>
            </div>
                <div style="display: flex; align-items: center; gap: 24px; margin-top: 8px;">
                    <div style="display: flex; align-items: center; gap: 8px; color: var(--text-secondary); font-size: 14px;">
                        <i class="fas fa-eye"></i> <?php echo $topico['views']; ?>
        </div>
                    <button class="topic-action" style="color: var(--text-secondary); font-size: 14px; background: none; border: none; display: flex; align-items: center; gap: 6px;">
                        <i class="fas fa-share"></i> Compartilhar
                        </button>
                    </div>
                </div>
                
            <div class="responses-section">
                <h2 class="section-title" style="margin-bottom: 18px; font-size: 1.1rem; color: var(--text-secondary); font-weight: 600;">Comentários</h2>
                <div class="topics-list">
                    <?php if (!empty($respostas[0])): ?>
                        <?php exibirRespostas($respostas); ?>
                    <?php else: ?>
                        <div class="no-topics">
                            <p>Nenhum comentário ainda. Seja o primeiro a comentar!</p>
                </div>
                    <?php endif; ?>
            </div>

                <div class="new-response-form" style="margin-top: 24px;">
                    <h3 style="margin-bottom: 12px;">Adicionar um comentário</h3>
            <form method="post" action="processar_resposta.php">
                <input type="hidden" name="topico_id" value="<?php echo $topico_id; ?>">
                        <textarea id="nova_resposta" name="conteudo" placeholder="O que você pensa sobre isso?" style="width: 100%; min-height: 80px; margin-bottom: 10px;"></textarea>
                        <button type="submit" class="btn-comentar" style="margin-top: 4px;">
                            <i class="fas fa-paper-plane"></i> Comentar
                        </button>
            </form>
                </div>
        </div>
    </div>

        <div class="sidebar">
            <div class="sidebar-card welcome-card">
                <h2>Sobre o Tópico</h2>
                <div class="topic-info">
                    <p><strong>Categoria:</strong> <?php echo htmlspecialchars($topico['categoria_nome']); ?></p>
                    <p><strong>Autor:</strong> <?php echo htmlspecialchars($topico['autor_nome']); ?></p>
                    <p><strong>Data:</strong> <?php echo date('d/m/Y H:i', strtotime($topico['created_at'])); ?></p>
                    <p><strong>Visualizações:</strong> <?php echo $topico['views']; ?></p>
                </div>
            </div>

            <div class="sidebar-card">
                <h2>Regras do Fórum</h2>
                <ol class="rules-list">
                    <li>Seja respeitoso com os outros usuários</li>
                    <li>Não compartilhe informações pessoais</li>
                    <li>Evite spam e conteúdo promocional</li>
                    <li>Mantenha a discussão relacionada ao tema</li>
                    <li>Respeite as leis de direitos autorais</li>
                    <li>Posts que violarem estas regras serão removidos sem aviso prévio</li>
                </ol>
            </div>
        </div>
    </div>

    <!-- Modal de Edição -->
    <div id="modalEditarResposta" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Editar Resposta</h2>
                <button type="button" class="close-button">&times;</button>
            </div>
            <form id="formEditarResposta" method="post">
                <input type="hidden" id="resposta_id" name="resposta_id">
                <textarea id="editar_resposta" name="conteudo"></textarea>
                <div class="modal-buttons">
                    <button type="button" class="btn-excluir" onclick="excluirResposta(document.getElementById('resposta_id').value)">
                        <i class="fas fa-trash"></i> Excluir
                    </button>
                    <button type="submit" class="btn-submit">
                        <i class="fas fa-save"></i> Salvar
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal de Confirmação de Exclusão -->
    <div id="modalConfirmarExclusao" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2><i class="fas fa-exclamation-triangle"></i> Confirmar Exclusão</h2>
                <button type="button" class="close-button">&times;</button>
            </div>
            <div class="modal-body">
                <p>Tem certeza que deseja excluir esta resposta?</p>
                <p class="warning-text">Esta ação não poderá ser desfeita.</p>
            </div>
            <div class="modal-buttons">
                <button class="btn-cancel" onclick="fecharModalExclusao()">
                    <i class="fas fa-times"></i> Cancelar
    </button>
                <button class="btn-confirm" onclick="confirmarExclusao()">
                    <i class="fas fa-trash"></i> Excluir
                </button>
            </div>
        </div>
    </div>

    <!-- Modal de Seleção de Categoria -->
    <div id="modalCategoria" class="modal" style="display:none; align-items:center; justify-content:center;">
        <div class="modal-content" style="max-width: 400px;">
            <div class="modal-header">
                <h2>Escolha a Categoria</h2>
                <button type="button" class="close-button" onclick="fecharModalCategoria()">&times;</button>
            </div>
            <div class="modal-body">
                <label for="select-categoria" style="font-weight:500; margin-bottom:8px; display:block;">Categoria:</label>
                <select id="select-categoria" style="width:100%; padding:8px; border-radius:4px; border:1px solid var(--border); font-size:15px;">
                    <option value="">Selecione...</option>
                    <?php
                    $query_todas_categorias = "SELECT id, nome FROM appestudo.forum_categorias WHERE status = true ORDER BY nome";
                    $result_todas_categorias = pg_query($conexao, $query_todas_categorias);
                    while ($cat = pg_fetch_assoc($result_todas_categorias)) {
                        echo '<option value="' . $cat['id'] . '">' . htmlspecialchars($cat['nome']) . '</option>';
                    }
                    ?>
                </select>
            </div>
            <div class="modal-buttons" style="justify-content: flex-end; margin-top: 18px;">
                <button class="btn-submit" onclick="continuarCriarPost()"><i class="fas fa-arrow-right"></i> Continuar</button>
                <button class="btn-cancel" onclick="fecharModalCategoria()" style="margin-left:8px;">Cancelar</button>
            </div>
        </div>
    </div>

    <script>
        // Funções de votação
        function votar(respostaId, tipo) {
            fetch('votar_resposta.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    resposta_id: respostaId,
                    tipo_voto: tipo
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const resposta = document.querySelector(`[data-resposta-id="${respostaId}"]`);
                    const upvoteBtn = resposta.querySelector('.upvote');
                    const downvoteBtn = resposta.querySelector('.downvote');
                    
                    if (tipo === 1) {
                        upvoteBtn.classList.toggle('voted');
                        downvoteBtn.classList.remove('voted');
                    } else {
                        downvoteBtn.classList.toggle('voted');
                        upvoteBtn.classList.remove('voted');
                    }
                    
                    upvoteBtn.querySelector('.vote-count').textContent = data.upvotes;
                    downvoteBtn.querySelector('.vote-count').textContent = data.downvotes;
                } else {
                    alert(data.message || 'Erro ao registrar voto');
                }
            })
            .catch(error => {
                console.error('Erro:', error);
                alert('Erro ao processar a votação');
            });
        }

        // Funções do modal de edição
function editarResposta(id, conteudo) {
    conteudo = conteudo.replace(/&quot;/g, '"')
                      .replace(/&#039;/g, "'")
                      .replace(/&lt;/g, '<')
                      .replace(/&gt;/g, '>')
                      .replace(/&amp;/g, '&');

    if (!tinymce.get('editar_resposta')) {
        tinymce.init({
            selector: '#editar_resposta',
            height: 300,
            language: 'pt_BR',
            menubar: false,
            branding: false,
            promotion: false,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'charmap',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'table', 'wordcount'
            ],
            toolbar: 'undo redo | styles | bold italic | alignleft aligncenter alignright alignjustify | ' +
                    'bullist numlist | link | forecolor backcolor',
                    content_style: 'body { font-family: "Inter", sans-serif; font-size: 14px }',
            setup: function(editor) {
                editor.on('init', function() {
                    editor.setContent(conteudo);
                });
            }
        });
    } else {
        tinymce.get('editar_resposta').setContent(conteudo);
    }

    document.getElementById('resposta_id').value = id;
            document.getElementById('modalEditarResposta').style.display = 'block';
}

        // Funções do modal de exclusão
function excluirResposta(id) {
            document.getElementById('modalConfirmarExclusao').setAttribute('data-resposta-id', id);
            document.getElementById('modalConfirmarExclusao').style.display = 'block';
}

function fecharModalExclusao() {
            document.getElementById('modalConfirmarExclusao').style.display = 'none';
}

function confirmarExclusao() {
            const respostaId = document.getElementById('modalConfirmarExclusao').getAttribute('data-resposta-id');
            const respostaElement = document.querySelector(`[data-resposta-id="${respostaId}"]`);
    
    if (respostaElement) {
        respostaElement.remove();
    }
    
    fecharModalExclusao();

    fetch('processar_resposta.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
                body: `acao=excluir&resposta_id=${respostaId}`
    });
}

        // Event Listeners
document.addEventListener('DOMContentLoaded', function() {
            // Fechar modais ao clicar fora
    window.onclick = function(event) {
                const modalEditar = document.getElementById('modalEditarResposta');
                const modalExclusao = document.getElementById('modalConfirmarExclusao');
                
                if (event.target == modalEditar) {
                    modalEditar.style.display = "none";
                }
        if (event.target == modalExclusao) {
            fecharModalExclusao();
        }
            }

            // Fechar modais ao clicar no X
            document.querySelectorAll('.close-button').forEach(button => {
                button.onclick = function() {
                    this.closest('.modal').style.display = "none";
}
            });

            // Form de edição
    const formEditarResposta = document.getElementById('formEditarResposta');
            if (formEditarResposta) {
    formEditarResposta.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const submitButton = this.querySelector('button[type="submit"]');
        const originalText = submitButton.innerHTML;
        
        try {
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Salvando...';
            
            const respostaId = document.getElementById('resposta_id').value;
            const conteudo = tinymce.get('editar_resposta').getContent();
            
            const formData = new FormData();
            formData.append('resposta_id', respostaId);
            formData.append('conteudo', conteudo);
            formData.append('acao', 'editar');
            
            const response = await fetch('processar_resposta.php', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();
            
            if (data.success) {
                window.location.reload();
            } else {
                alert(data.message || 'Erro ao atualizar resposta');
                submitButton.disabled = false;
                submitButton.innerHTML = originalText;
            }
        } catch (error) {
            console.error('Erro:', error);
                        alert('Erro ao processar a requisição');
            submitButton.disabled = false;
            submitButton.innerHTML = originalText;
        }
    });
            }
    });

        // Função para abrir o campo de resposta abaixo do comentário
        function responderComentario(parentId, autorNome) {
            // Remover qualquer formulário de resposta já aberto
            const formExistente = document.getElementById('form-resposta-aninhada');
            if (formExistente) {
                // Destroi o TinyMCE se existir
                if (window.tinymce && tinymce.get('textarea-aninhada')) {
                    tinymce.get('textarea-aninhada').remove();
                }
                formExistente.remove();
    }

            // Cria o formulário de resposta aninhada
            const form = document.createElement('form');
            form.method = 'post';
            form.action = 'processar_resposta.php';
            form.id = 'form-resposta-aninhada';
            form.style.marginTop = '16px';
            form.innerHTML = `
                <input type=\"hidden\" name=\"topico_id\" value=\"<?php echo $topico_id; ?>\">
                <input type=\"hidden\" name=\"parent_id\" value=\"${parentId}\">
                <textarea id=\"textarea-aninhada\" name=\"conteudo\" placeholder=\"Respondendo para @${autorNome}\"></textarea>
                <button type=\"submit\" class=\"btn-responder\" style=\"margin-top:4px;\">
                    <i class='fas fa-paper-plane'></i> Responder
                </button>
                <button type=\"button\" onclick=\"removerFormRespostaAninhada()\" style=\"margin-left:8px; color:#dc3545; background:none; border:none;\">Cancelar</button>
            `;

            // Insere o formulário logo após o comentário
            const commentCard = document.querySelector(`[data-resposta-id='${parentId}']`);
            if (commentCard) {
                commentCard.appendChild(form);
                // Inicializa o TinyMCE no textarea aninhado
                setTimeout(() => {
                    if (window.tinymce) {
                        tinymce.init({
                            selector: '#textarea-aninhada',
                            height: 180,
                            language: 'pt_BR',
                            menubar: false,
                            branding: false,
                            promotion: false,
                            plugins: [
                                'advlist', 'autolink', 'lists', 'link', 'charmap',
                                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                                'insertdatetime', 'table', 'wordcount'
                            ],
                            toolbar: 'undo redo | styles | bold italic | alignleft aligncenter alignright alignjustify | ' +
                                    'bullist numlist | link | forecolor backcolor',
                            content_style: 'body { font-family: "Inter", sans-serif; font-size: 14px }',
                            style_formats: [
                                { title: 'Parágrafo', format: 'p' },
                                { title: 'Título 1', format: 'h1' },
                                { title: 'Título 2', format: 'h2' },
                                { title: 'Título 3', format: 'h3' }
                            ]
                        });
                    }
                }, 100);
            }
        }

        function removerFormRespostaAninhada() {
            if (window.tinymce && tinymce.get('textarea-aninhada')) {
                tinymce.get('textarea-aninhada').remove();
            }
            const form = document.getElementById('form-resposta-aninhada');
            if (form) form.remove();
}

        // Função para controlar o modo escuro
        function toggleDarkMode() {
            const body = document.body;
            const icon = document.querySelector('#toggleDarkMode i');
            
            // Alterna a classe dark-mode
            body.classList.toggle('dark-mode');
            
            // Atualiza o ícone
            if (body.classList.contains('dark-mode')) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
                localStorage.setItem('darkMode', 'enabled');
        } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
                localStorage.setItem('darkMode', 'disabled');
            }

            // Atualiza o tema do TinyMCE se estiver ativo
            if (window.tinymce) {
                const isDark = body.classList.contains('dark-mode');
                const editorStyle = isDark ? 
                    'body { font-family: "Inter", sans-serif; font-size: 14px; background: #2d2d2d; color: #e0e0e0; }' :
                    'body { font-family: "Inter", sans-serif; font-size: 14px; }';
                
                tinymce.editors.forEach(editor => {
                    editor.getBody().style.backgroundColor = isDark ? '#2d2d2d' : '#fff';
                    editor.getBody().style.color = isDark ? '#e0e0e0' : '#333';
                    editor.contentDocument.body.style.backgroundColor = isDark ? '#2d2d2d' : '#fff';
                    editor.contentDocument.body.style.color = isDark ? '#e0e0e0' : '#333';
                });
            }
        }

        // Verifica se o modo escuro estava ativado anteriormente
document.addEventListener('DOMContentLoaded', function() {
            const darkMode = localStorage.getItem('darkMode');
            const icon = document.querySelector('#toggleDarkMode i');
            
            if (darkMode === 'enabled') {
                document.body.classList.add('dark-mode');
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');

                // Aplica o tema escuro ao TinyMCE se estiver ativo
                if (window.tinymce) {
                    tinymce.editors.forEach(editor => {
                        editor.getBody().style.backgroundColor = '#2d2d2d';
                        editor.getBody().style.color = '#e0e0e0';
                        editor.contentDocument.body.style.backgroundColor = '#2d2d2d';
                        editor.contentDocument.body.style.color = '#e0e0e0';
                    });
                }
            }

            // Adiciona o evento de clique para o botão de modo escuro
            document.getElementById('toggleDarkMode').addEventListener('click', function(e) {
                e.preventDefault();
                toggleDarkMode();
            });
});

// Notificações - Dropdown
const bell = document.getElementById('notificationBell');
const dropdown = document.getElementById('notificationDropdown');
const badge = document.getElementById('notificationBadge');

function formatarTempo(data) {
    const d = new Date(data);
    const agora = new Date();
    const diff = (agora - d) / 1000;
    if (diff < 60) return 'agora mesmo';
    if (diff < 3600) return Math.floor(diff/60) + ' min atrás';
    if (diff < 86400) return Math.floor(diff/3600) + 'h atrás';
    return d.toLocaleDateString('pt-BR');
}

function atualizarBadgeNotificacoes() {
    fetch('buscar_notificacoes.php')
        .then(res => res.json())
        .then(data => {
            if (badge) {
                if (data.notificacoes && data.notificacoes.length > 0) {
                    const naoLidas = data.notificacoes.filter(n => !n.lida).length;
                    if (naoLidas > 0) {
                        badge.style.display = 'flex';
                        badge.textContent = naoLidas;
                    } else {
                        badge.style.display = 'none';
                    }
                } else {
                    badge.style.display = 'none';
                }
            }
        });
}

if (bell) {
    bell.addEventListener('click', function(e) {
        e.preventDefault();
        if (dropdown.style.display === 'block') {
            dropdown.style.display = 'none';
            return;
        }
        fetch('buscar_notificacoes.php')
            .then(res => res.json())
            .then(data => {
                const list = dropdown.querySelector('.dropdown-list');
                list.innerHTML = '';
                if (data.notificacoes && data.notificacoes.length > 0) {
                    let naoLidas = 0;
                    data.notificacoes.forEach(notif => {
                        if (!notif.lida) naoLidas++;
                        let link = notif.topico_id ? `ver_topico.php?id=${notif.topico_id}` : '#';
                        list.innerHTML += `
                            <li${notif.lida ? '' : ' style=\"background:#f0f6ff;\"'}>
                                <a href="${link}" style="text-decoration:none;color:inherit;display:block;">
                                    <span class=\"notif-title\">${notif.mensagem}</span><br>
                                    <span class=\"notif-time\">${formatarTempo(notif.data_criada)}</span>
                                </a>
                            </li>
                        `;
                    });
                    badge.style.display = naoLidas > 0 ? 'flex' : 'none';
                    badge.textContent = naoLidas;
                } else {
                    list.innerHTML = '<li><span class="notif-title">Nenhuma notificação</span></li>';
                    badge.style.display = 'none';
                }
                dropdown.style.display = 'block';
                fetch('marcar_notificacoes_lidas.php', { method: 'POST' })
                    .then(() => atualizarBadgeNotificacoes());
            });
        document.addEventListener('mousedown', function handler(event) {
            if (!dropdown.contains(event.target) && !bell.contains(event.target)) {
                dropdown.style.display = 'none';
                document.removeEventListener('mousedown', handler);
            }
        });
    });
}
document.addEventListener('DOMContentLoaded', atualizarBadgeNotificacoes);

document.addEventListener('DOMContentLoaded', function() {
    var btnCriarPost = document.getElementById('btnCriarPost');
    if (btnCriarPost) {
        btnCriarPost.addEventListener('click', function(e) {
            e.preventDefault();
            abrirModalCategoria();
        });
    }
});

function abrirModalCategoria() {
    document.getElementById('modalCategoria').style.display = 'flex';
}

function continuarCriarPost() {
    var select = document.getElementById('select-categoria');
    var categoriaId = select.value;
    if (categoriaId) {
        window.location.href = 'novo_topico.php?categoria=' + categoriaId;
    } else {
        alert('Selecione uma categoria!');
    }
}
   // Funções globais para manipulação do modal
   function fecharModalCategoria() {
    document.getElementById('modalCategoria').style.display = 'none';
    document.getElementById('select-categoria').value = '';
}
    </script>
</body>
</html>







