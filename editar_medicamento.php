<?php
// Conectar ao banco de dados (coloque aqui suas configurações de conexão)
include_once("conexao_POST.php");
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Processar o formulário de edição

    $id_medicamento = $_POST["id_medicamento"];
    $nome_medicamento = $_POST["nome"];
    $detalhes_medicamento = $_POST["detalhes"];
    $data_inicio = $_POST["data_inicio"];
    $dosagem = $_POST["dosagem"];
    $usuario_id = $_POST["usuario_id"];

    // Verificar se uma nova imagem foi enviada
    if ($_FILES["foto"]["size"] > 0) {
        // Ler a nova imagem do formulário e convertê-la em dados binários
       // $nova_imagem = file_get_contents($_FILES["foto"]["tmp_name"]);
        $nova_imagem=null;
        // Atualizar os dados, incluindo a nova imagem
        $query = "UPDATE appEstudo.medicamento SET nome = $1, detalhes = $2, data_inicio = $3, dosagem = $4, usuario_idusuario = $5, foto = $6 WHERE id_medicamento = $7";
        $result = pg_query_params($conexao, $query, [
            $nome_medicamento,
            $detalhes_medicamento,
            $data_inicio,
            $dosagem,
            $usuario_id,
            pg_escape_bytea($nova_imagem), // Converter a nova imagem para formato binário
            $id_medicamento
        ]);
    } else {
        // Atualizar os dados sem alterar a imagem
        $query = "UPDATE appEstudo.medicamento SET nome = $1, detalhes = $2, data_inicio = $3, dosagem = $4, usuario_idusuario = $5 WHERE id_medicamento = $6";
        $result = pg_query_params($conexao, $query, [
            $nome_medicamento,
            $detalhes_medicamento,
            $data_inicio,
            $dosagem,
            $usuario_id,
            $id_medicamento
        ]);
    }

    if (!$result) {
        die("Erro ao atualizar dados: " . pg_last_error());
    }

    header("Location: listar_medicamentos.php");
    exit();
}

// Verificar se o ID do medicamento foi fornecido na URL
if (isset($_GET["id"])) {
    $id_medicamento = $_GET["id"];
    $query = "SELECT * FROM appEstudo.medicamento WHERE id_medicamento = $1";
    $result = pg_query_params($conexao, $query, [$id_medicamento]);

    if ($result && pg_num_rows($result) > 0) {
        $medicamento = pg_fetch_assoc($result);
        // Certifique-se de que a data está formatada corretamente para o input de data
        $medicamento['data_inicio'] = !empty($medicamento['data_inicio']) ? date('Y-m-d', strtotime($medicamento['data_inicio'])) : '';
    } else {
        die("Medicamento não encontrado ou erro na consulta.");
    }
} else {
    die("ID do medicamento não fornecido.");
}
?>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Medicamento da Maya Rafaela</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background: #f5f6fa;
            color: #2d3436;
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: #6c5ce7;
            color: white;
            padding: 20px 0;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2em;
            margin: 0;
        }

        .container {
            max-width: 800px;
            margin: 40px auto;
            padding: 0 20px;
            flex-grow: 1;
        }

        .back-button {
            display: inline-block;
            padding: 10px 20px;
            background: #a29bfe;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin-bottom: 20px;
            transition: background 0.3s;
        }

        .back-button:hover {
            background: #6c5ce7;
        }

        .card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .welcome-message {
            background: #6c5ce7;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 25px;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #2d3436;
            font-weight: 500;
        }

        input[type="text"],
        input[type="date"],
        textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        input[type="text"]:focus,
        input[type="date"]:focus,
        textarea:focus {
            border-color: #6c5ce7;
            outline: none;
        }

        .file-input-container {
            position: relative;
            margin-top: 10px;
        }

        .file-input-button {
            display: inline-block;
            padding: 12px 20px;
            background: #a29bfe;
            color: white;
            border-radius: 8px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .file-input-button:hover {
            background: #6c5ce7;
        }

        input[type="file"] {
            position: absolute;
            left: -9999px;
        }

        .submit-button {
            background: #6c5ce7;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: background 0.3s;
            width: 100%;
            margin-top: 20px;
        }

        .submit-button:hover {
            background: #5046b8;
        }

        .error-message {
            background: #ff7675;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        footer {
            background: #6c5ce7;
            color: white;
            text-align: center;
            padding: 20px;
            margin-top: auto;
        }

        @media (max-width: 768px) {
            .container {
                margin: 20px auto;
            }

            .card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
<header class="header">
    <h1>Editar Medicamento - Maya Rafaela</h1>
</header>

<main class="container">
    <a href="medicamento.php" class="back-button">
        <i class="fas fa-arrow-left"></i> Voltar
    </a>

    <div class="card">
             <form method="post" enctype="multipart/form-data" onsubmit="return validateForm()">
            <input type="hidden" name="id_medicamento" value="<?= $medicamento['id_medicamento'] ?>">
            <input type="hidden" name="usuario_id" value="<?= $medicamento['usuario_idusuario'] ?>">



            <div class="form-group">
                <label for="nome">
                    <i class="fas fa-pills"></i> Nome do Medicamento:
                </label>
                <input type="text" name="nome" value="<?= $medicamento['nome'] ?>" required><br>
            </div>

            <div class="form-group">
                <label for="detalhes">
                    <i class="fas fa-info-circle"></i> Detalhes:
                </label>
                <textarea name="detalhes"><?= $medicamento['detalhes'] ?></textarea><br>
            </div>

            <div class="form-group">
                <label for="data_inicio">
                    <i class="fas fa-calendar-alt"></i> Data de Início:
                </label>
                <input type="date" name="data_inicio" value="<?= $medicamento['data_inicio'] ?>"><br>
            </div>

            <div class="form-group">
                <label for="dosagem">
                    <i class="fas fa-prescription"></i> Dosagem:
                </label>
                <input type="text" name="dosagem" value="<?= $medicamento['dosagem'] ?>"><br>
            </div>

                 <div class="form-group">
                     <label>
                         <i class="fas fa-image"></i> Imagem do Medicamento:
                     </label>
                     <div class="file-input-container">
                         <label for="foto" class="file-input-button">
                             <i class="fas fa-upload"></i> Escolher Imagem
                         </label>
                         <input type="file" name="foto" id="foto"><br>
                     </div>
                     <div id="file-name" style="margin-top: 10px; color: #666;"></div>
                 </div>


                 <button type="submit" class="submit-button">
                <i class="fas fa-save"></i> Salvar Alteração
            </button>
        </form>
    </div>
</main>

<footer>
    <p>&copy; 2024 Sistema de Medicamentos Maya Rafaela</p>
</footer>

<script>
    // Mostrar nome do arquivo selecionado
    document.getElementById('foto').addEventListener('change', function(e) {
        const fileName = e.target.files[0]?.name || 'Nenhum arquivo selecionado';
        document.getElementById('file-name').textContent = fileName;
    });


    // Validação do formulário
    function validateForm() {
        const nome = document.getElementById('nome').value.trim();
        const dosagem = document.getElementById('dosagem').value.trim();

        if (nome === '') {
            alert('Por favor, preencha o nome do medicamento.');
            return false;
        }

        return true;
    }
</script>
</body>
</html>

<?php
// Fechar a conexão com o banco de dados
pg_close($conexao);
?>
