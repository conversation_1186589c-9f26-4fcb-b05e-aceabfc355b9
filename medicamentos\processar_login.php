<?php
// processar_login.php
require_once 'includes/auth.php';

// Verifica se o formulário foi enviado
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verifica se os campos foram preenchidos
    if (empty($_POST['usuario']) || empty($_POST['senha'])) {
        header("Location: login.php?erro=vazio");
        exit;
    }
    
    $usuario = $_POST['usuario'];
    $senha = $_POST['senha'];
    
    // Tenta fazer login
    $auth = new Auth();
    $usuario_dados = $auth->login($usuario, $senha);
    
    if ($usuario_dados) {
        // Login bem-sucedido, armazena dados na sessão
        $_SESSION['usuario_id'] = $usuario_dados['idusuario'];
        $_SESSION['usuario_nome'] = $usuario_dados['nome'];
        $_SESSION['usuario_email'] = $usuario_dados['email'];
        $_SESSION['is_admin'] = (bool)$usuario_dados['is_admin'];
        
        // Redireciona para a página inicial
        header("Location: index.php");
        exit;
    } else {
        // Login falhou
        header("Location: login.php?erro=credenciais");
        exit;
    }
} else {
    // Se não for POST, redireciona para a página de login
    header("Location: login.php");
    exit;
}
?>