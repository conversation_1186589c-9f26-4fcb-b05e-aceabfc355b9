/* Estilos para o modal do calendário */
.dia-item {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.dia-item:hover {
  transform: scale(1.15);
  z-index: 10;
}

/* Estilo do modal */
.modal-estudo {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1050;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

/* Garantir que o SweetAlert2 fique acima do modal de evento */
.swal2-container {
  z-index: 99999 !important;
}

.swal2-shown {
  z-index: 99999 !important;
}

.swal2-popup {
  z-index: 99999 !important;
}

.modal-estudo.ativo {
  visibility: visible;
  opacity: 1;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(3px);
}

#modal-estudo .modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  background: var(--hover);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
  overflow: hidden;
}

#modal-estudo .modal-body {
  overflow-y: auto;
  max-height: calc(90vh - 6rem);
  padding-right: 1rem;
}

#modal-estudo .modal-titulo {
  font-family: 'Quicksand', sans-serif;
  color: var(--primary-blue);
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  background: rgba(0, 0, 0, 0.1);
  border-bottom: 2px solid var(--vintage-gold, #b8860b);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.8rem 0;
  border-radius: 4px;
}

#modal-estudo .modal-conteudo {
  padding: 1rem;
}

#modal-estudo h4 {
  color: var(--primary-blue);
  margin-bottom: 0.8rem;
  font-size: 1.1rem;
}

#modal-estudo h5 {
  color: var(--primary-blue);
  margin: 1.5rem 0 0.5rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

#modal-estudo p {
  background: rgba(0, 0, 0, 0.03);
  padding: 0.8rem;
  border-radius: 6px;
  margin: 0.5rem 0;
}

/* Estilização da barra de rolagem */
.modal-body::-webkit-scrollbar {
  width: 8px;
}

.modal-body::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: var(--vintage-gold, #b8860b);
  border-radius: 4px;
}

/* Estilos específicos para o conteúdo do modal de estudo */
.modal-header-estudo {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.modal-header-estudo h3 {
  font-family: 'Quicksand', sans-serif;
  color: var(--primary-blue);
  margin: 0;
  font-size: 1.3rem;
}

.tempo-total {
  background: var(--primary-blue);
  color: var(--hover);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-family: 'Courier Prime', monospace;
  font-weight: 700;
}

.tempo-label {
  margin-right: 0.5rem;
  font-size: 0.9rem;
  opacity: 0.8;
}

.tempo-valor {
  font-size: 1.1rem;
}

.estudos-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.estudo-item {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 1rem;
  transition: transform 0.2s ease;
} [data-theme="dark"] .estudo-item {
     background: var(--card-border);
}

.estudo-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.estudo-cabecalho {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px dashed rgba(0, 0, 0, 0.1);
}

.materia-nome {
  font-family: 'Quicksand', sans-serif;
  font-size: 1.2rem;
  margin: 0;
}

.estudo-tempo {
  background: var(--primary-blue);
  color: var(--hover);
  font-family: 'Courier Prime', monospace;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 700;
}

.estudo-detalhes {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0.5rem 1rem;
}

.detalhe-linha {
  margin-bottom: 0.5rem;
  font-size: 0.95rem;
}

.detalhe-linha strong {
  color: var(--primary-blue);
  margin-right: 0.3rem;
}

.ponto-estudado, .descricao {
  grid-column: 1 / -1;
  background: rgba(0, 0, 0, 0.03);
  padding: 0.8rem;
  border-radius: 6px;
  margin-top: 0.5rem;
}

.descricao {
  margin-top: 0.75rem;
  background: rgba(0, 0, 139, 0.03);
  border-left: 3px solid rgba(0, 0, 139, 0.2);
}

.sem-estudos, .sem-estudo-mensagem {
  text-align: center;
  padding: 2rem;
  background: rgba(0, 0, 0, 0.03);
  border-radius: 8px;
  color: #666;
}

.sem-estudo-mensagem i {
  font-size: 3rem;
  color: #ccc;
  margin-bottom: 1rem;
  display: block;
}

.loading-spinner {
  text-align: center;
  padding: 3rem;
  font-size: 1.2rem;
  color: #666;
}

.loading-spinner i {
  font-size: 2rem;
  margin-bottom: 1rem;
  display: block;
  color: var(--dark-bg, #00008B);
}

.error-message {
  background: #fee;
  color: #c00;
  padding: 1rem;
  border-radius: 8px;
  border-left: 4px solid #c00;
  margin: 1rem 0;
}

.meta-diaria {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-top: 1rem;
  padding: 1rem;
  border-radius: 8px;
  font-family: 'Quicksand', sans-serif;
  font-weight: 600;
}

.meta-diaria.pendente {
  background: rgba(255, 152, 0, 0.1);
  border: 1px solid rgba(255, 152, 0, 0.3);
  color: #ff9800;
}

.meta-diaria.completada {
  background: rgba(76, 175, 80, 0.1);
  border: 1px solid rgba(76, 175, 80, 0.3);
  color: #4caf50;
}

.meta-diaria i {
  font-size: 1.5rem;
}

/* Responsividade */
@media (max-width: 768px) {
  .modal-content {
      width: 95%;
      padding: 1.5rem;
  }
  
  .estudo-detalhes {
      grid-template-columns: 1fr;
  }
  
  .estudo-cabecalho {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
  }
  
  .tempo-total {
      font-size: 0.9rem;
      padding: 0.4rem 0.8rem;
  }
}

/* Estilo para o botão de excluir no modal */
.excluirEventoModal {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  margin-top: 20px;
  width: 100%;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.excluirEventoModal:hover {
  background-color: #c82333;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4);
}

.excluirEventoModal:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.excluirEventoModal:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.3);
}

/* Estilo para o botão de salvar no modal */
.salvarEventoModal {
  background-color: #28a745;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  margin-top: 20px;
  width: 100%;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.salvarEventoModal:hover {
  background-color: #218838;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

.salvarEventoModal:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.salvarEventoModal:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.3);
}

/* Adicionar uma linha divisória antes do botão */
.modal-conteudo {
  position: relative;
}

.modal-conteudo .excluirEventoModal {
  position: relative;
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
