<?php
// editar_card.php
session_start();
include_once("assets/config.php");
include_once("helpers.php");

if (!isset($_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit();
}

redirectIfNotAdmin($conexao, $_SESSION['idusuario']);

if (!isset($_GET['id'])) {
    header("Location: flashcards.php");
    exit();
}

$card_id = (int)$_GET['id'];
$mensagem = '';

// Buscar informações do card, tópico e sua hierarquia
$query_card = "
    SELECT 
        f.*,
        t.nome as topic_name,
        t.id as topic_id,
        d.nome as deck_name,
        d.id as deck_id,
        c.nome as categoria_name,
        m.nome as materia_nome
    FROM appestudo.flashcards f
    JOIN appestudo.flashcard_topic_association ft ON ft.flashcard_id = f.id
    JOIN appestudo.flashcard_topics t ON t.id = ft.topic_id
    JOIN appestudo.flashcard_deck_association fd ON fd.flashcard_id = f.id
    JOIN appestudo.flashcard_decks d ON d.id = fd.deck_id
    JOIN appestudo.flashcard_categories c ON c.id = d.category_id
    JOIN appestudo.materia m ON m.idmateria = d.materia_id
    WHERE f.id = $1
    LIMIT 1";
$result_card = pg_query_params($conexao, $query_card, array($card_id));
$card = pg_fetch_assoc($result_card);

if (!$card) {
    header("Location: flashcards.php");
    exit();
}

// Buscar mapa mental existente
$query_mindmap = "SELECT imagem_base64 FROM appestudo.flashcard_mindmaps WHERE flashcard_id = $1";
$result_mindmap = pg_query_params($conexao, $query_mindmap, array($card_id));
$mindmap = pg_fetch_assoc($result_mindmap);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $pergunta = trim($_POST['pergunta']);
    $resposta = trim($_POST['resposta']);
    $resumo = trim($_POST['resumo']);
    $previsao_legal = trim($_POST['previsao_legal']);
    $jurisprudencia = trim($_POST['jurisprudencia']);
    
    if (!empty($pergunta) && !empty($resposta)) {
        pg_query($conexao, "BEGIN");
        
        try {
            // Atualizar dados do card
            $query_update = "
                UPDATE appestudo.flashcards 
                SET pergunta = $1, resposta = $2, resumo = $3, 
                    previsao_legal = $4, jurisprudencia = $5
                WHERE id = $6";
            
            $result = pg_query_params($conexao, $query_update, array(
                $pergunta, $resposta, $resumo, 
                $previsao_legal, $jurisprudencia, $card_id
            ));

            // Tratar upload de novo mapa mental
            if (isset($_FILES['mapa_mental']) && $_FILES['mapa_mental']['error'] === 0) {
                $imagedata = file_get_contents($_FILES['mapa_mental']['tmp_name']);
                $base64 = base64_encode($imagedata);
                
                // Deletar mapa mental existente
                pg_query_params($conexao, 
                    "DELETE FROM appestudo.flashcard_mindmaps WHERE flashcard_id = $1",
                    array($card_id)
                );
                
                // Inserir novo mapa mental
                $query_mindmap = "
                    INSERT INTO appestudo.flashcard_mindmaps (flashcard_id, imagem_base64)
                    VALUES ($1, $2)";
                pg_query_params($conexao, $query_mindmap, array($card_id, $base64));
            }

            pg_query($conexao, "COMMIT");
            header("Location: ver_cards.php?topico=" . $card['topic_id']);
            exit();
            
        } catch (Exception $e) {
            pg_query($conexao, "ROLLBACK");
            $mensagem = "Erro ao atualizar card: " . $e->getMessage();
        }
    } else {
        $mensagem = "Pergunta e resposta são obrigatórios.";
    }
}
?>

<!-- Restante do código HTML permanece o mesmo -->

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editar Card - <?php echo htmlspecialchars($card['topic_name']); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.8.2/tinymce.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Quicksand:wght@300..700&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Tektur:wght@400..900&family=Varela+Round&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #000080;
            --paper-color: #FFFFFF;
            --secondary-color: #4169E1;
            --background-color: #E8ECF3;
            --text-color: #2C3345;
            --border-color: #B8C2CC;
        }

        body {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 40px;
            line-height: 1.6;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .breadcrumb {
            color: var(--secondary-color);
            margin-bottom: 25px;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .form-container {
            background: var(--paper-color);
            padding: 30px;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
        }

        .form-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            color: var(--primary-color);
            margin-bottom: 8px;
            font-weight: 500;
        }

        .editor-content {
            min-height: 200px;
        }

        .file-input-container {
            margin-top: 15px;
        }

        .current-image {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            text-align: center;
        }

        .current-image img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin: 10px 0;
        }

        .actions {
            display: flex;
            justify-content: flex-end;
            gap: 12px;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-family: inherit;
            font-size: 1rem;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,128,0.15);
        }

        .btn-secondary {
            background: white;
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .file-input-button {
            background: #f8f9fa;
            border: 2px solid var(--border-color);
            color: var(--primary-color);
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: all 0.2s;
            font-weight: 500;
        }

        .file-input-button:hover {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }

        .file-input-hidden {
            display: none;
        }

        .file-name {
            margin-top: 8px;
            font-size: 0.9rem;
            color: #666;
        }

        .error-message {
            background: #FEE;
            border: 1px solid #FAA;
            color: #A00;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .error-message i {
            font-size: 1.2rem;
        }

        .btn-back {
            position: fixed;
            top: 20px;
            left: 20px;
            width: 50px;
            height: 50px;
            background: white;
            border: 2px solid var(--border-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.2s;
            z-index: 1000;
        }

        .btn-back:hover {
            transform: translateY(-2px);
            background: var(--primary-color);
            color: white;
        }

        @media (max-width: 768px) {
            body {
                padding: 20px;
            }

            .actions {
                flex-direction: column;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <a href="ver_cards.php?topico=<?php echo $card['topic_id']; ?>" class="btn-back">
        <i class="fas fa-arrow-left"></i>
    </a>

    <div class="container">
        <div class="form-container">
            <div class="breadcrumb">
                <i class="fas fa-layer-group"></i>
                <?php echo htmlspecialchars($card['categoria_name']); ?> > 
                <?php echo htmlspecialchars($card['materia_nome']); ?> >
                <?php echo htmlspecialchars($card['deck_name']); ?> >
                <?php echo htmlspecialchars($card['topic_name']); ?>
            </div>

            <h1>Editar Card</h1>

            <?php if (!empty($mensagem)): ?>
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($mensagem); ?>
                </div>
            <?php endif; ?>

            <form method="POST" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="pergunta">Pergunta*</label>
                    <textarea id="pergunta" name="pergunta" class="editor-content" required>
                        <?php echo htmlspecialchars($card['pergunta']); ?>
                    </textarea>
                </div>

                <div class="form-group">
                    <label for="resposta">Resposta*</label>
                    <textarea id="resposta" name="resposta" class="editor-content" required>
                        <?php echo htmlspecialchars($card['resposta']); ?>
                    </textarea>
                </div>

                <div class="form-group">
                    <label for="resumo">Resumo</label>
                    <textarea id="resumo" name="resumo" class="editor-content">
                        <?php echo htmlspecialchars($card['resumo']); ?>
                    </textarea>
                </div>

                <div class="form-group">
                    <label for="previsao_legal">Previsão Legal</label>
                    <textarea id="previsao_legal" name="previsao_legal" class="editor-content">
                        <?php echo htmlspecialchars($card['previsao_legal']); ?>
                    </textarea>
                </div>

                <div class="form-group">
                    <label for="jurisprudencia">Jurisprudência</label>
                    <textarea id="jurisprudencia" name="jurisprudencia" class="editor-content">
                        <?php echo htmlspecialchars($card['jurisprudencia']); ?>
                    </textarea>
                </div>

                <div class="form-group">
                    <label>Mapa Mental</label>
                    <?php if ($mindmap): ?>
                        <div class="current-image">
                            <p>Mapa mental atual:</p>
                            <img src="data:image/jpeg;base64,<?php echo $mindmap['imagem_base64']; ?>" 
                                 alt="Mapa Mental Atual">
                        </div>
                    <?php endif; ?>
                    <div class="file-input-container">
                        <label class="file-input-button">
                            <i class="fas fa-upload"></i>
                            <?php echo $mindmap ? 'Alterar Mapa Mental' : 'Adicionar Mapa Mental'; ?>
                            <input type="file" class="file-input-hidden" id="mapa_mental" 
                                   name="mapa_mental" accept="image/*">
                        </label>
                        <div class="file-name" id="fileName">
                            Nenhum arquivo selecionado
                        </div>
                    </div>
                </div>

                <div class="actions">
                    <a href="ver_cards.php?topico=<?php echo $card['topic_id']; ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Salvar Alterações
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Configuração do TinyMCE
        tinymce.init({
            selector: '.editor-content',
            height: 300,
            menubar: false,
            language: 'pt_BR',
            language_url: 'https://cdn.tiny.cloud/1/no-api-key/tinymce/6/langs/pt_BR.js',
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap',
                'visualblocks', 'code', 'fullscreen',
                'table', 'help', 'wordcount', 'emoticons'
            ],
            toolbar: [
                'styles | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify',
                'bullist numlist | outdent indent | forecolor backcolor | table | removeformat'
            ],
            content_style: `
                body {
                    font-family: 'Inter', system-ui, -apple-system, sans-serif;
                    font-size: 16px;
                    line-height: 1.6;
                    color: #2C3345;
                }
            `,
            browser_spellcheck: true,
            contextmenu: false,
            resize: true,
            paste_data_images: true,
            paste_as_text: false,
            table_style_by_css: true,
            table_sizing_mode: 'relative',
            table_default_attributes: {
                class: 'table-responsive'
            },
            table_default_styles: {
                width: '100%'
            },
            statusbar: false,
            elementpath: false,
            branding: false,
            promotion: false,
            translations: {
                'pt_BR': {
                    'Bold': 'Negrito',
                    'Italic': 'Itálico',
                    'Underline': 'Sublinhado',
                    'Strikethrough': 'Tachado',
                    'Align left': 'Alinhar à esquerda',
                    'Align center': 'Centralizar',
                    'Align right': 'Alinhar à direita',
                    'Justify': 'Justificar',
                    'Bullet list': 'Lista não ordenada',
                    'Numbered list': 'Lista ordenada',
                    'Decrease indent': 'Diminuir recuo',
                    'Increase indent': 'Aumentar recuo',
                    'Clear formatting': 'Limpar formatação'
                }
            }
        });

        // Script para atualizar o nome do arquivo quando selecionado
        document.getElementById('mapa_mental').addEventListener('change', function(e) {
            const fileName = e.target.files[0] ? e.target.files[0].name : 'Nenhum arquivo selecionado';
            document.getElementById('fileName').textContent = fileName;
        });
    </script>
</body>
</html>