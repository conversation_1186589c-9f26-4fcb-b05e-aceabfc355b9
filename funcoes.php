<?php
// Função para converter tempo (HH:MM:SS) para segundos
function converterParaSegundos($tempo) {
    list($horas, $minutos, $segundos) = explode(':', $tempo);
    return $horas * 3600 + $minutos * 60 + $segundos;
}

// Função para converter segundos para tempo (HH:MM:SS)
function converterParaTempo($segundos) {
    $horas = floor($segundos / 3600);
    $segundos %= 3600;
    $minutos = floor($segundos / 60);
    $segundos %= 60;
    return sprintf('%02d:%02d:%02d', $horas, $minutos, $segundos);
}
?>
