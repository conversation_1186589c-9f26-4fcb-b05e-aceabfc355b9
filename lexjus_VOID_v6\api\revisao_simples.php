<?php
// API simplificada para teste do sistema de revisão
ob_start();
header('Content-Type: application/json');

session_start();
require_once __DIR__ . '/../../conexao_POST.php';

// Verificar autenticação
if (!isset($_SESSION['idusuario'])) {
    ob_clean();
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit;
}

$usuario_id = $_SESSION['idusuario'];
$metodo = $_SERVER['REQUEST_METHOD'];
$dados = json_decode(file_get_contents('php://input'), true);

try {
    switch ($metodo) {
        case 'GET':
            if (isset($_GET['acao']) && $_GET['acao'] === 'estatisticas') {
                obterEstatisticasSimples($conexao, $usuario_id);
            } else {
                listarRevisoesSimples($conexao, $usuario_id);
            }
            break;

        case 'POST':
            if (isset($dados['acao'])) {
                switch ($dados['acao']) {
                    case 'iniciar':
                        iniciarRevisaoSimples($conexao, $usuario_id, $dados);
                        break;
                    case 'responder':
                        responderRevisaoSimples($conexao, $usuario_id, $dados);
                        break;
                    default:
                        throw new Exception('Ação não reconhecida');
                }
            } else {
                throw new Exception('Ação não especificada');
            }
            break;

        default:
            throw new Exception('Método não permitido');
    }
} catch (Exception $e) {
    ob_clean();
    http_response_code(500);
    echo json_encode(['erro' => $e->getMessage()]);
}

function obterEstatisticasSimples($conexao, $usuario_id) {
    $query = "
        SELECT 
            COUNT(*) as total_artigos,
            COUNT(CASE WHEN status = 'novo' THEN 1 END) as novos,
            COUNT(CASE WHEN status = 'aprendendo' THEN 1 END) as aprendendo,
            COUNT(CASE WHEN status = 'revisando' THEN 1 END) as revisando,
            COUNT(CASE WHEN status = 'dominado' THEN 1 END) as dominados,
            COUNT(CASE WHEN status = 'dificil' THEN 1 END) as dificeis,
            COUNT(CASE WHEN data_proxima_revisao <= CURRENT_TIMESTAMP THEN 1 END) as pendentes,
            ROUND(AVG(facilidade), 2) as facilidade_media
        FROM appestudo.lexjus_revisoes 
        WHERE usuario_id = $1";

    $result = pg_query_params($conexao, $query, [$usuario_id]);

    if (!$result) {
        throw new Exception('Erro ao obter estatísticas: ' . pg_last_error($conexao));
    }

    $stats = pg_fetch_assoc($result);
    
    // Converter para tipos apropriados
    foreach ($stats as $key => $value) {
        if (in_array($key, ['total_artigos', 'novos', 'aprendendo', 'revisando', 'dominados', 'dificeis', 'pendentes'])) {
            $stats[$key] = (int)$value;
        } elseif ($key === 'facilidade_media') {
            $stats[$key] = (float)$value;
        }
    }

    ob_clean();
    echo json_encode(['estatisticas' => $stats]);
}

function listarRevisoesSimples($conexao, $usuario_id) {
    $query = "
        SELECT * FROM appestudo.lexjus_revisoes 
        WHERE usuario_id = $1 
        ORDER BY data_atualizacao DESC";

    $result = pg_query_params($conexao, $query, [$usuario_id]);

    if (!$result) {
        throw new Exception('Erro ao consultar revisões: ' . pg_last_error($conexao));
    }

    $revisoes = [];
    while ($row = pg_fetch_assoc($result)) {
        $revisoes[] = [
            'id' => (int)$row['id'],
            'artigo_numero' => $row['artigo_numero'],
            'facilidade' => (float)$row['facilidade'],
            'intervalo_dias' => (int)$row['intervalo_dias'],
            'repeticoes' => (int)$row['repeticoes'],
            'status' => $row['status'],
            'data_proxima_revisao' => $row['data_proxima_revisao'],
            'total_revisoes' => (int)$row['total_revisoes'],
            'acertos_consecutivos' => (int)$row['acertos_consecutivos']
        ];
    }

    ob_clean();
    echo json_encode(['revisoes' => $revisoes]);
}

function iniciarRevisaoSimples($conexao, $usuario_id, $dados) {
    if (!isset($dados['artigo_numero'])) {
        throw new Exception('Número do artigo não informado');
    }

    $artigo_numero = $dados['artigo_numero'];

    // Verificar se já existe
    $query_check = "
        SELECT id FROM appestudo.lexjus_revisoes 
        WHERE usuario_id = $1 AND artigo_numero = $2";

    $result_check = pg_query_params($conexao, $query_check, [$usuario_id, $artigo_numero]);

    if (!$result_check) {
        throw new Exception('Erro na consulta: ' . pg_last_error($conexao));
    }

    if (pg_num_rows($result_check) > 0) {
        $row = pg_fetch_assoc($result_check);
        ob_clean();
        echo json_encode([
            'sucesso' => true,
            'revisao_id' => (int)$row['id'],
            'mensagem' => 'Revisão já existente'
        ]);
        return;
    }

    // Criar nova revisão
    $query_insert = "
        INSERT INTO appestudo.lexjus_revisoes 
        (usuario_id, artigo_numero, status, data_proxima_revisao)
        VALUES ($1, $2, 'novo', CURRENT_TIMESTAMP)
        RETURNING id";

    $result_insert = pg_query_params($conexao, $query_insert, [$usuario_id, $artigo_numero]);

    if (!$result_insert) {
        throw new Exception('Erro ao criar revisão: ' . pg_last_error($conexao));
    }

    $row = pg_fetch_assoc($result_insert);
    $revisao_id = $row['id'];

    ob_clean();
    echo json_encode([
        'sucesso' => true,
        'revisao_id' => (int)$revisao_id,
        'mensagem' => 'Revisão iniciada com sucesso'
    ]);
}

function responderRevisaoSimples($conexao, $usuario_id, $dados) {
    if (!isset($dados['artigo_numero']) || !isset($dados['qualidade'])) {
        throw new Exception('Dados incompletos');
    }

    $artigo_numero = $dados['artigo_numero'];
    $qualidade = (int)$dados['qualidade'];

    if ($qualidade < 0 || $qualidade > 5) {
        throw new Exception('Qualidade deve estar entre 0 e 5');
    }

    // Buscar revisão atual
    $query_current = "
        SELECT * FROM appestudo.lexjus_revisoes 
        WHERE usuario_id = $1 AND artigo_numero = $2";

    $result_current = pg_query_params($conexao, $query_current, [$usuario_id, $artigo_numero]);

    if (!$result_current) {
        throw new Exception('Erro na consulta: ' . pg_last_error($conexao));
    }

    if (pg_num_rows($result_current) === 0) {
        throw new Exception('Revisão não encontrada para o artigo ' . $artigo_numero);
    }

    $revisao_atual = pg_fetch_assoc($result_current);

    // Algoritmo SM-2 simplificado
    $facilidade_atual = (float)$revisao_atual['facilidade'];
    $intervalo_atual = (int)$revisao_atual['intervalo_dias'];
    $repeticoes_atual = (int)$revisao_atual['repeticoes'];

    // Calcular nova facilidade
    $nova_facilidade = $facilidade_atual + (0.1 - (5 - $qualidade) * (0.08 + (5 - $qualidade) * 0.02));
    $nova_facilidade = max(1.30, min(3.00, $nova_facilidade));

    // Calcular novo intervalo e repetições
    if ($qualidade < 3) {
        $novas_repeticoes = 0;
        $novo_intervalo = 1;
        $novo_status = 'dificil';
    } else {
        $novas_repeticoes = $repeticoes_atual + 1;
        
        if ($novas_repeticoes == 1) {
            $novo_intervalo = 1;
        } elseif ($novas_repeticoes == 2) {
            $novo_intervalo = 6;
        } else {
            $novo_intervalo = round($intervalo_atual * $nova_facilidade);
        }

        if ($novas_repeticoes >= 5) {
            $novo_status = 'dominado';
        } elseif ($novas_repeticoes >= 2) {
            $novo_status = 'revisando';
        } else {
            $novo_status = 'aprendendo';
        }
    }

    $nova_data_revisao = date('Y-m-d H:i:s', strtotime("+{$novo_intervalo} days"));

    // Atualizar revisão
    $query_update = "
        UPDATE appestudo.lexjus_revisoes 
        SET facilidade = $1,
            intervalo_dias = $2,
            repeticoes = $3,
            data_ultima_revisao = CURRENT_TIMESTAMP,
            data_proxima_revisao = $4,
            status = $5,
            ultima_qualidade = $6,
            total_revisoes = total_revisoes + 1,
            acertos_consecutivos = CASE 
                WHEN $6 >= 3 THEN acertos_consecutivos + 1 
                ELSE 0 
            END
        WHERE usuario_id = $7 AND artigo_numero = $8";

    $result_update = pg_query_params($conexao, $query_update, [
        round($nova_facilidade, 2),
        $novo_intervalo,
        $novas_repeticoes,
        $nova_data_revisao,
        $novo_status,
        $qualidade,
        $usuario_id,
        $artigo_numero
    ]);

    if (!$result_update) {
        throw new Exception('Erro ao atualizar revisão: ' . pg_last_error($conexao));
    }

    ob_clean();
    echo json_encode([
        'sucesso' => true,
        'nova_facilidade' => round($nova_facilidade, 2),
        'novo_intervalo' => $novo_intervalo,
        'novas_repeticoes' => $novas_repeticoes,
        'data_proxima_revisao' => $nova_data_revisao,
        'novo_status' => $novo_status,
        'mensagem' => 'Resposta processada com sucesso'
    ]);
}
?>
