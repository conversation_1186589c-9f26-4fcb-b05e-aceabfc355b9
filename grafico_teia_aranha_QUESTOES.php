<?php
include 'conexao_POST.php';
include 'processa_index.php';
include_once 'funcoes.php'; // Inclua as funções aqui

// Verifica se $id_planejamento e $id_usuario estão definidos
if (!isset($id_planejamento) || !isset($id_usuario)) {
    die("ID do planejamento ou ID do usuário não definido.");
}

// Consulta para obter os dados das matérias associadas ao planejamento
$query_consultar_materias_planejamento = "
    SELECT pm.materia_idmateria, m.nome AS nome_materia, m.cor AS cor_materia
    FROM appEstudo.planejamento_materia pm
    INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    WHERE pm.planejamento_idplanejamento = $id_planejamento";

$resultado_materias_planejamento = pg_query($conexao, $query_consultar_materias_planejamento);

// Inicializa arrays para armazenar os dados das matérias
$materias_no_planejamento = array();
$cores_materias = array();

// Preenche os arrays com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_materias_planejamento)) {
    $materia_id = $row['materia_idmateria'];
    $nome_materia = $row['nome_materia'];
    $cor_materia = $row['cor_materia'];

    $materias_no_planejamento[$materia_id] = $nome_materia;
    $cores_materias[$nome_materia] = $cor_materia;
}

// Consulta para obter a quantidade de questões certas, erradas e total de questões por matéria para o usuário específico
$query_consulta_questoes = "
    SELECT e.materia_idmateria, 
           m.nome AS nome_materia,
           SUM(e.q_total) AS total_questoes,
           SUM(e.q_certa) AS total_certas, 
           SUM(e.q_errada) AS total_erradas
    FROM appEstudo.estudos e
    INNER JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria
    WHERE e.planejamento_usuario_idusuario = $id_usuario
      AND e.metodo = 'Questões'
    GROUP BY e.materia_idmateria, m.nome";

$resultado_consulta_questoes = pg_query($conexao, $query_consulta_questoes);

// Inicializa arrays para armazenar os dados das questões por matéria
$questoes_por_materia = array();

// Preenche o array com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_consulta_questoes)) {
    $materia = $row['nome_materia'];
    $total_questoes = $row['total_questoes'];
    $total_certas = $row['total_certas'];
    $total_erradas = $row['total_erradas'];

    $questoes_por_materia[$materia] = array(
        'total' => (int)$total_questoes, // Garantir que o total seja um número inteiro
        'certas' => (int)$total_certas, // Garantir que as certas sejam um número inteiro
        'erradas' => (int)$total_erradas // Garantir que as erradas sejam um número inteiro
    );
}

// Convertendo os arrays para JSON para serem utilizados no JavaScript
$dados_json = json_encode($questoes_por_materia);
$cores_json = json_encode($cores_materias);
?>

<div id="grafico"></div>
<style>
    .highcharts-title {
        font-family: 'Indie Flower', cursive;
    }
    .highcharts-xaxis-labels text {
        font-family: "Courier Prime", monospace;
    }
    .highcharts-yaxis-labels text, .highcharts-tooltip text {
        font-family: "Courier Prime", monospace;
    }
</style>


<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Recuperando os dados do PHP
        var questoesPorMateria = <?php echo $dados_json; ?>;
        var coresMaterias = <?php echo $cores_json; ?>;

        // Extrair categorias (matérias) e dados (questões certas, erradas e totais) do objeto
        var materias = Object.keys(questoesPorMateria);
        var questoesCertas = materias.map(materia => questoesPorMateria[materia].certas);
        var questoesErradas = materias.map(materia => questoesPorMateria[materia].erradas);
        var questoesTotais = materias.map(materia => questoesPorMateria[materia].total);

        // Calcular e formatar a porcentagem de questões certas e erradas
        var questoesCertasPorcentagem = materias.map(materia => {
            var certas = questoesPorMateria[materia].certas;
            var total = questoesPorMateria[materia].total;
            return total > 0 ? ((certas / total) * 100).toFixed(1) : 0;
        });

        var questoesErradasPorcentagem = materias.map(materia => {
            var erradas = questoesPorMateria[materia].erradas;
            var total = questoesPorMateria[materia].total;
            return total > 0 ? ((erradas / total) * 100).toFixed(1) : 0;
        });

        // Configuração do gráfico Highcharts
        Highcharts.chart('grafico', {
            chart: {
                type: 'column',
                backgroundColor: 'rgba(0, 0, 0, 0)' // Fundo transparente
            },
            title: {
                text: 'Quantidade de Questões <span style="color: green;">Certas</span> e <span style="color: red;">Erradas</span> por Matéria',
            },
            xAxis: {
                categories: materias,
                crosshair: true,
                lineColor: 'black', // Cor da linha horizontal
                tickColor: 'black' // Cor dos ticks (marcadores) no eixo x
            },
            yAxis: {
                min: 0,
                title: {
                    text: 'Quantidade de Questões'
                },
                gridLineColor: 'rgba(0,0,0,0.41)' // Cor das linhas do grid no eixo y
            },
            tooltip: {
                headerFormat: '<b>{point.x}</b><br/>',
                pointFormat: '{series.name}: {point.y}<br/>Total: {point.stackTotal} ({point.percentage:.1f}%)'
            },
            plotOptions: {
                column: {
                    stacking: 'normal',
                    dataLabels: {
                        enabled: true,
                        formatter: function () {
                            var total = this.point.stackTotal;
                            var percentage = total > 0 ? ((this.y / total) * 100).toFixed(1) + '%' : '';
                            return this.y !== 0 ? this.y + ' (' + percentage + ')' : null;
                        }
                    },
                    colorByPoint: false, // Desativar colorByPoint para definir cores manualmente
                    borderColor: 'rgba(0,0,0,0.39)', // Cor do contorno da barra
                    borderWidth: 1, // Largura do contorno da barra
                    colors: ['#000000', '#000000'] // Definir cores das barras (preto para ambas as séries)
                }
            },
            series: [{
                name: 'Erradas',
                data: questoesErradas,
                color: 'rgba(255,0,0,0.66)'
            }, {
                name: 'Certas',
                data: questoesCertas,
                color: 'rgba(12,135,12,0.6)'
            }],
            credits: {
                enabled: false // Remover o crédito no canto inferior direito
            }
        });
    });
</script>
