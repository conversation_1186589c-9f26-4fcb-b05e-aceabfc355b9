<?php
// index.php
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'includes/medicamento.php';
require_once 'includes/registro.php';

// Verificar se o usuário está logado
verificarLogin();

// Instanciar objetos
$medicamento = new Medicamento();
$registro = new Registro();

// Obter os próximos medicamentos a serem tomados
$proximos = $medicamento->obterProximos();

// Obter medicamentos pendentes (que deveriam ter sido tomados)
$pendentes = $registro->listarPendentes();

// Obter todos os medicamentos ativos
$medicamentos = $medicamento->listar();
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MediMaya • Controle de Medicamentos</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
    <div class="container my-4">
        <header class="mb-4 d-flex justify-content-between align-items-center">
            <div>
                <h1 class="text-center"><i class="fas fa-pills me-2"></i>MediMaya</h1>
                <p class="text-muted mb-0">Seu assistente pessoal de medicamentos</p>
            </div>
            <div class="dropdown">
                <button class="btn btn-secondary dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-user me-1"></i> <?= htmlspecialchars($_SESSION['usuario_nome']) ?>
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                    <?php if ($_SESSION['is_admin']): ?>
                    <li><a class="dropdown-item" href="admin.php"><i class="fas fa-cog me-2"></i>Painel Admin</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <?php endif; ?>
                    <li><a class="dropdown-item" href="logout.php"><i class="fas fa-sign-out-alt me-2"></i>Sair</a></li>
                </ul>
            </div>
        </header>
        
        <div class="row fade-in">
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h2 class="h5 mb-0"><i class="fas fa-exclamation-circle me-2"></i>Medicamentos Pendentes</h2>
                    </div>
                    <div class="card-body">
                        <?php if ($pendentes->rowCount() > 0): ?>
                            <div class="list-group">
                                <?php while ($item = $pendentes->fetch()): ?>
                                    <div class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                        <div>
                                            <h5 class="mb-1"><?= htmlspecialchars($item['nome']) ?></h5>
                                            <p class="mb-1"><i class="fas fa-prescription me-1"></i> Dosagem: <?= htmlspecialchars($item['dosagem']) ?></p>
                                            <small><i class="far fa-clock me-1"></i> Horário: <?= date('d/m/Y H:i', strtotime($item['data_hora'])) ?></small>
                                        </div>
                                        <a href="confirmar_uso.php?id=<?= $item['id'] ?>" class="btn btn-sm btn-success">
                                            <i class="fas fa-check me-1"></i> Confirmar
                                        </a>
                                    </div>
                                <?php endwhile; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                                <p>Não há medicamentos pendentes.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-4">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h2 class="h5 mb-0"><i class="fas fa-hourglass-half me-2"></i>Próximos Medicamentos</h2>
                    </div>
                    <div class="card-body">
                        <?php if ($proximos->rowCount() > 0): ?>
                            <div class="list-group">
                                <?php while ($item = $proximos->fetch()): ?>
                                    <div class="list-group-item">
                                        <h5 class="mb-1"><?= htmlspecialchars($item['nome']) ?></h5>
                                        <p class="mb-1"><i class="fas fa-prescription me-1"></i> Dosagem: <?= htmlspecialchars($item['dosagem']) ?></p>
                                        <small><i class="far fa-clock me-1"></i> Horário: <?= date('d/m/Y H:i', strtotime($item['data_hora'])) ?></small>
                                    </div>
                                <?php endwhile; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-center py-4">
                                <i class="far fa-calendar-check fa-3x text-info mb-3"></i>
                                <p>Não há medicamentos programados para as próximas horas.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mb-4 fade-in">
            <div class="card-header bg-secondary text-white d-flex justify-content-between align-items-center">
                <h2 class="h5 mb-0"><i class="fas fa-list-alt me-2"></i>Meus Medicamentos</h2>
                <a href="adicionar_medicamento.php" class="btn btn-sm btn-light">
                    <i class="fas fa-plus me-1"></i> Adicionar Novo
                </a>
            </div>
            <div class="card-body">
                <?php if ($medicamentos->rowCount() > 0): ?>
                    <div class="row medicamentos-grid">
                        <?php while ($item = $medicamentos->fetch()): ?>
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            <i class="fas fa-capsules me-2"></i><?= htmlspecialchars($item['nome']) ?>
                                        </h5>
                                        <h6 class="card-subtitle mb-2 text-muted"><?= htmlspecialchars($item['dosagem']) ?></h6>
                                        <p class="card-text">
                                            <i class="fas fa-clock me-1"></i> Intervalo: <?= $item['intervalo_horas'] ?> horas<br>
                                            <i class="fas fa-calendar-day me-1"></i> Início: <?= date('d/m/Y', strtotime($item['data_inicio'])) ?><br>
                                            <i class="fas fa-calendar-alt me-1"></i> Duração: <?= $item['dias_tratamento'] ?> dias
                                        </p>
                                    </div>
                                <div class="card-footer">
                                    <div class="d-flex justify-content-between flex-wrap gap-2">
                                        <a href="visualizar_medicamento.php?id=<?= $item['id'] ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye me-1"></i> Detalhes
                                        </a>
                                        <div class="d-flex gap-2">
                                            <a href="editar_medicamento.php?id=<?= $item['id'] ?>" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit me-1"></i> Editar
                                            </a>
                                            <a href="excluir_medicamento.php?id=<?= $item['id'] ?>" class="btn btn-sm btn-danger" onclick="return confirm('Tem certeza que deseja excluir este medicamento e todos os seus registros?')">
                                                <i class="fas fa-trash-alt me-1"></i> Excluir
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-5">
                        <i class="fas fa-prescription-bottle fa-4x text-muted mb-3"></i>
                        <p class="mb-4">Nenhum medicamento cadastrado.</p>
                        <a href="adicionar_medicamento.php" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus me-2"></i> Adicionar Primeiro Medicamento
                        </a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <script src="assets/js/app.js"></script>
</body>
</html>