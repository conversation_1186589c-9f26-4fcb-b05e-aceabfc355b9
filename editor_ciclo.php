<?php
session_start();
include_once("conexao_POST.php");

$id_usuario = $_SESSION['idusuario'];

// Buscar id do planejamento atual do usuário
$query_planejamento = "SELECT idplanejamento 
                      FROM appEstudo.planejamento 
                      WHERE usuario_idusuario = $id_usuario";
$resultado_planejamento = pg_query($conexao, $query_planejamento);
$planejamento = pg_fetch_assoc($resultado_planejamento);
$id_planejamento = $planejamento['idplanejamento'];

// Buscar matérias do planejamento com sua ordem atual
$sql = "SELECT 
            mhp.materia_idmateria,
            m.nome,
            m.cor,
            mhp.ordem
        FROM appEstudo.planejamento_materia mhp
        JOIN appEstudo.materia m ON m.idmateria = mhp.materia_idmateria
        WHERE mhp.planejamento_idplanejamento = $id_planejamento
        ORDER BY mhp.ordem ASC";

$result = pg_query($conexao, $sql);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title>Ordenar Matérias</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <link href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css" rel="stylesheet">

    <style>
        :root {
    --primary: #00008B;
    --secondary: #f8f0e3;
    --accent: #4a4a4a;
    --border: #e0e0e0;
    --text: #2c3e50;
    --active: #00008B;
    --hover: #f5f5f5;
    --primary-blue: #00008B;
    
    

    --parchment: #f8f0e3;
    --vintage-gold: #b8860b;
    --burgundy: rgb(222, 184, 135);
    --gold-accent: #daa520;
    --shadow-color: rgba(0, 0, 0, 0.2);
    --elegant-border: linear-gradient(45deg, var(--vintage-gold), #ffd700, var(--vintage-gold));
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --dark-bg: #00008B;

    --white: #ffffff;
    --shadow: rgba(0, 0, 0, 0.1);
}
        body {
            font-family: 'Courier Prime', monospace;
            padding: 20px;
            background: var(--hover);;
            color: #2c1810;
            line-height: 1.6;
        }

        .container {
            max-width: 600px;
            margin: 0 auto;
            background: #fff;
            padding: 0;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid #d3c5b8;
            position: relative;
            overflow: hidden;
        }

        .header-vintage {
            background: var(--primary);
            padding: 20px;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
            position: relative;
        }

        .header-vintage h2 {
            font-family: 'Quicksand', sans-serif;
            font-size: 24px;
            color: #fff;
            margin: 0 0 10px 0;
            padding: 0;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
            letter-spacing: 1px;
        }

        .header-vintage p {
            font-family: 'Quicksand', sans-serif;
            font-size: 14px;
            color: var(--hover);
            margin: 0;
            padding: 0;
            font-weight: 400;
            opacity: 0.9;
        }

        .content-vintage {
            padding: 30px;
            position: relative;
        }

        .materia-item {
            display: flex;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            background: var(--hover);
            border-radius: 6px;
            border-left: 4px solid;
            cursor: move;
            transition: all 0.3s ease;
            box-shadow: 2px 2px 5px rgba(0,0,0,0.05);
        }

        .materia-item:hover {
            transform: translateX(5px);
            box-shadow: 3px 3px 10px rgba(0,0,0,0.1);
        }

        .ordem-numero {
            width: 32px;
            height: 32px;
            background: #fff;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: bold;
            border: 2px solid #d4a373;
            color: #774936;
            font-family: 'Courier Prime', monospace;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .materia-nome {
            flex-grow: 1;
            font-family: 'Quicksand', sans-serif;
            font-size: 16px;
            font-weight: 500;
        }

        .drag-handle {
            color: #b08968;
            margin-left: 15px;
            cursor: move;
            transition: color 0.3s;
        }

        .drag-handle:hover {
            color: #8b4513;
        }

        .btn-salvar {
            display: block;
            width: 100%;
            padding: 15px;
            background: var(--primary);
            color: var(--hover);
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-family: 'Quicksand', sans-serif;
            cursor: pointer;
            margin-top: 25px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .btn-salvar:hover {
            background: #2563eb;
            color: var(--hover);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .mensagem {
            padding: 15px;
            margin: 15px 0;
            border-radius: 6px;
            display: none;
            font-family: 'Quicksand', sans-serif;
            text-align: center;
            font-weight: 500;
        }

        .sucesso {
            background: linear-gradient(to right, #e9edc9, #fefae0);
            color: #606c38;
            border: 1px solid #bc6c25;
        }

        .erro {
            background: linear-gradient(to right, #ffd7d7, #ffe5e5);
            color: #9b2226;
            border: 1px solid #9b2226;
        }

        .placeholder {
            border: 2px dashed #bc6c25;
            background: #fefae0;
            height: 54px;
            margin: 10px 0;
            border-radius: 6px;
            opacity: 0.7;
        }

        .header-vintage::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(to right,
            rgba(0,0,139,0.7),
            rgba(25,25,179,0.7),
            rgba(0,0,139,0.7));
        }

        .content-vintage::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 10px;
            background: linear-gradient(to bottom,
            rgba(0,0,0,0.05),
            rgba(0,0,0,0));
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header-vintage">
        <h2>Ordenar Matérias</h2>
        <p>Arraste as matérias para reorganizar a ordem do ciclo</p>
    </div>

    <div class="content-vintage">
        <div id="lista-materias">
            <?php
            while ($materia = pg_fetch_assoc($result)): ?>
                <div class="materia-item"
                     data-id="<?php echo $materia['materia_idmateria']; ?>"
                     style="border-left-color: <?php echo $materia['cor']; ?>">
                    <div class="ordem-numero"></div>
                    <div class="materia-nome"><?php echo htmlspecialchars($materia['nome']); ?></div>
                    <div class="drag-handle">
                        <i class="fas fa-grip-lines"></i>
                    </div>
                </div>
            <?php endwhile; ?>
        </div>

        <button class="btn-salvar" onclick="salvarOrdem()">Salvar Nova Ordem</button>
        <div class="mensagem sucesso" id="mensagem-sucesso">Ordem salva com sucesso!</div>
        <div class="mensagem erro" id="mensagem-erro">Erro ao salvar a ordem. Tente novamente.</div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Inicializar números de ordem
        atualizarNumeros();

        // Inicializar sortable
        $("#lista-materias").sortable({
            placeholder: "placeholder",
            handle: ".drag-handle",
            update: atualizarNumeros
        });
    });

    function atualizarNumeros() {
        $(".materia-item").each(function(index) {
            $(this).find(".ordem-numero").text(index + 1);
        });
    }

    function salvarOrdem() {
        var ordem = [];
        $(".materia-item").each(function(index) {
            ordem.push({
                materia_id: $(this).data('id'),
                ordem: index + 1
            });
        });

        $.ajax({
            url: 'salvar_ordem_ciclo.php',
            method: 'POST',
            data: {
                ordem: JSON.stringify(ordem),
                planejamento_id: <?php echo $id_planejamento; ?>
            },
            success: function(response) {
                $("#mensagem-sucesso").fadeIn().delay(1000).fadeOut(400, function() {
                    // Mostra mensagem de atualização
                    $("#mensagem-sucesso").html("Atualizando sua página... Aguarde Carregamento...");
                    $("#mensagem-sucesso").fadeIn().delay(1500).fadeOut(400, function() {
                        // Atualiza a página pai e fecha o modal
                        if (window.parent && window.parent.atualizarCiclo) {
                            window.parent.atualizarCiclo();
                        }
                        if (window.parent && window.parent.fecharModalCiclo) {
                            window.parent.fecharModalCiclo();
                        }
                    });
                });
            },
            error: function() {
                $("#mensagem-erro").fadeIn().delay(2000).fadeOut();
            }
        });
    }
</script>
</body>
</html>