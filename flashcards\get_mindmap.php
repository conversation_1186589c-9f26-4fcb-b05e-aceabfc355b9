<?php
// get_mindmap.php
session_start();
include_once("assets/config.php");

if (!isset($_SESSION['idusuario']) || !isset($_GET['flashcard_id'])) {
    echo json_encode(['error' => 'Parâmetros inválidos']);
    exit();
}

$flashcard_id = (int)$_GET['flashcard_id'];

$query = "
    SELECT imagem_base64
    FROM appestudo.flashcard_mindmaps
    WHERE flashcard_id = $1";

$result = pg_query_params($conexao, $query, array($flashcard_id));
$mindmap = pg_fetch_assoc($result);

header('Content-Type: application/json');
echo json_encode($mindmap ?: ['imagem_base64' => null]);
?>