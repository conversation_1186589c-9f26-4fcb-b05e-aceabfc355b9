<?php
// Headers para debug
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Log de debug
error_log("=== TESTE PRODUÇÃO === " . date('Y-m-d H:i:s'));
error_log("Método: " . $_SERVER['REQUEST_METHOD']);
error_log("Content-Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'N/A'));

session_start();
require_once __DIR__ . '/../../conexao_POST.php';

// Verificar se o usuário está autenticado
if (!isset($_SESSION['idusuario'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Usuário não autenticado']);
    exit;
}

$usuario_id = $_SESSION['idusuario'];

try {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // Ler dados POST
        $input = file_get_contents('php://input');
        error_log("Input recebido: " . $input);
        
        if (empty($input)) {
            throw new Exception("Nenhum dado POST recebido");
        }
        
        $dados = json_decode($input, true);
        
        if ($dados === null) {
            throw new Exception("Erro ao decodificar JSON: " . json_last_error_msg());
        }
        
        error_log("Dados decodificados: " . json_encode($dados));
        
        // Validar dados básicos
        if (!isset($dados['acao'])) {
            throw new Exception("Ação não especificada");
        }
        
        switch ($dados['acao']) {
            case 'teste_basico':
                echo json_encode([
                    'sucesso' => true,
                    'mensagem' => 'Teste básico funcionando',
                    'usuario_id' => $usuario_id,
                    'timestamp' => date('Y-m-d H:i:s'),
                    'dados_recebidos' => $dados
                ]);
                break;
                
            case 'teste_banco':
                // Testar conexão com banco
                if (!$conexao) {
                    throw new Exception("Conexão com banco não estabelecida");
                }
                
                $query = "SELECT COUNT(*) as total FROM appestudo.lexjus_revisoes WHERE usuario_id = $1";
                $result = pg_query_params($conexao, $query, [$usuario_id]);
                
                if (!$result) {
                    throw new Exception("Erro na query: " . pg_last_error($conexao));
                }
                
                $row = pg_fetch_assoc($result);
                
                echo json_encode([
                    'sucesso' => true,
                    'mensagem' => 'Teste de banco funcionando',
                    'total_revisoes' => (int)$row['total'],
                    'usuario_id' => $usuario_id
                ]);
                break;
                
            case 'teste_lei':
                // Testar obtenção de lei
                if (!isset($dados['lei_codigo'])) {
                    throw new Exception("lei_codigo não fornecido");
                }
                
                $lei_codigo = $dados['lei_codigo'];
                
                // Testar função obterLeiId
                if (!function_exists('obterLeiId')) {
                    throw new Exception("Função obterLeiId não existe");
                }
                
                $lei_id = obterLeiId($conexao, $lei_codigo);
                
                echo json_encode([
                    'sucesso' => true,
                    'mensagem' => 'Teste de lei funcionando',
                    'lei_codigo' => $lei_codigo,
                    'lei_id' => $lei_id
                ]);
                break;
                
            case 'teste_revisao_simples':
                // Testar inserção simples na tabela de revisão
                if (!isset($dados['artigo_numero']) || !isset($dados['lei_codigo'])) {
                    throw new Exception("artigo_numero ou lei_codigo não fornecidos");
                }
                
                $artigo_numero = $dados['artigo_numero'];
                $lei_codigo = $dados['lei_codigo'];
                $lei_id = obterLeiId($conexao, $lei_codigo);
                
                // Verificar se já existe
                $query_check = "
                    SELECT id FROM appestudo.lexjus_revisoes
                    WHERE usuario_id = $1 AND artigo_numero = $2 AND lei_id = $3
                ";
                
                $result_check = pg_query_params($conexao, $query_check, [$usuario_id, $artigo_numero, $lei_id]);
                
                if (pg_num_rows($result_check) > 0) {
                    echo json_encode([
                        'sucesso' => true,
                        'mensagem' => 'Revisão já existe',
                        'acao' => 'ja_existe'
                    ]);
                } else {
                    // Tentar inserir
                    $query_insert = "
                        INSERT INTO appestudo.lexjus_revisoes
                        (usuario_id, artigo_numero, lei_id, status, data_proxima_revisao)
                        VALUES ($1, $2, $3, 'novo', CURRENT_TIMESTAMP)
                        RETURNING id
                    ";
                    
                    $result_insert = pg_query_params($conexao, $query_insert, [$usuario_id, $artigo_numero, $lei_id]);
                    
                    if (!$result_insert) {
                        throw new Exception("Erro ao inserir: " . pg_last_error($conexao));
                    }
                    
                    $row = pg_fetch_assoc($result_insert);
                    
                    echo json_encode([
                        'sucesso' => true,
                        'mensagem' => 'Revisão inserida com sucesso',
                        'revisao_id' => (int)$row['id'],
                        'acao' => 'inserido'
                    ]);
                }
                break;
                
            default:
                throw new Exception("Ação não reconhecida: " . $dados['acao']);
        }
        
    } else {
        // GET - Mostrar informações de debug
        echo json_encode([
            'sucesso' => true,
            'mensagem' => 'Endpoint de teste funcionando',
            'metodo' => $_SERVER['REQUEST_METHOD'],
            'usuario_id' => $usuario_id,
            'php_version' => phpversion(),
            'extensoes' => [
                'json' => extension_loaded('json'),
                'pgsql' => extension_loaded('pgsql')
            ],
            'acoes_disponiveis' => [
                'teste_basico',
                'teste_banco', 
                'teste_lei',
                'teste_revisao_simples'
            ]
        ]);
    }
    
} catch (Exception $e) {
    error_log("ERRO no teste de produção: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    
    http_response_code(500);
    echo json_encode([
        'erro' => $e->getMessage(),
        'arquivo' => $e->getFile(),
        'linha' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
    
} catch (Throwable $t) {
    error_log("ERRO FATAL no teste de produção: " . $t->getMessage());
    
    http_response_code(500);
    echo json_encode([
        'erro_fatal' => $t->getMessage(),
        'arquivo' => $t->getFile(),
        'linha' => $t->getLine()
    ]);
}

if (isset($conexao)) {
    pg_close($conexao);
}
?>
