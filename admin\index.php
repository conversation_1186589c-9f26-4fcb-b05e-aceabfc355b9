<?php
session_start();
require_once('../includes/auth.php');
require_once('../conexao_POST.php');
require_once('../cadastros/includes/verify_admin.php');

// Verifica se é o super admin
verificarSuperAdmin($conexao, false);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Painel Administrativo</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <style>
        :root {
            --primary: #00008B;
            --hover: #0000CD;
            --background: #f5f5f5;
            --card: #ffffff;
        }

        body {
            font-family: 'Quicksand', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: var(--background);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            background-color: var(--card);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            margin: 0;
            color: var(--primary);
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }

        .card {
            background-color: var(--card);
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .card h2 {
            margin-top: 0;
            color: var(--primary);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card p {
            color: #666;
            margin-bottom: 20px;
        }

        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: var(--primary);
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background-color 0.2s;
        }

        .btn:hover {
            background-color: var(--hover);
        }

        .logout-btn {
            background-color: #dc3545;
        }

        .logout-btn:hover {
            background-color: #c82333;
        }

        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-shield-alt"></i> Painel Administrativo</h1>
            <div class="user-info">
                <span>Olá, <?php echo htmlspecialchars($_SESSION['nome']); ?></span>
                <a href="../sair.php" class="btn logout-btn"><i class="fas fa-sign-out-alt"></i> Sair</a>
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h2><i class="fas fa-users"></i> Logs de Login</h2>
                <p>Visualize todos os registros de login dos usuários no sistema.</p>
                <a href="visualizar_logins.php" class="btn">
                    <i class="fas fa-eye"></i> Visualizar Logs
                </a>
            </div>

            <div class="card">
                <h2><i class="fas fa-cogs"></i> Manutenção de Logs</h2>
                <p>Gerencie e monitore os logs do sistema.</p>
                <a href="manutencao_logs.php" class="btn">
                    <i class="fas fa-wrench"></i> Gerenciar Logs
                </a>
            </div>

            <div class="card">
                <h2><i class="fas fa-user-cog"></i> Gerenciar Usuários</h2>
                <p>Administre as contas de usuários do sistema.</p>
                <a href="gerenciar_usuarios.php" class="btn">
                    <i class="fas fa-users-cog"></i> Gerenciar
                </a>
            </div>

            <div class="card">
                <h2><i class="fas fa-chart-line"></i> Estatísticas</h2>
                <p>Visualize estatísticas e métricas do sistema.</p>
                <a href="estatisticas.php" class="btn">
                    <i class="fas fa-chart-bar"></i> Ver Estatísticas
                </a>
            </div>

            <div class="card">
                <h2><i class="fas fa-database"></i> Backup</h2>
                <p>Gerencie backups do banco de dados.</p>
                <a href="backup.php" class="btn">
                    <i class="fas fa-download"></i> Gerenciar Backup
                </a>
            </div>

            <div class="card">
                <h2><i class="fas fa-bell"></i> Notificações</h2>
                <p>Configure e gerencie notificações do sistema.</p>
                <a href="notificacoes.php" class="btn">
                    <i class="fas fa-cog"></i> Configurar
                </a>
            </div>
        </div>
    </div>
</body>
</html>

