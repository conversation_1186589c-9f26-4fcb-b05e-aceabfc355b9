<?php
// Iniciar a sessão no início do arquivo
session_start();

// Incluir arquivo de conexão
include_once("../conexao_POST.php");

// Função para registrar logs de depuração
function log_debug($mensagem) {
    error_log("[DEBUG] " . $mensagem);
}

// Verificar se o usuário está logado
if (!isset($_SESSION['idusuario']) || !isset($_SESSION['validacao']) || $_SESSION['validacao'] !== true) {
    log_debug("Sessão inválida: idusuario=" . (isset($_SESSION['idusuario']) ? $_SESSION['idusuario'] : 'não definido') . 
              ", validacao=" . (isset($_SESSION['validacao']) ? $_SESSION['validacao'] : 'não definida'));
    
    // Redirecionar para a página de login
    echo '<script>
        alert("Sessão expirada ou inválida. Por favor, faça login novamente.");
        window.location.href = "login_index.php";
    </script>';
    exit;
}

// Verificar se os dados do formulário foram enviados
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    log_debug("Método de requisição inválido: " . $_SERVER['REQUEST_METHOD']);
    echo '<script>
        alert("Método de requisição inválido.");
        window.history.back();
    </script>';
    exit;
}

// Verificar se os campos obrigatórios foram enviados
$campos_obrigatorios = ['id_usuario', 'id_planejamento', 'start_date', 'end_date'];
foreach ($campos_obrigatorios as $campo) {
    if (!isset($_POST[$campo]) || empty($_POST[$campo])) {
        log_debug("Campo obrigatório não enviado: " . $campo);
        echo '<script>
            alert("Por favor, preencha todos os campos obrigatórios.");
            window.history.back();
        </script>';
        exit;
    }
}

// Verificar se o ID do usuário enviado corresponde ao ID da sessão
if ($_POST['id_usuario'] != $_SESSION['idusuario']) {
    log_debug("ID do usuário não corresponde: POST=" . $_POST['id_usuario'] . ", SESSION=" . $_SESSION['idusuario']);
    echo '<script>
        alert("Dados de usuário inválidos.");
        window.location.href = "login_index.php";
    </script>';
    exit;
}

// Obter dados do formulário
$id_usuario = $_POST['id_usuario'];
$id_planejamento = $_POST['id_planejamento'];
$data_inicio = $_POST['start_date'];
$data_fim = $_POST['end_date'];
$usar_config_avancada = isset($_POST['usar_config_avancada']) ? true : false;

// Processar os dados com base na configuração escolhida
if ($usar_config_avancada) {
    // Configuração avançada - dias específicos com matérias específicas
    if (!isset($_POST['dias_ativos']) || empty($_POST['dias_ativos'])) {
        log_debug("Nenhum dia ativo selecionado na configuração avançada");
        echo '<script>
            alert("Por favor, selecione pelo menos um dia da semana.");
            window.history.back();
        </script>';
        exit;
    }
    
    $dias_ativos = $_POST['dias_ativos'];
    $materias_por_dia = isset($_POST['materias_dia']) ? $_POST['materias_dia'] : [];
    
    // Verificar se cada dia ativo tem pelo menos uma matéria selecionada
    foreach ($dias_ativos as $dia) {
        if (!isset($materias_por_dia[$dia]) || empty($materias_por_dia[$dia])) {
            log_debug("Dia ativo sem matérias selecionadas: " . $dia);
            echo '<script>
                alert("Por favor, selecione pelo menos uma matéria para cada dia ativo.");
                window.history.back();
            </script>';
            exit;
        }
    }
    
    // Processar a configuração avançada
    log_debug("Processando configuração avançada");
    // Aqui você implementaria a lógica para criar os eventos com base na configuração avançada
    
} else {
    // Configuração básica - dias da semana com número fixo de matérias por dia
    if (!isset($_POST['dias_semana']) || empty($_POST['dias_semana'])) {
        log_debug("Nenhum dia da semana selecionado na configuração básica");
        echo '<script>
            alert("Por favor, selecione pelo menos um dia da semana.");
            window.history.back();
        </script>';
        exit;
    }
    
    if (!isset($_POST['materias_selecionadas']) || empty($_POST['materias_selecionadas'])) {
        log_debug("Nenhuma matéria selecionada na configuração básica");
        echo '<script>
            alert("Por favor, selecione pelo menos uma matéria.");
            window.history.back();
        </script>';
        exit;
    }
    
    $dias_semana = $_POST['dias_semana'];
    $materias_selecionadas = $_POST['materias_selecionadas'];
    $materias_por_dia = isset($_POST['materias_per_day']) ? intval($_POST['materias_per_day']) : 1;
    
    // Processar a configuração básica
    log_debug("Processando configuração básica");
    // Aqui você implementaria a lógica para criar os eventos com base na configuração básica
}

// Implementar a lógica para criar os eventos na agenda
try {
    // Converter as datas para objetos DateTime
    $data_inicio_obj = new DateTime($data_inicio);
    $data_fim_obj = new DateTime($data_fim);

    // Adicionar um dia à data de fim para incluir o último dia
    $data_fim_obj->modify('+1 day');

    // Criar um intervalo de datas
    $intervalo = new DateInterval('P1D');
    $periodo = new DatePeriod($data_inicio_obj, $intervalo, $data_fim_obj);

    // --- Otimização: Preparar para inserção em lote ---
    $eventos_para_inserir = []; // Array para guardar os dados de todos os eventos
    $valores_parametros = []; // Array para guardar todos os valores para pg_query_params
    $placeholder_index = 1; // Contador para os placeholders ($1, $2, ...)
    // --- Fim da preparação ---

    // Percorrer cada dia no intervalo
    foreach ($periodo as $data) {
        $dia_semana = $data->format('w'); // 0 (domingo) a 6 (sábado)
        $data_evento_str = $data->format('Y-m-d');
        // Definir horários fixos ou buscar de outra configuração, se necessário
        $hora_inicio = "08:00:00";
        $hora_fim = "10:00:00";

        $materias_do_dia = []; // Matérias a serem inseridas para este dia específico

        // Verificar se este dia da semana está selecionado e obter matérias
        if ($usar_config_avancada) {
            if (in_array($dia_semana, $dias_ativos) && isset($materias_por_dia[$dia_semana])) {
                $materias_do_dia = $materias_por_dia[$dia_semana];
            }
        } else {
            // Configuração básica
            if (in_array($dia_semana, $dias_semana)) {
                $materias_dia_disponiveis = $materias_selecionadas;
                shuffle($materias_dia_disponiveis);
                $materias_do_dia = array_slice($materias_dia_disponiveis, 0, min($materias_por_dia, count($materias_dia_disponiveis)));
            }
        }

        // Adicionar os eventos deste dia à lista de inserção em lote
        foreach ($materias_do_dia as $materia) {
            $titulo_evento = $materia;
            $detalhes_evento = ""; // Deixando o campo detalhes vazio
            $tipo = "Planejamento";
            $realizado = 'f'; // false

            // Adicionar placeholders para este evento (ex: ($1, $2, $3, $4, $5, $6, $7))
            $placeholders_evento = [];
            for ($i = 0; $i < 7; $i++) {
                $placeholders_evento[] = '$' . $placeholder_index++;
            }
            $eventos_para_inserir[] = '(' . implode(', ', $placeholders_evento) . ')';

            // Adicionar os valores correspondentes ao array de parâmetros
            $valores_parametros[] = $id_usuario;
            $valores_parametros[] = $titulo_evento;
            $valores_parametros[] = $data_evento_str . " " . $hora_inicio;
            $valores_parametros[] = $data_evento_str . " " . $hora_fim;
            $valores_parametros[] = $tipo;
            $valores_parametros[] = $detalhes_evento;
            $valores_parametros[] = $realizado;
        }
    } // Fim do loop foreach ($periodo as $data)

    // --- Executar a inserção em lote ---
    $eventos_criados = 0;
    if (!empty($eventos_para_inserir)) {
        $query_inserir_lote = "INSERT INTO appEstudo.agenda
                                (usuario_idusuario, titulo, data_inicio, data_fim, tipo_evento, detalhes, realizado)
                                VALUES " . implode(', ', $eventos_para_inserir); // Junta os blocos de placeholders: VALUES (...), (...), ...

        log_debug("Executando inserção em lote com " . count($eventos_para_inserir) . " eventos.");
        $resultado_lote = pg_query_params($conexao, $query_inserir_lote, $valores_parametros);

        if ($resultado_lote) {
            // pg_affected_rows pode ser usado para confirmar quantas linhas foram inseridas
            $eventos_criados = pg_affected_rows($resultado_lote);
            log_debug("Inserção em lote bem-sucedida. Eventos criados: " . $eventos_criados);
        } else {
            log_debug("Erro ao inserir eventos em lote: " . pg_last_error($conexao));
            // Lançar uma exceção ou definir uma mensagem de erro específica
            throw new Exception("Erro ao salvar os eventos na agenda.");
        }
    } else {
        log_debug("Nenhum evento para inserir em lote.");
    }
    // --- Fim da execução em lote ---


    // --- Usar Mensagens Flash de Sessão ---
    if ($eventos_criados > 0) {
        $_SESSION['flash_message'] = [
            'status' => 'success',
            'message' => "Calendário preenchido com sucesso! " . $eventos_criados . " eventos criados."
        ];
        
        // Disparar atualização automática da página index.php
        $_SESSION['trigger_index_update'] = true;
    } else {
        $_SESSION['flash_message'] = [
            'status' => 'warning', // Ou 'info'
            'message' => "Nenhum evento foi criado. Verifique as datas e seleções."
        ];
    }

    // Redireciona sem parâmetros GET
    header('Location: index.php');
    exit;

} catch (Exception $e) {
    log_debug("Erro ao processar o calendário: " . $e->getMessage());

    // --- Usar Mensagens Flash de Sessão para Erro ---
    $_SESSION['flash_message'] = [
        'status' => 'danger',
        'message' => "Erro ao processar o calendário: " . htmlspecialchars($e->getMessage())
    ];

    // Redireciona sem parâmetros GET em caso de erro
    header('Location: index.php');
    exit;
}

?>



