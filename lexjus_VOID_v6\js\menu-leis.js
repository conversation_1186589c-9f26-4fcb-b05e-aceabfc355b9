/**
 * Sistema de Menu de Seleção de Leis
 * Gerencia a interface para escolha e troca de leis
 */

class MenuLeis {
    constructor() {
        this.leis = [];
        this.leiAtual = null;
        this.overlay = null;
        this.container = null;
        this.callbacks = {
            onLeiTrocada: null
        };
        
        this.init();
    }
    
    init() {
        this.criarHTML();
        this.bindEventos();
        this.carregarLeiAtual();
    }
    
    /**
     * Cria a estrutura HTML do menu
     */
    criarHTML() {
        // Criar overlay
        this.overlay = document.createElement('div');
        this.overlay.className = 'menu-leis-overlay';
        this.overlay.innerHTML = `
            <div class="menu-leis-container">
                <button class="menu-leis-fechar" title="Fechar">
                    <i class="fas fa-times"></i>
                </button>
                
                <div class="menu-leis-header">
                    <h2><i class="fas fa-books"></i> Escolha sua Lei</h2>
                    <p>Selecione qual lei você deseja estudar</p>
                </div>
                
                <div class="menu-leis-content">
                    <div class="menu-leis-loading">
                        <i class="fas fa-spinner"></i>
                        <p>Carregando leis disponíveis...</p>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(this.overlay);
        this.container = this.overlay.querySelector('.menu-leis-container');
    }
    
    /**
     * Vincula eventos do menu
     */
    bindEventos() {
        // Fechar menu
        this.overlay.querySelector('.menu-leis-fechar').addEventListener('click', () => {
            this.fechar();
        });
        
        // Fechar clicando no overlay
        this.overlay.addEventListener('click', (e) => {
            if (e.target === this.overlay) {
                this.fechar();
            }
        });
        
        // Fechar com ESC
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.overlay.classList.contains('ativo')) {
                this.fechar();
            }
        });
    }
    
    /**
     * Abre o menu de leis
     */
    async abrir() {
        console.log('🔄 Abrindo menu de leis...');
        console.log('Overlay existe:', !!this.overlay);
        console.log('Leis carregadas:', this.leis.length);

        this.overlay.classList.add('ativo');
        document.body.style.overflow = 'hidden';

        console.log('Classe "ativo" adicionada ao overlay');

        // Carregar leis se ainda não carregou
        if (this.leis.length === 0) {
            console.log('Carregando leis...');
            await this.carregarLeis();
        }

        console.log('Renderizando leis...');
        this.renderizarLeis();
        console.log('✅ Menu de leis aberto');
    }
    
    /**
     * Fecha o menu de leis
     */
    fechar() {
        this.overlay.classList.remove('ativo');
        document.body.style.overflow = '';
    }
    
    /**
     * Carrega a lei atual do usuário
     */
    async carregarLeiAtual() {
        try {
            const response = await fetch('api/leis.php?acao=atual');
            const data = await response.json();
            
            if (data.sucesso) {
                this.leiAtual = data.lei_atual.lei_atual;
            }
        } catch (error) {
            console.error('Erro ao carregar lei atual:', error);
        }
    }
    
    /**
     * Carrega todas as leis disponíveis
     */
    async carregarLeis() {
        try {
            const response = await fetch('api/leis.php?acao=listar');
            const data = await response.json();
            
            if (data.sucesso) {
                this.leis = data.leis;
            } else {
                throw new Error(data.erro || 'Erro ao carregar leis');
            }
        } catch (error) {
            console.error('Erro ao carregar leis:', error);
            this.mostrarErro('Erro ao carregar leis disponíveis');
        }
    }
    
    /**
     * Renderiza a lista de leis
     */
    renderizarLeis() {
        const content = this.overlay.querySelector('.menu-leis-content');
        
        if (this.leis.length === 0) {
            content.innerHTML = `
                <div class="menu-leis-loading">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Nenhuma lei disponível</p>
                </div>
            `;
            return;
        }
        
        const leisHTML = this.leis.map((lei, index) => {
            const isAtual = lei.codigo === this.leiAtual;
            const temProgresso = lei.artigos_lidos > 0;
            const percentual = lei.percentual_progresso || 0;
            
            return `
                <div class="lei-card ${isAtual ? 'atual' : ''}" data-codigo="${lei.codigo}" style="animation-delay: ${index * 0.1}s">
                    <div class="lei-card-header">
                        <div class="lei-icone" style="background: ${lei.cor_tema}">
                            <i class="${lei.icone}"></i>
                        </div>
                        <div class="lei-info">
                            <h3>${lei.nome}</h3>
                            <p>${lei.nome_completo}</p>
                        </div>
                    </div>
                    
                    <div class="lei-descricao">
                        ${lei.descricao}
                    </div>
                    
                    <div class="lei-stats">
                        <div class="lei-stat">
                            <span class="lei-stat-numero">${lei.total_artigos}</span>
                            <span class="lei-stat-label">Artigos</span>
                        </div>
                        <div class="lei-stat">
                            <span class="lei-stat-numero">${lei.artigos_lidos}</span>
                            <span class="lei-stat-label">Lidos</span>
                        </div>
                        <div class="lei-stat">
                            <span class="lei-stat-numero">${lei.total_revisoes}</span>
                            <span class="lei-stat-label">Revisões</span>
                        </div>
                    </div>
                    
                    <div class="lei-progresso">
                        <div class="lei-progresso-label">
                            <span>Progresso</span>
                            <span>${percentual}%</span>
                        </div>
                        <div class="lei-progresso-barra">
                            <div class="lei-progresso-preenchimento" style="width: ${percentual}%"></div>
                        </div>
                    </div>
                    
                    <button class="lei-botao ${temProgresso ? 'continuar' : 'estudar'}" 
                            onclick="menuLeis.selecionarLei('${lei.codigo}')">
                        ${isAtual ? 'Estudando Agora' : (temProgresso ? 'Continuar Estudo' : 'Começar a Estudar')}
                    </button>
                </div>
            `;
        }).join('');
        
        content.innerHTML = `
            <div class="leis-grid">
                ${leisHTML}
            </div>
        `;
    }
    
    /**
     * Seleciona uma lei para estudo
     */
    async selecionarLei(codigo) {
        if (codigo === this.leiAtual) {
            this.fechar();
            return;
        }
        
        // Mostrar loading
        const botao = this.overlay.querySelector(`[data-codigo="${codigo}"] .lei-botao`);
        const textoOriginal = botao.textContent;
        botao.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Carregando...';
        botao.disabled = true;
        
        try {
            // Trocar lei no servidor
            const response = await fetch('api/leis.php?acao=trocar', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ codigo })
            });
            
            const data = await response.json();
            
            if (data.sucesso) {
                this.leiAtual = codigo;
                
                // Notificar mudança
                if (this.callbacks.onLeiTrocada) {
                    this.callbacks.onLeiTrocada(codigo);
                }
                
                // Mostrar sucesso
                this.mostrarSucesso(`Lei alterada para ${this.leis.find(l => l.codigo === codigo)?.nome}`);
                
                // Fechar menu após um delay
                setTimeout(() => {
                    this.fechar();
                    // Recarregar página para aplicar mudanças
                    window.location.reload();
                }, 1500);
                
            } else {
                throw new Error(data.erro || 'Erro ao trocar lei');
            }
            
        } catch (error) {
            console.error('Erro ao selecionar lei:', error);
            this.mostrarErro('Erro ao trocar lei');
            
            // Restaurar botão
            botao.textContent = textoOriginal;
            botao.disabled = false;
        }
    }
    
    /**
     * Mostra mensagem de sucesso
     */
    mostrarSucesso(mensagem) {
        this.mostrarNotificacao(mensagem, 'sucesso');
    }
    
    /**
     * Mostra mensagem de erro
     */
    mostrarErro(mensagem) {
        this.mostrarNotificacao(mensagem, 'erro');
    }
    
    /**
     * Mostra notificação
     */
    mostrarNotificacao(mensagem, tipo = 'info') {
        // Criar notificação
        const notificacao = document.createElement('div');
        notificacao.className = `notificacao notificacao-${tipo}`;
        notificacao.innerHTML = `
            <i class="fas fa-${tipo === 'sucesso' ? 'check-circle' : 'exclamation-circle'}"></i>
            <span>${mensagem}</span>
        `;
        
        // Adicionar estilos inline se não existirem
        if (!document.querySelector('#notificacao-styles')) {
            const styles = document.createElement('style');
            styles.id = 'notificacao-styles';
            styles.textContent = `
                .notificacao {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 15px 20px;
                    border-radius: 8px;
                    color: white;
                    font-weight: 500;
                    z-index: 10001;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                    transform: translateX(100%);
                    transition: transform 0.3s ease;
                }
                .notificacao-sucesso { background: #27ae60; }
                .notificacao-erro { background: #e74c3c; }
                .notificacao.mostrar { transform: translateX(0); }
            `;
            document.head.appendChild(styles);
        }
        
        document.body.appendChild(notificacao);
        
        // Animar entrada
        setTimeout(() => notificacao.classList.add('mostrar'), 100);
        
        // Remover após 3 segundos
        setTimeout(() => {
            notificacao.classList.remove('mostrar');
            setTimeout(() => notificacao.remove(), 300);
        }, 3000);
    }
    
    /**
     * Define callback para quando lei é trocada
     */
    onLeiTrocada(callback) {
        this.callbacks.onLeiTrocada = callback;
    }
    
    /**
     * Obtém a lei atual
     */
    getLeiAtual() {
        return this.leiAtual;
    }
    
    /**
     * Obtém informações de uma lei específica
     */
    getLei(codigo) {
        return this.leis.find(lei => lei.codigo === codigo);
    }
}

// Instância global
let menuLeis;

// Inicializar quando DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 Inicializando MenuLeis...');
    menuLeis = new MenuLeis();

    // Disponibilizar globalmente
    window.menuLeis = menuLeis;
    console.log('✅ MenuLeis inicializado e disponibilizado globalmente');
});


