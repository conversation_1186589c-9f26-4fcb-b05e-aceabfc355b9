<?php
include 'processa_index.php';
include 'consulta_banco_ultima_proxima.php';
include_once("conexao_POST.php");

if (!$conexao) {
    die("Erro na conexão: " . pg_last_error());
}

$id_usuario = $_SESSION['idusuario'];

if ($semRegistro === true) {
    echo '
    <h3>Último Estudo</h3>
    <div class="card_ultimo">
        <div class="card-header_ultimo text-center">
            <h4><strong>Não Iniciou o Estudo!</strong></h4>
        </div>
    </div>';
} else {
    echo '
    <div class="card_ultimo">
        <h3>Último Estudo</h3>
        <div class="card-header_ultimo text-center">
            <h4><strong>' . $ultimo_estudo['nome_materia_estudo'] . '</strong></h4>
        </div>
        <div class="card-body_ultimo">
            <h5 class="card-text_ultimo text-center">Último Estudo:<strong> ' . $ultimo_estudo['ponto_estudado'] . '</strong></h5>
            <h5 class="card-text_ultimo text-center">Método:<strong> <b class="text-secondary">' . $ultimo_estudo['metodo'] . '</b></strong></h5>
        </div>
        <div class="card-row_ultimo">
            <div class="card-col_ultimo text-body">
                <h5 class="card-text_ultimo"><strong>Dia da Semana:</strong><b class="text-secondary">' . $dias_semana[$dia_semana] . '</b></h5>
            </div>
            <div class="card-col_ultimo text-end_ultimo">
                <h5 class="card-text_ultimo"><strong>Data: </strong><b class="text-secondary">' . $data_estudo . '</b></h5>
            </div>
        </div>
    </div>';
}
?>

<style>
    /* Estilos de cartão */
    .card_ultimo {

        margin-bottom: 20px;
        background-color: #0DCAF0;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .card-header_ultimo {
        background-color: rgba(69, 146, 222, 0.29);
        color: #2b2723;
        padding: 15px;
        text-align: center;
        font-size: 1.25em;
        border-bottom: 2px solid rgba(69, 146, 222, 0.5); /* Borda inferior para relevo */
        box-shadow: inset 0 -3px 3px rgba(0, 0, 0, 0.1); /* Sombra interna para relevo */
    }

    .card-body_ultimo {
        padding: 20px;
        background-color: #0DCAF0;
        overflow-y: auto; /* Adicionar rolagem se o conteúdo ultrapassar a altura */
    }

    .card-text_ultimo {
        margin-bottom: 10px;
        color: #343a40;
    }

    .card-row_ultimo {
        display: flex;
        justify-content: space-between;
        padding: 10px 20px;
    }

    .card-col_ultimo {
        flex: 1;
        padding: 5px;
    }

    .text-center {
        text-align: center;
    }

    .text-end_ultimo {
        text-align: right;
        margin-left: auto; /* Adiciona margem automática à esquerda para empurrar o conteúdo para a extrema direita */
    }
</style>
