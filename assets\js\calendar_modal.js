// calendar_modal_final.js
document.addEventListener('DOMContentLoaded', function() {
  console.log('Calendar modal script initializing...');
  
  // Seletor para os dias do calendário
  const diasItems = document.querySelectorAll('.dia-item');
  console.log(`Found ${diasItems.length} calendar days`);
  
  // Adicionar evento de clique a todos os dias do calendário
  diasItems.forEach(function(diaItem, index) {
      const title = diaItem.getAttribute('title');
      const hasEstudo = diaItem.classList.contains('estudou');
      
      // Adiciona o evento de clique no dia
      diaItem.addEventListener('click', function(e) {
          e.preventDefault();
          
          // Pegar o título diretamente para a data
          const title = this.getAttribute('title');
          console.log(`Clicked on day with title: ${title}, has study: ${this.classList.contains('estudou')}`);
          
          // Verificar se o título está no formato esperado (DD/MM/YYYY)
          if (!title || !/^\d{2}\/\d{2}\/\d{4}$/.test(title)) {
              console.error('Error: title is not in expected format DD/MM/YYYY');
              return;
          }
          
          // Converter de DD/MM/YYYY para YYYY-MM-DD
          const [day, month, year] = title.split('/');
          const formattedDate = `${year}-${month}-${day}`;
          console.log(`Converted date: ${formattedDate}`);
          
          if (this.classList.contains('estudou')) {
              obterDetalhesEstudo(formattedDate);
          } else {
              exibirMensagemSemEstudo(formattedDate);
          }
      });
      
      // Também adiciona o evento aos elementos filhos
      Array.from(diaItem.children).forEach(child => {
          child.addEventListener('click', function(e) {
              e.stopPropagation();
              diaItem.click();
          });
      });
  });
  
  // Adicionar evento de teclado para fechar modal com ESC
  document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
          const modal = document.getElementById('modalEstudoDia');
          if (modal) {
              console.log('Escape key pressed, closing modal');
              document.body.removeChild(modal);
          }
      }
  });
});

// Função para obter os detalhes do estudo para uma data específica
function obterDetalhesEstudo(data) {
  console.log('Data para buscar estudos:', data);
  
  // Validar o formato da data (YYYY-MM-DD)
  if (!data || !/^\d{4}-\d{2}-\d{2}$/.test(data)) {
      console.error('Formato de data inválido:', data);
      criarModal('Erro', '<div class="error-message">Formato de data inválido: ' + data + '</div>');
      return;
  }
  
  // Mostrar indicador de carregamento
  criarModal('Carregando...', '<div class="loading-spinner"><i class="fas fa-spinner fa-spin"></i>Buscando dados do estudo...</div>');
  
  // Fazer a requisição AJAX para obter os dados
  fetch('obter_estudo_data.php?data=' + encodeURIComponent(data))
      .then(response => {
          if (!response.ok) {
              throw new Error('Erro na resposta: ' + response.statusText);
          }
          return response.json();
      })
      .then(data => {
          if (data.erro) {
              throw new Error(data.erro);
          }
          exibirModalDetalhes(data);
      })
      .catch(error => {
          console.error('Erro ao obter detalhes:', error);
          criarModal('Erro', '<div class="error-message">Erro ao carregar os detalhes do estudo: ' + error.message + '</div>');
      });
}

// Função para criar o conteúdo do modal com os detalhes do estudo
function exibirModalDetalhes(data) {
  const dataFormatada = formatarData(data.data);
  let conteudoHTML = `
      <div class="modal-header-estudo">
          <h3>Estudos do dia ${dataFormatada}</h3>
          <div class="tempo-total">
              <span class="tempo-label">Tempo Total:</span>
              <span class="tempo-valor">${data.tempo_total_formatado}</span>
          </div>
      </div>
      <div class="estudos-container">
  `;
  
  if (data.estudos.length === 0) {
      conteudoHTML += '<div class="sem-estudos">Nenhum estudo registrado para esta data.</div>';
  } else {
      data.estudos.forEach(estudo => {
          conteudoHTML += `
              <div class="estudo-item" style="border-left: 4px solid ${estudo.cor}">
                  <div class="estudo-cabecalho">
                      <h4 class="materia-nome" style="color: ${estudo.cor}">${estudo.nome_materia}</h4>
                      <div class="estudo-tempo">${estudo.tempo_estudo}</div>
                  </div>
                  <div class="estudo-detalhes">
                      <div class="detalhe-linha"><strong>Curso:</strong> ${estudo.nome_curso}</div>
                      <div class="detalhe-linha"><strong>Método:</strong> ${estudo.metodo}</div>
                      <div class="detalhe-linha"><strong>Horário:</strong> ${estudo.hora_inicio} - ${estudo.hora_fim}</div>
                      <div class="detalhe-linha"><strong>Tempo Bruto:</strong> ${estudo.tempo_bruto}</div>
                      <div class="detalhe-linha"><strong>Tempo Perdido:</strong> ${estudo.tempo_perdido}</div>
                      <div class="detalhe-linha ponto-estudado"><strong>Ponto Estudado:</strong> ${estudo.ponto_estudado}</div>
                      ${estudo.descricao ? `<div class="detalhe-linha descricao"><strong>Observações:</strong> ${estudo.descricao}</div>` : ''}
                  </div>
              </div>
          `;
      });
  }
  
  conteudoHTML += `</div>`;
  
  // Adicionar avaliação de meta diária
  if (data.tempo_planejado_segundos > 0) {
      const porcentagemMeta = (data.tempo_total_segundos / data.tempo_planejado_segundos) * 100;
      const statusMeta = porcentagemMeta >= 100 ? 'completada' : 'pendente';
      const mensagemMeta = porcentagemMeta >= 100 
          ? 'Meta diária atingida! 🎉' 
          : `Meta diária: ${data.tempo_planejado} (${porcentagemMeta.toFixed(1)}% concluído)`;
      
      conteudoHTML += `
          <div class="meta-diaria ${statusMeta}">
              <i class="fas ${porcentagemMeta >= 100 ? 'fa-check-circle' : 'fa-clock'}"></i>
              ${mensagemMeta}
          </div>
      `;
  }
  
  criarModal(dataFormatada, conteudoHTML);
}

// Função para exibir mensagem quando não há estudo na data
function exibirMensagemSemEstudo(data) {
  const dataFormatada = formatarData(data);
  const conteudoHTML = `
      <div class="sem-estudo-mensagem">
          <i class="fas fa-calendar-times"></i>
          <p>Nenhum estudo registrado para o dia ${dataFormatada}.</p>
      </div>
  `;
  
  criarModal(dataFormatada, conteudoHTML);
}

// Função para criar ou atualizar o modal com eventos de fechamento diretos
function criarModal(titulo, conteudo) {
  // Remover modal existente se houver
  const modalExistente = document.getElementById('modalEstudoDia');
  if (modalExistente) {
      document.body.removeChild(modalExistente);
  }
  
  // Criar novo modal
  const modalHTML = `
      <div id="modalEstudoDia" class="modal-estudo ativo">
          <div class="modal-overlay"></div>
          <div class="modal-content">
              <button class="modal-close">
                  <i class="fas fa-times"></i>
              </button>
              <div class="modal-body">
                  <h3 class="modal-titulo">${titulo}</h3>
                  <div class="modal-conteudo">
                      ${conteudo}
                  </div>
              </div>
          </div>
      </div>
  `;
  
  document.body.insertAdjacentHTML('beforeend', modalHTML);
  console.log('Modal criado');
  
  // Adicionar evento de clique direto para fechar o modal
  const fecharBotao = document.querySelector('#modalEstudoDia .modal-close');
  if (fecharBotao) {
      fecharBotao.onclick = function() {
          console.log('Botão de fechar clicado diretamente');
          const modal = document.getElementById('modalEstudoDia');
          if (modal) {
              document.body.removeChild(modal);
              console.log('Modal removido pelo botão fechar');
          }
          return false;
      };
  }
  
  const overlay = document.querySelector('#modalEstudoDia .modal-overlay');
  if (overlay) {
      overlay.onclick = function() {
          console.log('Overlay clicado diretamente');
          const modal = document.getElementById('modalEstudoDia');
          if (modal) {
              document.body.removeChild(modal);
              console.log('Modal removido pelo overlay');
          }
          return false;
      };
  }
}

// Função para formatar a data
function formatarData(dataStr) {
  console.log('String de data recebida:', dataStr);
  
  // Verificar se a data está no formato ISO (YYYY-MM-DD)
  if (typeof dataStr === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dataStr)) {
      const [ano, mes, dia] = dataStr.split('-').map(Number);
      return `${dia.toString().padStart(2, '0')}/${mes.toString().padStart(2, '0')}/${ano}`;
  } 
  
  // Fallback para o método padrão
  try {
      const data = new Date(dataStr);
      if (isNaN(data.getTime())) {
          throw new Error('Data inválida');
      }
      return data.toLocaleDateString('pt-BR', {
          day: '2-digit',
          month: '2-digit',
          year: 'numeric'
      });
  } catch (e) {
      console.error('Erro ao formatar data:', e);
      return 'Data inválida';
  }
}