<?php
session_start();
include_once("conexao_POST.php");

if (!$conexao) {
    die("Erro na conexão: " . pg_last_error());
}

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $id_planejamento = $_POST['id_planejamento'];
    $novo_nome = $_POST['nome'];
    $nova_data_inicio = $_POST['data_inicio'];
    $nova_data_fim = $_POST['data_fim'];
    $novo_tempo_planejamento = $_POST['tempo_planejamento'];
    $materias_selecionadas = isset($_POST['materias']) ? $_POST['materias'] : [];

    if ($nova_data_inicio > $nova_data_fim) {
        $_SESSION['error_msg'] = "A data de início não pode ser maior que a data de término.";
        header("Location: 0editar_planejamento.php");
        exit;
    } else {
        $query_atualizar_planejamento = "UPDATE appEstudo.planejamento 
            SET nome = '$novo_nome', 
                data_inicio = '$nova_data_inicio', 
                data_fim = '$nova_data_fim', 
                tempo_planejamento = '$novo_tempo_planejamento' 
            WHERE idplanejamento = $id_planejamento";

        $resultado_atualizar_planejamento = pg_query($conexao, $query_atualizar_planejamento);
        $query_remover_relacionamentos = "DELETE FROM appEstudo.planejamento_materia 
            WHERE planejamento_idplanejamento = $id_planejamento";

        $resultado_remover_relacionamentos = pg_query($conexao, $query_remover_relacionamentos);
        $sucesso_relacionamentos = true;

        foreach ($materias_selecionadas as $id_materia) {
            $query_adicionar_relacionamento = "INSERT INTO appEstudo.planejamento_materia 
                (planejamento_idplanejamento, materia_idmateria) 
                VALUES ($id_planejamento, $id_materia)";

            $resultado = pg_query($conexao, $query_adicionar_relacionamento);
            if (!$resultado) {
                $sucesso_relacionamentos = false;
                break;
            }
        }

        if ($resultado_atualizar_planejamento && $resultado_remover_relacionamentos && $sucesso_relacionamentos) {
            ?>
            <!DOCTYPE html>
            <html lang="pt-br">
            <head>
                <meta charset="UTF-8">
                <title>Sucesso</title>
                <style>
                    :root {
                        --parchment: #f8f0e3;
                        --vintage-gold: #b8860b;
                        --burgundy: #800020;
                        --gold-accent: #daa520;
                        --shadow-color: rgba(0, 0, 0, 0.2);
                        --elegant-border: linear-gradient(45deg, var(--vintage-gold), #ffd700, var(--vintage-gold));
                    }

                    body {
                        margin: 0;
                        padding: 0;
                        font-family: 'Quicksand', sans-serif;
                        background: rgba(0, 0, 0, 0.5);
                    }

                    .modal-overlay {
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, 0.5);
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        z-index: 1000;
                        backdrop-filter: blur(3px);
                    }

                    .modal-content {
                        background: var(--hover);
                        padding: 2rem;
                        border: 1px solid var(--vintage-gold);
                        box-shadow:
                                0 0 0 1px var(--vintage-gold),
                                0 0 0 10px var(--parchment),
                                0 0 0 11px var(--vintage-gold),
                                0 2px 5px 11px var(--shadow-color);
                        max-width: 400px;
                        width: 90%;
                        position: relative;
                        animation: slideIn 0.3s ease-out;
                    }

                    .modal-corner {
                        position: absolute;
                        width: 20px;
                        height: 20px;
                        border: 1px solid var(--vintage-gold);
                        pointer-events: none;
                    }

                    .modal-corner-tl { top: 10px; left: 10px; border-right: 0; border-bottom: 0; }
                    .modal-corner-tr { top: 10px; right: 10px; border-left: 0; border-bottom: 0; }
                    .modal-corner-bl { bottom: 10px; left: 10px; border-right: 0; border-top: 0; }
                    .modal-corner-br { bottom: 10px; right: 10px; border-left: 0; border-top: 0; }

                    .modal-title {
                        font-family: 'Cinzel', serif;
                        color: var(--burgundy);
                        text-align: center;
                        font-size: 1.5rem;
                        margin-bottom: 1.5rem;
                        padding-bottom: 1rem;
                        position: relative;
                    }

                    .modal-title::after {
                        content: '';
                        position: absolute;
                        bottom: 0;
                        left: 50%;
                        transform: translateX(-50%);
                        width: 100px;
                        height: 2px;
                        background: var(--elegant-border);
                    }

                    .modal-message {
                        text-align: center;
                        font-size: 1.1rem;
                        margin-bottom: 2rem;
                        line-height: 1.6;
                    }

                    .btn-close {
                        font-family: 'Cinzel', serif;
                        background: transparent;
                        border: 2px solid #8B0000;
                        color: #8B0000;
                        padding: 1rem 2rem;
                        font-size: 1.1rem;
                        text-transform: uppercase;
                        letter-spacing: 2px;
                        cursor: pointer;
                        position: relative;
                        overflow: hidden;
                        z-index: 1;
                        transition: all 0.3s ease;
                    }

                    .btn-close:hover {
                        background: #8B0000;
                        color: white;
                    }

                    @keyframes slideIn {
                        from { transform: translateY(-20px); opacity: 0; }
                        to { transform: translateY(0); opacity: 1; }
                    }
                </style>
            </head>
            <body>
            <div class="modal-overlay">
                <div class="modal-content">
                    <div class="modal-corner modal-corner-tl"></div>
                    <div class="modal-corner modal-corner-tr"></div>
                    <div class="modal-corner modal-corner-bl"></div>
                    <div class="modal-corner modal-corner-br"></div>

                    <h3 class="modal-title">Sucesso!</h3>
                    <p class="modal-message">As alterações foram salvas com sucesso.</p>

                    <div style="text-align: center;">
                        <button class="btn-close" onclick="redirecionarParaEdicao()">Fechar</button>
                    </div>
                </div>
            </div>

            <script>
                function redirecionarParaEdicao() {
                    // Atualiza a página principal se existir
                    if (window.opener) {
                        window.opener.location.reload();
                    }
                    // Redireciona para a página de edição
                    window.location.href = '0editar_planejamento.php';
                }
            </script>
            </body>
            </html>
            <?php
        } else {
            $_SESSION['error_msg'] = "Erro ao salvar as alterações.";
            header("Location: 0editar_planejamento.php");
        }
    }
}

pg_close($conexao);
?>