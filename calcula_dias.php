<?php
include 'processa_dias.php';

$url_anterior = 'calcula_dias.php';
$url_frente = 'calcula_dias.php';
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <title>Hora Líquida por Dia</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
<body>
<center>
    <table border="1" id="tabela-dias">


        <button type="button" onclick="alterarTipoGrafico()">Alterar Tipo de Gráfico</button>

        <script>
            function carregarDados(acao, contador) {
                $.ajax({
                    type: "POST",
                    url: "processa_dias.php",
                    data: { acao: acao, contador: contador },
                    success: function(response) {
                        var dados = JSON.parse(response);
                        atualizarTabela(dados.temposEstudados);
                        atualizarGrafico(dados.datas, dados.tempos);
                    }
                });
            }

            function atualizarTabela(temposEstudados) {
                var tabela = $("#tabela-dias");
                tabela.find("tr:gt(0)").remove(); // Remove todas as linhas, exceto a primeira (cabeçalho)

                $.each(temposEstudados, function(index, tempo) {
                    var dataFormatada = new Date(tempo.data).toLocaleDateString("pt-BR");
                    var diaSemana = new Date(tempo.data).toLocaleDateString("pt-BR", { weekday: 'long' });
                    var horas = Math.floor(tempo.tempo / 3600);
                    var minutos = Math.floor((tempo.tempo % 3600) / 60);

                    tabela.append("<tr><td>" + dataFormatada + "</td><td>" + diaSemana + "</td><td>" + horas + " horas e " + minutos + " minutos</td></tr>");
                });
            }

            function atualizarGrafico(datas, tempos) {
                myChart.data.labels = datas;
                myChart.data.datasets[0].data = tempos;
                myChart.update();
            }

            function alterarTipoGrafico() {
                var tipoAtual = myChart.config.type;
                var novoTipo = tipoAtual === 'line' ? 'bar' : 'line';

                myChart.config.type = novoTipo;
                myChart.update();
            }
        </script>

        <?php
       // echo $contador;

        $diasDaSemana = array(
            'Sun' => 'Domingo',
            'Mon' => 'Segunda-feira',
            'Tue' => 'Terça-feira',
            'Wed' => 'Quarta-feira',
            'Thu' => 'Quinta-feira',
            'Fri' => 'Sexta-feira',
            'Sat' => 'Sábado'
        );

        $datas = [];
        $tempos = [];

        foreach ($tempoEstudadoPorDia as $data => $tempo) {
            $dataFormatada = date('d/m/Y', strtotime($data));
            $diaSemana = $diasDaSemana[date('D', strtotime($data))];
            $horas = floor($tempo / 3600);
            $minutos = floor(($tempo % 3600) / 60);

           // echo "<tr>";
            //echo "<td>$dataFormatada</td>";
            //echo "<td>$diaSemana</td>";
            //echo "<td>$horas horas e $minutos minutos</td>";
            //echo "</tr>";

            $datas[] = "$dataFormatada ($diaSemana)";
            $tempos[] = $tempo;
        }

        // Cálculo da média de horas estudadas
        $numeroDias = count($tempos);
        $totalSegundos = array_sum($tempos);
        $mediaHorasEstudadas = $numeroDias > 0 ? $totalSegundos / ($numeroDias * 3600) : 0;
        $mediaHoras = floor($mediaHorasEstudadas);
        $mediaMinutos = floor(($mediaHorasEstudadas - $mediaHoras) * 60);
        ?>
    </table>
</center>

<canvas id="myChart" style="max-width: 2000px; max-height: 650px;"></canvas>

<script>
    var ctx = document.getElementById('myChart').getContext('2d');
    var datas = <?php echo json_encode($datas); ?>;
    var tempos = <?php echo json_encode($tempos); ?>;
    var mediaHoras = <?php echo $mediaHoras; ?>;
    var mediaMinutos = <?php echo $mediaMinutos; ?>;

    var tempoPlanejamento = <?php echo $tempoPlanejamento ?>;
    var metadeTempoPlanejamentoSegundos = tempoPlanejamento / 2;

    var duracaoTotal = tempos.reduce((total, tempo) => total + tempo, 0);
    var duracaoTotalEmHoras = Math.floor(duracaoTotal / 3600);
    var duracaoTotalEmMinutos = Math.floor((duracaoTotal % 3600) / 60);

    // Configuração da fonte para o gráfico
    Chart.defaults.font.family = '"Courier Prime", monospace';

    var backgroundColors = tempos.map(tempo => {
        if (tempo < metadeTempoPlanejamentoSegundos) { // 2 horas e 30 minutos em segundos
            return '#f2aaaa';
        } else if (tempo < tempoPlanejamento) { // 2 horas e 59 minutos em segundos
            return '#f7e982';
        } else {
            return '#93f77f';
        }
    });

    var myChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: datas,
            datasets: [{
                label: 'Tempo Estudado por Dia',
                data: tempos,
                backgroundColor: backgroundColors,
                borderColor: 'black',
                pointBackgroundColor: backgroundColors,
                pointRadius: 12,
                borderWidth: 2,
            }]
        },
        options: {
            plugins: {
                title: {
                    display: true,
                    text: 'Duração Total: ' + duracaoTotalEmHoras + 'h ' + (duracaoTotalEmMinutos < 10 ? '0' : '') + duracaoTotalEmMinutos + 'min | Tempo Médio de Estudo: ' + mediaHoras + 'h ' + (mediaMinutos < 10 ? '0' : '') + mediaMinutos + 'min',
                    position: 'bottom',
                    font: {
                        family: '"Courier Prime", monospace',
                        size: 16
                    },
                    color: '#333'
                },
                legend: {
                    display: false
                },
                tooltip: {
                    callbacks: {
                        label: function (context) {
                            var horas = Math.floor(context.raw / 3600);
                            var minutos = Math.floor((context.raw % 3600) / 60);
                            return horas + 'h ' + (minutos < 10 ? '0' : '') + minutos + 'm';
                        }
                    },
                    title: {
                        font: {
                            family: '"Courier Prime", monospace'
                        }
                    },
                    body: {
                        font: {
                            family: '"Courier Prime", monospace'
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function (value) {
                            var horas = Math.floor(value / 3600);
                            var minutos = Math.floor((value % 3600) / 60);
                            return horas + ':' + (minutos < 10 ? '0' : '') + minutos;
                        },
                        font: {
                            family: '"Courier Prime", monospace'
                        }
                    },
                    title: {
                        display: true,
                        text: 'Tempo (Horas:Minutos)',
                        font: {
                            family: '"Courier Prime", monospace'
                        }
                    }
                },
                x: {
                    ticks: {
                        font: {
                            family: '"Courier Prime", monospace'
                        }
                    }
                }
            }
        }
    });

    function alterarTipoGrafico() {
        var tipoAtual = myChart.config.type;
        var novoTipo = tipoAtual === 'line' ? 'bar' : 'line';

        myChart.config.type = novoTipo;
        myChart.update();
    }
</script>
</body>
</html>
