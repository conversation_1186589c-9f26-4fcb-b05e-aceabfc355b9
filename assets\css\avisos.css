  .aviso-item {
    padding: 10px;
    margin-bottom: 10px;
    border-left: 3px solid #ccc;
    background-color: #f9f9f9;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.aviso-item:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.aviso-data {
    display: block;
    font-size: 0.8rem;
    color: #777;
    margin-bottom: 3px;
}

.aviso-importante {
    border-left-color: #e74c3c;
    background-color: #fdedec;
    color: var(--card-border);
}

.aviso-destaque {
    border-left-color: #3498db;
    background-color: #ebf5fb;
    color: var(--card-border);
}

.aviso-alerta {
    border-left-color: #f39c12;
    background-color: #fef5e7;
    color: var(--card-border);
}

.aviso-item p {
    margin: 0;
    line-height: 1.4;
}

.aviso-item strong {
    font-weight: 600;
}

/* Animação de entrada para novos avisos */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.aviso-item {
    animation: slideIn 0.3s ease-out forwards;
}

/* Estilo para ícones */
.aviso-item i {
    margin-right: 8px;
}

/* Responsividade */
@media (max-width: 768px) {
    .card-content_planejamento .card-content_planejamento_agenda{
        padding: 10px;
    }
    
    .aviso-item {
        padding: 8px;
    }
}
