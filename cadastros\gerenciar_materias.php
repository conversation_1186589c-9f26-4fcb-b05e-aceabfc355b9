<?php
session_start();
require_once('../includes/auth.php');
require_once('../conexao_POST.php');
require_once('includes/verify_admin.php');

// Verifica se é admin
verificarAcessoAdmin($conexao, false);

// Consultar matérias
$query_materias = "SELECT * FROM appestudo.materia ORDER BY nome";
$resultado_materias = pg_query($conexao, $query_materias);

?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Matérias</title>
    <link href="https://fonts.googleapis.com/css2?family=Varela+Round&family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert2/11.7.32/sweetalert2.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert2/11.7.32/sweetalert2.all.min.js"></script>
    <style>
        :root {
            --primary: #00008B;
            --primary-light: #3949ab;
            --secondary: #f8f0e3;
            --accent: #4a4a4a;
            --border: #e0e0e0;
            --text: #2c3e50;
            --active: #00008B;
            --hover: #f5f5f5;
            --primary-blue: #00008B;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --background: #f5f5f5;
            --card-background: white;
            --gradient-start: #00008B;
            --gradient-end: #0000CD;
        }

        [data-theme="dark"] {
            --primary: #4169E1;
            --secondary: #1a1a2e;
            --accent: #6c7293;
            --border: #2d2d42;
            --text: #e4e6f0;
            --active: #4169E1;
            --hover: #232338;
            --primary-blue: #4169E1;
            --shadow-color: rgba(0, 0, 0, 0.3);
            --background: #0a0a1f;
            --card-background: #13132b;
            --gradient-start: #4169E1;
            --gradient-end: #6495ED;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background: var(--background);
            color: var(--text);
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            padding: 20px;
            box-shadow: 0 4px 12px var(--shadow-color);
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
            z-index: 1;
        }

        .header-left {
            position: relative;
            z-index: 2;
            display: flex;
            align-items: center;
        }

        .theme-toggle {
            cursor: pointer;
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }

        .theme-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            cursor: pointer;
            padding: 12px;
            border-radius: 50%;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .theme-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .header_centro {
            text-align: center;
            margin: 30px 0;
            position: relative;
        }

        .header_centro h1 {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: 20px;
            font-weight: 700;
            text-shadow: 2px 2px 4px var(--shadow-color);
        }

        .menu-icon {
            color: var(--primary);
            margin-bottom: 15px;
        }

        .menu-icon i {
            font-size: 4rem;
            color: var(--primary);
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            filter: drop-shadow(2px 2px 4px var(--shadow-color));
        }

        .btn-voltar {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.3s ease;
            min-width: 120px;
            background-color: var(--primary);
            color: white;
            text-decoration: none;
        }

        .btn-voltar:hover {
            background-color: var(--primary);
            transform: translateY(-2px);
            text-decoration: none;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 12px 24px;
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-family: 'Quicksand', sans-serif;
            font-weight: 600;
            text-align: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            margin: 5px;
            box-shadow: 0 4px 12px var(--shadow-color);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px var(--shadow-color);
            opacity: 0.9;
        }

        .btn i {
            font-size: 1.1rem;
        }

        .table {
            background: var(--card-background);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 16px var(--shadow-color);
            margin-top: 20px;
        }

        .table thead {
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            color: white;
        }

        .table th {
            font-weight: 600;
            padding: 15px;
            border: none;
        }

        .table td {
            padding: 15px;
            border-bottom: 1px solid var(--border);
            color: var(--text);
        }

        .table tbody tr:hover {
            background: var(--hover);
        }

        .color-preview {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 10px;
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1050;
            overflow-x: hidden;
            overflow-y: auto;
            outline: 0;
        }

        .modal.fade {
            opacity: 0;
            transition: opacity 0.15s linear;
        }

        .modal.show {
            display: block;
            opacity: 1;
        }

        .modal-dialog {
            position: relative;
            width: auto;
            margin: 1.75rem auto;
            max-width: 500px;
            pointer-events: none;
        }

        .modal.show .modal-dialog {
            transform: none;
            transition: transform 0.3s ease-out;
        }

        .modal-content {
            position: relative;
            display: flex;
            flex-direction: column;
            width: 100%;
            pointer-events: auto;
            background-color: var(--card-background);
            border-radius: 15px;
            box-shadow: 0 8px 16px var(--shadow-color);
        }

        .modal-header {
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            color: white;
            border-radius: 15px 15px 0 0;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .modal-title {
            color: white;
            font-weight: 600;
            margin: 0;
            font-size: 1.25rem;
        }

        .modal-body {
            padding: 20px;
            color: var(--text);
        }

        .modal-footer {
            border-top: 1px solid var(--border);
            padding: 20px;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--border);
            border-radius: 12px;
            background: var(--card-background);
            color: var(--text);
            font-size: 1rem;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }

        .form-control:focus {
            border-color: var(--primary);
            outline: none;
            box-shadow: 0 0 0 4px rgba(0, 0, 139, 0.1);
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            color: var(--text);
            font-weight: 600;
            font-size: 0.95rem;
        }

        .btn-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            padding: 0;
            margin: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            filter: brightness(0) invert(1);
            opacity: 0.8;
        }

        .btn-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: rotate(90deg);
            opacity: 1;
        }

        .btn-close:focus {
            box-shadow: none;
            outline: none;
        }

        .btn-close::before {
            content: "×";
            font-size: 24px;
            line-height: 1;
            color: white;
        }

        .logo img {
            height: 50px;
            filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.2));
        }

        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 1040;
            width: 100vw;
            height: 100vh;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .modal-backdrop.fade {
            opacity: 0;
        }

        .modal-backdrop.show {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header {
                padding: 15px;
            }
            
            .header_centro h1 {
                font-size: 2rem;
            }
            
            .menu-icon i {
                font-size: 3rem;
            }
            
            .table {
                font-size: 0.9rem;
            }
            
            .btn {
                padding: 10px 20px;
            }
            
            .theme-toggle {
                top: 15px;
                right: 15px;
            }
            
            .theme-btn {
                padding: 10px;
            }
            
            .modal-dialog {
                width: 95%;
                margin: 10px;
            }
        }

        .color-picker-container {
            display: flex;
            align-items: center;
            gap: 15px;
            background: var(--hover);
            padding: 15px;
            border-radius: 12px;
            margin-bottom: 15px;
        }

        .color-preview-large {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            box-shadow: 0 4px 8px var(--shadow-color);
            border: 2px solid var(--border);
        }

        .color-info {
            flex: 1;
        }

        .color-info label {
            display: block;
            margin-bottom: 5px;
            color: var(--text);
            font-weight: 600;
        }

        .color-value {
            font-family: monospace;
            padding: 5px 10px;
            background: var(--card-background);
            border-radius: 6px;
            border: 1px solid var(--border);
            color: var(--text);
        }

        input[type="color"] {
            -webkit-appearance: none;
            width: 100%;
            height: 40px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            background: none;
        }

        input[type="color"]::-webkit-color-swatch-wrapper {
            padding: 0;
        }

        input[type="color"]::-webkit-color-swatch {
            border: 2px solid var(--border);
            border-radius: 8px;
            box-shadow: 0 2px 4px var(--shadow-color);
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <div class="logo">
                <img src="../logo/logo_vertical.png" alt="Logo">
            </div>
        </div>
    </div>

    <div class="theme-toggle">
        <button id="theme-toggle-btn" class="theme-btn">
            <i id="theme-icon" class="fas fa-moon"></i>
        </button>
    </div>

    <div class="container">
        <!-- Botão Voltar -->
        <div style="margin: 20px 0;">
            <a href="index.php" class="btn-voltar">
                <i class="fas fa-arrow-left"></i> Voltar
            </a>
        </div>

        <div class="header_centro">
            <div class="menu-icon">
                <i class="fas fa-book fa-3x"></i>
            </div>
            <h1>Gerenciar Matérias</h1>
        </div>
        
        <!-- Botão para adicionar nova matéria -->
        <button type="button" class="btn" data-bs-toggle="modal" data-bs-target="#addMateriaModal">
            <i class="fas fa-plus"></i> Nova Matéria
        </button>

        <!-- Tabela de matérias -->
        <table class="table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Nome</th>
                    <th>Cor</th>
                    <th>Ações</th>
                </tr>
            </thead>
            <tbody>
                <?php while ($materia = pg_fetch_assoc($resultado_materias)): ?>
                    <tr>
                        <td><?= htmlspecialchars($materia['idmateria']) ?></td>
                        <td><?= htmlspecialchars($materia['nome']) ?></td>
                        <td>
                            <span class="color-preview" style="background-color: <?= htmlspecialchars($materia['cor']) ?>"></span>
                            <?= htmlspecialchars($materia['cor']) ?>
                        </td>
                        <td>
                            <button class="btn" onclick="editarMateria(<?= $materia['idmateria'] ?>, '<?= $materia['nome'] ?>', '<?= $materia['cor'] ?>')" data-bs-toggle="modal" data-bs-target="#editMateriaModal">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn" style="background: linear-gradient(135deg, #dc3545, #c82333);" onclick="confirmarExclusao(<?= $materia['idmateria'] ?>, '<?= addslashes($materia['nome']) ?>')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                <?php endwhile; ?>
            </tbody>
        </table>

        <!-- Modal Adicionar Matéria -->
        <div class="modal fade" id="addMateriaModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Nova Matéria</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                    </div>
                    <form action="processar_materia.php" method="POST">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="nome" class="form-label">Nome da Matéria</label>
                                <input type="text" class="form-control" id="nome" name="nome" required>
                            </div>
                            <div class="mb-3">
                                <label for="cor" class="form-label">Cor</label>
                                <input type="color" class="form-control" id="cor" name="cor" required>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn" style="background: linear-gradient(135deg, #6c757d, #495057);" data-bs-dismiss="modal">Cancelar</button>
                            <button type="submit" name="acao" value="adicionar" class="btn">Salvar</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Modal Editar Matéria -->
        <div class="modal fade" id="editMateriaModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Editar Matéria</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                    </div>
                    <form action="processar_materia.php" method="POST">
                        <input type="hidden" id="edit_id" name="id">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label for="edit_nome" class="form-label">Nome da Matéria</label>
                                <input type="text" class="form-control" id="edit_nome" name="nome" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Cor da Matéria</label>
                                <div class="color-picker-container">
                                    <div class="color-preview-large" id="colorPreview"></div>
                                    <div class="color-info">
                                        <label>Cor Atual</label>
                                        <div class="color-value" id="colorValue"></div>
                                    </div>
                                </div>
                                <input type="color" class="form-control" id="edit_cor" name="cor" required>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn" style="background: linear-gradient(135deg, #6c757d, #495057);" data-bs-dismiss="modal">Cancelar</button>
                            <button type="submit" name="acao" value="editar" class="btn">Salvar</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Modal de Confirmação de Exclusão -->
        <div class="modal fade" id="modalDelete" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Confirmar Exclusão</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Fechar"></button>
                    </div>
                    <div class="modal-body">
                        <p>Tem certeza que deseja excluir a matéria <strong id="materiaNome"></strong>?</p>
                        <p style="color: #dc3545;">Esta ação não pode ser desfeita!</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn" style="background: linear-gradient(135deg, #6c757d, #495057);" data-bs-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn" style="background: linear-gradient(135deg, #dc3545, #c82333);" id="btnConfirmarExclusao">Excluir</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editarMateria(id, nome, cor) {
            document.getElementById('edit_id').value = id;
            document.getElementById('edit_nome').value = nome;
            document.getElementById('edit_cor').value = cor;
            
            // Atualiza o preview da cor
            const colorPreview = document.getElementById('colorPreview');
            const colorValue = document.getElementById('colorValue');
            colorPreview.style.backgroundColor = cor;
            colorValue.textContent = cor;

            // Adiciona evento para atualizar o preview quando a cor mudar
            document.getElementById('edit_cor').addEventListener('input', function(e) {
                colorPreview.style.backgroundColor = e.target.value;
                colorValue.textContent = e.target.value;
            });
        }

        function confirmarExclusao(id, nome) {
            document.getElementById('materiaNome').textContent = nome;
            const modal = new bootstrap.Modal(document.getElementById('modalDelete'));
            
            document.getElementById('btnConfirmarExclusao').onclick = function() {
                window.location.href = 'processar_materia.php?acao=excluir&id=' + id;
            };
            
            modal.show();
        }

        // Configuração do tema
        document.addEventListener('DOMContentLoaded', function() {
            const themeToggleBtn = document.getElementById('theme-toggle-btn');
            if (themeToggleBtn) {
                themeToggleBtn.addEventListener('click', function() {
                    const currentTheme = document.documentElement.getAttribute('data-theme');
                    const newTheme = currentTheme === 'dark' ? '' : 'dark';
                    document.documentElement.setAttribute('data-theme', newTheme);
                    localStorage.setItem('theme', newTheme);
                    updateThemeIcon(newTheme);
                });
            }
        });

        function updateThemeIcon(theme) {
            const icon = document.getElementById('theme-icon');
            if (icon) {
                icon.className = theme === 'dark' ? 'fas fa-sun' : 'fas fa-moon';
            }
        }

        // Aplicar tema salvo ao carregar
        const savedTheme = localStorage.getItem('theme') || '';
        document.documentElement.setAttribute('data-theme', savedTheme);
        updateThemeIcon(savedTheme);
    </script>
</body>
</html>



