<?php
include_once("../../session_config.php");
require_once("../../assets/config.php");
require_once('../../cadastros/includes/verify_admin.php');

if (!verificarSessaoValida()) {
    header("Location: ../../login_index.php");
    exit();
}

// Verifica se é qualquer admin
verificarAcessoAdmin($conexao, false);

// Processar ações
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action === 'create') {
        $nome = trim($_POST['nome']);
        $descricao = trim($_POST['descricao']);
        $ordem = (int)$_POST['ordem'];
        
        $query = "INSERT INTO appestudo.forum_categorias (nome, descricao, ordem) VALUES ($1, $2, $3)";
        pg_query_params($conexao, $query, array($nome, $descricao, $ordem));
        
    } elseif ($action === 'update') {
        $id = (int)$_POST['id'];
        $nome = trim($_POST['nome']);
        $descricao = trim($_POST['descricao']);
        $ordem = (int)$_POST['ordem'];
        $status = isset($_POST['status']) ? true : false;
        
        $query = "UPDATE appestudo.forum_categorias 
                 SET nome = $1, descricao = $2, ordem = $3, status = $4 
                 WHERE id = $5";
        pg_query_params($conexao, $query, array($nome, $descricao, $ordem, $status, $id));
        
    } elseif ($action === 'delete') {
        // Para deletar, vamos exigir que seja super admin
        verificarSuperAdmin($conexao, false);
        
        $id = (int)$_POST['id'];
        
        // Verifica se existem tópicos na categoria
        $query_check = "SELECT COUNT(*) FROM appestudo.forum_topicos WHERE categoria_id = $1";
        $result_check = pg_query_params($conexao, $query_check, array($id));
        $count = pg_fetch_result($result_check, 0, 0);
        
        if ($count == 0) {
            $query = "DELETE FROM appestudo.forum_categorias WHERE id = $1";
            pg_query_params($conexao, $query, array($id));
        } else {
            // Apenas desativa a categoria
            $query = "UPDATE appestudo.forum_categorias SET status = false WHERE id = $1";
            pg_query_params($conexao, $query, array($id));
        }
    }
    
    header("Location: categorias.php");
    exit();
}

// Buscar categorias
$query_categorias = "SELECT * FROM appestudo.forum_categorias ORDER BY ordem";
$result_categorias = pg_query($conexao, $query_categorias);
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gerenciar Categorias - Admin</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <?php $nome_admin = isset($_SESSION['nome']) ? $_SESSION['nome'] : 'Administrador'; ?>
    <header class="admin-header">
        <a href="index.php" class="admin-logo"><strong>PlanejaAqui</strong> <span style="font-weight:400;">Admin</span></a>
        <div class="admin-header-actions">
            <span style="font-size:1rem; color:var(--text-secondary); margin-right:16px; font-weight:500;">
                <?php echo htmlspecialchars($nome_admin); ?>
            </span>
            <button class="icon-button" id="toggleDarkMode" title="Alternar modo escuro">
                <i class="fas fa-moon"></i>
            </button>
        </div>
    </header>
    <div class="container">
        <a href="index.php" class="btn-primary" style="display:inline-flex;align-items:center;gap:8px;margin-bottom:18px;text-decoration:none;font-size:1rem;">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
        <h1>Gerenciar Categorias</h1>
        
        <button onclick="showModal('createCategoryModal')" class="btn-primary">
            <i class="fas fa-plus"></i> Nova Categoria
        </button>
        
        <div class="categories-list" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(340px, 1fr)); gap: 20px; margin-top: 20px;">
            <?php while ($categoria = pg_fetch_assoc($result_categorias)): ?>
                <div class="category-card <?php echo $categoria['status'] ? '' : 'inactive'; ?>">
                    <div class="category-info">
                        <h3><?php echo htmlspecialchars($categoria['nome']); ?></h3>
                        <p><?php echo htmlspecialchars($categoria['descricao']); ?></p>
                        <span class="order-badge">Ordem: <?php echo $categoria['ordem']; ?></span>
                    </div>
                    <div class="category-actions">
                        <button onclick="editCategory(<?php echo htmlspecialchars(json_encode($categoria)); ?>)" class="btn-edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button onclick="deleteCategory(<?php echo $categoria['id']; ?>)" class="btn-delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            <?php endwhile; ?>
        </div>
    </div>

    <!-- Modal de Nova Categoria -->
    <div id="createCategoryModal" class="modal">
        <div class="modal-content">
            <h2>Nova Categoria</h2>
            <form action="categorias.php" method="POST">
                <input type="hidden" name="action" value="create">
                
                <div class="form-group">
                    <label for="nome">Nome:</label>
                    <input type="text" id="nome" name="nome" required>
                </div>
                
                <div class="form-group">
                    <label for="descricao">Descrição:</label>
                    <textarea id="descricao" name="descricao" required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="ordem">Ordem:</label>
                    <input type="number" id="ordem" name="ordem" required>
                </div>
                
                <div class="form-actions">
                    <button type="button" onclick="hideModal('createCategoryModal')" class="btn-secondary">Cancelar</button>
                    <button type="submit" class="btn-primary">Criar</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal de Edição -->
    <div id="editCategoryModal" class="modal">
        <div class="modal-content">
            <h2>Editar Categoria</h2>
            <form action="categorias.php" method="POST">
                <input type="hidden" name="action" value="update">
                <input type="hidden" name="id" id="edit-id">
                
                <div class="form-group">
                    <label for="edit-nome">Nome:</label>
                    <input type="text" id="edit-nome" name="nome" required>
                </div>
                
                <div class="form-group">
                    <label for="edit-descricao">Descrição:</label>
                    <textarea id="edit-descricao" name="descricao" required></textarea>
                </div>
                
                <div class="form-group">
                    <label for="edit-ordem">Ordem:</label>
                    <input type="number" id="edit-ordem" name="ordem" required>
                </div>
                
                <div class="form-group">
                    <label>
                        <input type="checkbox" name="status" id="edit-status">
                        Ativo
                    </label>
                </div>
                
                <div class="form-actions">
                    <button type="button" onclick="hideModal('editCategoryModal')" class="btn-secondary">Cancelar</button>
                    <button type="submit" class="btn-primary">Salvar</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function showModal(modalId) {
            document.getElementById(modalId).style.display = 'flex';
        }
        
        function hideModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }
        
        function editCategory(categoria) {
            document.getElementById('edit-id').value = categoria.id;
            document.getElementById('edit-nome').value = categoria.nome;
            document.getElementById('edit-descricao').value = categoria.descricao;
            document.getElementById('edit-ordem').value = categoria.ordem;
            document.getElementById('edit-status').checked = categoria.status;
            
            showModal('editCategoryModal');
        }
        
        function deleteCategory(id) {
            if (confirm('Tem certeza que deseja excluir esta categoria?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = 'categorias.php';
                
                const actionInput = document.createElement('input');
                actionInput.type = 'hidden';
                actionInput.name = 'action';
                actionInput.value = 'delete';
                
                const idInput = document.createElement('input');
                idInput.type = 'hidden';
                idInput.name = 'id';
                idInput.value = id;
                
                form.appendChild(actionInput);
                form.appendChild(idInput);
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Dark mode toggle igual ao index.php
        function toggleDarkMode() {
            const body = document.body;
            const icon = document.querySelector('#toggleDarkMode i');
            body.classList.toggle('dark-mode');
            if (body.classList.contains('dark-mode')) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
                localStorage.setItem('adminDarkMode', 'enabled');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
                localStorage.setItem('adminDarkMode', 'disabled');
            }
        }
        document.addEventListener('DOMContentLoaded', function() {
            const darkMode = localStorage.getItem('adminDarkMode');
            const icon = document.querySelector('#toggleDarkMode i');
            if (darkMode === 'enabled') {
                document.body.classList.add('dark-mode');
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            }
            document.getElementById('toggleDarkMode').addEventListener('click', function(e) {
                e.preventDefault();
                toggleDarkMode();
            });
        });
    </script>
</body>
</html>

