:root {
    --primary: #00008B;
    --secondary: #f8f0e3;
    --accent: #4a4a4a;
    --border: #e0e0e0;
    --text: #2c3e50;
    --active: #00008B;
    --hover: #f5f5f5;
    --primary-blue: #00008B;
    
    

    --parchment: #f8f0e3;
    --vintage-gold: #b8860b;
    --burgundy: rgb(222, 184, 135);
    --gold-accent: #daa520;
    --shadow-color: rgba(0, 0, 0, 0.2);
    --elegant-border: linear-gradient(45deg, var(--vintage-gold), #ffd700, var(--vintage-gold));
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --dark-bg: #00008B;

    --white: #ffffff;
    --shadow: rgba(0, 0, 0, 0.1);
}

body[data-theme="dark"] {
    --primary: #00008B; /* Royal Blue - Adjust if needed for dark theme */
    --secondary: #1a1a2e; /* Dark blue-ish background */
    --accent: #e94560; /* Accent color - Example: Pink/Red */
    --border: #2a2a48; /* Darker border color */
    --text: #e0e0e0; /* Light text color for dark backgrounds */
    --active: #5252ff; /* Brighter blue for active elements */
    --hover: #23233a; /* Slightly lighter dark for hover */
    --primary-blue: #5252ff; /* Brighter blue for primary actions */

    --parchment: #1e1e32; /* Darker parchment */
    --vintage-gold: #b8860b; /* Keep gold, or adjust if it clashes */
    --burgundy: #902d58; /* Darker burgundy/purple */
    --gold-accent: #daa520; /* Keep gold accent, or adjust */
    --shadow-color: rgba(255, 255, 255, 0.05); /* Lighter shadow for dark theme */
    --elegant-border: linear-gradient(45deg, var(--vintage-gold), #ffd700, var(--vintage-gold)); /* May need adjustment */
    --success-color: #28a745; /* Keep or adjust for contrast */
    --danger-color: #dc3545; /* Keep or adjust for contrast */
    --warning-color: #ffc107; /* Keep or adjust for contrast */
    --dark-bg: #121220; /* Very dark background for some elements */

    --white: #1e1e32; /* Represents 'white' surfaces, now dark */
    --shadow: rgba(255, 255, 255, 0.08); /* Lighter shadow */

    /* Specific component adjustments */
    --card-bg: #1e1e32;
    --card-border: #2a2a48;
    --card-text: #c0c0c0;
    --modal-bg: #23233a;
    --modal-header-bg: #1a1a2e;
    --button-bg: var(--primary-blue);
    --button-text: #ffffff;
    --button-hover-bg: #6262ff;
    --input-bg: #2a2a48;
    --input-text: #e0e0e0;
    --input-border: #3a3a5a;

    /* Ensure high contrast for text on primary backgrounds */
    --text-on-primary: #ffffff;

    /* RGB versions for rgba() usage */
    --primary-blue-rgb: 82, 82, 255; /* Corresponds to #5252ff */
    --card-bg-rgb: 30, 30, 50; /* Corresponds to #1e1e32 or similar */
    --text-rgb: 224, 224, 224; /* Corresponds to #e0e0e0 */
    --vintage-gold-rgb: 184, 134, 11; /* Corresponds to #b8860b */
    --danger-rgb: 220, 53, 69; /* Corresponds to #dc3545 or #e94560 (accent) */
    --accent-rgb: 233, 69, 96; /* Corresponds to #e94560 */
}

.tabs-container {
   
   
    max-width: 1400px;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    flex: 1;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}

.tabs-wrapper {
    background: var(--white);
    padding: 1rem 1rem 0rem 1rem;
    position: sticky;   
    z-index: 10;
}

.tabs {
    display: flex;
    gap: 0.5rem;
    /*border-bottom: 1px solid var(--border);*/
    padding-bottom: 0.5rem;
    /*margin-bottom: 1rem;*/
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    gap: 0.25rem;
    /*padding-bottom: 0.25rem;
    margin-bottom: 0.5rem;*/
}

.tabs::-webkit-scrollbar {
    display: none;
}

.tab {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    background: transparent;
    color: var(--text);
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
    font-size: 0.9rem;
    font-weight: 500;
    outline: none;
}

.tab:hover {
    background: rgba(0, 0, 139, 0.05); /* Light theme hover */
    color: var(--primary-blue); /* Adapts */
}

body[data-theme="dark"] .tab:hover {
    background: var(--hover); /* Dark theme hover */
    /* color: var(--primary-blue); --primary-blue is already light/bright for dark theme */
}

.tab:focus {
    outline: none;
}

.tab.active {
    background: var(--primary-blue); /* Adapts */
    color: var(--white); /* Light theme: white text */
}

body[data-theme="dark"] .tab.active {
    /* background: var(--primary-blue); Already adapted */
    color: var(--text-on-primary); /* Dark theme: light text */
}

.tab.active::after {
    transform: scaleX(1);
}

.content {
    background: var(--white); /* Adapts to dark theme's --white */
    box-shadow: 0 4px 12px var(--shadow); /* Adapts to dark theme's --shadow */
    padding: 2rem 1rem 1rem 1rem;
    display: none;
    flex: 1;
    overflow-y: auto;
    height: calc(100vh - 110px);
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.3s ease, transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.content.active {
    display: block;
    opacity: 1;
    transform: translateY(0);
}

.content-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    padding: 0.5rem;
}

.card {
    background: var(--white); /* Adapts */
    padding: 1.2rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px var(--shadow); /* Adapts */
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    /* Adding a default border for light theme might alter appearance, so only add for dark. */
}

body[data-theme="dark"] .card {
    background: var(--card-bg);
    box-shadow: 0 2px 8px var(--shadow); /* Re-affirm or use a more specific card shadow if needed */
    border: 1px solid var(--card-border);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow); /* Adapts */
}

body[data-theme="dark"] .card:hover {
    box-shadow: 0 4px 12px var(--shadow-color); /* Use a slightly different shadow for hover if defined */
    /* transform: translateY(-2px); already there */
}

/* Tablets */
@media (max-width: 1024px) {
    .tabs-container {
        padding: 0.5rem;
    }

    .content-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 0.75rem;
    }

    .card {
        padding: 1rem;
    }
}

/* Mobile */
@media (max-width: 768px) {
    .tabs-container {
        margin-top: 0;
        padding: 0.5rem;
    }

    .menu-toggle {
        display: block;
        position: fixed;
        top: 10px;
        right: 10px;
        z-index: 1000;
        background: var(--primary);
        color: white;
        border: none;
        padding: 0.5rem;
        border-radius: 4px;
    }

    .tabs-wrapper {
        position: sticky;
        top: 60px;
        border-radius: 0;
        background: var(--white);
        z-index: 5;
        padding: 0.75rem;
    }


    .tab {
        width: 100%;
        text-align: left;
        padding: 0.4rem 0.8rem;
        border-radius: 4px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.85rem;
    }

    .tab i {
        width: 20px;
        text-align: center;
    }

    .tab:hover {
        background: rgba(0, 0, 139, 0.05); /* Mobile light theme hover */
    }
    /* body[data-theme="dark"] .tab:hover already handled by general .tab:hover */

    .tab.active {
        background: var(--primary-blue); /* Adapts */
        color: white; /* Mobile light theme active tab text */
    }
    /* body[data-theme="dark"] .tab.active already handled by general .tab.active */

    .tab:active {
        transform: scale(0.95);
    }

    .content {
        margin-top: 50px; /* Espaço para o botão de menu */
        height: calc(100vh - 60px);
        padding: 0.75rem;
        border-radius: 0;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
        padding: 0;
    }

    /* Ajustes para cards em mobile */
    .card {
        padding: 1rem;
        margin-bottom: 0.75rem;
    }

    /* Ajustes para gráficos em mobile */
    .grafico_heatmap {
        height: auto;
        /*min-height: 300px;*/
    }

    /* Ajustes para a agenda em mobile */
    .agenda {
        margin: 0.5rem 0;
    }

    .handwritten {
        font-size: 1rem;
        padding: 0.75rem;
    }
}

/* Mobile pequeno */
@media (max-width: 480px) {
    .tabs-container {
        padding: 0.25rem;
    }

    .content {
        padding: 0.5rem;
    }

    .card {
        padding: 0.75rem;
    }

    .tab {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }
}

/* Estilos gerais */
* {
    margin: 0;
    /* padding: 0; */
    box-sizing: border-box;
}

body {
    display: flex;
    min-height: 100vh;
    margin: 0;
    font-family: 'Quicksand', sans-serif;
    overflow-x: hidden;
    /* padding: 1rem; */
    background: var(--hover);
}

.header {
    width: 250px;
    min-width: 250px;
    background-color: var(--primary-blue);
    box-shadow: 2px 0 5px rgba(0,0,0,0.2);
    height: 100vh;
    color: var(--white);
    z-index: 1000;
    position: fixed;
    left: 0;
    top: 0;
    transition: width 0.3s ease;
    overflow: visible; /* Garantir que tooltips sejam visíveis */
}

.nav-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
    padding: 0.5rem;
    position: relative;
    width: 100%;
    overflow: visible; /* Garantir que tooltips sejam visíveis */
}

.logo {
    text-align: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
}

.logo a {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

.logo img {
    height: 40px;
    width: auto;
    object-fit: contain;
    /*display: block;*/
    margin: 0 auto;
}

/* Menu Vertical */
.nav-menu {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    padding: 1.5rem 0;
    margin-top: 1rem;
    flex: 1;
}

.nav-item {
    text-decoration: none;
    color: var(--white); /* Light theme: white from :root */
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    position: relative;
    overflow: visible; /* Mudado para visible para permitir tooltips */
    background: transparent;
    text-decoration: none;
}

.nav-item:before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item:hover {
    color: var(--white);
    transform: translateX(8px);
    text-decoration: none;
}

.nav-item:hover:before {
    transform: translateX(0);
}

.nav-item i {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    position: relative;
    z-index: 1;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-item:hover i {
    transform: scale(1.1);
}

.nav-item span {
    position: relative;
    z-index: 1;
    font-weight: 500;
}

/* Informações do Usuário */
.user-info {
    /*padding: 1rem;*/
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--white);
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 0;
    background-color: var(--primary-blue); /* Adapts */
}

body[data-theme="dark"] .nav-item {
    color: var(--text-on-primary); /* Dark theme: light text */
}

body[data-theme="dark"] .nav-item:hover {
    color: var(--text-on-primary); /* Ensure hover text is also light */
    /* The :before element background is rgba(255,255,255,0.1).
       In dark theme, this will be rgba(var(--text-on-primary-rgb), 0.1) if we want a lighten effect.
       Let's assume --text-on-primary-rgb: 255, 255, 255;
       If --text-on-primary is #e0e0e0, then --text-on-primary-rgb would be 224,224,224.
       For now, we can add a specific override if the default is not good.
       Default behavior: rgba(255,255,255,0.1) on dark header will be a subtle dark overlay. */
}

body[data-theme="dark"] .nav-item:hover:before {
    background: rgba(var(--text-rgb), 0.1); /* Use a light transparent overlay for hover effect */
}


body[data-theme="dark"] .logo {
    border-bottom: 1px solid var(--border);
}

body[data-theme="dark"] .user-info {
    border-top: 1px solid var(--border);
    /* background-color: var(--primary-blue); Is already set on .user-info, adapts */
}

.user-info h1 {
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
    color: var(--white); /* Light theme: white from :root */
}

body[data-theme="dark"] .user-info h1 {
    color: var(--text-on-primary); /* Dark theme: light text */
}

/* Versão Mobile do Menu */
@media (max-width: 768px) {
    .nav-wrapper {
        padding: 0.5rem 1rem;
        height: 60px;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        position: static;
    }

    .user-info {
        position: static;
        display: block;
        margin: 0;
        padding: 0;
        border: none;
        order: 1;
        width: auto;
        background: none;
    }

    .user-info h1 {
        font-size: 0.9rem;
        white-space: nowrap;
        margin: 0;
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Container Principal */
main.tabs-container {
    flex: 1;
    margin-left: 250px;
    box-sizing: border-box;
    overflow-x: hidden;
    min-height: 100vh;
    transition: margin-left 0.3s ease;
}

/* Menu toggle para responsividade */
.menu-toggle {
    display: none;
    background: transparent;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--white);
    padding: 0.5rem;
}

/* Responsividade */
@media (max-width: 1024px) {
    .header {
        width: 200px;
        min-width: 200px;
    }

    main.tabs-container {
        margin-left: 200px;
    }

    .logo img {
        height: 35px;
    }
}

@media (max-width: 768px) {
    body {
        flex-direction: column;
    }

    .header {
        width: 100%;
        min-width: 100%;
        height: 60px;
        position: fixed;
        top: 0;
        left: 0;
        overflow: visible;
    }

    .nav-wrapper {
        padding: 0.5rem 1rem;
        height: 60px;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }

    main.tabs-container {
        margin-left: 0;
        margin-top: 60px;
        padding: 0.5rem;
    }

    .logo {
        padding: 0;
        border: none;
    }

    .logo img {
        height: 30px;
    }

    .nav-menu {
        display: none;
        position: absolute;
        top: 60px;
        left: 0;
        width: 100%;
        background-color: var(--primary-blue);
        padding: 1rem;
        box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        flex-direction: column;
        gap: 0.5rem;
    }

    .nav-menu.active {
        display: flex;
    }

    .nav-item {
        padding: 0.8rem 1rem;
        border-radius: 4px;
        color: var(--white);
    }

    .nav-item:hover {
        background-color: rgba(255, 255, 255, 0.1);
    }

    .menu-toggle {
        display: block;
        margin-left: 1.5rem;
        padding: 0.5rem;
        z-index: 2;
    }

    .user-info {
        display: block;
        margin: 0;
        padding: 0;
        border: none;
    }

    .user-info h1 {
        font-size: 1rem;
        white-space: nowrap;
        margin-right: 1rem;
    }

    /* Container para agrupar user-info e menu-toggle */
    .header-right {
        display: flex;
        align-items: center;
        gap: 1.5rem;
        margin-left: auto;
    }

    .user-info {
        display: block;
        margin: 0;
        padding: 0;
        border: none;
        order: 1;
    }

    .user-info h1 {
        font-size: 0.9rem;
        white-space: nowrap;
        margin: 0;
    }

    .menu-toggle {
        display: block;
        padding: 0.5rem;
        order: 2;
        position: relative;
        z-index: 2;
        margin: 0;
        background: transparent;
    }
}

/* ============================
   Estilos do Grid e Layout
   ============================ */

.estudo_hoje{ grid-area: c; width: 100%; height: 100%; }
.planejamento{ grid-area: d; }
.resumo{ grid-area: e; }
.ciclo{ grid-area: f; }
.listar_ultimo{ grid-area: g; }
.listar_proximo{ grid-area: h; }
.grafico_tempo_turno{ grid-area: m; }
.grafico_dia_estudado_mes{ grid-area: n; }
.grafico_hora_liquida_dia{ grid-area: o; }
.grafico_radar{ grid-area: p; width: 100%; height: 100%; }
.simulado{ grid-area: s; }
.questoes{ grid-area: q; }

/* ============================
   Gráficos e Visualizações
   ============================ */

.grafico_heatmap {
    grid-area: j;
    width: 100%;
    /*max-width: 1000px;*/
   /* height: 950px; */
    background: var(--white); /* Adapts */
    border-radius: 12px;
    box-shadow: 0 4px 6px var(--shadow); /* Adapts, removed hardcoded rgb */
    margin: 0 auto;
}
/*
body[data-theme="dark"] .grafico_heatmap {
    background: var(--card-bg);
    box-shadow: 0 4px 6px var(--shadow); Re-affirm or use specific if needed 
}*/

/* ============================
   Cards e Containers
   ============================ */

.caixa-titulo1 {
    background: var(--white); /* Adapts */
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

body[data-theme="dark"] .caixa-titulo1 {
    background: var(--card-bg);
    border: 1px solid var(--card-border); /* Optional: add border for dark mode */
}

/* Estilos dos títulos */
.planejamento-info h3,
.resumo-container h3,
.ciclo-container h3 {
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    padding: 1.5rem;
    color: white; /* Light theme: white text on dark-bg */
    background-color: var(--dark-bg); /* This variable is dark by definition */
}

body[data-theme="dark"] .planejamento-info h3,
body[data-theme="dark"] .resumo-container h3,
body[data-theme="dark"] .ciclo-container h3 {
    color: var(--text-on-primary); /* Dark theme: light text on dark-bg */
    /* background-color: var(--dark-bg); Already dark, no change needed unless a different shade is desired */
}

.calendario h3 {
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    padding: 1.1rem;
    color: white; /* Light theme: white text on dark-bg */
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    background-color: var(--dark-bg); /* This variable is dark by definition */
}

body[data-theme="dark"] .calendario h3 {
    color: var(--text-on-primary); /* Dark theme: light text on dark-bg */
}

/* ============================
   Agenda
   ============================ */

.agenda {
    background: var(--white); /* Adapts */
    border-radius: 15px;
    box-shadow: 0 8px 20px var(--shadow); /* Adapts */
    overflow: hidden;
    transition: transform 0.3s ease;
}

body[data-theme="dark"] .agenda {
    background: var(--card-bg);
    box-shadow: 0 8px 20px var(--shadow);
    border: 1px solid var(--card-border);
}

.agenda:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.12);
}

/* ============================


.handwritten {
    background-color: var(--dark-bg); /* This variable is dark by definition */
    color: white; /* Light theme: white text on dark-bg */
    margin: 0;
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    font-size: 1.2rem;
    font-weight: 600;
    letter-spacing: 0.5px;
    text-shadow: none;
}
   ============================ */

.agenda pre {
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.95); /* Light background for pre */
    color: #333; /* Dark text for pre on light background */
    font-family: 'Courier Prime', monospace;
    white-space: pre-wrap;
    margin: 0;
}

/* ============================
   Status e Quadros
   ============================ */

.quadrado_pendente,
.quadrado_hoje,
.quadrado_proximo_1 {
    margin: 0.75rem 0;
    border-radius: 8px;
    transition: transform 0.2s ease;
}

.quadrado_pendente:hover,
.quadrado_hoje:hover,
.quadrado_proximo_1:hover {
    transform: translateX(5px);
}

.quadrado_pendente {
    background: rgba(255, 99, 132, 0.1); /* Light theme */
    border: 1px solid rgba(255, 99, 132, 0.3);
    color: var(--text); /* Default text color from :root */
}

body[data-theme="dark"] .quadrado_pendente {
    background: rgba(var(--danger-rgb), 0.2); /* Use danger-rgb for consistency */
    border: 1px solid rgba(var(--danger-rgb), 0.4);
    color: var(--text); /* Ensure text inside is readable (light text from dark theme) */
}

.quadrado_hoje {
    background: rgba(75, 192, 192, 0.1); /* Light theme */
    border: 1px solid rgba(75, 192, 192, 0.3);
    color: var(--text); /* Default text color */
}

body[data-theme="dark"] .quadrado_hoje {
    background: rgba(75, 192, 192, 0.2); /* Teal color, adjust if a variable exists */
    border: 1px solid rgba(75, 192, 192, 0.4);
    color: var(--text);
}

.quadrado_proximo_1 {
    background: rgba(54, 162, 235, 0.1); /* Light theme */
    border: 1px solid rgba(54, 162, 235, 0.3);
    color: var(--text); /* Default text color */
}

body[data-theme="dark"] .quadrado_proximo_1 {
    background: rgba(var(--primary-blue-rgb), 0.2); /* Use primary-blue-rgb */
    border: 1px solid rgba(var(--primary-blue-rgb), 0.4);
    color: var(--text);
}

.quadrado_pendente strong,
.quadrado_hoje strong,
.quadrado_proximo_1 strong {
    font-family: 'Roboto Condensed', sans-serif;
    font-size: 0.9rem;
    line-height: 1.6;
    color: var(--text); /* This should adapt if the parent's color is set to var(--text) in dark mode.
                           If parent color is not set or is default browser black, this needs specific override.
                           Assuming the parent .quadrado_... now has color: var(--text) in dark theme. */
}

body[data-theme="dark"] .handwritten {
    color: var(--text-on-primary);
    /* background-color: var(--dark-bg); Already dark, no change needed */
}

body[data-theme="dark"] .agenda pre {
    background: rgba(var(--text-rgb), 0.05); /* Subtle light text color background */
    color: var(--text); /* Light text for pre */
}

/* ============================
   Responsividade
   ============================ */

@media (max-width: 768px) {
    .agenda {
        margin: 1rem;
    }
    .handwritten {
        font-size: 1.1rem;
    }
}

.handwritten:hover {
    transform: scale(1.05);
}



/* Classes de Turno */
.turno-card-compacto {
    background: var(--white); /* Adapts */
    border-radius: 8px;
    box-shadow: 0 2px 4px var(--shadow-color); /* Adapts, using var(--shadow-color) from :root */
    padding: 0.75rem;
    position: relative;
}

body[data-theme="dark"] .turno-card-compacto {
    background: var(--card-bg);
    box-shadow: 0 2px 4px var(--shadow); /* Dark theme shadow */
    border: 1px solid var(--card-border);
}

.turno-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--dark-bg); /* Light theme: dark text */
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    font-size: 0.9rem;
}

body[data-theme="dark"] .turno-header {
    color: var(--text); /* Dark theme: light text */
}

.turno-header i {
    color: var(--vintage-gold); /* Adapts */
    padding: 0.5rem;
    background: rgba(var(--vintage-gold-rgb), 0.1); /* Adapts, ensure --vintage-gold-rgb is defined */
    border-radius: 50%;
    font-size: 1rem;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.turno-info {
    display: flex;
    align-items: baseline;

}

.turno-info strong {
    font-size: 1.1rem;
    color: var(--dark-bg); /* Light theme: dark text */
    font-family: 'Courier Prime', monospace;
}

body[data-theme="dark"] .turno-info strong {
    color: var(--text); /* Dark theme: light text */
}

.turno-info .tempo {
    color: var(--vintage-gold); /* Adapts */
    font-size: 0.9rem;
    font-family: 'Courier Prime', monospace;
}




.dias-grid::-webkit-scrollbar {
    height: 6px;
}

.dias-grid::-webkit-scrollbar-track {
    background: #f1f1f1; /* Light theme track */
    border-radius: 3px;
}

body[data-theme="dark"] .dias-grid::-webkit-scrollbar-track {
    background: var(--input-bg); /* Dark theme track */
}

.dias-grid::-webkit-scrollbar-thumb {
    background: #888; /* Light theme thumb */
    border-radius: 3px;
}

body[data-theme="dark"] .dias-grid::-webkit-scrollbar-thumb {
    background: var(--accent); /* Dark theme thumb */
}


.dia-item.estudou {
    background: var(--primary-blue); /* Adapts */
    border: 1px solid var(--primary-blue); /* Adapts */
}

.dia-item.nao-estudou {
    background: #f44336; /* Red color, generally fine on dark/light */
    border: 1px solid #e53935; /* Darker red */
}

.dia-item.dia-atual {
    background: #FFF; /* Light theme: white background */
    border: 2px dashed #666; /* Light theme: gray dashed border */
}

body[data-theme="dark"] .dia-item.dia-atual {
    background: var(--card-bg); /* Dark theme: card background */
    border: 2px dashed var(--border); /* Dark theme: border color */
}

.dia-item.dia-atual::after {
    content: attr(data-date);
    position: absolute;
    top: 0px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.75rem;
    color: white; /* Light theme: white text on primary-blue */
    background: var(--primary-blue); /* Adapts */
    padding: 3px 8px;
    border-radius: 4px;
    white-space: nowrap;
    z-index: 9999; /* Valor alto para garantir que fique por cima */
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.dia-item.dia-atual:hover::after {
    content: "Vamos estudar hoje? 📚";
    width: auto;
    min-width: 130px;
    text-align: center;
    padding: 4px 12px;
}

.dia-item.estudou::before {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: white; /* Text on --primary-blue */
    font-size: 0.7rem;
    position: absolute;
}

body[data-theme="dark"] .dia-item.estudou::before {
    color: var(--text-on-primary); /* Ensure light text on primary blue */
}

.dia-item.nao-estudou::before {
    content: '\f00d';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: white; /* Text on red background */
    font-size: 0.7rem;
    position: absolute;
}
/* color: white on red background is usually fine, no specific dark theme override unless needed */

.dia-numero {
    font-size: 0.6rem;
    position: absolute;
    top: 1px;
    left: 2px;
    z-index: 5;
}

.dia-item.estudou .dia-numero,
.dia-item.nao-estudou .dia-numero {
    color: rgba(255, 255, 255, 0.8); /* Light text, generally fine on colored backgrounds */
}

.dia-item.dia-atual .dia-numero {
    color: #666; /* Light theme: dark text on white background */
}

body[data-theme="dark"] .dia-item.dia-atual .dia-numero {
    color: var(--text); /* Dark theme: light text on card background */
}

body[data-theme="dark"] .dia-item.dia-atual::after {
    color: var(--text-on-primary); /* Ensure light text on primary blue */
    box-shadow: 0 2px 4px var(--shadow); /* Add shadow for dark mode */
}

.dia-emoji {
    display: none;
}

/* Dropdown para o nome do usuário */
.user-dropdown {
    position: relative;
    cursor: pointer;
}

.user-name {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 5px 10px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.user-name:hover {
    background-color: rgba(0, 0, 139, 0.15); /* Light theme hover */
}

body[data-theme="dark"] .user-name:hover {
    background-color: var(--hover); /* Dark theme hover, inherits from .header's --primary-blue bg */
}

.dropdown-arrow {
    transition: transform 0.3s ease;
    /* Color is inherited from .user-name -> .user-info h1 -> var(--white) or var(--text-on-primary) */
}

/* .dropdown-arrow.rotate - no color change */

.dropdown-content {
    display: none;
    position: absolute;
    right: 0;
    bottom: 100%;
    margin-bottom: 5px;
    background-color: var(--hover); /* Adapts for light theme */
    min-width: 250px;
    box-shadow: 0 2px 10px var(--shadow); /* Adapts for light theme */
    z-index: 1000;
    /*border-radius: 8px; Added for consistency */
    padding: 8px 0; /* Added for consistency */
}

/* body[data-theme="dark"] .dropdown-content {
    background-color: var(--secondary); Dark theme: use secondary (darker) background
    box-shadow: 0 2px 10px var(--shadow); Dark theme shadow
    border: 1px solid var(--border); Add border for dark theme
}*/

.dropdown-content.show {
    display: block;
    animation: fadeInUp 0.3s ease; /* Mudado nome da animação */
}

@keyframes fadeInUp { /* Nova animação para subir */
    from {
        opacity: 0;
        transform: translateY(10px); /* Valor positivo para subir */
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 20px;
    color: var(--primary-blue); /* Adapts, text on var(--hover) background */
    text-decoration: none;
    transition: background-color 0.3s ease;
}

body[data-theme="dark"] .dropdown-item {
    color: var(--text); /* Dark theme: light text on secondary background */
}

.dropdown-item i {
    width: 20px;
    text-align: center;
    /*color: var(--primary-blue);  Adapts */
}
/*
body[data-theme="dark"] .dropdown-item i {
    color: var(--primary-blue);  Ensure it's the bright version if needed, or var(--accent) 
}*/

.dropdown-item:hover {
    background-color: rgba(0, 0, 139, 0.15); /* Light theme hover */
    color: var(--primary-blue); /* Adapts */
}

body[data-theme="dark"] .dropdown-item:hover {
    background-color: var(--hover); /* Dark theme hover */
    color: var(--primary-blue); /* Or var(--accent) for more pop */
}

/* Separador entre os itens */
.dropdown-content a:not(:last-child) {
    border-bottom: 1px solid var(--border); /* Adapts */
}
/* body[data-theme="dark"] .dropdown-content a:not(:last-child) - var(--border) already dark */





.ciclo-container h3 {
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    padding: 1.5rem;
    color: white;
    background-color:  var(--dark-bg);


}




.handwritten:hover {
    transform: scale(1.05);
}


/* Matérias e Itens */
.materia {
    padding: 1rem;
    margin-bottom: 0.5rem;
    background: var(--parchment); /* Adapts */
    border: 1px solid var(--vintage-gold); /* Adapts */
    border-radius: 8px;
    transition: all 0.3s ease;
    color: var(--text); /* Added default text color */
}

body[data-theme="dark"] .materia {
    /* --parchment is dark, --vintage-gold is gold. Text should be light. */
    color: var(--text); /* Ensure text is light */
}

.materia:hover {
    transform: translateX(5px);
    box-shadow: 2px 2px 5px var(--shadow); /* Adapts */
}

.destacada1 {
    position: relative;
    border: 2px solid var(--vintage-gold); /* Adapts */
    background: linear-gradient(45deg, var(--parchment), var(--white)); /* Adapts */
    color: var(--text); /* Added default text color */
}

body[data-theme="dark"] .destacada1 {
    color: var(--text); /* Ensure text is light */
}

.destacada1::before {
    content: '★';
    position: absolute;
    top: -10px;
    right: -10px;
    background: var(--vintage-gold); /* Adapts */
    color: white; /* Light theme: white text on gold */
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

/* Progress Bar */
.progress {
    height: 25px;
    background: var(--parchment); /* Adapts */
    border: 1px solid var(--vintage-gold); /* Adapts */
    border-radius: 12px;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-bar {
    background: linear-gradient(45deg, var(--vintage-gold), var(--gold-accent)); /* Adapts */
    transition: width 0.5s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    color: white; /* Light theme: white text on gold gradient */
    text-shadow: 1px 1px 2px var(--shadow-color); /* Adapts */
}

body[data-theme="dark"] .progress-bar {
    color: var(--text-on-primary); /* Dark theme: light text on gold gradient */
    /* text-shadow: 1px 1px 2px var(--shadow); Already uses var(--shadow-color) which is dark_theme's shadow */
}


.proxima-materia {
    position: relative;
    padding-right: 40px !important;
    border: 2px solid var(--dark-bg) !important; /* --dark-bg adapts */
    background: linear-gradient(45deg, var(--parchment), var(--white)); /* Adapts */
    box-shadow: 0 2px 4px var(--shadow); /* Adapts */
    color: var(--text); /* Added default text color */
}

body[data-theme="dark"] .proxima-materia {
    color: var(--text); /* Ensure text is light */
}

.proxima-materia::after {
    content: "próxima";
    position: absolute;
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--dark-bg); /* Adapts */
    color: white; /* Light theme: white text on dark-bg */
    padding: 4px 8px;
    font-size: 0.7em;
    border-radius: 12px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.ultima-materia {
    position: relative;
    padding-right: 40px !important;
    border: 2px solid var(--burgundy) !important; /* --burgundy adapts */
    background: linear-gradient(45deg, var(--parchment), var(--white)); /* Adapts */
    box-shadow: 0 2px 4px var(--shadow); /* Adapts */
    color: var(--text); /* Added default text color */
}

body[data-theme="dark"] .ultima-materia {
    color: var(--text); /* Ensure text is light */
}

.ultima-materia::after {
    content: "última";
    position: absolute;
    right: -10px;
    top: 50%;
    transform: translateY(-50%);
    background: var(--burgundy); /* Adapts */
    color: white; /* Light theme: white text on burgundy */
    padding: 4px 8px;
    font-size: 0.7em;
    border-radius: 12px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Animações para ambos */
.proxima-materia::before,
.ultima-materia::before {
    content: "";
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    animation: pulse 1.5s infinite;
}

.proxima-materia::before {
    background: var(--vintage-gold);
}

.ultima-materia::before {
    background: var(--burgundy);
}

@keyframes pulse {
    0% {
        transform: translateY(-50%) scale(1);
        opacity: 1;
    }
    50% {
        transform: translateY(-50%) scale(1.5);
        opacity: 0.5;
    }
    100% {
        transform: translateY(-50%) scale(1);
        opacity: 1;
    }
}

/* Estilos do Planejamento */
.planejamento-info {
    padding: 1rem;
    background: linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0.95)); /* Light theme gradient */
    border-radius: 12px;
    box-shadow: 0 4px 6px var(--shadow-color); /* Adapts, uses :root shadow-color */
}

body[data-theme="dark"] .planejamento-info {
    background: linear-gradient(to bottom, rgba(var(--card-bg-rgb),0.8), rgba(var(--card-bg-rgb),0.95)); /* Dark theme gradient */
    box-shadow: 0 4px 6px var(--shadow); /* Dark theme shadow */
    border: 1px solid var(--border);
}

.info-row {
    display: flex;
    gap: 1rem;
    margin: 1rem 0;
    justify-content: space-between;
}

.info-card {
    background: var(--white); /* Adapts */
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px var(--shadow); /* Adapts */
    border: 1px solid rgba(var(--vintage-gold-rgb), 0.2); /* Adapts */
    flex: 1;
    margin-bottom: 1rem;
    color: var(--text); /* Added default text color for content */
}

body[data-theme="dark"] .info-card {
    background: var(--card-bg);
    box-shadow: 0 2px 4px var(--shadow);
    border: 1px solid rgba(var(--vintage-gold-rgb), 0.3); /* Slightly more visible gold border */
    color: var(--card-text); /* Dark theme specific text color for cards */
}

.info-label {
    display: block;
    font-size: 1rem;
    color: var(--active);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.info-value {
    font-size: 1rem;
    font-weight: 600;
    color: #333; /* Light theme: dark text */
    font-family: 'Varela Round', 'Quicksand', sans-serif;
}

body[data-theme="dark"] .info-value {
    color: var(--text); /* Dark theme: light text */
}

.highlight-blue {
    color: #2196F3; /* Bright blue, usually fine */
}

body[data-theme="dark"] .highlight-blue {
    color: var(--active); /* Use a theme variable if defined and suitable */
}

.highlight-red {
    color: #f44336; /* Bright red, usually fine */
}

body[data-theme="dark"] .highlight-red {
    color: var(--danger-color); /* Use a theme variable if defined and suitable */
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    margin: 1rem 0;
}

.status-badge i {
    margin-right: 0.5rem;
}

.not-started {
    background-color: #fff3e0; /* Light orange background */
    color: #f57c00; /* Dark orange text */
    border: 1px solid #ffe0b2; /* Light orange border */
}

body[data-theme="dark"] .not-started {
    background-color: rgba(var(--warning-rgb), 0.2); /* Assuming --warning-rgb for orange tones */
    color: var(--warning-color); /* Use warning color variable */
    border: 1px solid rgba(var(--warning-rgb), 0.4);
}

.completed {
    background-color: #e8f5e9; /* Light green background */
    color: #43a047; /* Dark green text */
    border: 1px solid #c8e6c9; /* Light green border */
}

body[data-theme="dark"] .completed {
    background-color: rgba(var(--success-rgb), 0.2); /* Assuming --success-rgb for green tones */
    color: var(--success-color); /* Use success color variable */
    border: 1px solid rgba(var(--success-rgb), 0.4);
}

/* Progress Bar Styles */
.progress-section {
    margin-top: 1.5rem;
}

.progress-label {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: #666; /* Light theme: gray text */
}

body[data-theme="dark"] .progress-label {
    color: var(--text); /* Dark theme: light text */
}

.progress-percentage {
    font-weight: 600;
    color: var(--vintage-gold); /* Adapts */
}

.progress-bar-container {
    background: #f5f5f5; /* Light theme: light gray background */
    border-radius: 20px;
    height: 12px;
    overflow: hidden;
    box-shadow: inset 0 1px 2px var(--shadow); /* Adapts */
}

body[data-theme="dark"] .progress-bar-container {
    background: var(--input-bg); /* Dark theme: use input background or similar dark shade */
    box-shadow: inset 0 1px 2px var(--shadow-color); /* Dark theme shadow */
}

.progress-bar-custom {
    height: 100%;
    background: linear-gradient(45deg, var(--vintage-gold), var(--gold-accent));
    border-radius: 20px;
    position: relative;
    transition: width 0.5s ease;
}

.progress-glow {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.3),
            transparent
    );
    animation: progress-glow 1.5s infinite;
}

@keyframes progress-glow {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Adicione as variáveis de cor RGB */
:root {
    --vintage-gold-rgb: 184, 134, 11;
    --gold-accent-rgb: 218, 165, 32;
}

/* Estilos do Resumo */
.resumo-container {
    padding: 1rem;
    background: linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0.95)); /* Light theme gradient */
    border-radius: 12px;
    box-shadow: 0 4px 6px var(--shadow-color); /* Adapts */
}

body[data-theme="dark"] .resumo-container {
    background: linear-gradient(to bottom, rgba(var(--card-bg-rgb),0.8), rgba(var(--card-bg-rgb),0.95)); /* Dark theme gradient */
    box-shadow: 0 4px 6px var(--shadow); /* Dark theme shadow */
    border: 1px solid var(--border);
}

.resumo-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.resumo-card {
    flex: 1;
    background: var(--white); /* Adapts */
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 2px 4px var(--shadow); /* Adapts */
    border: 1px solid rgba(0, 0, 0, 0.05); /* Light theme very light border */
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    color: var(--text); /* Added default text color */
}

body[data-theme="dark"] .resumo-card {
    background: var(--card-bg);
    box-shadow: 0 2px 4px var(--shadow);
    border: 1px solid var(--card-border);
    color: var(--card-text); /* Dark theme specific card text color */
}

.resumo-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #f8f9fa; /* Light theme: very light gray */
    border-radius: 8px;
}

body[data-theme="dark"] .card-icon {
    background: var(--hover); /* Dark theme: use hover color for icon background */
}

.card-icon i {
    font-size: 1.2rem;
    color: var(--text); /* Default text color, likely needs adjustment */
}

body[data-theme="dark"] .card-icon i {
    color: var(--accent); /* Dark theme: use accent color for icons */
}

.card-content {
    flex: 1;
}

.card-label {
    font-size: 0.85rem;
    color: #666; /* Light theme: gray text */
    margin-bottom: 0.3rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

body[data-theme="dark"] .card-label {
    color: var(--text); /* Dark theme: light text */
}

.card-value {
    font-size: 1.1rem;
    font-weight: 600;
    font-family: 'Courier Prime', monospace;
    color: var(--text); /* Should adapt from parent .resumo-card */
}
/* No specific dark theme override for .card-value if parent .resumo-card color is set correctly */

/* Streak Card */
.streak-card {
    margin-top: 1rem;
    background: linear-gradient(45deg, #f8f9fa, var(--white)); /* Adapts */
    border: 1px solid var(--border); /* Adapts */
    color: var(--text); /* Added default text color */
}

body[data-theme="dark"] .streak-card {
    background: linear-gradient(45deg, var(--hover), var(--card-bg)); /* Dark theme gradient */
    border: 1px solid var(--card-border);
    color: var(--text); /* Ensure light text */
}

/* Highlight Colors */
.highlight-green {
    color: #28a745;
}

.highlight-red {
    color: #dc3545;
}

.highlight-blue {
    color: #2196F3;
}

/* Responsive Design */
@media (max-width: 768px) {
    .resumo-row {
        flex-direction: column;
    }

    .resumo-card {
        width: 100%;
    }
}

/* Animação para os ícones */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.card-icon i {
    animation: pulse 2s infinite;
}

/* Efeito hover nos cards */
.resumo-card {
    position: relative;
    overflow: hidden;
}

.resumo-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
            45deg,
            transparent,
            rgba(255, 255, 255, 0.3),
            transparent
    );
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.resumo-card:hover::after {
    transform: translateX(100%);
}

/* Estilos do Ciclo de Estudos */
.ciclo-container {
    padding: 1.5rem;
    background: linear-gradient(to bottom, rgba(255,255,255,0.8), rgba(255,255,255,0.95)); /* Light theme gradient */
    border-radius: 12px;
    box-shadow: 0 4px 6px var(--shadow-color); /* Adapts */
}

body[data-theme="dark"] .ciclo-container {
    background: linear-gradient(to bottom, rgba(var(--card-bg-rgb),0.8), rgba(var(--card-bg-rgb),0.95)); /* Dark theme gradient */
    box-shadow: 0 4px 6px var(--shadow); /* Dark theme shadow */
    border: 1px solid var(--border);
}

/* Status Inicial */
.ciclo-status {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 2rem;
    border-radius: 10px;
    background: linear-gradient(45deg, #f8f9fa, var(--white)); /* Adapts */
    box-shadow: 0 2px 4px var(--shadow); /* Adapts */
    color: var(--text); /* Added default text color */
}

body[data-theme="dark"] .ciclo-status {
    background: linear-gradient(45deg, var(--hover), var(--card-bg)); /* Dark theme gradient */
    box-shadow: 0 2px 4px var(--shadow);
    border: 1px solid var(--card-border); /* If it's card-like */
    color: var(--text); /* Ensure light text */
}

.ciclo-status.not-started {
    border: 2px dashed #adb5bd; /* Light theme: gray dashed border */
}

body[data-theme="dark"] .ciclo-status.not-started {
    border: 2px dashed var(--border); /* Dark theme: use theme border color */
}

.status-icon {
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: #f8f9fa; /* Light theme: very light gray */
    font-size: 1.5rem;
    color: #adb5bd; /* Light theme: gray icon color */
}

body[data-theme="dark"] .status-icon {
    background: var(--hover); /* Dark theme: use hover color for background */
    color: var(--text); /* Dark theme: light icon color */
}

.status-message h4 {
    margin: 0;
    color: #495057; /* Light theme: dark gray text */
    font-size: 1.2rem;
    font-weight: 600;
}

body[data-theme="dark"] .status-message h4 {
    color: var(--text); /* Dark theme: light text */
}

.status-message p {
    margin: 0.5rem 0 0;
    color: #6c757d; /* Light theme: medium gray text */
}

body[data-theme="dark"] .status-message p {
    color: var(--text); /* Dark theme: light text, or a slightly dimmer one if needed */
}

/* Cards das Matérias */
.ciclo-materias {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    padding: 1rem;
}

.materia-card {
    background: var(--white); /* Adapts */
    border: 1px solid #e9ecef; /* Light theme border */
    border-left-width: 4px; /* Left border color needs consideration */
    border-left-color: var(--primary-blue); /* Example: using primary blue for light theme */
    border-radius: 8px;
    padding: 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    color: var(--text); /* Default text color */
}

body[data-theme="dark"] .materia-card {
    background: var(--card-bg);
    border: 1px solid var(--card-border);
    border-left-width: 4px;
    border-left-color: var(--primary-blue); /* Dark theme: use primary blue (which is bright) */
    color: var(--card-text); /* Dark theme specific card text color */
}

.materia-card:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.materia-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.materia-nome {
    font-size: 1rem;
    font-weight: 500;
    color: inherit; /* Inherits from .materia-card */
}

.materia-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.materia-badge.ultima {
    background: rgba(255, 99, 132, 0.15); /* Light pink background */
    color: rgb(200, 60, 80); /* Dark pink text */
}

body[data-theme="dark"] .materia-badge.ultima {
    background: rgba(var(--danger-rgb), 0.2); /* Use danger-rgb */
    color: var(--danger-color); /* Or a lighter shade of red if needed */
}

.materia-badge.proxima {
    background: rgba(54, 162, 235, 0.15); /* Light blue background */
    color: rgb(40, 120, 180); /* Dark blue text */
}

body[data-theme="dark"] .materia-badge.proxima {
    background: rgba(var(--primary-blue-rgb), 0.2); /* Use primary-blue-rgb */
    color: var(--primary-blue); /* Or a lighter shade of blue */
}

/* Indicador de Progresso
.materia-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: #f8f9fa;
    overflow: hidden;
}

.progress-indicator {
    width: 30%;
    height: 100%;
    position: absolute;
    animation: progress 2s ease infinite;
}

@keyframes progress {
    0% {
        left: -30%;
    }
    100% {
        left: 100%;
    }
}
 */

/* Legenda */
.ciclo-legenda {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef; /* Light theme border */
}

body[data-theme="dark"] .ciclo-legenda {
    border-top: 1px solid var(--border); /* Dark theme border */
}

.legenda-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    color: #6c757d; /* Light theme: medium gray text */
}

body[data-theme="dark"] .legenda-item {
    color: var(--text); /* Dark theme: light text */
}

.legenda-item i {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: #f8f9fa; /* Light theme: very light gray background for icon */
    color: #6c757d; /* Assuming icon is a font-icon and inherits color */
}

body[data-theme="dark"] .legenda-item i {
    background: var(--hover); /* Dark theme: use hover color for background */
    color: var(--text); /* Dark theme: light icon color */
}

/* Efeitos de Hover e Animações */
.materia-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
            45deg,
            transparent,
            rgba(255, 255, 255, 0.3),
            transparent
    );
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.materia-card:hover::after {
    transform: translateX(100%);
}

/* Responsividade */
@media (max-width: 768px) {
    .ciclo-container {
        padding: 1rem;
    }

    .ciclo-legenda {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
}

/* Variáveis de cores em RGB para opacidade */
:root {
    --burgundy-rgb: 222, 184, 135;
    --vintage-gold-rgb: 184, 134, 11;
}

.stat-card.turno-card {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    padding: 1rem;
    margin-top: 1rem;
    background: var(--white); /* Adapts */
    border-radius: 8px;
    box-shadow: 0 2px 4px var(--shadow); /* Adapts */
    border: 1px solid rgba(var(--vintage-gold-rgb), 0.2); /* Adapts, using vintage-gold-rgb */
    display: flex;
    flex-direction: column;
}

body[data-theme="dark"] .stat-card.turno-card {
    background: var(--card-bg);
    box-shadow: 0 2px 4px var(--shadow);
    border: 1px solid var(--card-border); /* Or a gold border: rgba(var(--vintage-gold-rgb), 0.3) */
}

.turno-card .stat-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    color: var(--dark-bg); /* Light theme: dark text */
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(var(--vintage-gold-rgb), 0.2); /* Adapts */
}

body[data-theme="dark"] .turno-card .stat-title {
    color: var(--text); /* Dark theme: light text */
    border-bottom: 1px solid rgba(var(--vintage-gold-rgb), 0.3); /* Slightly more visible gold border */
}

/* Estilo específico para o ícone */
.turno-card .stat-title i {
    color: var(--vintage-gold); /* Adapts */
    font-size: 1.5rem;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(var(--vintage-gold-rgb), 0.1); /* Adapts */
    border-radius: 50%;
    padding: 0.5rem;
}
/* No specific dark theme for icon itself if gold on dark card-bg is fine */

.turno-card .stat-value {
    font-size: 1.3rem;
    margin: 0.5rem 0;
    color: var(--dark-bg); /* Light theme: dark text */
    font-weight: 600;
}

body[data-theme="dark"] .turno-card .stat-value {
    color: var(--text); /* Dark theme: light text */
}

.turno-card .tempo-estudo {
    font-size: 1rem;
    color: var(--vintage-gold); /* Adapts */
    margin-top: 0.25rem;
    font-family: 'Courier Prime', monospace;
}

.turno-card .turno-desc {
    font-size: 0.9rem;
    color: #666; /* Light theme: medium gray text */
    font-style: italic;
    margin-top: 0.75rem;
    padding-top: 0.75rem;
    border-top: 1px solid rgba(var(--vintage-gold-rgb), 0.1); /* Adapts */
    font-family: 'Quicksand', sans-serif;
}

body[data-theme="dark"] .turno-card .turno-desc {
    color: var(--text); /* Dark theme: light text */
    border-top: 1px solid rgba(var(--vintage-gold-rgb), 0.2); /* Slightly more visible gold border */
}

.turno-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Estilos do Modal */
.modal-estudo {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 9999;
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease;
}

.modal-estudo.ativo {
    visibility: visible;
    opacity: 1;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(3px);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.9);
    background: var(--hover); /* Light theme: use hover color (likely light gray) */
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease;
    overflow: hidden;
}

.modal-estudo.ativo .modal-content {
    transform: translate(-50%, -50%) scale(1);
}

.modal-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--dark-bg); /* Adapts */
    border: none;
    color: white; /* Light theme: white icon on dark-bg */
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: var(--vintage-gold);
    transform: rotate(90deg);
}

.modal-titulo {
    font-family: 'Varela Round', 'Quicksand', sans-serif;
    color: var(--dark-bg); /* Light theme: dark text on light modal */
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    background: rgba(0, 0, 0, 0.1); /* Light theme: subtle dark stripe */
    border-bottom: 2px solid var(--vintage-gold); /* Adapts */

    /* Centralização vertical e horizontal */
    display: flex;
    align-items: center;
    justify-content: center;

    /* Ajuste do padding para criar a faixa */
    padding: 0.8rem 0;

    /* Opcional: arredondar as bordas da faixa */
    border-radius: 4px;
}

.modal-body {
    overflow-y: auto;
    max-height: calc(90vh - 6rem);
    padding-right: 1rem;
}

/* Estilização da barra de rolagem */
.modal-body::-webkit-scrollbar {
    width: 8px;
}

.modal-body::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1); /* Light theme: light track */
    border-radius: 4px;
}

body[data-theme="dark"] .modal-body::-webkit-scrollbar-track {
    background: var(--input-bg); /* Dark theme: dark track */
}

.modal-body::-webkit-scrollbar-thumb {
    background: var(--vintage-gold); /* Adapts */
    border-radius: 4px;
}

body[data-theme="dark"] .modal-body::-webkit-scrollbar-thumb {
    background: var(--accent); /* Dark theme: use accent or vintage-gold if contrast is good */
}

/* Efeito hover nos cards clicáveis */
.materia-card.ultima-materia,
.materia-card.proxima-materia {
    cursor: pointer;
    transition: all 0.3s ease;
}

.materia-card.ultima-materia:hover,
.materia-card.proxima-materia:hover {
    transform: translateX(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}




.sem-eventos {
    text-align: center;
    padding: 20px;
    background: rgba(0, 0, 0, 0.05); /* Light theme: very light gray */
    border-radius: 8px;
    margin: 10px 0;
    color: var(--text); /* Added default text color */
}

body[data-theme="dark"] .sem-eventos {
    background: var(--hover); /* Dark theme: use hover color */
    color: var(--text); /* Ensure light text */
}

.sem-eventos i {
    font-size: 2em;
    color: #999; /* Light theme: gray icon */
    margin-bottom: 10px;
    display: block;
}

body[data-theme="dark"] .sem-eventos i {
    color: var(--text); /* Dark theme: light icon */
}

.sem-eventos p {
    margin: 0;
    color: #666; /* Light theme: gray text */
    font-style: italic;
    font-family: Arial, sans-serif;
}

body[data-theme="dark"] .sem-eventos p {
    color: var(--text); /* Dark theme: light text */
}

.quadrado_pendente,
.quadrado_hoje,
.quadrado_proximo_1 {
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.02);
}

.evento-link {
    text-decoration: none;
    transition: opacity 0.2s;
}

.evento-link:hover {
    opacity: 0.8;
}

pre {
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 0;
    font-family: inherit;
}

.card-planejamento {
    background: var(--white); /* Adapts */
    border-radius: 8px;
    box-shadow: 0 2px 4px var(--shadow); /* Adapts */
    overflow: hidden;
    margin-bottom: 0.5rem;
    color: var(--text); /* Added default text color */
}

body[data-theme="dark"] .card-planejamento {
    background: var(--card-bg);
    box-shadow: 0 2px 4px var(--shadow);
    border: 1px solid var(--card-border);
    color: var(--text); /* Ensure light text */
}


.info-item {
    background: #f8f9fa; /* Light theme: very light gray */
    padding: 0.75rem;
    border-radius: 6px;
    margin-bottom: 0.75rem;
    color: var(--text); /* Default text from :root */
}

body[data-theme="dark"] .info-item {
    background: var(--hover); /* Dark theme: use hover color */
    color: var(--text); /* Ensure light text */
}



.info-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
}

.texto-azul { color: #2196F3; } /* Already handled by .highlight-blue if used consistently */
.texto-vermelho { color: #dc3545; } /* Already handled by .highlight-red */

.status-badge { /* General status badge styling */
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border-radius: 6px;
    font-size: 0.9rem;
    margin-bottom: 0.75rem;
    /* Default light theme if not .pendente or .concluido */
    background: var(--hover); /* Example default */
    color: var(--text);
    border: 1px solid var(--border);
}

.status-badge.pendente {
    background: #fff3e0; /* Light orange background */
    color: #f57c00; /* Dark orange text */
    border: 1px solid #ffe0b2; /* Added for consistency if missing */
}

body[data-theme="dark"] .status-badge.pendente {
    background: rgba(var(--warning-rgb), 0.2); /* Use warning-rgb */
    color: var(--warning-color);
    border: 1px solid rgba(var(--warning-rgb), 0.4);
}

.status-badge.concluido {
    background: #e8f5e9; /* Light green background */
    color: #43a047; /* Dark green text */
    border: 1px solid #c8e6c9; /* Added for consistency if missing */
}

body[data-theme="dark"] .status-badge.concluido {
    background: rgba(var(--success-rgb), 0.2); /* Use success-rgb */
    color: var(--success-color);
    border: 1px solid rgba(var(--success-rgb), 0.4);
}

.progresso {
    margin-top: 1rem;
}

.progresso-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    color: var(--text); /* Added default text color */
}

body[data-theme="dark"] .progresso-header {
    color: var(--text); /* Ensure light text */
}

.barra-progresso {
    background: #e9ecef; /* Light theme: light gray background */
    border-radius: 999px;
    height: 8px;
    overflow: hidden;
}

body[data-theme="dark"] .barra-progresso {
    background: var(--input-bg); /* Dark theme: use input background or similar */
}

.progresso-preenchimento {
    background: var(--primary-blue); /* Adapts */
    height: 100%;
    border-radius: 999px;
    transition: width 0.3s ease;
}

.porcentagem {
    color:var(--primary-blue); /* Adapts */
    font-weight: 500;
}


.turno-card-compacto {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgb(43, 39, 35);
    padding: 0.75rem;
    position: relative;
}



.turno-header i {
    color: var(--vintage-gold);
    padding: 0.5rem;
    background: rgba(184, 134, 11, 0.1);
    border-radius: 50%;
    font-size: 1rem;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}



.turno-info strong {
    font-size: 1.1rem;
    color: var(--dark-bg);
    font-family: 'Courier Prime', monospace;
}

.turno-info .tempo {
    color: var(--vintage-gold);
    font-size: 0.9rem;
    font-family: 'Courier Prime', monospace;
}

.turno-desc {
    color: #666;
    font-size: 0.8rem;
    font-style: italic;
    display: block;
    border-top: 1px solid rgba(184, 134, 11, 0.1);
    padding-top: 0.5rem;
    font-family: 'Quicksand', sans-serif;
}



/* Grid dos dias - AQUI ESTÁ O ELEMENTO QUE VOCÊ PROCURA */
.dias-grid { 
    display: flex; 
    flex-direction: row; 
    gap: 0; 
    padding: 2.8rem 0.2rem 0.2rem 1.9rem;
    overflow-x: auto; 
    width: 100%; 
    align-items: center; 
    min-height: 35px;  /* Reduzido a altura mínima */ 
    /* background: white; */
    border-radius: 4px; 
    /*box-shadow: 0 4px 6px var(--shadow);*/
    position: relative;
    /*padding-top: 2rem;  Espaço adicional para a data acima */
}

/* Estilo da barra de rolagem */
.dias-grid::-webkit-scrollbar {
    height: 6px;
}

.dias-grid::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.dias-grid::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

/* Item do dia */
.dia-item {
    flex: 0 0 32px;  /* Reduzido o tamanho dos quadrados */
    height: 32px;    /* Reduzido a altura dos quadrados */
    border-radius: 4px;
    position: relative;
    margin-right: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

/* Estilos dos estados dos dias */




.dia-item.dia-atual {
    background: #FFF;
    border: 2px dashed #666;
}



.dia-item.dia-atual:hover::after {
    content: "Vamos estudar hoje? 📚";
    width: auto;
    min-width: 130px;
    text-align: center;
    padding: 4px 12px;
}

/* Ícones para dias estudados/não estudados */
.dia-item.estudou::before {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: white;
    font-size: 0.7rem;
    position: absolute;
}

.dia-item.nao-estudou::before {
    content: '\f00d';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    color: white;
    font-size: 0.7rem;
    position: absolute;
}

/* Número do dia */
.dia-numero {
    font-size: 0.6rem;
    position: absolute;
    top: 1px;
    left: 2px;
    z-index: 5;
}

.dia-item.estudou .dia-numero,
.dia-item.nao-estudou .dia-numero {
    color: rgba(255, 255, 255, 0.8);
}

.dia-item.dia-atual .dia-numero {
    color: #666;
}

/* Removendo o emoji */
.dia-emoji {
    display: none;
}

/* Estilo do FullCalendar */
.fc .fc-toolbar.fc-header-toolbar {
    margin-bottom: 0.5px;
    padding: 10px;
}

.fc .fc-button-primary {
    background-color: var(--primary); /* Adapts */
    border-color: var(--hover); /* Adapts */
    color: var(--fc-button-text-color); /* Needs definition or replacement */
}

body[data-theme="dark"] .fc .fc-button-primary {
    background-color: var(--button-bg);
    border-color: var(--button-bg);
    color: var(--button-text);
}

.primary:not(:disabled).fc-button-active, .fc .fc-button-primary:not(:disabled):active {
    background-color: var(--hover); /* Adapts */
    border-color: var(--primary); /* Adapts */
    color: var(--primary); /* Text color on hover/active for light theme */
}

body[data-theme="dark"] .primary:not(:disabled).fc-button-active, 
body[data-theme="dark"] .fc .fc-button-primary:not(:disabled):active {
    background-color: var(--button-hover-bg); /* Dark theme active/pressed button */
    border-color: var(--button-hover-bg);
    color: var(--button-text);
}

.fc .fc-button-primary:disabled {
    background-color: var(--hover); /* Adapts */
    border-color: var(--primary); /* Adapts */
    color: var(--primary); /* Text color for disabled for light theme */
}

body[data-theme="dark"] .fc .fc-button-primary:disabled {
    background-color: var(--input-bg); /* Dark theme disabled button */
    border-color: var(--input-bg);
    color: var(--text); /* Muted text */
    opacity: 0.7;
}

/* .fc .fc-button-primary:not(:disabled).fc-button-active is duplicate of above */

.fc .fc-button-primary:hover {
    background-color: var(--hover); /* Adapts */
    border-color: var(--primary); /* Adapts */
    color: var(--primary); /* Text color on hover for light theme */
}

body[data-theme="dark"] .fc .fc-button-primary:hover {
    background-color: var(--button-hover-bg);
    border-color: var(--button-hover-bg);
    color: var(--button-text);
}

.fc .fc-toolbar-title {
    font-size: 1.75em;
    margin: 0px;
    text-transform: lowercase;
    color: var(--primary); /* Adapts */
    font-family: 'Varela Round', 'Quicksand', sans-serif;
}

body[data-theme="dark"] .fc .fc-toolbar-title {
    color: var(--text); /* Dark theme: light text for title */
}

.fc .fc-toolbar-title::first-letter {
    text-transform: uppercase;
}


.header-nav {
    max-height: calc(100vh - 100px); /* altura máxima considerando o cabeçalho */
    overflow-y: auto; /* adiciona barra de rolagem vertical quando necessário */
    overflow-x: visible; /* Permitir tooltips horizontais */
    scrollbar-width: thin; /* para Firefox */
    scrollbar-color: #00008B #e3eafd; /* Light theme scrollbar colors */
}

body[data-theme="dark"] .header-nav {
    scrollbar-color: var(--primary-blue) var(--input-bg); /* Dark theme scrollbar for Firefox */
}


/* Estilização da barra de rolagem para Chrome/Safari/Edge */
.header-nav::-webkit-scrollbar {
    width: 8px;
}

.header-nav::-webkit-scrollbar-track {
    background: #e3eafd; /* Light theme track */
    border-radius: 4px;
}

body[data-theme="dark"] .header-nav::-webkit-scrollbar-track {
    background: var(--input-bg); /* Dark theme track */
}

.header-nav::-webkit-scrollbar-thumb {
    background: #00008B; /* Light theme thumb (primary color) */
    border-radius: 4px;
}
body[data-theme="dark"] .header-nav::-webkit-scrollbar-thumb {
    background: var(--primary-blue); /* Dark theme thumb (bright blue) */
}


.header-nav::-webkit-scrollbar-thumb:hover {
    background: #000066; /* Light theme thumb hover (darker blue) */
}

body[data-theme="dark"] .header-nav::-webkit-scrollbar-thumb:hover {
    background: var(--button-hover-bg); /* Dark theme thumb hover (brighter blue) */
}

/* Adicione estes estilos após a classe .header */
.header {
    // ... existing code ...
    transition: width 0.3s ease;
}

.header.collapsed {
    width: 60px;
    min-width: 60px;
}

.header.collapsed .nav-item span {
    display: none;
}

.header.collapsed .logo img {
    width: 30px;
    height: 30px;
}

/* Ajustes para o user-info quando retraído */
.header.collapsed .user-info {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 60px;
    background: var(--primary-blue);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    padding: 0;
    border-top: 1px solid rgba(255,255,255,0.1);
    overflow: hidden;
}

.header.collapsed .user-info .user-name {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
}

.header.collapsed .user-info .user-name i.fas.fa-user-circle {
    font-size: 2rem;
    margin: 0;
}

.header.collapsed .user-info .user-name span,
.header.collapsed .user-info .user-name .dropdown-arrow {
    display: none !important;
}

.header.collapsed .user-info .user-name {
    font-size: 0; /* Esconde qualquer texto residual */
}

.toggle-nav-btn {
    position: absolute;
    right: -15px;
    top: 20px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: var(--primary-blue); /* Adapts */
    border: 2px solid white; /* Problematic in dark theme as white variable becomes dark */
    color: white; /* Problematic in dark theme */
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: transform 0.3s ease;
    z-index: 1000;
}

.header.collapsed .toggle-nav-btn i {
    transform: rotate(180deg);
}

/* Ajuste do container principal quando a nav está retraída */
.header.collapsed ~ main.tabs-container {
    margin-left: 60px;
}

@media (max-width: 768px) {
    .toggle-nav-btn {
        display: none;
    }
    
    .header.collapsed + main.tabs-container {
        margin-left: 0;
    }
}

/* ... existing code ... */

.logo {
    text-align: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
}

.logo a {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
}

/* Controle de visibilidade das logos */
.logo-expanded {
    display: block;
}

.logo-collapsed {
    display: none;
}

/* Quando a barra está retraída */
.header.collapsed .logo-expanded {
    display: none;
}

.header.collapsed .logo-collapsed {
    display: block;
}

/* ... existing code ... */

/* Estilo base para os itens de navegação */
.nav-item {
    display: flex;
    align-items: center;
    gap: 10px;
    transition: all 0.3s ease;
}

/* Quando o header estiver retraído */
.header.collapsed .nav-item {
    justify-content: center;
    padding: 0.5rem;
}

.header.collapsed .nav-item i {
    margin: 0;
}

/* Oculta o texto quando retraído */
.header.collapsed .nav-item span {
    display: none;
}

/* Adiciona tooltip ao passar o mouse */
.header.collapsed .nav-item {
    position: relative;
}

.header.collapsed .nav-item:hover::after {
    content: attr(data-title);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background: #fff; /* Hardcoded white background for tooltip */
    color: #333; /* Hardcoded dark text for tooltip */
    padding: 5px 10px;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2); /* Standard shadow */
    white-space: nowrap;
    z-index: 1000;
    margin-left: 10px;
}

body[data-theme="dark"] .header.collapsed .nav-item:hover::after {
    background: var(--card-bg); /* Dark background for tooltip */
    color: var(--text); /* Light text for tooltip */
    box-shadow: 0 2px 5px var(--shadow); /* Dark theme shadow */
    border: 1px solid var(--border);
}

.header.collapsed .dropdown-content .dropdown-item {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 12px 0;
    font-size: 10px; /* Esconde o texto */
    height: 40px;
    min-height: 40px;
    /*border: none;*/
    background: none;
}
.header.collapsed .dropdown-content .dropdown-item:hover {
    background-color: rgba(0, 0, 139, 0.15); /* Light theme hover */
    color: var(--primary-blue); /* Adapts */
}

body[data-theme="dark"] .header.collapsed .dropdown-content .dropdown-item:hover {
    background-color: var(--hover); /* Dark theme hover */
    /* color: var(--primary-blue); Adapts, should be light enough */
}


.header.collapsed .dropdown-content .dropdown-item i {
    font-size: 1.6em !important;
    /* color: var(--primary-blue) !important;  Adapts */
    margin: 0 !important;
    display: inline-block !important;
}

body[data-theme="dark"] .toggle-nav-btn {
    border: 2px solid var(--text-on-primary);
    color: var(--text-on-primary);
    background: var(--primary-blue); /* Ensure bg is consistent if it wasn't already adapting */
}

body[data-theme="dark"] .header.collapsed .toggle-nav-btn i {
    transform: rotate(180deg); /* Rotation is fine */
    vertical-align: middle !important;
}
.header.collapsed .dropdown-content .dropdown-item span {
    display: none !important;
}




.header.collapsed .dropdown-content .dropdown-item::after {
    content: none !important;
}

.header.collapsed .dropdown-content {
    position: fixed !important;
    left: 0px !important;
    bottom: 63px !important;
    top: auto !important;
    min-width: 60px !important;
    width: 60px !important;
    z-index: 5000 !important;
    margin-bottom: 0 !important;
    /* border-radius: 8px 8px 8px 8px; */
}


.header.collapsed .user-info {
   
    z-index: 3001 !important;
}

.header.collapsed .user-name {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}
/* Barra de rolagem dos eventos do calendário */
.fc-popover-body { /* This is for the content within the "more" popover in FullCalendar */
    max-height: 300px; /* ajuste conforme necessário */
    overflow-y: auto;
    /* Scrollbar styling for .fc-popover-body */
    scrollbar-width: thin;
    scrollbar-color: var(--vintage-gold) var(--hover); /* Light theme scrollbar for popover */
}

.fc-popover-body::-webkit-scrollbar {
    width: 8px;
}
.fc-popover-body::-webkit-scrollbar-track {
    background: var(--hover); /* Light theme track */
}
.fc-popover-body::-webkit-scrollbar-thumb {
    background: var(--vintage-gold); /* Light theme thumb */
}

body[data-theme="dark"] .fc-popover-body {
    scrollbar-color: var(--vintage-gold) var(--input-bg); /* Dark theme scrollbar for popover */
}
body[data-theme="dark"] .fc-popover-body::-webkit-scrollbar-track {
    background: var(--input-bg); /* Dark theme track */
}
body[data-theme="dark"] .fc-popover-body::-webkit-scrollbar-thumb {
    background: var(--vintage-gold); /* Keep gold, or use var(--accent) */
}

body[data-theme="dark"] .modal-especifico-titulo-input {
    background-color: var(--card-border);
}
body[data-theme="dark"] .modal-especifico-data {
    background-color: var(--card-border);
}
body[data-theme="dark"] .modal-especifico-detalhes-input {
    background-color: var(--card-border);
}
body[data-theme="dark"] .card-header {
    color: var(--text);
}



#ranking-posicao {
    font-weight: bold;
    color: #00008B;
    background: #e3eafd;
    padding: 2px 8px;
    border-radius: 12px;
    margin-left: 5px;
    font-size: 0.98em;
    vertical-align: middle;
    transition: color 0.2s, background 0.2s;
}

/* Modo escuro: cor transparente */
body[data-theme="dark"] #ranking-posicao {
    color: var(--dark-bg);
}

body[data-theme="dark"] .bg-primary-50 {
        background-color: var(--fc-border-color);
}

.text-sm {
    color: black;
}
.card-content_planejamento{
padding: 10px;
}
.card-content_planejamento_agenda{
    padding: 10px;
}

/* Tooltip override - garantir que funcione */
.header.collapsed .nav-item:hover::after {
    content: attr(data-title) !important;
    position: absolute !important;
    left: calc(100% + 15px) !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: #fff !important;
    color: #333 !important;
    padding: 8px 12px !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.25) !important;
    white-space: nowrap !important;
    z-index: 999999 !important;
    font-size: 14px !important;
    font-weight: 500 !important;
    border: 1px solid rgba(0,0,0,0.1) !important;
    opacity: 0 !important;
    animation: tooltipFadeIn 0.2s ease-out forwards !important;
    display: block !important;
    pointer-events: none !important;
}

body[data-theme="dark"] .header.collapsed .nav-item:hover::after {
    background: #2a2a48 !important;
    color: #e0e0e0 !important;
    border: 1px solid #3a3a5a !important;
    box-shadow: 0 4px 12px rgba(255,255,255,0.05) !important;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateY(-50%) translateX(-5px);
    }
    to {
        opacity: 1;
        transform: translateY(-50%) translateX(0);
    }
}