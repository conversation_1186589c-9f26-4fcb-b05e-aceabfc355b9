<?php
session_start();
// Inclua o arquivo de conexão com o banco de dados
include 'conexao_POST.php'; // Inclui o arquivo de conexão
$id_usuario = $_SESSION['idusuario'];

// Verifica se o formulário foi submetido
if ($_SERVER["REQUEST_METHOD"] == "POST") {

    // Verifica se os campos 'id', 'id_planejamento' e 'detalhe' foram enviados
    if (isset($_POST['id']) && isset($_POST['id_planejamento']) && isset($_POST['detalhe'])) {
        $id = $_POST['id'];
        $id_planejamento = $_POST['id_planejamento'];
        $detalhe = $_POST['detalhe'];

        // Prepara a query SQL para verificar se o registro já existe
        $query_verificar = "SELECT COUNT(*) AS total FROM appEstudo.anotacao WHERE materia_idmateria = $1 AND planejamento_idplanejamento = $2 AND planejamento_usuario_idusuario = $3";
        $params_verificar = array($id, $id_planejamento, $id_usuario);
        $result_verificar = pg_query_params($conexao, $query_verificar, $params_verificar);
        $row_verificar = pg_fetch_assoc($result_verificar);
        $registro_existe = intval($row_verificar['total']) > 0;

        if ($registro_existe) {
            // Prepara a query SQL para atualizar o detalhe da matéria no banco de dados
            $query = "UPDATE appEstudo.anotacao SET detalhe = $1 WHERE materia_idmateria = $2 AND planejamento_idplanejamento = $3 AND planejamento_usuario_idusuario = $4";
            $params = array($detalhe, $id, $id_planejamento, $id_usuario);
        } else {
            // Prepara a query SQL para inserir um novo registro na tabela anotacao
            $query = "INSERT INTO appEstudo.anotacao (detalhe, materia_idmateria, planejamento_idplanejamento, planejamento_usuario_idusuario) VALUES ($1, $2, $3, $4)";
            $params = array($detalhe, $id, $id_planejamento, $id_usuario);
        }

        // Executa a query SQL com os parâmetros
        $result = pg_query_params($conexao, $query, $params);

        if ($result) {
            // Redireciona de volta para a página principal ou exibe uma mensagem de sucesso
            header("Location: modal_planejamento_000.php"); // Altere o nome do arquivo conforme necessário
            exit();
        } else {
            echo "Erro ao atualizar ou inserir detalhe da matéria.";
        }
    } else {
        echo "Campos necessários não enviados.";
    }
} else {
    echo "Método inválido de requisição.";
}
?>
