<?php
session_start();
require_once("assets/config.php");
// Não precisa de verify_admin.php aqui, pois é uma ação de processamento, não uma página visual.
// A verificação de admin já ocorreu na página que chama este script.

header('Content-Type: application/json');

$response = ['success' => false, 'message' => 'Ocorreu um erro.'];

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $response['message'] = 'Método de requisição inválido.';
    echo json_encode($response);
    exit;
}

// Determinar a ação: adicionar ou editar ANTES de verificar CSRF para saber qual nome de token usar.
$acao = isset($_POST['acao']) ? $_POST['acao'] : 'adicionar'; 

// Verificar CSRF token
$csrf_token_post_name = 'csrf_token_conteudo'; // Default para adicionar
if ($acao === 'editar') {
    $csrf_token_post_name = 'csrf_token_conteudo_editar';
} elseif ($acao === 'excluir') {
    // O JS está enviando 'csrf_token_op' para exclusão, mas ele tenta pegar de campos existentes.
    // Idealmente, o JS deveria pegar o token de um campo PHP que sempre existe com o mesmo nome, ex: $_SESSION['csrf_token'] impresso num campo.
    // Por ora, vamos fazer o PHP esperar 'csrf_token_op' para exclusão, que é o que o JS tentará enviar.
    // Se o JS não encontrar csrf_token_conteudo_editar ou csrf_token_conteudo, ele tentará enviar 'csrf_token_op' com o token da sessão (o que não é ideal via JS puro).
    // A melhor abordagem é garantir que o JS SEMPRE envie o mesmo nome de token CSRF que o PHP espera para a ação.
    // Para o JS atual, ele está enviando 'csrf_token_op'.
    // Vamos ajustar o PHP para esperar 'csrf_token_op' para a ação de excluir.
    // No JS, na função procederComExclusaoConteudo, o token é adicionado como formData.append('csrf_token_op', csrfToken.value);
    $csrf_token_post_name = 'csrf_token_op'; 
}

if (!isset($_POST[$csrf_token_post_name]) || !hash_equals($_SESSION['csrf_token'], $_POST[$csrf_token_post_name])) {
    $response['message'] = 'Falha na verificação CSRF. Recarregue a página e tente novamente.';
    
    $log_message = "Falha CSRF ao processar conteúdo edital. Acao: " . $acao . ". ";
    $log_message .= "Token SESSAO: " . (isset($_SESSION['csrf_token']) ? $_SESSION['csrf_token'] : 'SESSAO_TOKEN_NAO_DEFINIDO') . ". ";
    $log_message .= "Token POST Esperado (" . $csrf_token_post_name . "): ";
    $log_message .= (isset($_POST[$csrf_token_post_name]) ? $_POST[$csrf_token_post_name] : 'POST_TOKEN_NAO_RECEBIDO');
    // error_log($log_message); // Comentado para teste

    echo json_encode($response);
    exit;
}

// Ação já foi determinada acima
// Obter dados do POST
$id_edital_fk = isset($_POST['id_edital_fk']) ? (int)$_POST['id_edital_fk'] : null;
$materia_id = isset($_POST['materia_id']) ? (int)$_POST['materia_id'] : null;
$descricao = isset($_POST['descricao']) ? trim($_POST['descricao']) : null;
$capitulo = isset($_POST['capitulo']) ? trim($_POST['capitulo']) : null;
$ordem = isset($_POST['ordem']) && $_POST['ordem'] !== '' ? (int)$_POST['ordem'] : null; // Ordem pode ser 0

// Para edição, precisamos do ID do conteúdo
$id_conteudo_editar = null;
if ($acao === 'editar') {
    $id_conteudo_editar = isset($_POST['id_conteudo_editar']) ? (int)$_POST['id_conteudo_editar'] : null;
    // CSRF token para editar é 'csrf_token_conteudo_editar'
    // A verificação CSRF geral já foi feita, mas se quiser tokens por ação, ajuste aqui.
}


// Validações
if ($acao === 'editar' && empty($id_conteudo_editar)) {
    $response['message'] = 'ID do Conteúdo para edição não fornecido.';
    echo json_encode($response);
    exit;
}

if ($acao === 'adicionar' && empty($id_edital_fk)) { 
    $response['message'] = 'ID do Edital não fornecido para novo conteúdo.';
    echo json_encode($response);
    exit;
}

// Validações comuns APENAS para adicionar e editar
if ($acao === 'adicionar' || $acao === 'editar') {
    if (empty($materia_id)) {
        $response['message'] = 'Matéria é obrigatória.';
        echo json_encode($response);
        exit;
    }
    // A validação de materia_id já está aqui dentro e correta.
    // Agora, adicionamos a validação de descricao:
    if (empty($descricao)) {
        $response['message'] = 'Descrição do conteúdo é obrigatória.';
        echo json_encode($response);
        exit;
    }
}

// O if (empty($descricao)) { ... } original foi movido para cima e não existe mais aqui.

if ($acao === 'adicionar') {
    $query = "INSERT INTO appestudo.conteudo_edital (edital_id, materia_id, descricao, capitulo, ordem) 
                VALUES ($1, $2, $3, $4, $5) RETURNING id_conteudo";
    $params = [
        $id_edital_fk,
        $materia_id,
        $descricao,
        (empty($capitulo) ? null : $capitulo),
        ($ordem === null ? null : $ordem)
    ];

    try {
        $resultado = pg_query_params($conexao, $query, $params);
        if ($resultado) {
            $novo_conteudo = pg_fetch_assoc($resultado);
            if ($novo_conteudo && isset($novo_conteudo['id_conteudo'])){
                $response['success'] = true;
                $response['message'] = 'Conteúdo adicionado com sucesso!';
                $response['id_conteudo_novo'] = $novo_conteudo['id_conteudo'];
            } else {
                $response['message'] = 'Erro ao obter ID do novo conteúdo.';
                 error_log("Ao adicionar conteúdo (sem ID retornado): " . pg_last_error($conexao));
            }
        } else {
            $response['message'] = 'Erro ao adicionar conteúdo: ' . pg_last_error($conexao);
            error_log("Erro SQL ao adicionar conteúdo: " . pg_last_error($conexao) . " Query: " . $query . " Params: " . print_r($params, true));
        }
    } catch (Exception $e) {
        $response['message'] = 'Exceção ao adicionar: ' . $e->getMessage();
        error_log("Exceção em processar_conteudo_edital (adicionar): " . $e->getMessage());
    }

} elseif ($acao === 'editar') {
    // Note: edital_id não é atualizado aqui, pois o conteúdo pertence a um edital específico.
    // Se precisar mover conteúdo entre editais, seria uma lógica diferente.
    $query = "UPDATE appestudo.conteudo_edital 
                SET materia_id = $1, descricao = $2, capitulo = $3, ordem = $4
                WHERE id_conteudo = $5";
    $params = [
        $materia_id,
        $descricao,
        (empty($capitulo) ? null : $capitulo),
        ($ordem === null ? null : $ordem),
        $id_conteudo_editar
    ];

    try {
        $resultado = pg_query_params($conexao, $query, $params);
        if ($resultado) {
            if (pg_affected_rows($resultado) > 0) {
                $response['success'] = true;
                $response['message'] = 'Conteúdo atualizado com sucesso!';
            } else {
                // Isso pode acontecer se os dados enviados forem idênticos aos já existentes
                // ou se o id_conteudo não existir (embora a validação de ID devesse pegar isso antes)
                $response['success'] = true; // Considerar sucesso se não houver erro e nenhuma linha afetada (dados iguais)
                $response['message'] = 'Nenhuma alteração detectada ou conteúdo não encontrado com o ID fornecido.';
            }
        } else {
            $response['message'] = 'Erro ao atualizar conteúdo: ' . pg_last_error($conexao);
            error_log("Erro SQL ao atualizar conteúdo: " . pg_last_error($conexao) . " Query: " . $query . " Params: " . print_r($params, true));
        }
    } catch (Exception $e) {
        $response['message'] = 'Exceção ao atualizar: ' . $e->getMessage();
        error_log("Exceção em processar_conteudo_edital (editar): " . $e->getMessage());
    }
} elseif ($acao === 'excluir') {
    $id_conteudo_excluir = isset($_POST['id_conteudo_excluir']) ? (int)$_POST['id_conteudo_excluir'] : null;

    if (empty($id_conteudo_excluir)) {
        $response['message'] = 'ID do Conteúdo para exclusão não fornecido.';
    } else {
        $query = "DELETE FROM appestudo.conteudo_edital WHERE id_conteudo = $1";
        $params = [$id_conteudo_excluir];

        try {
            $resultado = pg_query_params($conexao, $query, $params);
            if ($resultado) {
                if (pg_affected_rows($resultado) > 0) {
                    $response['success'] = true;
                    $response['message'] = 'Conteúdo excluído com sucesso!';
                } else {
                    $response['message'] = 'Nenhum conteúdo encontrado com o ID fornecido para exclusão, ou já foi excluído.';
                    // Não necessariamente um erro se o objetivo era garantir que não existisse.
                    // $response['success'] = true; // Pode considerar sucesso se não deu erro SQL.
                }
            } else {
                $response['message'] = 'Erro ao excluir conteúdo: ' . pg_last_error($conexao);
                error_log("Erro SQL ao excluir conteúdo: " . pg_last_error($conexao) . " Query: " . $query . " Params: " . print_r($params, true));
            }
        } catch (Exception $e) {
            $response['message'] = 'Exceção ao excluir: ' . $e->getMessage();
            error_log("Exceção em processar_conteudo_edital (excluir): " . $e->getMessage());
        }
    }
}
// O catch global desalinhado foi removido.

pg_close($conexao);
echo json_encode($response);
exit;
?>