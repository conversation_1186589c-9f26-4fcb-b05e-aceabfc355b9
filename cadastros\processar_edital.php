<?php
// Desativar a exibição de erros para evitar que eles contaminem a saída JSON
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Definir o cabeçalho de conteúdo JSON logo no início
header('Content-Type: application/json');

// Iniciar o buffer de saída para controlar o que é enviado ao cliente
ob_start();

session_start();
require_once("assets/config.php");
require_once("includes/verify_admin.php");

// Função para registrar erros em um arquivo de log
function debug($mensagem, $dados = null) {
    $log = date('Y-m-d H:i:s') . ' - ' . $mensagem;
    if ($dados !== null) {
        $log .= ' - ' . print_r($dados, true);
    }
    error_log($log);
}

// Função para enviar resposta JSON e encerrar o script
function enviarResposta($sucesso, $mensagem) {
    // Limpar qualquer saída anterior
    ob_clean();
    
    // Enviar resposta JSON
    echo json_encode([
        'success' => $sucesso,
        'message' => $mensagem
    ]);
    
    // Encerrar o script
    exit;
}

// Verificar se o usuário é administrador
try {
    verificarAcessoAdmin($conexao, true);
} catch (Exception $e) {
    enviarResposta(false, 'Acesso negado: ' . $e->getMessage());
}

// Função para validar dados do edital
function validarDadosEdital($dados) {
    if (empty($dados['nome'])) {
        throw new Exception("O nome do edital é obrigatório.");
    }
    
    if (empty($dados['ano']) || !is_numeric($dados['ano'])) {
        throw new Exception("O ano do edital é obrigatório e deve ser um número.");
    }
    
    if (empty($dados['orgao'])) {
        throw new Exception("O órgão do edital é obrigatório.");
    }
}

// Função para processar upload de imagem
function processarUploadImagem($arquivo) {
    if (!isset($arquivo) || $arquivo['error'] != 0) {
        return null;
    }
    
    // Verificar o tipo de arquivo
    $tipos_permitidos = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!in_array($arquivo['type'], $tipos_permitidos)) {
        throw new Exception("Tipo de arquivo não permitido. Apenas imagens JPG, PNG, GIF e WEBP são aceitas.");
    }
    
    // Verificar o tamanho do arquivo (máximo 2MB)
    $tamanho_maximo = 2 * 1024 * 1024; // 2MB em bytes
    if ($arquivo['size'] > $tamanho_maximo) {
        throw new Exception("O arquivo é muito grande. Tamanho máximo permitido: 2MB.");
    }
    
    // Definir o caminho para o diretório de uploads
    // Caminho absoluto no servidor
    $uploadDir = $_SERVER['DOCUMENT_ROOT'] . '/cronograma_inteligente/uploads/logos/';
    
    // Verificar se o diretório existe, se não, tentar criá-lo
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0777, true)) {
            throw new Exception("Não foi possível criar o diretório de uploads. Verifique as permissões.");
        }
    }
    
    // Gerar um nome único para o arquivo
    $timestamp = time();
    $extensao = pathinfo($arquivo['name'], PATHINFO_EXTENSION);
    $fileName = 'logo_' . $timestamp . '_' . uniqid() . '.' . $extensao;
    $uploadFile = $uploadDir . $fileName;
    
    // Mover o arquivo para o diretório de destino
    if (!move_uploaded_file($arquivo['tmp_name'], $uploadFile)) {
        throw new Exception("Falha ao fazer upload do arquivo. Verifique as permissões do diretório.");
    }
    
    // Retornar o caminho relativo para armazenar no banco de dados
    // Este é o caminho que será usado nas URLs
    return '/cronograma_inteligente/uploads/logos/' . $fileName;
}

// Determinar a ação a ser executada
$acao = $_GET['acao'] ?? ($_POST['acao'] ?? 'cadastrar');

try {
    // Cadastrar novo edital
    if ($acao === 'cadastrar') {
        try {
            // Validar dados
            validarDadosEdital($_POST);
            
            // Processar upload da logo (se houver)
            $logo_url = null;
            if (isset($_FILES['logo']) && $_FILES['logo']['error'] == 0) {
                $logo_url = processarUploadImagem($_FILES['logo']);
            }
            
            // Sanitizar dados
            $nome = $_POST['nome'];
            $descricao = $_POST['descricao'] ?? '';
            $ano = intval($_POST['ano']);
            $orgao = $_POST['orgao'];
            
            // Inserir no banco de dados
            $query = "INSERT INTO appestudo.edital (nome, descricao, ano, orgao, logo_url) 
                      VALUES ($1, $2, $3, $4, $5) 
                      RETURNING id_edital";
            
            $resultado = pg_query_params(
                $conexao, 
                $query, 
                array($nome, $descricao, $ano, $orgao, $logo_url)
            );
            
            if (!$resultado) {
                throw new Exception("Erro ao cadastrar edital: " . pg_last_error($conexao));
            }
            
            enviarResposta(true, 'Edital cadastrado com sucesso!');
        } catch (Exception $e) {
            enviarResposta(false, $e->getMessage());
        }
    }
    
    // Editar edital existente
    else if ($acao === 'editar') {
        try {
            // Validar dados
            validarDadosEdital($_POST);
            
            // Verificar se o ID foi fornecido
            if (!isset($_POST['id_edital']) || empty($_POST['id_edital'])) {
                throw new Exception("ID do edital não fornecido.");
            }
            
            // Sanitizar dados
            $id_edital = intval($_POST['id_edital']);
            $nome = $_POST['nome'];
            $descricao = $_POST['descricao'] ?? '';
            $ano = intval($_POST['ano']);
            $orgao = $_POST['orgao'];
            
            // Verificar se o edital existe
            $query_verificar = "SELECT logo_url FROM appestudo.edital WHERE id_edital = $1";
            $resultado_verificar = pg_query_params($conexao, $query_verificar, array($id_edital));
            
            if (pg_num_rows($resultado_verificar) == 0) {
                throw new Exception("Edital não encontrado.");
            }
            
            $edital_atual = pg_fetch_assoc($resultado_verificar);
            $logo_url = $edital_atual['logo_url'];
            
            // Processar upload da nova logo (se houver)
            if (isset($_FILES['logo']) && $_FILES['logo']['error'] == 0) {
                // Processar o upload da nova logo
                $novo_logo_url = processarUploadImagem($_FILES['logo']);
                
                // Se o upload foi bem-sucedido, atualizar o URL da logo
                if ($novo_logo_url) {
                    $logo_url = $novo_logo_url;
                }
            }
            
            // Atualizar no banco de dados
            $query = "UPDATE appestudo.edital 
                      SET nome = $1, descricao = $2, ano = $3, orgao = $4, logo_url = $5 
                      WHERE id_edital = $6";
            
            $resultado = pg_query_params(
                $conexao, 
                $query, 
                array($nome, $descricao, $ano, $orgao, $logo_url, $id_edital)
            );
            
            if (!$resultado) {
                throw new Exception("Erro ao atualizar edital: " . pg_last_error($conexao));
            }
            
            enviarResposta(true, 'Edital atualizado com sucesso!');
        } catch (Exception $e) {
            enviarResposta(false, $e->getMessage());
        }
    }
    
    // Excluir edital
    else if ($acao === 'excluir') {
        try {
            // Verificar se o ID foi fornecido
            if (!isset($_POST['id_edital']) || empty($_POST['id_edital'])) {
                throw new Exception("ID do edital não fornecido.");
            }
            
            // Sanitizar o ID
            $id_edital = intval($_POST['id_edital']);
            
            // Verificar se o edital existe
            $query_verificar = "SELECT logo_url FROM appestudo.edital WHERE id_edital = $1";
            $resultado_verificar = pg_query_params($conexao, $query_verificar, array($id_edital));
            
            if (pg_num_rows($resultado_verificar) == 0) {
                throw new Exception("Edital não encontrado.");
            }
            
            // Iniciar transação
            pg_query($conexao, "BEGIN");
            
            // Excluir do banco de dados
            $query = "DELETE FROM appestudo.edital WHERE id_edital = $1";
            $resultado = pg_query_params($conexao, $query, array($id_edital));
            
            if (!$resultado) {
                pg_query($conexao, "ROLLBACK");
                throw new Exception("Erro ao excluir edital: " . pg_last_error($conexao));
            }
            
            pg_query($conexao, "COMMIT");
            
            enviarResposta(true, 'Edital excluído com sucesso!');
        } catch (Exception $e) {
            if (isset($conexao)) {
                pg_query($conexao, "ROLLBACK");
            }
            enviarResposta(false, $e->getMessage());
        }
    }
    
    // Ação inválida
    else {
        enviarResposta(false, 'Ação inválida.');
    }
} catch (Exception $e) {
    enviarResposta(false, $e->getMessage());
}
?>


