<?php
session_start();
require_once('../includes/auth.php');
require_once('../conexao_POST.php');
require_once('../cadastros/includes/verify_admin.php');

// Verifica se é o super admin
verificarSuperAdmin($conexao, false);

function executarLimpeza($conexao, $dias, $tipo_evento = null) {
    try {
        if ($tipo_evento) {
            $query = "DELETE FROM appEstudo.log_seguranca 
                     WHERE data_evento < CURRENT_DATE - INTERVAL '$dias days'
                     AND tipo_evento = $1
                     RETURNING COUNT(*)";
            $result = pg_query_params($conexao, $query, array($tipo_evento));
        } else {
            $query = "DELETE FROM appEstudo.log_seguranca 
                     WHERE data_evento < CURRENT_DATE - INTERVAL '$dias days'
                     RETURNING COUNT(*)";
            $result = pg_query($conexao, $query);
        }
        
        $count = pg_fetch_result($result, 0, 0);
        return $count;
    } catch (Exception $e) {
        error_log("Erro na limpeza dos logs: " . $e->getMessage());
        return false;
    }
}

function obterEstatisticasDetalhadas($conexao) {
    $stats = array();
    
    // Tamanho da tabela
    $query = "SELECT pg_size_pretty(pg_total_relation_size('appEstudo.log_seguranca')) as tamanho";
    $result = pg_query($conexao, $query);
    $stats['tamanho'] = pg_fetch_result($result, 0, 0);
    
    // Total de registros
    $query = "SELECT COUNT(*) FROM appEstudo.log_seguranca";
    $result = pg_query($conexao, $query);
    $stats['total'] = pg_fetch_result($result, 0, 0);
    
    // Estatísticas por tipo de evento
    $query = "SELECT 
                tipo_evento, 
                COUNT(*) as total,
                MIN(data_evento) as primeiro_registro,
                MAX(data_evento) as ultimo_registro,
                COUNT(DISTINCT usuario_id) as usuarios_unicos,
                COUNT(DISTINCT ip) as ips_unicos
              FROM appEstudo.log_seguranca 
              GROUP BY tipo_evento";
    $result = pg_query($conexao, $query);
    $stats['por_tipo'] = array();
    while ($row = pg_fetch_assoc($result)) {
        $stats['por_tipo'][$row['tipo_evento']] = $row;
    }
    
    // Top 10 IPs com mais acessos
    $query = "SELECT 
                ip, 
                COUNT(*) as total,
                array_agg(DISTINCT tipo_evento) as tipos_evento,
                MAX(data_evento) as ultimo_acesso
              FROM appEstudo.log_seguranca 
              GROUP BY ip 
              ORDER BY total DESC 
              LIMIT 10";
    $result = pg_query($conexao, $query);
    $stats['top_ips'] = array();
    while ($row = pg_fetch_assoc($result)) {
        $stats['top_ips'][] = $row;
    }
    
    // Top 10 usuários com mais eventos
    $query = "SELECT 
                ls.usuario_id,
                u.nome,
                COUNT(*) as total,
                array_agg(DISTINCT tipo_evento) as tipos_evento,
                MAX(data_evento) as ultimo_evento
              FROM appEstudo.log_seguranca ls
              LEFT JOIN appEstudo.usuario u ON ls.usuario_id = u.idusuario
              GROUP BY ls.usuario_id, u.nome
              ORDER BY total DESC 
              LIMIT 10";
    $result = pg_query($conexao, $query);
    $stats['top_usuarios'] = array();
    while ($row = pg_fetch_assoc($result)) {
        $stats['top_usuarios'][] = $row;
    }
    
    // Estatísticas por período
    $query = "SELECT 
                date_trunc('day', data_evento) as dia,
                COUNT(*) as total,
                COUNT(DISTINCT usuario_id) as usuarios_unicos,
                COUNT(DISTINCT ip) as ips_unicos
              FROM appEstudo.log_seguranca 
              WHERE data_evento >= CURRENT_DATE - INTERVAL '30 days'
              GROUP BY dia 
              ORDER BY dia DESC";
    $result = pg_query($conexao, $query);
    $stats['ultimos_30_dias'] = array();
    while ($row = pg_fetch_assoc($result)) {
        $stats['ultimos_30_dias'][] = $row;
    }
    
    return $stats;
}

$mensagem = '';
$stats = obterEstatisticasDetalhadas($conexao);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['limpar'])) {
        // Backup antes da limpeza (opcional)
        $data_backup = date('Y-m-d_H-i-s');
        $query_backup = "COPY appEstudo.log_seguranca TO '/tmp/backup_logs_$data_backup.csv' WITH CSV HEADER";
        pg_query($conexao, $query_backup);
        
        // Executa limpeza
        $logs_sucesso = executarLimpeza($conexao, 30, 'login_sucesso');
        $logs_falha = executarLimpeza($conexao, 90, 'login_falha');
        
        if ($logs_sucesso !== false && $logs_falha !== false) {
            $mensagem = "Limpeza concluída! Removidos $logs_sucesso logs de sucesso e $logs_falha logs de falha.";
            $stats = obterEstatisticasDetalhadas($conexao); // Atualiza estatísticas
        } else {
            $mensagem = "Erro ao executar a limpeza. Verifique os logs do servidor.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manutenção de Logs</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Quicksand', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }
        .btn {
            background: #00008B;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-family: 'Quicksand', sans-serif;
        }
        .btn:hover {
            background: #000066;
        }
        .message {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            padding: 10px;
            border: 1px solid #dee2e6;
            text-align: left;
        }
        th {
            background: #f8f9fa;
        }
        .section {
            margin-bottom: 30px;
        }
        .chart {
            width: 100%;
            height: 300px;
            margin: 20px 0;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container">
        <h1>Manutenção de Logs do Sistema</h1>
        
        <?php if ($mensagem): ?>
            <div class="message"><?php echo htmlspecialchars($mensagem); ?></div>
        <?php endif; ?>

        <div class="section">
            <h2>Informações Gerais</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>Tamanho da Tabela</h3>
                    <p><?php echo htmlspecialchars($stats['tamanho']); ?></p>
                </div>
                <div class="stat-card">
                    <h3>Total de Registros</h3>
                    <p><?php echo number_format($stats['total']); ?></p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>Estatísticas por Tipo de Evento</h2>
            <table>
                <thead>
                    <tr>
                        <th>Tipo de Evento</th>
                        <th>Total</th>
                        <th>Primeiro Registro</th>
                        <th>Último Registro</th>
                        <th>Usuários Únicos</th>
                        <th>IPs Únicos</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($stats['por_tipo'] as $tipo => $dados): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($tipo); ?></td>
                        <td><?php echo number_format($dados['total']); ?></td>
                        <td><?php echo date('d/m/Y H:i:s', strtotime($dados['primeiro_registro'])); ?></td>
                        <td><?php echo date('d/m/Y H:i:s', strtotime($dados['ultimo_registro'])); ?></td>
                        <td><?php echo number_format($dados['usuarios_unicos']); ?></td>
                        <td><?php echo number_format($dados['ips_unicos']); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>Top 10 IPs</h2>
            <table>
                <thead>
                    <tr>
                        <th>IP</th>
                        <th>Total de Eventos</th>
                        <th>Tipos de Evento</th>
                        <th>Último Acesso</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($stats['top_ips'] as $ip): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($ip['ip']); ?></td>
                        <td><?php echo number_format($ip['total']); ?></td>
                        <td><?php echo htmlspecialchars(implode(', ', array_unique(explode(',', trim($ip['tipos_evento'], '{}'))))); ?></td>
                        <td><?php echo date('d/m/Y H:i:s', strtotime($ip['ultimo_acesso'])); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>Top 10 Usuários</h2>
            <table>
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Nome</th>
                        <th>Total de Eventos</th>
                        <th>Tipos de Evento</th>
                        <th>Último Evento</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($stats['top_usuarios'] as $usuario): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($usuario['usuario_id']); ?></td>
                        <td><?php echo htmlspecialchars($usuario['nome'] ?? 'N/A'); ?></td>
                        <td><?php echo number_format($usuario['total']); ?></td>
                        <td><?php echo htmlspecialchars(implode(', ', array_unique(explode(',', trim($usuario['tipos_evento'], '{}'))))); ?></td>
                        <td><?php echo date('d/m/Y H:i:s', strtotime($usuario['ultimo_evento'])); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>Atividade nos Últimos 30 Dias</h2>
            <canvas id="activityChart" class="chart"></canvas>
        </div>

        <form method="POST" onsubmit="return confirm('Tem certeza que deseja limpar os logs antigos? Esta ação não pode ser desfeita.');">
            <button type="submit" name="limpar" class="btn">Limpar Logs Antigos</button>
        </form>

        <a href="index.php" class="btn" style="background: #6c757d; margin-top: 20px; display: inline-block;">
            <i class="fas fa-arrow-left"></i> Voltar
        </a>
    </div>

    <script>
        // Gráfico de atividade
        const ctx = document.getElementById('activityChart').getContext('2d');
        const data = <?php echo json_encode($stats['ultimos_30_dias']); ?>;
        
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(item => new Date(item.dia).toLocaleDateString()),
                datasets: [{
                    label: 'Total de Eventos',
                    data: data.map(item => item.total),
                    borderColor: '#00008B',
                    tension: 0.1
                }, {
                    label: 'Usuários Únicos',
                    data: data.map(item => item.usuarios_unicos),
                    borderColor: '#4CAF50',
                    tension: 0.1
                }, {
                    label: 'IPs Únicos',
                    data: data.map(item => item.ips_unicos),
                    borderColor: '#FFA500',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>





