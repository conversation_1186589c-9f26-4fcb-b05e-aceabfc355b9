<div class="ciclo">
    <!-- Modal do Editor de Ciclo -->
    <div id="modal-ciclo" style="display:none; position:fixed; z-index:99999; left:0; top:0; width:100%; height:100%; background:rgba(30,41,59,0.18); align-items:center; justify-content:center; backdrop-filter: blur(1px);">
        <div style="background:#fff; border-radius:16px; box-shadow:0 8px 32px rgba(0,0,0,0.18); width:95%; max-width:650px; min-height:600px; position:relative; overflow:hidden; margin: 20px auto; transform: translateY(-20px);">
            <button onclick="fecharModalCiclo()" class="modal-close" style="position: absolute; top: 1rem; right: 1rem; background: var(--danger-color); border: none; width: 32px; height: 32px; border-radius: 50%; color: white; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.3s ease;">&times;</button>
            <iframe id="iframe-ciclo" src="" style="width:100%; height:95vh; border:none; border-radius:16px;"></iframe>
        </div>
    </div>

    <style>
        .ciclo-header {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
            position: relative;
        }

        .btn-editar-ciclo {
            background: linear-gradient(145deg, #f8f6f1, #e8e4d9);
            border: 1px solid #2563eb;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-family: 'Quicksand', sans-serif;
            color: #1e3a8a;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
        }

        .btn-editar-ciclo::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(
                    to bottom,
                    rgba(255, 255, 255, 0.1) 0%,
                    rgba(255, 255, 255, 0.2) 50%,
                    rgba(255, 255, 255, 0) 100%
            );
            transition: all 0.3s ease;
        }

        .btn-editar-ciclo:hover {
            background: linear-gradient(145deg, #dbeafe, #dbeafe);
            color: #1e3a8a;
            border-color: #2563eb;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            transform: translateY(-1px);
        }

        .btn-editar-ciclo:hover::before {
            opacity: 0;
        }

        .btn-editar-ciclo i {
            font-size: 14px;
            color: inherit;
            transition: transform 0.3s ease;
        }

        .btn-editar-ciclo:hover i {
            transform: rotate(180deg);
        }

        /* Adicionar decoração vintage nas extremidades do botão */
        .btn-editar-ciclo::after {
            content: '•';
            position: absolute;
            color: #1e3a8a;
            font-size: 18px;
            opacity: 0.5;
        }

        .btn-editar-ciclo::before {
            left: 8px;
        }

        .btn-editar-ciclo::after {
            right: 8px;
        }

        /* Efeito de papel antigo */
        .btn-editar-ciclo {
            background-image:
                    linear-gradient(to right, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.1) 100%),
                    url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noise'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100' height='100' filter='url(%23noise)' opacity='0.05'/%3E%3C/svg%3E");
        }

        /* Efeito de pressionar o botão */
        .btn-editar-ciclo:active {
            transform: translateY(1px);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
                }
        
                .status-message h4,
                .status-message p {
                    text-align: justify;
                }

        @media (max-width: 600px) {
            #ciclo-roda { width: 98vw !important; height: 98vw !important; min-width: 0 !important; min-height: 0 !important; }
        }
        .nome-materia-externo {
            font-size: 15px !important;
            font-weight: bold !important;
            background: rgba(255,255,255,0.97) !important;
            border-radius: 14px !important;
            padding: 6px 10px !important;
            box-shadow: 0 3px 10px rgba(30,41,59,0.10), 0 1px 4px rgba(0,0,0,0.07) !important;
            color: #222 !important;
            min-width: 90px !important;
            min-height: 26px !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            transition: transform 0.18s, box-shadow 0.18s, border 0.18s !important;
            z-index: 2;
        }
        .nome-materia-externo:hover, .nome-materia-externo:focus {
            transform: scale(1.10);
            box-shadow: 0 6px 18px rgba(30,144,255,0.13), 0 2px 8px rgba(0,0,0,0.10);
            border: 2px solid #00008B !important;
            outline: none;
        }
        .linha-materia {
            border-top-width: 1.2px !important;
            opacity: 0.45 !important;
            z-index: 1;
        }
        .ciclo-tooltip-materia {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            background: rgba(255,255,255,0.98);
            color: #1e293b;
            font-size: 1.1rem;
            font-weight: bold;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(30,41,59,0.10);
            padding: 12px 22px;
            pointer-events: none;
            z-index: 10;
            white-space: pre-line;
            text-align: center;
            opacity: 0;
            transition: opacity 0.18s;
        }
        .ciclo-tooltip-materia.ativo {
            opacity: 1;
        }
        .link-conteudo a {
            color: #00008B;
            text-decoration: none;
            border-bottom: 1px dotted #00008B;
            transition: all 0.2s ease;
            word-break: break-all;
            display: inline-block;
            max-width: 100%;
            overflow-wrap: anywhere;
        }
    </style>
    <script>
        function abrirPopUpCiclo() {
            document.getElementById('modal-ciclo').style.display = 'flex';
            document.getElementById('iframe-ciclo').src = 'editor_ciclo.php';
        }

        function fecharModalCiclo() {
            document.getElementById('modal-ciclo').style.display = 'none';
            document.getElementById('iframe-ciclo').src = '';
        }

        // Função para atualizar o ciclo após salvar
        function atualizarCiclo() {
            window.location.reload();
        }

        function abrirModalMateriaPorId(idMateria, nome) {
            fetch('ultimo_estudo_materia.php?id=' + encodeURIComponent(idMateria))
                .then(res => res.json())
                .then(dados => {
                    if (dados.erro) {
                        abrirModal(`<p style="color:red;">${dados.erro}</p>`, nome);
                    } else {
                        function formatTime(t) {
                            if (!t) return '-';
                            if (typeof t === 'string' && t.match(/^\d{2}:\d{2}/)) return t;
                            let h = Math.floor(t / 3600);
                            let m = Math.floor((t % 3600) / 60);
                            return `${h}h${m.toString().padStart(2, '0')}min`;
                        }
                        let nomeCurso = dados.curso ? dados.curso.replace(/'/g, "\\'") : 'Gran Cursos';
                        let nomeMateria = dados.nome ? dados.nome.replace(/'/g, "\\'") : '';
                        let html = `
                            <div class="modal-header-estudo">
                                <h3>Último Registro de Estudo</h3>
                                <div class="tempo-total">
                                    <span class="tempo-label">Tempo Líquido:</span>
                                    <span class="tempo-valor">${formatTime(dados.tempo_liquido)}</span>
                                </div>
                            </div>
                            <div class="estudos-container">
                                <div class="estudo-item" style="border-left: 4px solid ${dados.cor || '#666'}">
                                    <div class="estudo-cabecalho">
                                        <h4 class="materia-nome">${dados.tipo || '-'}</h4>
                                        <div class="estudo-tempo">${formatTime(dados.tempo_liquido)}</div>
                                    </div>
                                    <div class="estudo-detalhes">
                                        <div class="detalhe-linha"><strong>Data:</strong> ${dados.data || '-'}</div>
                                        <div class="detalhe-linha"><strong>Hora Início:</strong> ${dados.tempo_inicio || '-'}</div>
                                        <div class="detalhe-linha"><strong>Hora Fim:</strong> ${dados.tempo_fim || '-'}</div>
                                        <div class="detalhe-linha"><strong>Tempo Bruto:</strong> ${formatTime(dados.tempo_bruto)}</div>
                                        <div class="detalhe-linha"><strong>Tempo Perdido:</strong> ${formatTime(dados.tempo_perdido)}</div>
                                        <div class="detalhe-linha"><strong>Método:</strong> ${dados.tipo || '-'}</div>
                                        <div class="detalhe-linha"><strong>Curso:</strong> ${dados.curso || '-'}</div>
                                        <div class="detalhe-linha"><strong>Questões Totais:</strong> ${dados.questoes_total || '-'}</div>
                                        <div class="detalhe-linha"><strong>Certas:</strong> ${dados.questoes_certas || '-'}</div>
                                        <div class="detalhe-linha"><strong>Erradas:</strong> ${dados.questoes_erradas || '-'}</div>
                                        <div class="detalhe-linha"><strong>Ponto Estudado:</strong> ${dados.ponto_estudado || '-'}</div>
                                        ${dados.link_conteudo ? `
                                            <div class="detalhe-linha link-conteudo">
                                                <strong>Link do Conteúdo:</strong> 
                                                <a href="${dados.link_conteudo}" target="_blank" style="color: #00008B; text-decoration: none; border-bottom: 1px dotted #00008B; transition: all 0.2s ease;">
                                                    ${dados.link_conteudo}
                                                    <i class="fas fa-external-link-alt" style="font-size: 0.8em; margin-left: 4px;"></i>
                                                </a>
                                            </div>
                                        ` : ''}
                                        <div class="detalhe-linha"><strong>Revisões:</strong> ${dados.revisoes || '-'}</div>
                                        <div class="detalhe-linha descricao"><strong>Observações:</strong> ${dados.observacao || '-'}</div>
                                    </div>
                                </div>
                                <div style="text-align:center; margin-top:18px;">
                                    <button class="btn-ciclo-navegacao" style="background: var(--primary); color: white; font-size: 1rem; padding: 10px 22px; border-radius: 12px; border: none; cursor: pointer; display: inline-flex; align-items: center; gap: 8px;" onclick="abrirCronometroComCampos('${nomeMateria}', '${nomeCurso}', '${dados.tipo ? dados.tipo.replace(/'/g, "\\'") : ''}', '${dados.link_conteudo ? encodeURIComponent(dados.link_conteudo) : ''}')">
                                        <i class='fas fa-play'></i> Estudar esta matéria
                                    </button>
                                </div>
                            </div>
                        `;
                        abrirModal(html, nome);
                    }
                })
                .catch(() => abrirModal('<p style="color:red;">Erro ao buscar dados.</p>', nome));
        }

        function desenharRodaComTooltip(materias, ultima, proxima) {
            const size = 340;
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            const ctx = canvas.getContext('2d');
            const cx = size/2, cy = size/2, r = 110, rInterno = 55;
            const total = materias.length;
            const anguloFatia = 2 * Math.PI / total;
            const container = document.getElementById('ciclo-roda');
            container.innerHTML = '';
            container.appendChild(canvas);
            Array.from(container.querySelectorAll('.ciclo-tooltip-materia')).forEach(e => e.remove());

            // Tooltip central
            const tooltip = document.createElement('div');
            tooltip.className = 'ciclo-tooltip-materia';
            tooltip.innerText = '';
            container.appendChild(tooltip);

            // Guardar as áreas das fatias para hover
            const fatias = [];
            materias.forEach((mat, i) => {
                const inicio = i * anguloFatia;
                const fim = inicio + anguloFatia;
                // Desenhar fatia
                ctx.beginPath();
                ctx.moveTo(cx, cy);
                ctx.arc(cx, cy, r, inicio, fim);
                ctx.closePath();
                ctx.fillStyle = mat.cor;
                ctx.globalAlpha = 0.85;
                ctx.fill();
                ctx.globalAlpha = 1;
                // Destaque para próxima
                if (mat.nome === proxima) {
                    ctx.save();
                    ctx.beginPath();
                    ctx.moveTo(cx, cy);
                    ctx.arc(cx, cy, r, inicio, fim);
                    ctx.closePath();
                    ctx.lineWidth = 7;
                    ctx.strokeStyle = '#1e90ff';
                    ctx.shadowColor = '#1e90ff';
                    ctx.shadowBlur = 10;
                    ctx.stroke();
                    ctx.restore();
                }
                // Destaque para última
                if (mat.nome === ultima) {
                    ctx.save();
                    ctx.beginPath();
                    ctx.moveTo(cx, cy);
                    ctx.arc(cx, cy, r, inicio, fim);
                    ctx.closePath();
                    ctx.lineWidth = 7;
                    ctx.strokeStyle = '#e76f51';
                    ctx.shadowColor = '#e76f51';
                    ctx.shadowBlur = 10;
                    ctx.stroke();
                    ctx.restore();
                }
                // Guardar área da fatia
                fatias.push({inicio, fim, nome: mat.nome});
            });

            // Círculo central
            ctx.beginPath();
            ctx.arc(cx, cy, rInterno, 0, 2 * Math.PI);
            ctx.fillStyle = '#fff';
            ctx.shadowColor = '#bbb';
            ctx.shadowBlur = 7;
            ctx.fill();
            ctx.shadowBlur = 0;
            ctx.font = 'bold 17px Quicksand, Arial';
            ctx.fillStyle = '#1e293b';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText('Ciclo', cx, cy - 10);
            ctx.font = '13px Quicksand, Arial';
            ctx.fillText('de Estudo', cx, cy + 12);

            // Interatividade: mostrar tooltip ao passar o mouse
            canvas.addEventListener('mousemove', function(e) {
                const rect = canvas.getBoundingClientRect();
                const mx = e.clientX - rect.left;
                const my = e.clientY - rect.top;
                const dx = mx - cx;
                const dy = my - cy;
                const dist = Math.sqrt(dx*dx + dy*dy);
                if (dist < rInterno || dist > r) {
                    tooltip.classList.remove('ativo');
                    tooltip.innerText = '';
                    canvas.style.cursor = 'default';
                    return;
                }
                let ang = Math.atan2(dy, dx);
                if (ang < 0) ang += 2*Math.PI;
                let found = false;
                for (const fatia of fatias) {
                    if (ang >= fatia.inicio && ang < fatia.fim) {
                        tooltip.innerText = fatia.nome;
                        tooltip.classList.add('ativo');
                        canvas.style.cursor = 'pointer';
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    tooltip.classList.remove('ativo');
                    tooltip.innerText = '';
                    canvas.style.cursor = 'default';
                }
            });
            canvas.addEventListener('mouseleave', function() {
                tooltip.classList.remove('ativo');
                tooltip.innerText = '';
                canvas.style.cursor = 'default';
            });
            // Clique na fatia
            canvas.addEventListener('click', function(e) {
                const rect = canvas.getBoundingClientRect();
                const mx = e.clientX - rect.left;
                const my = e.clientY - rect.top;
                const dx = mx - cx;
                const dy = my - cy;
                const dist = Math.sqrt(dx*dx + dy*dy);
                if (dist < rInterno || dist > r) return;
                let ang = Math.atan2(dy, dx);
                if (ang < 0) ang += 2*Math.PI;
                for (const fatia of fatias) {
                    if (ang >= fatia.inicio && ang < fatia.fim) {
                        abrirModalMateriaPorId(materias[idx].id, materias[idx].nome);
                        break;
                    }
                }
            });
        }

        function cicloBindBotoes() {
            const btnUltima = document.getElementById('btn-ultima-estudada');
            const btnProxima = document.getElementById('btn-proxima-estudada');
            if (btnUltima && !btnUltima.dataset.cicloBound) {
                btnUltima.onclick = () => renderCicloChart(true, false);
                btnUltima.dataset.cicloBound = '1';
            }
            if (btnProxima && !btnProxima.dataset.cicloBound) {
                btnProxima.onclick = () => renderCicloChart(false, true);
                btnProxima.dataset.cicloBound = '1';
            }
        }
        cicloBindBotoes();
        setTimeout(cicloBindBotoes, 500);
        const observer = new MutationObserver(cicloBindBotoes);
        observer.observe(document.body, { childList: true, subtree: true });

        // Função para verificar e abrir o cronômetro
        function verificarEAbrirCronometro(materia, nomeCurso) {
            // Sempre envie o parâmetro curso, mesmo que seja o valor padrão
            nomeCurso = nomeCurso || 'Gran Cursos';
            let url = `0cronometro.php?materia=${encodeURIComponent(materia)}&curso=${encodeURIComponent(nomeCurso)}`;
            const largura = 1000;
            const altura = 800;
            const left = (screen.width - largura) / 2;
            const top = (screen.height - altura) / 2;
            window.open(url, '_blank', 
                `width=${largura},height=${altura},left=${left},top=${top},` +
                'scrollbars=yes,status=no,toolbar=no,location=no,directories=no,menubar=no,resizable=yes,fullscreen=no'
            );
        }

        // Função para abrir o cronômetro com a próxima matéria
        function abrirCronometroProximaMateria() {
            const proximaMateria = <?php echo json_encode($proximaMateriaEstudada ?? $proximoMateriaNome); ?>;
            let idProximaMateria = <?php echo json_encode($id_proxima_materia); ?>;

            if (proximaMateria && idProximaMateria) {
                // Buscar o nome do curso, método e link do conteúdo via AJAX
                fetch('ultimo_estudo_materia.php?id=' + encodeURIComponent(idProximaMateria))
                    .then(res => res.json())
                    .then(dados => {
                        let nomeCurso = dados.curso ? dados.curso.replace(/'/g, "\\'") : '';
                        let metodo = dados.tipo ? dados.tipo.replace(/'/g, "\\'") : '';
                        let linkConteudo = dados.link_conteudo ? encodeURIComponent(dados.link_conteudo) : '';
                        let url = `0cronometro.php?materia=${encodeURIComponent(proximaMateria)}&curso=${encodeURIComponent(nomeCurso)}&metodo=${encodeURIComponent(metodo)}&link_conteudo=${linkConteudo}`;
                        const largura = 1000;
                        const altura = 800;
                        const left = (screen.width - largura) / 2;
                        const top = (screen.height - altura) / 2;
                        window.open(url, '_blank', 
                            `width=${largura},height=${altura},left=${left},top=${top},` +
                            'scrollbars=yes,status=no,toolbar=no,location=no,directories=no,menubar=no,resizable=yes,fullscreen=no'
                        );
                    })
                    .catch(() => {
                        // Se der erro, abre com curso vazio
                        verificarEAbrirCronometro(proximaMateria, '');
                    });
            } else {
                alert('Não há próxima matéria definida no ciclo.');
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            var btn = document.getElementById('btn-estudar-proxima');
            if (btn) {
                btn.addEventListener('click', abrirCronometroProximaMateria);
            }
        });

        // Adicionar função JS para abrir o cronômetro com todos os campos preenchidos
        function abrirCronometroComCampos(materia, curso, metodo, link_conteudo) {
            let url = `0cronometro.php?materia=${encodeURIComponent(materia)}&curso=${encodeURIComponent(curso)}&metodo=${encodeURIComponent(metodo)}&link_conteudo=${link_conteudo}`;
            const largura = 1000;
            const altura = 800;
            const left = (screen.width - largura) / 2;
            const top = (screen.height - altura) / 2;
            window.open(url, '_blank', 
                `width=${largura},height=${altura},left=${left},top=${top},` +
                'scrollbars=yes,status=no,toolbar=no,location=no,directories=no,menubar=no,resizable=yes,fullscreen=no'
            );
        }
    </script>


    <div class="card-planejamento">
        <div class="card-header" style="margin-bottom: 15px;">
            <h3>Ciclo de Estudo </h3>
        </div>
        <div class="ciclo-header">
            <button onclick="abrirPopUpCiclo()" class="btn-editar-ciclo">
                <i class="fas fa-sort"></i>
                Ordenar Matérias
            </button>
        </div>
        <?php if ($id_proxima_materia === null): ?>
            <div class="ciclo-status not-started">
                <div class="status-icon">
                    <i class="fas fa-hourglass-start"></i>
                </div>
                <div class="status-message">
                    <h4>Ciclo Não Iniciado</h4>
                    <p>Comece seus estudos para iniciar o ciclo, estude uma matéria para começar</p>
                </div>
            </div>
        <?php else: ?>
            <div class="ciclo-materias" style="display: flex; flex-direction: column; align-items: center;">
                <div style="width: 320px; height: 320px; position: relative;">
                    <canvas id="cicloChart" style="width: 100% !important; height: 100% !important;"></canvas>
                    <div id="ciclo-nome-materia" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; pointer-events: none; opacity: 0; transition: opacity 0.3s ease;">
                        <div style="font-size: 16px; font-weight: bold; color: #1e293b; margin-bottom: 4px;"></div>
                        <div style="font-size: 14px; color: #64748b;"></div>
                    </div>
                </div>
                <script>
                    // Dados vindos do PHP
                    const materias = <?php echo json_encode($materias_no_planejamento_2); ?>;
                    const ultima = <?php echo json_encode($ultimo_estudo['nome_materia_estudo']); ?>;
                    const proxima = <?php echo json_encode($proximaMateriaEstudada ?? $proximoMateriaNome); ?>;

                    // Preparar dados para Chart.js
                    const cicloLabels = materias.map(m => m.nome);
                    const cicloData = materias.map(m => 1); // Cada matéria = 1 fatia
                    const cicloBackgroundColors = materias.map(m => m.cor);
                    // Função para gerar bordas (padrão)
                    function getCicloBorderColors(destaqueUltima = false, destaqueProxima = false) {
                        return materias.map(m => {
                            if (destaqueUltima && m.nome === ultima) return '#dc3545';
                            if (destaqueProxima && m.nome === proxima) return '#00008B';
                            return '#fff';
                        });
                    }
                    function getCicloBorderWidths(destaqueUltima = false, destaqueProxima = false) {
                        return materias.map(m => {
                            if (destaqueUltima && m.nome === ultima) return 12;
                            if (destaqueProxima && m.nome === proxima) return 12;
                            return 2;
                        });
                    }
                    function getCicloShadows(destaqueUltima = false, destaqueProxima = false) {
                        // Retorna um array de sombras para cada fatia
                        return materias.map(m => {
                            if (destaqueUltima && m.nome === ultima) return '0 0 16px 4px #dc3545';
                            if (destaqueProxima && m.nome === proxima) return '0 0 16px 4px #00008B';
                            return 'none';
                        });
                    }
                    let cicloChart;
                    function renderCicloChart(destaqueUltima = false, destaqueProxima = false) {
                        const ctx = document.getElementById('cicloChart').getContext('2d');
                        if (cicloChart) cicloChart.destroy();
                        
                        // Atualizar o nome da matéria no centro
                        const nomeMateriaElement = document.getElementById('ciclo-nome-materia');
                        const nomeElement = nomeMateriaElement.querySelector('div:first-child');
                        const statusElement = nomeMateriaElement.querySelector('div:last-child');
                        
                        if (destaqueUltima) {
                            nomeElement.textContent = ultima;
                            statusElement.textContent = 'Última matéria estudada';
                            statusElement.style.color = '#dc3545';
                            nomeMateriaElement.style.opacity = '1';
                        } else if (destaqueProxima) {
                            nomeElement.textContent = proxima;
                            statusElement.textContent = 'Próxima matéria a estudar';
                            statusElement.style.color = '#00008B';
                            nomeMateriaElement.style.opacity = '1';
                        } else {
                            nomeMateriaElement.style.opacity = '0';
                        }
                        
                        // Preparar dados para destacar as fatias
                        const borderColors = materias.map(m => {
                            if (destaqueUltima && m.nome === ultima) return '#dc3545';
                            if (destaqueProxima && m.nome === proxima) return '#00008B';
                            return '#fff';
                        });
                        
                        const borderWidths = materias.map(m => {
                            if (destaqueUltima && m.nome === ultima) return 4;
                            if (destaqueProxima && m.nome === proxima) return 4;
                            return 2;
                        });
                        
                        const hoverOffsets = materias.map(m => {
                            if (destaqueUltima && m.nome === ultima) return 15;
                            if (destaqueProxima && m.nome === proxima) return 15;
                            return 10;
                        });

                        // Adicionar efeito de brilho para as fatias destacadas
                        const glowEffects = materias.map(m => {
                            if (destaqueUltima && m.nome === ultima) {
                                return {
                                    shadowColor: '#dc3545',
                                    shadowBlur: 20,
                                    shadowOffsetX: 0,
                                    shadowOffsetY: 0
                                };
                            }
                            if (destaqueProxima && m.nome === proxima) {
                                return {
                                    shadowColor: '#00008B',
                                    shadowBlur: 20,
                                    shadowOffsetX: 0,
                                    shadowOffsetY: 0
                                };
                            }
                            return {
                                shadowColor: 'transparent',
                                shadowBlur: 0,
                                shadowOffsetX: 0,
                                shadowOffsetY: 0
                            };
                        });
                        
                        cicloChart = new Chart(ctx, {
                            type: 'doughnut',
                            data: {
                                labels: cicloLabels,
                                datasets: [{
                                    data: cicloData,
                                    backgroundColor: cicloBackgroundColors,
                                    borderColor: borderColors,
                                    borderWidth: borderWidths,
                                    hoverOffset: hoverOffsets,
                                    spacing: 10 // <-- Adicione esta linha!
                                }]
                            },
                            options: {
                                cutout: '60%',
                                plugins: {
                                    legend: { display: false },
                                    tooltip: {
                                        enabled: false,
                                        external: function(context) {
                                            // Remove tooltip antigo se existir
                                            let tooltipEl = document.getElementById('ciclo-tooltip-custom');
                                            if (!tooltipEl) {
                                                tooltipEl = document.createElement('div');
                                                tooltipEl.id = 'ciclo-tooltip-custom';
                                                tooltipEl.style.position = 'absolute';
                                                tooltipEl.style.pointerEvents = 'none';
                                                tooltipEl.style.zIndex = '9999';
                                                tooltipEl.style.transition = 'all 0.18s';
                                                tooltipEl.style.borderRadius = '12px';
                                                tooltipEl.style.boxShadow = '0 2px 12px rgba(30,41,59,0.10)';
                                                tooltipEl.style.padding = '12px 22px';
                                                tooltipEl.style.fontWeight = 'bold';
                                                tooltipEl.style.fontSize = '1.1rem';
                                                tooltipEl.style.textAlign = 'center';
                                                tooltipEl.style.whiteSpace = 'nowrap';
                                                document.body.appendChild(tooltipEl);
                                            }
                                            const tooltipModel = context.tooltip;
                                            if (tooltipModel.opacity === 0) {
                                                tooltipEl.style.opacity = 0;
                                                return;
                                            }
                                            // Pega o índice da fatia
                                            const dataIndex = tooltipModel.dataPoints[0].dataIndex;
                                            const corMateria = context.chart.data.datasets[0].backgroundColor[dataIndex];
                                            const nomeMateria = tooltipModel.dataPoints[0].label;
                                            // Contraste: texto branco para cores escuras, preto para claras
                                            function getContrastYIQ(hexcolor){
                                                hexcolor = hexcolor.replace('#', '');
                                                if (hexcolor.length === 3) hexcolor = hexcolor.split('').map(x => x + x).join('');
                                                const r = parseInt(hexcolor.substr(0,2),16);
                                                const g = parseInt(hexcolor.substr(2,2),16);
                                                const b = parseInt(hexcolor.substr(4,2),16);
                                                const yiq = ((r*299)+(g*587)+(b*114))/1000;
                                                return (yiq >= 128) ? '#222' : '#fff';
                                            }
                                            tooltipEl.innerHTML = nomeMateria;
                                            tooltipEl.style.background = corMateria;
                                            tooltipEl.style.color = getContrastYIQ(corMateria);
                                            // Posição
                                            const canvas = context.chart.canvas;
                                            const rect = canvas.getBoundingClientRect();
                                            tooltipEl.style.opacity = 1;
                                            tooltipEl.style.left = rect.left + window.pageXOffset + tooltipModel.caretX - tooltipEl.offsetWidth/2 + 'px';
                                            tooltipEl.style.top = rect.top + window.pageYOffset + tooltipModel.caretY - tooltipEl.offsetHeight - 12 + 'px';
                                        }
                                    },
                                    datalabels: { display: false }
                                },
                                onClick: (e, elements) => {
                                    if (elements.length > 0) {
                                        const idx = elements[0].index;
                                        abrirModalMateriaPorId(materias[idx].id, materias[idx].nome);
                                    }
                                },
                                responsive: true,
                                maintainAspectRatio: false,
                                animation: { 
                                    duration: 400,
                                    easing: 'easeOutQuart'
                                }
                            }
                        });
                        
                        // Aplicar efeitos de brilho
                        setTimeout(() => {
                            const meta = cicloChart.getDatasetMeta(0);
                            materias.forEach((m, i) => {
                                const arc = meta.data[i];
                                if (!arc) return;
                                
                                const effect = glowEffects[i];
                                arc.options.shadowBlur = effect.shadowBlur;
                                arc.options.shadowColor = effect.shadowColor;
                                arc.options.shadowOffsetX = effect.shadowOffsetX;
                                arc.options.shadowOffsetY = effect.shadowOffsetY;
                            });
                            cicloChart.update();
                        }, 10);
                    }
                    // Render inicial já destacando a próxima matéria
                    renderCicloChart(false, true);
                </script>
                <!-- Substituir os botões de navegação por botões com IDs exclusivos -->
        <div style="display: flex; justify-content: center; gap: 16px; margin-top: 24px;">
            <button id="btn-ultima-estudada" class="btn-ciclo-navegacao ultima">
                <i class="fas fa-history"></i> Última Estudada
            </button>
            <button id="btn-proxima-estudada" class="btn-ciclo-navegacao">
                <i class="fas fa-forward"></i> Próxima a Estudar
            </button>
            <button id="btn-estudar-proxima" class="btn-ciclo-navegacao" style="background: var(--primary); color: white;">
                <i class="fas fa-play"></i> Estudar Próxima Matéria
            </button>
        </div>
            </div>
        <?php endif; ?>
    </div>

</div>

