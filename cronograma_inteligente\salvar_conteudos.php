<?php
//salvar_conteudos.php
session_start();
if (!isset($_SESSION['idusuario'])) {
    header("Location: /login_index.php");
    exit();
}

include_once("assets/config.php");

$usuario_id = $_SESSION['idusuario'];
$conteudos_selecionados = $_POST['conteudos'] ?? array();

if (empty($conteudos_selecionados)) {
    $_SESSION['erro'] = "Você precisa selecionar pelo menos um conteúdo para estudar.";
    header("Location: selecionar_conteudos.php");
    exit();
}

try {
    // Iniciar transação
    pg_query($conexao, "BEGIN");

    // 1. Primeiro, desativar todos os conteúdos do usuário
    $query_desativar = "
        UPDATE appestudo.usuario_conteudo 
        SET status = false 
        WHERE usuario_id = $1";
    pg_query_params($conexao, $query_desativar, array($usuario_id));

    // 2. Buscar conteúdos que já existem para o usuário
    $query_existentes = "
        SELECT conteudo_id 
        FROM appestudo.usuario_conteudo 
        WHERE usuario_id = $1 
        AND conteudo_id = ANY($2)";
    $result_existentes = pg_query_params($conexao, $query_existentes, array(
        $usuario_id,
        '{' . implode(',', array_map('intval', $conteudos_selecionados)) . '}'
    ));

    $conteudos_existentes = array();
    while ($row = pg_fetch_assoc($result_existentes)) {
        $conteudos_existentes[] = $row['conteudo_id'];
    }

    // 3. Atualizar conteúdos existentes
    if (!empty($conteudos_existentes)) {
        $query_update = "
            UPDATE appestudo.usuario_conteudo 
            SET status = true 
            WHERE usuario_id = $1 
            AND conteudo_id = ANY($2)";
        pg_query_params($conexao, $query_update, array(
            $usuario_id,
            '{' . implode(',', array_map('intval', $conteudos_existentes)) . '}'
        ));
    }

    // 4. Inserir novos conteúdos
    $novos_conteudos = array_diff($conteudos_selecionados, $conteudos_existentes);
    if (!empty($novos_conteudos)) {
        $valores = array();
        foreach ($novos_conteudos as $conteudo_id) {
            $valores[] = "($usuario_id, $conteudo_id, true)";
        }

        $query_insert = "
            INSERT INTO appestudo.usuario_conteudo 
            (usuario_id, conteudo_id, status) 
            VALUES " . implode(',', $valores);
        pg_query($conexao, $query_insert);
    }

    // Commit da transação
    pg_query($conexao, "COMMIT");

    $_SESSION['sucesso'] = "Conteúdos selecionados com sucesso! Agora configure sua prova.";
    header("Location: configurar_prova.php");
    exit();

} catch (Exception $e) {
    // Rollback em caso de erro
    pg_query($conexao, "ROLLBACK");

    $_SESSION['erro'] = "Erro ao salvar conteúdos: " . $e->getMessage();
    header("Location: selecionar_conteudos.php");
    exit();
}
?>