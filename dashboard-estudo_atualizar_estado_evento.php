<?php
// Desabilitar a saída de warnings e notices como HTML
ini_set('display_errors', 0);
error_reporting(0);

// Garantir que nenhum output foi enviado antes
ob_clean();

// Definir o tipo de conteúdo como JSON
header('Content-Type: application/json');

include_once("conexao_POST.php");

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $eventoIdExcluir = $_POST['eventoIdexcluir'] ?? null;

    if ($eventoIdExcluir !== null) {
        try {
            // Verificar na tabela estudos usando idestudos
            $query_verificar_estudos = "SELECT idestudos FROM appEstudo.estudos WHERE idestudos = $1";
            $resultado_verificar_estudos = pg_query_params($conexao, $query_verificar_estudos, array($eventoIdExcluir));
            
            // Debug info
            $debug_info = [
                'id_recebido' => $eventoIdExcluir,
                'existe_em_estudos' => ($resultado_verificar_estudos && pg_num_rows($resultado_verificar_estudos) > 0),
                'erro_query' => pg_last_error($conexao)
            ];

            if ($resultado_verificar_estudos && pg_num_rows($resultado_verificar_estudos) > 0) {
                // Excluir da tabela estudos usando idestudos
                $query_excluir = "DELETE FROM appEstudo.estudos WHERE idestudos = $1 RETURNING idestudos";
                $resultado_excluir = pg_query_params($conexao, $query_excluir, array($eventoIdExcluir));
                
                if ($resultado_excluir && pg_num_rows($resultado_excluir) > 0) {
                    echo json_encode([
                        'success' => true,
                        'message' => 'Evento excluído com sucesso',
                        'debug' => $debug_info
                    ]);
                    exit;
                }
            }

            // Se chegou aqui, não encontrou o evento
            echo json_encode([
                'success' => false,
                'message' => 'Evento não encontrado',
                'debug' => $debug_info
            ]);
            
        } catch (Exception $e) {
            echo json_encode([
                'success' => false,
                'message' => 'Erro ao excluir: ' . $e->getMessage(),
                'debug' => $debug_info ?? null
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'ID do evento não fornecido'
        ]);
    }
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Método não permitido'
    ]);
}

exit;
?>
