<?php
// rate_card.php
session_start();
include_once("assets/config.php");

if (!isset($_SESSION['idusuario'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Usuário não autenticado']);
    exit();
}

$data = json_decode(file_get_contents('php://input'), true);

if (!isset($data['cardId']) || !isset($data['rating'])) {
    echo json_encode(['error' => 'Dados incompletos']);
    exit();
}

$card_id = (int)$data['cardId'];
$rating = $data['rating'];
$usuario_id = $_SESSION['idusuario'];

// Mapear avaliações para valores numéricos
$rating_values = [
    'hard' => 1,
    'medium' => 3,
    'easy' => 5
];

$rating_value = $rating_values[$rating];

// Iniciar transação
pg_query($conexao, "BEGIN");

try {
    // Buscar progresso atual do card
    $query_progress = "
        SELECT id, nivel_conhecimento, intervalo_atual, fator_facilidade 
        FROM appestudo.flashcard_progress 
        WHERE flashcard_id = $1 AND usuario_id = $2";
    
    $result_progress = pg_query_params($conexao, $query_progress, array($card_id, $usuario_id));
    $progress = pg_fetch_assoc($result_progress);

    if ($progress) {
        // Atualizar progresso existente
        $query = "
            SELECT * FROM appestudo.calculate_next_review($1, $2, $3)";
        
        $result = pg_query_params($conexao, $query, array(
            $rating_value,
            $progress['intervalo_atual'],
            $progress['fator_facilidade']
        ));

        $next_review = pg_fetch_assoc($result);
        $novo_intervalo = $next_review['novo_intervalo'];
        $novo_fator = $next_review['novo_fator'];

        $proxima_revisao = date('Y-m-d H:i:s', strtotime("+{$novo_intervalo} days"));

        $query_update = "
            UPDATE appestudo.flashcard_progress 
            SET 
                nivel_conhecimento = $1,
                ultima_revisao = CURRENT_TIMESTAMP,
                proxima_revisao = $2,
                total_revisoes = total_revisoes + 1,
                revisoes_corretas = revisoes_corretas + CASE WHEN $3 >= 4 THEN 1 ELSE 0 END,
                intervalo_atual = $4,
                fator_facilidade = $5
            WHERE id = $6";

        pg_query_params($conexao, $query_update, array(
            $rating_value,
            $proxima_revisao,
            $rating_value,
            $novo_intervalo,
            $novo_fator,
            $progress['id']
        ));

    } else {
        // Criar novo progresso
        $query_insert = "
            INSERT INTO appestudo.flashcard_progress (
                usuario_id,
                flashcard_id,
                nivel_conhecimento,
                ultima_revisao,
                proxima_revisao,
                total_revisoes,
                revisoes_corretas,
                intervalo_atual,
                fator_facilidade
            ) VALUES ($1, $2, $3, CURRENT_TIMESTAMP, 
                      CURRENT_TIMESTAMP + interval '1 day',
                      1, CASE WHEN $4 >= 4 THEN 1 ELSE 0 END,
                      1, 2.5)";

        pg_query_params($conexao, $query_insert, array(
            $usuario_id,
            $card_id,
            $rating_value,
            $rating_value
        ));
    }

    // Registrar no histórico
    $query_history = "
        INSERT INTO appestudo.flashcard_review_history (
            progress_id,
            avaliacao,
            tempo_resposta
        ) VALUES (
            (SELECT id FROM appestudo.flashcard_progress 
             WHERE flashcard_id = $1 AND usuario_id = $2),
            $3,
            CASE 
                WHEN $4 = 'hard' THEN 60
                WHEN $4 = 'medium' THEN 30
                ELSE 15
            END
        )";

    pg_query_params($conexao, $query_history, array(
        $card_id,
        $usuario_id,
        $rating_value,
        $rating
    ));

    pg_query($conexao, "COMMIT");
    echo json_encode(['success' => true]);

} catch (Exception $e) {
    pg_query($conexao, "ROLLBACK");
    echo json_encode(['error' => 'Erro ao salvar avaliação: ' . $e->getMessage()]);
}
?>