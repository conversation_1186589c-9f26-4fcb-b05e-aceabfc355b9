<?php
include_once("../session_config.php");
include_once("../assets/config.php");

if (!verificarSessaoValida()) {
    echo json_encode(['success' => false, 'message' => 'Usuário não autenticado']);
    exit();
}

// Verifica se é uma nova resposta ou uma edição/exclusão
if (isset($_POST['topico_id'])) {
    // Nova resposta
    $topico_id = (int)$_POST['topico_id'];
    $conteudo = trim($_POST['conteudo']);
    $parent_id = isset($_POST['parent_id']) && $_POST['parent_id'] !== '' ? (int)$_POST['parent_id'] : null;
    
    if (empty($conteudo)) {
        echo json_encode(['success' => false, 'message' => 'O conteúdo não pode estar vazio']);
        exit();
    }

    if ($parent_id) {
        $query = "INSERT INTO appestudo.forum_respostas (topico_id, usuario_id, conteudo, parent_id) VALUES ($1, $2, $3, $4)";
        $params = array($topico_id, $_SESSION['idusuario'], $conteudo, $parent_id);
    } else {
        $query = "INSERT INTO appestudo.forum_respostas (topico_id, usuario_id, conteudo) VALUES ($1, $2, $3)";
        $params = array($topico_id, $_SESSION['idusuario'], $conteudo);
    }

    $result = pg_query_params($conexao, $query, $params);

    if ($result) {
        // Notificar o autor do tópico, se não for o próprio autor da resposta
        $queryAutor = "SELECT usuario_id FROM appestudo.forum_topicos WHERE id = $1";
        $resultAutor = pg_query_params($conexao, $queryAutor, [$topico_id]);
        if ($rowAutor = pg_fetch_assoc($resultAutor)) {
            $id_autor_topico = $rowAutor['usuario_id'];
            if ($id_autor_topico != $_SESSION['idusuario']) {
                $mensagem = "Seu tópico recebeu uma nova resposta!";
                $queryNotif = "INSERT INTO appestudo.forum_notificacoes (usuario_id, mensagem, topico_id) VALUES ($1, $2, $3)";
                pg_query_params($conexao, $queryNotif, [$id_autor_topico, $mensagem, $topico_id]);
            }
        }
        header("Location: ver_topico.php?id=" . $topico_id);
        exit();
    } else {
        echo json_encode(['success' => false, 'message' => 'Erro ao criar resposta']);
        exit();
    }

} else {
    // Edição ou exclusão de resposta existente
    $acao = $_POST['acao'] ?? '';
    $resposta_id = (int)($_POST['resposta_id'] ?? 0);

    if ($resposta_id === 0) {
        echo json_encode(['success' => false, 'message' => 'ID da resposta não fornecido']);
        exit();
    }

    // Verificar se o usuário é o autor da resposta
    $query_check = "SELECT usuario_id FROM appestudo.forum_respostas WHERE id = $1";
    $result_check = pg_query_params($conexao, $query_check, array($resposta_id));
    $resposta = pg_fetch_assoc($result_check);

    if (!$resposta || $resposta['usuario_id'] != $_SESSION['idusuario']) {
        echo json_encode(['success' => false, 'message' => 'Você não tem permissão para modificar esta resposta']);
        exit();
    }

    switch ($acao) {
        case 'editar':
            $conteudo = trim($_POST['conteudo']);
            if (empty($conteudo)) {
                echo json_encode(['success' => false, 'message' => 'O conteúdo não pode estar vazio']);
                exit();
            }

            $query = "UPDATE appestudo.forum_respostas 
                     SET conteudo = $1, updated_at = CURRENT_TIMESTAMP 
                     WHERE id = $2 AND usuario_id = $3";
            $result = pg_query_params($conexao, $query, array($conteudo, $resposta_id, $_SESSION['idusuario']));
            break;

        case 'excluir':
            $query = "UPDATE appestudo.forum_respostas 
                     SET status = false 
                     WHERE id = $1 AND usuario_id = $2";
            $result = pg_query_params($conexao, $query, array($resposta_id, $_SESSION['idusuario']));
            break;

        default:
            echo json_encode(['success' => false, 'message' => 'Ação inválida']);
            exit();
    }

    if ($result) {
        echo json_encode(['success' => true]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Erro ao processar a solicitação']);
    }
}
?>


