/* Dashboard de Estatísticas - Estilos */

/* Container principal */
.dashboard-estatisticas {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    z-index: 10000;
    display: none;
    flex-direction: column;
    opacity: 0;
    transition: all 0.3s ease;
}

.dashboard-estatisticas.ativo {
    display: flex;
    opacity: 1;
}

/* Cabeçalho do dashboard */
.dashboard-header {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.dashboard-header h2 {
    margin: 0;
    font-size: 1.8rem;
    font-weight: 600;
}

.dashboard-header h2 i {
    margin-right: 10px;
    color: #3498db;
}

.dashboard-controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.form-select {
    padding: 8px 12px;
    border: none;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 0.9rem;
    min-width: 120px;
}

.form-select option {
    background: #2c3e50;
    color: white;
}

.btn {
    padding: 8px 15px;
    border: none;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Conteúdo do dashboard */
.dashboard-content {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* Resumo geral */
.resumo-geral {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.card-resumo {
    background: white;
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.card-resumo:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.card-resumo::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(45deg, #3498db, #2ecc71);
}

.card-icone {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    color: white;
    font-size: 1.5rem;
}

.card-info h3 {
    margin: 0 0 5px 0;
    font-size: 0.9rem;
    color: #7f8c8d;
    text-transform: uppercase;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.card-valor {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.card-meta {
    font-size: 0.8rem;
    color: #95a5a6;
}

/* Gráficos */
.dashboard-graficos {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.grafico-container {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.grafico-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #ecf0f1;
}

.grafico-header h3 {
    margin: 0;
    font-size: 1.2rem;
    color: #2c3e50;
    font-weight: 600;
}

.grafico-container canvas {
    max-height: 300px;
}

/* Ranking */
.ranking-container {
    max-height: 300px;
    overflow-y: auto;
}

.ranking-item {
    display: flex;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #ecf0f1;
    transition: all 0.3s ease;
}

.ranking-item:hover {
    background: #f8f9fa;
    padding-left: 10px;
}

.ranking-posicao {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #3498db;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 15px;
    font-size: 0.9rem;
}

.ranking-posicao.primeiro {
    background: #f1c40f;
}

.ranking-posicao.segundo {
    background: #95a5a6;
}

.ranking-posicao.terceiro {
    background: #cd7f32;
}

.ranking-lei {
    flex: 1;
    display: flex;
    align-items: center;
}

.ranking-icone {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 12px;
    font-size: 0.9rem;
}

.ranking-info h4 {
    margin: 0 0 3px 0;
    font-size: 0.95rem;
    color: #2c3e50;
}

.ranking-info p {
    margin: 0;
    font-size: 0.8rem;
    color: #7f8c8d;
}

.ranking-valor {
    font-weight: bold;
    color: #2c3e50;
    font-size: 1.1rem;
}

/* Detalhes por lei */
.detalhes-leis {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 25px;
}

.detalhe-lei {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    border-left: 4px solid;
}

.detalhe-lei-header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.detalhe-lei-icone {
    width: 45px;
    height: 45px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 15px;
    font-size: 1.2rem;
}

.detalhe-lei-info h3 {
    margin: 0 0 5px 0;
    font-size: 1.1rem;
    color: #2c3e50;
}

.detalhe-lei-info p {
    margin: 0;
    font-size: 0.85rem;
    color: #7f8c8d;
}

.detalhe-lei-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.detalhe-stat {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
}

.detalhe-stat-valor {
    font-size: 1.5rem;
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.detalhe-stat-label {
    font-size: 0.8rem;
    color: #7f8c8d;
    text-transform: uppercase;
    font-weight: 600;
}

/* Loading */
.dashboard-loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: none;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 10001;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #ecf0f1;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.dashboard-loading p {
    color: #7f8c8d;
    font-size: 1.1rem;
    margin: 0;
}

/* Responsividade */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 15px 20px;
        flex-direction: column;
        gap: 15px;
    }
    
    .dashboard-controls {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .dashboard-content {
        padding: 20px;
    }
    
    .resumo-geral {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .dashboard-graficos {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .grafico-container {
        padding: 20px;
    }
    
    .detalhes-leis {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}

@media (max-width: 480px) {
    .card-resumo {
        padding: 20px;
        flex-direction: column;
        text-align: center;
    }
    
    .card-icone {
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .dashboard-graficos {
        grid-template-columns: 1fr;
    }
    
    .grafico-container {
        min-width: unset;
    }
}
