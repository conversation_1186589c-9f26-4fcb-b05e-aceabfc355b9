<?php

$hostname = "app_estudo.postgresql.dbaas.com.br";
$user = "app_estudo";
$password = "Lucasb90#";
$database = "app_estudo";
$port = "5432";

// Conexão com o PostgreSQL
$conexao = pg_connect("host=$hostname port=$port dbname=$database user=$user password=$password");

if (!$conexao) {
    error_log("Erro PostgreSQL: " . pg_last_error());
    die("Falha na conexão com o banco de dados: " . pg_last_error());
}

// Teste a conexão
try {
    $result = pg_query($conexao, "SELECT 1");
    if (!$result) {
        throw new Exception(pg_last_error($conexao));
    }
} catch (Exception $e) {
    error_log("Erro teste conexão: " . $e->getMessage());
    die("Erro ao testar conexão: " . $e->getMessage());
}

?>
