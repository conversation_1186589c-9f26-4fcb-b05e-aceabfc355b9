<?php
// api/assinaturas.php
require_once '../config/database.php';
require_once '../includes/auth.php';

// Verifica se o usuário está autenticado e é admin
if (!isAdmin()) {
    header('HTTP/1.1 403 Forbidden');
    echo json_encode(['erro' => 'Acesso negado']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['id'])) {
    $id = (int)$_GET['id'];
    
    $sql = "SELECT a.*, u.nome as nome_usuario, p.nome as nome_plano 
            FROM appestudo.assinaturas a 
            JOIN appestudo.usuario u ON a.usuario_id = u.idusuario 
            JOIN appestudo.planos p ON a.plano_id = p.id 
            WHERE a.id = $1";
            
    $result = pg_query_params($conexao, $sql, array($id));
    
    if (!$result) {
        header('HTTP/1.1 500 Internal Server Error');
        echo json_encode(['erro' => 'Erro ao buscar dados']);
        exit;
    }
    
    $assinatura = pg_fetch_assoc($result);
    
    if (!$assinatura) {
        header('HTTP/1.1 404 Not Found');
        echo json_encode(['erro' => 'Assinatura não encontrada']);
        exit;
    }
    
    header('Content-Type: application/json');
    echo json_encode($assinatura);
} else {
    header('HTTP/1.1 400 Bad Request');
    echo json_encode(['erro' => 'Requisição inválida']);
}