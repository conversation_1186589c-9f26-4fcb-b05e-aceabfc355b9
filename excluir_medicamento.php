<?php
// Conectar ao banco de dados (coloque aqui suas configurações de conexão)
include_once("conexao_POST.php");
if (isset($_GET["id"])) {
    $id_medicamento = $_GET["id"];

    // Consulta SQL para excluir o medicamento
    $query = "DELETE FROM appEstudo.medicamento WHERE id_medicamento = $1";
    $result = pg_query_params($conexao, $query, [$id_medicamento]);

    if (!$result) {
        die("Erro ao excluir medicamento: " . pg_last_error());
    }

    header("Location: listar_medicamentos.php");
    exit();
} else {
    die("ID do medicamento não fornecido.");
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Excluir Medicamento</title>
</head>
<body>
<h1>Excluir Medicamento</h1>
<p>Tem certeza de que deseja excluir este medicamento?</p>
<a href="listar_medicamentos.php">Cancelar</a>
<a href="?id=<?= $id_medicamento ?>">Confirmar Exclusão</a>
</body>
</html>

<?php
// Fechar a conexão com o banco de dados
pg_close($conexao);
?>
