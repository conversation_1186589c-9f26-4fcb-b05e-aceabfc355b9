<?php
//selecionar_conteudos.php

session_start();
require_once 'includes/verificar_modulo.php';
require_once 'includes/mensagens.php';
verificarModulo();
if (!isset($_SESSION['idusuario'])) {
    header("Location: /login_index.php");
    exit();
}

include_once 'assets/config.php';
include_once 'components/mensagens.php';

$usuario_id = $_SESSION['idusuario'];

// Buscar prova ativa
$query_prova = "
    SELECT p.* 
    FROM appestudo.provas p
    WHERE p.usuario_id = $1 
    AND p.status = true 
    ORDER BY p.created_at DESC 
    LIMIT 1";
$result_prova = pg_query_params($conexao, $query_prova, array($usuario_id));
$prova = pg_fetch_assoc($result_prova);



// Buscar edital atual
$query_edital = "
    SELECT e.* 
    FROM appestudo.usuario_edital ue
    JOIN appestudo.edital e ON ue.edital_id = e.id_edital
    WHERE ue.usuario_id = $1";
$result_edital = pg_query_params($conexao, $query_edital, array($usuario_id));
$edital = pg_fetch_assoc($result_edital);

// Consulta para buscar o nome do usuário com base no ID
$query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = $usuario_id";
$resultado_nome = pg_query($conexao, $query_buscar_nome);

if ($resultado_nome && pg_num_rows($resultado_nome) > 0) {
    $row = pg_fetch_assoc($resultado_nome);
    $nome_usuario = $row['nome'];
} else {
    echo "Usuário não encontrado.";
}

// Buscar conteúdos do edital agrupados por matéria
$query_conteudos = "
WITH conteudos_validos AS (
    SELECT ce.id_conteudo
    FROM appestudo.conteudo_edital ce
    WHERE ce.edital_id = $2
    AND ce.capitulo ~ '^[0-9.]+$'
)
SELECT 
    m.idmateria,
    m.nome as materia_nome,
    m.cor,
    ce.id_conteudo,
    ce.descricao,
    ce.capitulo,
    ce.ordem,
    COALESCE(uc.status, false) as selecionado
FROM conteudos_validos cv
JOIN appestudo.conteudo_edital ce ON cv.id_conteudo = ce.id_conteudo
JOIN appestudo.materia m ON ce.materia_id = m.idmateria
LEFT JOIN appestudo.usuario_conteudo uc 
    ON ce.id_conteudo = uc.conteudo_id 
    AND uc.usuario_id = $1
WHERE ce.edital_id = $2
ORDER BY m.nome, ce.ordem, ce.capitulo;";

$result_conteudos = pg_query_params($conexao, $query_conteudos, array($usuario_id, $edital['id_edital']));

// Agrupar conteúdos por matéria
$materias = array();
while ($row = pg_fetch_assoc($result_conteudos)) {
    $materia_id = $row['idmateria'];
    if (!isset($materias[$materia_id])) {
        $materias[$materia_id] = array(
            'nome' => $row['materia_nome'],
            'cor' => $row['cor'],
            'conteudos' => array()
        );
    }
    $materias[$materia_id]['conteudos'][] = $row;
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Selecionar Conteúdos - Plano Inteligente</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Old+Standard+TT:wght@400;700&family=Crimson+Text:wght@400;600&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <style>
:root {
    /* Cores modo claro (já existentes) */
    --primary: #00008B;
    --secondary: #f8f0e3;
    --accent: #4a4a4a;
    --border: #e0e0e0;
    --text: #2c3e50;
    --active: #00008B;
    --hover: #f5f5f5;
    --primary-blue: #00008B;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --background: #f5f5f5;
    --card-background: white;
    --checkbox-border: #ccc;
}

[data-theme="dark"] {
    --primary: #4169E1;
    --secondary: #1a1a2e;
    --accent: #6c7293;
    --border: #2d2d42;
    --text: #e4e6f0;
    --active: #4169E1;
    --hover: #232338;
    --primary-blue: #4169E1;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --background: #0a0a1f;
    --card-background: #13132b;
    --checkbox-border: #4169E1;
}

        body {
            font-family: 'Quicksand', sans-serif;
            background-color: var(--hover);
            color: var(--text);
            line-height: 1.6;
            margin: 0;
            min-height: 100vh;
            box-sizing: border-box;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            font-family: 'Quicksand', sans-serif;
            text-align: center;
            color: var(--primary);
            font-size: 2rem;
            margin-bottom: 30px;
            font-weight: 600;
        }

        .materias-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .materia-card {
            background: white;
            border: 1px solid var(--border);
            padding: 20px;
            border-radius: 8px;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            position: relative;
            overflow: hidden;
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .materia-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        .materia-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(139, 69, 19, 0.2);
        }

        .materia-title {
            font-family: 'Quicksand', sans-serif;
            color: var(--primary);
            font-size: 1.2rem;
            margin: 0;
            font-weight: 600;
        }

        .materia-controls {
            display: flex;
            gap: 10px;
        }

        .conteudo-item {
            padding: 10px;
            border: 1px solid var(--border);
            border-radius: 4px;
            margin-bottom: 8px;
            background: white;
            transition: all 0.2s ease;
        }

        .conteudo-item:hover {
            background: var(--hover);
        }

        .conteudo-item label {
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
        }

        .conteudo-capitulo {
            font-weight: bold;
            color: var(--secondary-color);
        }

        .btn-submit {
            display: block;
            width: 100%;
            max-width: 300px;
            margin: 30px auto;
            padding: 12px;
            background: var(--primary);
            color: white;
            border: none;
            border-radius: 8px;
            font-family: 'Quicksand', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .btn-submit:hover {
            background: var(--primary-blue);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        .select-controls {
            margin-bottom: 20px;
            text-align: center;
        }

        .btn-select-all {
            padding: 8px 16px;
            margin: 0 5px;
            background: var(--primary);
            border: 1px solid var(--border);
            border-radius: 8px;
            font-family: 'Quicksand', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-select-all:hover {
            background: var(--primary-blue);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        @media (max-width: 768px) {
            body {
                padding: 20px;
            }

            .materias-container {
                grid-template-columns: 1fr;
            }
        }

        .flow-info {
            background: white;
            padding: 20px;
            margin-bottom: 30px;
            border: 1px solid var(--border);
            box-shadow: 0 2px 4px var(--shadow-color);
            border-radius: 8px;
        }

        .steps-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 20px;
            max-width: 800px;
            margin: 0 auto;
            position: relative;
        }

        .steps-container::before {
            content: "";
            position: absolute;
            top: 25px;
            left: 50px;
            right: 50px;
            height: 2px;
            background: rgba(139, 69, 19, 0.2);
            z-index: 0;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            z-index: 1;
        }

        .step-number {
            width: 50px;
            height: 50px;
            background: white;
            border: 1px solid var(--border);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Quicksand', sans-serif;
            font-size: 1.2rem;
            color: var(--text);
        }

        .step.active .step-number {
            background: var(--primary);
            color: white;
            border-color: var(--primary);
        }

        .step.completed .step-number {
            background: var(--accent);
            color: white;
            border-color: var(--accent);
        }

        .step-text {
            font-family: 'Quicksand', sans-serif;
            color: var(--text);
            font-size: 0.9rem;
            text-align: center;
        }

        .step.active .step-text {
            color: var(--primary);
            font-weight: 600;
        }

        .step.completed .step-text {
            color: var(--accent);
        }

        .intro-text {
            text-align: center;
            margin-bottom: 30px;
            font-family: 'Quicksand', sans-serif;
            color: var(--text);
            font-style: italic;
        }

        /* Melhorias nos controles de seleção */
        .select-controls {
            background: var(--paper-color);
            padding: 15px;
            border-radius: 8px;
            margin: 20px auto;
            max-width: 600px;
            box-shadow: 3px 3px 0 var(--border-color);
            border: 1px solid var(--border-color);
        }

        .btn-select-all {
            padding: 8px 16px;
            margin: 0 5px;
            border: 2px solid var(--border-color);
            background: white;
            border-radius: 4px;
            font-family: 'Quicksand', sans-serif;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-select-all:hover {
            background: var(--primary);
            color: white;
            transform: translateY(-2px);
        }

        /* Header styles */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: white;
            border-bottom: 1px solid var(--border);
            margin-bottom: 30px;
            border-radius: 0 0 10px 10px;
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo img {
            height: 50px;
            width: auto;
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 15px;
            background: var(--hover);
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .user-info i {
            color: var(--primary);
            font-size: 1.2rem;
        }

        .user-name {
            color: var(--text);
            font-weight: 600;
        }

        .btn-voltar {
            position: fixed;
            top: 140px;
            left: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: white;
            border: 1px solid var(--border);
            border-radius: 50%;
            box-shadow: 0 2px 4px var(--shadow-color);
            color: var(--primary);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .btn-voltar:hover {
            background: var(--primary);
            color: white;
            transform: scale(1.1);
            box-shadow: 0 4px 8px var(--shadow-color);
        }

        /* Ajustes responsivos */
        @media (max-width: 768px) {
            .steps-container {
                flex-direction: column;
                gap: 10px;
            }

            .steps-container::before {
                width: 2px;
                height: auto;
                top: 50px;
                bottom: 50px;
                left: 24px;
                right: auto;
            }

            .step {
                width: 100%;
                flex-direction: row;
                gap: 20px;
            }

            .step-text {
                text-align: left;
            }

            .header {
                padding: 15px;
            }

            .logo img {
                height: 30px;
            }

            .btn-voltar {
                left: 10px;
                top: 70px;
                width: 35px;
                height: 35px;
            }
        }

        /* Estilos do botão de tema */
.theme-toggle {
    margin-left: 15px;
}

.theme-btn {
    background: transparent;
    border: none;
    color: var(--primary);
    cursor: pointer;
    font-size: 1.2rem;
    padding: 8px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.theme-btn:hover {
    background: var(--hover);
}

/* Atualize os componentes existentes */
body {
    background-color: var(--background);
    color: var(--text);
}

.header {
    background: var(--card-background);
    border-color: var(--border);
}

.materia-card {
    background: var(--card-background);
    border-color: var(--border);
}

.conteudo-item {
    background: var(--card-background);
    border-color: var(--border);
    color: var(--text);
}

.conteudo-item:hover {
    background: var(--hover);
}

.flow-info {
    background: var(--card-background);
    border-color: var(--border);
}

.select-controls {
    background: var(--card-background);
    border-color: var(--border);
}

.btn-select-all {
    background: var(--card-background);
    color: var(--text);
    border-color: var(--border);
}

.btn-select-all:hover {
    background: var(--primary);
    color: white;
}

/* Estilização dos checkboxes para o modo escuro */
.conteudo-item input[type="checkbox"] {
    appearance: none;
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    border: 2px solid var(--checkbox-border);
    border-radius: 4px;
    outline: none;
    cursor: pointer;
    position: relative;
    background: var(--card-background);
}

.conteudo-item input[type="checkbox"]:checked {
    background: var(--primary);
    border-color: var(--primary);
}

.conteudo-item input[type="checkbox"]:checked::after {
    content: '✓';
    color: white;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 12px;
}

/* Botão voltar */
.btn-voltar {
    background: var(--card-background);
    border-color: var(--border);
}

.btn-voltar:hover {
    background: var(--primary);
    color: white;
}

/* Ajustes de texto */
.intro-text {
    color: var(--text);
}

.step-text {
    color: var(--text);
}

.user-info {
    background: var(--hover);
}

/* Estilos para os logos */
.logo {
    position: relative;
}

.logo img {
    height: 50px;
    width: auto;
    transition: opacity 0.3s ease;
}

.logo-dark {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
}

/* Controle de visibilidade baseado no tema */
[data-theme="dark"] .logo-light {
    opacity: 0;
}

[data-theme="dark"] .logo-dark {
    opacity: 1;
}

[data-theme="dark"] #disciplinas-overview {
    background-color: var(--card-background);
}

[data-theme="dark"] #disciplinas-overview .bg-blue-50 {
    background-color: rgba(59, 130, 246, 0.1);
}

[data-theme="dark"] #disciplinas-overview .bg-green-50 {
    background-color: rgba(16, 185, 129, 0.1);
}

[data-theme="dark"] #disciplinas-overview .text-blue-800,
[data-theme="dark"] #disciplinas-overview .text-blue-900,
[data-theme="dark"] #disciplinas-overview .text-green-800,
[data-theme="dark"] #disciplinas-overview .text-green-900 {
    color: var(--text);
}

.bg-theme {
    background-color: var(--card-background);
}

/* Certifique-se que esta classe tenha prioridade sobre o Tailwind */
.bg-theme.bg-theme {
    background-color: var(--card-background);
}
    </style>
</head>
<body>
    <div class="header">
        <div class="header-left">
            <div class="logo">
            <img src="../logo/logo_vertical_azul.png" alt="Logo" class="logo-light">
            <img src="../logo/logo_vertical.png" alt="Logo" class="logo-dark">
            </div>
        </div>
        <div class="header-right">
    <div class="user-info">
        <i class="fas fa-user-circle"></i>
        <span class="user-name"><?php echo htmlspecialchars($nome_usuario); ?></span>
    </div>
    <div class="theme-toggle">
        <button id="theme-toggle-btn" class="theme-btn">
            <i class="fas fa-moon"></i>
        </button>
    </div>
</div>
    </div>
    <a href="selecionar_edital.php" class="btn-voltar">
        <i class="fas fa-arrow-left"></i>
    </a>

<div class="container">
    <h1>Selecionar Conteúdos para Estudo</h1>

    <div class="flow-info">
        <div class="steps-container">
            <div class="step completed">
                <div class="step-number"><i class="fas fa-check"></i></div>
                <div class="step-text">Selecionar Edital</div>
            </div>
            <div class="step active">
                <div class="step-number">2</div>
                <div class="step-text">Selecionar Conteúdos</div>
            </div>
            <div class="step">
                <div class="step-number">3</div>
                <div class="step-text">Configurar Plano</div>
            </div>
            <div class="step">
                 <div class="step-number">4</div>
                  <div class="step-text">Definir Horários</div>
            </div>
        </div>
    </div>

    <div class="intro-text">
        <p>Selecione os conteúdos que você pretende estudar. Você pode selecionar todos de uma vez, por matéria,
            ou individualmente. É possível alterar essa seleção posteriormente no seu plano de estudos.</p>
    </div>

    <?php mostrarMensagens(); ?>


    <?php
    $total_disciplinas = count($materias);
    $total_conteudos = 0;
    $conteudos_selecionados = 0;
    
    foreach ($materias as $materia) {
        $total_conteudos += count($materia['conteudos']);
        foreach ($materia['conteudos'] as $conteudo) {
            if ($conteudo['selecionado']) {
                $conteudos_selecionados++;
            }
        }
    }
    ?>
    <div id="disciplinas-overview">
    <div class="w-full max-w-4xl mx-auto mb-8 bg-theme rounded-lg shadow-md p-6">
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
            <div class="text-center p-4 bg-blue-50 rounded-lg">
                <h3 class="text-lg font-semibold text-blue-800">Total de Disciplinas</h3>
                <p class="text-2xl font-bold text-blue-900"><?php echo $total_disciplinas; ?></p>
            </div>
            <div class="text-center p-4 bg-green-50 rounded-lg">
                <h3 class="text-lg font-semibold text-green-800">Conteúdos Selecionados</h3>
                <p class="text-2xl font-bold text-green-900">
                    <span id="total-selecionados">0</span> / <span id="total-conteudos">0</span>
                </p>
                <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                    <div id="progresso-geral" class="bg-green-600 h-2 rounded-full" style="width: 0%"></div>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
            <?php foreach ($materias as $materia_id => $materia): ?>
            <div class="p-3 rounded-lg border-l-4" style="border-left-color: <?php echo $materia['cor']; ?>">
                <h4 class="font-medium text-sm"><?php echo htmlspecialchars($materia['nome']); ?></h4>
                <div class="mt-2 w-full bg-gray-200 rounded-full h-2">
                    <div class="progress-bar bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                </div>
                <div class="text-xs mt-1 text-gray-600">
                    <span class="conteudos-count">0/0</span> conteúdos
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</div>

    <div class="select-controls">
        <button type="button" class="btn-select-all" onclick="selecionarTodos(true)">
            Selecionar Todos
        </button>
        <button type="button" class="btn-select-all" onclick="selecionarTodos(false)">
            Desmarcar Todos
        </button>
    </div>

    <form action="salvar_conteudos.php" method="POST">
        <div class="materias-container">
            <?php foreach ($materias as $materia_id => $materia): ?>
                <div class="materia-card" style="border-left: 4px solid <?= $materia['cor'] ?>">
                    <div class="materia-header">
                        <h3 class="materia-title"><?= htmlspecialchars($materia['nome']) ?></h3>
                        <div class="materia-controls">
                            <button type="button" onclick="selecionarMateria(<?= $materia_id ?>, true)">
                                <i class="fas fa-check-square"></i>
                            </button>
                            <button type="button" onclick="selecionarMateria(<?= $materia_id ?>, false)">
                                <i class="fas fa-square"></i>
                            </button>
                        </div>
                    </div>

                    <div class="conteudos-list">
                        <?php foreach ($materia['conteudos'] as $conteudo): ?>
                            <div class="conteudo-item">
                                <label>
                                    <input type="checkbox" name="conteudos[]"
                                           value="<?= $conteudo['id_conteudo'] ?>"
                                        <?= $conteudo['selecionado'] ? 'checked' : '' ?>
                                           data-materia="<?= $materia_id ?>">
                                    <span>
                                            <span class="conteudo-capitulo"><?= htmlspecialchars($conteudo['capitulo']) ?></span>
                                            <?= htmlspecialchars($conteudo['descricao']) ?>
                                        </span>
                                </label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <button type="submit" class="btn-submit">Configurar Prova</button>
    </form>
</div>

<script>
function atualizarEstatisticas() {
    // Contadores gerais
    let totalConteudos = 0;
    let conteudosSelecionados = 0;

    // Para cada matéria no overview
    document.querySelectorAll('#disciplinas-overview .border-l-4').forEach(materiaOverview => {
        // Encontrar o card correspondente na lista de matérias
        const materiaNome = materiaOverview.querySelector('h4').textContent.trim();
        const materiaCard = Array.from(document.querySelectorAll('.materia-card')).find(card => 
            card.querySelector('.materia-title').textContent.trim() === materiaNome
        );

        if (materiaCard) {
            const checkboxes = materiaCard.querySelectorAll('input[type="checkbox"]');
            const totalMateria = checkboxes.length;
            const selecionadosMateria = Array.from(checkboxes).filter(cb => cb.checked).length;

            totalConteudos += totalMateria;
            conteudosSelecionados += selecionadosMateria;

            // Atualizar barra de progresso no overview
            const porcentagem = (selecionadosMateria / totalMateria) * 100;
            const barraProgresso = materiaOverview.querySelector('.progress-bar');
            if (barraProgresso) {
                barraProgresso.style.width = `${porcentagem}%`;
            }

            // Atualizar contador no overview
            const contadorMateria = materiaOverview.querySelector('.conteudos-count');
            if (contadorMateria) {
                contadorMateria.textContent = `${selecionadosMateria}/${totalMateria}`;
            }
        }
    });

    // Atualizar contadores gerais
    const totalSelecionadosElement = document.getElementById('total-selecionados');
    const totalConteudosElement = document.getElementById('total-conteudos');
    if (totalSelecionadosElement) {
        totalSelecionadosElement.textContent = conteudosSelecionados;
    }
    if (totalConteudosElement) {
        totalConteudosElement.textContent = totalConteudos;
    }

    // Atualizar porcentagem geral
    const porcentagemGeral = (conteudosSelecionados / totalConteudos) * 100;
    const barraProgressoGeral = document.getElementById('progresso-geral');
    if (barraProgressoGeral) {
        barraProgressoGeral.style.width = `${porcentagemGeral}%`;
    }
}
// Modificar as funções existentes para chamar atualizarEstatisticas()
function selecionarTodos(selecionar) {
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        checkbox.checked = selecionar;
    });
    atualizarEstatisticas(); // Adicionar esta linha
}

function selecionarMateria(materiaId, selecionar) {
    document.querySelectorAll(`input[data-materia="${materiaId}"]`).forEach(checkbox => {
        checkbox.checked = selecionar;
    });
    atualizarEstatisticas(); // Adicionar esta linha
}

// Adicionar listener para checkboxes individuais
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        checkbox.addEventListener('change', atualizarEstatisticas);
    });
    
    // Fazer a primeira atualização quando a página carrega
    atualizarEstatisticas();
});

    // Adicione o script do tema
// Substitua o script do tema atual por esta versão otimizada
document.addEventListener('DOMContentLoaded', () => {
    const themeToggleBtn = document.getElementById('theme-toggle-btn');
    const themeIcon = themeToggleBtn.querySelector('i');
    const html = document.documentElement;
    
    // Aplica o tema inicial sem transição
    const savedTheme = localStorage.getItem('theme') || 'light';
    html.style.setProperty('transition', 'none');
    html.setAttribute('data-theme', savedTheme);
    updateIcon(savedTheme);
    
    // Força um reflow para aplicar as mudanças sem transição
    html.offsetHeight;
    
    // Restaura as transições
    html.style.removeProperty('transition');
    
    themeToggleBtn.addEventListener('click', () => {
        // Desativa temporariamente animações pesadas
        document.body.style.pointerEvents = 'none';
        
        const currentTheme = html.getAttribute('data-theme');
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        html.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        updateIcon(newTheme);
        
        // Reativa interações após um breve delay
        setTimeout(() => {
            document.body.style.pointerEvents = '';
        }, 200);
    });
    
    function updateIcon(theme) {
        if (theme === 'dark') {
            themeIcon.classList.remove('fa-moon');
            themeIcon.classList.add('fa-sun');
        } else {
            themeIcon.classList.remove('fa-sun');
            themeIcon.classList.add('fa-moon');
        }
    }
});
</script>
</body>
</html>
