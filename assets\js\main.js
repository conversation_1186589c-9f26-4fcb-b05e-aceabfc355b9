// Funções de popup (mantidas como estão)
function abrirPopUp50() {
    var url = "0cronometro.php";
    var novaJanela = window.open(url, "_blank", "width=" + screen.availWidth + ", height=" + screen.availHeight + ", left=0, top=0, resizable=yes, scrollbars=yes");
    if (novaJanela) {
        novaJanela.focus();
        novaJanela.moveTo(0, 0);
        novaJanela.resizeTo(screen.availWidth, screen.availHeight);
    }
}

function abrirPopUp6() { window.open("historico.php", "_blank"); }
function abrirPopUp40() { window.open("painel_materias.php", "_blank"); }
function abrirPopUp60() { window.open("cronograma_inteligente_1_v2_1_VSC/index.php", "_blank"); }
function abrirPopUp90() { window.open("cf/index.php", "_blank"); }
function abrirPopUp80() { window.open("flashcards/flashcards.php", "_blank"); }
function abrirPopUp() { window.open("planejamento/editar_planejamento.php", "_blank"); }
function abrirPopUp_avisos() { window.open("avisos.php", "_blank"); }
function abrirPopUp11() { window.open("0editar_curso.php", "_blank"); }
function abrirPopUpForum() { window.open("forum/index.php", "_blank"); }
function abrirPopUpAgendaAutomatica() { window.open("agenda_automatica/index.php", "_blank"); }

function abrirPopUplexjus() { window.open("lexjus_VOID_v6/home_mae.php", "_blank"); }

function abrirPopUp7() {
    var largura = 1500, altura = 1200;
    var left = (screen.width - largura) / 2;
    var top = (screen.height - altura) / 2;
    window.open("calendario_agenda_AUTOMATICO.php", "_blank",
        `width=${largura}, height=${altura}, left=${left}, top=${top}`);
}

function abrirPopUp9() {
    var largura = 600, altura = 800;
    var left = (screen.width - largura) / 2;
    var top = (screen.height - altura) / 2;
    window.open("revisao.php", "_blank",
        `width=${largura}, height=${altura}, left=${left}, top=${top}`);
}

// Funções para os gráficos
function abrirPopUpGrafico1() {
    var largura = 1100, altura = 800;
    var left = (screen.width - largura) / 2;
    var top = (screen.height - altura) / 2;
    window.open("grafico_ponto_estudado.php", "_blank",
        `width=${largura}, height=${altura}, left=${left}, top=${top}`);
}

function abrirPopUpGrafico2() {
    var largura = 1100, altura = 800;
    var left = (screen.width - largura) / 2;
    var top = (screen.height - altura) / 2;
    window.open("grafico_disciplina_por_curso.php", "_blank",
        `width=${largura}, height=${altura}, left=${left}, top=${top}`);
}

function abrirPopUpGrafico3() {
    var largura = 1100, altura = 800;
    var left = (screen.width - largura) / 2;
    var top = (screen.height - altura) / 2;
    window.open("grafico_ciclo_estudo.php", "_blank",
        `width=${largura}, height=${altura}, left=${left}, top=${top}`);
}

// Função para o scroll do navbar
window.onscroll = function() {
    var header = document.querySelector('.header');
    if (header) {
        header.classList.toggle('fixed', window.pageYOffset > 0);
    }
};

// Função genérica para abrir modal
function abrirModal(conteudo, titulo) {
    // Remove modal anterior se existir
    $('#modal-estudo').remove();

    const modalHTML = `
        <div id="modal-estudo" class="modal-estudo ativo">
            <div class="modal-overlay"></div>
            <div class="modal-content">
                <button class="modal-close">
                    <i class="fas fa-times"></i>
                </button>
                <div class="modal-body">
                    <h3 class="modal-titulo">${titulo}</h3>
                    <div class="modal-conteudo">
                        ${conteudo}
                    </div>
                </div>
            </div>
        </div>
    `;

    const modalElement = $(modalHTML).appendTo('body');

    const fecharModalERestaurarScroll = function() {
        modalElement.remove();
        document.body.style.overflow = '';
        $(document).off('keydown.modal');
    };

    modalElement.find('.modal-close, .modal-overlay').on('click', fecharModalERestaurarScroll);

    $(document).on('keydown.modal', function(e) {
        if (e.key === 'Escape') {
            fecharModalERestaurarScroll();
        }
    });
}

// Função para mostrar modal de evento
function mostrarModalEvento(dados) {
    const modalContent = `
        <div class="modal-conteudo">
            <h4><strong>Tipo:</strong> ${dados.tipo || 'Não definido'}</h4>
            <h4><strong>Início:</strong> ${dados.data_inicio || 'Não definido'}</h4>
            <h4><strong>Fim:</strong> ${dados.data_fim || 'Não definido'}</h4>
            <br>
            <h5><strong>Detalhes:</strong></h5>
            <div class="details-box p-3 mt-2 bg-light rounded">
                ${dados.detalhes || 'Sem detalhes'}
            </div>
            
            <div class="modal-footer mt-4">
                ${dados.id ? `
                    ${dados.realizado !== 't' ? `
                        <button type="button" class="btn btn-success marcar-realizado" data-id="${dados.id}">
                            <i class="fas fa-check"></i> Marcar como Realizado
                        </button>
                    ` : `
                        <button type="button" class="btn btn-secondary" disabled>
                            <i class="fas fa-check-circle"></i> Evento Realizado
                        </button>
                    `}
                    <button type="button" class="btn btn-danger excluir-evento" data-id="${dados.id}">
                        <i class="fas fa-trash"></i> Excluir
                    </button>
                ` : ''}
            </div>
        </div>
    `;

    abrirModal(modalContent, dados.title || dados.titulo || 'Sem título');

    if (dados.id) {
        // Marcar como realizado
        $('.marcar-realizado').on('click', function() {
            const eventoId = $(this).data('id');
            $.ajax({
                url: 'agenda_atualizar_evento.php',
                method: 'POST',
                data: { eventoId: eventoId, estadoCheckbox: true },
                success: function(response) {
                    // 1. Atualizar o modal
                    $(this).prop('disabled', true).removeClass('btn-success').addClass('btn-secondary')
                        .html('<i class="fas fa-check-circle"></i> Evento Realizado');

                    // 2. Atualizar a agenda
                    atualizarAgenda(eventoId, response); // Passar a resposta para obter a data
                },
                error: function() { alert('Erro ao atualizar o evento.'); }
            });
        });

        // Excluir evento
        $('.excluir-evento').on('click', function() {
            if (confirm('Tem certeza que deseja excluir este evento?')) {
                const eventoId = $(this).data('id');
                $.ajax({
                    url: 'agenda_atualizar_estado_evento.php',
                    method: 'POST',
                    data: { eventoIdexcluir: eventoId },
                    success: function() {
                        // 1. Fechar o modal
                        $('#modal-estudo').remove();

                        // 2. Atualizar a agenda
                        atualizarAgenda(eventoId);
                    },
                    error: function() { alert('Erro ao excluir o evento.'); }
                });
            }
        });
    }
}

// Função para atualizar a agenda
function atualizarAgenda(eventoId, response) {
    $.ajax({
        url: 'atualizar_agenda_component.php',
        method: 'GET',
        success: function(html) {
            // Identifica o container principal da agenda
            const cardContent = document.querySelector('.card-planejamento');
            if (cardContent) {
                // Mantém o cabeçalho original
                const header = cardContent.querySelector('.card-header');
                const headerHtml = header ? header.outerHTML : '';
                
                // Atualiza o conteúdo mantendo o cabeçalho
                cardContent.innerHTML = headerHtml + '<div class="card-content">' + html + '</div>';
                
                // Reativa eventos de clique
                document.querySelectorAll('.evento-link').forEach(function(eventoLink) {
                    eventoLink.addEventListener('click', function(e) {
                        e.preventDefault();
                        const titulo = this.textContent.trim();
                        const eventoId = this.dataset.id;

                        $.ajax({
                            url: 'buscar_detalhes_evento.php',
                            method: 'POST',
                            data: { titulo: titulo, evento_id: eventoId },
                            dataType: 'json',
                            success: function(response) {
                                mostrarModalEvento(response);
                            },
                            error: function(xhr, status, error) {
                                console.error('Erro ao buscar detalhes do evento:', error);
                            }
                        });
                    });
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('Erro ao atualizar agenda:', error);
        }
    });
}

// Inicialização quando a página carrega
document.addEventListener('DOMContentLoaded', function() {



    // Adicionar eventos de clique nas matérias
    const cartoesMaterias = document.querySelectorAll('.materia-card');
    const cardUltimoEstudo = document.querySelector('.listar_ultimo');
    const cardProximoEstudo = document.querySelector('.listar_proximo');

    cartoesMaterias.forEach(cartao => {
        cartao.addEventListener('click', function() {
            const ehUltima = this.classList.contains('ultima-materia');
            const ehProxima = this.classList.contains('proxima-materia');
            const nomeMateriaElement = this.querySelector('.materia-nome');
            const nomeMateria = nomeMateriaElement ? nomeMateriaElement.textContent : '';

            if (ehUltima && cardUltimoEstudo) {
                abrirModal(cardUltimoEstudo.innerHTML, `Último Estudo - ${nomeMateria}`);
            } else if (ehProxima && cardProximoEstudo) {
                abrirModal(cardProximoEstudo.innerHTML, `Próximo Estudo - ${nomeMateria}`);
            }
        });

        if (cartao.classList.contains('ultima-materia') || cartao.classList.contains('proxima-materia')) {
            cartao.style.cursor = 'pointer';
        }
    });

    // Eventos da agenda
    document.querySelectorAll('.evento-link').forEach(function(eventoLink) {
        eventoLink.addEventListener('click', function(e) {
            e.preventDefault();
            const titulo = this.textContent.trim();
            const eventoId = this.dataset.id;

            $.ajax({
                url: 'buscar_detalhes_evento.php',
                method: 'POST',
                data: { titulo: titulo, evento_id: eventoId },
                dataType: 'json',
                success: function(response) {
                    if (response.erro) {
                        console.error('Erro:', response.erro);
                        alert('Erro ao buscar detalhes do evento: ' + response.erro);
                        return;
                    }

                    if (response.multiplos === true && Array.isArray(response.eventos)) {
                        mostrarModalSelecao(response.eventos);
                    } else {
                        mostrarModalEvento(response);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Status:', status);
                    console.error('Erro:', error);
                    console.error('Resposta completa:', xhr.responseText);
                    alert('Erro ao buscar detalhes do evento');
                }
            });
        });
    });
});