<?php
session_start();

$response = array(); // Inicializar um array para a resposta

if (isset($_SESSION['idusuario']) && isset($_POST['data_inicio'])) {
    $idUsuario = $_SESSION['idusuario'];

    // Verifica se o campo "tipo_evento" é "Planejamento" e define o título como a matéria, caso contrário, usa o título
    if ($_POST['tipo_evento'] === 'Planejamento') {
        $titulo = isset($_POST['materia']) ? $_POST['materia'] : null;
    } else {
        $titulo = isset($_POST['titulo']) ? $_POST['titulo'] : null;
    }

    if ($titulo !== null){
        $dataInicio = $_POST['data_inicio'];
        $dataFim = $_POST['data_inicio'];
        $tipo_evento = $_POST['tipo_evento'];
        $detalhes = isset($_POST['detalhes']) ? $_POST['detalhes'] : '';

        include_once("conexao_POST.php"); // Inclua aqui o arquivo de conexão com o banco de dados

        // Preparar e executar a consulta SQL para inserção
        $query = "INSERT INTO appEstudo.agenda (usuario_idusuario, titulo, data_inicio, data_fim, tipo_evento, detalhes) VALUES ($1, $2, $3, $4, $5, $6)";
        $stmt = pg_prepare($conexao, "insere_evento", $query);
        $result = pg_execute($conexao, "insere_evento", array($idUsuario, $titulo, $dataInicio, $dataFim, $tipo_evento, $detalhes));

        if ($result) {
            $response['success'] = true;
            $response['message'] = "Inserção bem-sucedida!";
            // Redirecionar para a página "calendario_agenda"
            header('Location: calendario_agenda.php');
            exit; // Certifique-se de sair do script após o redirecionamento
        } else {
            $response['success'] = false;
            $response['message'] = "Erro ao inserir evento no banco de dados.";
        }
    } else {
        $response['success'] = false;
        $response['message'] = "Dados incompletos ou sessão não iniciada.";
    }
} else {
    $response['success'] = false;
    $response['message'] = "Dados incompletos ou sessão não iniciada.";
}

// Enviar a resposta no formato JSON
header('Content-Type: application/json');
echo json_encode($response);
?>
