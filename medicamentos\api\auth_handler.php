<?php
// api/auth_handler.php
require_once '../includes/auth.php';

// Instanciar objeto de autenticação
$auth = new Auth();

// Método HTTP
$method = $_SERVER['REQUEST_METHOD'];

// Verificar se é uma solicitação POST
if ($method === 'POST') {
    // Obter dados do corpo da requisição
    $data = json_decode(file_get_contents('php://input'), true);

    if (!$data || !isset($data['usuario']) || !isset($data['senha'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Dados inválidos. Forneça usuário e senha.'
        ]);
        exit;
    }

    // Autenticar usuário usando a classe Auth existente
    $resultado = $auth->login($data['usuario'], $data['senha']);

    if ($resultado) {
        // Login bem-sucedido
        echo json_encode([
            'success' => true,
            'message' => 'Login realizado com sucesso',
            'usuario' => [
                'id' => $resultado['idusuario'],
                'nome' => $resultado['nome'],
                'usuario' => $resultado['usuario'],
                'email' => $resultado['email'],
                'is_admin' => (bool)$resultado['is_admin']
            ]
        ]);
    } else {
        // Falha no login
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'Usuário ou senha incorretos'
        ]);
    }
} else {
    // Método não permitido
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Método não permitido. Use POST para autenticação.'
    ]);
}
?>