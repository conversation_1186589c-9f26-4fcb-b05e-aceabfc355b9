<?php
function verificarAutenticacao($conexao) {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    if (!isset($_SESSION['idusuario'])) {
        header("Location: login_index.php");
        exit();
    }

    $id_usuario = filter_var($_SESSION['idusuario'], FILTER_VALIDATE_INT);
    
    // Verificação de status
    $query_validar = "
        SELECT idusuario, status, tentativas_falhas 
        FROM appEstudo.usuario 
        WHERE idusuario = $1";
    
    $result_validar = pg_query_params($conexao, $query_validar, array($id_usuario));
    $usuario = pg_fetch_assoc($result_validar);

    if (!$usuario) {
        // Registra o evento de falha
        $ip = $_SERVER['REMOTE_ADDR'];
        $query_log = "INSERT INTO appEstudo.log_seguranca (usuario_id, tipo_evento, ip) 
                      VALUES ($1, 'autenticacao_falha', $2)";
        pg_query_params($conexao, $query_log, array($id_usuario, $ip));
        
        session_destroy();
        header("Location: ../login_index.php?erro=usuario_invalido");
        exit();
    }

    // Verifica status do usuário
    if ($usuario['status'] !== 'ativo') {
        // Registra o evento de acesso com status inativo
        $ip = $_SERVER['REMOTE_ADDR'];
        $query_log = "INSERT INTO appEstudo.log_seguranca (usuario_id, tipo_evento, ip, detalhes) 
                      VALUES ($1, 'acesso_negado', $2, $3)";
        pg_query_params($conexao, $query_log, array($id_usuario, $ip, "Status: " . $usuario['status']));
        
        session_destroy();
        header("Location: ../login_index.php?erro=conta_" . $usuario['status']);
        exit();
    }

    // Registra acesso bem-sucedido
    $ip = $_SERVER['REMOTE_ADDR'];
    $query_log = "INSERT INTO appEstudo.log_seguranca (usuario_id, tipo_evento, ip) 
                  VALUES ($1, 'acesso_verificado', $2)";
    pg_query_params($conexao, $query_log, array($id_usuario, $ip));

    return $id_usuario;
}

// Nova função para registrar tentativa falha
function registrarTentativaFalha($conexao, $usuario_id) {
    // Incrementa contador de tentativas
    $query_update = "
        UPDATE appEstudo.usuario 
        SET tentativas_falhas = tentativas_falhas + 1
        WHERE idusuario = $1
        RETURNING tentativas_falhas";
    
    $result = pg_query_params($conexao, $query_update, array($usuario_id));
    $row = pg_fetch_assoc($result);
    
    // Se atingiu 10 tentativas, bloqueia a conta
    if ($row['tentativas_falhas'] >= 10) {
        $query_bloquear = "
            UPDATE appEstudo.usuario 
            SET status = 'bloqueado'
            WHERE idusuario = $1";
        pg_query_params($conexao, $query_bloquear, array($usuario_id));
    }

    // Registra no log
    $ip = $_SERVER['REMOTE_ADDR'];
    $query_log = "
        INSERT INTO appEstudo.log_seguranca (usuario_id, tipo_evento, ip) 
        VALUES ($1, 'login_falha', $2)";
    pg_query_params($conexao, $query_log, array($usuario_id, $ip));
}

// Nova função para resetar tentativas após login bem-sucedido
function resetarTentativas($conexao, $usuario_id) {
    $query = "
        UPDATE appEstudo.usuario 
        SET tentativas_falhas = 0
        WHERE idusuario = $1";
    pg_query_params($conexao, $query, array($usuario_id));
}

// Função para verificar rate limiting
function verificarRateLimit($conexao, $id_usuario, $acao) {
    $max_requisicoes = 100; // máximo de requisições por minuto
    $janela_tempo = 60;     // tempo em segundos
    
    $query = "SELECT COUNT(*) FROM appEstudo.log_requisicoes 
              WHERE usuario_id = $1 
              AND acao = $2 
              AND data_requisicao > CURRENT_TIMESTAMP - INTERVAL '$3 seconds'";
              
    $result = pg_query_params($conexao, $query, array($id_usuario, $acao, $janela_tempo));
    $count = pg_fetch_result($result, 0, 0);
    
    if ($count > $max_requisicoes) {
        http_response_code(429);
        die(json_encode(['erro' => 'Muitas requisições. Aguarde um momento.']));
    }
    
    // Registra a requisição
    pg_query_params($conexao, 
        "INSERT INTO appEstudo.log_requisicoes (usuario_id, acao) VALUES ($1, $2)",
        array($id_usuario, $acao)
    );
}

// Função para sanitizar dados de entrada
function sanitizarEntrada($dado) {
    $dado = trim($dado);
    $dado = stripslashes($dado);
    $dado = htmlspecialchars($dado, ENT_QUOTES, 'UTF-8');
    return $dado;
}

// Função para validar token CSRF
function verificarCSRF() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }

    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        if (!isset($_POST['csrf_token']) || $_POST['csrf_token'] !== $_SESSION['csrf_token']) {
            http_response_code(403);
            die('Token CSRF inválido');
        }
    }
}





