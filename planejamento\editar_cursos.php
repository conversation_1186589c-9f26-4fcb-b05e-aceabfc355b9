<?php
session_start();
include_once("../assets/config.php");

if (!isset($_SESSION['idusuario'])) {
    header('Location: login.php');
    exit;
}

$id_usuario = $_SESSION['idusuario'];

// Buscar cursos do usuário
$query_cursos = "SELECT * FROM appEstudo.curso";
$resultado = pg_query($conexao, $query_cursos);
$cursos = array();

while ($row = pg_fetch_assoc($resultado)) {
    $cursos[] = $row;
}

// Buscar cursos já selecionados pelo usuário
$query_selecionados = "SELECT curso_idcurso FROM appEstudo.usuario_has_curso WHERE usuario_idusuario = $id_usuario";
$resultado_selecionados = pg_query($conexao, $query_selecionados);
$cursos_selecionados = array();

while ($row = pg_fetch_assoc($resultado_selecionados)) {
    $cursos_selecionados[] = $row['curso_idcurso'];
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Seleção de Cursos</title>
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --primary: #00008B;
            --secondary: #f8f0e3;
            --accent: #4a4a4a;
            --border: #e0e0e0;
            --text: #2c3e50;
            --active: #00008B;
            --hover: #f5f5f5;
            --primary-blue: #00008B;
            --shadow-color: rgba(0, 0, 0, 0.1);
            --background: #f5f5f5;
            --card-background: white;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: var(--background);
            margin: 0;
            padding: 20px;
        }

        .app-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            background-color: var(--card-background);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px var(--shadow-color);
            text-align: center;
        }

        .header h1 {
            color: var(--primary);
            margin: 0;
        }

        .curso-container {
            background-color: var(--card-background);
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .curso-container h2 {
            color: var(--primary);
            margin-bottom: 20px;
        }

        .curso-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            transition: background-color 0.3s;
        }

        .curso-item:hover {
            background-color: var(--hover);
        }

        .curso-item label {
            margin-left: 10px;
            color: var(--text);
            cursor: pointer;
        }

        .btn-salvar {
            background-color: var(--primary);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-top: 20px;
            transition: opacity 0.3s;
        }

        .btn-salvar:hover {
            opacity: 0.9;
        }

        .mensagem {
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            text-align: center;
        }

        .mensagem.sucesso {
            background-color: #d4edda;
            color: #155724;
        }

        .mensagem.erro {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .search-box {
            position: relative;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .search-input {
            width: 100%;
            padding: 12px 40px 12px 15px;
            border: 1px solid var(--border);
            border-radius: 50px;
            font-size: 16px;
            transition: all 0.3s;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .search-input:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 2px rgba(0,0,139,0.2);
            outline: none;
        }
        
        .search-icon {
            position: absolute;
            right: 15px;
            color: var(--accent);
        }
        
        .resultados-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding: 8px 15px;
            background-color: #f0f7ff;
            border-radius: 5px;
            font-size: 14px;
            color: var(--primary);
        }
        
        .btn-limpar {
            background: none;
            border: none;
            color: var(--primary);
            font-size: 14px;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 3px;
            transition: background-color 0.2s;
        }
        
        .btn-limpar:hover {
            background-color: rgba(0,0,139,0.1);
        }
        
        .mensagem-vazia {
            padding: 30px;
            text-align: center;
            background-color: #f8f9fa;
            border-radius: 8px;
            color: var(--accent);
            margin: 20px 0;
        }
        
        .curso-item {
            animation: fadeIn 0.3s ease;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div id="app" class="app-container">
        <div class="header">
            <h1>Seleção de Cursos</h1>
        </div>

        <div class="curso-container">
            <h2>Escolha os Cursos que Você Utiliza:</h2>
            
            <div class="search-box">
                <input 
                    type="text" 
                    v-model="termoBusca" 
                    placeholder="Pesquisar cursos..."
                    @input="filtrarCursos"
                    class="search-input"
                >
                <span class="search-icon">
                    <i class="fas fa-search"></i>
                </span>
            </div>
            
            <div class="resultados-info" v-if="termoBusca.length > 0">
                <span>{{ cursosFiltrados.length }} curso(s) encontrado(s)</span>
                <button class="btn-limpar" @click="limparBusca">Limpar</button>
            </div>
            
            <div v-for="curso in cursosFiltrados" :key="curso.idcurso" class="curso-item">
                <input 
                    type="checkbox" 
                    :id="'curso-' + curso.idcurso"
                    v-model="cursosSelecionados"
                    :value="curso.idcurso"
                    @change="handleChange"
                >
                <label :for="'curso-' + curso.idcurso">{{ curso.nome }}</label>
            </div>
            
            <div class="mensagem-vazia" v-if="cursosFiltrados.length === 0">
                <p>Nenhum curso encontrado com o termo "{{ termoBusca }}"</p>
            </div>

            <button class="btn-salvar" @click="salvarCursos" :disabled="salvando">
                {{ salvando ? 'Salvando...' : 'Salvar Seleção' }}
            </button>

            <div v-if="mensagem" :class="['mensagem', mensagem.tipo]">
                {{ mensagem.texto }}
            </div>
        </div>
    </div>

    <script>
        const { createApp } = Vue

        createApp({
            data() {
                return {
                    cursos: <?php echo json_encode($cursos); ?>,
                    cursosSelecionados: <?php echo json_encode($cursos_selecionados); ?>,
                    salvando: false,
                    mensagem: null,
                    termoBusca: '',
                    cursosFiltrados: []
                }
            },
            created() {
                // Inicializar cursos filtrados com todos os cursos
                this.cursosFiltrados = [...this.cursos];
            },
            methods: {
                async salvarCursos() {
                    this.salvando = true
                    try {
                        const response = await fetch('salvar_cursos_usuario.php', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({
                                cursos: this.cursosSelecionados
                            })
                        })

                        const data = await response.json()
                        
                        this.mensagem = {
                            texto: data.success ? 'Cursos salvos com sucesso!' : 'Erro ao salvar cursos.',
                            tipo: data.success ? 'sucesso' : 'erro'
                        }

                        if (data.success && window.opener) {
                            window.opener.location.reload()
                        }
                    } catch (error) {
                        this.mensagem = {
                            texto: 'Erro ao salvar cursos.',
                            tipo: 'erro'
                        }
                    } finally {
                        this.salvando = false
                        setTimeout(() => {
                            this.mensagem = null
                        }, 3000)
                    }
                },
                handleChange() {
                    // Limpa mensagem quando usuário faz uma nova seleção
                    this.mensagem = null
                },
                filtrarCursos() {
                    if (!this.termoBusca.trim()) {
                        this.cursosFiltrados = [...this.cursos];
                        return;
                    }

                    const termo = this.termoBusca.toLowerCase().trim();
                    this.cursosFiltrados = this.cursos.filter(curso => 
                        curso.nome.toLowerCase().includes(termo)
                    );
                },
                limparBusca() {
                    this.termoBusca = '';
                    this.filtrarCursos();
                }
            }
        }).mount('#app')
    </script>
</body>
</html>