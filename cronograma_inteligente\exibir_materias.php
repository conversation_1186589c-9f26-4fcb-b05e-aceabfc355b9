<?php
session_start();
if (!isset($_SESSION['idusuario']) || $_SESSION['idusuario'] != 1) {
    header("Location: login.php");
    exit();
}

include_once("assets/config.php");

// Obter o ID do edital da URL
$edital_id = isset($_GET['edital_id']) ? (int) $_GET['edital_id'] : 0;
if (!$edital_id) {
    header("Location: listagem_editais.php");
    exit();
}

// Buscar o nome do edital
$query_edital = "SELECT nome FROM appestudo.edital WHERE id_edital = $edital_id";
$result_edital = pg_query($conexao, $query_edital);
$edital = pg_fetch_assoc($result_edital);
if (!$edital) {
    die("Edital não encontrado.");
}

$usuario_id = $_SESSION['idusuario'];
$query_selecionados = "
    SELECT conteudo_id 
    FROM appestudo.usuario_conteudo 
    WHERE usuario_id = $usuario_id AND status = true
";
$result_selecionados = pg_query($conexao, $query_selecionados);

$conteudos_selecionados = [];
while ($row = pg_fetch_assoc($result_selecionados)) {
    $conteudos_selecionados[] = $row['conteudo_id'];
}

// Consulta para listar matérias e conteúdos
$query_materias = "
    SELECT 
        m.idmateria, 
        m.nome AS materia_nome, 
        m.cor, 
        c.descricao, 
        c.capitulo, 
        c.ordem,
        c.id_conteudo,
        CAST(split_part(c.capitulo, '.', 1) AS INTEGER) AS capitulo_principal,
        COALESCE(CAST(NULLIF(split_part(c.capitulo, '.', 2), '') AS INTEGER), 0) AS subnivel1,
        COALESCE(CAST(NULLIF(split_part(c.capitulo, '.', 3), '') AS INTEGER), 0) AS subnivel2
    FROM 
        appestudo.conteudo_edital AS c
    JOIN 
        appestudo.materia AS m ON c.materia_id = m.idmateria
    WHERE 
        c.edital_id = $edital_id
    ORDER BY 
        capitulo_principal, subnivel1, subnivel2, c.ordem;
";

$result_materias = pg_query($conexao, $query_materias);
if (!$result_materias) {
    die("Erro ao buscar as matérias do edital.");
}

// Organizar as matérias
$materias = [];
while ($row = pg_fetch_assoc($result_materias)) {
    $materia_id = $row['idmateria'];
    if (!isset($materias[$materia_id])) {
        $materias[$materia_id] = [
            'nome' => $row['materia_nome'],
            'cor' => $row['cor'],
            'conteudos' => []
        ];
    }
    $materias[$materia_id]['conteudos'][] = [
        'descricao' => $row['descricao'],
        'capitulo' => $row['capitulo'],
        'id_conteudo' => $row['id_conteudo']
    ];
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Matérias do Edital - <?= htmlspecialchars($edital['nome']) ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700&family=Old+Standard+TT:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #B85C5C; /* Vermelho claro queimado */
            --secondary-color: #D2691E;
            --background-color: #F5E6D3;
            --paper-color: #EDE3D0; /* Papel velho um pouco mais claro o*/
            --text-color: #2C1810;
            --border-color: #8B4513;
            --accent-gold: #DAA520;
            --shadow-color: rgba(139, 69, 19, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Old Standard TT', serif;
            min-height: 100vh;
            background: var(--background-color);
            background-image:
                    linear-gradient(rgba(245, 230, 211, 0.9), rgba(245, 230, 211, 0.9)),
                    url("data:image/svg+xml,%3Csvg width='52' height='26' viewBox='0 0 52 26' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%238b4513' fill-opacity='0.1'%3E%3Cpath d='M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z' /%3E%3C/g%3E%3C/svg%3E");
            padding: 40px 20px;
            color: var(--text-color);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
        }

        /* Botão Voltar */
        .btn-voltar {
            position: fixed;
            top: 20px;
            left: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 50px;
            height: 50px;
            background: var(--paper-color);
            border: 2px solid var(--border-color);
            border-radius: 50%;
            box-shadow: 4px 4px 0 var(--border-color);
            color: var(--primary-color);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .btn-voltar::before {
            content: '';
            position: absolute;
            width: calc(100% + 4px);
            height: calc(100% + 4px);
            border: 1px solid var(--border-color);
            border-radius: 50%;
            opacity: 0.5;
            transform: scale(0.9);
            transition: all 0.3s ease;
        }

        .btn-voltar:hover {
            transform: translateY(-2px);
            box-shadow: 4px 4px 0 var(--border-color);
            background-color: var(--secondary-color);
            color: white;
        }

        .btn-voltar:hover::before {
            transform: scale(1.1);
            opacity: 0;
        }

        /* Header */
        .header-vintage {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .header-vintage h2 {
            font-family: 'Playfair Display', serif;
            font-size: 2.5rem;
            color: var(--primary-color);
            text-transform: uppercase;
            letter-spacing: 2px;
            position: relative;
            display: inline-block;
            margin-bottom: 20px;
        }

        .header-vintage h2::after {
            content: "";
            display: block;
            width: 150px;
            height: 3px;
            background: var(--primary-color);
            margin: 15px auto 0;
        }

        /* Container Principal */
        .materias-container {
            background: var(--paper-color);
            border-radius: 10px;
            padding: 40px;
            box-shadow: 8px 8px 0 var(--border-color);
            border: 2px solid var(--border-color);
            position: relative;
            margin-bottom: 40px;
        }

        /* Seleção Global */
        .select-all-global-wrapper {
            background: white;
            padding: 20px;
            margin-bottom: 30px;
            border: 2px solid var(--border-color);
            box-shadow: 4px 4px 0 var(--border-color);
            border-radius: 8px;
            text-align: center;
        }

        .select-all-global {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            padding: 12px 24px;
            background: var(--paper-color);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .select-all-global:hover {
            background: var(--background-color);
            transform: translateY(-2px);
        }

        .select-all-global-label {
            font-family: 'Playfair Display', serif;
            color: var(--primary-color);
            font-size: 1.1rem;
            font-weight: 600;
        }
        /* Grupos de Matérias */
        .materia-group {
            background: white;
            border: 2px solid var(--border-color);
            box-shadow: 4px 4px 0 var(--border-color);
            margin-bottom: 20px;
            border-radius: 8px;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .materia-group:hover {
            transform: translate(-2px, -2px);
            box-shadow: 6px 6px 0 var(--border-color);
        }

        .materia-header {
            padding: 20px;
            background: var(--paper-color);
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .materia-header-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .materia-icon {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: var(--primary-color);
            border-radius: 50%;
            color: var(--paper-color);
            font-size: 1.5rem;
            border: 2px solid var(--border-color);
            transition: all 0.3s ease;
        }

        .materia-group:hover .materia-icon {
            transform: rotate(5deg);
        }

        .materia-nome {
            font-family: 'Playfair Display', serif;
            font-size: 1.4rem;
            color: var(--primary-color);
        }

        /* Controles da Matéria */
        .materia-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .select-all-wrapper {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 15px;
            background: white;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .select-all-wrapper:hover {
            background: var(--background-color);
            transform: translateY(-2px);
        }

        .toggle-icon {
            transition: transform 0.3s ease;
            color: var(--primary-color);
            font-size: 1.2rem;
        }

        .materia-group.expanded .toggle-icon {
            transform: rotate(180deg);
        }

        /* Lista de Conteúdos */
        .conteudo-list {
            padding: 20px;
            background: white;
            display: none;
        }

        .materia-group.expanded .conteudo-list {
            display: block;
            animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .conteudo-item {
            padding: 15px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            margin-bottom: 10px;
            background: var(--paper-color);
            transition: all 0.3s ease;
        }

        .conteudo-item:hover {
            transform: translateX(5px);
            box-shadow: 3px 3px 0 var(--border-color);
        }

        .conteudo-item label {
            display: flex;
            align-items: center;
            gap: 15px;
            cursor: pointer;
        }

        .capitulo-number {
            font-family: 'Old Standard TT', monospace;
            color: var(--primary-color);
            font-weight: bold;
            min-width: 50px;
        }

        /* Checkboxes Estilizados */
        input[type="checkbox"] {
            appearance: none;
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-radius: 4px;
            background: white;
            cursor: pointer;
            position: relative;
            transition: all 0.2s ease;
        }

        input[type="checkbox"]:checked {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        input[type="checkbox"]:checked::before {
            content: '✓';
            position: absolute;
            color: white;
            font-size: 14px;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        input[type="checkbox"]:indeterminate {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }

        input[type="checkbox"]:indeterminate::before {
            content: '';
            position: absolute;
            width: 10px;
            height: 2px;
            background: white;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        /* Botão Salvar */
        .btn-salvar {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            gap: 10px;
            padding: 15px 30px;
            background: var(--primary-color);
            color: var(--paper-color);
            border: 2px solid var(--border-color);
            box-shadow: 4px 4px 0 var(--border-color);
            font-family: 'Playfair Display', serif;
            font-size: 1.2rem;
            text-transform: uppercase;
            letter-spacing: 2px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 30px;
        }

        .btn-salvar:hover {
            transform: translate(-2px, -2px);
            box-shadow: 6px 6px 0 var(--border-color);
            background: var(--secondary-color);
        }

        .btn-salvar i {
            transition: transform 0.3s ease;
        }

        .btn-salvar:hover i {
            transform: translateX(5px);
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }

            .header-vintage h2 {
                font-size: 1.8rem;
            }

            .materias-container {
                padding: 20px;
            }

            .materia-header {
                padding: 15px;
                flex-direction: column;
                gap: 10px;
            }

            .materia-controls {
                width: 100%;
                justify-content: space-between;
            }

            .btn-voltar {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }

            .select-all-wrapper {
                padding: 6px 10px;
            }

            .btn-salvar {
                padding: 12px 20px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
<a href="listagem_editais.php" class="btn-voltar">
    <i class="fas fa-arrow-left"></i>
</a>

<div class="container">
    <div class="header-vintage">
        <h2>Matérias do Edital: <?= htmlspecialchars($edital['nome']) ?></h2>
    </div>

    <div class="select-all-global-wrapper">
        <label class="select-all-global">
            <input type="checkbox" id="selectAllGlobal">
            <span class="select-all-global-label">Selecionar Todos os Conteúdos</span>
        </label>
    </div>

    <div class="materias-container">
        <form action="selecionar_estudo.php" method="POST">
            <?php foreach ($materias as $id => $materia): ?>
                <div class="materia-group">
                    <div class="materia-header" style="border-bottom: 2px solid <?= htmlspecialchars($materia['cor']) ?>">
                        <div class="materia-header-content">
                            <div class="materia-icon" style="background-color: <?= htmlspecialchars($materia['cor']) ?>">
                                <i class="fas fa-book"></i>
                            </div>
                            <h3 class="materia-nome"><?= htmlspecialchars($materia['nome']) ?></h3>
                        </div>
                        <div class="materia-controls">
                            <div class="select-all-wrapper">
                                <input type="checkbox" class="select-all-checkbox" data-materia-id="<?= htmlspecialchars($id) ?>">
                                <span class="select-all-label">Selecionar Todos</span>
                            </div>
                            <i class="fas fa-chevron-down toggle-icon"></i>
                        </div>
                    </div>
                    <div class="conteudo-list">
                        <?php foreach ($materia['conteudos'] as $conteudo): ?>
                            <?php $checked = in_array($conteudo['id_conteudo'], $conteudos_selecionados) ? 'checked' : ''; ?>
                            <div class="conteudo-item">
                                <label>
                                    <input type="checkbox" name="conteudos[]"
                                           value="<?= htmlspecialchars($conteudo['id_conteudo']) ?>"
                                           class="conteudo-checkbox"
                                           data-materia-id="<?= htmlspecialchars($id) ?>"
                                        <?= $checked ?>>
                                    <span class="capitulo-number"><?= htmlspecialchars($conteudo['capitulo']) ?></span>
                                    <span><?= htmlspecialchars($conteudo['descricao']) ?></span>
                                </label>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            <?php endforeach; ?>

            <button type="submit" class="btn-salvar">
                Selecionar para Estudo
                <i class="fas fa-arrow-right"></i>
            </button>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const selectAllGlobal = document.getElementById('selectAllGlobal');

        // Função para alternar expansão da matéria
        function toggleMateria(header, event) {
            if (event && (event.target.closest('.select-all-wrapper') || event.target.type === 'checkbox')) {
                return;
            }
            const materiaGroup = header.closest('.materia-group');
            materiaGroup.classList.toggle('expanded');
        }

        // Eventos para expandir/retrair
        document.querySelectorAll('.materia-header').forEach(header => {
            header.addEventListener('click', (event) => toggleMateria(header, event));
        });

        // Manipulador para checkbox global
        selectAllGlobal.addEventListener('change', function() {
            const allCheckboxes = document.querySelectorAll('.conteudo-checkbox');
            const allMateriaCheckboxes = document.querySelectorAll('.select-all-checkbox');

            allCheckboxes.forEach(cb => {
                cb.checked = this.checked;
            });

            allMateriaCheckboxes.forEach(cb => {
                cb.checked = this.checked;
                cb.indeterminate = false;
            });

            document.querySelectorAll('.materia-group').forEach(group => {
                if (this.checked) {
                    group.classList.add('expanded');
                }
            });
        });

        // Manipulador para "Selecionar Todos" de cada matéria
        document.querySelectorAll('.select-all-checkbox').forEach(checkbox => {
            checkbox.addEventListener('click', function(event) {
                event.stopPropagation();
                const materiaId = this.dataset.materiaId;
                const materiaGroup = this.closest('.materia-group');
                const conteudoCheckboxes = document.querySelectorAll(
                    `.conteudo-checkbox[data-materia-id="${materiaId}"]`
                );

                conteudoCheckboxes.forEach(cb => {
                    cb.checked = this.checked;
                });

                if (this.checked) {
                    materiaGroup.classList.add('expanded');
                }

                updateGlobalCheckboxState();
            });
        });

        // Manipulador para checkboxes individuais
        document.querySelectorAll('.conteudo-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const materiaId = this.dataset.materiaId;
                const materiaGroup = this.closest('.materia-group');

                updateMateriaCheckboxState(materiaId);
                updateGlobalCheckboxState();

                if (this.checked) {
                    materiaGroup.classList.add('expanded');
                }
            });
        });

        // Atualizar estado do checkbox da matéria
        function updateMateriaCheckboxState(materiaId) {
            const materiaCheckbox = document.querySelector(
                `.select-all-checkbox[data-materia-id="${materiaId}"]`
            );
            const conteudoCheckboxes = document.querySelectorAll(
                `.conteudo-checkbox[data-materia-id="${materiaId}"]`
            );

            const allChecked = Array.from(conteudoCheckboxes).every(cb => cb.checked);
            const someChecked = Array.from(conteudoCheckboxes).some(cb => cb.checked);

            materiaCheckbox.checked = allChecked;
            materiaCheckbox.indeterminate = someChecked && !allChecked;
        }

        // Atualizar estado do checkbox global
        function updateGlobalCheckboxState() {
            const allCheckboxes = document.querySelectorAll('.conteudo-checkbox');
            const allChecked = Array.from(allCheckboxes).every(cb => cb.checked);
            const someChecked = Array.from(allCheckboxes).some(cb => cb.checked);

            selectAllGlobal.checked = allChecked;
            selectAllGlobal.indeterminate = someChecked && !allChecked;
        }

        // Inicializar estados
        document.querySelectorAll('.materia-group').forEach(group => {
            const materiaId = group.querySelector('.select-all-checkbox').dataset.materiaId;
            updateMateriaCheckboxState(materiaId);
        });
        updateGlobalCheckboxState();
    });
</script>

</body>
</html>