<?php
/**
 * Script de Migração para Novo Sistema de ID
 * Sistema de Fidelidade Barbearia
 * 
 * Este script migra os IDs dos usuários do formato antigo (nome-codigo-cpf)
 * para o novo formato (cpf-tipo)
 * 
 * ATENÇÃO: Execute este script apenas uma vez e faça backup antes!
 */

require_once 'config/database.php';
require_once 'config/response.php';

class MigrationManager {
    private $db;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }
    
    /**
     * Executar migração completa
     */
    public function runMigration() {
        try {
            echo "=== INICIANDO MIGRAÇÃO PARA NOVO SISTEMA DE ID ===\n\n";
            
            // 1. Verificar estado atual
            $this->checkCurrentState();
            
            // 2. Criar backups
            $this->createBackups();
            
            // 3. Verificar conflitos
            $conflicts = $this->checkConflicts();
            if (!empty($conflicts)) {
                echo "❌ CONFLITOS ENCONTRADOS! Resolva antes de continuar:\n";
                foreach ($conflicts as $conflict) {
                    echo "- CPF: {$conflict['cpf']}, Tipo: {$conflict['tipo']}, IDs: {$conflict['old_ids']}\n";
                }
                return false;
            }
            
            // 4. Executar migração
            $this->executeMigration();
            
            // 5. Verificar resultado
            $this->verifyMigration();
            
            echo "\n✅ MIGRAÇÃO CONCLUÍDA COM SUCESSO!\n";
            return true;
            
        } catch (Exception $e) {
            echo "❌ ERRO NA MIGRAÇÃO: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * Verificar estado atual do banco
     */
    private function checkCurrentState() {
        echo "📊 Verificando estado atual do banco...\n";
        
        $sql = "SELECT COUNT(*) as total FROM usuarios";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $total = $stmt->fetch()['total'];
        
        echo "- Total de usuários: $total\n";
        
        // Verificar quantos já estão no novo formato
        $sql = "SELECT COUNT(*) as total FROM usuarios WHERE id REGEXP '^[0-9]{11}-(CLIENTE|BARBEIRO)$'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $newFormat = $stmt->fetch()['total'];
        
        echo "- Usuários já no novo formato: $newFormat\n";
        echo "- Usuários a migrar: " . ($total - $newFormat) . "\n\n";
    }
    
    /**
     * Criar tabelas de backup
     */
    private function createBackups() {
        echo "💾 Criando backups das tabelas...\n";
        
        $tables = [
            'usuarios',
            'perfis_clientes',
            'pontuacao_fidelidade',
            'historico_atendimentos',
            'brindes_pendentes',
            'historico_brindes_entregues',
            'recompensas_resgatadas',
            'barbeiro_master'
        ];
        
        foreach ($tables as $table) {
            $sql = "CREATE TABLE {$table}_backup AS SELECT * FROM $table";
            $this->db->exec($sql);
            echo "- Backup criado: {$table}_backup\n";
        }
        
        echo "\n";
    }
    
    /**
     * Verificar conflitos (mesmo CPF e tipo)
     */
    private function checkConflicts() {
        echo "🔍 Verificando conflitos...\n";
        
        $sql = "SELECT 
                    cpf, 
                    tipo, 
                    COUNT(*) as count,
                    GROUP_CONCAT(id) as old_ids
                FROM usuarios 
                GROUP BY cpf, tipo 
                HAVING COUNT(*) > 1";
        
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $conflicts = $stmt->fetchAll();
        
        if (empty($conflicts)) {
            echo "✅ Nenhum conflito encontrado\n\n";
        }
        
        return $conflicts;
    }
    
    /**
     * Executar migração dos dados
     */
    private function executeMigration() {
        echo "🔄 Executando migração dos dados...\n";
        
        $this->db->beginTransaction();
        
        try {
            // Desabilitar verificações de chave estrangeira
            $this->db->exec("SET FOREIGN_KEY_CHECKS = 0");
            
            // Criar tabela de mapeamento
            $this->createMappingTable();
            
            // Atualizar cada tabela
            $this->updateUsersTable();
            $this->updateProfilesTable();
            $this->updateFidelityTable();
            $this->updateHistoryTable();
            $this->updatePendingRewardsTable();
            $this->updateDeliveredRewardsTable();
            $this->updateRedeemedRewardsTable();
            $this->updateMasterTable();
            
            // Reabilitar verificações de chave estrangeira
            $this->db->exec("SET FOREIGN_KEY_CHECKS = 1");
            
            $this->db->commit();
            echo "✅ Migração executada com sucesso\n\n";
            
        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }
    
    /**
     * Criar tabela de mapeamento
     */
    private function createMappingTable() {
        $sql = "CREATE TEMPORARY TABLE id_mapping (
            old_id VARCHAR(100),
            new_id VARCHAR(100),
            cpf VARCHAR(11),
            tipo ENUM('cliente', 'barbeiro'),
            PRIMARY KEY (old_id),
            UNIQUE KEY (new_id)
        )";
        $this->db->exec($sql);
        
        $sql = "INSERT INTO id_mapping (old_id, new_id, cpf, tipo)
                SELECT 
                    id as old_id,
                    CONCAT(cpf, '-', UPPER(tipo)) as new_id,
                    cpf,
                    tipo
                FROM usuarios
                WHERE id NOT REGEXP '^[0-9]{11}-(CLIENTE|BARBEIRO)$'";
        $this->db->exec($sql);
        
        echo "- Tabela de mapeamento criada\n";
    }
    
    /**
     * Atualizar tabela usuarios
     */
    private function updateUsersTable() {
        $sql = "UPDATE usuarios u
                JOIN id_mapping m ON u.id = m.old_id
                SET u.id = m.new_id";
        $affected = $this->db->exec($sql);
        echo "- Tabela usuarios atualizada ($affected registros)\n";
    }
    
    /**
     * Atualizar tabela perfis_clientes
     */
    private function updateProfilesTable() {
        $sql = "UPDATE perfis_clientes pc
                JOIN id_mapping m ON pc.usuario_id = m.old_id
                SET pc.usuario_id = m.new_id";
        $affected = $this->db->exec($sql);
        echo "- Tabela perfis_clientes atualizada ($affected registros)\n";
    }
    
    /**
     * Atualizar tabela pontuacao_fidelidade
     */
    private function updateFidelityTable() {
        $sql = "UPDATE pontuacao_fidelidade pf
                JOIN id_mapping m ON pf.cliente_id = m.old_id
                SET pf.cliente_id = m.new_id";
        $affected = $this->db->exec($sql);
        echo "- Tabela pontuacao_fidelidade atualizada ($affected registros)\n";
    }
    
    /**
     * Atualizar tabela historico_atendimentos
     */
    private function updateHistoryTable() {
        $sql = "UPDATE historico_atendimentos ha
                JOIN id_mapping m ON ha.cliente_id = m.old_id
                SET ha.cliente_id = m.new_id";
        $affected = $this->db->exec($sql);
        echo "- Tabela historico_atendimentos atualizada ($affected registros)\n";
    }
    
    /**
     * Atualizar tabela brindes_pendentes
     */
    private function updatePendingRewardsTable() {
        $sql = "UPDATE brindes_pendentes bp
                JOIN id_mapping m ON bp.cliente_id = m.old_id
                SET bp.cliente_id = m.new_id";
        $affected = $this->db->exec($sql);
        echo "- Tabela brindes_pendentes atualizada ($affected registros)\n";
    }
    
    /**
     * Atualizar tabela historico_brindes_entregues
     */
    private function updateDeliveredRewardsTable() {
        $sql = "UPDATE historico_brindes_entregues hbe
                JOIN id_mapping m ON hbe.cliente_id = m.old_id
                SET hbe.cliente_id = m.new_id";
        $affected = $this->db->exec($sql);
        echo "- Tabela historico_brindes_entregues atualizada ($affected registros)\n";
    }
    
    /**
     * Atualizar tabela recompensas_resgatadas
     */
    private function updateRedeemedRewardsTable() {
        $sql = "UPDATE recompensas_resgatadas rr
                JOIN id_mapping m ON rr.cliente_id = m.old_id
                SET rr.cliente_id = m.new_id";
        $affected = $this->db->exec($sql);
        echo "- Tabela recompensas_resgatadas atualizada ($affected registros)\n";
    }
    
    /**
     * Atualizar tabela barbeiro_master
     */
    private function updateMasterTable() {
        $sql = "UPDATE barbeiro_master bm
                JOIN id_mapping m ON bm.dono_id = m.old_id
                SET bm.dono_id = m.new_id";
        $affected = $this->db->exec($sql);
        echo "- Tabela barbeiro_master atualizada ($affected registros)\n";
    }
    
    /**
     * Verificar resultado da migração
     */
    private function verifyMigration() {
        echo "🔍 Verificando resultado da migração...\n";
        
        // Verificar formato dos IDs
        $sql = "SELECT COUNT(*) as total FROM usuarios WHERE id REGEXP '^[0-9]{11}-(CLIENTE|BARBEIRO)$'";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $correctFormat = $stmt->fetch()['total'];
        
        $sql = "SELECT COUNT(*) as total FROM usuarios";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $totalUsers = $stmt->fetch()['total'];
        
        echo "- Usuários com formato correto: $correctFormat/$totalUsers\n";
        
        // Verificar IDs duplicados
        $sql = "SELECT id, COUNT(*) as count FROM usuarios GROUP BY id HAVING COUNT(*) > 1";
        $stmt = $this->db->prepare($sql);
        $stmt->execute();
        $duplicates = $stmt->fetchAll();
        
        if (empty($duplicates)) {
            echo "✅ Nenhum ID duplicado encontrado\n";
        } else {
            echo "❌ IDs duplicados encontrados:\n";
            foreach ($duplicates as $dup) {
                echo "- ID: {$dup['id']}, Count: {$dup['count']}\n";
            }
        }
    }
}

// Executar migração se chamado diretamente
if (php_sapi_name() === 'cli') {
    $migration = new MigrationManager();
    $success = $migration->runMigration();
    exit($success ? 0 : 1);
}
?>
