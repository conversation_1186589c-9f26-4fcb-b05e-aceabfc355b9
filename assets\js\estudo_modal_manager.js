// estudo_modal_manager.js
class EstudoModalManager {
    constructor() {
        console.log('EstudoModalManager: Construtor iniciado');
        this.modalAtivo = false;
        this.init();
    }

    init() {
        $('.ultimo-estudo, .proxima-materia').on('click', (e) => {
            if (this.modalAtivo) return;

            const $target = $(e.currentTarget);
            // Adicione esta verificação:
            if ($target.hasClass('evento-link') && !$target.hasClass('estudo-evento')) return;

            const dados = $target.data('estudo');
            if (dados) {
                this.mostrarModal(dados);
            }
        });
    }

    mostrarModal(dados) {
        console.log('EstudoModalManager: Mostrando modal com dados:', dados);

        // Limpa todos os modais existentes
        $('.modal-estudo').remove();
        $('.modal-agenda').remove();
        $('#modalEvento').remove();

        const modalContent = `
            <div class="modal-conteudo">
                <h4><strong>Matéria:</strong> ${dados.nome_materia || dados.nome_materia_estudo || 'Não definido'}</h4>
                <h4><strong>Curso:</strong> ${dados.nome_curso || 'Não definido'}</h4>
                <h4><strong>Método:</strong> ${dados.metodo || 'Não definido'}</h4>
                <h4><strong>Início:</strong> ${dados.hora_inicio || 'Não definido'}</h4>
                <h4><strong>Fim:</strong> ${dados.hora_fim || 'Não definido'}</h4>
                ${dados.tempo_liquido ? `<h4><strong>Tempo Líquido:</strong> ${dados.tempo_liquido}</h4>` : ''}
                ${dados.tempo_perdido ? `<h4><strong>Tempo Perdido:</strong> ${dados.tempo_perdido}</h4>` : ''}
                
                <div class="details-box p-3 mt-2 bg-light rounded">
                    <h5><strong>Ponto Estudado:</strong></h5>
                    <p>${dados.ponto_estudado || 'Não definido'}</p>
                </div>
            </div>
        `;

        this.abrirModal(modalContent, 'Detalhes do Estudo');
    }

    abrirModal(conteudo, titulo) {
        console.log('EstudoModalManager: Abrindo modal');

        const modalHTML = `
            <div id="modal-estudo-materias" class="modal-estudo ativo">
                <div class="modal-overlay"></div>
                <div class="modal-content">
                    <button class="modal-close">
                        <i class="fas fa-times"></i>
                    </button>
                    <div class="modal-body">
                        <h3 class="modal-titulo">${titulo}</h3>
                        <div class="modal-conteudo">
                            ${conteudo}
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove quaisquer modais existentes
        this.fecharModal();

        // Adiciona o novo modal
        const modalElement = $(modalHTML).appendTo('body');
        document.body.style.overflow = 'hidden';
        this.modalAtivo = true;

        // Bind dos eventos
        const self = this;
        modalElement.find('.modal-close, .modal-overlay').on('click', () => {
            console.log('EstudoModalManager: Clique para fechar modal');
            self.fecharModal();
        });

        $(document).on('keydown.estudoModal', (e) => {
            if (e.key === 'Escape') {
                console.log('EstudoModalManager: ESC pressionado');
                self.fecharModal();
            }
        });
    }

    fecharModal() {
        console.log('EstudoModalManager: Fechando modal');
        $('#modal-estudo-materias').remove();
        document.body.style.overflow = '';
        $(document).off('keydown.estudoModal');
        this.modalAtivo = false;
    }
}

// Inicialização única
console.log('EstudoModalManager: Verificando instância existente');
if (!window.estudoModalManager) {
    console.log('EstudoModalManager: Criando nova instância');
    window.estudoModalManager = new EstudoModalManager();
} else {
    console.log('EstudoModalManager: Instância já existe');
}