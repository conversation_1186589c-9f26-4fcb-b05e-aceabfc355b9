<?php
// criar_topico.php
session_start();
include_once("assets/config.php");
include_once("helpers.php");

if (!isset($_SESSION['idusuario'])) {
    header("Location: login_index.php");
    exit();
}

redirectIfNotAdmin($conexao, $_SESSION['idusuario']);

if (!isset($_GET['baralho'])) {
    header("Location: flashcards.php");
    exit();
}

$baralho_id = (int)$_GET['baralho'];
$mensagem = '';

// Buscar informações do baralho e categoria
$query_baralho = "
    SELECT d.nome as deck_name, d.materia_id, c.nome as categoria_name, c.id as categoria_id, m.nome as materia_nome
    FROM appestudo.flashcard_decks d
    JOIN appestudo.flashcard_categories c ON c.id = d.category_id
    JOIN appestudo.materia m ON m.idmateria = d.materia_id
    WHERE d.id = $1";
$result_baralho = pg_query_params($conexao, $query_baralho, array($baralho_id));
$baralho = pg_fetch_assoc($result_baralho);

if (!$baralho) {
    header("Location: flashcards.php");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nome = trim($_POST['nome']);
    $descricao = trim($_POST['descricao']);
    
    if (!empty($nome)) {
        // Verificar se já existe um tópico com este nome no baralho
        $query_check = "
            SELECT id FROM appestudo.flashcard_topics 
            WHERE deck_id = $1 AND LOWER(nome) = LOWER($2)";
        $result_check = pg_query_params($conexao, $query_check, array($baralho_id, $nome));
        
        if (pg_num_rows($result_check) > 0) {
            $mensagem = "Já existe um tópico com este nome neste baralho.";
        } else {
            // Buscar a maior ordem atual
            $query_ordem = "
                SELECT COALESCE(MAX(ordem), 0) + 1 as proxima_ordem 
                FROM appestudo.flashcard_topics 
                WHERE deck_id = $1";
            $result_ordem = pg_query_params($conexao, $query_ordem, array($baralho_id));
            $ordem = pg_fetch_assoc($result_ordem)['proxima_ordem'];
            
            $query = "
                INSERT INTO appestudo.flashcard_topics 
                    (deck_id, nome, descricao, ordem, status) 
                VALUES ($1, $2, $3, $4, true)";
            
            $result = pg_query_params($conexao, $query, array(
                $baralho_id, 
                $nome, 
                $descricao, 
                $ordem
            ));
            
            if ($result) {
                header("Location: ver_topicos.php?baralho=" . $baralho_id);
                exit();
            } else {
                $mensagem = "Erro ao criar tópico. Tente novamente.";
            }
        }
    } else {
        $mensagem = "O nome do tópico é obrigatório.";
    }
}
?>

<?php
// [Manter todo o código PHP anterior até o DOCTYPE] ?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Novo Tópico - <?php echo htmlspecialchars($baralho['deck_name']); ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&family=Quicksand:wght@300..700&family=Roboto+Condensed:ital,wght@0,100..900;1,100..900&family=Tektur:wght@400..900&family=Varela+Round&display=swap" rel="stylesheet">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');

        :root {
            --primary-color: #000080;
            --paper-color: #FFFFFF;
            --secondary-color: #4169E1;
            --background-color: #E8ECF3;
            --text-color: #2C3345;
            --border-color: #B8C2CC;
        }

        body {
            font-family: 'Varela Round', 'Quicksand', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            margin: 0;
            padding: 20px;
            line-height: 1.7;
            min-height: 100vh;
            letter-spacing: -0.011em;
        }

        .container {
            max-width: 1000px;
            margin: 20px auto 0;
            width: calc(100% - 40px);
            position: relative;
        }

        .form-container {
            background: var(--paper-color);
            padding: 40px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            box-shadow: 4px 4px 0 var(--border-color);
            margin-top: 60px;
        }

        .breadcrumb {
            color: var(--secondary-color);
            font-style: italic;
            margin-bottom: 20px;
            font-size: 0.9375rem;
            font-weight: 500;
        }

        h1 {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 30px;
            font-size: 1.8rem;
        }

        .form-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            color: var(--primary-color);
            margin-bottom: 8px;
            font-weight: 500;
            font-size: 1rem;
        }

        input[type="text"],
        textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
            font-size: 1rem;
            color: var(--text-color);
            line-height: 1.7;
            background: white;
            transition: border-color 0.3s ease;
            box-sizing: border-box;
        }

        textarea {
            min-height: 120px;
            resize: vertical;
        }

        input[type="text"]:focus,
        textarea:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .actions {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        .btn {
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-family: 'Inter', system-ui, -apple-system, sans-serif;
            font-size: 1rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
            border: none;
            box-shadow: 4px 4px 0 var(--border-color);
        }

        .btn-secondary {
            background: var(--paper-color);
            color: var(--primary-color);
            border: 2px solid var(--border-color);
            box-shadow: 4px 4px 0 var(--border-color);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 6px 6px 0 var(--border-color);
        }

        .error-message {
            background: #FEE;
            border: 2px solid #FAA;
            color: #A00;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 25px;
            font-size: 0.9375rem;
        }

        .btn-back {
            position: absolute;
            top: -75px;
            left: 0;
            width: 50px;
            height: 50px;
            background: var(--paper-color);
            border: 2px solid var(--border-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--primary-color);
            text-decoration: none;
            font-size: 1.2rem;
            transition: all 0.3s ease;
            box-shadow: 4px 4px 0 var(--border-color);
        }

        .btn-back:hover {
            transform: translateY(-2px);
            background: var(--primary-color);
            color: white;
            box-shadow: 6px 6px 0 var(--border-color);
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .container {
                width: calc(100% - 20px);
                margin-top: 70px;
            }

            .form-container {
                padding: 20px;
                margin-top: 20px;
            }

            .actions {
                flex-direction: column-reverse;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }

            .btn-back {
                position: fixed;
                top: 10px;
                left: 10px;
                z-index: 1000;
            }
        }

        @media (max-width: 480px) {
            .form-container {
                padding: 15px;
            }

            h1 {
                font-size: 1.5rem;
            }

            .breadcrumb {
                font-size: 0.875rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <a href="ver_topicos.php?baralho=<?php echo $baralho_id; ?>" class="btn-back">
            <i class="fas fa-arrow-left"></i>
        </a>

        <div class="form-container">
            <div class="breadcrumb">
                <?php echo htmlspecialchars($baralho['categoria_name']); ?> > 
                <?php echo htmlspecialchars($baralho['materia_nome']); ?>
            </div>

            <h1>Novo Tópico</h1>

            <?php if (!empty($mensagem)): ?>
                <div class="error-message">
                    <i class="fas fa-exclamation-circle"></i>
                    <?php echo htmlspecialchars($mensagem); ?>
                </div>
            <?php endif; ?>

            <form method="POST">
                <div class="form-group">
                    <label for="nome">Nome do Tópico*</label>
                    <input type="text" id="nome" name="nome" required 
                           value="<?php echo isset($_POST['nome']) ? htmlspecialchars($_POST['nome']) : ''; ?>"
                           placeholder="Ex: Princípios Constitucionais">
                </div>

                <div class="form-group">
                    <label for="descricao">Descrição do Tópico</label>
                    <textarea id="descricao" name="descricao" 
                              placeholder="Descreva o conteúdo deste tópico..."><?php echo isset($_POST['descricao']) ? htmlspecialchars($_POST['descricao']) : ''; ?></textarea>
                </div>

                <div class="actions">
                    <a href="ver_topicos.php?baralho=<?php echo $baralho_id; ?>" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-check"></i>
                        Criar Tópico
                    </button>
                </div>
            </form>
        </div>
    </div>
</body>
</html>