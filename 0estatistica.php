<?php

?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Estatística</title>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels"></script>
    <style>
        .grafico-container {
            padding: 1.5rem;
           /* background: var(--hover);*/
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            margin: 1rem;
            position: relative;
            height: 400px;
        }/*body[data-theme="dark"] .grafico-container {
            background: var(--border);
        }*/

        .legenda {
            display: flex;
            justify-content: center;
            gap: 2rem;
            padding: 1rem;
            margin-top: 1rem;
            flex-wrap: wrap;
        }

        .legenda-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            /*background: #f8f9fa;*/
            transition: all 0.3s ease;
        }

        .legenda-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .legenda-cor {
            width: 10px;
            height: 10px;
            border-radius: 50%;
        }

        .legenda-texto {
            font-family: 'Poppins', sans-serif;
            font-size: 0.9rem;
            color: #495057;
        }

        .legenda-vermelho {
            background: rgba(255, 99, 132, 0.8);
        }

        .legenda-amarelo {
            background: rgba(255, 206, 86, 0.8);
        }

        .legenda-azul {
            background: rgba(54, 162, 235, 0.8);
        }
    </style>
</head>
<body>



    <div class="grafico-container">
        <canvas id="chart-options-example"></canvas>
    </div>

    <div class="legenda">
        <div class="legenda-item">
            <div class="legenda-cor legenda-vermelho"></div>
            <span class="legenda-texto">Até 10 dias</span>
        </div>
        <div class="legenda-item">
            <div class="legenda-cor legenda-amarelo"></div>
            <span class="legenda-texto">11 a 20 dias</span>
        </div>
        <div class="legenda-item">
            <div class="legenda-cor legenda-azul"></div>
            <span class="legenda-texto">Mais de 20 dias</span>
        </div>
    </div>


<script>
    // PHP - Recuperar os dados do PostgreSQL
    <?php
    include_once("conexao_POST.php");
    if (isset($_SESSION['idusuario'])) {
        $id_usuario = $_SESSION['idusuario'];
        $query_consultar_estudos = "SELECT e.*, 
       m_estudo.nome AS nome_materia_estudo, 
       m_estudo.cor AS cor_materia_estudo
FROM appEstudo.estudos e
INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
LEFT JOIN appEstudo.materia m_estudo ON e.materia_idmateria = m_estudo.idmateria
WHERE u.idusuario = $id_usuario
ORDER BY EXTRACT(YEAR FROM e.data), EXTRACT(MONTH FROM e.data)";
    }
    // Consulta para obter as datas do banco de dados
    $result = pg_query($conexao, $query_consultar_estudos);

    // Função para obter o nome do mês e o ano em português
    function obterNomeMesEAno($data) {
        $mesesEmPortugues = array(
            'January' => 'Janeiro',
            'February' => 'Fevereiro',
            'March' => 'Março',
            'April' => 'Abril',
            'May' => 'Maio',
            'June' => 'Junho',
            'July' => 'Julho',
            'August' => 'Agosto',
            'September' => 'Setembro',
            'October' => 'Outubro',
            'November' => 'Novembro',
            'December' => 'Dezembro'
        );

        $timestamp = strtotime($data);
        $mes = date("F", $timestamp);
        $ano = date("Y", $timestamp);
        return $mesesEmPortugues[$mes] . '/' . $ano;
    }

    // Array para armazenar as datas já contabilizadas
    $datasContabilizadas = array();

    // Array para armazenar as contagens por mês/ano
    $meses = array();

    // Percorre os resultados da consulta
    while ($row = pg_fetch_assoc($result)) {
        // Obter a data
        $data = $row["data"];

        // Verificar se a data já foi contabilizada
        if (!in_array($data, $datasContabilizadas)) {
            // Adicionar a data às datas contabilizadas
            $datasContabilizadas[] = $data;

            // Obter o mês e ano da data formatado em português
            $mesEAno = obterNomeMesEAno($data);

            // Incrementar a contagem para o mês atual ou definir como 1 se for o primeiro encontro
            if (isset($meses[$mesEAno])) {
                $meses[$mesEAno]++;
            } else {
                $meses[$mesEAno] = 1;
            }
        }
    }

    // Função para definir as cores com base na quantidade de dias por mês
    function definirCoresPorMes($meses) {
        $cores = array();

        foreach ($meses as $mes => $quantidade) {
            if ($quantidade <= 10) {
                $cores[$mes] = 'rgba(255, 99, 132, 0.2)'; // Vermelho
            } elseif ($quantidade > 20) {
                $cores[$mes] = 'rgba(54, 162, 235, 0.2)'; // Azul
            } else {
                $cores[$mes] = 'rgba(255, 206, 86, 0.2)'; // Amarelo
            }
        }

        return $cores;
    }

    // Obter as cores com base na quantidade de dias por mês
    $coresPorMes = definirCoresPorMes($meses);
    // Adiciona a contagem máxima de dias por mês
    $maxDiasPorMes = array();
    foreach ($meses as $mes => $quantidade) {
        if (!isset($maxDiasPorMes[$mes]) || $quantidade > $maxDiasPorMes[$mes]) {
            $maxDiasPorMes[$mes] = $quantidade;
        }
    }
    ?>

    // JavaScript - Dados obtidos do PHP
    const dataFromPHP = <?php echo json_encode(array_values($meses)); ?>;
    const labelsFromPHP = <?php echo json_encode(array_keys($meses)); ?>;
    Chart.register(ChartDataLabels);

    // Função para determinar a cor baseada no valor
    function getBarColor(value) {
        if (value <= 10) return 'rgba(255, 99, 132, 0.8)';
        if (value <= 20) return 'rgba(255, 206, 86, 0.8)';
        return 'rgba(54, 162, 235, 0.8)';
    }

    // Cores para as barras
    const backgroundColors = dataFromPHP.map(value => getBarColor(value));

    // Configuração do gráfico
    const config = {
        type: 'bar',
        data: {
            labels: labelsFromPHP,
            datasets: [
                {
                    label: 'Dias Estudados',
                    data: dataFromPHP,
                    backgroundColor: backgroundColors,
                    borderColor: backgroundColors,
                    borderWidth: 1,
                    borderRadius: 6,
                    barThickness: 20,
                    order: 2, // Para garantir que as barras fiquem atrás da linha
                    datalabels: {
                        display: true // Mostra os labels apenas nas barras
                    }
                },
                {
                    label: 'Linha de Tendência', // Label diferente para o tooltip
                    data: dataFromPHP,
                    type: 'line',
                    fill: false,
                    backgroundColor: 'transparent',
                    borderColor: 'rgba(0, 0, 0, 0.5)',
                    borderWidth: 2,
                    pointRadius: 4,
                    pointBackgroundColor: backgroundColors,
                    tension: 0.4,
                    order: 1, // Para garantir que a linha fique na frente das barras
                    showLine: true, // Força a exibição da linha
                    datalabels: {
                        display: false // Esconde os labels na linha
                    }
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                intersect: false,
                mode: 'index'
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            family: 'Poppins',
                            size: 12
                        },
                        color: 'var(--text)'
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        font: {
                            family: 'Poppins',
                            size: 12
                        },
                        color: 'var(--text)'
                    }
                }
            },
            plugins: {
                legend: {
                    display: false // Mantém a legenda oculta
                },
                tooltip: {
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    titleColor: '#495057',
                    bodyColor: '#495057',
                    titleFont: {
                        family: 'Poppins',
                        size: 13,
                        weight: 600
                    },
                    bodyFont: {
                        family: 'Poppins',
                        size: 12
                    },
                    callbacks: {
                        title: function(tooltipItems) {
                            return tooltipItems[0].label;
                        },
                        label: function(context) {
                            if (context.datasetIndex === 0) { // Mostra apenas para as barras
                                return 'Dias Estudados: ' + context.raw;
                            }
                            return null; // Não mostra label para a linha
                        }
                    },
                    borderColor: 'rgba(0, 0, 0, 0.1)',
                    borderWidth: 1,
                    padding: 12,
                    displayColors: false,
                    cornerRadius: 6
                },
                datalabels: {
                    color: '#495057',
                    anchor: 'end',
                    align: 'top',
                    offset: 6,
                    font: {
                        family: 'Poppins',
                        size: 11,
                        weight: '500'
                    },
                    formatter: (value) => value
                }
            }
        }
    };

    // Criar o gráfico
    new Chart(
        document.getElementById('chart-options-example'),
        config
    );
</script>

</body>
</html>
