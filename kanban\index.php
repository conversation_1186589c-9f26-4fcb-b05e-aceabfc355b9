<?php
session_start();
require_once('../includes/auth.php');
include_once '../conexao_POST.php';
require_once('../cadastros/includes/verify_admin.php');

// Verifica se é o super admin
verificarSuperAdmin($conexao, false);
?>
<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kanban <PERSON>oal</title>
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 CSS e JS -->
    <link href="https://cdn.jsdelivr.net/npm/@sweetalert2/theme-minimal/minimal.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
 
    <!-- TinyMCE em vez de Quill -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/tinymce/6.7.2/tinymce.min.js"></script>
     
    <link rel="stylesheet" href="assets/css/kanban.css">

    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
</head>
<body>
    <div class="kanban-container">
        <header class="kanban-header">
            <h1><i class="fas fa-tasks"></i> Meu Kanban</h1>
            <button class="btn-add" onclick="abrirModalTarefa()">
                <i class="fas fa-plus"></i> Nova Tarefa
            </button>
        </header>

        <div class="kanban-board">
            <div class="kanban-column" data-status="backlog">
                <div class="column-header">
                    <h2><i class="fas fa-list"></i> Backlog</h2>
                    <div class="header-controls">
                        <span class="task-count">0</span>
                        <span class="collapse-icon"><i class="fas fa-minus"></i></span>
                    </div>
                </div>
                <div class="task-list" id="backlog"></div>
            </div>

            <div class="kanban-column" data-status="em_andamento">
                <div class="column-header">
                    <h2><i class="fas fa-spinner"></i> Em Andamento</h2>
                    <div class="header-controls">
                        <span class="task-count">0</span>
                        <span class="collapse-icon"><i class="fas fa-minus"></i></span>
                    </div>
                </div>
                <div class="task-list" id="em_andamento"></div>
            </div>

            <div class="kanban-column" data-status="concluido">
                <div class="column-header">
                    <h2><i class="fas fa-check"></i> Concluído</h2>
                    <div class="header-controls">
                        <span class="task-count">0</span>
                        <span class="collapse-icon"><i class="fas fa-minus"></i></span>
                    </div>
                </div>
                <div class="task-list" id="concluido"></div>
            </div>

            <div class="kanban-column" data-status="arquivado">
                <div class="column-header">
                    <h2><i class="fas fa-archive"></i> Arquivado</h2>
                    <div class="header-controls">
                        <span class="task-count">0</span>
                        <span class="collapse-icon"><i class="fas fa-minus"></i></span>
                    </div>
                </div>
                <div class="task-list" id="arquivado"></div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/Sortable/1.15.2/Sortable.min.js"></script> <!-- Atualizado para 1.15.2 -->
    <script src="assets/js/kanban.js"></script>
</body>
</html>

