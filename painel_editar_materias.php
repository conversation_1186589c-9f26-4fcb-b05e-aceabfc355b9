<?php
session_start();

// Validação da sessão
if (!isset($_SESSION['idusuario']) || !is_numeric($_SESSION['idusuario'])) {
    header('Location: login_index.php');
    exit();
}

// Validação e sanitização dos parâmetros POST
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $id_materia = filter_var($_POST['id_materia'], FILTER_VALIDATE_INT);
    $id_planejamento = filter_var($_POST['id_planejamento'], FILTER_VALIDATE_INT);
    
    if ($id_materia === false || $id_planejamento === false) {
        error_log("Tentativa de acesso com parâmetros inválidos: " . 
                 "ID Matéria: {$_POST['id_materia']}, " . 
                 "ID Planejamento: {$_POST['id_planejamento']}");
        die("Parâmetros inválidos");
    }
} else {
    header('Location: painel_materias.php');
    exit();
}

include_once 'conexao_POST.php';

// Consulta usando prepared statement com validação adicional de usuário
$query = "
    SELECT m.nome, m.cor, a.detalhe 
    FROM appEstudo.materia m 
    LEFT JOIN appEstudo.anotacao a ON m.idmateria = a.materia_idmateria 
        AND a.planejamento_idplanejamento = $1 
        AND a.planejamento_usuario_idusuario = $2
    WHERE m.idmateria = $3
    AND EXISTS (
        SELECT 1 
        FROM appEstudo.planejamento_materia pm
        INNER JOIN appEstudo.planejamento p ON pm.planejamento_idplanejamento = p.idplanejamento
        WHERE pm.materia_idmateria = m.idmateria
        AND pm.planejamento_idplanejamento = $1
        AND p.usuario_idusuario = $2
    )";

// Execução segura da query com parâmetros
try {
    $result = pg_query_params(
        $conexao, 
        $query, 
        array(
            $id_planejamento,
            $_SESSION['idusuario'],
            $id_materia
        )
    );

    if (!$result) {
        throw new Exception(pg_last_error($conexao));
    }

    $materia = pg_fetch_assoc($result);

    if (!$materia) {
        error_log("Tentativa de acesso a matéria não autorizada: ID Matéria: $id_materia");
        die("Matéria não encontrada ou acesso não autorizado");
    }

} catch (Exception $e) {
    error_log("Erro na consulta: " . $e->getMessage());
    die("Erro ao processar a requisição");
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editor - <?= htmlspecialchars($materia['nome']) ?></title>
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&family=Quicksand:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <script src="https://clevert.com.br/lib/ckeditor/ckeditor.js"></script>
    <style>
        :root {
            --parchment: #f8f0e3;
            --vintage-gold: #4a4a4a;
            --burgundy: #00008B;
            --gold-accent: #daa520;
            --shadow-color: rgba(0, 0, 0, 0.2);
            --elegant-border: linear-gradient(45deg, var(--vintage-gold), #ffd700, var(--vintage-gold));
            --primary-color: <?= $materia['cor'] ?>;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Quicksand', sans-serif;
            background-color: var(--parchment);
            color: #2d3748;
            min-height: 100vh;
            position: relative;
        }

        body::before {
            content: "";
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url('lei_seca/img/ConcurseiroOff_v1_2.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            opacity: 0.1;
            z-index: -1;
        }

        .app-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .header {
            background: white;
            border-bottom: 1px solid var(--vintage-gold);
            padding: 1.5rem 2rem;
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 2px 4px var(--shadow-color);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .materia-title {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .materia-title h1 {
            font-family: 'Cinzel', serif;
            font-size: 2rem;
            color: var(--burgundy);
            font-weight: 700;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .materia-badge {
            background: var(--primary-color);
            color: white;
            padding: 0.5rem 1.5rem;
            font-size: 0.875rem;
            box-shadow:
                    0 0 0 1px rgba(255, 255, 255, 0.3),
                    0 2px 4px rgba(0, 0, 0, 0.2);
            font-family: 'Cinzel', serif;
            letter-spacing: 1px;
            text-transform: uppercase;
        }

        .actions {
            display: flex;
            gap: 1rem;
        }

        .btn {
            font-family: 'Cinzel', serif;
            padding: 0.75rem 1.5rem;
            font-size: 0.9rem;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 1px;
            background: transparent;
        }

        .btn-back {
            color: var(--burgundy);
            border-color: var(--burgundy);
        }

        .btn-back:hover {
            background: var(--burgundy);
            color: white;
        }

        .btn-edit {
            color: var(--vintage-gold);
            border-color: var(--vintage-gold);
        }

        .btn-edit:hover {
            background: var(--vintage-gold);
            color: white;
        }

        .btn-save {
            display: none;
            color: #2e7d32;
            border-color: #2e7d32;
        }

        .btn-save:hover {
            background: #2e7d32;
            color: white;
        }

        .btn-cancel {
            display: none;
            color: #c62828;
            border-color: #c62828;
        }

        .btn-cancel:hover {
            background: #c62828;
            color: white;
        }

        .main-content {
            flex: 1;
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }

        .editor-container {
            background: white;
            box-shadow:
                    0 0 0 1px var(--vintage-gold),
                    0 0 0 8px white,
                    0 0 0 9px var(--vintage-gold),
                    0 4px 15px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            position: relative;
        }

        .editor-container::before {
            content: '';
            position: absolute;
            top: 1rem;
            left: 1rem;
            right: 1rem;
            bottom: 1rem;
            border: 1px solid var(--vintage-gold);
            pointer-events: none;
            opacity: 0.5;
        }

        /* Estilização do CKEditor */
        .cke_chrome {
            border: 1px solid var(--vintage-gold) !important;
            box-shadow: none !important;
        }

        .cke_top {
            background: var(--parchment) !important;
            border-bottom: 1px solid var(--vintage-gold) !important;
            padding: 8px !important;
        }

        .cke_bottom {
            background: var(--parchment) !important;
            border-top: 1px solid var(--vintage-gold) !important;
        }

        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                gap: 1rem;
            }

            .materia-title {
                flex-direction: column;
                text-align: center;
            }

            .actions {
                flex-wrap: wrap;
                justify-content: center;
            }

            .materia-title h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
<div class="app-container">
    <header class="header">
        <div class="header-content">
            <div class="materia-title">
                <h1><?= htmlspecialchars($materia['nome']) ?></h1>
                <span class="materia-badge">Matéria</span>
            </div>
            <div class="actions">
                <button onclick="window.location.href='painel_materias.php'" class="btn btn-back">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" width="20" height="20">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                    Voltar
                </button>
                <button onclick="habilitarEdicao()" id="btnEdit" class="btn btn-edit">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" width="20" height="20">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                    </svg>
                    Editar
                </button>
                <button onclick="salvarConteudo()" id="btnSave" class="btn btn-save">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" width="20" height="20">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                    Salvar
                </button>
                <button onclick="cancelarEdicao()" id="btnCancel" class="btn btn-cancel">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" width="20" height="20">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                    Cancelar
                </button>
            </div>
        </div>
    </header>

    <main class="main-content">
        <div class="editor-container">
            <form id="formEditor" action="painel_salvar_conteudo.php" method="POST">
                <input type="hidden" name="id_materia" value="<?= $id_materia ?>">
                <input type="hidden" name="id_planejamento" value="<?= $id_planejamento ?>">
                <textarea id="editor" name="detalhe"><?= htmlspecialchars($materia['detalhe'] ?? '') ?></textarea>
            </form>
        </div>
    </main>
</div>

<script>
    let editor;

    window.onload = function() {
        editor = CKEDITOR.replace('editor', {
            height: '70vh',
            readOnly: true
        });

        editor.on('instanceReady', function() {
            editor.document.on('click', function(event) {
                var target = event.data.getTarget();
                if (target.is('a')) {
                    var url = target.getAttribute('href');
                    window.open(url, '_blank');
                }
            });
        });
    };

    function habilitarEdicao() {
        editor.setReadOnly(false);
        document.getElementById('btnEdit').style.display = 'none';
        document.getElementById('btnSave').style.display = 'flex';
        document.getElementById('btnCancel').style.display = 'flex';
    }

    function cancelarEdicao() {
        editor.setReadOnly(true);
        document.getElementById('btnEdit').style.display = 'flex';
        document.getElementById('btnSave').style.display = 'none';
        document.getElementById('btnCancel').style.display = 'none';
        location.reload();
    }

    function salvarConteudo() {
        document.getElementById('formEditor').submit();
    }
</script>
</body>
</html>
