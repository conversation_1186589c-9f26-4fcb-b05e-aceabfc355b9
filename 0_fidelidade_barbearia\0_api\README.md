# API Sistema de Fidelidade da Barbearia

Esta API REST foi desenvolvida em PHP para fazer a comunicação entre o aplicativo Flutter e o banco de dados MySQL do sistema de fidelidade da barbearia.

## Configuração

### Requisitos
- PHP 7.4 ou superior
- MySQL 5.7 ou superior
- Apache com mod_rewrite habilitado

### Instalação
1. Faça upload dos arquivos para o servidor web
2. Configure as credenciais do banco de dados em `config/database.php`
3. Certifique-se de que o arquivo `.htaccess` está funcionando
4. Teste a API acessando `/api/v1/health`

## Endpoints Disponíveis

### Autenticação
- `GET /api/v1/auth/login?cpf={cpf}&senha={senha}&tipo={tipo}` - Autenticar usuário

### Usuários
- `POST /api/v1/users` - Criar usuário
- `GET /api/v1/users/cpf/{cpf}` - Buscar usuário por CPF
- `GET /api/v1/users/{id}` - Buscar usuário por ID
- `GET /api/v1/users/clients` - Listar todos os clientes
- `GET /api/v1/users/search?nome={nome}&tipo={tipo}` - Buscar usuários por nome
- `PUT /api/v1/users/{id}` - Atualizar usuário
- `GET /api/v1/users/complete/{id}` - Buscar dados completos do cliente (perfil + pontuação + histórico)

### Perfis de Cliente
- `POST /api/v1/users/profile` - Criar perfil
- `GET /api/v1/users/{id}/profile` - Buscar perfil
- `PUT /api/v1/users/{id}/profile` - Atualizar perfil
- `DELETE /api/v1/users/{id}/profile` - Deletar perfil
- `GET /api/v1/users/birthdays?days={dias}` - Aniversários próximos

### Sistema de Fidelidade
- `GET /api/v1/fidelity/score/{cliente_id}` - Buscar pontuação do cliente
- `POST /api/v1/fidelity/service` - Adicionar ponto de atendimento
- `PUT /api/v1/fidelity/remove-point` - Remover ponto
- `PUT /api/v1/fidelity/reset-points` - Zerar pontos de categoria
- `GET /api/v1/fidelity/history?cliente_id={id}&limit={limit}` - Histórico de atendimentos
- `GET /api/v1/fidelity/config` - Configurações do sistema
- `PUT /api/v1/fidelity/config` - Atualizar configurações
- `GET /api/v1/fidelity/stats` - Estatísticas gerais

### Recompensas e Brindes
- `GET /api/v1/rewards/pending/{cliente_id}` - Brindes pendentes do cliente
- `GET /api/v1/rewards/pending` - Todos os brindes pendentes
- `GET /api/v1/rewards/pending/count` - Contar brindes pendentes
- `PUT /api/v1/rewards/deliver` - Marcar brinde como entregue
- `GET /api/v1/rewards/delivered/{cliente_id}` - Histórico de brindes entregues
- `POST /api/v1/rewards/redeem` - Resgatar recompensa especial
- `GET /api/v1/rewards/redeemed/{cliente_id}` - Recompensas resgatadas
- `GET /api/v1/rewards/stats` - Estatísticas de recompensas

### Senha Mestra
- `POST /api/v1/master-password/verify` - Verificar senha mestra
- `POST /api/v1/master-password/update` - Atualizar senha mestra
- `GET /api/v1/master-password/history` - Histórico de alterações

### Relatórios
- `GET /api/v1/reports/clients` - Relatório de clientes
- `GET /api/v1/reports/stats` - Estatísticas da barbearia
- `GET /api/v1/reports/clients/vip-status` - Clientes por status VIP
- `GET /api/v1/reports/services/period?start_date={data}&end_date={data}` - Atendimentos por período
- `GET /api/v1/reports/clients/top?limit={limite}` - Clientes mais frequentes
- `GET /api/v1/reports/birthdays/month` - Aniversariantes do mês
- `GET /api/v1/reports/performance/monthly?year={ano}&month={mes}` - Performance mensal
- `GET /api/v1/reports/export` - Exportar dados

### Procedures e Functions
- `POST /api/v1/procedure/{nome}` - Executar procedure
- `POST /api/v1/function/{nome}` - Executar function

### Saúde da API
- `GET /api/v1/health` - Status da API

## Formato das Respostas

### Sucesso
```json
{
  "success": true,
  "message": "Operação realizada com sucesso",
  "data": {...},
  "timestamp": "2024-01-01 12:00:00"
}
```

### Erro
```json
{
  "success": false,
  "message": "Mensagem de erro",
  "error_code": 500,
  "timestamp": "2024-01-01 12:00:00"
}
```

## Códigos de Status HTTP

- `200` - OK
- `201` - Criado
- `400` - Requisição inválida
- `401` - Não autorizado
- `404` - Não encontrado
- `405` - Método não permitido
- `422` - Dados inválidos
- `500` - Erro interno do servidor

## Segurança

- Todas as consultas usam prepared statements para prevenir SQL injection
- Validação de dados de entrada
- Headers CORS configurados
- Logs de erro para debugging

## Logs

Os logs de erro são salvos no log padrão do PHP. Para debugging, verifique:
- `/var/log/apache2/error.log` (Linux)
- `C:\xampp\apache\logs\error.log` (Windows/XAMPP)
