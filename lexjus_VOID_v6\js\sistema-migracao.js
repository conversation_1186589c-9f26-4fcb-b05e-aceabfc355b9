/**
 * Sistema de Migração de Dados Multi-Leis
 * Interface completa para gerenciar migração com backup e verificação
 */

class SistemaMigracao {
    constructor() {
        this.status = null;
        this.executando = false;
        this.intervalos = {};
        
        this.init();
    }
    
    init() {
        this.criarInterface();
        this.bindEventos();
        this.verificarStatus();
    }
    
    /**
     * Cria a interface do sistema de migração
     */
    criarInterface() {
        // Verificar se já existe
        if (document.getElementById('sistemaMigracao')) return;
        
        const sistema = document.createElement('div');
        sistema.id = 'sistemaMigracao';
        sistema.className = 'sistema-migracao';
        sistema.innerHTML = `
            <div class="migracao-overlay">
                <div class="migracao-container">
                    <div class="migracao-header">
                        <h2><i class="fas fa-database"></i> Sistema de Migração Multi-Leis</h2>
                        <button id="btnFecharMigracao" class="btn-fechar">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="migracao-content">
                        <!-- Status da Migração -->
                        <div class="status-section">
                            <h3><i class="fas fa-info-circle"></i> Status Atual</h3>
                            <div class="status-cards">
                                <div class="status-card" id="cardEstrutura">
                                    <div class="status-icon">
                                        <i class="fas fa-building"></i>
                                    </div>
                                    <div class="status-info">
                                        <h4>Estrutura Multi-Leis</h4>
                                        <p class="status-text">Verificando...</p>
                                    </div>
                                    <div class="status-indicator"></div>
                                </div>
                                
                                <div class="status-card" id="cardDados">
                                    <div class="status-icon">
                                        <i class="fas fa-database"></i>
                                    </div>
                                    <div class="status-info">
                                        <h4>Dados Migrados</h4>
                                        <p class="status-text">Verificando...</p>
                                    </div>
                                    <div class="status-indicator"></div>
                                </div>
                                
                                <div class="status-card" id="cardBackup">
                                    <div class="status-icon">
                                        <i class="fas fa-shield-alt"></i>
                                    </div>
                                    <div class="status-info">
                                        <h4>Backup de Segurança</h4>
                                        <p class="status-text">Verificando...</p>
                                    </div>
                                    <div class="status-indicator"></div>
                                </div>
                                
                                <div class="status-card" id="cardIntegridade">
                                    <div class="status-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="status-info">
                                        <h4>Integridade dos Dados</h4>
                                        <p class="status-text">Verificando...</p>
                                    </div>
                                    <div class="status-indicator"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Detalhes por Tabela -->
                        <div class="detalhes-section" id="detalhesTabelas" style="display: none;">
                            <h3><i class="fas fa-table"></i> Detalhes por Tabela</h3>
                            <div class="tabelas-grid" id="tabelasGrid">
                                <!-- Será preenchido dinamicamente -->
                            </div>
                        </div>
                        
                        <!-- Ações -->
                        <div class="acoes-section">
                            <h3><i class="fas fa-cogs"></i> Ações Disponíveis</h3>
                            <div class="acoes-buttons">
                                <button id="btnCriarBackup" class="btn btn-warning">
                                    <i class="fas fa-shield-alt"></i>
                                    Criar Backup
                                </button>
                                
                                <button id="btnExecutarMigracao" class="btn btn-primary">
                                    <i class="fas fa-play"></i>
                                    Executar Migração
                                </button>
                                
                                <button id="btnVerificarIntegridade" class="btn btn-info">
                                    <i class="fas fa-search"></i>
                                    Verificar Integridade
                                </button>
                                
                                <button id="btnGerarRelatorio" class="btn btn-success">
                                    <i class="fas fa-file-alt"></i>
                                    Gerar Relatório
                                </button>
                            </div>
                        </div>
                        
                        <!-- Log de Execução -->
                        <div class="log-section">
                            <h3><i class="fas fa-terminal"></i> Log de Execução</h3>
                            <div class="log-container" id="logContainer">
                                <div class="log-empty">
                                    <i class="fas fa-info-circle"></i>
                                    <p>Nenhuma ação executada ainda</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Progresso -->
                        <div class="progresso-section" id="progressoSection" style="display: none;">
                            <h3><i class="fas fa-tasks"></i> Progresso da Migração</h3>
                            <div class="progresso-container">
                                <div class="progresso-barra">
                                    <div class="progresso-preenchimento" id="progressoPreenchimento"></div>
                                </div>
                                <div class="progresso-texto" id="progressoTexto">Preparando...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(sistema);
    }
    
    /**
     * Vincula eventos do sistema
     */
    bindEventos() {
        // Fechar sistema
        document.getElementById('btnFecharMigracao').addEventListener('click', () => {
            this.fechar();
        });
        
        // Ações
        document.getElementById('btnCriarBackup').addEventListener('click', () => {
            this.criarBackup();
        });
        
        document.getElementById('btnExecutarMigracao').addEventListener('click', () => {
            this.executarMigracao();
        });
        
        document.getElementById('btnVerificarIntegridade').addEventListener('click', () => {
            this.verificarIntegridade();
        });
        
        document.getElementById('btnGerarRelatorio').addEventListener('click', () => {
            this.gerarRelatorio();
        });
        
        // Fechar com ESC
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isAberto()) {
                this.fechar();
            }
        });
    }
    
    /**
     * Verifica status atual da migração
     */
    async verificarStatus() {
        try {
            const response = await fetch('./api/migracao.php?acao=status');
            const data = await response.json();
            
            if (data.sucesso) {
                this.status = data.status;
                this.atualizarInterface();
            } else {
                this.adicionarLog('Erro ao verificar status: ' + data.erro, 'erro');
            }
        } catch (error) {
            console.error('Erro ao verificar status:', error);
            this.adicionarLog('Erro de conexão ao verificar status', 'erro');
        }
    }
    
    /**
     * Atualiza interface com status atual
     */
    atualizarInterface() {
        if (!this.status) return;
        
        // Atualizar cards de status
        this.atualizarCardStatus('cardEstrutura', this.status.estrutura_criada, 
            this.status.estrutura_criada ? 'Estrutura criada' : 'Estrutura não encontrada');
        
        this.atualizarCardStatus('cardDados', this.status.dados_migrados,
            this.status.dados_migrados ? 'Dados migrados' : 'Dados não migrados');
        
        this.atualizarCardStatus('cardBackup', this.status.backup_existe,
            this.status.backup_existe ? 'Backup disponível' : 'Backup não encontrado');
        
        this.atualizarCardStatus('cardIntegridade', this.status.integridade_ok,
            this.status.integridade_ok ? 'Integridade OK' : 'Problemas encontrados');
        
        // Mostrar detalhes das tabelas se houver dados
        if (this.status.registros_por_tabela && Object.keys(this.status.registros_por_tabela).length > 0) {
            this.mostrarDetalhesTabelas();
        }
        
        // Atualizar botões baseado no status
        this.atualizarBotoes();
    }
    
    /**
     * Atualiza card de status
     */
    atualizarCardStatus(cardId, status, texto) {
        const card = document.getElementById(cardId);
        if (!card) return;
        
        const statusText = card.querySelector('.status-text');
        const indicator = card.querySelector('.status-indicator');
        
        statusText.textContent = texto;
        
        // Remover classes anteriores
        indicator.classList.remove('status-ok', 'status-erro', 'status-aviso');
        
        // Adicionar classe baseada no status
        if (status === true) {
            indicator.classList.add('status-ok');
        } else if (status === false) {
            indicator.classList.add('status-erro');
        } else {
            indicator.classList.add('status-aviso');
        }
    }
    
    /**
     * Mostra detalhes das tabelas
     */
    mostrarDetalhesTabelas() {
        const section = document.getElementById('detalhesTabelas');
        const grid = document.getElementById('tabelasGrid');
        
        if (!this.status.registros_por_tabela) return;
        
        grid.innerHTML = '';
        
        Object.entries(this.status.registros_por_tabela).forEach(([tabela, dados]) => {
            const card = document.createElement('div');
            card.className = 'tabela-card';
            card.innerHTML = `
                <div class="tabela-header">
                    <h4>${tabela.replace('lexjus_', '')}</h4>
                    <span class="tabela-percentual">${dados.percentual}%</span>
                </div>
                <div class="tabela-stats">
                    <div class="stat">
                        <span class="stat-label">Total:</span>
                        <span class="stat-valor">${dados.total}</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Migrados:</span>
                        <span class="stat-valor">${dados.migrados}</span>
                    </div>
                    <div class="stat">
                        <span class="stat-label">Pendentes:</span>
                        <span class="stat-valor">${dados.nao_migrados}</span>
                    </div>
                </div>
                <div class="tabela-progresso">
                    <div class="progresso-barra-mini">
                        <div class="progresso-preenchimento-mini" style="width: ${dados.percentual}%"></div>
                    </div>
                </div>
            `;
            
            grid.appendChild(card);
        });
        
        section.style.display = 'block';
    }
    
    /**
     * Atualiza estado dos botões
     */
    atualizarBotoes() {
        const btnBackup = document.getElementById('btnCriarBackup');
        const btnMigracao = document.getElementById('btnExecutarMigracao');
        const btnIntegridade = document.getElementById('btnVerificarIntegridade');
        const btnRelatorio = document.getElementById('btnGerarRelatorio');
        
        // Backup sempre disponível
        btnBackup.disabled = this.executando;
        
        // Migração só se estrutura existir e dados não migrados
        btnMigracao.disabled = this.executando || !this.status.estrutura_criada || this.status.dados_migrados;
        
        // Integridade só se estrutura existir
        btnIntegridade.disabled = this.executando || !this.status.estrutura_criada;
        
        // Relatório só se estrutura existir
        btnRelatorio.disabled = this.executando || !this.status.estrutura_criada;
        
        // Atualizar textos dos botões
        if (this.status.dados_migrados) {
            btnMigracao.innerHTML = '<i class="fas fa-check"></i> Migração Concluída';
        } else {
            btnMigracao.innerHTML = '<i class="fas fa-play"></i> Executar Migração';
        }
    }
    
    /**
     * Cria backup dos dados
     */
    async criarBackup() {
        this.mostrarProgresso('Criando backup de segurança...');
        this.adicionarLog('Iniciando criação de backup...', 'info');
        
        try {
            const response = await fetch('./api/migracao.php?acao=backup');
            const data = await response.json();
            
            if (data.sucesso) {
                this.adicionarLog(`Backup criado: ${data.backup_file} (${this.formatarTamanho(data.tamanho)})`, 'sucesso');
                this.verificarStatus(); // Atualizar status
            } else {
                this.adicionarLog('Erro ao criar backup: ' + data.erro, 'erro');
            }
        } catch (error) {
            console.error('Erro ao criar backup:', error);
            this.adicionarLog('Erro de conexão ao criar backup', 'erro');
        } finally {
            this.ocultarProgresso();
        }
    }
    
    /**
     * Executa migração completa
     */
    async executarMigracao() {
        if (!confirm('Tem certeza que deseja executar a migração? Esta ação irá modificar a estrutura dos dados.')) {
            return;
        }
        
        this.executando = true;
        this.mostrarProgresso('Executando migração...');
        this.adicionarLog('Iniciando migração para sistema multi-leis...', 'info');
        
        try {
            const response = await fetch('./api/migracao.php?acao=executar', {
                method: 'POST'
            });
            const data = await response.json();
            
            if (data.sucesso) {
                // Adicionar logs da migração
                data.log.forEach(linha => {
                    this.adicionarLog(linha, 'sucesso');
                });
                
                this.adicionarLog('Migração concluída com sucesso!', 'sucesso');
                this.verificarStatus(); // Atualizar status
            } else {
                this.adicionarLog('Erro na migração: ' + data.erro, 'erro');
                
                // Adicionar logs se existirem
                if (data.log) {
                    data.log.forEach(linha => {
                        this.adicionarLog(linha, 'aviso');
                    });
                }
            }
        } catch (error) {
            console.error('Erro ao executar migração:', error);
            this.adicionarLog('Erro de conexão ao executar migração', 'erro');
        } finally {
            this.executando = false;
            this.ocultarProgresso();
            this.atualizarBotoes();
        }
    }
    
    /**
     * Verifica integridade dos dados
     */
    async verificarIntegridade() {
        this.mostrarProgresso('Verificando integridade dos dados...');
        this.adicionarLog('Iniciando verificação de integridade...', 'info');
        
        try {
            const response = await fetch('./api/migracao.php?acao=verificar');
            const data = await response.json();
            
            if (data.sucesso) {
                if (data.integridade_ok) {
                    this.adicionarLog('Verificação concluída: Integridade OK', 'sucesso');
                } else {
                    this.adicionarLog('Problemas de integridade encontrados:', 'aviso');
                    data.problemas.forEach(problema => {
                        this.adicionarLog('- ' + problema, 'erro');
                    });
                }
                
                // Mostrar estatísticas
                this.adicionarLog('Estatísticas por tabela:', 'info');
                Object.entries(data.estatisticas).forEach(([tabela, stats]) => {
                    this.adicionarLog(`${tabela}: ${stats.total} total, ${stats.com_lei_id} com lei_id`, 'info');
                });
                
            } else {
                this.adicionarLog('Erro na verificação: ' + data.erro, 'erro');
            }
        } catch (error) {
            console.error('Erro ao verificar integridade:', error);
            this.adicionarLog('Erro de conexão ao verificar integridade', 'erro');
        } finally {
            this.ocultarProgresso();
        }
    }
    
    /**
     * Gera relatório da migração
     */
    async gerarRelatorio() {
        this.mostrarProgresso('Gerando relatório...');
        this.adicionarLog('Gerando relatório da migração...', 'info');
        
        try {
            const response = await fetch('./api/migracao.php?acao=relatorio');
            const data = await response.json();
            
            if (data.sucesso) {
                this.mostrarRelatorio(data.relatorio);
                this.adicionarLog('Relatório gerado com sucesso', 'sucesso');
            } else {
                this.adicionarLog('Erro ao gerar relatório: ' + data.erro, 'erro');
            }
        } catch (error) {
            console.error('Erro ao gerar relatório:', error);
            this.adicionarLog('Erro de conexão ao gerar relatório', 'erro');
        } finally {
            this.ocultarProgresso();
        }
    }
    
    /**
     * Mostra relatório em modal
     */
    mostrarRelatorio(relatorio) {
        const modal = document.createElement('div');
        modal.className = 'relatorio-modal';
        modal.innerHTML = `
            <div class="relatorio-container">
                <div class="relatorio-header">
                    <h3>Relatório de Migração</h3>
                    <button class="btn-fechar-relatorio">×</button>
                </div>
                <div class="relatorio-content">
                    <div class="relatorio-section">
                        <h4>Leis Disponíveis</h4>
                        <div class="leis-lista">
                            ${relatorio.leis_disponiveis.map(lei => `
                                <div class="lei-item">
                                    <span class="lei-codigo" style="background: ${lei.cor_tema}">${lei.codigo}</span>
                                    <span class="lei-nome">${lei.nome}</span>
                                    <span class="lei-artigos">${lei.total_artigos} artigos</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    
                    <div class="relatorio-section">
                        <h4>Distribuição de Dados</h4>
                        <table class="relatorio-tabela">
                            <thead>
                                <tr>
                                    <th>Lei</th>
                                    <th>Revisões</th>
                                    <th>Favoritos</th>
                                    <th>Listas</th>
                                    <th>Progresso</th>
                                    <th>Anotações</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${relatorio.distribuicao_dados.map(lei => `
                                    <tr>
                                        <td><strong>${lei.codigo}</strong></td>
                                        <td>${lei.revisoes}</td>
                                        <td>${lei.favoritos}</td>
                                        <td>${lei.listas}</td>
                                        <td>${lei.progresso}</td>
                                        <td>${lei.anotacoes}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="relatorio-section">
                        <h4>Resumo</h4>
                        <p><strong>Usuários com preferências:</strong> ${relatorio.usuarios_com_preferencias}</p>
                        <p><strong>Data de geração:</strong> ${relatorio.data_geracao}</p>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        // Fechar modal
        modal.querySelector('.btn-fechar-relatorio').addEventListener('click', () => {
            modal.remove();
        });
        
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.remove();
            }
        });
    }
    
    /**
     * Adiciona linha ao log
     */
    adicionarLog(mensagem, tipo = 'info') {
        const container = document.getElementById('logContainer');
        const empty = container.querySelector('.log-empty');
        
        if (empty) {
            empty.remove();
        }
        
        const linha = document.createElement('div');
        linha.className = `log-linha log-${tipo}`;
        linha.innerHTML = `
            <span class="log-timestamp">${new Date().toLocaleTimeString()}</span>
            <span class="log-mensagem">${mensagem}</span>
        `;
        
        container.appendChild(linha);
        container.scrollTop = container.scrollHeight;
    }
    
    /**
     * Mostra barra de progresso
     */
    mostrarProgresso(texto) {
        const section = document.getElementById('progressoSection');
        const textoElement = document.getElementById('progressoTexto');
        
        textoElement.textContent = texto;
        section.style.display = 'block';
        
        // Animar barra
        this.animarProgresso();
    }
    
    /**
     * Oculta barra de progresso
     */
    ocultarProgresso() {
        const section = document.getElementById('progressoSection');
        section.style.display = 'none';
        
        // Parar animação
        if (this.intervalos.progresso) {
            clearInterval(this.intervalos.progresso);
            delete this.intervalos.progresso;
        }
    }
    
    /**
     * Anima barra de progresso
     */
    animarProgresso() {
        const preenchimento = document.getElementById('progressoPreenchimento');
        let progresso = 0;
        
        this.intervalos.progresso = setInterval(() => {
            progresso = (progresso + Math.random() * 10) % 100;
            preenchimento.style.width = progresso + '%';
        }, 200);
    }
    
    /**
     * Formata tamanho de arquivo
     */
    formatarTamanho(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * Abre o sistema
     */
    abrir() {
        const sistema = document.getElementById('sistemaMigracao');
        if (sistema) {
            sistema.classList.add('ativo');
            document.body.style.overflow = 'hidden';
            this.verificarStatus();
        }
    }
    
    /**
     * Fecha o sistema
     */
    fechar() {
        const sistema = document.getElementById('sistemaMigracao');
        if (sistema) {
            sistema.classList.remove('ativo');
            document.body.style.overflow = '';
        }
    }
    
    /**
     * Verifica se sistema está aberto
     */
    isAberto() {
        const sistema = document.getElementById('sistemaMigracao');
        return sistema && sistema.classList.contains('ativo');
    }
}

// Instância global
let sistemaMigracao;

// Inicializar quando DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    sistemaMigracao = new SistemaMigracao();
    
    // Disponibilizar globalmente
    window.sistemaMigracao = sistemaMigracao;
});

// Exportar para uso em outros módulos
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SistemaMigracao;
}
