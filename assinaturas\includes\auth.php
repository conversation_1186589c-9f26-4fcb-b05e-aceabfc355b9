<?php
// includes/auth.php
session_start();

function login($conexao, $email, $senha) {
    $sql = "SELECT idusuario, nome, email, senha, is_admin 
            FROM appestudo.usuario 
            WHERE email = $1 AND senha = $2";
            
    $result = pg_query_params($conexao, $sql, array($email, $senha));
    
    if (!$result) {
        error_log("Erro na consulta: " . pg_last_error($conexao));
        return false;
    }
    
    $usuario = pg_fetch_assoc($result);
    
    if ($usuario) {
        $_SESSION['user_id'] = $usuario['idusuario'];
        $_SESSION['user_name'] = $usuario['nome'];
        $_SESSION['user_email'] = $usuario['email'];
        $_SESSION['is_admin'] = $usuario['is_admin'] == 't';
        return true;
    }
    
    return false;
}

function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function isAdmin() {
    return isset($_SESSION['is_admin']) && $_SESSION['is_admin'] === true;
}

function logout() {
    session_destroy();
    session_start();
}

function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: login.php');
        exit;
    }
}

function requireAdmin() {
    if (!isAdmin()) {
        header('Location: index.php');
        exit;
    }
}

// Função para debug apenas
function getCurrentUser() {
    if (isLoggedIn()) {
        return [
            'id' => $_SESSION['user_id'],
            'name' => $_SESSION['user_name'],
            'email' => $_SESSION['user_email'],
            'is_admin' => $_SESSION['is_admin']
        ];
    }
    return null;
}