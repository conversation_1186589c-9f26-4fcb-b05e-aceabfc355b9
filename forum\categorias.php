<?php
include_once("../session_config.php");
include_once("../assets/config.php");

if (!verificarSessaoValida()) {
    header("Location: ../login_index.php");
    exit();
}

// Buscar todas as categorias ativas
$query_categorias = "SELECT id, nome, descricao FROM appestudo.forum_categorias WHERE status = true ORDER BY nome";
$result_categorias = pg_query($conexao, $query_categorias);
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Categorias - Fórum PlanejaAqui</title>
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <link rel="stylesheet" href="css/forum.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <header class="main-header">
        <div class="header-container">
            <div class="header-left">
                <a href="index.php" class="logo">
                    <strong>PlanejaAqui</strong> <span>Fórum</span>
                </a>
                <form class="search-container" action="buscar.php" method="get" style="display:flex;">
                    <input type="text" name="termo" placeholder="Buscar no fórum..." class="search-input">
                    <button type="submit" class="search-button"><i class="fas fa-search"></i></button>
                </form>
            </div>
            <div class="header-right">
                <a href="novo_topico.php" class="btn-criar-post">Criar Post</a>
                <div class="notification-wrapper" style="position:relative;display:inline-block;">
                    <a href="#" class="icon-button" id="notificationBell" title="Notificações">
                        <i class="far fa-bell"></i>
                        <span class="notification-badge" id="notificationBadge" style="display:none;">2</span>
                    </a>
                    <div class="notification-dropdown" id="notificationDropdown">
                        <div class="dropdown-header">Notificações</div>
                        <ul class="dropdown-list"></ul>
                        <div class="dropdown-footer"><a href="todas_notificacoes.php">Ver todas</a></div>
                    </div>
                </div>
                <a href="#" class="icon-button" id="toggleDarkMode" title="Alternar modo escuro">
                    <i class="fas fa-moon"></i>
                </a>
            </div>
        </div>
    </header>

    <div class="main-container">
        <div class="content-container">
            <div class="forum-header">
                <div class="breadcrumb">
                    <a href="index.php">Fórum</a> > Categorias
                </div>
                <h1>Categorias do Fórum</h1>
                <p class="category-description">Encontre tópicos por categoria e descubra discussões sobre os mais diversos temas!</p>
            </div>
            <div class="topics-list topics-grid">
                <?php if ($result_categorias && pg_num_rows($result_categorias) > 0): ?>
                    <?php while ($cat = pg_fetch_assoc($result_categorias)): ?>
                        <div class="topic-card" style="align-items: flex-start;">
                            <div class="topic-content">
                                <div class="topic-category" style="font-size: 15px; font-weight: 600; color: var(--primary); margin-bottom: 6px;">
                                    c/<?php echo htmlspecialchars($cat['nome']); ?>
                                </div>
                                <div class="topic-excerpt" style="margin-bottom: 10px; color: var(--text-secondary);">
                                    <?php echo htmlspecialchars($cat['descricao']); ?>
                                </div>
                                <a href="ver_categoria.php?id=<?php echo $cat['id']; ?>" class="btn-criar-post" style="padding: 6px 18px; font-size: 14px;">Ver Categoria</a>
                            </div>
                        </div>
                    <?php endwhile; ?>
                <?php else: ?>
                    <div class="no-topics">
                        <p>Nenhuma categoria encontrada.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        <div class="sidebar">
            <div class="sidebar-card">
                <h2>Sobre as Categorias</h2>
                <p>As categorias organizam os tópicos do fórum por assunto. Clique em uma categoria para ver as discussões relacionadas e participar!</p>
            </div>
            <div class="sidebar-card">
                <h2>Regras do Fórum</h2>
                <ol class="rules-list">
                    <li>Seja respeitoso com os outros usuários</li>
                    <li>Não compartilhe informações pessoais</li>
                    <li>Evite spam e conteúdo promocional</li>
                    <li>Mantenha a discussão relacionada ao tema</li>
                    <li>Respeite as leis de direitos autorais</li>
                    <li>Posts que violarem estas regras serão removidos sem aviso prévio</li>
                </ol>
            </div>
        </div>
    </div>
    <script>
        // Função para controlar o modo escuro
        function toggleDarkMode() {
            const body = document.body;
            const icon = document.querySelector('#toggleDarkMode i');
            
            // Alterna a classe dark-mode
            body.classList.toggle('dark-mode');
            
            // Atualiza o ícone
            if (body.classList.contains('dark-mode')) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
                localStorage.setItem('darkMode', 'enabled');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
                localStorage.setItem('darkMode', 'disabled');
            }
        }

        // Verifica se o modo escuro estava ativado anteriormente
        document.addEventListener('DOMContentLoaded', function() {
            const darkMode = localStorage.getItem('darkMode');
            const icon = document.querySelector('#toggleDarkMode i');
            
            if (darkMode === 'enabled') {
                document.body.classList.add('dark-mode');
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            }

            // Adiciona o evento de clique para o botão de modo escuro
            document.getElementById('toggleDarkMode').addEventListener('click', function(e) {
                e.preventDefault();
                toggleDarkMode();
            });
        });
    </script>
    <script>
        // Notificações - Dropdown
        const bell = document.getElementById('notificationBell');
        const dropdown = document.getElementById('notificationDropdown');
        const badge = document.getElementById('notificationBadge');

        function formatarTempo(data) {
            const d = new Date(data);
            const agora = new Date();
            const diff = (agora - d) / 1000;
            if (diff < 60) return 'agora mesmo';
            if (diff < 3600) return Math.floor(diff/60) + ' min atrás';
            if (diff < 86400) return Math.floor(diff/3600) + 'h atrás';
            return d.toLocaleDateString('pt-BR');
        }

        function atualizarBadgeNotificacoes() {
            fetch('buscar_notificacoes.php')
                .then(res => res.json())
                .then(data => {
                    if (badge) {
                        if (data.notificacoes && data.notificacoes.length > 0) {
                            const naoLidas = data.notificacoes.filter(n => !n.lida).length;
                            if (naoLidas > 0) {
                                badge.style.display = 'flex';
                                badge.textContent = naoLidas;
                            } else {
                                badge.style.display = 'none';
                            }
                        } else {
                            badge.style.display = 'none';
                        }
                    }
                });
        }

        if (bell) {
            bell.addEventListener('click', function(e) {
                e.preventDefault();
                if (dropdown.style.display === 'block') {
                    dropdown.style.display = 'none';
                    return;
                }
                fetch('buscar_notificacoes.php')
                    .then(res => res.json())
                    .then(data => {
                        const list = dropdown.querySelector('.dropdown-list');
                        list.innerHTML = '';
                        if (data.notificacoes && data.notificacoes.length > 0) {
                            let naoLidas = 0;
                            data.notificacoes.forEach(notif => {
                                if (!notif.lida) naoLidas++;
                                let link = notif.topico_id ? `ver_topico.php?id=${notif.topico_id}` : '#';
                                list.innerHTML += `
                                    <li${notif.lida ? '' : ' style=\"background:#f0f6ff;\"'}>
                                        <a href="${link}" style="text-decoration:none;color:inherit;display:block;">
                                            <span class=\"notif-title\">${notif.mensagem}</span><br>
                                            <span class=\"notif-time\">${formatarTempo(notif.data_criada)}</span>
                                        </a>
                                    </li>
                                `;
                            });
                            badge.style.display = naoLidas > 0 ? 'flex' : 'none';
                            badge.textContent = naoLidas;
                        } else {
                            list.innerHTML = '<li><span class="notif-title">Nenhuma notificação</span></li>';
                            badge.style.display = 'none';
                        }
                        dropdown.style.display = 'block';
                        fetch('marcar_notificacoes_lidas.php', { method: 'POST' })
                            .then(() => atualizarBadgeNotificacoes());
                    });
                document.addEventListener('mousedown', function handler(event) {
                    if (!dropdown.contains(event.target) && !bell.contains(event.target)) {
                        dropdown.style.display = 'none';
                        document.removeEventListener('mousedown', handler);
                    }
                });
            });
        }
        document.addEventListener('DOMContentLoaded', atualizarBadgeNotificacoes);
    </script>
</body>
</html> 