<?php
session_start();
include_once 'assets/config.php';
include_once 'includes/calculos.php';
include_once 'includes/plano_estudo_logic.php';

if (!isset($_SESSION['idusuario'])) {
    die("Usuário não logado");
}

$usuario_id = $_SESSION['idusuario'];

echo "<h1>🔍 DEBUG FINAL - Análise Detalhada</h1>";
echo "<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    .error { background-color: #ffebee; border-color: #f44336; }
    .success { background-color: #e8f5e9; border-color: #4caf50; }
    .warning { background-color: #fff3e0; border-color: #ff9800; }
    .info { background-color: #e3f2fd; border-color: #2196f3; }
    table { border-collapse: collapse; width: 100%; margin: 10px 0; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f5f5f5; }
    .nivel1 { background-color: #ffcdd2; }
    .nivel2 { background-color: #c8e6c9; }
    .nivel3 { background-color: #bbdefb; }
    pre { background-color: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
</style>";

try {
    $planoData = new PlanoEstudoLogic($conexao);
    $conteudos_brutos = $planoData->getConteudos();
    
    // Filtrar apenas Economia
    $economia_bruta = array_filter($conteudos_brutos, function($item) {
        return stripos($item['materia_nome'], 'economia') !== false;
    });
    
    echo "<div class='section info'>";
    echo "<h2>1️⃣ CONTEÚDOS BRUTOS DE ECONOMIA</h2>";
    echo "<table>";
    echo "<tr><th>ID</th><th>Matéria ID</th><th>Capítulo</th><th>Descrição</th><th>Nível</th></tr>";

    foreach ($economia_bruta as $item) {
        $nivel = count(explode('.', $item['capitulo']));
        $class = $nivel == 1 ? 'nivel1' : ($nivel == 2 ? 'nivel2' : 'nivel3');
        echo "<tr class='$class'>";
        echo "<td>{$item['id']}</td>";
        echo "<td>{$item['idmateria']}</td>";
        echo "<td><strong>{$item['capitulo']}</strong></td>";
        echo "<td>" . substr($item['descricao'], 0, 60) . "...</td>";
        echo "<td>Nível $nivel</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<p><strong>Total:</strong> " . count($economia_bruta) . "</p>";
    echo "</div>";
    
    // Aplicar filtro
    $conteudos_filtrados = filtrarTodosConteudos($conteudos_brutos);
    $economia_filtrada = array_filter($conteudos_filtrados, function($item) {
        return stripos($item['materia_nome'], 'economia') !== false;
    });
    
    echo "<div class='section success'>";
    echo "<h2>2️⃣ CONTEÚDOS APÓS FILTRO</h2>";
    echo "<table>";
    echo "<tr><th>ID</th><th>Capítulo</th><th>Descrição</th><th>Nível</th></tr>";
    
    foreach ($economia_filtrada as $item) {
        $nivel = count(explode('.', $item['capitulo']));
        $class = $nivel == 1 ? 'nivel1' : ($nivel == 2 ? 'nivel2' : 'nivel3');
        echo "<tr class='$class'>";
        echo "<td>{$item['id']}</td>";
        echo "<td><strong>{$item['capitulo']}</strong></td>";
        echo "<td>" . substr($item['descricao'], 0, 60) . "...</td>";
        echo "<td>Nível $nivel</td>";
        echo "</tr>";
    }
    echo "</table>";
    echo "<p><strong>Total após filtro:</strong> " . count($economia_filtrada) . "</p>";
    echo "</div>";
    
    // Análise da lógica de filtro
    echo "<div class='section warning'>";
    echo "<h2>3️⃣ ANÁLISE DA LÓGICA DE FILTRO</h2>";
    
    // Simular a função analisarEstruturaMaterias
    $niveis = [];
    $total_itens = count($economia_bruta);
    
    foreach ($economia_bruta as $conteudo) {
        $partes = explode('.', $conteudo['capitulo']);
        $nivel = count($partes);
        $niveis[$nivel] = ($niveis[$nivel] ?? 0) + 1;
    }
    
    echo "<p><strong>Total de itens:</strong> $total_itens</p>";
    echo "<p><strong>Níveis encontrados:</strong></p>";
    foreach ($niveis as $nivel => $quantidade) {
        echo "<p>- Nível $nivel: $quantidade itens</p>";
    }
    
    $estrategia = analisarEstruturaMaterias($economia_bruta, 'Economia');
    echo "<p><strong>Estratégia aplicada:</strong> <span style='color: red; font-weight: bold;'>$estrategia</span></p>";
    
    // Mostrar a lógica de pais/filhos
    echo "<h3>🔍 Lógica Pais/Filhos:</h3>";
    $tem_filhos = [];
    
    foreach ($economia_bruta as $item) {
        $capitulo = $item['capitulo'];
        $partes = explode('.', $capitulo);
        
        if (count($partes) > 1) {
            array_pop($partes);
            $pai = implode('.', $partes);
            $chave_pai = 'Economia|' . $pai;
            $tem_filhos[$chave_pai] = true;
        }
    }
    
    echo "<p><strong>Capítulos que têm filhos (deveriam ser removidos):</strong></p>";
    foreach ($economia_bruta as $item) {
        $chave_conteudo = 'Economia|' . $item['capitulo'];
        $tem_filho = isset($tem_filhos[$chave_conteudo]);
        $status = $tem_filho ? "🔴 DEVERIA SER REMOVIDO" : "🟢 DEVERIA APARECER";
        echo "<p>- Capítulo <strong>{$item['capitulo']}</strong>: $status</p>";
    }
    echo "</div>";
    
    // Verificar o que realmente está sendo retornado
    echo "<div class='section error'>";
    echo "<h2>4️⃣ COMPARAÇÃO: ESPERADO vs REAL</h2>";
    
    $esperados = [];
    foreach ($economia_bruta as $item) {
        $chave_conteudo = 'Economia|' . $item['capitulo'];
        if (!isset($tem_filhos[$chave_conteudo])) {
            $esperados[] = $item['capitulo'];
        }
    }
    
    $reais = [];
    foreach ($economia_filtrada as $item) {
        $reais[] = $item['capitulo'];
    }
    
    echo "<p><strong>ESPERADO (sem filhos):</strong> " . implode(', ', $esperados) . "</p>";
    echo "<p><strong>REAL (após filtro):</strong> " . implode(', ', $reais) . "</p>";
    
    $diferenca = array_diff($reais, $esperados);
    if (!empty($diferenca)) {
        echo "<p style='color: red;'><strong>❌ PROBLEMA:</strong> Itens extras: " . implode(', ', $diferenca) . "</p>";
    }
    
    $faltando = array_diff($esperados, $reais);
    if (!empty($faltando)) {
        echo "<p style='color: red;'><strong>❌ PROBLEMA:</strong> Itens faltando: " . implode(', ', $faltando) . "</p>";
    }
    
    if (empty($diferenca) && empty($faltando)) {
        echo "<p style='color: green;'><strong>✅ CORRETO:</strong> Filtro funcionando perfeitamente!</p>";
    }
    
    echo "</div>";
    
    // TESTE COM MATÉRIA ESPECÍFICA
    echo "<div class='section info'>";
    echo "<h2>5️⃣ TESTE COM MATÉRIA ESPECÍFICA</h2>";

    if (isset($_GET['materia'])) {
        $materia_teste = $_GET['materia'];
        echo "<h3>🔍 Testando matéria: $materia_teste</h3>";

        $conteudos_materia = $planoData->getConteudosPorMateria($materia_teste);

        echo "<p><strong>Conteúdos encontrados:</strong> " . count($conteudos_materia) . "</p>";
        echo "<table>";
        echo "<tr><th>ID</th><th>Capítulo</th><th>Descrição</th><th>Nível</th></tr>";

        foreach ($conteudos_materia as $item) {
            $nivel = count(explode('.', $item['capitulo']));
            $class = $nivel == 1 ? 'nivel1' : ($nivel == 2 ? 'nivel2' : 'nivel3');
            echo "<tr class='$class'>";
            echo "<td>{$item['id']}</td>";
            echo "<td><strong>{$item['capitulo']}</strong></td>";
            echo "<td>" . substr($item['descricao'], 0, 80) . "...</td>";
            echo "<td>Nível $nivel</td>";
            echo "</tr>";
        }
        echo "</table>";

        // Aplicar filtro específico
        $filtrados_especifico = filtrarConteudosPorMateria($conteudos_materia, $materia_teste);

        echo "<h3>📋 Após filtro específico:</h3>";
        echo "<p><strong>Conteúdos filtrados:</strong> " . count($filtrados_especifico) . "</p>";
        echo "<table>";
        echo "<tr><th>ID</th><th>Capítulo</th><th>Descrição</th><th>Nível</th></tr>";

        foreach ($filtrados_especifico as $item) {
            $nivel = count(explode('.', $item['capitulo']));
            $class = $nivel == 1 ? 'nivel1' : ($nivel == 2 ? 'nivel2' : 'nivel3');
            echo "<tr class='$class'>";
            echo "<td>{$item['id']}</td>";
            echo "<td><strong>{$item['capitulo']}</strong></td>";
            echo "<td>" . substr($item['descricao'], 0, 80) . "...</td>";
            echo "<td>Nível $nivel</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>Para testar uma matéria específica, adicione <code>?materia=NomeDaMateria</code> na URL</p>";
        echo "<p>Exemplos:</p>";
        echo "<ul>";
        echo "<li><a href='?materia=Economia'>Economia</a></li>";
        echo "<li><a href='?materia=Direito Constitucional'>Direito Constitucional</a></li>";
        echo "<li><a href='?materia=Direito Administrativo'>Direito Administrativo</a></li>";
        echo "</ul>";
    }
    echo "</div>";

    // TESTE COMPLETO DO FLUXO
    echo "<div class='section warning'>";
    echo "<h2>6️⃣ TESTE COMPLETO DO FLUXO (COMO NA INTERFACE)</h2>";

    // Simular exatamente o que faz plano_estudo_inteligente.php
    $planoData_novo = new PlanoEstudoLogic($conexao);
    $conteudos_interface = $planoData_novo->getConteudos();
    $conteudosFiltrados_interface = filtrarTodosConteudos($conteudos_interface);
    $conteudosPorSemana_interface = $planoData_novo->organizarConteudosPorSemanaFiltrados();

    echo "<h3>📊 Dados da Interface:</h3>";
    echo "<p><strong>Total conteúdos brutos:</strong> " . count($conteudos_interface) . "</p>";
    echo "<p><strong>Total conteúdos filtrados:</strong> " . count($conteudosFiltrados_interface) . "</p>";

    // Filtrar apenas Economia dos conteúdos filtrados
    $economia_interface = array_filter($conteudosFiltrados_interface, function($item) {
        return stripos($item['materia_nome'], 'economia') !== false;
    });

    echo "<p><strong>Economia filtrada (interface):</strong> " . count($economia_interface) . "</p>";
    echo "<table>";
    echo "<tr><th>ID</th><th>Capítulo</th><th>Descrição</th></tr>";
    foreach ($economia_interface as $item) {
        echo "<tr>";
        echo "<td>{$item['id']}</td>";
        echo "<td><strong>{$item['capitulo']}</strong></td>";
        echo "<td>" . substr($item['descricao'], 0, 60) . "...</td>";
        echo "</tr>";
    }
    echo "</table>";

    // Verificar o que tem no organizarConteudosPorSemana
    echo "<h3>📅 Conteúdos Por Semana:</h3>";
    $total_semanas = count($conteudosPorSemana_interface);
    echo "<p><strong>Total de semanas:</strong> $total_semanas</p>";

    $economia_por_semana = [];
    foreach ($conteudosPorSemana_interface as $semana_index => $semana) {
        foreach ($semana as $dia_index => $dia) {
            foreach ($dia as $conteudo) {
                if (stripos($conteudo['materia_nome'], 'economia') !== false) {
                    $economia_por_semana[] = $conteudo;
                }
            }
        }
    }

    echo "<p><strong>Economia no cronograma por semana:</strong> " . count($economia_por_semana) . "</p>";
    if (!empty($economia_por_semana)) {
        echo "<table>";
        echo "<tr><th>ID</th><th>Capítulo</th><th>Descrição</th></tr>";
        foreach (array_slice($economia_por_semana, 0, 10) as $item) {
            echo "<tr>";
            echo "<td>{$item['id']}</td>";
            echo "<td><strong>{$item['capitulo']}</strong></td>";
            echo "<td>" . substr($item['descricao'], 0, 60) . "...</td>";
            echo "</tr>";
        }
        echo "</table>";
        if (count($economia_por_semana) > 10) {
            echo "<p><em>... e mais " . (count($economia_por_semana) - 10) . " itens</em></p>";
        }
    }

    echo "</div>";

    // COMPARAÇÃO FINAL
    echo "<div class='section error'>";
    echo "<h2>7️⃣ COMPARAÇÃO FINAL</h2>";

    $capitulos_filtrados = [];
    foreach ($economia_interface as $item) {
        $capitulos_filtrados[] = $item['capitulo'];
    }

    $capitulos_cronograma = [];
    foreach ($economia_por_semana as $item) {
        $capitulos_cronograma[] = $item['capitulo'];
    }

    echo "<p><strong>Capítulos nos conteúdos filtrados:</strong> " . implode(', ', array_unique($capitulos_filtrados)) . "</p>";
    echo "<p><strong>Capítulos no cronograma por semana:</strong> " . implode(', ', array_unique($capitulos_cronograma)) . "</p>";

    if (array_unique($capitulos_filtrados) === array_unique($capitulos_cronograma)) {
        echo "<p style='color: green;'><strong>✅ IGUAIS:</strong> O problema não está no filtro!</p>";
        echo "<p style='color: red;'><strong>🔍 INVESTIGAR:</strong> O problema deve estar na renderização HTML dos cards!</p>";
    } else {
        echo "<p style='color: red;'><strong>❌ DIFERENTES:</strong> O problema está na organização por semana!</p>";
    }

    echo "</div>";

    // DEBUG DOS CAMPOS DE DESCRIÇÃO
    echo "<div class='section info'>";
    echo "<h2>8️⃣ DEBUG DOS CAMPOS DE DESCRIÇÃO</h2>";

    echo "<p>Vamos ver o que tem nos campos descricao_capitulo_* que estão causando o problema na renderização:</p>";

    foreach ($economia_interface as $item) {
        echo "<h4>📋 Capítulo {$item['capitulo']}:</h4>";
        echo "<table>";
        echo "<tr><th>Campo</th><th>Valor</th></tr>";
        echo "<tr><td>capitulo</td><td><strong>{$item['capitulo']}</strong></td></tr>";
        echo "<tr><td>descricao</td><td>" . substr($item['descricao'], 0, 80) . "...</td></tr>";
        echo "<tr><td>descricao_capitulo_principal</td><td>" . ($item['descricao_capitulo_principal'] ?? 'NULL') . "</td></tr>";
        echo "<tr><td>descricao_capitulo_secundario</td><td>" . ($item['descricao_capitulo_secundario'] ?? 'NULL') . "</td></tr>";
        echo "<tr><td>descricao_capitulo_terciario</td><td>" . ($item['descricao_capitulo_terciario'] ?? 'NULL') . "</td></tr>";
        echo "</table>";
        echo "<br>";
    }

    echo "</div>";

    // DEBUG PARA ENTENDER A HIERARQUIA DESEJADA
    echo "<div class='section warning'>";
    echo "<h2>9️⃣ COMO DEVERIA APARECER A HIERARQUIA</h2>";

    echo "<p>Para mostrar pai + filho, precisamos dos dados dos capítulos pais também:</p>";

    // Buscar TODOS os dados de Economia (incluindo pais)
    $economia_completa = array_filter($conteudos_brutos, function($item) {
        return stripos($item['materia_nome'], 'economia') !== false;
    });

    // Organizar por hierarquia
    $por_nivel = [];
    foreach ($economia_completa as $item) {
        $nivel = count(explode('.', $item['capitulo']));
        $por_nivel[$nivel][] = $item;
    }

    echo "<h3>📋 Estrutura Hierárquica Completa:</h3>";

    // Mostrar como deveria aparecer
    foreach ($por_nivel[1] as $pai) {
        echo "<div style='margin: 10px 0; padding: 10px; border-left: 3px solid #007bff;'>";
        echo "<strong>📁 {$pai['capitulo']}. " . substr($pai['descricao'], 0, 50) . "...</strong><br>";

        // Buscar filhos deste pai
        $filhos = array_filter($economia_completa, function($item) use ($pai) {
            return strpos($item['capitulo'], $pai['capitulo'] . '.') === 0 &&
                   $item['capitulo'] !== $pai['capitulo'];
        });

        foreach ($filhos as $filho) {
            $nivel_filho = count(explode('.', $filho['capitulo']));
            $indent = str_repeat('&nbsp;&nbsp;&nbsp;&nbsp;', $nivel_filho - 1);
            echo "$indent└── 📄 <strong>{$filho['capitulo']}</strong>. " . substr($filho['descricao'], 0, 60) . "...<br>";
        }
        echo "</div>";
    }

    echo "<h3>🎯 O que você quer que apareça nos cards?</h3>";
    echo "<p>Opção 1: <strong>Apenas os filhos</strong> (1.1, 2.1, 3.1, 4.1, 5.1.1) - como está agora</p>";
    echo "<p>Opção 2: <strong>Pai + Filho juntos</strong> no mesmo card:</p>";
    echo "<ul>";
    echo "<li>Card 1: '1. Introdução geral... → 1.1. Escassez e escolha...'</li>";
    echo "<li>Card 2: '2. Microeconomia → 2.1. Lei da oferta e demanda...'</li>";
    echo "<li>Card 3: '3. Macroeconomia → 3.1. As contas nacionais...'</li>";
    echo "</ul>";
    echo "<p>Opção 3: <strong>Cards separados</strong> mas mostrando a hierarquia visual</p>";

    echo "</div>";

    // DEBUG DA BUSCA DOS PAIS
    echo "<div class='section error'>";
    echo "<h2>🔟 DEBUG DA BUSCA DOS PAIS</h2>";

    echo "<p>Vamos ver o que está acontecendo quando buscamos os capítulos pais:</p>";

    foreach ($economia_interface as $item) {
        echo "<h4>🔍 Debugando capítulo {$item['capitulo']}:</h4>";

        $partes_capitulo = explode('.', $item['capitulo']);
        if (count($partes_capitulo) > 1) {
            $capitulo_pai = $partes_capitulo[0];

            echo "<p><strong>Capítulo filho:</strong> {$item['capitulo']}</p>";
            echo "<p><strong>Capítulo pai esperado:</strong> {$capitulo_pai}</p>";
            echo "<p><strong>materia_id usado:</strong> {$item['idmateria']}</p>";

            // Fazer a mesma query que está sendo usada na interface
            $query_pai = "SELECT capitulo, descricao FROM appestudo.conteudo_edital
                         WHERE materia_id = $1 AND capitulo = $2
                         ORDER BY ordem LIMIT 1";
            $result_pai = pg_query_params($conexao, $query_pai,
                array($item['idmateria'], $capitulo_pai));

            if ($result_pai && pg_num_rows($result_pai) > 0) {
                $pai_data = pg_fetch_assoc($result_pai);
                echo "<p><strong>Pai encontrado:</strong> {$pai_data['capitulo']} - " . substr($pai_data['descricao'], 0, 80) . "...</p>";
            } else {
                echo "<p style='color: red;'><strong>❌ PAI NÃO ENCONTRADO!</strong></p>";

                // Vamos ver o que tem na materia_id
                $query_debug = "SELECT capitulo, descricao FROM appestudo.conteudo_edital
                               WHERE materia_id = $1
                               ORDER BY capitulo";
                $result_debug = pg_query_params($conexao, $query_debug, array($item['idmateria']));

                echo "<p><strong>Todos os capítulos desta matéria:</strong></p>";
                echo "<ul>";
                while ($row = pg_fetch_assoc($result_debug)) {
                    $highlight = ($row['capitulo'] == $capitulo_pai) ? " style='background-color: yellow;'" : "";
                    echo "<li$highlight>{$row['capitulo']} - " . substr($row['descricao'], 0, 60) . "...</li>";
                }
                echo "</ul>";
            }
        } else {
            echo "<p>Capítulo principal (sem pai): {$item['capitulo']}</p>";
        }
        echo "<hr>";
    }

    echo "</div>";

    // TESTE PARA TODAS AS MATÉRIAS
    echo "<div class='section warning'>";
    echo "<h2>1️⃣1️⃣ TESTE PARA TODAS AS MATÉRIAS</h2>";

    echo "<p>Vamos testar se o sistema funciona para todas as matérias:</p>";

    // Buscar todas as matérias disponíveis
    $query_materias = "SELECT DISTINCT m.idmateria, m.nome as materia_nome
                       FROM appestudo.materia m
                       JOIN appestudo.conteudo_edital ce ON m.idmateria = ce.materia_id
                       WHERE ce.edital_id = (SELECT edital_id FROM appestudo.usuario_edital WHERE usuario_id = $1 LIMIT 1)
                       ORDER BY m.nome";
    $result_materias = pg_query_params($conexao, $query_materias, array($usuario_id));

    if ($result_materias) {
        echo "<h3>📚 Matérias encontradas:</h3>";
        echo "<ul>";
        while ($materia = pg_fetch_assoc($result_materias)) {
            echo "<li><a href='?materia=" . urlencode($materia['materia_nome']) . "'>{$materia['materia_nome']}</a> (ID: {$materia['idmateria']})</li>";
        }
        echo "</ul>";

        if (isset($_GET['materia'])) {
            $materia_teste = $_GET['materia'];
            echo "<h3>🔍 Testando matéria: $materia_teste</h3>";

            // Buscar conteúdos desta matéria
            $conteudos_materia_teste = array_filter($conteudos_brutos, function($item) use ($materia_teste) {
                return stripos($item['materia_nome'], $materia_teste) !== false;
            });

            if (!empty($conteudos_materia_teste)) {
                echo "<p><strong>Total de conteúdos:</strong> " . count($conteudos_materia_teste) . "</p>";

                // Aplicar filtro
                $filtrados_materia_teste = filtrarTodosConteudos($conteudos_materia_teste);
                echo "<p><strong>Após filtro:</strong> " . count($filtrados_materia_teste) . "</p>";

                // Mostrar estrutura hierárquica
                echo "<h4>📋 Estrutura Hierárquica:</h4>";

                // Organizar por níveis
                $por_nivel = [];
                foreach ($conteudos_materia_teste as $item) {
                    $nivel = count(explode('.', $item['capitulo']));
                    $por_nivel[$nivel][] = $item;
                }

                // Mostrar cada nível
                foreach ($por_nivel as $nivel => $itens) {
                    echo "<p><strong>Nível $nivel:</strong> " . count($itens) . " itens</p>";
                    echo "<ul>";
                    foreach (array_slice($itens, 0, 5) as $item) {
                        $highlight = in_array($item, $filtrados_materia_teste) ? " style='background-color: #90EE90;'" : " style='background-color: #FFB6C1;'";
                        echo "<li$highlight>{$item['capitulo']} - " . substr($item['descricao'], 0, 80) . "...</li>";
                    }
                    if (count($itens) > 5) {
                        echo "<li><em>... e mais " . (count($itens) - 5) . " itens</em></li>";
                    }
                    echo "</ul>";
                }

                echo "<p><small>🟢 Verde = Aparece após filtro | 🔴 Rosa = Removido pelo filtro</small></p>";

            } else {
                echo "<p style='color: red;'>❌ Nenhum conteúdo encontrado para esta matéria!</p>";
            }
        }
    }

    echo "</div>";

    // TESTE DA NOVA FUNCIONALIDADE
    echo "<div class='section success'>";
    echo "<h2>1️⃣2️⃣ TESTE: REMOÇÃO DE PAIS DUPLICADOS</h2>";

    echo "<p>Testando a nova funcionalidade que remove pais que vão aparecer junto com filhos:</p>";

    // Aplicar apenas o filtro tradicional
    $apenas_filtrados = filtrarTodosConteudos($conteudos_brutos);
    $economia_apenas_filtrada = array_filter($apenas_filtrados, function($item) {
        return stripos($item['materia_nome'], 'economia') !== false;
    });

    // Aplicar também a remoção de pais duplicados
    $sem_pais_duplicados = removerPaisComFilhos($economia_apenas_filtrada);

    echo "<h3>📊 Comparação:</h3>";
    echo "<div style='display: flex; gap: 20px;'>";

    echo "<div style='flex: 1;'>";
    echo "<h4>🔵 APENAS FILTRO TRADICIONAL:</h4>";
    echo "<p><strong>Total:</strong> " . count($economia_apenas_filtrada) . "</p>";
    foreach ($economia_apenas_filtrada as $item) {
        echo "<p>• {$item['capitulo']} - " . substr($item['descricao'], 0, 40) . "...</p>";
    }
    echo "</div>";

    echo "<div style='flex: 1;'>";
    echo "<h4>🟢 APÓS REMOVER PAIS DUPLICADOS:</h4>";
    echo "<p><strong>Total:</strong> " . count($sem_pais_duplicados) . "</p>";
    foreach ($sem_pais_duplicados as $item) {
        echo "<p>• {$item['capitulo']} - " . substr($item['descricao'], 0, 40) . "...</p>";
    }
    echo "</div>";

    echo "</div>";

    $removidos = array_diff(
        array_column($economia_apenas_filtrada, 'capitulo'),
        array_column($sem_pais_duplicados, 'capitulo')
    );

    if (!empty($removidos)) {
        echo "<h4>🗑️ Capítulos removidos (pais que têm filhos):</h4>";
        echo "<p style='color: red;'><strong>" . implode(', ', $removidos) . "</strong></p>";
        echo "<p><small>Estes capítulos foram removidos porque vão aparecer junto com seus filhos nos cards.</small></p>";
    } else {
        echo "<p style='color: green;'><strong>✅ Nenhum pai foi removido - todos os capítulos são folhas!</strong></p>";
    }

    echo "</div>";

    // DEBUG DO BALANCEAMENTO INTELIGENTE
    echo "<div class='section error'>";
    echo "<h2>1️⃣3️⃣ ANÁLISE DO BALANCEAMENTO INTELIGENTE</h2>";

    echo "<p>Vamos analisar se o sistema está realmente fazendo balanceamento inteligente:</p>";

    // Buscar prova ativa do usuário
    $query_prova_ativa = "SELECT id FROM appestudo.provas
                          WHERE usuario_id = $1 AND status = true
                          ORDER BY created_at DESC LIMIT 1";
    $result_prova = pg_query_params($conexao, $query_prova_ativa, array($usuario_id));

    if (!$result_prova || pg_num_rows($result_prova) == 0) {
        echo "<p style='color: red;'>❌ Nenhuma prova ativa encontrada! Configure uma prova primeiro.</p>";
        echo "</div>";
        return;
    }

    $prova_ativa = pg_fetch_assoc($result_prova);
    $prova_id = $prova_ativa['id'];

    // Buscar informações de peso e configurações da prova ativa
    $query_pesos = "SELECT
        m.nome as materia_nome,
        m.idmateria,
        COALESCE(pm.peso, 1) as peso,
        COALESCE(pm.nivel_dificuldade, 3) as dificuldade,
        COUNT(ce.id_conteudo) as total_conteudos
    FROM appestudo.materia m
    LEFT JOIN appestudo.pesos_materias pm ON m.idmateria = pm.materia_id AND pm.prova_id = $2
    LEFT JOIN appestudo.conteudo_edital ce ON m.idmateria = ce.materia_id
    WHERE ce.edital_id = (SELECT edital_id FROM appestudo.usuario_edital WHERE usuario_id = $1 LIMIT 1)
    GROUP BY m.idmateria, m.nome, pm.peso, pm.nivel_dificuldade
    ORDER BY m.nome";

    $result_pesos = pg_query_params($conexao, $query_pesos, array($usuario_id, $prova_id));

    if ($result_pesos) {
        echo "<h3>🎯 Prova Ativa:</h3>";
        echo "<p><strong>ID da Prova:</strong> {$prova_id}</p>";

        echo "<h3>⚖️ Pesos e Prioridades das Matérias (Configuração do Usuário):</h3>";
        echo "<table>";
        echo "<tr><th>Matéria</th><th>Peso</th><th>Dificuldade</th><th>Total Conteúdos</th><th>Prioridade Calculada</th></tr>";

        $materias_info = [];
        while ($row = pg_fetch_assoc($result_pesos)) {
            $prioridade = $row['peso'] * $row['dificuldade'];
            $materias_info[] = $row;

            echo "<tr>";
            echo "<td>{$row['materia_nome']}</td>";
            echo "<td>{$row['peso']}</td>";
            echo "<td>{$row['dificuldade']}</td>";
            echo "<td>{$row['total_conteudos']}</td>";
            echo "<td><strong>{$prioridade}</strong></td>";
            echo "</tr>";
        }
        echo "</table>";

        // Buscar configurações de dias e horas
        $query_config = "SELECT
            data_prova
        FROM appestudo.provas
        WHERE usuario_id = $1
        ORDER BY id DESC LIMIT 1";

        $result_config = pg_query_params($conexao, $query_config, array($usuario_id));

        if ($result_config && pg_num_rows($result_config) > 0) {
            $config = pg_fetch_assoc($result_config);

            echo "<h3>📅 Configurações de Estudo:</h3>";
            echo "<table>";
            echo "<tr><th>Configuração</th><th>Valor</th></tr>";
            echo "<tr><td>Data prova</td><td>{$config['data_prova']}</td></tr>";
            echo "</table>";

            // Calcular tempo disponível
            $data_inicio = new DateTime(); // Hoje
            $data_prova = new DateTime($config['data_prova']);
            $dias_totais = $data_inicio->diff($data_prova)->days;

            // Assumir 6 dias por semana e 4 horas por dia (valores padrão)
            $dias_por_semana = 6;
            $horas_por_dia = 4;
            $semanas_disponiveis = floor($dias_totais / 7);
            $dias_estudo_totais = $semanas_disponiveis * $dias_por_semana;
            $horas_totais = $dias_estudo_totais * $horas_por_dia;

            echo "<h3>⏰ Análise de Tempo:</h3>";
            echo "<table>";
            echo "<tr><th>Métrica</th><th>Valor</th></tr>";
            echo "<tr><td>Dias até a prova</td><td>{$dias_totais}</td></tr>";
            echo "<tr><td>Dias de estudo por semana</td><td>{$dias_por_semana}</td></tr>";
            echo "<tr><td>Semanas disponíveis</td><td>{$semanas_disponiveis}</td></tr>";
            echo "<tr><td>Total de dias de estudo</td><td>{$dias_estudo_totais}</td></tr>";
            echo "<tr><td>Total de horas disponíveis</td><td><strong>{$horas_totais}h</strong></td></tr>";
            echo "</table>";

            // Análise do balanceamento atual
            echo "<h3>🧠 Análise do Balanceamento Atual:</h3>";

            // Aplicar filtros para ter os dados corretos
            $conteudos_para_analise = filtrarTodosConteudos($conteudos_brutos);
            $conteudos_para_analise = removerPaisComFilhos($conteudos_para_analise);

            $total_conteudos_filtrados = count($conteudos_para_analise);
            $horas_por_conteudo = $horas_totais / $total_conteudos_filtrados;

            echo "<p><strong>Total de conteúdos filtrados:</strong> {$total_conteudos_filtrados}</p>";
            echo "<p><strong>Horas por conteúdo (média):</strong> " . number_format($horas_por_conteudo, 2) . "h</p>";

            // Análise dos pesos por matéria
            echo "<h4>📊 ANÁLISE DOS PESOS POR MATÉRIA:</h4>";

            // Agrupar por matéria e somar pesos
            $materias_agrupadas = [];
            foreach ($materias_info as $materia) {
                $nome = $materia['materia_nome'];
                if (!isset($materias_agrupadas[$nome])) {
                    $materias_agrupadas[$nome] = [
                        'total_conteudos' => 0,
                        'peso_maximo' => 0,
                        'prioridade_maxima' => 0,
                        'pesos_diferentes' => []
                    ];
                }

                $materias_agrupadas[$nome]['total_conteudos'] += $materia['total_conteudos'];
                $materias_agrupadas[$nome]['peso_maximo'] = max($materias_agrupadas[$nome]['peso_maximo'], $materia['peso']);
                $prioridade = $materia['peso'] * $materia['dificuldade'];
                $materias_agrupadas[$nome]['prioridade_maxima'] = max($materias_agrupadas[$nome]['prioridade_maxima'], $prioridade);
                $materias_agrupadas[$nome]['pesos_diferentes'][] = $materia['peso'];
            }

            // Ordenar por prioridade máxima
            uasort($materias_agrupadas, function($a, $b) {
                return $b['prioridade_maxima'] - $a['prioridade_maxima'];
            });

            echo "<table>";
            echo "<tr><th>Matéria</th><th>Total Conteúdos</th><th>Peso Máximo</th><th>Prioridade Máxima</th><th>Pesos Diferentes</th></tr>";

            foreach ($materias_agrupadas as $nome => $info) {
                $pesos_unicos = array_unique($info['pesos_diferentes']);
                $pesos_str = implode(', ', $pesos_unicos);
                $cor_prioridade = $info['prioridade_maxima'] >= 20 ? 'red' : ($info['prioridade_maxima'] >= 10 ? 'orange' : 'green');

                echo "<tr>";
                echo "<td><strong>$nome</strong></td>";
                echo "<td>{$info['total_conteudos']}</td>";
                echo "<td>{$info['peso_maximo']}</td>";
                echo "<td style='color: $cor_prioridade;'><strong>{$info['prioridade_maxima']}</strong></td>";
                echo "<td>$pesos_str</td>";
                echo "</tr>";
            }
            echo "</table>";

            // Verificar se está considerando pesos
            echo "<h4>❌ PROBLEMAS IDENTIFICADOS:</h4>";
            echo "<ul>";
            echo "<li>🔴 <strong>Não considera pesos:</strong> Todas as matérias recebem o mesmo tempo</li>";
            echo "<li>🔴 <strong>Não considera dificuldade:</strong> Matérias difíceis deveriam ter mais tempo</li>";
            echo "<li>🔴 <strong>Distribuição sequencial:</strong> Não há intercalação inteligente</li>";
            echo "<li>🔴 <strong>Sem balanceamento diário:</strong> Pode sobrecarregar alguns dias</li>";
            echo "</ul>";

            echo "<h4>✅ SOLUÇÕES NECESSÁRIAS:</h4>";
            echo "<ul>";
            echo "<li>🟢 <strong>Calcular tempo por matéria</strong> baseado no peso e dificuldade</li>";
            echo "<li>🟢 <strong>Distribuir conteúdos</strong> proporcionalmente ao longo das semanas</li>";
            echo "<li>🟢 <strong>Intercalar matérias</strong> para evitar monotonia</li>";
            echo "<li>🟢 <strong>Balancear carga diária</strong> respeitando as horas disponíveis</li>";
            echo "</ul>";
        }
    }

    echo "</div>";

    // TESTE DO BALANCEAMENTO INTELIGENTE
    echo "<div class='section success'>";
    echo "<h2>1️⃣4️⃣ TESTE DO BALANCEAMENTO INTELIGENTE</h2>";

    echo "<p>Testando o novo algoritmo de balanceamento baseado em pesos:</p>";

    // Aplicar filtros e balanceamento
    $conteudos_filtrados_debug = filtrarTodosConteudos($conteudos_brutos);
    $conteudos_filtrados_debug = removerPaisComFilhos($conteudos_filtrados_debug);
    $conteudos_balanceados = balancearConteudosInteligente($conteudos_filtrados_debug, $conexao, $usuario_id);

    echo "<h3>📊 COMPARAÇÃO: ANTES vs DEPOIS DO BALANCEAMENTO</h3>";

    echo "<div style='display: flex; gap: 20px;'>";

    // ANTES (primeiros 10)
    echo "<div style='flex: 1;'>";
    echo "<h4>🔴 ANTES (Sequencial):</h4>";
    foreach (array_slice($conteudos_filtrados_debug, 0, 10) as $i => $item) {
        $pos = $i + 1;
        echo "<p><strong>$pos.</strong> {$item['materia_nome']} - {$item['capitulo']}</p>";
    }
    echo "</div>";

    // DEPOIS (primeiros 10)
    echo "<div style='flex: 1;'>";
    echo "<h4>🟢 DEPOIS (Balanceado por Prioridade):</h4>";
    foreach (array_slice($conteudos_balanceados, 0, 10) as $i => $item) {
        $pos = $i + 1;
        $prioridade = isset($item['prioridade_materia']) ? $item['prioridade_materia'] : 'N/A';
        $cor = $prioridade >= 20 ? 'red' : ($prioridade >= 10 ? 'orange' : 'green');
        echo "<p><strong>$pos.</strong> <span style='color: $cor;'>[P:$prioridade]</span> {$item['materia_nome']} - {$item['capitulo']}</p>";
    }
    echo "</div>";

    echo "</div>";

    // Análise da distribuição
    echo "<h3>📈 ANÁLISE DA DISTRIBUIÇÃO:</h3>";

    $distribuicao_antes = [];
    $distribuicao_depois = [];

    // Contar primeiros 20 itens de cada
    foreach (array_slice($conteudos_filtrados_debug, 0, 20) as $item) {
        $materia = $item['materia_nome'];
        $distribuicao_antes[$materia] = ($distribuicao_antes[$materia] ?? 0) + 1;
    }

    foreach (array_slice($conteudos_balanceados, 0, 20) as $item) {
        $materia = $item['materia_nome'];
        $distribuicao_depois[$materia] = ($distribuicao_depois[$materia] ?? 0) + 1;
    }

    echo "<table>";
    echo "<tr><th>Matéria</th><th>Antes (primeiros 20)</th><th>Depois (primeiros 20)</th><th>Diferença</th></tr>";

    $todas_materias = array_unique(array_merge(array_keys($distribuicao_antes), array_keys($distribuicao_depois)));

    foreach ($todas_materias as $materia) {
        $antes = $distribuicao_antes[$materia] ?? 0;
        $depois = $distribuicao_depois[$materia] ?? 0;
        $diferenca = $depois - $antes;
        $cor_diferenca = $diferenca > 0 ? 'green' : ($diferenca < 0 ? 'red' : 'gray');

        echo "<tr>";
        echo "<td><strong>$materia</strong></td>";
        echo "<td>$antes</td>";
        echo "<td>$depois</td>";
        echo "<td style='color: $cor_diferenca;'>" . ($diferenca > 0 ? '+' : '') . "$diferenca</td>";
        echo "</tr>";
    }
    echo "</table>";

    echo "<p><small>🟢 Verde = Matéria ganhou prioridade | 🔴 Vermelho = Matéria perdeu prioridade</small></p>";

    echo "</div>";

    // TESTE DA INTERCALAÇÃO INTELIGENTE
    echo "<div class='section info'>";
    echo "<h2>1️⃣5️⃣ TESTE DA INTERCALAÇÃO INTELIGENTE</h2>";

    echo "<p>Testando se o sistema está intercalando matérias para evitar monotonia:</p>";

    // Aplicar apenas balanceamento (sem intercalação)
    $conteudos_so_balanceados = [];
    foreach ($conteudos_balanceados as $conteudo) {
        // Remover a intercalação para comparar
        $conteudos_so_balanceados[] = $conteudo;
    }

    // Ordenar apenas por prioridade (sem intercalação)
    usort($conteudos_so_balanceados, function($a, $b) {
        if ($a['prioridade_materia'] == $b['prioridade_materia']) {
            return strcmp($a['materia_nome'], $b['materia_nome']);
        }
        return $b['prioridade_materia'] - $a['prioridade_materia'];
    });

    echo "<h3>📊 COMPARAÇÃO: SEM vs COM INTERCALAÇÃO</h3>";

    echo "<div style='display: flex; gap: 20px;'>";

    // SEM INTERCALAÇÃO (primeiros 15)
    echo "<div style='flex: 1;'>";
    echo "<h4>🔴 SEM INTERCALAÇÃO:</h4>";
    foreach (array_slice($conteudos_so_balanceados, 0, 15) as $i => $item) {
        $pos = $i + 1;
        $prioridade = $item['prioridade_materia'];
        $cor = $prioridade >= 20 ? 'red' : ($prioridade >= 10 ? 'orange' : 'green');
        echo "<p><strong>$pos.</strong> <span style='color: $cor;'>[P:$prioridade]</span> {$item['materia_nome']}</p>";
    }
    echo "</div>";

    // COM INTERCALAÇÃO (primeiros 15)
    echo "<div style='flex: 1;'>";
    echo "<h4>🟢 COM INTERCALAÇÃO:</h4>";
    foreach (array_slice($conteudos_balanceados, 0, 15) as $i => $item) {
        $pos = $i + 1;
        $prioridade = $item['prioridade_materia'];
        $cor = $prioridade >= 20 ? 'red' : ($prioridade >= 10 ? 'orange' : 'green');
        echo "<p><strong>$pos.</strong> <span style='color: $cor;'>[P:$prioridade]</span> {$item['materia_nome']}</p>";
    }
    echo "</div>";

    echo "</div>";

    // Análise de consecutivos
    echo "<h3>📈 ANÁLISE DE CONSECUTIVOS:</h3>";

    // Função para contar consecutivos
    $analisarConsecutivos = function($lista) {
        $consecutivos = [];
        $atual_materia = null;
        $contador = 0;
        $max_consecutivos = 0;

        foreach ($lista as $item) {
            if ($item['materia_nome'] === $atual_materia) {
                $contador++;
            } else {
                if ($atual_materia !== null) {
                    $consecutivos[] = ['materia' => $atual_materia, 'count' => $contador];
                    $max_consecutivos = max($max_consecutivos, $contador);
                }
                $atual_materia = $item['materia_nome'];
                $contador = 1;
            }
        }

        if ($atual_materia !== null) {
            $consecutivos[] = ['materia' => $atual_materia, 'count' => $contador];
            $max_consecutivos = max($max_consecutivos, $contador);
        }

        return ['sequencias' => $consecutivos, 'max' => $max_consecutivos];
    };

    $analise_sem = $analisarConsecutivos(array_slice($conteudos_so_balanceados, 0, 20));
    $analise_com = $analisarConsecutivos(array_slice($conteudos_balanceados, 0, 20));

    echo "<table>";
    echo "<tr><th>Métrica</th><th>Sem Intercalação</th><th>Com Intercalação</th><th>Melhoria</th></tr>";
    echo "<tr>";
    echo "<td>Máximo consecutivos</td>";
    echo "<td>{$analise_sem['max']}</td>";
    echo "<td>{$analise_com['max']}</td>";
    $melhoria = $analise_sem['max'] - $analise_com['max'];
    $cor_melhoria = $melhoria > 0 ? 'green' : 'red';
    echo "<td style='color: $cor_melhoria;'>" . ($melhoria > 0 ? '-' : '+') . abs($melhoria) . "</td>";
    echo "</tr>";
    echo "</table>";

    echo "<p><small>🎯 <strong>Objetivo:</strong> Reduzir o máximo de consecutivos para evitar monotonia</small></p>";

    echo "</div>";

} catch (Exception $e) {
    echo "<div class='section error'>";
    echo "<h2>❌ ERRO</h2>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
