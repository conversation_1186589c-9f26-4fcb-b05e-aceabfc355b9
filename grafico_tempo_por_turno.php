<?php
include 'conexao_POST.php';
include 'processa_index.php';

// Consultar os IDs e nomes das matérias associadas ao planejamento
$query_consultar_materias_planejamento = "
    SELECT pm.materia_idmateria, m.nome AS nome_materia, m.cor AS cor_materia
    FROM appEstudo.planejamento_materia pm
    INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    WHERE pm.planejamento_idplanejamento = $id_planejamento";

$resultado_materias_planejamento = pg_query($conexao, $query_consultar_materias_planejamento);

// Inicializa arrays para armazenar os dados das matérias
$materias_no_planejamento = array();
$cores_materias = array();

// Preenche os arrays com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_materias_planejamento)) {
    $materia_id = $row['materia_idmateria'];
    $nome_materia = $row['nome_materia'];
    $cor_materia = $row['cor_materia'];

    $materias_no_planejamento[$materia_id] = $nome_materia;
    $cores_materias[$nome_materia] = $cor_materia;
}

// Realiza a consulta ao banco de dados para obter os dados de estudo por ponto estudado de cada matéria no planejamento
$query_consulta_pontos_estudo = "
    SELECT e.hora_inicio,
           e.hora_fim,
           m.nome AS nome_materia
    FROM appEstudo.estudos e
    INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
    INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
    LEFT JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria
    WHERE u.idusuario = $id_usuario AND e.materia_idmateria IN (" . implode(',', array_keys($materias_no_planejamento)) . ")
    ORDER BY m.nome, e.hora_inicio";

$resultado_consulta_pontos_estudo = pg_query($conexao, $query_consulta_pontos_estudo);

// Função para calcular o tempo em cada turno
function calcular_tempo_por_turno($hora_inicio, $hora_fim) {
    $turnos = [
        "Madrugada" => [0, 6],
        "Manhã" => [6, 12],
        "Tarde" => [12, 18],
        "Noite" => [18, 24]
    ];
    $tempo_por_turno = array_fill_keys(array_keys($turnos), 0);
    $hora_inicio = strtotime($hora_inicio);
    $hora_fim = strtotime($hora_fim);

    foreach ($turnos as $turno => $horas) {
        $inicio_turno = strtotime(date('Y-m-d', $hora_inicio) . " +" . $horas[0] . " hours");
        $fim_turno = strtotime(date('Y-m-d', $hora_inicio) . " +" . $horas[1] . " hours");
        if ($hora_fim < $inicio_turno) continue;
        if ($hora_inicio > $fim_turno) continue;
        $inicio = max($hora_inicio, $inicio_turno);
        $fim = min($hora_fim, $fim_turno);
        $tempo_por_turno[$turno] += $fim - $inicio;
    }
    return $tempo_por_turno;
}

// Inicializa um array para armazenar os tempos por turno
$tempos_por_turno = array_fill_keys(["Madrugada", "Manhã", "Tarde", "Noite"], 0);

// Preenche o array com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_consulta_pontos_estudo)) {
    $hora_inicio = $row['hora_inicio'];
    $hora_fim = $row['hora_fim'];
    $tempo_por_turno = calcular_tempo_por_turno($hora_inicio, $hora_fim);
    foreach ($tempo_por_turno as $turno => $tempo) {
        $tempos_por_turno[$turno] += $tempo;
    }
}

// Função para converter segundos em HH:MM:SS
function formatar_tempo($segundos) {
    $horas = floor($segundos / 3600);
    $minutos = floor(($segundos % 3600) / 60);
    $segundos = $segundos % 60;
    return sprintf('%02d:%02d:%02d', $horas, $minutos, $segundos);
}

// Converte os tempos de segundos para horas
foreach ($tempos_por_turno as $turno => $tempo) {
    $tempos_por_turno[$turno] = $tempo / 3600;
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">

    <!-- Highcharts library -->

</head>
<body>

<div id="container"></div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const temposPorTurno = {
            Madrugada: <?php echo $tempos_por_turno['Madrugada']; ?>,
            Manhã: <?php echo $tempos_por_turno['Manhã']; ?>,
            Tarde: <?php echo $tempos_por_turno['Tarde']; ?>,
            Noite: <?php echo $tempos_por_turno['Noite']; ?>
        };

        const categories = [
            '<b> Madrugada (00:00 - 06:00) </b>',
            '<b> Manhã (06:00 - 12:00) </b>',
            '<b> Tarde (12:00 - 18:00) </b>',
            '<b> Noite (18:00 - 24:00) </b>'
        ];

        const data = [
            temposPorTurno.Madrugada,
            temposPorTurno.Manhã,
            temposPorTurno.Tarde,
            temposPorTurno.Noite
        ];

        const maxValue = Math.max(...data);

        Highcharts.chart('container', {
            chart: {
                type: 'column',
                backgroundColor: null // Fundo transparente
            },
            title: {
                text: null,
            },
            xAxis: {
                categories: categories,
                //title: {
                //   text: 'Turno'
                //}
            },
            yAxis: {
                min: 0,
                title: {
                    text: 'Tempo de Estudo (Horas)'
                }
            },
            plotOptions: {
                series: {
                    borderColor: 'black', // Cor da borda
                    borderWidth: 1 // Largura da borda
                }
            },
            series: [{
                showInLegend: false,
                data: data.map(value => ({
                    y: value,
                    color: value === maxValue ? 'rgba(0,187,255,0.62)' : 'rgba(0,0,0,0.2)'
                }))
            }],
            tooltip: {
                formatter: function() {
                    const totalHours = this.y;
                    const hours = Math.floor(totalHours);
                    const minutes = Math.floor((totalHours - hours) * 60);
                    const seconds = Math.floor(((totalHours - hours) * 60 - minutes) * 60);
                    return `<b>${this.x}</b><br/>${hours} horas, ${minutes} minutos, ${seconds} segundos`;
                }
            },
            accessibility: {
                enabled: false
            },
            credits: {
                enabled: false
            }
        });
    });
</script>

</body>
</html>
