<?php
include 'conexao_POST.php';
include 'processa_index.php';

// Consultar os IDs e nomes das matérias associadas ao planejamento
$query_consultar_materias_planejamento = "
    SELECT pm.materia_idmateria, m.nome AS nome_materia, m.cor AS cor_materia
    FROM appEstudo.planejamento_materia pm
    INNER JOIN appEstudo.materia m ON pm.materia_idmateria = m.idmateria
    WHERE pm.planejamento_idplanejamento = $id_planejamento";

$resultado_materias_planejamento = pg_query($conexao, $query_consultar_materias_planejamento);

// Inicializa arrays para armazenar os dados das matérias
$materias_no_planejamento = array();
$cores_materias = array();

// Preenche os arrays com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_materias_planejamento)) {
    $materia_id = $row['materia_idmateria'];
    $nome_materia = $row['nome_materia'];
    $cor_materia = $row['cor_materia'];

    $materias_no_planejamento[$materia_id] = $nome_materia;
    $cores_materias[$nome_materia] = $cor_materia;
}

// Realiza a consulta ao banco de dados para obter os dados de estudo por ponto estudado de cada matéria no planejamento
$query_consulta_pontos_estudo = "
    SELECT m.nome AS nome_materia, 
           e.ponto_estudado AS ponto_estudado,
           e.tempo_liquido AS tempo_estudo,
           to_char(e.data, 'DD-MM-YYYY') AS data_estudo,
           c.nome AS nome_curso
    FROM appEstudo.estudos e
    INNER JOIN appEstudo.usuario u ON e.planejamento_usuario_idusuario = u.idusuario
    INNER JOIN appEstudo.planejamento p ON e.planejamento_idplanejamento = p.idplanejamento
    LEFT JOIN appEstudo.materia m ON e.materia_idmateria = m.idmateria
    LEFT JOIN appEstudo.curso c ON e.idcurso = c.idcurso
    WHERE u.idusuario = $id_usuario AND e.materia_idmateria IN (" . implode(',', array_keys($materias_no_planejamento)) . ")
    ORDER BY m.nome, e.data";

$resultado_consulta_pontos_estudo = pg_query($conexao, $query_consulta_pontos_estudo);

// Inicializa um array para armazenar os dados de tempo de estudo por ponto estudado para cada matéria
$pontos_estudo_por_materia = array();

// Preenche o array com os resultados da consulta
while ($row = pg_fetch_assoc($resultado_consulta_pontos_estudo)) {
    $materia = $row['nome_materia'];
    $ponto_estudado = $row['ponto_estudado'];
    $tempo_estudo = $row['tempo_estudo']; // Tempo no formato "HH:MM:SS"
    $data_estudo = $row['data_estudo']; // Data do estudo
    $nome_curso = $row['nome_curso']; // Nome do curso

    // Adiciona os tempos de estudo por ponto estudado para a matéria correspondente
    if (!isset($pontos_estudo_por_materia[$materia])) {
        $pontos_estudo_por_materia[$materia] = array();
    }
    $pontos_estudo_por_materia[$materia][] = array(
        'ponto_estudado' => $ponto_estudado,
        'tempo_estudo' => $tempo_estudo,
        'data_estudo' => $data_estudo,
        'nome_curso' => $nome_curso
    );
}
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Histórico de Estudo</title>
    <link rel="stylesheet" href="css/index1.css">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/css/bootstrap.min.css" integrity="sha384-MCw98/SFnGE8fJT3GXwEOngsV7Zt27NXFoaoApmYm81iuXoPkFOJwJ8ERdknLPMO" crossorigin="anonymous">
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.1.3/js/bootstrap.min.js" integrity="sha384-ChfqqxuZUCnJSK3+MXmPNIyE6ZbWh2IMqE241rYiqJxyMiZ6OW/JmZQ5stwEULTy" crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.14.7/umd/popper.min.js"></script>
    <style>
        body {
            font-family: 'Times New Roman', Times, serif; /* Definindo a fonte para 'Times New Roman' */
            font-size: 15px; /* Defina o tamanho da fonte aqui */
        }
        .table-container {
            width: 100%;
            margin-bottom: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        th {
            text-align: center;
        }

        .table-header {
            background-color: lightgray;
            text-align: center;
            font-weight: bold;
            font-size: 1.2em;
        }

        .table-title {
            text-align: center;
            font-weight: bold;
        }

        tbody tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        tbody tr:hover {
            background-color: #f1f1f1;
        }

        .menu {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f4f4f4;
            border: 1px solid #ddd;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .menu a {
            padding: 10px 15px;
            text-decoration: none;
            color: #fff;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
            border-radius: 5px;
            transition: background-color 0.3s, color 0.3s;
        }

        .menu a:hover {
            background-color: #e9e9e9;
            color: #000;
        }

        .back-to-top {
            text-align: center;
            margin-top: 10px;
        }

        .back-to-top a {
            text-decoration: none;
            color: #fff;
            background-color: #2b2723;
            padding: 10px 15px;
            border-radius: 5px;
            display: inline-block;
            transition: background-color 0.3s, color 0.3s;
        }

        .back-to-top a:hover {
            background-color: rgba(43, 39, 35, 0.76);
        }

        .quadro-titulo {
            background-color: #f4f4f4;
            padding: 20px;
            border: 1px solid #ccc;
            margin: 0;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); /* Sombra para relevo */
            text-align: center;
            font-family: "Courier Prime", monospace;
            font-weight: bold;
        }
        .quadro-titulo h1 {
            font-family: "Courier Prime", monospace;
            font-weight: bold;
            color: #333;
            margin: 0;
        }
    </style>
</head>
<body>

<h1 class="quadro-titulo">Histórico de Estudo Por Disciplina</h1>

<div id="menu-top" class="menu">
    <?php
    // Cria o menu com links para cada matéria
    foreach ($pontos_estudo_por_materia as $materia => $pontos_estudo) {
        $anchor = str_replace(' ', '_', $materia); // Substitui espaços por underscores nos IDs
        $cor_materia = $cores_materias[$materia]; // Obtém a cor da matéria
        echo "<a href='#$anchor' style='background-color: $cor_materia;'>$materia</a>";
    }
    ?>
</div>

    <a href="#" class="btn btn-dark btn-sm" style="font-family: 'Courier Prime', monospace;" onclick="window.close(); return false;">Fechar Página</a>

<?php
// Loop para exibir as tabelas para cada matéria
foreach ($pontos_estudo_por_materia as $materia => $pontos_estudo) {
    $cor_materia = $cores_materias[$materia]; // Obtém a cor da matéria
    $anchor = str_replace(' ', '_', $materia); // Substitui espaços por underscores nos IDs

    echo "<div class='table-container' id='$anchor'>
            <table>
                <thead>
                    <tr class='table-header' style='background-color: $cor_materia;'>
                        <th colspan='4'>$materia</th>
                    </tr>
                    <tr class='table-title'>
                        <th>Ponto Estudado</th>
                        <th>Tempo de Estudo</th>
                        <th>Data do Estudo</th>
                        <th>Curso</th>
                    </tr>
                </thead>
                <tbody>";
    foreach ($pontos_estudo as $info) {
        echo "<tr>
                <td>{$info['ponto_estudado']}</td>
                <td>{$info['tempo_estudo']}</td>
                <td>{$info['data_estudo']}</td>
                <td>{$info['nome_curso']}</td>
            </tr>";
    }
    echo "</tbody></table>
          <div class='back-to-top'>
              <a href='#menu-top'>Voltar ao Topo</a>
          </div>
          </div>";
}
?>

</body>
</html>


