<?php
session_start();
require_once '../../conexao_POST.php';

header('Content-Type: application/json');

try {
    $dados = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($dados['id']) || !isset($dados['status'])) {
        throw new Exception('Dados incompletos');
    }

    // Validar status permitidos conforme definido no schema
    $status_permitidos = ['backlog', 'em_andamento', 'concluido', 'arquivado'];
    if (!in_array($dados['status'], $status_permitidos)) {
        throw new Exception('Status inválido');
    }

    $sql = "UPDATE appestudo.kanban_tarefas 
            SET status = $1 
            WHERE id = $2 AND usuario_id = $3";

    $result = pg_query_params($conexao, $sql, array(
        $dados['status'],
        $dados['id'],
        $_SESSION['idusuario']
    ));

    if (!$result) {
        throw new Exception(pg_last_error($conexao));
    }

    if (pg_affected_rows($result) === 0) {
        throw new Exception('Tarefa não encontrada ou sem permissão');
    }

    echo json_encode(['success' => true]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['success' => false, 'erro' => $e->getMessage()]);
}