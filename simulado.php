<?php
include_once 'funcoes.php';

if (!isset($id_planejamento) || !isset($id_usuario)) {
    die("ID do planejamento ou ID do usuário não definido.");
}

// Consulta SQL para buscar todos os simulados
$query = "SELECT 
            data,
            ponto_estudado,
            q_total,
            q_errada,
            q_certa
          FROM appEstudo.estudos 
          WHERE metodo = 'Simulado'
            AND planejamento_usuario_idusuario = $id_usuario
          ORDER BY data ASC";

$resultado = pg_query($conexao, $query);
$simulados = array();
$dados_grafico = array();

while ($row = pg_fetch_assoc($resultado)) {
    $simulados[] = $row;

    // Preparar dados para o gráfico
    $percentual_acerto = ($row['q_total'] > 0) ?
        round(($row['q_certa'] / $row['q_total']) * 100, 2) : 0;

    $dados_grafico[] = array(
        'data' => date('d/m/Y', strtotime($row['data'])),
        'percentual' => $percentual_acerto,
        'total' => (int)$row['q_total'],
        'acertos' => (int)$row['q_certa'],
        'erros' => (int)$row['q_errada']
    );
}

// Verificação de segurança dos dados
if (empty($dados_grafico)) {
    $dados_grafico = array();
}
$dados_grafico_json = json_encode($dados_grafico) ?: '[]';

// Reverter array para mostrar do mais recente para o mais antigo
$simulados = array_reverse($simulados);
$total_simulados = count($simulados);
?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <link href="https://fonts.googleapis.com/css2?family=Cinzel:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Courier+Prime:wght@400;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Garantir que o Highcharts seja carregado apenas uma vez -->


    <style>
        /* Adicione este novo estilo */
        .simulado-header {
            background: #00008B;
            color: white;
            padding: 1.5rem;
            border-bottom: 3px solid #b8860b;
            /* removido text-align: center */
        }

        .simulado-header h1 {
            font-size: 1.2rem;
            font-weight: 600;
            letter-spacing: 0.5px;
            font-family: 'Cinzel', serif;
            margin: 0;
            margin-bottom: 0.5rem;
            text-align: left; /* Garantir alinhamento à esquerda */
        }

        .simulado-subtitulo {
            font-size: 0.9rem;
            opacity: 0.8;
            font-family: 'Courier Prime', monospace;
            color: white;
            text-align: left; /* Garantir alinhamento à esquerda */
        }

        .simulado-card {
            max-width: 800px;
            margin: 2rem auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            position: relative;
        }

        .simulado-header {
            background: #000080;
            color: white;
            padding: 1.5rem;
            text-align: center;
            border-bottom: 3px solid #b8860b;
        }

        .carousel-controls {
            display: flex;
            justify-content: center;
            gap: 1rem;
            padding: 1rem;
            background: rgba(184, 134, 11, 0.1);
        }

        .nav-button {
            font-family: 'Quicksand', sans-serif;
            background: var(--primary);
            color: white;
        /*  border: 2px solid #b8860b; */
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .nav-button:hover {
           /* background: #b8860b; */
            transform: translateY(-2px);
        }

        .nav-button:disabled {
            background: #ccc;
            border-color: #999;
            cursor: not-allowed;
            transform: none;
        }

        .simulado-content {
            padding: 2rem;
            background: var(--parchment, #f8f0e3);
            margin: 1rem;
            border-radius: 8px;
            transition: opacity 0.3s ease;
        }

        .data-simulado {
            font-family: 'Courier Prime', monospace;
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .simulado-counter {
            background: #800020;
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-family: 'Quicksand', sans-serif;
        }

        .nome-simulado {
            font-family: 'Quicksand', sans-serif;
            color: #800020;
            font-size: 1.3rem;
            margin: 1rem 0;
            text-align: center;
            padding: 0.5rem;
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .stats-grid {
            display: grid;
            /*grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));*/
            gap: 1rem;
            margin-top: 1.5rem;
        }

        .stat-item {
            background: white;
            padding: 1rem;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            transition: transform 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-3px);
        }

        .stat-label {
            font-family: 'Quicksand', sans-serif;
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0.5rem;
        }



        .stat-value.total { color: #2c3e50; }
        .stat-value.erros { color: #dc3545; }
        .stat-value.acertos { color: #28a745; }

        .primeiro-simulado {
            background: var(--parchment, #f8f0e3);
            padding: 2rem;
            text-align: center;
            margin: 2rem;
            border-radius: 8px;
            border: 2px dashed #b8860b;
        }

        .primeiro-simulado h2 {
            font-family: 'Cinzel', serif;
            color: #800020;
            margin: 0;
            font-size: 1.8rem;
        }

        .performance-indicator {
            margin-top: 1rem;
            padding: 0.5rem;
            text-align: center;
            font-family: 'Cinzel', serif;
            color: white;
            border-radius: 4px;
        }

        .performance-good { background: rgba(40, 167, 69, 0.9); }
        .performance-medium { background: rgba(255, 193, 7, 0.9); }
        .performance-needs-improvement { background: rgba(220, 53, 69, 0.9); }

        .graph-container {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            position: relative;
            width: calc(100% - 2rem);
            min-height: 450px;
        }

        #simulados-evolucao-grafico {
            height: 400px;
            width: 100%;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        #simulados-evolucao-grafico.loaded {
            opacity: 1;
        }

        .graph-title {
            font-family: 'Cinzel', serif;
            color: #800020;
            text-align: center;
            margin-bottom: 1rem;
            font-size: 1.2rem;
            padding: 0 1rem;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10;
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .loading-overlay.active {
            opacity: 1;
            pointer-events: all;
        }
        /*
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #800020;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
            */

        .toggle-view-btn {
            font-family: 'Cinzel', serif;
            background: #800020;
            color: white;
            border: 2px solid #b8860b;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 1rem auto;
            display: block;
        }

        .toggle-view-btn:hover {
            background: #b8860b;
        }

        .view-section {
            display: none;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .view-section.active {
            display: block;
            opacity: 1;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 600px) {
            .stats-grid {
                grid-template-columns: 1fr;
            }

            .carousel-controls {
                flex-direction: column;
                align-items: center;
            }

            .nav-button {
                width: 100%;
                margin: 0.25rem 0;
            }

            .data-simulado {
                flex-direction: column;
                gap: 0.5rem;
                text-align: center;
            }
        }
    </style>
</head>
<body>
<div class="simulado-card">

    <?php if ($total_simulados == 0): ?>
        <div class="primeiro-simulado">
            <h2>Realize Seu 1º Simulado</h2>
        </div>
    <?php else: ?>
        <button class="toggle-view-btn" onclick="toggleView()">
            Alternar Visualização
        </button>

        <!-- Seção do Gráfico -->
        <div id="graph-view" class="view-section active">
            <div class="graph-container">
                <div class="loading-overlay active">
                    <div class="loading-spinner"></div>
                </div>
                <div class="graph-title">Evolução do Desempenho nos Simulados</div>
                <div id="simulados-evolucao-grafico"></div>
            </div>
        </div>

        <!-- Seção dos Cards -->
        <div id="cards-view" class="view-section">
            <div class="carousel-controls">
                <button class="nav-button" id="prevBtn" onclick="navegarSimulado(-1)">← Anterior</button>
                <button class="nav-button" id="nextBtn" onclick="navegarSimulado(1)">Próximo →</button>
            </div>

            <?php foreach ($simulados as $index => $simulado): ?>
                <div class="simulado-content" id="simulado-<?php echo $index; ?>"
                     style="display: <?php echo $index == 0 ? 'block' : 'none'; ?>; opacity: 1;">
                    <div class="data-simulado">
                        <?php
                        $dia_semana = date('N', strtotime($simulado['data']));
                        $dias_semana = array(
                            1 => 'Segunda-feira',
                            2 => 'Terça-feira',
                            3 => 'Quarta-feira',
                            4 => 'Quinta-feira',
                            5 => 'Sexta-feira',
                            6 => 'Sábado',
                            7 => 'Domingo'
                        );
                        $data_formatada = date('d-m-Y', strtotime($simulado['data']));
                        echo "<span><strong>{$dias_semana[$dia_semana]}</strong> • {$data_formatada}</span>";
                        ?>
                        <span class="simulado-counter">Simulado <?php echo $total_simulados - $index; ?> de <?php echo $total_simulados; ?></span>
                    </div>

                    <div class="nome-simulado">
                        <?php echo $simulado['ponto_estudado']; ?>
                    </div>

                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-label">Total de Questões</div>
                            <div class="stat-value total"><?php echo $simulado['q_total']; ?></div>
                        </div>

                        <div class="stat-item">
                            <div class="stat-label">Erros</div>
                            <div class="stat-value erros"><?php echo $simulado['q_errada']; ?></div>
                        </div>

                        <div class="stat-item">
                            <div class="stat-label">Acertos</div>
                            <div class="stat-value acertos"><?php echo $simulado['q_certa']; ?></div>
                        </div>
                    </div>

                    <?php
                    $percentual_acerto = ($simulado['q_total'] > 0) ? ($simulado['q_certa'] / $simulado['q_total']) * 100 : 0;
                    $classe_performance = '';
                    $mensagem_performance = '';

                    if ($percentual_acerto >= 70) {
                        $classe_performance = 'performance-good';
                        $mensagem_performance = 'Excelente desempenho!';
                    } elseif ($percentual_acerto >= 50) {
                        $classe_performance = 'performance-medium';
                        $mensagem_performance = 'Bom desempenho, continue praticando!';
                    } else {
                        $classe_performance = 'performance-needs-improvement';
                        $mensagem_performance = 'Continue estudando, você vai melhorar!';
                    }
                    ?>

                    <div class="performance-indicator <?php echo $classe_performance; ?>">
                        <?php echo $mensagem_performance; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<script>
    let simuladoAtual = 0;
    const totalSimulados = <?php echo $total_simulados; ?>;
    let viewMode = 'graph';
    let chart = null;

    function initGrafico() {
        if (!document.getElementById('simulados-evolucao-grafico')) return;

        const dados = <?php echo $dados_grafico_json; ?>;
        const loadingOverlay = document.querySelector('.loading-overlay');

        if (!dados || !dados.length) {
            document.getElementById('simulados-evolucao-grafico').innerHTML =
                '<div style="text-align: center; padding: 2rem;">Nenhum dado disponível</div>';
            loadingOverlay.classList.remove('active');
            return;
        }

        if (chart) {
            chart.destroy();
        }

        // Tema personalizado do Highcharts
        Highcharts.theme = {
            colors: ['#28a745', '#dc3545', '#4a90e2', '#b8860b'],
            chart: {
                backgroundColor: 'transparent',
                style: {
                    fontFamily: 'Quicksand, sans-serif'
                }
            },
            title: {
                style: {
                    color: '#800020',
                    fontFamily: 'Cinzel, serif'
                }
            },
            subtitle: {
                style: {
                    color: '#666666',
                    fontFamily: 'Quicksand, sans-serif'
                }
            },
            xAxis: {
                gridLineWidth: 1,
                labels: {
                    style: {
                        fontFamily: 'Quicksand, sans-serif'
                    }
                }
            },
            yAxis: {
                gridLineWidth: 1,
                title: {
                    style: {
                        fontFamily: 'Cinzel, serif'
                    }
                },
                labels: {
                    style: {
                        fontFamily: 'Quicksand, sans-serif'
                    }
                }
            },
            legend: {
                itemStyle: {
                    fontFamily: 'Quicksand, sans-serif',
                    color: '#333333'
                }
            },
            tooltip: {
                backgroundColor: 'rgba(255, 255, 255, 0.95)',
                borderWidth: 1,
                borderColor: '#b8860b',
                style: {
                    fontFamily: 'Quicksand, sans-serif'
                }
            }
        };

        // Aplicar o tema
        Highcharts.setOptions(Highcharts.theme);

        chart = Highcharts.chart('simulados-evolucao-grafico', {
            chart: {
                height: 400,
                events: {
                    load: function() {
                        loadingOverlay.classList.remove('active');
                        document.getElementById('simulados-evolucao-grafico').classList.add('loaded');
                    }
                }
            },
            title: {
                text: null
            },
            xAxis: {
                categories: dados.map(d => d.data),
                crosshair: true,
                labels: {
                    rotation: -45
                }
            },
            yAxis: [{
                title: {
                    text: 'Quantidade de Questões'
                }
            }, {
                title: {
                    text: 'Taxa de Acerto (%)'
                },
                opposite: true,
                labels: {
                    format: '{value}%'
                }
            }],
            tooltip: {
                shared: true,
                useHTML: true,
                headerFormat: '<table><tr><th colspan="2">{point.key}</th></tr>',
                pointFormat: '<tr><td style="color: {series.color}">{series.name}: </td>' +
                    '<td style="text-align: right"><b>{point.y}</b></td></tr>',
                footerFormat: '</table>'
            },
            tooltip: {
                shared: true,
                useHTML: true,
                headerFormat: '<table><tr><th colspan="2">{point.key}</th></tr>',
                pointFormatter: function() {
                    let suffix = '';
                    if (this.series.name === 'Taxa de Acerto') {
                        suffix = '%';
                    } else {
                        //suffix = '';
                    }
                    return '<tr><td style="color: ' + this.series.color + '">' +
                        this.series.name + ': </td>' +
                        '<td style="text-align: right"><b>' + this.y + suffix + '</b></td></tr>';
                },
                footerFormat: '</table>'
            },
            series: [{
                name: 'Acertos',
                type: 'column',
                data: dados.map(d => d.acertos)
            }, {
                name: 'Erros',
                type: 'column',
                data: dados.map(d => d.erros)
            }, {
                name: 'Total de Questões',
                type: 'line',
                data: dados.map(d => d.total),
                color: '#4a90e2',
                dashStyle: 'shortdot',
                marker: {
                    symbol: 'square'
                }
            }, {
                name: 'Taxa de Acerto',
                type: 'spline',
                data: dados.map(d => d.percentual),
                yAxis: 1,
                dataLabels: {
                    enabled: true,
                    format: '{point.y}%'
                },
                marker: {
                    lineWidth: 2,
                    lineColor: '#b8860b',
                    fillColor: 'white'
                }
            }]
        });
    }

    function navegarSimulado(direcao) {
        const simuladoAtualElement = document.getElementById(`simulado-${simuladoAtual}`);
        simuladoAtualElement.style.opacity = '0';

        setTimeout(() => {
            simuladoAtualElement.style.display = 'none';
            simuladoAtual += direcao;
            atualizarBotoes();

            const novoSimuladoElement = document.getElementById(`simulado-${simuladoAtual}`);
            novoSimuladoElement.style.display = 'block';
            setTimeout(() => {
                novoSimuladoElement.style.opacity = '1';
            }, 50);
        }, 300);
    }

    function atualizarBotoes() {
        document.getElementById('prevBtn').disabled = simuladoAtual === 0;
        document.getElementById('nextBtn').disabled = simuladoAtual === totalSimulados - 1;
    }

    function toggleView() {
        const graphView = document.getElementById('graph-view');
        const cardsView = document.getElementById('cards-view');
        const loadingOverlay = document.querySelector('.loading-overlay');

        if (viewMode === 'cards') {
            graphView.classList.add('active');
            cardsView.classList.remove('active');
            viewMode = 'graph';
            loadingOverlay.classList.add('active');
            setTimeout(initGrafico, 300);
        } else {
            graphView.classList.remove('active');
            cardsView.classList.add('active');
            viewMode = 'cards';
        }
    }

    // Inicialização
    if (totalSimulados > 0) {
        atualizarBotoes();
        // Aguarda um momento para inicializar o gráfico
        setTimeout(initGrafico, 300);
    }

    // Atualiza o gráfico quando a janela é redimensionada
    let resizeTimer;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            if (viewMode === 'graph') {
                initGrafico();
            }
        }, 250);
    });
</script>
</body>
</html>