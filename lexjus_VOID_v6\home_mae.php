<?php
// Headers para evitar cache da página HTML
header('Cache-Control: no-cache, no-store, must-revalidate, max-age=0');
header('Pragma: no-cache');
header('Expires: Thu, 01 Jan 1970 00:00:00 GMT');

// Incluir sistema de cache busting
require_once 'includes/cache_buster.php';
include_once("../session_config.php");
require_once '../conexao_POST.php';

// Verificar se o usuário está logado
if (!verificarSessaoValida()) {
    header("Location: ../login_index.php");
    exit();
}

// Buscar nome do usuário
$query_buscar_nome = "SELECT nome FROM appEstudo.usuario WHERE idusuario = " . $_SESSION['idusuario'];
$resultado_nome = pg_query($conexao, $query_buscar_nome);
$nome_usuario = ($resultado_nome && pg_num_rows($resultado_nome) > 0)
    ? pg_fetch_assoc($resultado_nome)['nome']
    : "Usuário";
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LexJus - Página Principal</title>
    <link rel="shortcut icon" href="../logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <meta name="app-version" content="<?php echo cache_version(); ?>">
    
    <!-- CSS Principal -->
    <link rel="stylesheet" href="<?php echo cache_css('style.css'); ?>">
    <link rel="stylesheet" href="<?php echo cache_css('css/home-netflix.css'); ?>">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
</head>
<body>
    <div class="header-barra">
        <div class="header-left">
            <div class="logo">
                <img src="../logo/logo_vertical_azul.png" alt="Logo" class="logo-light">
                <img src="../logo/logo_vertical.png" alt="Logo" class="logo-dark">
            </div>
        </div>
        <div class="header-right">
            <div class="user-info">
                <i class="fas fa-user-circle"></i>
                <span class="user-name"><?php echo htmlspecialchars($nome_usuario ?? 'Usuário'); ?></span>
            </div>
            <div class="theme-toggle">
                <button id="themeToggle" class="theme-btn" title="Alternar tema">
                    <i class="fas fa-moon" id="themeIcon"></i>
                </button>
            </div>
        </div>
    </div>


    <!-- Seção Principal -->
    <section class="main-section">
        <div class="container">
            <div class="welcome-header">
                <h1 class="welcome-title">
                Seu, <span class="highlight">LexJus</span>!
                </h1>
                <p class="welcome-subtitle">
                    Escolha sua área de estudo
                </p>
            </div>
        </div>
    </section>

    <!-- Cards Section -->
    <section class="cards-section">
        <div class="container">
            <div class="cards-grid">
                <!-- Card Leis -->
                <div class="main-card" onclick="window.location.href='legis.php'">
                    <div class="card-icon">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title">Legislação</h3>
                        <p class="card-description">
                            Estude as principais leis brasileiras com nosso sistema interativo de aprendizado
                        </p>
                    </div>
                    <div class="card-arrow">
                        <i class="fas fa-arrow-right"></i>
                    </div>
                </div>

                <!-- Card Jurisprudência -->
                <div class="main-card" onclick="window.location.href='juris.php'">
                    <div class="card-icon">
                        <i class="fas fa-gavel"></i>
                    </div>
                    <div class="card-content">
                        <h3 class="card-title">Jurisprudência</h3>
                        <p class="card-description">
                            Explore decisões judiciais e precedentes dos tribunais superiores
                        </p>
                    </div>
                    <div class="card-arrow">
                        <i class="fas fa-arrow-right"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Script do tema escuro -->
    <script>
        // Sistema de tema escuro/claro
        const themeToggle = document.getElementById('themeToggle');
        const themeIcon = document.getElementById('themeIcon');
        const body = document.body;

        // Verificar tema salvo
        const savedTheme = localStorage.getItem('theme') || 'light';
        body.setAttribute('data-theme', savedTheme);
        updateThemeIcon(savedTheme === 'dark');

        // Toggle do tema
        themeToggle.addEventListener('click', () => {
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            body.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            updateThemeIcon(newTheme === 'dark');
        });

        function updateThemeIcon(isDark) {
            themeIcon.className = isDark ? 'fas fa-sun' : 'fas fa-moon';
        }

        // Adicionar efeito simples de clique nos cards
        document.querySelectorAll('.main-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.98)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>
