<?php
/**
 * Verificar nomes dos usuários
 */

// Configurações do banco
$host = 'barbeiro_br.mysql.dbaas.com.br';
$db_name = 'barbeiro_br';
$username = 'barbeiro_br';
$password = 'Lucasb90#';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$db_name;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "=== VERIFICANDO NOMES DOS USUÁRIOS ===\n\n";
    
    $stmt = $pdo->query("SELECT id, nome, cpf, tipo FROM usuarios ORDER BY tipo, nome");
    $users = $stmt->fetchAll();
    
    echo "📊 Usuários no banco:\n";
    foreach ($users as $user) {
        $nome = trim($user['nome']);
        $nomeLength = strlen($nome);
        
        echo "- ID: {$user['id']}\n";
        echo "  Nome: '{$nome}' (length: $nomeLength)\n";
        echo "  CPF: {$user['cpf']}\n";
        echo "  Tipo: {$user['tipo']}\n\n";
        
        if (empty($nome)) {
            echo "  ⚠️ NOME VAZIO DETECTADO!\n\n";
        }
    }
    
    // Verificar se há caracteres especiais ou problemas de encoding
    echo "🔍 Verificando encoding e caracteres especiais:\n";
    foreach ($users as $user) {
        $nome = $user['nome'];
        $hex = bin2hex($nome);
        echo "- Nome: '{$nome}' | Hex: $hex\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erro: " . $e->getMessage() . "\n";
}
?>
