<?php
/**
 * Teste simples para verificar dados existentes
 */

$baseUrl = 'https://planejaaqui.com.br/fidelidade_barbearia/api/v1';

function makeRequest($url, $method = 'GET', $data = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    if ($data && in_array($method, ['POST', 'PUT'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'code' => $httpCode,
        'body' => json_decode($response, true)
    ];
}

echo "🔍 VERIFICANDO DADOS EXISTENTES\n";
echo str_repeat('=', 50) . "\n";

// 1. Buscar dono por CPF
echo "\n1. Buscando dono (CPF: 00000000000):\n";
$result = makeRequest("$baseUrl/users/cpf/00000000000");
echo "Status: " . $result['code'] . "\n";
echo "Response: " . json_encode($result['body'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

// 2. Listar todos os clientes
echo "\n2. Listando todos os clientes:\n";
$result = makeRequest("$baseUrl/users/clients");
echo "Status: " . $result['code'] . "\n";
echo "Response: " . json_encode($result['body'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

// 2.1. Testar endpoint de dados completos para um cliente específico
echo "\n2.1. Testando dados completos do cliente 'João Teste Cliente-CLT12345678901':\n";
$clienteId = 'João Teste Cliente-CLT12345678901';
$result = makeRequest("$baseUrl/users/complete/" . urlencode($clienteId));
echo "Status: " . $result['code'] . "\n";
echo "Response: " . json_encode($result['body'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

// 3. Verificar senha mestra atual
echo "\n3. Verificando senha mestra (123):\n";
$result = makeRequest("$baseUrl/master-password/verify", 'POST', ['senha' => '123']);
echo "Status: " . $result['code'] . "\n";
echo "Response: " . json_encode($result['body'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

// 4. Buscar função get_senha_mestra
echo "\n4. Buscando senha mestra atual via function:\n";
$result = makeRequest("$baseUrl/function/get_senha_mestra", 'POST', []);
echo "Status: " . $result['code'] . "\n";
echo "Response: " . json_encode($result['body'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

// 5. Estatísticas gerais
echo "\n5. Estatísticas gerais:\n";
$result = makeRequest("$baseUrl/fidelity/stats");
echo "Status: " . $result['code'] . "\n";
echo "Response: " . json_encode($result['body'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";

echo "\n✅ VERIFICAÇÃO CONCLUÍDA!\n";
?>
