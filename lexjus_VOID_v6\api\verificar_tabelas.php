<?php
session_start();
require_once '../../conexao_POST.php';

// Headers para CORS e JSON
header('Content-Type: text/html; charset=utf-8');
header('Access-Control-Allow-Origin: *');

// Verificar se o usuário está autenticado
if (!isset($_SESSION['idusuario'])) {
    echo "<h1>❌ Usuário não autenticado</h1>";
    echo "<p>Faça login no sistema primeiro.</p>";
    exit;
}

$usuario_id = $_SESSION['idusuario'];

echo "<h1>Verificação das Tabelas - LexJus</h1>";

// 1. Verificar se a tabela lexjus_leis existe
echo "<h2>1. Verificando tabela lexjus_leis</h2>";
$query = "SELECT table_name FROM information_schema.tables WHERE table_schema = 'appestudo' AND table_name = 'lexjus_leis'";
$result = pg_query($conexao, $query);

if ($result && pg_num_rows($result) > 0) {
    echo "✅ Tabela lexjus_leis existe<br>";
    
    // Listar leis disponíveis
    $query_leis = "SELECT id, codigo, nome FROM appestudo.lexjus_leis ORDER BY id";
    $result_leis = pg_query($conexao, $query_leis);
    
    if ($result_leis) {
        echo "<strong>Leis disponíveis:</strong><br>";
        while ($row = pg_fetch_assoc($result_leis)) {
            echo "- ID: {$row['id']}, Código: {$row['codigo']}, Nome: {$row['nome']}<br>";
        }
    }
} else {
    echo "❌ Tabela lexjus_leis NÃO existe<br>";
}

// 2. Verificar se a tabela lexjus_user_preferencias existe
echo "<h2>2. Verificando tabela lexjus_user_preferencias</h2>";
$query = "SELECT table_name FROM information_schema.tables WHERE table_schema = 'appestudo' AND table_name = 'lexjus_user_preferencias'";
$result = pg_query($conexao, $query);

if ($result && pg_num_rows($result) > 0) {
    echo "✅ Tabela lexjus_user_preferencias existe<br>";
    
    // Verificar preferência do usuário atual
    $query_pref = "SELECT * FROM appestudo.lexjus_user_preferencias WHERE usuario_id = $1";
    $result_pref = pg_query_params($conexao, $query_pref, [$usuario_id]);
    
    if ($result_pref && pg_num_rows($result_pref) > 0) {
        $pref = pg_fetch_assoc($result_pref);
        echo "<strong>Preferência do usuário $usuario_id:</strong><br>";
        echo "- Lei atual: {$pref['lei_atual']}<br>";
        echo "- Tema: {$pref['tema']}<br>";
    } else {
        echo "⚠️ Usuário $usuario_id não tem preferências cadastradas<br>";
    }
} else {
    echo "❌ Tabela lexjus_user_preferencias NÃO existe<br>";
}

// 3. Verificar se a tabela lexjus_progresso existe
echo "<h2>3. Verificando tabela lexjus_progresso</h2>";
$query = "SELECT table_name FROM information_schema.tables WHERE table_schema = 'appestudo' AND table_name = 'lexjus_progresso'";
$result = pg_query($conexao, $query);

if ($result && pg_num_rows($result) > 0) {
    echo "✅ Tabela lexjus_progresso existe<br>";
    
    // Verificar estrutura da tabela
    $query_cols = "SELECT column_name, data_type FROM information_schema.columns WHERE table_schema = 'appestudo' AND table_name = 'lexjus_progresso' ORDER BY ordinal_position";
    $result_cols = pg_query($conexao, $query_cols);
    
    if ($result_cols) {
        echo "<strong>Estrutura da tabela:</strong><br>";
        while ($col = pg_fetch_assoc($result_cols)) {
            echo "- {$col['column_name']}: {$col['data_type']}<br>";
        }
    }
    
    // Verificar progresso do usuário atual
    $query_prog = "SELECT COUNT(*) as total FROM appestudo.lexjus_progresso WHERE usuario_id = $1";
    $result_prog = pg_query_params($conexao, $query_prog, [$usuario_id]);
    
    if ($result_prog) {
        $prog = pg_fetch_assoc($result_prog);
        echo "<strong>Progresso do usuário $usuario_id:</strong> {$prog['total']} artigos lidos<br>";
    }
} else {
    echo "❌ Tabela lexjus_progresso NÃO existe<br>";
}

// 4. Testar a função obterLeiAtualUsuario
echo "<h2>4. Testando função obterLeiAtualUsuario</h2>";

function obterLeiAtualUsuario($conexao, $usuario_id) {
    // Primeiro, verificar se existe preferência do usuário
    $query = "SELECT l.id, l.codigo, p.lei_atual
              FROM appestudo.lexjus_user_preferencias p
              JOIN appestudo.lexjus_leis l ON l.codigo = p.lei_atual
              WHERE p.usuario_id = $1";

    $result = pg_query_params($conexao, $query, [$usuario_id]);

    if ($result && pg_num_rows($result) > 0) {
        $row = pg_fetch_assoc($result);
        echo "✅ Lei atual encontrada: {$row['codigo']} (ID: {$row['id']})<br>";
        return $row['id'];
    }

    echo "⚠️ Nenhuma preferência encontrada, usando CF como fallback<br>";

    // Fallback: retornar ID da Constituição Federal
    $query_cf = "SELECT id FROM appestudo.lexjus_leis WHERE codigo = 'CF'";
    $result_cf = pg_query($conexao, $query_cf);

    if ($result_cf && pg_num_rows($result_cf) > 0) {
        $row_cf = pg_fetch_assoc($result_cf);
        echo "✅ CF encontrada como fallback (ID: {$row_cf['id']})<br>";
        return $row_cf['id'];
    }

    echo "❌ ERRO: Nem preferência nem CF encontrada!<br>";
    return null;
}

$lei_id = obterLeiAtualUsuario($conexao, $usuario_id);
echo "<strong>Resultado:</strong> Lei ID = " . ($lei_id ? $lei_id : 'NULL') . "<br>";

// 5. Verificar se há erros de conexão
echo "<h2>5. Status da Conexão</h2>";
if ($conexao) {
    echo "✅ Conexão PostgreSQL ativa<br>";
    echo "Status: " . pg_connection_status($conexao) . "<br>";
} else {
    echo "❌ Erro na conexão PostgreSQL<br>";
    echo "Erro: " . pg_last_error() . "<br>";
}

echo "<h2>6. Teste da API de Progresso</h2>";

// Simular uma requisição POST para a API de progresso
$dados_teste = [
    'artigo_numero' => 'Art. 1º',
    'lido' => true
];

echo "<strong>Testando inserção de progresso:</strong><br>";
echo "Dados: " . json_encode($dados_teste) . "<br>";

$lei_id = obterLeiAtualUsuario($conexao, $usuario_id);

if ($lei_id) {
    $query_insert = "INSERT INTO appestudo.lexjus_progresso (usuario_id, lei_id, artigo_numero, lido)
                     VALUES ($1, $2, $3, $4)
                     ON CONFLICT (usuario_id, lei_id, artigo_numero)
                     DO UPDATE SET lido = $4, data_leitura = CURRENT_TIMESTAMP";

    $result_insert = pg_query_params($conexao, $query_insert, [$usuario_id, $lei_id, $dados_teste['artigo_numero'], $dados_teste['lido']]);

    if ($result_insert) {
        echo "✅ Inserção de progresso funcionou!<br>";
    } else {
        echo "❌ Erro na inserção de progresso:<br>";
        echo "Erro PostgreSQL: " . pg_last_error($conexao) . "<br>";
    }
} else {
    echo "❌ Não foi possível obter lei_id para teste<br>";
}

echo "<h2>7. Informações da Sessão</h2>";
echo "Usuário ID: " . $usuario_id . "<br>";
echo "Sessão ativa: " . (session_status() === PHP_SESSION_ACTIVE ? 'Sim' : 'Não') . "<br>";

echo "<h2>8. Verificar Constraints</h2>";
$query_constraints = "SELECT conname, contype FROM pg_constraint
                      WHERE conrelid = 'appestudo.lexjus_progresso'::regclass";
$result_constraints = pg_query($conexao, $query_constraints);

if ($result_constraints) {
    echo "<strong>Constraints da tabela lexjus_progresso:</strong><br>";
    while ($constraint = pg_fetch_assoc($result_constraints)) {
        echo "- {$constraint['conname']} (tipo: {$constraint['contype']})<br>";
    }
} else {
    echo "❌ Erro ao verificar constraints<br>";
}

?>
