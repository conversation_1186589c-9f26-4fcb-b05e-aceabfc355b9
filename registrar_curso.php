      <?php
        session_start();
        ?>

<!DOCTYPE html>
<html lang="pt-br">
<head>
    <title>Seleção de Cursos</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,700;1,400&family=Special+Elite&family=Cinzel:wght@400;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Quicksand:wght@400;500;600;700&display=swap');

        :root {
            --parchment: #f8f0e3;
            --vintage-gold: #b8860b;
            --dark-ink: #1a1a1a;
            --burgundy: #800020;
            --gold-accent: #daa520;
            --shadow-color: rgba(0, 0, 0, 0.2);
            --elegant-border: linear-gradient(45deg, var(--vintage-gold), #ffd700, var(--vintage-gold));
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Quicksand', sans-serif;
            background: #2c1810 url("data:image/svg+xml,%3Csvg width='50' height='50' viewBox='0 0 50 50' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23daa520' fill-opacity='0.1'%3E%3Cpath d='M0 0h50v50H0z'/%3E%3Ccircle cx='25' cy='25' r='1'/%3E%3C/g%3E%3C/svg%3E");
            color: var(--dark-ink);
            line-height: 1.6;
            padding: 2rem 0;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        .elegant-paper {
            background: var(--parchment);
            background-image:
                    linear-gradient(rgba(255, 255, 255, .2) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, .2) 1px, transparent 1px);
            background-size: 20px 20px;
            border: 1px solid var(--vintage-gold);
            box-shadow:
                    0 0 0 1px var(--vintage-gold),
                    0 0 0 15px var(--parchment),
                    0 0 0 16px var(--vintage-gold),
                    0 2px 5px 16px var(--shadow-color);
            padding: 4rem;
            position: relative;
            border-radius: 2px;
            margin: 2rem auto;
            max-width: 1100px;
        }

        .header {
            text-align: center;
            margin-bottom: 3rem;
            position: relative;
        }

        .header h1 {
            font-family: 'Cinzel', serif;
            color: var(--burgundy);
            font-size: 3.5rem;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 2px rgba(0, 0, 0, 0.1);
            letter-spacing: 3px;
            position: relative;
            padding-bottom: 1.5rem;
        }

        .header h1::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 3px;
            background: var(--elegant-border);
        }

        .header p {
            color: var(--dark-ink);
            font-size: 1.2rem;
            font-family: 'Quicksand', sans-serif;
        }

        .card {
            background: rgba(255, 255, 255, 0.5);
            border: 2px solid var(--vintage-gold);
            padding: 2.5rem;
            margin-bottom: 2rem;
            position: relative;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 5px;
            left: 5px;
            right: 5px;
            bottom: 5px;
            border: 1px solid var(--gold-accent);
            pointer-events: none;
        }

        .card h2 {
            font-family: 'Cinzel', serif;
            color: var(--burgundy);
            font-size: 1.8rem;
            margin-bottom: 2rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--vintage-gold);
        }

        label.form-group {
            margin-bottom: 1.5rem;
            display: block;
            padding: 1rem;
            border: 1px solid var(--vintage-gold);
            background: rgba(255, 255, 255, 0.7);
            border-radius: 4px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        label.form-group:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        input[type="checkbox"] {
            accent-color: var(--burgundy);
            width: 20px;
            height: 20px;
            margin-right: 1rem;
            border: 2px solid var(--vintage-gold);
            cursor: pointer;
        }

        button[type="submit"] {
            font-family: 'Cinzel', serif;
            background: transparent;
            border: 2px solid var(--vintage-gold);
            color: var(--dark-ink);
            padding: 1rem 2rem;
            font-size: 1.1rem;
            text-transform: uppercase;
            letter-spacing: 2px;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            z-index: 1;
            width: 100%;
            margin-top: 2rem;
            transition: all 0.4s ease;
            border-radius: 4px;
        }

        button[type="submit"]::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--elegant-border);
            transition: all 0.4s ease;
            z-index: -1;
        }

        button[type="submit"]:hover::before {
            left: 0;
        }

        button[type="submit"]:hover {
            color: var(--dark-ink);
            border-color: transparent;
            text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.2);
        }

        @media (max-width: 768px) {
            .elegant-paper {
                padding: 2rem;
                margin: 1rem;
            }

            .header h1 {
                font-size: 2.5rem;
            }

            .card {
                padding: 1.5rem;
            }
        }

        @media (max-width: 480px) {
            .elegant-paper {
                padding: 1.5rem;
                margin: 0.5rem;
            }

            .header h1 {
                font-size: 2rem;
            }

            .form-group {
                padding: 0.8rem;
            }
        }
    </style>
</head>
<body>
<div class="container">
    <div class="elegant-paper">
        <div class="header">
            <h1>Seleção de Cursos</h1>
            <p>Gerencie seus estudos de forma eficiente</p>
        </div>

        <?php

        include_once("conexao_POST.php");

        if (!$conexao) {
            die("Erro na conexão: " . pg_last_error());
        }

        if (isset($_SESSION['idusuario'])) {
            $id_usuario = $_SESSION['idusuario'];

            $query_consultar_todos_cursos = "SELECT * FROM appEstudo.curso ORDER BY nome ASC";
            $resultado_todos_cursos = pg_query($conexao, $query_consultar_todos_cursos);

            if ($resultado_todos_cursos) {
                echo "<div class='card'>";
                echo "<h2><i class='fas fa-graduation-cap'></i> Escolha os Cursos que Você vai Utilizar:</h2>";
                echo "<form action='processar_curso.php' method='post'>";

                while ($curso = pg_fetch_assoc($resultado_todos_cursos)) {
                    $checked = '';
                    $query_verificar_relacionamento = "SELECT * FROM appEstudo.usuario_has_curso 
                                                   WHERE usuario_idusuario = $id_usuario 
                                                   AND curso_idcurso = " . $curso['idcurso'];
                    $resultado_relacionamento = pg_query($conexao, $query_verificar_relacionamento);
                    if (pg_num_rows($resultado_relacionamento) > 0) {
                        $checked = 'checked';
                    }

                    echo "<label class='form-group'>";
                    echo "<input type='checkbox' name='cursos[]' value='" . $curso['idcurso'] . "' $checked>";
                    echo $curso['nome'];
                    echo "</label>";
                }

                echo "<button type='submit'><i class='fas fa-save'></i> Salvar Seleção</button>";
                echo "</form>";
                echo "</div>";
            } else {
                echo "<p>Nenhum curso encontrado.</p>";
            }
        }

        pg_close($conexao);
        ?>
    </div>
</div>
</body>
</html>
