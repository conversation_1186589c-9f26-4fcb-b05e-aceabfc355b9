<?php
session_start();
require_once("assets/config.php");
require_once("includes/auth.php");

header('Content-Type: application/json');

try {
    $username = filter_input(INPUT_POST, 'username', FILTER_SANITIZE_STRING);
    $password = filter_input(INPUT_POST, 'password', FILTER_SANITIZE_STRING);

    if (empty($username) || empty($password)) {
        echo json_encode([
            'success' => false,
            'message' => 'Por favor, preencha todos os campos'
        ]);
        exit;
    }

    // Consulta o usuário com todos os campos necessários
    $query = "SELECT idusuario, senha, status, tentativas_falhas, usuario, 
                     nome, is_admin, email 
              FROM appEstudo.usuario 
              WHERE email = $1 OR usuario = $1";
    
    $result = pg_query_params($conexao, $query, array($username));
    
    if (!$result) {
        echo json_encode([
            'success' => false,
            'message' => 'Erro na consulta: ' . pg_last_error($conexao)
        ]);
        exit;
    }

    $usuario = pg_fetch_assoc($result);

    if (!$usuario) {
        echo json_encode([
            'success' => false,
            'message' => 'Usuário ou email não encontrado'
        ]);
        exit;
    }

    // Verifica o status da conta
    switch($usuario['status']) {
        case 'bloqueado':
            echo json_encode([
                'success' => false,
                'message' => 'Conta bloqueada. Entre em contato com o suporte.'
            ]);
            exit;
        case 'inativo':
            echo json_encode([
                'success' => false,
                'message' => 'Conta inativa. Entre em contato com o suporte.'
            ]);
            exit;
        case 'pendente':
            echo json_encode([
                'success' => false,
                'message' => 'Conta pendente de ativação.'
            ]);
            exit;
    }

    // Comparação direta da senha (sem hash)
    if ($password === $usuario['senha']) {
        // Login bem-sucedido - configurando todas as sessões necessárias
        $_SESSION['validacao'] = true;
        $_SESSION['username'] = $usuario['usuario'];
        $_SESSION['idusuario'] = $usuario['idusuario'];
        $_SESSION['nome'] = $usuario['nome'];  // Certifique-se que este campo existe na sua consulta SQL
        $_SESSION['is_admin'] = $usuario['is_admin'];  // Certifique-se que este campo existe na sua consulta SQL
        $_SESSION['ultimo_acesso'] = date('Y-m-d H:i:s');
        
        resetarTentativas($conexao, $usuario['idusuario']);
        
        $ip = $_SERVER['REMOTE_ADDR'];
        $query_log = "INSERT INTO appEstudo.log_seguranca (usuario_id, tipo_evento, ip) 
                      VALUES ($1, 'login_sucesso', $2)";
        pg_query_params($conexao, $query_log, array($usuario['idusuario'], $ip));

        echo json_encode([
            'success' => true,
            'redirect' => 'index.php'
        ]);
    } else {
        // Senha incorreta
        registrarTentativaFalha($conexao, $usuario['idusuario']);
        
        if ($usuario['tentativas_falhas'] >= 9) {
            echo json_encode([
                'success' => false,
                'message' => 'Conta bloqueada por excesso de tentativas. Entre em contato com o suporte.'
            ]);
        } else {
            $tentativas_restantes = 10 - ($usuario['tentativas_falhas'] + 1);
            echo json_encode([
                'success' => false,
                'message' => "Senha incorreta. Você tem mais {$tentativas_restantes} tentativa(s) antes do bloqueio da conta."
            ]);
        }
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Erro: ' . $e->getMessage()
    ]);
}
?>
