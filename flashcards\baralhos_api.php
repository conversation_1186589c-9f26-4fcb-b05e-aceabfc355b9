<?php
// baralhos_api.php
session_start();
include_once("assets/config.php");
include_once("helpers.php");

// Verifica autenticação
if (!isset($_SESSION['idusuario'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Usuário não autenticado']);
    exit();
}

// Verifica parâmetro de categoria
if (!isset($_GET['categoria'])) {
    header('Content-Type: application/json');
    echo json_encode(['error' => 'Categoria não especificada']);
    exit();
}

$categoria_id = (int)$_GET['categoria'];

// Buscar baralhos da categoria com informações da matéria e estatísticas
$query_baralhos = "
    SELECT 
        d.id,
        d.nome as nome_baralho,
        m.nome as nome_materia,
        m.cor as cor_materia,
        (
            SELECT COUNT(DISTINCT t.id)
            FROM appestudo.flashcard_topics t
            WHERE t.deck_id = d.id
        ) as total_topicos,
        (
            SELECT COUNT(DISTINCT fta.flashcard_id)
            FROM appestudo.flashcard_topic_association fta
            JOIN appestudo.flashcard_topics t ON t.id = fta.topic_id
            WHERE t.deck_id = d.id
        ) as total_cards,
        (
            SELECT COUNT(DISTINCT fp.usuario_id)
            FROM appestudo.flashcard_topic_association fta
            JOIN appestudo.flashcard_topics t ON t.id = fta.topic_id
            JOIN appestudo.flashcards f ON f.id = fta.flashcard_id
            JOIN appestudo.flashcard_progress fp ON fp.flashcard_id = f.id
            WHERE t.deck_id = d.id
        ) as total_estudantes
    FROM appestudo.flashcard_decks d
    JOIN appestudo.materia m ON m.idmateria = d.materia_id
    WHERE d.category_id = $1 AND d.status = true
    ORDER BY m.nome";

try {
    $result_baralhos = pg_query_params($conexao, $query_baralhos, array($categoria_id));
    
    if (!$result_baralhos) {
        throw new Exception("Erro na consulta: " . pg_last_error($conexao));
    }
    
    $baralhos = [];
    while ($row = pg_fetch_assoc($result_baralhos)) {
        // Converter valores numéricos de string para números
        $row['total_topicos'] = (int)$row['total_topicos'];
        $row['total_cards'] = (int)$row['total_cards']; 
        $row['total_estudantes'] = (int)$row['total_estudantes'];
        $baralhos[] = $row;
    }
    
    // Retornar JSON
    header('Content-Type: application/json');
    echo json_encode($baralhos);
    
} catch (Exception $e) {
    // Retornar erro
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}