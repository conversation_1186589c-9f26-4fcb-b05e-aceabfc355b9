<?php
/**
 * ARQUIVO DESABILITADO
 *
 * Este arquivo foi desabilitado porque o sistema de revisão
 * agora funciona apenas com o banco de dados, sem dependência
 * de APIs externas de artigos.
 *
 * O sistema usa a interface de estudo diretamente.
 */

// Retornar erro para indicar que esta API não deve ser usada
http_response_code(410); // Gone
header('Content-Type: application/json');
echo json_encode([
    'erro' => 'API desabilitada',
    'mensagem' => 'O sistema de revisão não usa mais esta API. Use apenas o banco de dados.',
    'codigo' => 'API_DESABILITADA'
]);
exit;

// Configurar cabeçalhos CORS
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($metodo === 'OPTIONS') {
    exit;
}

try {
    switch ($metodo) {
        case 'GET':
            if (isset($_GET['artigo_numero'])) {
                buscarArtigoPorNumero($conexao, $_GET['artigo_numero']);
            } else {
                http_response_code(400);
                echo json_encode(['erro' => 'Número do artigo não informado']);
            }
            break;

        default:
            http_response_code(405);
            echo json_encode(['erro' => 'Método não permitido']);
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['erro' => 'Erro interno do servidor: ' . $e->getMessage()]);
}

/**
 * Busca um artigo específico pelo número
 */
function buscarArtigoPorNumero($conexao, $artigo_numero) {
    // Limpar formatação do número do artigo
    $artigo_numero_limpo = preg_replace('/^Art\.\s*/', '', $artigo_numero);
    $artigo_numero_limpo = preg_replace('/\.$/', '', $artigo_numero_limpo);
    $artigo_numero_limpo = trim($artigo_numero_limpo);

    // Buscar no arquivo JSON real dos artigos
    $artigo_json = buscarArtigoNoJSON($artigo_numero_limpo);
    if ($artigo_json) {
        echo json_encode([
            'encontrado' => true,
            'artigo' => $artigo_json,
            'fonte' => 'json_file'
        ]);
        return;
    }

    // Se não encontrou no JSON, usar fallback
    $artigo_fallback = buscarArtigoFallback($artigo_numero_limpo);
    if ($artigo_fallback) {
        echo json_encode([
            'encontrado' => true,
            'artigo' => $artigo_fallback,
            'fonte' => 'fallback'
        ]);
    } else {
        echo json_encode([
            'encontrado' => false,
            'artigo_numero' => $artigo_numero,
            'mensagem' => 'Artigo não encontrado'
        ]);
    }
}

/**
 * Busca artigo no arquivo JSON real
 */
function buscarArtigoNoJSON($numero) {
    // Tentar diferentes caminhos possíveis para o arquivo JSON
    $caminhos_possiveis = [
        __DIR__ . '/../artigos.json',
        __DIR__ . '/../../lexjus/artigos.json',
        __DIR__ . '/../lexjus/artigos.json',
        __DIR__ . '/artigos.json'
    ];

    $arquivo_json = null;
    foreach ($caminhos_possiveis as $caminho) {
        if (file_exists($caminho)) {
            $arquivo_json = $caminho;
            break;
        }
    }

    if (!$arquivo_json) {
        error_log("Arquivo artigos.json não encontrado em nenhum dos caminhos: " . implode(', ', $caminhos_possiveis));
        return null;
    }

    $json_content = file_get_contents($arquivo_json);
    if (!$json_content) {
        error_log("Erro ao ler arquivo artigos.json");
        return null;
    }

    $artigos = json_decode($json_content, true);
    if (!$artigos) {
        error_log("Erro ao decodificar JSON dos artigos");
        return null;
    }

    // Normalizar número para busca
    $numero_normalizado = preg_replace('/[^\d]/', '', $numero);

    foreach ($artigos as $artigo) {
        $numero_artigo = $artigo['numero'];
        $numero_artigo_normalizado = preg_replace('/[^\d]/', '', $numero_artigo);

        // Comparar número exato ou normalizado
        if ($numero_artigo === $numero ||
            $numero_artigo_normalizado === $numero_normalizado ||
            $numero_artigo === $numero . 'º' ||
            $numero_artigo . 'º' === $numero) {

            // Processar conteúdo para separar caput, incisos, etc.
            return processarConteudoArtigo($artigo);
        }
    }

    return null;
}

/**
 * Processa o conteúdo do artigo para separar caput, incisos, parágrafos
 */
function processarConteudoArtigo($artigo) {
    $conteudo = $artigo['conteudo'];
    $numero = $artigo['numero'];

    // Separar por quebras de linha
    $linhas = explode("\n", $conteudo);

    $caput = '';
    $incisos = [];
    $paragrafos = [];
    $paragrafo_unico = '';

    $modo_atual = 'caput';

    foreach ($linhas as $linha) {
        $linha = trim($linha);
        if (empty($linha)) continue;

        // Detectar incisos (I -, II -, III -, etc.)
        if (preg_match('/^[IVX]+\s*-/', $linha)) {
            $modo_atual = 'incisos';
            $incisos[] = $linha;
        }
        // Detectar parágrafo único
        else if (stripos($linha, 'Parágrafo único') === 0) {
            $paragrafo_unico = $linha;
            $modo_atual = 'paragrafo_unico';
        }
        // Detectar parágrafos numerados (§ 1º, § 2º, etc.)
        else if (preg_match('/^§\s*\d+/', $linha)) {
            $modo_atual = 'paragrafos';
            $paragrafos[] = [
                'numero' => preg_replace('/^(§\s*\d+[ºª]?).*/', '$1', $linha),
                'texto' => preg_replace('/^§\s*\d+[ºª]?\s*/', '', $linha),
                'alineas' => []
            ];
        }
        // Caput (primeira linha que não é inciso nem parágrafo)
        else if ($modo_atual === 'caput') {
            $caput .= ($caput ? ' ' : '') . $linha;
        }
    }

    return [
        'numero' => $numero,
        'caput' => $caput,
        'incisos' => $incisos,
        'paragrafo_unico' => $paragrafo_unico,
        'paragrafos_numerados' => $paragrafos,
        'titulo' => 'Constituição Federal',
        'capitulo' => 'Artigo da Constituição'
    ];
}

/**
 * Busca artigo usando dados conhecidos (fallback)
 */
function buscarArtigoFallback($numero) {
    // Dados de alguns artigos conhecidos para fallback
    $artigos_conhecidos = [
        '1' => [
            'numero' => '1º',
            'caput' => 'A República Federativa do Brasil, formada pela união indissolúvel dos Estados e Municípios e do Distrito Federal, constitui-se em Estado Democrático de Direito e tem como fundamentos:',
            'incisos' => [
                'I - a soberania;',
                'II - a cidadania;',
                'III - a dignidade da pessoa humana;',
                'IV - os valores sociais do trabalho e da livre iniciativa;',
                'V - o pluralismo político.'
            ],
            'paragrafo_unico' => 'Todo o poder emana do povo, que o exerce por meio de representantes eleitos ou diretamente, nos termos desta Constituição.',
            'paragrafos_numerados' => [],
            'titulo' => 'Dos Princípios Fundamentais',
            'capitulo' => 'Título I'
        ],
        '2' => [
            'numero' => '2º',
            'caput' => 'São Poderes da União, independentes e harmônicos entre si, o Legislativo, o Executivo e o Judiciário.',
            'incisos' => [],
            'paragrafo_unico' => '',
            'paragrafos_numerados' => [],
            'titulo' => 'Dos Princípios Fundamentais',
            'capitulo' => 'Título I'
        ],
        '3' => [
            'numero' => '3º',
            'caput' => 'Constituem objetivos fundamentais da República Federativa do Brasil:',
            'incisos' => [
                'I - construir uma sociedade livre, justa e solidária;',
                'II - garantir o desenvolvimento nacional;',
                'III - erradicar a pobreza e a marginalização e reduzir as desigualdades sociais e regionais;',
                'IV - promover o bem de todos, sem preconceitos de origem, raça, sexo, cor, idade e quaisquer outras formas de discriminação.'
            ],
            'paragrafo_unico' => '',
            'paragrafos_numerados' => [],
            'titulo' => 'Dos Princípios Fundamentais',
            'capitulo' => 'Título I'
        ],
        '5' => [
            'numero' => '5º',
            'caput' => 'Todos são iguais perante a lei, sem distinção de qualquer natureza, garantindo-se aos brasileiros e aos estrangeiros residentes no País a inviolabilidade do direito à vida, à liberdade, à igualdade, à segurança e à propriedade, nos termos seguintes:',
            'incisos' => [
                'I - homens e mulheres são iguais em direitos e obrigações, nos termos desta Constituição;',
                'II - ninguém será obrigado a fazer ou deixar de fazer alguma coisa senão em virtude de lei;',
                'III - ninguém será submetido a tortura nem a tratamento desumano ou degradante;',
                'IV - é livre a manifestação do pensamento, sendo vedado o anonimato;',
                'V - é assegurado o direito de resposta, proporcional ao agravo, além da indenização por dano material, moral ou à imagem;'
                // ... mais incisos podem ser adicionados
            ],
            'paragrafo_unico' => '',
            'paragrafos_numerados' => [
                [
                    'numero' => '§ 1º',
                    'texto' => 'As normas definidoras dos direitos e garantias fundamentais têm aplicação imediata.',
                    'alineas' => []
                ],
                [
                    'numero' => '§ 2º',
                    'texto' => 'Os direitos e garantias expressos nesta Constituição não excluem outros decorrentes do regime e dos princípios por ela adotados, ou dos tratados internacionais em que a República Federativa do Brasil seja parte.',
                    'alineas' => []
                ]
            ],
            'titulo' => 'Dos Direitos e Deveres Individuais e Coletivos',
            'capitulo' => 'Título II - Capítulo I'
        ],
        '13' => [
            'numero' => '13',
            'caput' => 'É livre o exercício de qualquer trabalho, ofício ou profissão, atendidas as qualificações profissionais que a lei estabelecer.',
            'incisos' => [],
            'paragrafo_unico' => '',
            'paragrafos_numerados' => [],
            'titulo' => 'Dos Direitos e Deveres Individuais e Coletivos',
            'capitulo' => 'Título II - Capítulo I'
        ],
        '14' => [
            'numero' => '14',
            'caput' => 'A soberania popular será exercida pelo sufrágio universal e pelo voto direto e secreto, com valor igual para todos, e, nos termos da lei, mediante:',
            'incisos' => [
                'I - plebiscito;',
                'II - referendo;',
                'III - iniciativa popular.'
            ],
            'paragrafo_unico' => '',
            'paragrafos_numerados' => [
                [
                    'numero' => '§ 1º',
                    'texto' => 'O alistamento eleitoral e o voto são obrigatórios para os maiores de dezoito anos.',
                    'alineas' => []
                ],
                [
                    'numero' => '§ 2º',
                    'texto' => 'Não podem alistar-se como eleitores os estrangeiros e, durante o período do serviço militar obrigatório, os conscritos.',
                    'alineas' => []
                ],
                [
                    'numero' => '§ 3º',
                    'texto' => 'São condições de elegibilidade, na forma da lei:',
                    'alineas' => [
                        'I - a nacionalidade brasileira;',
                        'II - o pleno exercício dos direitos políticos;',
                        'III - o alistamento eleitoral;',
                        'IV - o domicílio eleitoral na circunscrição;',
                        'V - a filiação partidária;',
                        'VI - a idade mínima de:'
                    ]
                ]
            ],
            'titulo' => 'Dos Direitos Políticos',
            'capitulo' => 'Título II - Capítulo IV'
        ],
        '37' => [
            'numero' => '37',
            'caput' => 'A administração pública direta e indireta de qualquer dos Poderes da União, dos Estados, do Distrito Federal e dos Municípios obedecerá aos princípios de legalidade, impessoalidade, moralidade, publicidade e eficiência e, também, ao seguinte:',
            'incisos' => [
                'I - os cargos, empregos e funções públicas são acessíveis aos brasileiros que preencham os requisitos estabelecidos em lei, assim como aos estrangeiros, na forma da lei;',
                'II - a investidura em cargo ou emprego público depende de aprovação prévia em concurso público de provas ou de provas e títulos, de acordo com a natureza e a complexidade do cargo ou emprego, na forma prevista em lei, ressalvadas as nomeações para cargo em comissão declarado em lei de livre nomeação e exoneração;'
                // ... mais incisos
            ],
            'paragrafo_unico' => '',
            'paragrafos_numerados' => [],
            'titulo' => 'Da Administração Pública',
            'capitulo' => 'Título III - Capítulo VII'
        ]
    ];

    // Normalizar número para busca
    $numero_normalizado = preg_replace('/[^\d]/', '', $numero);

    // Buscar por número exato ou normalizado
    if (isset($artigos_conhecidos[$numero])) {
        return $artigos_conhecidos[$numero];
    }

    if (isset($artigos_conhecidos[$numero_normalizado])) {
        return $artigos_conhecidos[$numero_normalizado];
    }

    // Se não encontrou, criar um artigo genérico baseado no número
    return [
        'numero' => $numero,
        'caput' => "Artigo {$numero} da Constituição Federal de 1988.",
        'incisos' => [],
        'paragrafo_unico' => '',
        'paragrafos_numerados' => [],
        'titulo' => 'Constituição Federal',
        'capitulo' => 'Artigo não catalogado'
    ];
}
?>
