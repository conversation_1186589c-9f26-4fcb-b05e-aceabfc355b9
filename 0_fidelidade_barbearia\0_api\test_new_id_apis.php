<?php
/**
 * Script de Teste para APIs com Novo Sistema de ID
 * Sistema de Fidelidade Barbearia
 * 
 * Este script testa todas as APIs modificadas para garantir que funcionam
 * corretamente com o novo sistema de identificação CPF-TIPO
 */

require_once 'config/database.php';
require_once 'config/response.php';
require_once 'endpoints/users.php';
require_once 'endpoints/auth.php';
require_once 'endpoints/fidelity.php';
require_once 'endpoints/rewards.php';

class ApiTester {
    private $db;
    private $testCpf = '12345678901';
    private $testClienteId;
    private $testBarbeiroId;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        
        $this->testClienteId = $this->testCpf . '-CLIENTE';
        $this->testBarbeiroId = $this->testCpf . '-BARBEIRO';
    }
    
    /**
     * Executar todos os testes
     */
    public function runTests() {
        echo "=== TESTANDO APIs COM NOVO SISTEMA DE ID ===\n\n";
        
        $tests = [
            'testUserCreation' => 'Criação de usuários',
            'testUserRetrieval' => 'Busca de usuários',
            'testAuthentication' => 'Autenticação',
            'testFidelityOperations' => 'Operações de fidelidade',
            'testRewardsOperations' => 'Operações de recompensas',
            'testIdValidation' => 'Validação de IDs',
            'testDuplicateHandling' => 'Tratamento de duplicatas'
        ];
        
        $passed = 0;
        $total = count($tests);
        
        foreach ($tests as $method => $description) {
            echo "🧪 Testando: $description\n";
            
            try {
                $result = $this->$method();
                if ($result) {
                    echo "✅ PASSOU\n\n";
                    $passed++;
                } else {
                    echo "❌ FALHOU\n\n";
                }
            } catch (Exception $e) {
                echo "❌ ERRO: " . $e->getMessage() . "\n\n";
            }
        }
        
        // Limpar dados de teste
        $this->cleanup();
        
        echo "=== RESULTADO DOS TESTES ===\n";
        echo "Passou: $passed/$total\n";
        
        return $passed === $total;
    }
    
    /**
     * Testar criação de usuários
     */
    private function testUserCreation() {
        // Testar criação de cliente
        $clienteData = [
            'cpf' => $this->testCpf,
            'nome' => 'Cliente Teste',
            'senha' => '123456',
            'tipo' => 'cliente'
        ];
        
        try {
            ob_start();
            createUser($clienteData, $this->db);
            $output = ob_get_clean();
            
            // Verificar se usuário foi criado com ID correto
            $sql = "SELECT id FROM usuarios WHERE cpf = ? AND tipo = 'cliente'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->testCpf]);
            $user = $stmt->fetch();
            
            if (!$user || $user['id'] !== $this->testClienteId) {
                echo "  ❌ Cliente não criado com ID correto\n";
                return false;
            }
            
            echo "  - Cliente criado com ID: {$user['id']}\n";
            
        } catch (Exception $e) {
            echo "  ❌ Erro ao criar cliente: " . $e->getMessage() . "\n";
            return false;
        }
        
        // Testar criação de barbeiro com mesmo CPF
        $barbeiroData = [
            'cpf' => $this->testCpf,
            'nome' => 'Barbeiro Teste',
            'senha' => '123456',
            'tipo' => 'barbeiro'
        ];
        
        try {
            ob_start();
            createUser($barbeiroData, $this->db);
            $output = ob_get_clean();
            
            // Verificar se barbeiro foi criado com ID correto
            $sql = "SELECT id FROM usuarios WHERE cpf = ? AND tipo = 'barbeiro'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->testCpf]);
            $user = $stmt->fetch();
            
            if (!$user || $user['id'] !== $this->testBarbeiroId) {
                echo "  ❌ Barbeiro não criado com ID correto\n";
                return false;
            }
            
            echo "  - Barbeiro criado com ID: {$user['id']}\n";
            
        } catch (Exception $e) {
            echo "  ❌ Erro ao criar barbeiro: " . $e->getMessage() . "\n";
            return false;
        }
        
        return true;
    }
    
    /**
     * Testar busca de usuários
     */
    private function testUserRetrieval() {
        // Testar busca por ID
        try {
            ob_start();
            getUserById($this->testClienteId, $this->db);
            $output = ob_get_clean();
            echo "  - Busca por ID funcionando\n";
        } catch (Exception $e) {
            echo "  ❌ Erro na busca por ID: " . $e->getMessage() . "\n";
            return false;
        }
        
        // Testar busca por CPF
        try {
            ob_start();
            getUserByCpf($this->testCpf, $this->db);
            $output = ob_get_clean();
            echo "  - Busca por CPF funcionando\n";
        } catch (Exception $e) {
            echo "  ❌ Erro na busca por CPF: " . $e->getMessage() . "\n";
            return false;
        }
        
        // Testar busca por CPF e tipo
        try {
            ob_start();
            getUserByCpfAndType($this->testCpf, 'cliente', $this->db);
            $output = ob_get_clean();
            echo "  - Busca por CPF e tipo funcionando\n";
        } catch (Exception $e) {
            echo "  ❌ Erro na busca por CPF e tipo: " . $e->getMessage() . "\n";
            return false;
        }
        
        return true;
    }
    
    /**
     * Testar autenticação
     */
    private function testAuthentication() {
        // Simular parâmetros GET
        $_GET['cpf'] = $this->testCpf;
        $_GET['senha'] = '123456';
        $_GET['tipo'] = 'cliente';
        
        try {
            ob_start();
            authenticateUser($this->db);
            $output = ob_get_clean();
            echo "  - Autenticação de cliente funcionando\n";
        } catch (Exception $e) {
            echo "  ❌ Erro na autenticação: " . $e->getMessage() . "\n";
            return false;
        }
        
        // Testar autenticação de barbeiro
        $_GET['tipo'] = 'barbeiro';
        
        try {
            ob_start();
            authenticateUser($this->db);
            $output = ob_get_clean();
            echo "  - Autenticação de barbeiro funcionando\n";
        } catch (Exception $e) {
            echo "  ❌ Erro na autenticação de barbeiro: " . $e->getMessage() . "\n";
            return false;
        }
        
        return true;
    }
    
    /**
     * Testar operações de fidelidade
     */
    private function testFidelityOperations() {
        // Testar busca de pontuação
        try {
            ob_start();
            getClientScore($this->testClienteId, $this->db);
            $output = ob_get_clean();
            echo "  - Busca de pontuação funcionando\n";
        } catch (Exception $e) {
            echo "  ❌ Erro na busca de pontuação: " . $e->getMessage() . "\n";
            return false;
        }
        
        // Testar adição de ponto
        $serviceData = [
            'cliente_id' => $this->testClienteId,
            'tipo_servico' => 'cabelo'
        ];
        
        try {
            ob_start();
            addServicePoint($serviceData, $this->db);
            $output = ob_get_clean();
            echo "  - Adição de ponto funcionando\n";
        } catch (Exception $e) {
            echo "  ❌ Erro na adição de ponto: " . $e->getMessage() . "\n";
            return false;
        }
        
        return true;
    }
    
    /**
     * Testar operações de recompensas
     */
    private function testRewardsOperations() {
        // Testar busca de brindes pendentes
        try {
            ob_start();
            getPendingRewards($this->testClienteId, $this->db);
            $output = ob_get_clean();
            echo "  - Busca de brindes pendentes funcionando\n";
        } catch (Exception $e) {
            echo "  ❌ Erro na busca de brindes: " . $e->getMessage() . "\n";
            return false;
        }
        
        return true;
    }
    
    /**
     * Testar validação de IDs
     */
    private function testIdValidation() {
        // Testar IDs válidos
        $validIds = [
            '12345678901-CLIENTE',
            '98765432100-BARBEIRO'
        ];
        
        foreach ($validIds as $id) {
            if (!validateClientId($id) && !preg_match('/^[0-9]{11}-BARBEIRO$/', $id)) {
                echo "  ❌ ID válido rejeitado: $id\n";
                return false;
            }
        }
        
        // Testar IDs inválidos
        $invalidIds = [
            'invalid-id',
            '123-CLIENTE',
            '12345678901-INVALID',
            '12345678901CLIENTE'
        ];
        
        foreach ($invalidIds as $id) {
            if (validateClientId($id)) {
                echo "  ❌ ID inválido aceito: $id\n";
                return false;
            }
        }
        
        echo "  - Validação de IDs funcionando corretamente\n";
        return true;
    }
    
    /**
     * Testar tratamento de duplicatas
     */
    private function testDuplicateHandling() {
        // Tentar criar cliente duplicado
        $duplicateData = [
            'cpf' => $this->testCpf,
            'nome' => 'Cliente Duplicado',
            'senha' => '123456',
            'tipo' => 'cliente'
        ];
        
        try {
            ob_start();
            createUser($duplicateData, $this->db);
            $output = ob_get_clean();
            
            // Se chegou aqui, não deveria ter permitido
            echo "  ❌ Duplicata foi permitida quando não deveria\n";
            return false;
            
        } catch (Exception $e) {
            // Esperado - deve dar erro
            echo "  - Duplicatas rejeitadas corretamente\n";
            return true;
        }
    }
    
    /**
     * Limpar dados de teste
     */
    private function cleanup() {
        try {
            $sql = "DELETE FROM usuarios WHERE cpf = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->testCpf]);
            
            $sql = "DELETE FROM pontuacao_fidelidade WHERE cliente_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->testClienteId]);
            
            $sql = "DELETE FROM historico_atendimentos WHERE cliente_id = ?";
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$this->testClienteId]);
            
            echo "🧹 Dados de teste limpos\n\n";
            
        } catch (Exception $e) {
            echo "⚠️ Erro ao limpar dados de teste: " . $e->getMessage() . "\n\n";
        }
    }
}

// Executar testes se chamado diretamente
if (php_sapi_name() === 'cli') {
    $tester = new ApiTester();
    $success = $tester->runTests();
    exit($success ? 0 : 1);
}
?>
