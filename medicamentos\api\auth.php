<?php
// api/auth.php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With');

require_once '../includes/auth.php';

// Instanciar objeto de autenticação
$auth = new Auth();

// Método HTTP
$method = $_SERVER['REQUEST_METHOD'];

// Verificar se é uma solicitação POST
if ($method === 'POST') {
    // Obter o tipo de rota (login, logout, etc.)
    $route = isset($_GET['route']) ? $_GET['route'] : '';

    // Processar login
    if ($route === 'login') {
        // Obter dados do corpo da requisição
        $data = json_decode(file_get_contents('php://input'), true);

        if (!$data || !isset($data['usuario']) || !isset($data['senha'])) {
            http_response_code(400);
            echo json_encode([
                'success' => false,
                'message' => 'Dados inválidos. Forneça usuário e senha.'
            ]);
            exit;
        }

        // Autenticar usuário usando a classe Auth existente
        $resultado = $auth->login($data['usuario'], $data['senha']);

        if ($resultado) {
            // Gerar token de autenticação simples
            $token = md5($resultado['idusuario'] . $resultado['usuario'] . time() . 'chave_secreta_medimaya');

            // Login bem-sucedido
            echo json_encode([
                'success' => true,
                'message' => 'Login realizado com sucesso',
                'token' => $token,  // Esta linha é nova - adiciona o token à resposta
                'usuario' => [
                    'id' => $resultado['idusuario'],
                    'nome' => $resultado['nome'],
                    'usuario' => $resultado['usuario'],
                    'email' => $resultado['email'],
                    'is_admin' => (bool)$resultado['is_admin']
                ]
            ]);
        } else {
            // Falha no login
            http_response_code(401);
            echo json_encode([
                'success' => false,
                'message' => 'Usuário ou senha incorretos'
            ]);
        }
    }
    // Se quiser implementar logout via API (opcional)
    else if ($route === 'logout') {
        // Implementação do logout se necessário
        echo json_encode([
            'success' => true,
            'message' => 'Logout realizado com sucesso'
        ]);
    }
    else {
        // Rota inválida
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'message' => 'Rota inválida'
        ]);
    }
} else {
    // Método não permitido
    http_response_code(405);
    echo json_encode([
        'success' => false,
        'message' => 'Método não permitido. Use POST para autenticação.'
    ]);
}
?>