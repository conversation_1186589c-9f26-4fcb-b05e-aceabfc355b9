<?php
/**
 * Script de teste para a API
 * Sistema de Fidelidade da Barbearia
 */

// Configurações
$baseUrl = 'https://planejaaqui.com.br/fidelidade_barbearia/api/v1';

// Função para fazer requisições
function makeRequest($url, $method = 'GET', $data = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Accept: application/json'
    ]);
    
    if ($data && in_array($method, ['POST', 'PUT'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'code' => $httpCode,
        'body' => json_decode($response, true)
    ];
}

// Função para exibir resultado do teste
function displayTest($testName, $result) {
    echo "\n=== $testName ===\n";
    echo "Status: " . $result['code'] . "\n";
    echo "Response: " . json_encode($result['body'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    echo str_repeat('-', 50) . "\n";
}

echo "🧪 INICIANDO TESTES DA API\n";
echo str_repeat('=', 50) . "\n";

// 1. Teste de saúde da API
$result = makeRequest("$baseUrl/health");
displayTest("Health Check", $result);

// 2. Teste de autenticação com dono existente
$result = makeRequest("$baseUrl/auth/login?cpf=00000000000&senha=123&tipo=barbeiro");
displayTest("Autenticação Dono", $result);

// 3. Teste de busca por CPF do dono
$result = makeRequest("$baseUrl/users/cpf/00000000000");
displayTest("Buscar Dono por CPF", $result);

// 4. Teste de criação de cliente
$userData = [
    'cpf' => '11144477735', // CPF válido para teste
    'nome' => 'João Teste Cliente',
    'senha' => '123456',
    'tipo' => 'cliente'
];
$result = makeRequest("$baseUrl/users", 'POST', $userData);
displayTest("Criar Cliente", $result);

// 5. Teste de autenticação do cliente
$result = makeRequest("$baseUrl/auth/login?cpf=11144477735&senha=123456&tipo=cliente");
displayTest("Autenticação Cliente", $result);

// 6. Teste de pontuação de fidelidade
$userId = 'João Teste Cliente-CLT11144477735'; // ID gerado automaticamente
$result = makeRequest("$baseUrl/fidelity/score/$userId");
displayTest("Buscar Pontuação", $result);

// 7. Teste de adicionar ponto
$serviceData = [
    'cliente_id' => $userId,
    'tipo_servico' => 'cabelo'
];
$result = makeRequest("$baseUrl/fidelity/service", 'POST', $serviceData);
displayTest("Adicionar Ponto", $result);

// 7. Teste de buscar pontuação atualizada
$result = makeRequest("$baseUrl/fidelity/score/$userId");
displayTest("Pontuação Atualizada", $result);

// 8. Teste de brindes pendentes
$result = makeRequest("$baseUrl/rewards/pending");
displayTest("Brindes Pendentes", $result);

// 9. Teste de estatísticas
$result = makeRequest("$baseUrl/fidelity/stats");
displayTest("Estatísticas", $result);

// 10. Teste de função MySQL
$functionData = ['user_id' => $userId];
$result = makeRequest("$baseUrl/function/is_usuario_dono", 'POST', $functionData);
displayTest("Teste Function MySQL", $result);

// 11. Teste de senha mestra
$passwordData = ['senha' => '123'];
$result = makeRequest("$baseUrl/master-password/verify", 'POST', $passwordData);
displayTest("Verificar Senha Mestra", $result);

// 11.1 Teste adicional - buscar senha mestra atual
$result = makeRequest("$baseUrl/function/get_senha_mestra", 'POST', []);
displayTest("Buscar Senha Mestra Atual", $result);

// 12. Teste de relatórios
$result = makeRequest("$baseUrl/reports/stats");
displayTest("Relatório de Estatísticas", $result);

echo "\n✅ TESTES CONCLUÍDOS!\n";
echo "Verifique os resultados acima para identificar possíveis problemas.\n";
echo "Em caso de erro 500, verifique os logs do servidor web.\n";
?>
