<?php
session_start();

// Define que estamos em um contexto de API
define('IS_API_CONTEXT', true);

// Desativa exibição de erros
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Define o cabeçalho para JSON
header('Content-Type: application/json');

try {
    // Inclui os arquivos necessários
    require_once '../conexao_POST.php';
    require_once 'includes/calculos.php';
    require_once 'includes/plano_estudo_logic.php';

    // Verificações básicas
    if (!isset($_SESSION['idusuario'])) {
        echo json_encode(['erro' => 'Usuário não autenticado']);
        exit;
    }

    if (!isset($_GET['materia'])) {
        echo json_encode(['erro' => 'Matéria não especificada']);
        exit;
    }

    if (!isset($conexao) || !$conexao) {
        echo json_encode(['erro' => 'Erro na conexão com o banco de dados']);
        exit;
    }

    $materia_nome = urldecode($_GET['materia']);

    // Usar a classe PlanoEstudoLogic como você fazia antes
    $planoData = new PlanoEstudoLogic($conexao);
    $id_planejamento = $planoData->getPlanejamentoAtivo();
    
    if (!$id_planejamento) {
        echo json_encode(['existe' => false]);
        exit;
    }

    // Primeiro, vamos pegar o ID da matéria
    $query_materia = "SELECT idmateria FROM appEstudo.materia WHERE nome = $1";
    $resultado_materia = pg_query_params($conexao, $query_materia, array($materia_nome));
    
    if (!$resultado_materia || pg_num_rows($resultado_materia) == 0) {
        echo json_encode(['existe' => false]);
        exit;
    }

    $materia = pg_fetch_assoc($resultado_materia);
    $id_materia = $materia['idmateria'];

    // Consulta para verificar se a matéria está no planejamento
    $query = "SELECT COUNT(*) as existe
              FROM appEstudo.planejamento_materia
              WHERE planejamento_idplanejamento = $1 
              AND materia_idmateria = $2";

    $resultado = pg_query_params($conexao, $query, array(
        $id_planejamento,
        $id_materia
    ));

    if (!$resultado) {
        echo json_encode(['erro' => 'Erro na consulta: ' . pg_last_error($conexao)]);
        exit;
    }

    $row = pg_fetch_assoc($resultado);
    $existe = ($row['existe'] > 0);

    echo json_encode(['existe' => $existe]);

} catch (Exception $e) {
    echo json_encode(['erro' => 'Erro ao verificar matéria: ' . $e->getMessage()]);
}
?>