<?php
session_start();
include_once("../assets/config.php");
require_once("includes/verify_admin.php");

// Verifica se é admin
verificarAcessoAdmin($conexao, true);

function limparEntrada($conexao, $dado) {
    $dado = trim($dado);
    return pg_escape_string($conexao, $dado);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $acao = $_POST['acao'];

    if ($acao === 'adicionar') {
        $nome = limparEntrada($conexao, $_POST['nome']);
        $cor = limparEntrada($conexao, $_POST['cor']);

        $query = "INSERT INTO appestudo.materia (nome, cor) VALUES ($1, $2)";
        $resultado = pg_query_params($conexao, $query, array($nome, $cor));

        if ($resultado) {
            $_SESSION['mensagem'] = "Matéria adicionada com sucesso!";
        } else {
            $_SESSION['erro'] = "Erro ao adicionar matéria.";
        }
    }

    elseif ($acao === 'editar') {
        $id = (int)$_POST['id'];
        $nome = limparEntrada($conexao, $_POST['nome']);
        $cor = limparEntrada($conexao, $_POST['cor']);

        $query = "UPDATE appestudo.materia SET nome = $1, cor = $2 WHERE idmateria = $3";
        $resultado = pg_query_params($conexao, $query, array($nome, $cor, $id));

        if ($resultado) {
            $_SESSION['mensagem'] = "Matéria atualizada com sucesso!";
        } else {
            $_SESSION['erro'] = "Erro ao atualizar matéria.";
        }
    }
}

elseif ($_SERVER['REQUEST_METHOD'] === 'GET' && isset($_GET['acao']) && $_GET['acao'] === 'excluir') {
    $id = (int)$_GET['id'];

    // Verificar se existem registros relacionados antes de excluir
    $query_check = "SELECT COUNT(*) FROM appestudo.conteudo_edital WHERE materia_id = $1";
    $result_check = pg_query_params($conexao, $query_check, array($id));
    $count = pg_fetch_result($result_check, 0, 0);

    if ($count > 0) {
        $_SESSION['erro'] = "Não é possível excluir esta matéria pois existem conteúdos relacionados.";
    } else {
        $query = "DELETE FROM appestudo.materia WHERE idmateria = $1";
        $resultado = pg_query_params($conexao, $query, array($id));

        if ($resultado) {
            $_SESSION['mensagem'] = "Matéria excluída com sucesso!";
        } else {
            $_SESSION['erro'] = "Erro ao excluir matéria.";
        }
    }
}

header("Location: gerenciar_materias.php");
exit();
