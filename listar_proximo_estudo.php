<!DOCTYPE html>
<html lang="pt-br">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mat<PERSON><PERSON> à Estudar</title>

    <style>
        body {
            font-family: 'Courier New', Courier, monospace;
        }

        table {
            margin: 0 auto;
            border-collapse: collapse;
            width: 80%;
            border: 1px solid #2b2723; /* Adiciona borda à tabela */
        }

        th {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #2b2723;
            border-right: 1px solid #2b2723; /* Adiciona borda à direita das células */
            white-space: nowrap; /* Evita quebras de linha */
            font-weight: bold; /* Negrito apenas para as células de cabeçalho */
        }

        td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #2b2723;
            border-right: 1px solid #2b2723; /* Adiciona borda à direita das células */
            white-space: nowrap; /* Evita quebras de linha */
        }

        th:first-child,
        td:first-child {
            border-left: 1px solid #2b2723; /* Adiciona borda à esquerda das células da primeira coluna */
        }


        .details {
            /* Remova a propriedade width e ajuste max-width conforme necessário */
            max-width: 60%;
            margin: 50px auto;
            background-color: rgba(222, 173, 69, 0.49);
            border: 2px solid #ccc;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            /* Adicione a propriedade word-wrap */
            word-wrap: break-word;
        }

        .details p {
            margin: 8px 0;
            line-height: 1.6;
            white-space: nowrap;
        }

        .details strong {
            width: 210px; /* Largura maior para a primeira coluna */
            display: inline-block;
            font-weight: bold;
        }
    </style>
</head>
<link rel="shortcut icon" href="logo/Estudo Off/favicon_32x32.png" type="image/x-icon">
<body>



<?php
include 'consulta_banco_ultima_proxima.php';
if ($semRegistro === true) {
    echo ' <h3>Próxima Matéria a Estudar</h3>
<div class="card text-black bg-warning mb-3" style="max-width;" xmlns="http://www.w3.org/1999/html">
           <div class="card-header text-center"><h4><strong><b class="text-danger">Não Iniciou o Estudo!</strong></b></h4></div>
           </div>';
}else {
    if ($resultado_proxima_materia && pg_num_rows($resultado_proxima_materia) > 0) {


        if ($resultado_estudo_proxima_materia_0 == true) {

            if ($linha_estudo_proxima_materia['hora_inicio'] != null) {

                echo '<div class="card text-black bg-warning mb-3" style="max-width: 100%;">
        <h3>Próxima Matéria a Estudar</h3>
        <div class="card-body">
            <div class="card-header text-center"><h4><strong>' . $linha_estudo_proxima_materia['nome_materia'] . '</strong></h4></div>
            <h5 class="card-text text-center"> <b class="text-danger">' . $linha_estudo_proxima_materia['nome_curso'] . '</b></h5>
            <h5 class="card-text text-center"> Último Estudo:<strong> ' . $linha_estudo_proxima_materia['ponto_estudado'] . '</strong></h5>
            <h5 class="card-text text-center">Método:<strong> <b class="text-danger">' . $linha_estudo_proxima_materia['metodo'] . '</b></strong> </h5>

            <div class="row">
                <div class="col text-body">
                    <p class="card-text text-body "><strong>Bruto: </strong> ' . $linha_estudo_proxima_materia['tempo_bruto'] . '<strong> Perdido: </strong> ' . $linha_estudo_proxima_materia['tempo_perdido'] . ' </p>
                </div>
                <div class="col text-end">
                    <p class="card-text text-end "><strong>Início: </strong> ' . $linha_estudo_proxima_materia['hora_inicio'] . '<strong> Fim: </strong> ' . $linha_estudo_proxima_materia['hora_fim'] . ' </p>
                </div>
            </div>

            <div class="row">
                <div class="col text-body">
                    <h5 class="card-text"><strong>Líquido:</strong><b class="text-danger"> ' . $linha_estudo_proxima_materia['tempo_liquido'] . '</b></strong></h5>
                </div>
                <div class="col text-end">
                    <h5 class="card-text text-end"><strong>Data: </strong><b class="text-danger">' . date('d/m/Y', strtotime($linha_estudo_proxima_materia['data'])) . '</b></h5>
                </div>
            </div>
        </div>
    </div>';
            } else {
                echo '<div class="card text-black bg-warning mb-3" style="max-width: 100%;">
        <h3>Próxima Matéria a Estudar</h3>
        <div class="card-body">
            <div class="card-header text-center"><h4><strong><span style="color: white; background-color: red;">ATENÇÃO</span> Ainda não Estudou Essa Matéria</strong></h4></div>
            <h4 class="card-title text-center"><strong>' . $linha_estudo_proxima_materia['nome_materia'] . '</strong></h4>
            <h5 class="card-title text-center"><strong><b class="text-danger">Comece!</strong></b></h5>
        </div>
    </div>';

            }
        } else {
            if ($proximaMateriaEstudada === null) {


                if ($resultado_primeira_materia_0 === true) {

                    //echo "Próxima matéria a ser estudada (primeira linha): " . $proximoIdMateria;


                    echo '<div class="card text-black bg-warning mb-3" style="max-width: 100%;">
        <h3>Próxima Matéria a Estudar</h3>
        <div class="card-body">
            <div class="card-header text-center"><h4><strong><span style="color: white; background-color: red;">ATENÇÃO</span> Muito Tempo sem Estudar</strong></h4></div>
            <h4 class="card-title text-center"><strong>' . $proximoMateriaNome . '</strong></h4>
            <h5 class="card-title text-center"><strong><b class="text-danger">Comece ou Revise!</strong></b></h5>
        </div>
    </div>';
                } else {
                    echo "Nenhuma matéria encontrada no planejamento.";
                }


            } else {
                echo '<div class="card text-black bg-warning mb-3" style="max-width: 100%;">
        <h3>Próxima Matéria a Estudar</h3>
        <div class="card-body">
            <div class="card-header text-center"><h4><strong>' . $proximaMateriaEstudada . '</strong></h4></div>
            <h4 class="card-title text-center"> <strong><span style="color: white; background-color: red;">ATENÇÃO</span> Estudo Não Iniciado</strong></h4>
            <h5 class="card-title text-center"><strong><b class="text-danger">Comece!</strong></b></h5>
        </div>
    </div>';
            }
        }
    }
}
//$proximaMateriaEstudada = $linha_estudo_proxima_materia['nome_materia'];
// Fechar a conexão com o banco de dados PostgreSQL
//pg_close($conexao);
?>