<?php
require_once 'config/database.php';
require_once 'includes/auth.php';

$erro = '';
$debug = ''; // para ajudar no desenvolvimento

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = filter_input(INPUT_POST, 'email', FILTER_SANITIZE_EMAIL);
    $senha = $_POST['senha'] ?? '';
    
    if (empty($email) || empty($senha)) {
        $erro = 'Por favor, preencha todos os campos.';
    } else {
        if (login($conexao, $email, $senha)) {
            // Adiciona mensagem de debug
            $user = getCurrentUser();
            $debug = "Login bem sucedido! Usuário: " . $user['email'] . " | Admin: " . ($user['is_admin'] ? 'Sim' : 'Não');
            
            // Redireciona com base no tipo de usuário
            if (isAdmin()) {
                header('Location: gerenciar-assinaturas.php');
            } else {
                header('Location: ../../index.html');
            }
            exit;
        } else {
            $erro = 'Email ou senha inválidos.';
            
            // Adiciona verificação do usuário para debug
            $sql = "SELECT email FROM appestudo.usuario WHERE email = $1";
            $result = pg_query_params($conexao, $sql, array($email));
            if (pg_num_rows($result) > 0) {
                $debug = "Email existe, mas senha incorreta";
            } else {
                $debug = "Email não encontrado";
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Sistema de Assinaturas</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .login-container {
            max-width: 400px;
            margin: 100px auto;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            border-radius: 5px;
        }
        .debug-info {
            margin-top: 20px;
            padding: 10px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <h2 class="text-center mb-4">Login</h2>
            
            <?php if ($erro): ?>
                <div class="alert alert-danger"><?= htmlspecialchars($erro) ?></div>
            <?php endif; ?>
            
            <?php if ($debug && isset($_GET['debug'])): ?>
                <div class="debug-info">
                    <strong>Debug:</strong> <?= htmlspecialchars($debug) ?>
                </div>
            <?php endif; ?>
            
            <form method="POST" action="">
                <div class="mb-3">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" class="form-control" id="email" name="email" 
                           value="<?= htmlspecialchars($email ?? '') ?>" required>
                </div>
                
                <div class="mb-3">
                    <label for="senha" class="form-label">Senha</label>
                    <input type="password" class="form-control" id="senha" name="senha" required>
                </div>
                
                <button type="submit" class="btn btn-primary w-100">Entrar</button>
            </form>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>