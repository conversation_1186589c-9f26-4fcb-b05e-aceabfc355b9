<?php
include_once("../session_config.php");
include_once("../assets/config.php");

if (!verificarSessaoValida()) {
    header("Location: ../login_index.php");
    exit();
}

$resposta_id = (int)$_POST['resposta_id'];
$conteudo = trim($_POST['conteudo']);
$acao = $_POST['acao']; // nova variável para determinar a ação (editar ou excluir)

// Verificar se o usuário é o autor da resposta
$query_check = "SELECT usuario_id FROM appestudo.forum_respostas WHERE id = $1";
$result_check = pg_query_params($conexao, $query_check, array($resposta_id));
$resposta = pg_fetch_assoc($result_check);

if ($resposta['usuario_id'] != $_SESSION['idusuario']) {
    echo json_encode(['success' => false, 'message' => 'Você não tem permissão para modificar esta resposta']);
    exit();
}

if ($acao === 'excluir') {
    // Soft delete - apenas marca como inativo
    $query = "UPDATE appestudo.forum_respostas SET status = false WHERE id = $1";
    $result = pg_query_params($conexao, $query, array($resposta_id));
} else {
    // Edição normal
    if (empty($conteudo)) {
        echo json_encode(['success' => false, 'message' => 'O conteúdo não pode estar vazio']);
        exit();
    }

    $query = "UPDATE appestudo.forum_respostas SET conteudo = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2";
    $result = pg_query_params($conexao, $query, array($conteudo, $resposta_id));
}

if ($result) {
    echo json_encode(['success' => true]);
} else {
    echo json_encode(['success' => false, 'message' => 'Erro ao processar a solicitação']);
}
?>