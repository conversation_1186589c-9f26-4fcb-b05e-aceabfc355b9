<?php
// Verifica se é APENAS o super admin (ID 1)
function verificarSuperAdmin($conexao, $isAPI = true) {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    if (!isset($_SESSION['idusuario']) || $_SESSION['idusuario'] != 1) {
        if ($isAPI) {
            header('Content-Type: application/json');
            http_response_code(403);
            echo json_encode(['error' => 'Acesso restrito ao administrador principal']);
        } else {
            $_SESSION['erro'] = "Acesso restrito ao administrador principal.";
            header('Location: ../login_index.php');
        }
        exit();
    }

    return true;
}

// Verifica se é qualquer admin (incluindo ID 1)
function verificarAcessoAdmin($conexao, $isAPI = true) {
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }

    if (!isset($_SESSION['idusuario']) || 
        (!isset($_SESSION['is_admin']) || ($_SESSION['is_admin'] !== 't' && $_SESSION['is_admin'] !== true))) {
        
        if ($isAPI) {
            header('Content-Type: application/json');
            http_response_code(403);
            echo json_encode(['error' => 'Acesso restrito a administradores']);
        } else {
            $_SESSION['erro'] = "Acesso restrito a administradores.";
            header('Location: ../login_index.php');
        }
        exit();
    }

    return true;
}
?>





