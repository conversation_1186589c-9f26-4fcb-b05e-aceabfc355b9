<?php
/**
 * Endpoint de Senha Mestra
 * Sistema de Fidelidade da Barbearia
 */

function handleMasterPassword($method, $action, $input, $db) {
    switch ($method) {
        case 'GET':
            if ($action === 'history') {
                getMasterPasswordHistory($db);
            } else {
                ApiResponse::error('Ação não especificada', 400);
            }
            break;
            
        case 'POST':
            if ($action === 'verify') {
                verifyMasterPassword($input, $db);
            } elseif ($action === 'update') {
                updateMasterPassword($input, $db);
            } else {
                ApiResponse::error('Ação não especificada', 400);
            }
            break;
            
        default:
            ApiResponse::methodNotAllowed();
    }
}

/**
 * Verificar senha mestra
 */
function verifyMasterPassword($input, $db) {
    if (empty($input['senha'])) {
        ApiResponse::validation(['senha'], 'Senha é obrigatória');
    }
    
    try {
        $sql = "SELECT get_senha_mestra() as senha_mestra";
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $result = $stmt->fetch();
        
        $senhaCorreta = $result['senha_mestra'] ?? 'admin123';
        $isValid = $input['senha'] === $senhaCorreta;
        
        ApiResponse::success(['valid' => $isValid]);
        
    } catch (PDOException $e) {
        error_log("Verify master password error: " . $e->getMessage());
        ApiResponse::error('Erro ao verificar senha mestra');
    }
}

/**
 * Atualizar senha mestra
 */
function updateMasterPassword($input, $db) {
    if (empty($input['nova_senha']) || empty($input['dono_id'])) {
        ApiResponse::validation(['nova_senha', 'dono_id'], 'Nova senha e ID do dono são obrigatórios');
    }
    
    if (!Validator::validatePassword($input['nova_senha'])) {
        ApiResponse::validation(['nova_senha'], 'Nova senha deve ter pelo menos 6 caracteres');
    }
    
    try {
        // Verificar se o usuário é realmente o dono
        $sql = "SELECT is_usuario_dono(?) as is_dono";
        $stmt = $db->prepare($sql);
        $stmt->execute([$input['dono_id']]);
        $result = $stmt->fetch();
        
        if (!$result['is_dono']) {
            ApiResponse::unauthorized('Apenas o dono pode alterar a senha mestra');
        }
        
        // Atualizar senha mestra usando a procedure
        $sql = "CALL atualizar_senha_mestra(?, ?)";
        $stmt = $db->prepare($sql);
        $stmt->execute([$input['nova_senha'], $input['dono_id']]);
        
        ApiResponse::success(null, 'Senha mestra atualizada com sucesso');
        
    } catch (PDOException $e) {
        error_log("Update master password error: " . $e->getMessage());
        ApiResponse::error('Erro ao atualizar senha mestra');
    }
}

/**
 * Buscar histórico de alterações da senha mestra
 */
function getMasterPasswordHistory($db) {
    try {
        $sql = "SELECT bm.data_atualizacao, u.nome as alterado_por 
                FROM barbeiro_master bm 
                JOIN usuarios u ON bm.dono_id = u.id 
                ORDER BY bm.data_atualizacao DESC 
                LIMIT 50";
        
        $stmt = $db->prepare($sql);
        $stmt->execute();
        $history = $stmt->fetchAll();
        
        ApiResponse::success($history);
        
    } catch (PDOException $e) {
        error_log("Get master password history error: " . $e->getMessage());
        ApiResponse::error('Erro ao buscar histórico da senha mestra');
    }
}
?>
