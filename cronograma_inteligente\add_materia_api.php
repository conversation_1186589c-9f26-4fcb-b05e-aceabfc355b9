<?php
// add_materia_api.php - Arquivo de API limpo
// Desabilita exibição de erros para o usuário (ainda registrará nos logs)
ini_set('display_errors', 0);
error_reporting(0);

// Define cabeçalho JSON
header('Content-Type: application/json');

// Inicia a sessão
session_start();

// Função de log limpa
function logError($msg) {
    error_log($msg);
}

try {
    // Verifica autenticação
    if (!isset($_SESSION['idusuario'])) {
        echo json_encode(['sucesso' => false, 'erro' => 'Usuário não autenticado']);
        exit;
    }

    // Verifica parâmetro
    if (!isset($_GET['materia']) || empty($_GET['materia'])) {
        echo json_encode(['sucesso' => false, 'erro' => 'Matéria não especificada']);
        exit;
    }

    $materia_nome = urldecode($_GET['materia']);
    $usuario_id = $_SESSION['idusuario'];
    
    // Inclui apenas conexão com banco - arquivo mínimo
    require_once '../conexao_POST.php';
    
    if (!isset($conexao) || !$conexao) {
        echo json_encode(['sucesso' => false, 'erro' => 'Erro na conexão com o banco de dados']);
        exit;
    }
    
    // Obter ID do planejamento ativo
    $query_planejamento = "SELECT idplanejamento 
                           FROM appEstudo.planejamento 
                           WHERE usuario_idusuario = $1 
                           ORDER BY data_inicio DESC 
                           LIMIT 1";
    
    $result_planejamento = pg_query_params($conexao, $query_planejamento, array($usuario_id));
    
    if (!$result_planejamento || pg_num_rows($result_planejamento) === 0) {
        echo json_encode(['sucesso' => false, 'erro' => 'Planejamento não encontrado']);
        exit;
    }
    
    $planejamento = pg_fetch_assoc($result_planejamento);
    $id_planejamento = $planejamento['idplanejamento'];
    
    // Obter ID da matéria
    $query_materia = "SELECT idmateria FROM appEstudo.materia WHERE nome = $1";
    $result_materia = pg_query_params($conexao, $query_materia, array($materia_nome));
    
    if (!$result_materia || pg_num_rows($result_materia) === 0) {
        echo json_encode(['sucesso' => false, 'erro' => 'Matéria não encontrada']);
        exit;
    }
    
    $materia = pg_fetch_assoc($result_materia);
    $id_materia = $materia['idmateria'];
    
    // Verificar se já existe
    $query_check = "SELECT 1 FROM appEstudo.planejamento_materia 
                    WHERE planejamento_idplanejamento = $1 
                    AND materia_idmateria = $2";
    
    $result_check = pg_query_params($conexao, $query_check, array(
        $id_planejamento,
        $id_materia
    ));
    
    if (pg_num_rows($result_check) > 0) {
        echo json_encode(['sucesso' => true, 'mensagem' => 'Matéria já existe no planejamento']);
        exit;
    }
    
    // Inserir relação
    $query_insert = "INSERT INTO appEstudo.planejamento_materia 
                    (planejamento_idplanejamento, materia_idmateria) 
                    VALUES ($1, $2)";
                    
    $result_insert = pg_query_params($conexao, $query_insert, array(
        $id_planejamento,
        $id_materia
    ));
    
    if (!$result_insert) {
        throw new Exception(pg_last_error($conexao));
    }
    
    echo json_encode(['sucesso' => true]);
    exit;
    
} catch (Exception $e) {
    logError("Erro em add_materia_api.php: " . $e->getMessage());
    echo json_encode(['sucesso' => false, 'erro' => $e->getMessage()]);
    exit;
}