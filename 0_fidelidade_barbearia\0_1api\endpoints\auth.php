<?php
/**
 * Endpoint de Autenticação
 * Sistema de Fidelidade da Barbearia
 */

function handleAuth($method, $action, $input, $db) {
    switch ($action) {
        case 'login':
            if ($method !== 'GET') {
                ApiResponse::methodNotAllowed();
            }
            authenticateUser($db);
            break;
            
        default:
            ApiResponse::notFound('Ação de autenticação não encontrada');
    }
}

/**
 * Autenticar usuário
 */
function authenticateUser($db) {
    // Obter parâmetros da query string
    $cpf = $_GET['cpf'] ?? '';
    $senha = $_GET['senha'] ?? '';
    $tipo = $_GET['tipo'] ?? '';
    
    // Validar parâmetros
    if (empty($cpf) || empty($senha) || empty($tipo)) {
        ApiResponse::validation(['cpf', 'senha', 'tipo'], 'CPF, senha e tipo são obrigatórios');
    }
    
    // Validar CPF
    if (!Validator::validateCPF($cpf)) {
        ApiResponse::validation(['cpf'], 'CPF inválido');
    }
    
    // Validar tipo
    if (!in_array($tipo, ['cliente', 'barbeiro'])) {
        ApiResponse::validation(['tipo'], 'Tipo deve ser "cliente" ou "barbeiro"');
    }
    
    try {
        // Buscar usuário no banco de dados usando CPF e tipo
        // O ID retornado já estará no novo formato: CPF-TIPO
        $sql = "SELECT id, cpf, nome, senha, tipo, is_owner, data_criacao, data_atualizacao
                FROM usuarios
                WHERE cpf = ? AND tipo = ?";

        $stmt = $db->prepare($sql);
        $stmt->execute([$cpf, $tipo]);
        $user = $stmt->fetch();
        
        if (!$user) {
            ApiResponse::error('Usuário não encontrado', 404);
        }
        
        // Verificar senha (assumindo que está em texto plano por enquanto)
        if ($user['senha'] !== $senha) {
            ApiResponse::error('Senha incorreta', 401);
        }
        
        // Remover senha da resposta
        unset($user['senha']);
        
        // Converter campos booleanos
        $user['is_owner'] = (bool) $user['is_owner'];
        
        ApiResponse::success($user, 'Login realizado com sucesso');
        
    } catch (PDOException $e) {
        error_log("Auth error: " . $e->getMessage());
        ApiResponse::error('Erro ao autenticar usuário');
    }
}
?>
