<?php
// Garantir que não há output antes do JSON
ob_start();

// Configurar headers
header('Content-Type: application/json');

// Iniciar sessão e incluir dependências
session_start();
require_once('../includes/auth.php');
require_once('../conexao_POST.php');
require_once('../cadastros/includes/verify_admin.php');

// Função para retornar resposta JSON e encerrar
function jsonResponse($success, $message) {
    ob_clean(); // Limpa qualquer output anterior
    echo json_encode([
        'success' => $success,
        'message' => $message
    ]);
    exit();
}

// Verifica se é o super admin (usando true pois é API JSON)
verificarSuperAdmin($conexao, true);

// Verificar método
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(false, 'Método inválido');
}

// Verificar ID
if (!isset($_POST['id'])) {
    jsonResponse(false, 'ID não fornecido');
}

$id = (int)$_POST['id'];

if ($id <= 0) {
    jsonResponse(false, 'ID inválido');
}

try {
    // Excluir a notificação
    $query = "DELETE FROM appestudo.notificacoes WHERE id = $1";
    $result = pg_query_params($conexao, $query, array($id)); // Corrigido: adicionado o parâmetro $query
    
    if (!$result) {
        throw new Exception(pg_last_error($conexao));
    }

    if (pg_affected_rows($result) === 0) {
        jsonResponse(false, 'Notificação não encontrada');
    }

    jsonResponse(true, 'Notificação excluída com sucesso');

} catch (Exception $e) {
    jsonResponse(false, $e->getMessage());
}
?>




