<?php
//agenda_excluir_evento.php
session_start();
include_once("../conexao_POST.php");

// Definir cabeçalhos para evitar cache
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');
header('Expires: 0');

// Registrar logs para depuração
error_log("=== INÍCIO DO PROCESSAMENTO DE EXCLUSÃO ===");
error_log("POST: " . print_r($_POST, true));
error_log("SESSION: " . print_r($_SESSION, true));

// Verificar se o usuário está logado
if (!isset($_SESSION['idusuario'])) {
    error_log("Usuário não logado");
    echo json_encode(['success' => false, 'message' => 'Usuário não logado']);
    exit;
}

// Verificar se o ID do evento foi recebido
if (!isset($_POST['eventoId'])) {
    error_log("ID do evento não fornecido");
    echo json_encode(['success' => false, 'message' => 'ID do evento não fornecido']);
    exit;
}

// Obter dados da requisição
$idUsuario = $_SESSION['idusuario'];
$eventoId = $_POST['eventoId'];

error_log("ID do Usuário: $idUsuario");
error_log("ID do Evento: $eventoId");

try {
    // Excluir o evento da tabela agenda
    $query = "DELETE FROM appEstudo.agenda 
              WHERE id = $1 
              AND usuario_idusuario = $2";
    
    error_log("Query SQL: $query");
    
    // Executar a query
    $result = pg_query_params($conexao, $query, array($eventoId, $idUsuario));
    
    if (!$result) {
        $error = pg_last_error($conexao);
        error_log("Erro SQL: $error");
        echo json_encode(['success' => false, 'message' => "Erro SQL: $error"]);
        exit;
    }
    
    $rowsAffected = pg_affected_rows($result);
    error_log("Linhas afetadas: $rowsAffected");
    
    if ($rowsAffected > 0) {
        error_log("Evento excluído com sucesso");
        echo json_encode(['success' => true, 'message' => 'Evento excluído com sucesso']);
    } else {
        error_log("Nenhum evento foi excluído");
        echo json_encode(['success' => false, 'message' => 'Nenhum evento foi excluído. Verifique o ID.']);
    }
    
} catch (Exception $e) {
    $errorMsg = $e->getMessage();
    error_log("Exceção: $errorMsg");
    echo json_encode(['success' => false, 'message' => "Erro: $errorMsg"]);
}

error_log("=== FIM DO PROCESSAMENTO DE EXCLUSÃO ===");
?>