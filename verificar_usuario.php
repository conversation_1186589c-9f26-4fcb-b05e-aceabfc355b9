<?php
include 'conexao_POST.php';

header('Content-Type: application/json');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $usuario = trim($_POST['usuario'] ?? '');
    $usuario = strtolower($usuario);
    
    // Sanitizar entrada
    $usuario = htmlspecialchars($usuario, ENT_QUOTES, 'UTF-8');
    
    // Verificar se usuário existe
    $sql = "SELECT usuario FROM usuario WHERE usuario = $1";
    $result = pg_query_params($conexao, $sql, array($usuario));
    
    $disponivel = pg_num_rows($result) === 0;
    
    echo json_encode(['disponivel' => $disponivel]);
} else {
    http_response_code(405);
    echo json_encode(['erro' => 'Método não permitido']);
}